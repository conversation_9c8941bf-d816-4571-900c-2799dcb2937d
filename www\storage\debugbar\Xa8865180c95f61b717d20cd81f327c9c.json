{"__meta": {"id": "Xa8865180c95f61b717d20cd81f327c9c", "datetime": "2025-06-07 04:42:06", "utime": **********.178317, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749271324.841993, "end": **********.178354, "duration": 1.3363609313964844, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749271324.841993, "relative_start": 0, "end": 1749271325.986113, "relative_end": 1749271325.986113, "duration": 1.1441199779510498, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749271325.986131, "relative_start": 1.1441378593444824, "end": **********.178357, "relative_end": 2.86102294921875e-06, "duration": 0.19222593307495117, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45107648, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00843, "accumulated_duration_str": "8.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.113497, "duration": 0.00636, "duration_str": "6.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.445}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.149066, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.445, "width_percent": 12.218}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.158922, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 87.663, "width_percent": 12.337}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1112155784 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1112155784\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1115911587 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1115911587\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1919589564 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919589564\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2139042596 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=xq5jkj%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1p8bbqf%7C1749271322870%7C5%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJFVXZxU3BRR01yWHZnODc0SUJITVE9PSIsInZhbHVlIjoialdYMTNQK0ZqWkd6MWp6M0duZklUTUNKYXZmTnY0dklhUlJ5YXc3WllFUDRWaHlYZ0kxNHcrc1gzbFlCbGJudURjYW1ROWI2VkhGdHEvYUdGdCt2bVV5Z3k5UDVIVWlESTlVUFgxWmxlcWlXMDdjSnB0OVAzaXBWQTkxdkYxQThkczZmRXpwZlRPR2VJT0NweW85RGlTRUd2dDY1UFlRZzZocEMxZXoxdllIOTRDb3pNS3BPaXJ2cnhEZlkvOU1FT0tvb3NkZzAybWZkbjRDTGNJY0o2Qkl4TUZ5R3daUkpkb1RUM3pZUTkrNmFvaWE4UmJMVnAxdXI4elFKdEgwcjlQb0VtRjU2QVdKY25uS0k1RkszOW1GS1VaQTkwd0FjN2ZIc1RvTW9FTkJqWTc0a1dtZ0YvUHE2SVpnNUkycElEdy9MdzIvenZpSTJHSG1SaXMrMXczSTBrRXRGOThHaWRpeHFvSFNIMDJYMit6eWRxdG1JM3BPWVdKVWxkRk1LZXBUMWkzSnhYZ3FsYnpPRkZOOGc4Zm42UW8vWVlDU3N0RGRIQThCQjVCQWxMdjFKdjEwWVU3RUZHQnhuaEF3dWRoVUd4bDF3RWdiWnB5SThzVFZPZnhJdnI3UXFjMXZBelRZcEF0K0hPZ3p0VU13RktLU1J1a0ZjWlJlVE4xRWQiLCJtYWMiOiI1NmZlODBiM2U1NjRjODA4MDAwNTIwZGNkNTE5MTU0NzRkZmQxMmY4MGFjYjA2MzY2OTFlYzZhN2Y3NzFjMDBkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVsclpLR2oySWVXMDVEb1RsS1F2K0E9PSIsInZhbHVlIjoiZi9kYVd5a0UzMUlQUHZyV2dGRnIwSUczT2MzeG0xUnlpcWNjMUxGMmM3Q0dpSmxGV1dsZEhoWmtxVE9Ud2FTaDJzUVRCbnNSbjFnSEFDaGJvb3lTODNEYXBFRW53ek41TmFwd1hrZlg5MlBtSTJtS0NmLzI0RTd6bHdxYlVtRTcycUdlNmZmSXhBemlxSXpzeVZNL29UbUo0dnFQZjdlUkZiY2t2bWRRNG5JazY1YUNYWUlOZlllenM4bFk1MWp0U0tKZDVKUE9kU2JiS0hsQW9xT3RORm4yQzQ5RHlaT2x1MnRzT0U4a2c0a3Bzdm11L3ovd0t1YlBRMTlyaC9ySjNRY2dObkR4MTFtMlEzcnFWSUVuN1NwcWRWRUxkWGdHZS9YK21YQ01LQ2ZjSFRZNnlRZzNYcTY0YllWVlFHbEhCTDZWd1hQeXlWR080QnRBRisvdUpoUlhOSTlNSmNKNGxYakFMTGRudE1UVXJuZ2hYSDM0WTJzSTdQbndvZzN5S2FnejJ6cnJ5MGdVU29uMnliOTFCM2dqVGdkRmdVUlU4YUwxbWwxNjllamxmb2w0YmlYeUN4bU9zTkFuemN2bURYWENoTUViaTdkYXAwV0tIR0VpY3ozeGZkMjRNNmdxTzFpZ3VnMXZjRDFBTml6OEVvNXU2YkZmWjRXT2JxZm0iLCJtYWMiOiI0MGE2NjY5NmYxMTdlZjAyMTNjNDBkNGM5ZmQzMWExMTVhNWFmZmU0YjE2ZjhjZWNhYzk4YjI3ZTBlN2IwNTFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139042596\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1112698747 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z1ibAMoy16lMUkDWMem3ATVFxZVkTly8GBk5DGQF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1112698747\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1557471775 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:42:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImErWXJBdEU2Ly9NazhOYjFxOEF3eEE9PSIsInZhbHVlIjoiUVZoUlVEbDg3bjNJc2ZkNlZOb2VndzB0K0N4STR3Sm1uYkkvcVhEbnBhdmVDdVB3bWRMMVRMVzFla0kvNGx5MTd5RlVEcnpBZktXbmdWRlo0ZGRJZFllb3BSV0dqV2krS3BVQ2dDTm91WDNjNGlySExZZXovTVVhRm5CcWNxMndoaDdSUnhyVEtiMWNGbWtkOUk4cVB6WGZXRkpia0lyOWZWYnVMVUNwU0VFc056RzFBYUJPZ0lCdVZxWGh6V2o1TTJaUGZXYUk2YmtNM1JBNXhzclhKaHAvc1FNdE9ZOTZFdHFMMWdYZzJXV0NYRkowSTJkUWpHVEw4dC9yMlVwbmZuVGVPeCt5K0FSZ0xIUEZqOThwMGNiaU1Nenh6SGlucERLYWJqMDJYYllFMzlscDZrMjdKQmx0QUMrVTk2OEtPK08zV3Y3aHZiaW85QjNhUHlpTFhGcjlJSi9vVnFBUW5HV2plcStKU2svZjZ6VWhWV2RmODJldFkvWSs1ZzNNY1MrNW9RZzR6TGt3MDU3d1RvTU42N05nVXB6OFlwNmJBNGdEdGRSOWMrdFVFeUV4WkRxNXhjNDBMbVNVM2MrbHZqTnMrVHVuYW1PLzBVN2ZCRVNCNzg0cUVMNFJVd00zQ1hUdG8zUnBCQTg1T3F0ZGpEeEdNSHloRml1ZFh6dFEiLCJtYWMiOiI4MWRjZDU0ZTg3OTliMTUxYjA5MGRlNjg4OWU3NWRkMTYwMzM5NThiODlmNjUxYTkxNTI3OGI3ZDM0MmNjOWNjIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:42:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFCNzVwbG9jcUJTcUJzRjJVSTRRcnc9PSIsInZhbHVlIjoielNiVE83YTA4OWs5SUoxajBoempYaFc2ZkZGNVlIekNvY2V4R1A5bWFhOUF1MmZocWRrNWI0clFjTlQ0NWxMdjVXcWxCWS90ME80LzJ6czl2Z2V5NG5wb1dIbmdQMW15ZUlDOHJuTHZvSUFpTWE1OWZWM2V3Nkd2Uml0SjZBN3NTWEhRSzVGYVRWTkZuWHVFc1djVEFjZk5QZ2l3aXFZMkFSYnYvTXYxVnNmOUxzbklUVTAxWXdEL3d6VzdmdjV6bncvTGE0YW1KZUJvZktRU1JVZGlVUFVEWkcxZVBQK0Z6QzFXTVkrZ0lUWGhSTmNnaG5YOU94djAxY1JvR0ptdUh5V1F0V0hxRXFXUGI5NFF1bXlsRVlQWDV0MWsvVWJ5K0JxcmNBMG5iSUpub3RrRVR3TnpaZ1FGb3BFVmtTQjM1dmZNbXNtNmRXMXpoSlJFUUl1L3gwQURRcnBRZWpISXR4NFEydjIxVlQveVdjclhxNG15MWtwSXJsTzdkanl4Q3hCVUJIcW5Jc3ViOEc0NXFpVWkvaGJhblRIQTRQWFdEVkEvcU5UbWV5d3hTTUJiRStrRFQ3TjhxRW9veGVQZFo0Q041b3F6MjRlZ2NqaElFN012RFRoZ3dKa21SUXhkN1Mrdlp3SGk0RHdmdlY5dU1aZDRhOEREKzRPWSt4S3YiLCJtYWMiOiJiNmFlNjJkMDk5YTE2YjhjZmYxNDkwZmIzZDY3YjYwMzM2M2U0NmViNDExZDNkM2E1YThjZmU2NTY5NWRhMzlmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:42:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImErWXJBdEU2Ly9NazhOYjFxOEF3eEE9PSIsInZhbHVlIjoiUVZoUlVEbDg3bjNJc2ZkNlZOb2VndzB0K0N4STR3Sm1uYkkvcVhEbnBhdmVDdVB3bWRMMVRMVzFla0kvNGx5MTd5RlVEcnpBZktXbmdWRlo0ZGRJZFllb3BSV0dqV2krS3BVQ2dDTm91WDNjNGlySExZZXovTVVhRm5CcWNxMndoaDdSUnhyVEtiMWNGbWtkOUk4cVB6WGZXRkpia0lyOWZWYnVMVUNwU0VFc056RzFBYUJPZ0lCdVZxWGh6V2o1TTJaUGZXYUk2YmtNM1JBNXhzclhKaHAvc1FNdE9ZOTZFdHFMMWdYZzJXV0NYRkowSTJkUWpHVEw4dC9yMlVwbmZuVGVPeCt5K0FSZ0xIUEZqOThwMGNiaU1Nenh6SGlucERLYWJqMDJYYllFMzlscDZrMjdKQmx0QUMrVTk2OEtPK08zV3Y3aHZiaW85QjNhUHlpTFhGcjlJSi9vVnFBUW5HV2plcStKU2svZjZ6VWhWV2RmODJldFkvWSs1ZzNNY1MrNW9RZzR6TGt3MDU3d1RvTU42N05nVXB6OFlwNmJBNGdEdGRSOWMrdFVFeUV4WkRxNXhjNDBMbVNVM2MrbHZqTnMrVHVuYW1PLzBVN2ZCRVNCNzg0cUVMNFJVd00zQ1hUdG8zUnBCQTg1T3F0ZGpEeEdNSHloRml1ZFh6dFEiLCJtYWMiOiI4MWRjZDU0ZTg3OTliMTUxYjA5MGRlNjg4OWU3NWRkMTYwMzM5NThiODlmNjUxYTkxNTI3OGI3ZDM0MmNjOWNjIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:42:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFCNzVwbG9jcUJTcUJzRjJVSTRRcnc9PSIsInZhbHVlIjoielNiVE83YTA4OWs5SUoxajBoempYaFc2ZkZGNVlIekNvY2V4R1A5bWFhOUF1MmZocWRrNWI0clFjTlQ0NWxMdjVXcWxCWS90ME80LzJ6czl2Z2V5NG5wb1dIbmdQMW15ZUlDOHJuTHZvSUFpTWE1OWZWM2V3Nkd2Uml0SjZBN3NTWEhRSzVGYVRWTkZuWHVFc1djVEFjZk5QZ2l3aXFZMkFSYnYvTXYxVnNmOUxzbklUVTAxWXdEL3d6VzdmdjV6bncvTGE0YW1KZUJvZktRU1JVZGlVUFVEWkcxZVBQK0Z6QzFXTVkrZ0lUWGhSTmNnaG5YOU94djAxY1JvR0ptdUh5V1F0V0hxRXFXUGI5NFF1bXlsRVlQWDV0MWsvVWJ5K0JxcmNBMG5iSUpub3RrRVR3TnpaZ1FGb3BFVmtTQjM1dmZNbXNtNmRXMXpoSlJFUUl1L3gwQURRcnBRZWpISXR4NFEydjIxVlQveVdjclhxNG15MWtwSXJsTzdkanl4Q3hCVUJIcW5Jc3ViOEc0NXFpVWkvaGJhblRIQTRQWFdEVkEvcU5UbWV5d3hTTUJiRStrRFQ3TjhxRW9veGVQZFo0Q041b3F6MjRlZ2NqaElFN012RFRoZ3dKa21SUXhkN1Mrdlp3SGk0RHdmdlY5dU1aZDRhOEREKzRPWSt4S3YiLCJtYWMiOiJiNmFlNjJkMDk5YTE2YjhjZmYxNDkwZmIzZDY3YjYwMzM2M2U0NmViNDExZDNkM2E1YThjZmU2NTY5NWRhMzlmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:42:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557471775\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}