{"__meta": {"id": "X0c543c7aa98f32359fd893b0c24df711", "datetime": "2025-06-06 19:13:13", "utime": **********.381822, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.840988, "end": **********.381852, "duration": 1.****************, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": **********.840988, "relative_start": 0, "end": **********.163467, "relative_end": **********.163467, "duration": 1.***************, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.163489, "relative_start": 1.****************, "end": **********.381855, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01921, "accumulated_duration_str": "19.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.27196, "duration": 0.01617, "duration_str": "16.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.175}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.32045, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.175, "width_percent": 7.028}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.352266, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.202, "width_percent": 8.798}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijc1ZEt2UzJmSmgzWGVGTXl0K2dVdlE9PSIsInZhbHVlIjoiRGhHWG04QnplZlVwQzhTdXpUZFpaLzduaXVZSm1HNXJuZjA5Rmo2TjIycERpNk4vQ1daZ2lVRHQ3ZU9XRUIvalZyN2NEQk14UVBHaFRmZ3dNYWZlejQ2bk5CN0RiNXpHNEhzKzgxaG5CWk9iSnZtb0NEUEtvR0tJaGMreWlxazJEbDJNVnZDZHJrcklwWW5nbDlUcXA2T0JQWVZoajRpSnJyYmNqdmp3UG53OHYycWtFUU95UjVhVWpOWlk2Z1VWVjdRSXBUaEZDQWZ4ajNTKzBad00zNG1NT2xVSVpaME9qOUhnWU5sL05LalczTnQxNG4wczhZSkp2YW8xZzVhVmhNQkJVcVF0anRNVXJGVXJlc1RnNGJQODJSeW9EY1lxQllqYWtzYUk3Mm54SnQ5N3llcmkzWlJ4b2VsZUpQeDhYRHp3My9KQ20yM2doMEtGcWpUc0VMNTFKWGs2UXdDOFJFTHVoMDJ0NHhHQklXZm9Tbml1R0VhVG5EMUluZzNrdDRPNDE4dU54Vnduekc4YXpTTHN5MXJEMHg1Y0JuajNrU1NxWkpwTFlOd0NCOFhEOFp0VnBHamNGYUVic0R6VWo3QVBpRzJaMkkvMzdrak9aQ3crUnpDdjk4WmpYMUx5dHZkc2RjUkhkT05tbTJZWHpucWtsUHc2ZHlIbWdxRzUiLCJtYWMiOiI5Zjg2NzhmNzZkNWI1NmNjZTU4N2JjMmFhMzI0Yjc0ZjIyMzFkMWFmOWZmMzQ4ZWU1YmZhNjg2NjkxZWNlYzJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlrOTVhRThCSjdoaktBdTloS3J0ekE9PSIsInZhbHVlIjoiMGFJd2dCUUdUZmVjOHRpaWcyOXBibTB2VnErWW8zazVEVmY0OHBpUUVmejJLWWdSNGUxVG1Qd0Zvb3pGNnR6bkFOVlFUWGtpYWt3TzVhYjA1LzFyTkRUSk8vQ2JvRlBXVEZEZ0NqOU1SczdHRUZ3QkpsaFFablpLR2JlRDIzVlZ3S05vLzFVbFhMSVhvYThjTUxrSnFKZnllRlpFUmIvU0IrK3ExY0xiMFRPRkJHL2xhTFd0Zk5JbkhHbmlIK3JRVnZRSnA5QnFUZWlZcEFQbUFIdTBDcFVna1d6ZlBjc2NJNHJ1S2hsWmVpMUZYcXV5cE94QkwzQXVZQTRDWjY0YWNwQ0Q3L0xZVHJsUXRDMHdSRHZQUi9WT0x6WmplVm1UZjd3N3JmeXZuU3VWdWxaWk9hRlVndmpJTHNsQm41QVdoQXlOdUdxSDlNb1BJY0preE9GdWJ5U2cvdTY1SUdHeWQralNnVGswNWpMbU9wU2hySyt5ZlI0MnhpVHJOVHdia2h0UUM5cXd6b1ozeVBGVmFQdHFLdTlKdGRBNUdxUDJUWEtmRjR6YjVQUlU1RDVGL3BDNjVvSjJMalFpS1lOQ1hybDc2R3BkaU9OUkJqL3JoUnpYUWVRMWtzWkdxeFdIcHZrSHlncjRmbHoyMzZkdTI3TGVaMzNPbFV5VHc3NnUiLCJtYWMiOiJiMDMwZWEyZWE4YjRhY2JlZDEzNTczODkxZmEzOGE2ZDcyYzRiNjNmYTBkZmZlNTViODg3Yzk1MmVkNzViMTk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PVt0h9icZb5VqFqziyPToWE7TqW2lYjvh7uvnOnw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2062595058 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNDOGRsTmxicUVSZndqdk9GK252S3c9PSIsInZhbHVlIjoiMlRUc0JqWWJwYUpSTGpEQTRQSFVzSE5YN0tTS3pJUnBoVnQ3R004SFFnT0VXNXFnaW1RNUc1SVBoWG13U1JWRWs2V05KdWZWRGpGVVVLMkZucHBjeWNmQnRCUVdBK2JyMnVGZ25LWVJZNmN6YUVjMkZnQ0ZUcHhWY1M0amtVSWd3Y1FWVC9iWEtwL1l2aXR1R2I2UGhScEg1b1R4V1RoWVBkOWJITWtTSVdwcG5yRzJIOGFLdUp2ZlZOeHpjeFIvM0x4eVBGSk9udGsrMzdad0JYdTBKYnVaYzhES002MEZ2a29OSEdYY0Q3WnREdXc5TzAwL0ZPY2pwYjlQeXVQMDRqMDdCOExRbWZlbE93WEJYemNzTGRrdm1OVHQ5Q0FuUUpCZlFZVlBnV3B3TGxWNS9FV3NrV1dkZVFjZGVLS0tlMHBpbk85Ym1xYjh3YTB4MGk3Mk0xbzFDWk5BZEFHK1FJY1RpdW1qOUY4ZHlubkNSa0w3SlYxdW5LaFJGakRHOUMzeDRCZnF6UWxEeVVhWDJMT1gxelp2aGp6d2htUGVRczZmRXoxRk9YbER4ZHZWT2hXY1p4Q1dIaGZZa1JUVFFJbUJkQkZaTVFFbTRyZ1gxenVUQ0FpRGhWa0ZxZWtOWTNzbGJQV1pORnRYSUNBV0kyeHFWWTA1VzVkUGtSYWUiLCJtYWMiOiI4NzE2OGEwMjRiMzkwMWQ4ODkwMTdjMDlkOWM5OTU5ODRiZDJlOGZiZTUyNzhmYWY3Y2Q3ODM1N2Q2NWQzNmU5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9iTUs3UEVuT21Dd3VjK2p5VTdLY0E9PSIsInZhbHVlIjoiOFZHRUI5eFByeG16UVlnU3drSWx1dktiK0xaQTZvUEhqa0FzZVp3UHZubTV4L2FHT0g4cVp4WWE5bjRkR2RQZ2RyczlqWS9HVVdhbFRBMVJ0RVdEZzZwTTNaNmtGTnNsNXlHT1RDUk52dFl1bGYyVkF2VDJLRjMxeTJyVUUyclJhdkYySC83U01MbXIrUG03RjdsTlJNSVJwcnlQWnBVVU55RDlDbm53LzU3UnU3NDFYelRzWWdZdmEyR1VtODJuVTltSE1GMlVLODI4Qzc3NVRwYjVDOXZZYW5Pb1lYNlJRdVM2WisvQmRta3Z3RGh6ekI0T1dMcGw3Y3A0eTg0WFFVNUp0YUJmZkVZY29ySHFZNmFlZWtXZWN0NDNYaHNnODFrSUNQT3FtcG9zREcxeHlVa3hOTVFQWUZZdkRZOHdpT1oxcWk4S2JaUVdwelpGMGxSbG00VzJoMGFxVU5NZmdZWU5sMmhiRlVQNmlkZnkwbVd0L1o4Z29YNVVyenUyNzQ5L0U2ZUhjOHJpVzdEK3ZRWGpFekZCaUtkeGZOZEtrSDhEQnd1MytPYlZOUDdyYjZadWhyelZwNU1qdWlmbUlUU1IvbnYraFMrakJQMzhHcDJtNGtodCtwT3lyTTFySHZjVE1iaCtlaUtSN1lES3dRY2crS0FpZXMvOTE1SDAiLCJtYWMiOiIxNzJiN2RjYWM1MDk0YTY3YjhjOTc0YTFhMzZkNzliZmMwMGQzNmE3NTA1ODJjYzgwYmEwMTFjNjU3MGY4M2JiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNDOGRsTmxicUVSZndqdk9GK252S3c9PSIsInZhbHVlIjoiMlRUc0JqWWJwYUpSTGpEQTRQSFVzSE5YN0tTS3pJUnBoVnQ3R004SFFnT0VXNXFnaW1RNUc1SVBoWG13U1JWRWs2V05KdWZWRGpGVVVLMkZucHBjeWNmQnRCUVdBK2JyMnVGZ25LWVJZNmN6YUVjMkZnQ0ZUcHhWY1M0amtVSWd3Y1FWVC9iWEtwL1l2aXR1R2I2UGhScEg1b1R4V1RoWVBkOWJITWtTSVdwcG5yRzJIOGFLdUp2ZlZOeHpjeFIvM0x4eVBGSk9udGsrMzdad0JYdTBKYnVaYzhES002MEZ2a29OSEdYY0Q3WnREdXc5TzAwL0ZPY2pwYjlQeXVQMDRqMDdCOExRbWZlbE93WEJYemNzTGRrdm1OVHQ5Q0FuUUpCZlFZVlBnV3B3TGxWNS9FV3NrV1dkZVFjZGVLS0tlMHBpbk85Ym1xYjh3YTB4MGk3Mk0xbzFDWk5BZEFHK1FJY1RpdW1qOUY4ZHlubkNSa0w3SlYxdW5LaFJGakRHOUMzeDRCZnF6UWxEeVVhWDJMT1gxelp2aGp6d2htUGVRczZmRXoxRk9YbER4ZHZWT2hXY1p4Q1dIaGZZa1JUVFFJbUJkQkZaTVFFbTRyZ1gxenVUQ0FpRGhWa0ZxZWtOWTNzbGJQV1pORnRYSUNBV0kyeHFWWTA1VzVkUGtSYWUiLCJtYWMiOiI4NzE2OGEwMjRiMzkwMWQ4ODkwMTdjMDlkOWM5OTU5ODRiZDJlOGZiZTUyNzhmYWY3Y2Q3ODM1N2Q2NWQzNmU5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9iTUs3UEVuT21Dd3VjK2p5VTdLY0E9PSIsInZhbHVlIjoiOFZHRUI5eFByeG16UVlnU3drSWx1dktiK0xaQTZvUEhqa0FzZVp3UHZubTV4L2FHT0g4cVp4WWE5bjRkR2RQZ2RyczlqWS9HVVdhbFRBMVJ0RVdEZzZwTTNaNmtGTnNsNXlHT1RDUk52dFl1bGYyVkF2VDJLRjMxeTJyVUUyclJhdkYySC83U01MbXIrUG03RjdsTlJNSVJwcnlQWnBVVU55RDlDbm53LzU3UnU3NDFYelRzWWdZdmEyR1VtODJuVTltSE1GMlVLODI4Qzc3NVRwYjVDOXZZYW5Pb1lYNlJRdVM2WisvQmRta3Z3RGh6ekI0T1dMcGw3Y3A0eTg0WFFVNUp0YUJmZkVZY29ySHFZNmFlZWtXZWN0NDNYaHNnODFrSUNQT3FtcG9zREcxeHlVa3hOTVFQWUZZdkRZOHdpT1oxcWk4S2JaUVdwelpGMGxSbG00VzJoMGFxVU5NZmdZWU5sMmhiRlVQNmlkZnkwbVd0L1o4Z29YNVVyenUyNzQ5L0U2ZUhjOHJpVzdEK3ZRWGpFekZCaUtkeGZOZEtrSDhEQnd1MytPYlZOUDdyYjZadWhyelZwNU1qdWlmbUlUU1IvbnYraFMrakJQMzhHcDJtNGtodCtwT3lyTTFySHZjVE1iaCtlaUtSN1lES3dRY2crS0FpZXMvOTE1SDAiLCJtYWMiOiIxNzJiN2RjYWM1MDk0YTY3YjhjOTc0YTFhMzZkNzliZmMwMGQzNmE3NTA1ODJjYzgwYmEwMTFjNjU3MGY4M2JiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062595058\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2044096044 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044096044\", {\"maxDepth\":0})</script>\n"}}