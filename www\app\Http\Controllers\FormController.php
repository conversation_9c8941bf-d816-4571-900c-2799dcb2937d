<?php

namespace App\Http\Controllers;

use App\Models\Form;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class FormController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $forms = Form::getVisibleForms();
        return view('forms.index', compact('forms'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Only company users can create forms
        if (Auth::user()->type != 'company') {
            return redirect()->back()->with('error', 'غير مصرح لك بإنشاء النماذج');
        }

        return view('forms.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Only company users can create forms
        if (Auth::user()->type != 'company') {
            return redirect()->back()->with('error', 'غير مصرح لك بإنشاء النماذج');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|in:operational,financial',
            'file' => 'required|file|mimes:pdf|max:102400', // 100MB max
            'visible_to_roles' => 'required|array|min:1',
            'visible_to_roles.*' => 'in:accountant,delivery,SUPER FIESR,SUPER FIESR BIG,Cashier,all'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Upload file
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('forms', $fileName, 'public');

            // Create form record
            Form::create([
                'name' => $request->name,
                'type' => $request->type,
                'file_path' => $filePath,
                'visible_to_roles' => $request->visible_to_roles,
                'created_by' => Auth::id()
            ]);

            return redirect()->route('dashboard')
                ->with('success', 'تم إنشاء النموذج بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء النموذج: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Form $form)
    {
        // Check if user can view this form
        if (!$form->canUserView()) {
            return redirect()->back()->with('error', 'غير مصرح لك بعرض هذا النموذج');
        }

        return response()->file(storage_path('app/public/' . $form->file_path));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Form $form)
    {
        // Only creator or company users can delete
        if (Auth::user()->type != 'company' && $form->created_by !== Auth::id()) {
            return redirect()->back()->with('error', 'غير مصرح لك بحذف هذا النموذج');
        }

        try {
            // Delete file
            if (Storage::disk('public')->exists($form->file_path)) {
                Storage::disk('public')->delete($form->file_path);
            }

            // Delete record
            $form->delete();

            return redirect()->back()->with('success', 'تم حذف النموذج بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف النموذج');
        }
    }
}
