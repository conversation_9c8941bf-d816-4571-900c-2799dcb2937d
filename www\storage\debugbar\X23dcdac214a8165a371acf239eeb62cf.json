{"__meta": {"id": "X23dcdac214a8165a371acf239eeb62cf", "datetime": "2025-06-06 19:26:33", "utime": **********.08386, "method": "GET", "uri": "/user/15/plan/2", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237991.39782, "end": **********.083887, "duration": 1.6860671043395996, "duration_str": "1.69s", "measures": [{"label": "Booting", "start": 1749237991.39782, "relative_start": 0, "end": **********.754579, "relative_end": **********.754579, "duration": 1.3567590713500977, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.754607, "relative_start": 1.3567869663238525, "end": **********.08389, "relative_end": 2.86102294921875e-06, "duration": 0.3292829990386963, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45130392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET user/{id}/plan/{pid}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\UserController@activePlan", "namespace": null, "prefix": "", "where": [], "as": "plan.active", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=537\" onclick=\"\">app/Http/Controllers/UserController.php:537-573</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.04104000000000001, "accumulated_duration_str": "41.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.865221, "duration": 0.02191, "duration_str": "21.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 53.387}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9255528, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 53.387, "width_percent": 2.68}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.961462, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 56.067, "width_percent": 4.215}, {"sql": "select * from `plans` where `plans`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 540}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.972918, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "UserController.php:540", "source": "app/Http/Controllers/UserController.php:540", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=540", "ajax": false, "filename": "UserController.php", "line": "540"}, "connection": "ty", "start_percent": 60.283, "width_percent": 2.632}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 546}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.980982, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "UserController.php:546", "source": "app/Http/Controllers/UserController.php:546", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=546", "ajax": false, "filename": "UserController.php", "line": "546"}, "connection": "ty", "start_percent": 62.914, "width_percent": 2.193}, {"sql": "select * from `plans` where `plans`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\app\\Models\\User.php", "line": 234}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 547}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9890552, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "User.php:234", "source": "app/Models/User.php:234", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=234", "ajax": false, "filename": "User.php", "line": "234"}, "connection": "ty", "start_percent": 65.107, "width_percent": 2.388}, {"sql": "update `users` set `plan` = 2, `users`.`updated_at` = '2025-06-06 19:26:32' where `id` = 15", "type": "query", "params": [], "bindings": ["2", "2025-06-06 19:26:32", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\app\\Models\\User.php", "line": 249}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 547}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.998246, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "User.php:249", "source": "app/Models/User.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=249", "ajax": false, "filename": "User.php", "line": "249"}, "connection": "ty", "start_percent": 67.495, "width_percent": 8.577}, {"sql": "select * from `users` where `created_by` = '15' and `type` != 'super admin' and `type` != 'company' and `type` != 'client'", "type": "query", "params": [], "bindings": ["15", "super admin", "company", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\app\\Models\\User.php", "line": 258}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 547}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.01023, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "User.php:258", "source": "app/Models/User.php:258", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=258", "ajax": false, "filename": "User.php", "line": "258"}, "connection": "ty", "start_percent": 76.072, "width_percent": 3.289}, {"sql": "select * from `users` where `created_by` = '15' and `type` = 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\app\\Models\\User.php", "line": 259}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 547}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.017597, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "User.php:259", "source": "app/Models/User.php:259", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=259", "ajax": false, "filename": "User.php", "line": "259"}, "connection": "ty", "start_percent": 79.362, "width_percent": 2.51}, {"sql": "select * from `customers` where `created_by` = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\app\\Models\\User.php", "line": 260}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 547}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.029111, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "User.php:260", "source": "app/Models/User.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=260", "ajax": false, "filename": "User.php", "line": "260"}, "connection": "ty", "start_percent": 81.871, "width_percent": 3.387}, {"sql": "select * from `venders` where `created_by` = '15'", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\app\\Models\\User.php", "line": 261}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 547}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.041611, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "User.php:261", "source": "app/Models/User.php:261", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=261", "ajax": false, "filename": "User.php", "line": "261"}, "connection": "ty", "start_percent": 85.258, "width_percent": 3.411}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\to\\app\\Models\\User.php", "line": 840}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 560}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.050687, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "User.php:840", "source": "app/Models/User.php:840", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=840", "ajax": false, "filename": "User.php", "line": "840"}, "connection": "ty", "start_percent": 88.67, "width_percent": 2.973}, {"sql": "insert into `orders` (`order_id`, `name`, `card_number`, `card_exp_month`, `card_exp_year`, `plan_name`, `plan_id`, `price`, `price_currency`, `txn_id`, `payment_status`, `receipt`, `user_id`, `updated_at`, `created_at`) values ('684340E90C329708113853', '', '', '', '', 'متاجر البقالة   و البيع بالتجزئة', 2, '0.00', '', '', 'success', '', 15, '2025-06-06 19:26:33', '2025-06-06 19:26:33')", "type": "query", "params": [], "bindings": ["684340E90C329708113853", "", "", "", "", "متاجر البقالة   و البيع بالتجزئة", "2", "0.00", "", "", "success", "", "15", "2025-06-06 19:26:33", "2025-06-06 19:26:33"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 550}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.059838, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "UserController.php:550", "source": "app/Http/Controllers/UserController.php:550", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=550", "ajax": false, "filename": "UserController.php", "line": "550"}, "connection": "ty", "start_percent": 91.642, "width_percent": 8.358}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Plan": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/user/15/plan/2\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "success": "Plan successfully upgraded.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user/15/plan/2", "status_code": "<pre class=sf-dump id=sf-dump-700028781 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-700028781\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1385298377 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1385298377\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1685581677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1685581677\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1194904387 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237988999%7C26%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJnN2wzV0xob1BmSHJXcjFqdG1kUHc9PSIsInZhbHVlIjoiMTJ6aHBydzV3K2N4bnY4alJjaksyQjZGQ0VYZS9sU2hOdWV6NktQTlR0QTFjbm8wZkg1MUxqaWpyOTJLOTgvdkJVRmJzNjluN1pLdUNDcGNheStoVWpzQnNiOW5mQ1ZFR2JXVVZpWGlHZGdZSzJCQTRGMHR0akN6Q0dDNldsa1Z2eXU1OS9SWTFxeGFQUE9kdWpMZ2xGUnZpUVZmNkUySXMwSkxBY0RWOTA3NSt3dlUySHNQeWhCcCtCdTRLRHFZQU5PK1JmaUJZWWYwRGo2cVMvVm5yaVd2aEhXQ3g4WHloSEszdmQ2dEt6ZTZJcVJzVWFJM3RpTmkyOFZzSnhQWWU2NmEzRlFhcUtHQTVHcmJ1SnVObUxvS0g2ZldUbEc4R09sSTZRb3J6a09VS3p4NVF3VnhjM1RpMnNhL1NGNnloYWk3NW14QzhnQ0J6elBRVXR4ZFcwc3FDQ1orQW9ZNlBrejFGd29LdUxORk9PUFVZVHl3dmhmS3JoYll0UWY3cnhhUFYwZStobnJTQnpoT2FzUGNTMXQ3bVk5cU5MbVRqNExXWHU5RHBSVGoveGphaVoxcUl1UDMwanZ1RW16VnMwVHd3bnUzNy9yOEdJRVRtU0E2U1Brc2ViMDhITkdLY0JpdGg1VXJSR0xzTzUrZFJ0Q29Db1c0TWZRMVI0c0EiLCJtYWMiOiJmNDlhZGNmMDM1N2E5OWViOGQwNGI1ZTY1NDExODliNzUxOGU4NTAyODc2NjIxOWJkZGE5ZTJjOTBlZTIyMmM2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktYZE5uNFN1TGZUbEcyZnBmcHc3ZlE9PSIsInZhbHVlIjoiSnh0V2dGeGh3ZlkvZTA0TGU4WnV3NHJQODA0OTRrZzZsbDV4TFZidlpkaExmdkdUelVJNlBFZWduMFd0ZjhJYy9hbmVGVk1lc01BWHY3V2xlRXpyYm9JMFRXeFlQLzMrZEQrdC90YXVLa2g2dm84QlZUaHAvRU1UMUtJdlJzTGVSMGhKK2dscW9XZjVTZ1d4V3hTSjY3ejU5OHV3ZUZwb0NQbjVxWXErRFk0UWJEKzdxcEpxYzZhQng2MElhR1pjalpMb0IzdGd4Q2R6dkVSUlJQdllPRnBtMlBiQmkyQzFNT2p3VkhmOFgxNmx2Rkg5ZEQydXp4NnNIck5YRllvM3ZrYWNtSGkrR05NbFBpcXVWMXhyR2hMSGVSQ2o2SVZaa1RZRGJKZUFsL0J4ZUduVmQveEdPdC91NndFaEh6OVpoQ0xkRHZiMzF0SG5tY2IzaUdCdjhOZlpvVzZxTkN2dU1xcVlRbXB6V0NBTElMaFIranJPajhxSU84eGJxU0xoeWIvY0pGT2ZLOVF1V2lOMjFVQmE3N2NPNG10TktFWDI2YXJ3dXNtQTNFNzVydmhlZ25pbXgyaldxcFhHQU9oK3lCUVJFQU1KLzJVakRxT1ZjQU9qTFR1MGF3Q3VBUlVqaGVtOHhPdmdrRDllNGNoM2hScmZnVmcwZmsyNFl2elEiLCJtYWMiOiI2MDE3N2E4ZDUyZGUyZTcyNTcyYWY1ZTQyZTFlMWE4MjM3Nzk0NTMwYzgxNTM0MTdkMjI4NDJhY2FhODhhNDYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194904387\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-678861819 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-678861819\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-493708598 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:26:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkgvcFV6SEQrand0azNuUERTQ24vTFE9PSIsInZhbHVlIjoibCs0VHJOaEQxdTRhaXRQcTdoQnB4UFhWeGk3eGNabTBCdlJHTUNKM0J6U2ZCblhvVUxWMnZpV0xOQlJkY0k0Nk85NFBzVjRlWjlNMmRhUVk4YWFEMjFwQjFZa1JTQVZ0aTZSMExLQUFsMjhkZzU4QU93dUdoNlZueUZ3N2U4cnhRc3pJbXVvS3lDTUczS0VJNFhpcWdNWDhnZXBpcUI5dXNCQnVjd3I5SUhaUVluZ1VHalFGVTdWbElwYUhCT0ZSOVFQYURtNFF3WURTYUtTaE53Qk03OW9iVFYrNUEzcktYb2RxWXJaKzZjT05OM1piMExTWGRmeWdiUDhNbDBGZGlXemR5ZUovaDJQcDhLTlE3MjFXOS9uNXFmOTlGdnJpRmhERWN6ZVJUZjNHY2czcUZmUlBOV2RjYjBQMk40WVZjWXR3WEx2anF0MWJhOWhxUUpSZDF3R3JrVDZPL3VVUkp5MDNEQVFJTnlBbi9US2pHcVZLS29Cc1U0Z3pRTDJDa2lId3JtTnhJMW1ZNzZuZEpJR0FWb3NTM2JFU3FaYkhwUXU5ME1KREwvc0RiekxkbU4wZEhReGtkcVFlUTEzRUZBcTZvQ1FoTU1iYnNNc1hlQnN6SkhkQnh3eFpMZFN2Yy9DdlBUQnZIZ3VlVDRwNEtFMkI0Z0t1MWJndk5JOUIiLCJtYWMiOiI5OTNjNzJjZjZiZDcwYzlhMjA5MGY3ZjcxYzRhNTVjZmZiNTgzODRiMGI1ZmZhZDY2Mjg5NmYxMDQ5YjIzYTVmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImV3MWVvNEpnMFdJU1pCdm44SzNEb2c9PSIsInZhbHVlIjoiS05UZjhJR2pES1FBTklCd1l6SFJGTSt2bXFxMDdJb1FuNlp0RHF0NUtrblUxSXQ3SDk5WXVRaTVVQXBzYk54c01wdm5tRVoxbSthTE1aV2EyaGd2NkVTSHpNZ2tJYVM2L1RINXhjSjdlbmVreEFiUmppZ1pWam82MUNHRWprR2N5QVd2cDVMd2svT2pHbzRQZmxCRk9JOEUrY2ZxY3QreGpPelJXY1Vja01DbEV6eGs4MmhOSWNyd1c5bGVEdldLUy9jN2Z1dEw4NklRMVFvRHVzRzg0V25xZktlN1g3WG12T1owR0ZZTloydGFXa2Fia2xFMTJobHFaaElOU3BYYmtNTFdRTlZEeFYyK0F3d2tMbyttTno0Qnl5ZWYvK2c3YkZHWlRyWkhTSzY3RHFJd1hrcElVT0R1Q0FHYld6Z3Y3VG1Jc1dHK3pENFFLcmlwRnR3R3ZKdEh4ODNZR2cxUTRxcHhrSEVRbDhjSkRpZGdTK3JtalB5dndNRWxBS3JObUcvN1BaZUt4eGplR1pCTElIVW5VTTBLcm9sTzFuQytvMDlob0lXb2RGUDcreWhrVThwbVhnaDU3V0d5Yi9leGROSkUreFZsQW5hQXZFbi9keEtoekoxeDJwSWR1WVRSa0h6U2gyVG51MVFEQ1NGN21PYzhGUkU2c05udTlsYjUiLCJtYWMiOiI3NTIxYTNiZDczZGVhODdlYjliMDk2YmQ0YjllYjMyMDc4NGM2YTZmYjIxNzViMmE1YmQzZTZlMGE2Y2VmYTk4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkgvcFV6SEQrand0azNuUERTQ24vTFE9PSIsInZhbHVlIjoibCs0VHJOaEQxdTRhaXRQcTdoQnB4UFhWeGk3eGNabTBCdlJHTUNKM0J6U2ZCblhvVUxWMnZpV0xOQlJkY0k0Nk85NFBzVjRlWjlNMmRhUVk4YWFEMjFwQjFZa1JTQVZ0aTZSMExLQUFsMjhkZzU4QU93dUdoNlZueUZ3N2U4cnhRc3pJbXVvS3lDTUczS0VJNFhpcWdNWDhnZXBpcUI5dXNCQnVjd3I5SUhaUVluZ1VHalFGVTdWbElwYUhCT0ZSOVFQYURtNFF3WURTYUtTaE53Qk03OW9iVFYrNUEzcktYb2RxWXJaKzZjT05OM1piMExTWGRmeWdiUDhNbDBGZGlXemR5ZUovaDJQcDhLTlE3MjFXOS9uNXFmOTlGdnJpRmhERWN6ZVJUZjNHY2czcUZmUlBOV2RjYjBQMk40WVZjWXR3WEx2anF0MWJhOWhxUUpSZDF3R3JrVDZPL3VVUkp5MDNEQVFJTnlBbi9US2pHcVZLS29Cc1U0Z3pRTDJDa2lId3JtTnhJMW1ZNzZuZEpJR0FWb3NTM2JFU3FaYkhwUXU5ME1KREwvc0RiekxkbU4wZEhReGtkcVFlUTEzRUZBcTZvQ1FoTU1iYnNNc1hlQnN6SkhkQnh3eFpMZFN2Yy9DdlBUQnZIZ3VlVDRwNEtFMkI0Z0t1MWJndk5JOUIiLCJtYWMiOiI5OTNjNzJjZjZiZDcwYzlhMjA5MGY3ZjcxYzRhNTVjZmZiNTgzODRiMGI1ZmZhZDY2Mjg5NmYxMDQ5YjIzYTVmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImV3MWVvNEpnMFdJU1pCdm44SzNEb2c9PSIsInZhbHVlIjoiS05UZjhJR2pES1FBTklCd1l6SFJGTSt2bXFxMDdJb1FuNlp0RHF0NUtrblUxSXQ3SDk5WXVRaTVVQXBzYk54c01wdm5tRVoxbSthTE1aV2EyaGd2NkVTSHpNZ2tJYVM2L1RINXhjSjdlbmVreEFiUmppZ1pWam82MUNHRWprR2N5QVd2cDVMd2svT2pHbzRQZmxCRk9JOEUrY2ZxY3QreGpPelJXY1Vja01DbEV6eGs4MmhOSWNyd1c5bGVEdldLUy9jN2Z1dEw4NklRMVFvRHVzRzg0V25xZktlN1g3WG12T1owR0ZZTloydGFXa2Fia2xFMTJobHFaaElOU3BYYmtNTFdRTlZEeFYyK0F3d2tMbyttTno0Qnl5ZWYvK2c3YkZHWlRyWkhTSzY3RHFJd1hrcElVT0R1Q0FHYld6Z3Y3VG1Jc1dHK3pENFFLcmlwRnR3R3ZKdEh4ODNZR2cxUTRxcHhrSEVRbDhjSkRpZGdTK3JtalB5dndNRWxBS3JObUcvN1BaZUt4eGplR1pCTElIVW5VTTBLcm9sTzFuQytvMDlob0lXb2RGUDcreWhrVThwbVhnaDU3V0d5Yi9leGROSkUreFZsQW5hQXZFbi9keEtoekoxeDJwSWR1WVRSa0h6U2gyVG51MVFEQ1NGN21PYzhGUkU2c05udTlsYjUiLCJtYWMiOiI3NTIxYTNiZDczZGVhODdlYjliMDk2YmQ0YjllYjMyMDc4NGM2YTZmYjIxNzViMmE1YmQzZTZlMGE2Y2VmYTk4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493708598\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1937164962 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/user/15/plan/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Plan successfully upgraded.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937164962\", {\"maxDepth\":0})</script>\n"}}