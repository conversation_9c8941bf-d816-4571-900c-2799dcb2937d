<?php
/**
 * اختبار سريع لتشخيص مشكلة الطباعة
 * ضع هذا الملف في مجلد public واستدعه من المتصفح
 */

// إعدادات قاعدة البيانات - عدل هذه القيم حسب إعداداتك
$host = 'localhost';
$dbname = 'your_database_name';
$username = 'your_username';
$password = 'your_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>🔍 تشخيص مشكلة الطباعة</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .code { background: #f5f5f5; padding: 10px; margin: 10px 0; }
    </style>";

    // 1. فحص أوامر الاستلام
    echo "<div class='section'>";
    echo "<h2>1. فحص أوامر الاستلام</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM receipt_orders");
    $orderCount = $stmt->fetch()['count'];
    echo "<div class='info'>📊 إجمالي أوامر الاستلام: $orderCount</div>";
    
    if ($orderCount > 0) {
        $stmt = $pdo->query("
            SELECT id, order_number, order_type, created_by, 
                   warehouse_id, vendor_id, created_at
            FROM receipt_orders 
            ORDER BY id DESC 
            LIMIT 5
        ");
        
        echo "<h3>آخر 5 أوامر:</h3>";
        echo "<table>";
        echo "<tr><th>ID</th><th>رقم الأمر</th><th>النوع</th><th>المنشئ</th><th>المستودع</th><th>المورد</th><th>التاريخ</th></tr>";
        
        while ($row = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['order_number']}</td>";
            echo "<td>{$row['order_type']}</td>";
            echo "<td>{$row['created_by']}</td>";
            echo "<td>{$row['warehouse_id']}</td>";
            echo "<td>{$row['vendor_id']}</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    // 2. فحص المستخدمين
    echo "<div class='section'>";
    echo "<h2>2. فحص المستخدمين</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    echo "<div class='info'>📊 إجمالي المستخدمين: $userCount</div>";
    
    if ($userCount > 0) {
        $stmt = $pdo->query("
            SELECT id, name, email, type, created_by
            FROM users 
            ORDER BY id ASC 
            LIMIT 10
        ");
        
        echo "<h3>عينة من المستخدمين:</h3>";
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>النوع</th><th>المنشئ</th></tr>";
        
        while ($row = $stmt->fetch()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "<td>{$row['type']}</td>";
            echo "<td>{$row['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    // 3. فحص العلاقة بين الأوامر والمستخدمين
    echo "<div class='section'>";
    echo "<h2>3. فحص العلاقة بين الأوامر والمستخدمين</h2>";
    
    $stmt = $pdo->query("
        SELECT ro.id, ro.order_number, ro.created_by, u.name as creator_name
        FROM receipt_orders ro
        LEFT JOIN users u ON ro.created_by = u.id
        ORDER BY ro.id DESC
        LIMIT 10
    ");
    
    echo "<h3>أوامر مع أسماء المنشئين:</h3>";
    echo "<table>";
    echo "<tr><th>ID الأمر</th><th>رقم الأمر</th><th>ID المنشئ</th><th>اسم المنشئ</th><th>الحالة</th></tr>";
    
    while ($row = $stmt->fetch()) {
        $status = $row['creator_name'] ? 'موجود' : 'مفقود';
        $statusClass = $row['creator_name'] ? 'success' : 'error';
        
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['order_number']}</td>";
        echo "<td>{$row['created_by']}</td>";
        echo "<td>{$row['creator_name']}</td>";
        echo "<td class='$statusClass'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";

    // 4. فحص أوامر بدون منشئ
    echo "<div class='section'>";
    echo "<h2>4. فحص أوامر بدون منشئ</h2>";
    
    $stmt = $pdo->query("
        SELECT ro.id, ro.order_number, ro.created_by
        FROM receipt_orders ro
        LEFT JOIN users u ON ro.created_by = u.id
        WHERE u.id IS NULL
        LIMIT 10
    ");
    
    $orphanOrders = $stmt->fetchAll();
    
    if (count($orphanOrders) > 0) {
        echo "<div class='warning'>⚠️ وُجدت " . count($orphanOrders) . " أوامر بدون منشئ:</div>";
        echo "<table>";
        echo "<tr><th>ID الأمر</th><th>رقم الأمر</th><th>ID المنشئ المفقود</th></tr>";
        
        foreach ($orphanOrders as $order) {
            echo "<tr>";
            echo "<td>{$order['id']}</td>";
            echo "<td>{$order['order_number']}</td>";
            echo "<td>{$order['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='info'>💡 هذه الأوامر تحتاج لإصلاح created_by</div>";
    } else {
        echo "<div class='success'>✅ جميع الأوامر لها منشئ صحيح</div>";
    }
    echo "</div>";

    // 5. اقتراح الإصلاح
    echo "<div class='section'>";
    echo "<h2>5. اقتراح الإصلاح</h2>";
    
    if (count($orphanOrders) > 0) {
        // البحث عن أول مستخدم
        $stmt = $pdo->query("SELECT id, name FROM users ORDER BY id ASC LIMIT 1");
        $firstUser = $stmt->fetch();
        
        if ($firstUser) {
            echo "<div class='info'>💡 يمكن إصلاح الأوامر المعطلة بربطها بالمستخدم الأول:</div>";
            echo "<div class='code'>";
            echo "المستخدم الأول: ID = {$firstUser['id']}, الاسم = {$firstUser['name']}<br>";
            echo "<br>SQL للإصلاح:<br>";
            echo "UPDATE receipt_orders SET created_by = {$firstUser['id']} WHERE created_by NOT IN (SELECT id FROM users);";
            echo "</div>";
        }
    }
    
    echo "<div class='info'>🔧 خطوات الإصلاح في Laravel:</div>";
    echo "<div class='code'>";
    echo "1. تحديث الكونترولر لمعالجة المستخدمين المفقودين ✅<br>";
    echo "2. تحديث صفحات العرض لمعالجة القيم الفارغة ✅<br>";
    echo "3. إصلاح البيانات المعطلة في قاعدة البيانات (اختياري)<br>";
    echo "</div>";
    echo "</div>";

    // 6. اختبار URL
    echo "<div class='section'>";
    echo "<h2>6. اختبار الروابط</h2>";
    
    if ($orderCount > 0) {
        $stmt = $pdo->query("SELECT id FROM receipt_orders ORDER BY id DESC LIMIT 1");
        $lastOrder = $stmt->fetch();
        
        if ($lastOrder) {
            $orderId = $lastOrder['id'];
            echo "<div class='info'>🔗 اختبر هذه الروابط:</div>";
            echo "<div class='code'>";
            echo "عرض الأمر: <a href='/receipt-order/$orderId' target='_blank'>/receipt-order/$orderId</a><br>";
            echo "طباعة الأمر: <a href='/receipt-order/$orderId?print=1' target='_blank'>/receipt-order/$orderId?print=1</a><br>";
            echo "</div>";
        }
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>✅ انتهاء التشخيص</h2>";
    echo "<div class='success'>تم إجراء جميع اختبارات التشخيص!</div>";
    echo "<div class='info'>💡 إذا كانت المشكلة مستمرة، تحقق من:</div>";
    echo "<ul>";
    echo "<li>ملفات اللوج في storage/logs/laravel.log</li>";
    echo "<li>أخطاء PHP في لوج الخادم</li>";
    echo "<li>أخطاء JavaScript في Console المتصفح</li>";
    echo "</ul>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    echo "<div class='info'>💡 تأكد من إعدادات قاعدة البيانات في بداية هذا الملف</div>";
}
?>
