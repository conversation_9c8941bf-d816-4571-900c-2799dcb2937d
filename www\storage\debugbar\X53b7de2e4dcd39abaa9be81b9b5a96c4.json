{"__meta": {"id": "X53b7de2e4dcd39abaa9be81b9b5a96c4", "datetime": "2025-06-06 19:15:16", "utime": **********.871038, "method": "GET", "uri": "/login-with-company/exit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237315.497506, "end": **********.871068, "duration": 1.3735620975494385, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749237315.497506, "relative_start": 0, "end": **********.715753, "relative_end": **********.715753, "duration": 1.2182471752166748, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.715774, "relative_start": 1.2182681560516357, "end": **********.871071, "relative_end": 3.0994415283203125e-06, "duration": 0.15529704093933105, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43522744, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login-with-company/exit", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@ExitCompany", "namespace": null, "prefix": "", "where": [], "as": "exit.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=669\" onclick=\"\">app/Http/Controllers/UserController.php:669-673</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.019930000000000003, "accumulated_duration_str": "19.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.810563, "duration": 0.018920000000000003, "duration_str": "18.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.932}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\laragon\\www\\to\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\laragon\\www\\to\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 137}, {"index": 19, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Models/Impersonate.php", "file": "C:\\laragon\\www\\to\\vendor\\lab404\\laravel-impersonate\\src\\Models\\Impersonate.php", "line": 64}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 671}], "start": **********.842963, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 94.932, "width_percent": 5.068}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login-with-company/exit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login-with-company/exit", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2134587431 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2134587431\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1974873292 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1974873292\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1840547659 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJodDBBcTZmVzJQWEpXNjVmems1RHc9PSIsInZhbHVlIjoiSkNoRDFNcDBsN2dGU0YwWkZJTElCWWFab2lNRy9Sd3A5UThPcGg2WjJlYzNudFZ3a25TWUJvWXRWbXVnRm9XUWRjNmxDcDlGNS9TUWxzU25sajJYSklxakYvYlA2L3M0Vis1L3BhT1NqU09JUVpLWFJzemkzcm5Ia0hWNDZaK1JnSUY0bnpPYlFHR2E0UXJCZWVYR0pFTlkvUjZqK3BLb1RkRThuSXk1QzZNSTdXVUU2RkkzN2tiRFY5M1NZYWh4Rk5NUkNBYllxZHhBSzNnNDR5aHFodHFONlc2bmpPTG9laTJ3dlQxdW85QkFYUkVXZ1V0ZUlwYzg0NTA0TVJEREFZcjBwN1RyclBZaWVJS3pMNjhvVkRxZkRvOUl3TU11dk5zSUNPa09aSlBlM05rUXRRLy84ZjRHV3pLVThGWitRZDFvVDdBeHozMERseE1JbDVrbkhXbGVCRlhMY1IyTFl6NFVRSnNLeDBkcGEyR20vVW56SmU5ZmZjbW5XNnRHb3hqUytlZUtTVHR2MUppajdTNUswTTVWOE1heWVvT0tFNXRWSnl6enpMRkNnM1I2eFJ0b1RxTngzNlcyOFo5U3FCRlJLNGViZm82akhGMmlLbFlDUW9Eb3lnZG5FdGt4TGREM1EySUFpVDE1QWJoU1VDd1FTZ0pPV0JLMHBqWWkiLCJtYWMiOiJkZjQ3OWViZDQyOTljZmJiOWVjNzlhNWYzMmRiMTJhMmViM2VmNWUwYjVmMjE0ZDZjNzUxYTkyNzg3OTY2ZGM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii95VTVBL2o3ckc1ZDVxaWFwRHkvcmc9PSIsInZhbHVlIjoiM2E1a3c3U3l6QThOZFJBOTJhTlVjSG1LZTlKOUpXZEN1WStxdlZTMzJPYnZiUFVRY1loZWZaSmRBRHRtWFhXZHdNS1c0ZUV3RTR3cE9xY1BZUmVqaExOTVNXRXBRUllya2hjN3IySlZycDJTcC9rNmdDQWUyNncrK285c3dHNk5ESXpQQyt3Y2RUMFEzNmJsWENGc003N3NxL2N1aWpnTi80RG9hZkpWUHJFRGxyMGpjbVdzTnRpZmpRYnFhdGNuc2RjeFdsWW5takszNFZhTWMwN2ZuamxUZUNBczVIR0JBZWlxVWpNcCtLSE4xN0lBZ1U2R0RLMVFlOE1nd0IwQ0k4cHhGeU1Cc2tDN1ZFU0ZhRFgwR202OU0vZEZ3cmJRM0E5R3ZPRFpPSTFRZDdJV2pqUER4ZWFQWDNnM2R3TkZtZmxKQXBXM1BBVEpKa3Vkekx2NUthNnZtZVBFbDdIQ0pSSGM5Z0FkY0ZpaXZJbm9KdERjSXFIVUduTG5XMjE4dmNFRGMzckFLNzZkWjN4ZGlrZ1dUYXZkVWhZMGxNZFkxU0YrYlR6THlWV0VVRzNmQ254ZnlvNk1WUTJXNmlqVWRHZ0oweDhNRE0yVjB2VDBmMXhwMlRwU1J1SWlKTFVSZExwRG1Da0ZwdDFNMlZMR1ZqaEpaS3ozYnpDL0VlczIiLCJtYWMiOiI1ZWI2N2Y4OWEzY2I2ZDBkMjJjY2U3NWM2OTAxNTY3OGQyMmY5OTc4OGE1Yjc4ZjRlNjkzNGUxMjJlYTk1MTQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840547659\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1770343889 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770343889\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-558618118 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:15:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVMT1NGQ3JrR3hHdithaGNCbnpoSmc9PSIsInZhbHVlIjoiRTNtZkRTVFZBbmVTWHRjcm1Qa21mYU5tSzlXaGc0S2k5YzhNd2pOTDZSanFYZ3ROc0d4VjZPR1NkRDRuSmJ4YWhDR29pT3FOK2ZZWWkyNS9jTSs3eFlCVlRHcFN3MnJLUHV0MVhBdElGSWt6dHJkWDc3L1ZlSFdKSlhsSlFTNkJ2MHRlOTlmN1p0WVdsVTRoUEJoNS9zU3hHOEo1elREMjAvenFuMW1ZY08rVEtzcjh3MlVqNXNVMWtlQk4xZldQc09lRGhYc3pGMDBDWWE5NFhFQVRaaEhFRmJLcXIrdXlqYWxpSENNaHFTZlZER2RFREZJT01YdE1uVTQ5eVJWaER6MGhDVWtNL1ZBZVdzZ3M3QWY0YS9aR29ld3JGd2tMOG84b3JISndUMUhQdmpiREJPQU1tR3V0ZW5PcTQ0Yzk5NU93WmVMTFMxaHJyeVkzR0hQNFFFeDZiS1JKUVRscUdzZEZWbVNnd1U5bjAraFZHVHpUK05mQW9OL3BqZlJsL0l6K0M2UG9QVFZmRjg3Zlkxb2RFQW5jRVdsVFRxc2JBTzZ0Wks0ZHVkN09rK1NUUkREYlVCUmNRMVRUMGRQeVpWd3d2NzUyRmwwUDloWUtCL2tkenlINWNneDViZzdneThkYkpwRVFQRkh3TVpoMGtnSW8rRDV0Tkc4dmJTUmgiLCJtYWMiOiI0MTk3MGRiN2JiYjFjZjBiNzI2ZmUzZGQ2MTc1ODkwMjcyYjlhZmQxMWZjNjU0NGVlMWUxYjQ3ZTM2OWQyZDAxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNudGZoemY5UnE2RVdNRk0rQ09YNUE9PSIsInZhbHVlIjoiS2tLT0NDRmkrbUFVRnMzSVBmUlVhTDF5L1BLcmY4NkNHcW1NeTIzVzlsc1dJZTJ1bFJ0TklRUi9nVEtpcUtPalhwTTZ6eDRIN2xtUEdaSjFuQTE4MjlzK1dVQTVIS08za2NpY0RaSWw0RkNFdkJnK2txbnNNcVRiNVFIc1gySDhiQXR0QnhKS2tISXlNQWVRUHdmTStQc05JVk1VSGd0a0RQMkRVdnpTaGd3S3ZtblpIc2xkK1prd21SdU0wNGFNNXNpOVg5dVlhQWVBMVlWUXoxeXVyckJnYldIYy9xV3ZhTE5xMUNxaG1zaTAyRHNwOGhtSW5CeUpZRWovTnptRWtXZkxuYnZjbVRkS05qbm1EcFpKcXdjdlBkL0t2S0k1WFFNVEFwRFI1b0NpaXhWRHdpRENlaEFCcHhIMXdhRnN6UWJtUHNtSmhqVkxXLzZHbk5ya01tbXRRT1FtWU5jS3V5L2ZnSndBT3Foek52cXYwTWthSzJqOUNEcTYvQm0xTERTVlkvUDZDQllHYmJCM05iVG1JUDdjQkFZbFpuRjZMcGVkMmhSMHlMaXlrZ0RqaENxMlk5ZXZYVUtJLzljQkpLWVR4RlYzQXpvWG8wWCtmUkpCbXZFc0pZWVFVY3B0aGlpZVZFeUFScnYrcjhqdTNzTmplQmpiN3VyZzNFZFQiLCJtYWMiOiIzYmNlODI5NzMxYzg5ZjllNTVjMTUzMmU4NGQ3MmIwMGI0YjA1MjA2NzdmYTU4MDhkZDg4ZjQwZDAxNjNiNWE5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVMT1NGQ3JrR3hHdithaGNCbnpoSmc9PSIsInZhbHVlIjoiRTNtZkRTVFZBbmVTWHRjcm1Qa21mYU5tSzlXaGc0S2k5YzhNd2pOTDZSanFYZ3ROc0d4VjZPR1NkRDRuSmJ4YWhDR29pT3FOK2ZZWWkyNS9jTSs3eFlCVlRHcFN3MnJLUHV0MVhBdElGSWt6dHJkWDc3L1ZlSFdKSlhsSlFTNkJ2MHRlOTlmN1p0WVdsVTRoUEJoNS9zU3hHOEo1elREMjAvenFuMW1ZY08rVEtzcjh3MlVqNXNVMWtlQk4xZldQc09lRGhYc3pGMDBDWWE5NFhFQVRaaEhFRmJLcXIrdXlqYWxpSENNaHFTZlZER2RFREZJT01YdE1uVTQ5eVJWaER6MGhDVWtNL1ZBZVdzZ3M3QWY0YS9aR29ld3JGd2tMOG84b3JISndUMUhQdmpiREJPQU1tR3V0ZW5PcTQ0Yzk5NU93WmVMTFMxaHJyeVkzR0hQNFFFeDZiS1JKUVRscUdzZEZWbVNnd1U5bjAraFZHVHpUK05mQW9OL3BqZlJsL0l6K0M2UG9QVFZmRjg3Zlkxb2RFQW5jRVdsVFRxc2JBTzZ0Wks0ZHVkN09rK1NUUkREYlVCUmNRMVRUMGRQeVpWd3d2NzUyRmwwUDloWUtCL2tkenlINWNneDViZzdneThkYkpwRVFQRkh3TVpoMGtnSW8rRDV0Tkc4dmJTUmgiLCJtYWMiOiI0MTk3MGRiN2JiYjFjZjBiNzI2ZmUzZGQ2MTc1ODkwMjcyYjlhZmQxMWZjNjU0NGVlMWUxYjQ3ZTM2OWQyZDAxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNudGZoemY5UnE2RVdNRk0rQ09YNUE9PSIsInZhbHVlIjoiS2tLT0NDRmkrbUFVRnMzSVBmUlVhTDF5L1BLcmY4NkNHcW1NeTIzVzlsc1dJZTJ1bFJ0TklRUi9nVEtpcUtPalhwTTZ6eDRIN2xtUEdaSjFuQTE4MjlzK1dVQTVIS08za2NpY0RaSWw0RkNFdkJnK2txbnNNcVRiNVFIc1gySDhiQXR0QnhKS2tISXlNQWVRUHdmTStQc05JVk1VSGd0a0RQMkRVdnpTaGd3S3ZtblpIc2xkK1prd21SdU0wNGFNNXNpOVg5dVlhQWVBMVlWUXoxeXVyckJnYldIYy9xV3ZhTE5xMUNxaG1zaTAyRHNwOGhtSW5CeUpZRWovTnptRWtXZkxuYnZjbVRkS05qbm1EcFpKcXdjdlBkL0t2S0k1WFFNVEFwRFI1b0NpaXhWRHdpRENlaEFCcHhIMXdhRnN6UWJtUHNtSmhqVkxXLzZHbk5ya01tbXRRT1FtWU5jS3V5L2ZnSndBT3Foek52cXYwTWthSzJqOUNEcTYvQm0xTERTVlkvUDZDQllHYmJCM05iVG1JUDdjQkFZbFpuRjZMcGVkMmhSMHlMaXlrZ0RqaENxMlk5ZXZYVUtJLzljQkpLWVR4RlYzQXpvWG8wWCtmUkpCbXZFc0pZWVFVY3B0aGlpZVZFeUFScnYrcjhqdTNzTmplQmpiN3VyZzNFZFQiLCJtYWMiOiIzYmNlODI5NzMxYzg5ZjllNTVjMTUzMmU4NGQ3MmIwMGI0YjA1MjA2NzdmYTU4MDhkZDg4ZjQwZDAxNjNiNWE5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558618118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1917941575 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/login-with-company/exit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917941575\", {\"maxDepth\":0})</script>\n"}}