{"__meta": {"id": "X153f5beb3fff6da3fa3d443ce5dd1b81", "datetime": "2025-06-06 21:55:23", "utime": **********.483619, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.173752, "end": **********.483648, "duration": 1.****************, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": **********.173752, "relative_start": 0, "end": **********.318542, "relative_end": **********.318542, "duration": 1.****************, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.318562, "relative_start": 1.****************, "end": **********.483651, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007390000000000001, "accumulated_duration_str": "7.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.405686, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.005}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.435774, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.005, "width_percent": 13.667}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.463418, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 77.673, "width_percent": 22.327}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=v1wjex%7C1749242089260%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZsSXV5VUpLWlF1OVk3M2JFdW5GZGc9PSIsInZhbHVlIjoia1BrZzVOV01SRmU0OWR2S3NnWmZRamhybzJCemg4OTFHUjR5OUdhVjlhYkpJSGNhMThTL0hUWmpzWGdFMVFocmI3SHlRaSt6RVZOL1A2UEVXa2NGSDk1VGp1UmpsK1hERFlFVHFoUHhwWWVoaHpOZ2taQUZKaHJSRXVaTTkxZXZBWmg5b1pMdm5IWGdaMkczOGQ0VmZrM2R4ZFE5UmlURUVMb2luUFF2b2JZNTJtZkt2SzNYMEVNU2lBRDBKUGw2TUNNYTZKendaRkhjeUpHTnpoRWRCbFRQc3lYanliblNRN09lYUowUFUzQWhxK1VuSWVNeERqT1VNRVQxdko1b1ZKYXFvc1BiWmpBbVpmdS9HcUN5NWkxejFqZ2dDSndZbjVLd1dHYWw2UFFYQUZrdUNTeE9sdmNJMkQ5eldFN2U3RDFaQVJpS01PUHYwdlIydm5mcW9iUnpoalhCcThxdWdUZGlMMUEreEtySnhWZmtkQ0plSE1LRVRxY09EUGRvTnZDV2ZuQnY1VmtuM1M2cXBMaXpZWHRFNVNSUk5CNDBsTElpWGR3VUh3VFFwZkRMK2FxZU0xUEV4K2xHbFVkTElMUlFIbmlIcmRXUFhMVlJhT1ZpTmV1S2VyWTFrN3FITGpkZGo5NHlPcCtoTHRUY3FaZW56anhqS0I2bStlTFMiLCJtYWMiOiI5Y2FmMjM3YjNkOWRjMjA0NDNlYzZlMDA4NGE0YzVjYzg2ZjY4NjUzYmVjODk2MzgwNTEzNDIyNzFhODhjYzcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlkxVE5XZE1sYjVSSTdIbkhkU3hGTnc9PSIsInZhbHVlIjoiYnB5TTQ4QXdKamNISDc0RmJNdEVXa2NhSFM2Uzd3QzQzbnk2K3Z4cXNxYjFkQnJTdEFOK0FLTjF4WWVOV3E3K1pkenFRVEFNMk81TElJN1VIeTZVS1FGQXpuQitXUzRPMjdORGdVVng0dE5Wd0hnT3V6YUNZcmZGOVp2amVZcVZ3MG1zeXFIQ1VyZytIZXpuRndQNGkrNHNxZm8xa3V4cUNPOUwyOFhYR2NVYzFHcW5RWEZxVkdabHZuYi9abmc3MTE4ZHNQZHFYeEZqR2Z3bURGV2NBMXU1WVZJWnFVdFlsU1NEbkdjK052RTVBWXdzZTRES05hazNONnltYzZUa3gxclZxUGFpVXdOVUJ5TmtIWUFnWmdOM1BxTjA3V2E4djBISGhsU0d2eWYyTWN4MzJrUVBMdlRKWloxblhrSThSK1UyOFllTHdkcXYrZmRKMkpBQnZrQWNrdHFCdmV2NlY0NUo2TzdpRERNMzVEdEVSeTRuT1BjczRTVzg3MHNxbUtEalNRd2tTNUtiS3hDazBMUXQ5MjBTcFlwcFRMblFLaUJYM0dMWUE1VUFEZ2tGVTQ3VFR6ODd0NC9wY2tieDZlUWI4V2dKSUcrS0dEWjdMWUE2OWJVMW9xdVNiVFR5Z1d5RE9sQkhUOXVWc1d1K3hEcTI2S2dTemFqQjZobFoiLCJtYWMiOiI2N2JkNDRlMzdjNTcwMTE2MTA0MDFkYWQ1M2VkYTYwYjBlNzBkNWJlNGE5OWQxOTgxNWU3NDQxNjlhYjg2OWIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hE2sfajo5A8qKfU8aNxLgaH66sUWlFc2M9YW325j</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1624160811 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 21:55:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlcwL0h4WkhTOFBkZk1mRHF3TkljaWc9PSIsInZhbHVlIjoibnk3aFFqMW1NaFIveWgyN2VDNHM1VHZRRU5Tb2FUR2lIWVI3UmRoS0FLaFFtdm5oOWFKdHBDMXFKL2VtMkRlMy9LUXc1KzRFempVbm9hQVNWbDZlbWpoTFBURmI0aWpjTkFqWTZhM1dQQTBSeXNNNnNURUxKcjY1aTVkTjE1SXZ1Q2dCNHRUUFZ3bzlBNFdxWWtnVjZZS3BsdmJOZkhHSW9wcWg2UTF5cklaVFdNUW1vY3Bhais2dzIxKzJzeDRQQzkvbHRLSmVaZm56QUsyNEtvUkZhU2c0czBaaFFUaXRGNXA3aE5DYzkyNWhSUitzb1FwckJtc1F2aFZCcTNOOUEybncxekJ1Wk5IaUdIcGhCcWVURXd2ZWhKUlhLdExJZTE0a3ZQc245bTNJbHBQbFJLNnRwK1E0NnVVTEQySVN0MFVTMnhwU2hUS2ZVWDRzamlSejFtaEZ4U283bkpPVW9MMzdRV3RON2E5VEtxTVREdlc5blI1Y0xSbmNaYnhCZkJFTmJNMWcxOVZyenE1NTZUT1RLTnVBSlJiVUd6MzFPM3dqMWlXUGtNcktlbnVnNnBkbUllNmIzU1RsZmt5ZUVuMlAyd0p5U2U5QkZIZ1FQK2hacmRtbHFzNzViWjk5c0NSeVhZa1ZIQURMVnZsL3FVcnhaSmZCZE16L3RYZmIiLCJtYWMiOiIyMzgwZjliMWU5YTE3N2ZkOGJlOTJiMjc3NTM3N2RmNWUwN2QxM2M2ZDRiNDQ4MTliMWJkNjc5YmVlY2IwYWM5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNzTjVka2NsRW56NmN3MlZLRVB0QUE9PSIsInZhbHVlIjoiU3hFb0xoK21zTW1WWDFUcnZXTlA0dEJ1bzNxREFaSHFSbXY3WVFCMjZ5MGJYSTB6Y1ZyNElMMkVPaERKR1pWODkwZDlXVUhZbFhuZWtTem81VWxSenU0WFJ3cWhScXVzS1dlaDVTdVRpK2V3Ykt3YUpUZjgyMzNuYW5rMUhEM1VkYk04OVBxZDJ3V1c3c2orWWJEazAzaXlnR28yOExWcVpoUXltaXlhRG8rTkxrNjhtOWZtd0l5QllyWnRLSWFNdklTaVhzOUpMYmU4anErMlRwMDl3Zmw5N0w3SkV6MFExTXMydGphS1ZCUThQNEZJL0FFTUR4VnRUT3VuWWsxR09mcHlRT2RzdkFPaUJhbFJQL2ZZVlZqWThtbVViZHZ0OGJMWjEzaHhsQy9rSHc5TFFzRGE4NGhpTEhIL0VxLzRYT2x0cUZ3VWc2b29xOEZTZDRncWk1NGJmZXhCVGc5V2d4U2htVTNRRkQ1ZW0wMEUwKzZWMzBHQ2FMczZiRGJBS0l4N1B4bWt4WjNoODcwWk5pTGRTNStKQWZZd25hOFVJakFjS3hwT2E0cXBMLzlqL1krTjkrN3FEWENOVmVTeXpwYVc0ekZJSlAzeG9GTElvVXF4ZjQvcFBNY05tZ1RxS01Sbng3Wk82ekJ0ay8wYWh6VWxVNXRCVEx3RkVDK3IiLCJtYWMiOiIzMDFlNjliNGI0MTllN2NmY2MwYjdmOTBhY2FlYThjNzMxMDc2YTA2YzA0MWQ5YTI4YjY5NDcwOGZlMTkyMGZmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlcwL0h4WkhTOFBkZk1mRHF3TkljaWc9PSIsInZhbHVlIjoibnk3aFFqMW1NaFIveWgyN2VDNHM1VHZRRU5Tb2FUR2lIWVI3UmRoS0FLaFFtdm5oOWFKdHBDMXFKL2VtMkRlMy9LUXc1KzRFempVbm9hQVNWbDZlbWpoTFBURmI0aWpjTkFqWTZhM1dQQTBSeXNNNnNURUxKcjY1aTVkTjE1SXZ1Q2dCNHRUUFZ3bzlBNFdxWWtnVjZZS3BsdmJOZkhHSW9wcWg2UTF5cklaVFdNUW1vY3Bhais2dzIxKzJzeDRQQzkvbHRLSmVaZm56QUsyNEtvUkZhU2c0czBaaFFUaXRGNXA3aE5DYzkyNWhSUitzb1FwckJtc1F2aFZCcTNOOUEybncxekJ1Wk5IaUdIcGhCcWVURXd2ZWhKUlhLdExJZTE0a3ZQc245bTNJbHBQbFJLNnRwK1E0NnVVTEQySVN0MFVTMnhwU2hUS2ZVWDRzamlSejFtaEZ4U283bkpPVW9MMzdRV3RON2E5VEtxTVREdlc5blI1Y0xSbmNaYnhCZkJFTmJNMWcxOVZyenE1NTZUT1RLTnVBSlJiVUd6MzFPM3dqMWlXUGtNcktlbnVnNnBkbUllNmIzU1RsZmt5ZUVuMlAyd0p5U2U5QkZIZ1FQK2hacmRtbHFzNzViWjk5c0NSeVhZa1ZIQURMVnZsL3FVcnhaSmZCZE16L3RYZmIiLCJtYWMiOiIyMzgwZjliMWU5YTE3N2ZkOGJlOTJiMjc3NTM3N2RmNWUwN2QxM2M2ZDRiNDQ4MTliMWJkNjc5YmVlY2IwYWM5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNzTjVka2NsRW56NmN3MlZLRVB0QUE9PSIsInZhbHVlIjoiU3hFb0xoK21zTW1WWDFUcnZXTlA0dEJ1bzNxREFaSHFSbXY3WVFCMjZ5MGJYSTB6Y1ZyNElMMkVPaERKR1pWODkwZDlXVUhZbFhuZWtTem81VWxSenU0WFJ3cWhScXVzS1dlaDVTdVRpK2V3Ykt3YUpUZjgyMzNuYW5rMUhEM1VkYk04OVBxZDJ3V1c3c2orWWJEazAzaXlnR28yOExWcVpoUXltaXlhRG8rTkxrNjhtOWZtd0l5QllyWnRLSWFNdklTaVhzOUpMYmU4anErMlRwMDl3Zmw5N0w3SkV6MFExTXMydGphS1ZCUThQNEZJL0FFTUR4VnRUT3VuWWsxR09mcHlRT2RzdkFPaUJhbFJQL2ZZVlZqWThtbVViZHZ0OGJMWjEzaHhsQy9rSHc5TFFzRGE4NGhpTEhIL0VxLzRYT2x0cUZ3VWc2b29xOEZTZDRncWk1NGJmZXhCVGc5V2d4U2htVTNRRkQ1ZW0wMEUwKzZWMzBHQ2FMczZiRGJBS0l4N1B4bWt4WjNoODcwWk5pTGRTNStKQWZZd25hOFVJakFjS3hwT2E0cXBMLzlqL1krTjkrN3FEWENOVmVTeXpwYVc0ekZJSlAzeG9GTElvVXF4ZjQvcFBNY05tZ1RxS01Sbng3Wk82ekJ0ay8wYWh6VWxVNXRCVEx3RkVDK3IiLCJtYWMiOiIzMDFlNjliNGI0MTllN2NmY2MwYjdmOTBhY2FlYThjNzMxMDc2YTA2YzA0MWQ5YTI4YjY5NDcwOGZlMTkyMGZmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624160811\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1055043879 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055043879\", {\"maxDepth\":0})</script>\n"}}