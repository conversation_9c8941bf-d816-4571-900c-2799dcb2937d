<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CategoryAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (Auth::check()) {
            // Allow access if user has 'create purchase' or 'manage constant category' permission
            if (Auth::user()->can('create purchase') || Auth::user()->can('manage constant category')) {
                return $next($request);
            }
            
            // Deny access if user doesn't have required permissions
            return redirect()->route('dashboard')->with('error', __('Access Denied. You do not have permission to access this feature.'));
        }

        // Redirect to login if not authenticated
        return redirect()->route('login');
    }
}
