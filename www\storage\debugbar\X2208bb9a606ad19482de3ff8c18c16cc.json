{"__meta": {"id": "X2208bb9a606ad19482de3ff8c18c16cc", "datetime": "2025-06-06 20:36:03", "utime": **********.914153, "method": "POST", "uri": "/logout", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242162.554698, "end": **********.914208, "duration": 1.3595099449157715, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749242162.554698, "relative_start": 0, "end": **********.7696, "relative_end": **********.7696, "duration": 1.2149019241333008, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.769622, "relative_start": 1.2149240970611572, "end": **********.914212, "relative_end": 4.0531158447265625e-06, "duration": 0.14458990097045898, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43368984, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST logout", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@destroy", "namespace": null, "prefix": "", "where": [], "as": "logout", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=243\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:243-252</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01881, "accumulated_duration_str": "18.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.85968, "duration": 0.01881, "duration_str": "18.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-133911674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-133911674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-599067189 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599067189\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkpydzBkUzhneEttcUtRTHVkK1dnRHc9PSIsInZhbHVlIjoiODJSc3ZsUUhOWVFyVDVYYVQ3NFFxRmZWRGZPYWF2YXRIMFE0MWFWTmtnRG95NXhQdzMxNkVweGdFN0NXL2tuNDNDbGtIY2pXQ1FadHJlQTlPSjYwaFhuVjFwV3BaUDVtSDl1T3h5Q3JKd2FpMmpZWmY1aE5vSzRGZHNid01HYk5kWHBLK0NyQVZpTVNad2piREFJczNkV0JIaHJmTWRvdmNJN0FqbGI4RFh3UXFxcVRuVmxrTDBlK3V3RzYzZXZ1NFJ4ZFVpeEd0WmEyelQ4anVzMUk1OVRCeDFBZXBxSnRmMVVUaHZmNWxkSTZLb3dROVQ5bHdKcitJMVJRcFFLQUxMQTdmR1pxYjhzb0pOQnhnYnJ0SXVDZW1uQ1JoN0NvOWNyQXExeXJYOHN4V3M1eThucFNTeWUyNWk3WkVaVGloTHZpazduY0lBcjZFSlFtRSt1VWhGaUZrTFhtRWhqanVabVo5dEd3VklHWlBScjgzWkZZekx1a2FiWFc1QklTL2FtRW1IYk5qSmFpTHFYZUM0VHF6ZnZTd2dMYW16M0ZHbDRkRnZXS053MXJVSzBRamkvVnJvMzRQbEh0OVlwRmx1c2tzQnUwMEY4OTR5RGx5aStXZGNqWEh2M3dGSDlMaGpVMEltTDBZNHY5Z1lHZVlzV3VCWXN3UEI5UWwxV0UiLCJtYWMiOiJmOGZjNzM1OGRhNThkZThkMDI5NzcwMjc1MjNmMjVhNDIyNzBkYzI3ZGE3MDY0NjMyZDhjMjM4MzI2ZDZjYzllIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imx6UTYxWVZpZHVUMHR4eFVld1RQYlE9PSIsInZhbHVlIjoidTRVRjIvSXY3T0JYYzZqQVlOL0pJcUJzMHc4alF5YThBNExxdWVSYTJyaXo0M0l0UUZwcVBvTTUyOG5tekhWdjI4WkRTdHlWRFNpbnpvQ0lPK3Fjcm91ajVVUG1tbkEycUdlVFd6OGpiUGxVUjgraVdlMXpUZFoyVFN5OVpja1pNLzVOdHpQWEZrS3ByUDJFTDNTb09FRXdnZ1h1ak5DK0lMNi9ZenZNU2U4dlNCaFU3T3R5bGxiTmlBdnU3VWpDUFJLeXUraG50Tkk3b0RQQ250U004WUlXM09BY0hCMjBZWFUvYmVnYUpHTytTaVBoeTdCVStKbTdTcmg2NjJZTWE5Qy80RGZxbkNNcXZWRjJPZ0Q5bDlmVW8zYk5SVXBWOUdpaVpQaEVRcDhITm90bW5rL2ZwQ1JJQ0VHVjRUZm45dWx2UVhCMWczYU0xdlMvdngxTHYvemxXblF5eGxGWWdYWEpXTFR6bkEwYTRlSDFpa21JUjBDSXgzS2VqYVh5SmxrOXVXRVg5ZFl2R3lHSGd6akFLSEtwTXB2d2p5b3NPbE9yRDdHRUNJZUZFemVadHNidnFkWmZrbFZQTzRvWHo3WS9zb25SMHVwVDJnTWx2eTlTMXkwREtIbzhKVy9pVkJRSG02VmxqNTZ1eDdMeDMwRTJQWldRTmhHbUxsNXMiLCJtYWMiOiI1ZjM1NjIxYjM0MjQ5OTM5MDBlNDdkMDg4OTRmNjYzMTMwYWFmNWY3NDUyMjJiZmVjYjgwN2Y4YTRjYjdkMjBjIiwidGFnIjoiIn0%3D; _clsk=1i6cij5%7C1749242138186%7C5%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1478813252 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478813252\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-908594577 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:36:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdlWm5SdWhvNjNXV3BsZjhsVm42V2c9PSIsInZhbHVlIjoieVFabWd4QVVnU3ZCSDNYT1gyMkVUMnYxUUE2V1RVN3JDWEdvd2ZtUXMxQ3k2d3huVXlYeGw5RFAwTjlKd25HeEVBTC9GSkowRlNGNytxRmNqWldsNW4weGs2azBqYXRPQkNtcVlibVVxRjJhSVd5a2daL1BMRllkSGNzbUZiY2M2ZEhRR3hjRnMwcG1lU01YQzJGaTdOUTdkbHN1eUZuU2hRZDNWak5DVW5STlo1eDYxYlVVOEJWSGg5M3YyQWpjZnR0WkMralBGWmJwT1BHQTJETmlSN2xKQmJuTkIreklCaHIwdFF5QWF1Y1g1ZmFXZFl3b3JZUmhLVW5iQUVEeDdSekNIZHdSb0hiUFBudFdLV3c3a3NxUlAzU0Q2LzhNaUR5dE54aFZvWUtRY0xidGRNUWxOM3JjSnhLOXEzRUJGRlhxbCt6QTcvWDJUb3BXVmZ0R0hwcGo1Tm5QeW9XZ0t0aTgzRmtKTTRZSndNRnRDTFVPLzBDaEQzSnN6d1dKdkxkKzUxeVg3OVZBUDN4V0dEMFg0aCtwMVRvVzEwRDY0eUp2bUViajJuWmdpbS9mb2RDMTNMcDNwcDZiRTFRaGNBS2kweGs3ZW5NR0hhU2NlSFgraFhDSWVOWGg5L3FzZlczeTdPN21GSDB3dTJEcGVPSkx6Mk9LL01ESE45YzciLCJtYWMiOiI5YzhiYzQ4Yjg3OGE2ZTcwZGFmYTkwZTcyMWNhODk2YTlmYmYzMTYxNmM0YjBhOWQzZWZiNTg1NDAxODc0OTZlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtINFhNbzJ1UE9VYmJaWHppNFBLbkE9PSIsInZhbHVlIjoiZUJnMVBpc2l5YXdtSmd4VGY3QUVNRUZvTGQyZkJLQUU3NVNHZmhoZ0hZKy9GTk9DSG4vYzlCbWJFOWFMMkgyTWFxdzVpcW1XOHhHdGNqNWtsMno0dHlyelR2R2N2MHJXa2t0M1JJL3E5STlIVmNsekNoWkRBclB1b3ZLNWQ2cHFQckdrZWxVUDl0QmtQN1h4Z0tNejRFZE05VlhHSWx3N2g1akg3WVN6Y3kvUS9JdGZPdTB1SjQ4M1dCRE5mWTFJTzN1ekVvMVJBRHB6cWdzWDZRYk9LNmN0dFpCeDhUK05DbUVncHZBWEljOEIrdHRmdHd6K0RWbktCMndqTk1zNjlraDVTYU1sQ016MHIxN3pJdml1YkNSZTB3RERhU3RRd2F3VDE5YjVkN0VaWlpHRkFTMldvYkJ0RnNTalhya0JCQmM0bVFLYkpsRTArRUk2Y0dwaElCTW8xSUxQQUM3T1FXTzZ3ZVd0d1hYcWFDYlVrcUdzSHpmRFF5TEpidTJTTHI1MmhET2djVVJZWXNkUkRaYUl2WkxoSDh4N2NTUHVBQWRDaUwvR2g1MWtyc2krYkpjdytrRFNGblJ1cnU1NmxuU1BKUllwdEY4NVhYb0xzcnIwbzFqZHhuc2MrUTBRZ2tDQUVTMHVDODRtNVA4bzYreFV3YmtNSEUvOHhST2UiLCJtYWMiOiIxYTkzZDU0NjcxYmZkZjMyOWZmYTFjODdhMDJmYTZmYWM3OGFhZmQxOTM4ZDA4YjUyNmFmZTQ5NzViZjQ3YjAyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdlWm5SdWhvNjNXV3BsZjhsVm42V2c9PSIsInZhbHVlIjoieVFabWd4QVVnU3ZCSDNYT1gyMkVUMnYxUUE2V1RVN3JDWEdvd2ZtUXMxQ3k2d3huVXlYeGw5RFAwTjlKd25HeEVBTC9GSkowRlNGNytxRmNqWldsNW4weGs2azBqYXRPQkNtcVlibVVxRjJhSVd5a2daL1BMRllkSGNzbUZiY2M2ZEhRR3hjRnMwcG1lU01YQzJGaTdOUTdkbHN1eUZuU2hRZDNWak5DVW5STlo1eDYxYlVVOEJWSGg5M3YyQWpjZnR0WkMralBGWmJwT1BHQTJETmlSN2xKQmJuTkIreklCaHIwdFF5QWF1Y1g1ZmFXZFl3b3JZUmhLVW5iQUVEeDdSekNIZHdSb0hiUFBudFdLV3c3a3NxUlAzU0Q2LzhNaUR5dE54aFZvWUtRY0xidGRNUWxOM3JjSnhLOXEzRUJGRlhxbCt6QTcvWDJUb3BXVmZ0R0hwcGo1Tm5QeW9XZ0t0aTgzRmtKTTRZSndNRnRDTFVPLzBDaEQzSnN6d1dKdkxkKzUxeVg3OVZBUDN4V0dEMFg0aCtwMVRvVzEwRDY0eUp2bUViajJuWmdpbS9mb2RDMTNMcDNwcDZiRTFRaGNBS2kweGs3ZW5NR0hhU2NlSFgraFhDSWVOWGg5L3FzZlczeTdPN21GSDB3dTJEcGVPSkx6Mk9LL01ESE45YzciLCJtYWMiOiI5YzhiYzQ4Yjg3OGE2ZTcwZGFmYTkwZTcyMWNhODk2YTlmYmYzMTYxNmM0YjBhOWQzZWZiNTg1NDAxODc0OTZlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtINFhNbzJ1UE9VYmJaWHppNFBLbkE9PSIsInZhbHVlIjoiZUJnMVBpc2l5YXdtSmd4VGY3QUVNRUZvTGQyZkJLQUU3NVNHZmhoZ0hZKy9GTk9DSG4vYzlCbWJFOWFMMkgyTWFxdzVpcW1XOHhHdGNqNWtsMno0dHlyelR2R2N2MHJXa2t0M1JJL3E5STlIVmNsekNoWkRBclB1b3ZLNWQ2cHFQckdrZWxVUDl0QmtQN1h4Z0tNejRFZE05VlhHSWx3N2g1akg3WVN6Y3kvUS9JdGZPdTB1SjQ4M1dCRE5mWTFJTzN1ekVvMVJBRHB6cWdzWDZRYk9LNmN0dFpCeDhUK05DbUVncHZBWEljOEIrdHRmdHd6K0RWbktCMndqTk1zNjlraDVTYU1sQ016MHIxN3pJdml1YkNSZTB3RERhU3RRd2F3VDE5YjVkN0VaWlpHRkFTMldvYkJ0RnNTalhya0JCQmM0bVFLYkpsRTArRUk2Y0dwaElCTW8xSUxQQUM3T1FXTzZ3ZVd0d1hYcWFDYlVrcUdzSHpmRFF5TEpidTJTTHI1MmhET2djVVJZWXNkUkRaYUl2WkxoSDh4N2NTUHVBQWRDaUwvR2g1MWtyc2krYkpjdytrRFNGblJ1cnU1NmxuU1BKUllwdEY4NVhYb0xzcnIwbzFqZHhuc2MrUTBRZ2tDQUVTMHVDODRtNVA4bzYreFV3YmtNSEUvOHhST2UiLCJtYWMiOiIxYTkzZDU0NjcxYmZkZjMyOWZmYTFjODdhMDJmYTZmYWM3OGFhZmQxOTM4ZDA4YjUyNmFmZTQ5NzViZjQ3YjAyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908594577\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2063655658 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063655658\", {\"maxDepth\":0})</script>\n"}}