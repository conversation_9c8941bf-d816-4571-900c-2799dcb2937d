{"__meta": {"id": "X30751ce0a69a26c8d4fdfe7597484b50", "datetime": "2025-06-07 07:32:09", "utime": **********.881531, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.231616, "end": **********.881555, "duration": 0.6499390602111816, "duration_str": "650ms", "measures": [{"label": "Booting", "start": **********.231616, "relative_start": 0, "end": **********.806073, "relative_end": **********.806073, "duration": 0.5744569301605225, "duration_str": "574ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.806092, "relative_start": 0.5744760036468506, "end": **********.881558, "relative_end": 2.86102294921875e-06, "duration": 0.07546591758728027, "duration_str": "75.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43339992, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00408, "accumulated_duration_str": "4.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.860779, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1991792890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1991792890\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1907790162 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1907790162\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-223062666 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281445387%7C8%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNmT01sdXpTR3BMbXNjOEt1dEhsL3c9PSIsInZhbHVlIjoiWWxteDZUTVRmcHdndjNYek90U1Q4MFR6S2xsOXlYN3U5eDE1Q2FlaEJtNEduN0ZFbTBBYjlPM2RPYVhuUlBrUlpxa3diM1dSNXBUalFPVVFRL0VMQ3F0SVl4Tm1OQ1FJL00zTVZZbUh1UkVuczZuU3E3WnNrcjV1OE1GbnMvUFFHbjFieThYWFRBTmdoTmdHMUlmcWwwclp3MGlVVWRzNHVVZFAzL1lPbEtlejdLQmlZL01sTm05eDM4ZHh0KzVWRkZHN3RVRWVOTi93MUpIUUVPRW94UTRFald3akdjTjlJTWxyMGtHN1lsa3BlS3ZTU0RJT01jaE83WllyK1hnanVPNWFrVkJlTmt6QnczMXZlNmcvZXVZdnB0blNzMGVlOFBVdjJRSHhLanc4QUVOcHVabDdlNUNLSnNIYmZBZlYzVFhhbFFpdDRHZk5xb3N0anVYQVM2ZExtamxFNHJTbktJdVZKZ3JiU0hNcytnMVdwUUFOemw3S29pQ2M1d2RMRE9WaUxucHV0cm53TGV2OHdleXhqMnZaZ0JGOExMSHpGcGZ3d0Rpc1lRdjVvejlpYmwweHJ6V212T3RrVEYveE90VUZuZWt2bUhlQmRQSzBJdk9WWk9wcWtDNzBtcVRwUVZGRnhaR3lhNjRqZ1d6cVFkWnJqeG9LOEU3WHJEZnYiLCJtYWMiOiI3YzhjNjgwY2M5Njg5OWNmZTE3MTU4ODZmM2MwNTAxZWI4ZDY5MWEwNjQzMzQxOGZhMmUxMThhMDg0ODdmOGFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlgzZlNpSHpYK1hxVm9VUDhlSyt3NGc9PSIsInZhbHVlIjoiOHRuMGpweVQ4dFBtWnpKMFM1OC8vUjV0MFdIczN4SWVYTTVPZWx4d3J0dk1lVGxWVHdxNHl0WUFldnU4d1pXRmM1L0p6ZW9hNjN0QXQzdm9acXNNT24vcnUrWUt6ZmFIT0R5OTVScFJGL2lZN3dOSXZZandDc2VEaXZvc1VYVUV1dHI0R0ZEVDhwbXpVa2o5cmZXc2J4YVUvQWpRNlc0N2FlSkRuTERKc0ZqQ004TmE5RExSWGRsY1NHbG1jRWUrbnpQM28vdE5zM2ZVcnlqQWFyOEszMmhIaS9BL21DSldQZlE5VXJ1dzdnVENCN0FVVlMycnl3V1o5OTNjSVdOclVGc1FndVdSZVAvamM3aXdSNit0SGl2T0RHOTJoeGwwL3F6Z2xmbXg2OWZSN2NRZlFkcDY3NHdjdytRSzVXQzVJTWpHL3lJRExTMXhSczFqYnFQb3pmN2RFSXBHMjNjNzVTMFVxSkE2QWx1TlBValRVdklLbmFjaS8zSW9FdFpOUGVHQWZMVER2WnM3NjhYLzBHaWY3V3hxckJyd3FrdkxGVXF5QWZ0VkFEVW5kelR4WnV1dWF5UnUvSFFFOUlyL256NGxlRURZSjMyUHF4anhSWGE1K0dzMXFmNUMvRktDamgrSlBhRThhaGIrU2ZWOUpVS0dKVC8vUWNnWDZhMXYiLCJtYWMiOiI2ZjhmNDE1OTJiYThlMzVjZWRlMGM0MmExYzFmNzM4NTJhYjAzYjUzNWI3YTUyMGQ3MTI2ZGMwOWNlNDFiYjI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223062666\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bmHYo9T8TH5q6dUCnl8ZG1w46p5IENlShdAmx5wt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1702766754 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:32:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJ5c1RLRjdjM2luZG5CZ2VXSG80U1E9PSIsInZhbHVlIjoiWUxJS0xnWnFROHEzamtFWU82Y0tZZ3lXdnhIVnZVR2tiaXhYU3JqMnpEOFJMMnBORXdycy9xVEFKdzhOMklLcmJ5UnBFZUlaUjlFMGUraG1YR1J4Zi8zQVdVZXZJR2VKQlBvV0FZTjBUdlR3eGRTR1oxTDZ1S3E1aGMxNFRlb0lHbkppR1YxeUNpbXJmZk13dHoxME8xYUFLbEVEcmoxeGNCK1EyNUs0Vlk4Qk5BM0lycFdMTlp0NXpzVXRoRng2Ryt1VVFJZ0hMR0t4T29LNXFzQVVjbndYdmhDMVZlckg5M1BiSm82bzBZbmJTT0xRRlltUEU0RFJyZ3hVVnZ0elk1TkNwNFhFSnljTkRCTk85UFlreUE4d3g3dlR0SXFiZUlRc2NKT1ZDNDgzcExhbWtJR3ZaNHFTRGlFb1JQU0c5VnJOVU9KL1hld1VBRkx6bkI0ZnlJQ0lqS3FDaEZpOTRVRWpwOEFDUVgraG5rRWZhR3l0aVRsMjlGTjRmUlVHQ1YrdklpSGhZaFlUVGFPak53cEtPMEVIb1RaVEhPQzU4TzRRdU9RUjZVekl1a3RaL0xodUlxZmQ2ZDlJQTJka0hPTEJXOTVUQ2ZrVHQ5T1FrYzNiNzJUeVBWSmZpVUhYdzdmNzZiZGVnVVprbzVNa0V3S0w3alFPTEhvd3p3bkEiLCJtYWMiOiJjYmIwNGU4ZDg2MjE3YmEyN2Q5NDZlNmJhMTg2ZjU4MjcwZDI3OTI4ZGQ4NDE3Zjg4MTQ4M2MwODYwYTNkYzEzIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:32:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRITm8vREtySksxT1JLK3NNcllDQkE9PSIsInZhbHVlIjoialJHTFpDZkFBYVQvcGh6VmdDbUpHRlhpWG1UUGZJZG9TVGFnK0VhVm4rMmxGdTVxSEk3UE5uaktlbGUxdXlvck0xSkJMVUFCYWNUVEx4b2VNOVJMUGduZUt3aWxZZnFOOU9BU1JBQkVRV1hqaWQwUkRaTW95alFTN3EvdTRDTFErUWwxSjJHYTI4UW5jcjEwVy9kNmJGK1p0SGxoYlBjT21sdEhmR0toR2FpSm85eElvcWVTSENUblZrUmdxY1U0WXQ2b3BkRWVva2FOenZNQ1B2TW03YWE5SWxzUDlySUpZRFdYZkIzcU5PRnpkTzg4RzJZYVVGdERxd2NXamQ2OVpXb25JanNMeUlEQzcvMGFkVFYyVXI3SnUxMU5zT1F4WG5ZNnN2LzRCQXhiR0N1Tm1IejkwNk1td2pIa2w1Z1V4RVIyb0V6ODhrMGgzWlpCd1BKekNPcDhRNlR5TktQL2xQUTZtSjE1Kzl5czUvQ0pZNG1WamJFNXFlMFd2Q09jSThuVWZyZk9qTlBtZXJiK2d4Y0ZxL0xwbExYTjc0VjQ2ZUtYWXJVVGphTTdQNmdqMWRSUmo5MDdLUzRSOEgvZkNiZVVqSTJsZUdMV2pXYTBZQklrVW5kTlpvM2pUL3pDenFCS3VvZkk1ZFhkL013L1ZJMGI2N0dGWm02bFJKMGMiLCJtYWMiOiJlYWMzY2UxMWNlZjUzOTYwMzY2MWI1MzAzZmM3NjM2NjJkMTYwZGM0YTc0MWE4MGZlOGMxYmQzNDUwOTFmN2ExIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:32:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJ5c1RLRjdjM2luZG5CZ2VXSG80U1E9PSIsInZhbHVlIjoiWUxJS0xnWnFROHEzamtFWU82Y0tZZ3lXdnhIVnZVR2tiaXhYU3JqMnpEOFJMMnBORXdycy9xVEFKdzhOMklLcmJ5UnBFZUlaUjlFMGUraG1YR1J4Zi8zQVdVZXZJR2VKQlBvV0FZTjBUdlR3eGRTR1oxTDZ1S3E1aGMxNFRlb0lHbkppR1YxeUNpbXJmZk13dHoxME8xYUFLbEVEcmoxeGNCK1EyNUs0Vlk4Qk5BM0lycFdMTlp0NXpzVXRoRng2Ryt1VVFJZ0hMR0t4T29LNXFzQVVjbndYdmhDMVZlckg5M1BiSm82bzBZbmJTT0xRRlltUEU0RFJyZ3hVVnZ0elk1TkNwNFhFSnljTkRCTk85UFlreUE4d3g3dlR0SXFiZUlRc2NKT1ZDNDgzcExhbWtJR3ZaNHFTRGlFb1JQU0c5VnJOVU9KL1hld1VBRkx6bkI0ZnlJQ0lqS3FDaEZpOTRVRWpwOEFDUVgraG5rRWZhR3l0aVRsMjlGTjRmUlVHQ1YrdklpSGhZaFlUVGFPak53cEtPMEVIb1RaVEhPQzU4TzRRdU9RUjZVekl1a3RaL0xodUlxZmQ2ZDlJQTJka0hPTEJXOTVUQ2ZrVHQ5T1FrYzNiNzJUeVBWSmZpVUhYdzdmNzZiZGVnVVprbzVNa0V3S0w3alFPTEhvd3p3bkEiLCJtYWMiOiJjYmIwNGU4ZDg2MjE3YmEyN2Q5NDZlNmJhMTg2ZjU4MjcwZDI3OTI4ZGQ4NDE3Zjg4MTQ4M2MwODYwYTNkYzEzIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:32:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRITm8vREtySksxT1JLK3NNcllDQkE9PSIsInZhbHVlIjoialJHTFpDZkFBYVQvcGh6VmdDbUpHRlhpWG1UUGZJZG9TVGFnK0VhVm4rMmxGdTVxSEk3UE5uaktlbGUxdXlvck0xSkJMVUFCYWNUVEx4b2VNOVJMUGduZUt3aWxZZnFOOU9BU1JBQkVRV1hqaWQwUkRaTW95alFTN3EvdTRDTFErUWwxSjJHYTI4UW5jcjEwVy9kNmJGK1p0SGxoYlBjT21sdEhmR0toR2FpSm85eElvcWVTSENUblZrUmdxY1U0WXQ2b3BkRWVva2FOenZNQ1B2TW03YWE5SWxzUDlySUpZRFdYZkIzcU5PRnpkTzg4RzJZYVVGdERxd2NXamQ2OVpXb25JanNMeUlEQzcvMGFkVFYyVXI3SnUxMU5zT1F4WG5ZNnN2LzRCQXhiR0N1Tm1IejkwNk1td2pIa2w1Z1V4RVIyb0V6ODhrMGgzWlpCd1BKekNPcDhRNlR5TktQL2xQUTZtSjE1Kzl5czUvQ0pZNG1WamJFNXFlMFd2Q09jSThuVWZyZk9qTlBtZXJiK2d4Y0ZxL0xwbExYTjc0VjQ2ZUtYWXJVVGphTTdQNmdqMWRSUmo5MDdLUzRSOEgvZkNiZVVqSTJsZUdMV2pXYTBZQklrVW5kTlpvM2pUL3pDenFCS3VvZkk1ZFhkL013L1ZJMGI2N0dGWm02bFJKMGMiLCJtYWMiOiJlYWMzY2UxMWNlZjUzOTYwMzY2MWI1MzAzZmM3NjM2NjJkMTYwZGM0YTc0MWE4MGZlOGMxYmQzNDUwOTFmN2ExIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:32:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702766754\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1020938110 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020938110\", {\"maxDepth\":0})</script>\n"}}