{"__meta": {"id": "X527b1504ce65b919c57e0de051cd1874", "datetime": "2025-06-06 19:17:57", "utime": **********.422928, "method": "GET", "uri": "/roles/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237475.631761, "end": **********.422955, "duration": 1.791193962097168, "duration_str": "1.79s", "measures": [{"label": "Booting", "start": 1749237475.631761, "relative_start": 0, "end": **********.006879, "relative_end": **********.006879, "duration": 1.3751180171966553, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.006913, "relative_start": 1.3751518726348877, "end": **********.422958, "relative_end": 2.86102294921875e-06, "duration": 0.4160449504852295, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52014240, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.create", "param_count": null, "params": [], "start": **********.358172, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/role/create.blade.phprole.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Frole%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.create"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.385473, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.create", "controller": "App\\Http\\Controllers\\RoleController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=31\" onclick=\"\">app/Http/Controllers/RoleController.php:31-57</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.018439999999999998, "accumulated_duration_str": "18.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.126354, "duration": 0.00626, "duration_str": "6.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 33.948}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.163698, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 33.948, "width_percent": 6.616}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2003958, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 40.564, "width_percent": 12.636}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2636468, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 53.2, "width_percent": 8.785}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.27083, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 61.985, "width_percent": 8.839}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\RoleController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.286438, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:38", "source": "app/Http/Controllers/RoleController.php:38", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=38", "ajax": false, "filename": "RoleController.php", "line": "38"}, "connection": "ty", "start_percent": 70.824, "width_percent": 22.777}, {"sql": "select * from `plans` where `plans`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.create", "file": "C:\\laragon\\www\\to\\resources\\views/role/create.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.368899, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 93.601, "width_percent": 6.399}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 538, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 540, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create role, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-264253108 data-indent-pad=\"  \"><span class=sf-dump-note>create role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264253108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.285188, "xdebug_link": null}]}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1"}, "request": {"path_info": "/roles/create", "status_code": "<pre class=sf-dump id=sf-dump-300843529 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-300843529\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1476341435 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1476341435\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1371338815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1371338815\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-341341788 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBEMXBlODB6NDdWb1RleTdkUHNKK1E9PSIsInZhbHVlIjoiQWRycTRUYlAvazRhZWhCMk5CYmUxcjdPVnRSaWkxdTJRM3pDYkgvQjRIMXJJMFA0TVJxL093dS91S1ZvR0lhbkhTUFJ6KzVZcGQ0Q0RIbGFla01IT3I4WFhqcStWeUdHUVk2ZU4rUThUZERWZy9mbi82ZWQ2NmpGZjVWeDQxeE1pR29JNHcrU1UyMkM4Z0FkRCtPZWhXanY1Wk1BRlZINHhSYzdKanNud3BpbWM0NDRoTWIyZG1PN0JrRHR2cHhET2pqbGd3clF3b2YxYWdYWW8vOHpsaGliZWZrejlvdk9BQ2hTNWdjdzU1dzRYQVRDc1ZTbGc1SGlEaERCZDRHSHB1Q2NCbE9tYm9iWkRuU1MwbG0zOGRFZmZCdVFJVWFDNUVQQWM0NWFLV2VHLzhiaXROWGRMVTZ3bW16VkNsYmFEQkFRMHVWczkvQ1BmaFlxaHR4N0EzOU9RQjROTHFueGoxdkpQSXVXQmtna0pFL3R0MExBMWxkTlRKcm0zKzhObEQvY0tuQXZqd3FSQzdCeGlIa25Ub1RvdUwzNmJPTzVYc0FET1g4TjNmamQ4alBiTlBlNzNZdHBjaTlpMlVUaHc4Z3EyQmIyc0grUmJEZ2VBWnkybWhodXlneXZVRE9wNjFYTnE0elhYYXdXd3NSVk8xUTNCTENxUXIxeFd3M08iLCJtYWMiOiJhZGM0MjY2NmVhYmFlOWU0ZDExM2YzYTk0ZTNmMTUwNWYyZTk1YzNjZGUzNjRjNjVhZWE4YTMzOTc2MzM2YWQ0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVCSDZDZEVkVDFyclp5dmdLYVF4Umc9PSIsInZhbHVlIjoiNFhGR25adHJad3RGQmdKZzUzMlhnYTNJVWJvQUg2dE92d0VKT3UvZEZMbCtsNDMzQWk2MEZKY3hyTWp4UzZ3Y3VCUmdZa0ZhZFlLUDl5Zy9oWkN6b2g5eTVNZW5pdWhoZXBPZEEvdGdDbTR2ODZJdEwvUTF4NllTZjEwdEZ4cENHcDU4SDF6V05pbFU2amdzU2J6ZytoV1VZS25qLzhOSWpvQ25GQ2x5S3dtMXpJYmtBSGhyMnByOVRRcGdFNFZ6NzlhWisrRllLaGxsV25tQkNXT2s1Rmg0YXZKdXZYeSthQm51dlZFRnhDUXRPWlR4Q2VUNG42RW01N2Jqbm1tSVBwT2pVNjk5R29GY2RyVFFmQkZsMER5NDZ0SmlmZUdEMjZGakFjVU5TMnd3MnpCV3VpUHpFSHdicHVkN1pKWEZmZVE0VDQ3S2JROUhuTXMzMVhmREIyN3JJZkJ5ait3cDhhQkg5N0Yxa201emdlcDlkbkNBc2FOZnVRRmh2aksySHI0R0QwdnppNFlvRm9hbjRxb3VIZU50b2VIdnc1bGU3NmZVejE4RmM1ZXNSZ2FZOVdDc3ZLdy9VWHZxOGlLT0prbTYxbHZPYkUrWU15ajJLUVZDNmNIdXEvTm1CSVR4SXlhbkR1QzVDTk0yZnora1NNY0I3NjNvUWNZY0s0Y0kiLCJtYWMiOiI3ZjEwYzQ5ZTViNzIyMWQ5YTg5YjU3OTFlOTBiNGM5OWZjN2E4OGEwNzhjNzU1OWYyNzY1ZTE4YjNmMWQ3NmU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341341788\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1862755158 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">07zLQ1IzhcCwivgYmqVlKD2YgfwQ5DGdasvfNucP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862755158\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1067421551 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:17:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVtVnNCOHNFTW8wTithQzlmeThKSlE9PSIsInZhbHVlIjoiS0RXUHFOclNMZWFOU0ljWm0vNXFMdGZKSDVwMEV2dlZRaitwN2xFUXhsYkx2c0t2cUxnUXdMeER4WTFERzg4ei94d0xucTNtVTcwb1pWcVFhNXliSjFFeVZXSk00Uldma2tSYjIya3g3MW9SaWdtd1UwQTFNZ25jNGdXQTNkVU1xQ1QvODN4SW1KY2FpZ3NHQXQ0N3NWckQ0RkNMODdPektWMndYQWsycm1RVnlHVHdMMC9GUG9ITVVWWFozcTAyR1RwdVhFRis3djdOMlBlemhoQXM0VXpzZmliMUZ2YzdtKzF6czlZaXhaNVBLalVzSTlwVmlnT2c5d0gwQVRweldsV3NsSlE1dnl1aXRzcFpWNVFoRENGZHhYa05udzA4elpNZi9qa1RZRExtbWthYjBiQ3ZzaHR3cGozbXNKOUhINmZiWUhZUDh4b0NPNEJ2VFkybndBRHRQQ1c4VFZ2SUx5S1M3QS91eU5aSnFVTUxvVXp1Uks0K1kvQU94eHhMMGVBWEE3dHNkZys2ZkJBS2oyWFlLRzFYY0RpcnVRQjNhem1LMDJZMFlqK21UeURkb0ZNUWhzVUdXTTlVeVBjbS9tM29jTkMzWXRYL1FOeTljNkdBR2FlRktOZW52OVA4NGxHb0NVN2VhcU1sRTVKSHhmaVI2QmFGenNMTkpvR0oiLCJtYWMiOiJhYmY4OTVmYjZhMjc1YjMwZjFhMjg2NzQyMjUwNzE3ZTA5Njk5NmVmOTcwMmZiMDQwNzkxZTk1YjE0YTc0ZTU5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:17:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZBcFh3aTgyWGxjRFNTUEN6c2E2UkE9PSIsInZhbHVlIjoiUzhDeW9UV3ZMc3hMdnR1TEtPL3JPK0NsNnV4MERrSGxPcHdEVDhwWXJKZDM3dlc2R2lFMXUvZmxpR0kxeHRkU2JFYkdKd2xWekVCeUhxb2VwVE1XSytKWnVKQzJmZnROVnJPN2VCbURlVy9MWWtuczhoblVLV0Z6b1k4SnBFemdQMDk5ZG54eVpqWFRZV2hNS2gvQU5LT2pENVNMOU42ZmNtaUdZR0lQWm1nOXFDN3lVN2s1N2xraUFJWHpieUdjWERPZUpMa0FUTmNNSDM2eWlrdVprK2tHeHdjVUU5VzZOU0lJTkxLUi95Q2VpUFIxVnhkaU8xeDBNT2h4MmpMbFR3Z2xheUFmVyszdDFLTTB2RVkyb3RqMVY4Yko1VnB4VXFnNWxSNDlkR1kyVWJvM2w2czZNSVBpL0RxZzY0SG11MjlJMVJFeGpHWWE3Q3dmZkZudkt6SVhnUkJmYmlvZ2M1ZysyOWxTVnFQeGFpeFhLTzdYUnk4cmFqbWJFQm1yS1FUSHpReHVzTmJKNDVVVjhqTk16T1FTclJMazRsR2c2NS8vb2lqNVpjeXo0V3JPUThZV01kZk1ZWlhrRlh0dHZIQjdLaDBLbTMvUWJjKzVDNnFBc1FabXVTZDVTWFBaRWQ5aUZLaXViZWhFdy9IbTIyMjhGZ0M4enc2aVh5a0QiLCJtYWMiOiI2MDQwNGFmNGJmYTJkMzg4MDY5MWI2YzUzOWMyNmVhZTVmMzQ5MGFlNDY3YTlmMzQxYjI1ZTA2ZmI1ZDVlZjM2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:17:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVtVnNCOHNFTW8wTithQzlmeThKSlE9PSIsInZhbHVlIjoiS0RXUHFOclNMZWFOU0ljWm0vNXFMdGZKSDVwMEV2dlZRaitwN2xFUXhsYkx2c0t2cUxnUXdMeER4WTFERzg4ei94d0xucTNtVTcwb1pWcVFhNXliSjFFeVZXSk00Uldma2tSYjIya3g3MW9SaWdtd1UwQTFNZ25jNGdXQTNkVU1xQ1QvODN4SW1KY2FpZ3NHQXQ0N3NWckQ0RkNMODdPektWMndYQWsycm1RVnlHVHdMMC9GUG9ITVVWWFozcTAyR1RwdVhFRis3djdOMlBlemhoQXM0VXpzZmliMUZ2YzdtKzF6czlZaXhaNVBLalVzSTlwVmlnT2c5d0gwQVRweldsV3NsSlE1dnl1aXRzcFpWNVFoRENGZHhYa05udzA4elpNZi9qa1RZRExtbWthYjBiQ3ZzaHR3cGozbXNKOUhINmZiWUhZUDh4b0NPNEJ2VFkybndBRHRQQ1c4VFZ2SUx5S1M3QS91eU5aSnFVTUxvVXp1Uks0K1kvQU94eHhMMGVBWEE3dHNkZys2ZkJBS2oyWFlLRzFYY0RpcnVRQjNhem1LMDJZMFlqK21UeURkb0ZNUWhzVUdXTTlVeVBjbS9tM29jTkMzWXRYL1FOeTljNkdBR2FlRktOZW52OVA4NGxHb0NVN2VhcU1sRTVKSHhmaVI2QmFGenNMTkpvR0oiLCJtYWMiOiJhYmY4OTVmYjZhMjc1YjMwZjFhMjg2NzQyMjUwNzE3ZTA5Njk5NmVmOTcwMmZiMDQwNzkxZTk1YjE0YTc0ZTU5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:17:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZBcFh3aTgyWGxjRFNTUEN6c2E2UkE9PSIsInZhbHVlIjoiUzhDeW9UV3ZMc3hMdnR1TEtPL3JPK0NsNnV4MERrSGxPcHdEVDhwWXJKZDM3dlc2R2lFMXUvZmxpR0kxeHRkU2JFYkdKd2xWekVCeUhxb2VwVE1XSytKWnVKQzJmZnROVnJPN2VCbURlVy9MWWtuczhoblVLV0Z6b1k4SnBFemdQMDk5ZG54eVpqWFRZV2hNS2gvQU5LT2pENVNMOU42ZmNtaUdZR0lQWm1nOXFDN3lVN2s1N2xraUFJWHpieUdjWERPZUpMa0FUTmNNSDM2eWlrdVprK2tHeHdjVUU5VzZOU0lJTkxLUi95Q2VpUFIxVnhkaU8xeDBNT2h4MmpMbFR3Z2xheUFmVyszdDFLTTB2RVkyb3RqMVY4Yko1VnB4VXFnNWxSNDlkR1kyVWJvM2w2czZNSVBpL0RxZzY0SG11MjlJMVJFeGpHWWE3Q3dmZkZudkt6SVhnUkJmYmlvZ2M1ZysyOWxTVnFQeGFpeFhLTzdYUnk4cmFqbWJFQm1yS1FUSHpReHVzTmJKNDVVVjhqTk16T1FTclJMazRsR2c2NS8vb2lqNVpjeXo0V3JPUThZV01kZk1ZWlhrRlh0dHZIQjdLaDBLbTMvUWJjKzVDNnFBc1FabXVTZDVTWFBaRWQ5aUZLaXViZWhFdy9IbTIyMjhGZ0M4enc2aVh5a0QiLCJtYWMiOiI2MDQwNGFmNGJmYTJkMzg4MDY5MWI2YzUzOWMyNmVhZTVmMzQ5MGFlNDY3YTlmMzQxYjI1ZTA2ZmI1ZDVlZjM2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:17:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067421551\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-300786443 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300786443\", {\"maxDepth\":0})</script>\n"}}