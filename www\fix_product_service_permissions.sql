-- حل مشكلة صلاحيات Product Service
-- تشغيل هذه الأوامر في قاعدة البيانات

-- 1. التحقق من وجود الصلاحيات المطلوبة
SELECT name FROM permissions WHERE name LIKE '%product%service%';

-- 2. إضافة الصلاحيات إذا لم تكن موجودة
INSERT IGNORE INTO permissions (name, guard_name, created_at, updated_at) VALUES
('manage product & service', 'web', NOW(), NOW()),
('create product & service', 'web', NOW(), NOW()),
('edit product & service', 'web', NOW(), NOW()),
('delete product & service', 'web', NOW(), NOW()),
('show product & service', 'web', NOW(), NOW());

-- 3. التحقق من الأدوار الموجودة
SELECT name FROM roles WHERE name IN ('company', 'accountant', 'Cashier', 'SUPER FIESR');

-- 4. إعطاء صلاحيات Product Service لدور company
INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT p.id, 'Spatie\\Permission\\Models\\Role', r.id
FROM permissions p, roles r
WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service', 'show product & service')
AND r.name = 'company';

-- 5. إعطاء صلاحيات Product Service لدور accountant
INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT p.id, 'Spatie\\Permission\\Models\\Role', r.id
FROM permissions p, roles r
WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service', 'show product & service')
AND r.name = 'accountant';

-- 6. إعطاء صلاحيات Product Service لدور Cashier
INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT p.id, 'Spatie\\Permission\\Models\\Role', r.id
FROM permissions p, roles r
WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service', 'show product & service')
AND r.name = 'Cashier';

-- 7. إعطاء صلاحيات Product Service لدور SUPER FIESR
INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT p.id, 'Spatie\\Permission\\Models\\Role', r.id
FROM permissions p, roles r
WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service', 'show product & service')
AND r.name = 'SUPER FIESR';

-- 8. التحقق من صلاحيات المستخدم الحالي (استبدل USER_ID برقم المستخدم)
-- SELECT u.name, u.type, r.name as role_name, p.name as permission_name
-- FROM users u
-- LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id
-- LEFT JOIN roles r ON mhr.role_id = r.id
-- LEFT JOIN model_has_permissions mhp ON r.id = mhp.model_id AND mhp.model_type = 'Spatie\\Permission\\Models\\Role'
-- LEFT JOIN permissions p ON mhp.permission_id = p.id
-- WHERE u.id = USER_ID AND p.name LIKE '%product%service%';

-- 9. إعطاء صلاحيات مباشرة لمستخدم محدد (إذا لزم الأمر)
-- استبدل USER_ID برقم المستخدم
-- INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
-- SELECT p.id, 'App\\Models\\User', USER_ID
-- FROM permissions p
-- WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service', 'show product & service');

-- 10. التحقق النهائي من الصلاحيات
SELECT 
    u.id,
    u.name,
    u.type,
    r.name as role_name,
    COUNT(p.id) as product_service_permissions
FROM users u
LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id
LEFT JOIN roles r ON mhr.role_id = r.id
LEFT JOIN model_has_permissions mhp ON r.id = mhp.model_id AND mhp.model_type = 'Spatie\\Permission\\Models\\Role'
LEFT JOIN permissions p ON mhp.permission_id = p.id AND p.name LIKE '%product%service%'
GROUP BY u.id, u.name, u.type, r.name
HAVING product_service_permissions > 0
ORDER BY u.id;
