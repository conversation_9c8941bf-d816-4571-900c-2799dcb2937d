<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReceiptVoucher extends Model
{
    use HasFactory;
    protected $guarded=[];
    protected $with = ['creator','receiptFrom'];
    protected $table="voucher_receipts";

    /**
     * Get the creator of the record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the updater of the record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function receiptFrom()
    {
        return $this->belongsTo(User::class, 'receipt_from_user_id');
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class, 'shift_id', 'id');
    }
    public function warehouse()
    {
        return $this->belongsTo(warehouse::class,'warehouse_id','id');
    }
}
