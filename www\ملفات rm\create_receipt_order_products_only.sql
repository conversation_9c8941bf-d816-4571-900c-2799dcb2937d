-- إن<PERSON><PERSON><PERSON> جدول منتجات أوامر الاستلام فقط
-- مل<PERSON> SQL مبسط لحل مشكلة الفشل

-- حذف الجدول إذا كان موجود
DROP TABLE IF EXISTS `receipt_order_products`;

-- إن<PERSON>اء جدول منتجات أوامر الاستلام
CREATE TABLE `receipt_order_products` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `receipt_order_id` bigint(20) UNSIGNED NOT NULL,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `quantity` decimal(15,2) NOT NULL,
    `unit_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `total_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `expiry_date` date DEFAULT NULL,
    `is_return` tinyint(1) NOT NULL DEFAULT 0,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `receipt_order_products_receipt_order_id_index` (`receipt_order_id`),
    KEY `receipt_order_products_product_id_index` (`product_id`),
    KEY `receipt_order_products_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- التحقق من نجاح الإنشاء
SELECT 'تم إنشاء جدول منتجات أوامر الاستلام بنجاح' as status;
DESCRIBE receipt_order_products;

-- اختبار إدراج بيانات تجريبية
INSERT INTO `receipt_order_products` (
    `receipt_order_id`, `product_id`, `quantity`, 
    `unit_cost`, `total_cost`, `created_at`, `updated_at`
) VALUES (
    1, 1, 10.00, 
    5.50, 55.00, NOW(), NOW()
);

-- عرض البيانات التجريبية
SELECT * FROM receipt_order_products WHERE receipt_order_id = 1;

-- حذف البيانات التجريبية
DELETE FROM receipt_order_products WHERE receipt_order_id = 1 AND product_id = 1;

-- رسالة النجاح
SELECT 
    'تم إنشاء جدول منتجات أوامر الاستلام بنجاح!' as message,
    NOW() as created_at;
