# نظام تخصيص صفحة الدخول - Login Page Customization System (محسن)

## نظرة عامة
تم تطوير وتحسين نظام شامل لتخصيص صفحة الدخول يسمح لـ Super Admin بتخصيص مظهر صفحة الدخول بالكامل مع دعم محسن للصور والألوان.

## ✨ التحسينات الجديدة

### 🔧 إصلاحات المشاكل الرئيسية
- **إصلاح مشكلة عرض الصور**: الآن يدعم جميع أنواع الصور بامتداداتها الأصلية
- **تحسين معاينة الصور**: معاينة فورية محسنة مع تأثيرات بصرية
- **إدارة أفضل للملفات**: حذف تلقائي للملفات القديمة عند رفع ملفات جديدة
- **تحقق محسن من الملفات**: فحص شامل لنوع وحجم وجودة الصور

### 🎨 الميزات المحسنة

#### 1. صور الخلفية (Background Images)
- ✅ إمكانية رفع 3 صور خلفية مختلفة
- ✅ دعم صيغ: JPG, JPEG, PNG, GIF, BMP, WEBP
- ✅ حد أقصى لحجم الملف: 20 ميجابايت
- ✅ معاينة فورية محسنة للصور المرفوعة
- 🆕 دعم السحب والإفلات (Drag & Drop)
- 🆕 تحقق من دقة الصور وتوصيات التحسين
- 🆕 عرض محسن للصور في صفحة الدخول مع تأثيرات شفافية

#### 2. تخصيص الألوان (Color Customization)
- ✅ **اللون الأساسي**: لون الأزرار والعناصر التفاعلية
- ✅ **لون الخلفية**: لون خلفية منطقة تسجيل الدخول
- ✅ منتقي ألوان تفاعلي مع معاينة فورية
- 🆕 معاينة مباشرة للألوان أثناء التغيير
- 🆕 تأثيرات بصرية محسنة للبطاقات والعناصر

#### 3. تخصيص الشعارات (Logo Customization)
- ✅ **شعار مخصص**: شعار خاص بصفحة الدخول
- ✅ **أيقونة التبويب**: favicon مخصص لصفحة الدخول
- ✅ دعم جميع صيغ الصور الشائعة + ICO للـ favicon
- 🆕 معاينة محسنة مع تأثيرات بصرية
- 🆕 دعم أحجام مختلفة مع توصيات للأبعاد المثلى

### 4. خيارات العرض (Display Options)
- تفعيل/إلغاء تفعيل التخصيص
- تأثيرات حركية للخلفية:
  - عرض شرائح (Slideshow)
  - تأثير التلاشي (Fade Effect)

## كيفية الوصول للنظام

### للـ Super Admin:
1. اذهب إلى **الإعدادات** → **System Settings**
2. ابحث عن قسم **"Login Page Customization"**
3. أو استخدم الرابط المباشر: `/settings#login-customization`

## كيفية الاستخدام

### 1. تفعيل النظام
- اختر **"Enable"** من قائمة **"Enable Login Customization"**
- احفظ التغييرات

### 2. رفع صور الخلفية
- اضغط على **"Choose file here"** لكل صورة خلفية
- اختر الصورة المطلوبة
- ستظهر معاينة فورية للصورة

### 3. تخصيص الألوان
- استخدم منتقي الألوان لاختيار **اللون الأساسي**
- اختر **لون الخلفية** المناسب
- ستظهر معاينة فورية للألوان

### 4. رفع الشعارات
- ارفع **شعار مخصص** لصفحة الدخول
- ارفع **أيقونة تبويب** مخصصة
- ستظهر معاينة فورية للشعارات

### 5. اختيار تأثيرات الحركة
- اختر **"Slideshow"** لعرض الصور بشكل متتالي
- اختر **"Fade Effect"** لتأثير تلاشي ناعم
- اختر **"Disable"** لإلغاء التأثيرات

## 🔧 التحسينات التقنية

### 🆕 ملفات جديدة:
- `app/Helpers/LoginCustomizationHelper.php` - فئة مساعدة لإدارة الصور
- `public/assets/js/login-customization.js` - JavaScript محسن للتفاعل
- `public/assets/css/login-customization.css` - أنماط CSS محسنة
- `public/storage/login_customization/` - مجلد تخزين الصور المخصصة

### 🔄 ملفات محسنة:
- `resources/views/settings/index.blade.php` - واجهة محسنة مع معاينة فورية
- `resources/views/layouts/auth.blade.php` - عرض محسن للصور والألوان
- `app/Http/Controllers/SystemController.php` - معالجة محسنة للملفات
- `routes/web.php` - Route محسن للأمان
- `resources/lang/ar.json` - ترجمات محسنة

### 🎯 تحسينات الأداء:
- **إدارة ذكية للملفات**: حذف تلقائي للملفات القديمة
- **تحقق محسن**: فحص شامل لنوع وحجم الملفات
- **معاينة سريعة**: تحميل فوري للصور بدون إعادة تحديث الصفحة
- **تحسين الذاكرة**: استخدام أمثل للموارد عند معالجة الصور

## الإعدادات في قاعدة البيانات

يتم حفظ الإعدادات التالية في جدول `settings`:

```sql
- login_bg_image_1: اسم ملف الصورة الأولى
- login_bg_image_2: اسم ملف الصورة الثانية  
- login_bg_image_3: اسم ملف الصورة الثالثة
- login_primary_color: اللون الأساسي (hex)
- login_background_color: لون الخلفية (hex)
- login_custom_logo: اسم ملف الشعار المخصص
- login_favicon: اسم ملف أيقونة التبويب
- enable_login_customization: تفعيل/إلغاء التخصيص (on/off)
- login_bg_animation: نوع التأثير الحركي (off/slideshow/fade)
```

## الأمان والصلاحيات

- **الوصول محدود**: فقط Super Admin يمكنه الوصول لهذه الإعدادات
- **التحقق من الملفات**: يتم التحقق من نوع وحجم الملفات المرفوعة
- **التشفير**: جميع الإعدادات محفوظة بشكل آمن في قاعدة البيانات

## استكشاف الأخطاء

### مشكلة: لا تظهر الصور
- تأكد من أن مجلد `public/storage/login_customization/` موجود
- تأكد من صلاحيات الكتابة على المجلد
- تحقق من حجم الملف (أقل من 20 ميجابايت)

### مشكلة: لا تطبق الألوان
- تأكد من تفعيل **"Enable Login Customization"**
- امسح cache المتصفح
- تحقق من صحة كود الألوان (hex format)

### مشكلة: لا يظهر الشعار المخصص
- تأكد من رفع الشعار بصيغة صحيحة
- تأكد من تفعيل التخصيص
- تحقق من مسار الملف

## الدعم الفني

للحصول على المساعدة:
1. تحقق من ملف الـ logs: `storage/logs/laravel.log`
2. تأكد من إعدادات الخادم
3. راجع صلاحيات الملفات والمجلدات

---

**ملاحظة**: هذا النظام متاح فقط لـ Super Admin ويؤثر على جميع المستخدمين في النظام.
