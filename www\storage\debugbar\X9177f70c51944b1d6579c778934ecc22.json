{"__meta": {"id": "X9177f70c51944b1d6579c778934ecc22", "datetime": "2025-06-06 20:34:48", "utime": **********.661334, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242087.20493, "end": **********.661363, "duration": 1.456432819366455, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749242087.20493, "relative_start": 0, "end": **********.389066, "relative_end": **********.389066, "duration": 1.184135913848877, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.389094, "relative_start": 1.184164047241211, "end": **********.661365, "relative_end": 2.1457672119140625e-06, "duration": 0.27227091789245605, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45709464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.55383, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.572231, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.633699, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.643364, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.0352, "accumulated_duration_str": "35.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4799092, "duration": 0.00538, "duration_str": "5.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 15.284}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4917831, "duration": 0.02151, "duration_str": "21.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 15.284, "width_percent": 61.108}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.523275, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 76.392, "width_percent": 3.21}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.555346, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 79.602, "width_percent": 3.58}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.574146, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 83.182, "width_percent": 3.523}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.602889, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 86.705, "width_percent": 4.545}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.612446, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 91.25, "width_percent": 2.528}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.620305, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 93.778, "width_percent": 3.58}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.636829, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.358, "width_percent": 2.642}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1447522985 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1447522985\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-405992674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-405992674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-640681847 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-640681847\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1055955532 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=v1wjex%7C1749242055720%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjQxZUh3cnVZc0twMFRSM05KdHI1RkE9PSIsInZhbHVlIjoiWXh5UjV0clJDbnMra2dseU1kNk9uMVFYQ0N2UnYxWFNMU3NTZTdESFNOd1NQTC9vWEdRR1dhMnNhUWZWUFhnODIrbUh2U1MycGwzNlNkMG1DZEc4dW9jVThScXNoelllQ2ZpT3ZmNUcxWnE0Ym5pRUQ2azRkRnR2d2YvUFR3NE1KZGlhb2M5aGN6USs4SkZKTjIyU0R0MXBFSXFVUlNmVWV6RzlSdFYwOExvc3cxNkpWQlBIU3p0eE5iUU1UTVpGSjFwek44eHFJSFRCOHRRWHRyLzhuVkRCQWNaZUF1OHFJbEJEeW1BUmJIc1dXcU5OamFlY0hoZVVDUUFZcjFGWmFiUmlGRG5qb2NkSEV4ODcrcUtaWUtaa2YrM3hiYXU1emtjWTFiaWl0QUZqUmN2UW5Pbjc1WWozaytMaEJ3K1NuQmtYZWwzM0xPa2JWbVdVMFV6azl4Z0FsRFVPSlpETVFmQ2R2cTlPRTM1SjdnTnZKSVdPU3kwRXRBYy9CRXZmTXNzeEVzYnRHK2gzcGRQU2g1WUdlR0R1Z3pCaTFZc2dNTTFYSWVzNTVtaVBUYkZLQmYzRXQ1SEx3M1ZoUVZIdkNXbERoRDh2VklpN0NDakZzSngvNFUyWlJTSWNaY0lKT0lURE8zNE01UUEzNkJ4MnZZeGcxekRGcGhTZkViRysiLCJtYWMiOiJiNGExMjVhYTUyYjExMDY2Yjk1MTZmODE5N2MxNWQ4ODY1MDQzODA3ZjkzZmNkMGJlN2FlNzZiMDNiZGViZTdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFHMUl4YXRiUHoyL1I3OU94KzRPd3c9PSIsInZhbHVlIjoiWnltL25hNmN0VSs4UVVoMkRKem1TRzM3SWpISDZKbDdoQkplM0tOWWFiTW9IU0tpRng0OEp3RE5Gb0VyV0xFaXhZd2crYjdxekk0VG5BYW1QVEd6WWx6VVdQTlJrb01JTHFsZldRQ1NWU3E3a05DMmY2Lzl1WURrY2syRDlNYjF3aEJ3VGZTa0ZlWUJOOS9BUXZqNStMdTBUMXpyMWNmNS9OdnhJRkExS1NOTmtDcDNmMTlUTG4vUWNETDBocW5jN1RaSjRNaHNONGhaaFNzeEZBaWVtMGo1NWRtUXdqMDBWMU04UjBuTEJqbXFjdGhnc29MR2ZiZmJIRHFGcTh0dWdIZWpwNlFuSHFuQVdvbS9RSU1RTzgxWW1JYWhjWDA3RUhjbThoOCtyYXR3b3RCZktCVXViYTNLNFM0ek84Vjk5ZjdsVC95aWY1blBCNUZOQTFkWEJnQldWS0NTc0J4V1pWRGM0N0o3RXRySGswRWdzejBuVkU0cjlpc0dPRkdweVhmLzNKSC9ndEtTYW1ROFNQUVZ0WHJhZ29UMUlQejd3UzBYdCtJRHQrVFduUXZ1ekhjUkc5MWtLSzY5dURMNmJLUUVMWlB0ZWNYT2cxOFBvRTY5Vk1CbVlMbllxYncxaXlYYzJ0MDhGMjBFTy9KYm1FY3llMHRsWHRSWGVURWgiLCJtYWMiOiJlMDQ2ZDExYmE2NmRkZTkxMjk4YWFkZDBkMjhlN2EyZDAwZDk1NDM2MjkzODFhMzdjNjQwZDUzZTBhOGQ4M2QxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055955532\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1342055315 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ib82UhgW7QbmPWC6dET38U6gjVREpdbmeIYDPeql</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342055315\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1330162873 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5YdFVwQXhQazNNc2pRbkNhMGxsdGc9PSIsInZhbHVlIjoieXUwaXArNUFTdE9sZ0NuWTRCM09RRDFKRlNyaUQyZEpQU2RsaENvTkpYNE9Ta0FvYlUvcDg1elhJOXBNL1RYcFVhdDNDTzdEWnpMV3I5MzJCcDRsaWp1aDZMK2VHa2paRnVnY3VLSldRU0xvTDN4S2w4OHdMaHgyKzRXdWpHdHNxQlNmYkNwOTc5WTk1OWI2aDdCcmtaSWdZR2xxcG9KVGdJNWlGRGhwTTN2UjhuS1VkOWJwb3pTeUU1Y3FXUHFIYWM4a1RIeTY5cjYwWVVPdG9aQzFPTkFqTHMvTzRHa3RQYmppcGx4SU5HYVoyUlM2eDkzc1VwcnZaNlRkd3lnRzAxNlRhRDBpcHlEQmN6ME5LRno2YnEvR0FVdXFuUHJ1S1dyMytUdWdUd0JJUUlVQ2I2T2JWYkRPTGNlWW82ZFp5Wk5DTVRLbFBsaXhJcDJ3VWJtM053NXJpMWVFc2V2N210bmZsdi81Y0EzYWpOdHN2dDNVYjI0TksyNjdhcmZhcVlUZmR6NFY0aHRPNWpHNkwwcGZoM3c0SWhvWWtTZG1yb3BMKzU2VlJYaVVMSXBiSXZ3eFVRRXg5bThCQWk5N3FxeEVGVFhnQnB4ME1FcGwrRW5ad09XKzU2UTNQd1E5L2JEbXBQNWlGM0hXMXZjYTYvc0lhaG9WZXFUMTlUU2wiLCJtYWMiOiI1ODY5NGU2Y2QzMjUyNDhhNTdlMzkyNzQxNTMzZTg2NjRmZTU1ZjZhNzhjMjhhNzVhZjczOTFkNzE1NzZmMzUwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im02NFBGaGZ1ZTMxTkpuNFFsSnQrMFE9PSIsInZhbHVlIjoibEg2bFB6RkRwM2RzVjh5dmdIdm1ycm1KT2lHZE1VajNCT0VqZnY5NTlHU3hJYXpzRTJ1QVIrdHB5MGlsSEdPNmdEbXpXK0lIV2Juc2JySG5jY250ZGc5S3QreGRhRlBPajFseXF1WjR1eVF5dzJRNVRnclMvRDlQcklnclRPNUd0T00vRjYvQkRiMXFIeDdNb3NvZ0ozVm5BSTZpMTRpaTh3VDd5emFkZ2t2Y3Z6QjFacHFRQXNJMEt5Y00yMjE4WTNtUEcxczNNamhPM29ZeXRoQ2xmV3pDVS9jbXpoNjVrblc0VXIrWnppYlFJSC9PSTZrb2JYZ3BPVXpOT2JjenNIams3WUF0c3FZS21ndHhKc3RuQXZBbERwb3h4YlM2VExjby85Q0IwTFNBTnJ6RzgvUXRuSks3UTA0WUdUWmhTa0w0WHI2ZUIrdmZNQlU3c2FIdllWeFJNLzlBVk5RSVRselJNNmE5NWNCMDNEVlFiSFZRdjdndGl1NlhYRFBPbzE5eTdDL05CVkZYNVpSR1cycCs5SzhVdXU1UVJwMDlLTFNGQkRHb1Q0dExjV1ZzaWpyaU1ocU9JcFNtWkJwOFcya0dJa045VzZ5cHI2TG5vNHk5dFRIR3JWMWQxMHFjalZ1M1dkaUFzVkpEY0dhUE5kTW5pUWZBQ3Zia1NJeFgiLCJtYWMiOiI0ZDU3ZTY2NjBiNDViN2Q4NDcyZjRiYmFmNDdkZDM1YjA3YTY1Zjk1MzlmNWUwMDRjNGNjOTU1NzE3NWQ4ZmI1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5YdFVwQXhQazNNc2pRbkNhMGxsdGc9PSIsInZhbHVlIjoieXUwaXArNUFTdE9sZ0NuWTRCM09RRDFKRlNyaUQyZEpQU2RsaENvTkpYNE9Ta0FvYlUvcDg1elhJOXBNL1RYcFVhdDNDTzdEWnpMV3I5MzJCcDRsaWp1aDZMK2VHa2paRnVnY3VLSldRU0xvTDN4S2w4OHdMaHgyKzRXdWpHdHNxQlNmYkNwOTc5WTk1OWI2aDdCcmtaSWdZR2xxcG9KVGdJNWlGRGhwTTN2UjhuS1VkOWJwb3pTeUU1Y3FXUHFIYWM4a1RIeTY5cjYwWVVPdG9aQzFPTkFqTHMvTzRHa3RQYmppcGx4SU5HYVoyUlM2eDkzc1VwcnZaNlRkd3lnRzAxNlRhRDBpcHlEQmN6ME5LRno2YnEvR0FVdXFuUHJ1S1dyMytUdWdUd0JJUUlVQ2I2T2JWYkRPTGNlWW82ZFp5Wk5DTVRLbFBsaXhJcDJ3VWJtM053NXJpMWVFc2V2N210bmZsdi81Y0EzYWpOdHN2dDNVYjI0TksyNjdhcmZhcVlUZmR6NFY0aHRPNWpHNkwwcGZoM3c0SWhvWWtTZG1yb3BMKzU2VlJYaVVMSXBiSXZ3eFVRRXg5bThCQWk5N3FxeEVGVFhnQnB4ME1FcGwrRW5ad09XKzU2UTNQd1E5L2JEbXBQNWlGM0hXMXZjYTYvc0lhaG9WZXFUMTlUU2wiLCJtYWMiOiI1ODY5NGU2Y2QzMjUyNDhhNTdlMzkyNzQxNTMzZTg2NjRmZTU1ZjZhNzhjMjhhNzVhZjczOTFkNzE1NzZmMzUwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im02NFBGaGZ1ZTMxTkpuNFFsSnQrMFE9PSIsInZhbHVlIjoibEg2bFB6RkRwM2RzVjh5dmdIdm1ycm1KT2lHZE1VajNCT0VqZnY5NTlHU3hJYXpzRTJ1QVIrdHB5MGlsSEdPNmdEbXpXK0lIV2Juc2JySG5jY250ZGc5S3QreGRhRlBPajFseXF1WjR1eVF5dzJRNVRnclMvRDlQcklnclRPNUd0T00vRjYvQkRiMXFIeDdNb3NvZ0ozVm5BSTZpMTRpaTh3VDd5emFkZ2t2Y3Z6QjFacHFRQXNJMEt5Y00yMjE4WTNtUEcxczNNamhPM29ZeXRoQ2xmV3pDVS9jbXpoNjVrblc0VXIrWnppYlFJSC9PSTZrb2JYZ3BPVXpOT2JjenNIams3WUF0c3FZS21ndHhKc3RuQXZBbERwb3h4YlM2VExjby85Q0IwTFNBTnJ6RzgvUXRuSks3UTA0WUdUWmhTa0w0WHI2ZUIrdmZNQlU3c2FIdllWeFJNLzlBVk5RSVRselJNNmE5NWNCMDNEVlFiSFZRdjdndGl1NlhYRFBPbzE5eTdDL05CVkZYNVpSR1cycCs5SzhVdXU1UVJwMDlLTFNGQkRHb1Q0dExjV1ZzaWpyaU1ocU9JcFNtWkJwOFcya0dJa045VzZ5cHI2TG5vNHk5dFRIR3JWMWQxMHFjalZ1M1dkaUFzVkpEY0dhUE5kTW5pUWZBQ3Zia1NJeFgiLCJtYWMiOiI0ZDU3ZTY2NjBiNDViN2Q4NDcyZjRiYmFmNDdkZDM1YjA3YTY1Zjk1MzlmNWUwMDRjNGNjOTU1NzE3NWQ4ZmI1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1330162873\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}