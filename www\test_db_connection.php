<?php
/**
 * اختبار الاتصال بقاعدة البيانات البعيدة
 * Test Remote Database Connection
 */

// قراءة إعدادات قاعدة البيانات من ملف .env
function loadEnv($file) {
    $env = [];
    if (file_exists($file)) {
        $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                list($key, $value) = explode('=', $line, 2);
                $env[trim($key)] = trim($value, '"\'');
            }
        }
    }
    return $env;
}

$env = loadEnv(__DIR__ . '/.env');

$host = $env['DB_HOST'] ?? '127.0.0.1';
$port = $env['DB_PORT'] ?? '3306';
$database = $env['DB_DATABASE'] ?? '';
$username = $env['DB_USERNAME'] ?? '';
$password = $env['DB_PASSWORD'] ?? '';

echo "<h2>🔍 اختبار الاتصال بقاعدة البيانات</h2>";
echo "<h3>Database Connection Test</h3>";

echo "<div style='background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<strong>إعدادات الاتصال / Connection Settings:</strong><br>";
echo "Host: " . htmlspecialchars($host) . "<br>";
echo "Port: " . htmlspecialchars($port) . "<br>";
echo "Database: " . htmlspecialchars($database) . "<br>";
echo "Username: " . htmlspecialchars($username) . "<br>";
echo "Password: " . (empty($password) ? 'Empty' : '***') . "<br>";
echo "</div>";

// اختبار الاتصال
try {
    echo "<h3>🔄 جاري الاختبار... / Testing Connection...</h3>";

    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 10
    ]);

    echo "<div style='background: #d4edda; color: #155724; padding: 15px; margin: 10px 0; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "✅ <strong>نجح الاتصال! / Connection Successful!</strong><br>";
    echo "تم الاتصال بقاعدة البيانات بنجاح / Successfully connected to database<br>";
    echo "</div>";

    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT VERSION() as version, NOW() as current_time");
    $result = $stmt->fetch();

    echo "<div style='background: #e2f3ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>معلومات الخادم / Server Info:</strong><br>";
    echo "MySQL Version: " . htmlspecialchars($result['version']) . "<br>";
    echo "Server Time: " . htmlspecialchars($result['current_time']) . "<br>";
    echo "</div>";

    // اختبار الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>الجداول الموجودة / Available Tables (" . count($tables) . "):</strong><br>";
    if (count($tables) > 0) {
        echo "<ul>";
        foreach (array_slice($tables, 0, 10) as $table) {
            echo "<li>" . htmlspecialchars($table) . "</li>";
        }
        if (count($tables) > 10) {
            echo "<li>... و " . (count($tables) - 10) . " جدول آخر / and " . (count($tables) - 10) . " more tables</li>";
        }
        echo "</ul>";
    } else {
        echo "لا توجد جداول / No tables found";
    }
    echo "</div>";

} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px 0; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "❌ <strong>فشل الاتصال! / Connection Failed!</strong><br>";
    echo "خطأ / Error: " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>الحلول المقترحة / Suggested Solutions:</strong><br>";
    echo "1. تحقق من صحة بيانات الاتصال / Check connection credentials<br>";
    echo "2. تأكد من أن الخادم يعمل / Ensure server is running<br>";
    echo "3. تحقق من إعدادات الجدار الناري / Check firewall settings<br>";
    echo "4. تأكد من السماح بالاتصالات الخارجية / Allow external connections<br>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>تم إنشاء هذا الاختبار في: " . date('Y-m-d H:i:s') . "</small></p>";
// اختبار الاتصال بقاعدة البيانات
$host = '127.0.0.1';
$port = '3306';
$database = 'ty';
$username = 'root';
$password = '';

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح!\n";
    echo "اسم قاعدة البيانات: $database\n";
    echo "الخادم: $host:$port\n";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "قاعدة البيانات الحالية: " . $result['current_db'] . "\n";
    
    // عرض الجداول الموجودة
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "\nعدد الجداول الموجودة: " . count($tables) . "\n";
    
    if (count($tables) > 0) {
        echo "أول 10 جداول:\n";
        foreach (array_slice($tables, 0, 10) as $table) {
            echo "- $table\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات:\n";
    echo "الرسالة: " . $e->getMessage() . "\n";
    echo "الكود: " . $e->getCode() . "\n";
    
    // اقتراحات لحل المشكلة
    echo "\n🔧 اقتراحات لحل المشكلة:\n";
    echo "1. تأكد من تشغيل خادم MySQL\n";
    echo "2. تأكد من وجود قاعدة البيانات 'ty'\n";
    echo "3. تأكد من صحة بيانات المستخدم\n";
    echo "4. تأكد من إعدادات الشبكة والمنافذ\n";
}
?>
