<?php
// اختبار الاتصال بقاعدة البيانات
$host = '127.0.0.1';
$port = '3306';
$database = 'ty';
$username = 'root';
$password = '';

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح!\n";
    echo "اسم قاعدة البيانات: $database\n";
    echo "الخادم: $host:$port\n";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "قاعدة البيانات الحالية: " . $result['current_db'] . "\n";
    
    // عرض الجداول الموجودة
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "\nعدد الجداول الموجودة: " . count($tables) . "\n";
    
    if (count($tables) > 0) {
        echo "أول 10 جداول:\n";
        foreach (array_slice($tables, 0, 10) as $table) {
            echo "- $table\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات:\n";
    echo "الرسالة: " . $e->getMessage() . "\n";
    echo "الكود: " . $e->getCode() . "\n";
    
    // اقتراحات لحل المشكلة
    echo "\n🔧 اقتراحات لحل المشكلة:\n";
    echo "1. تأكد من تشغيل خادم MySQL\n";
    echo "2. تأكد من وجود قاعدة البيانات 'ty'\n";
    echo "3. تأكد من صحة بيانات المستخدم\n";
    echo "4. تأكد من إعدادات الشبكة والمنافذ\n";
}
?>
