{"__meta": {"id": "X51402b1d2f6fc1a5c18f212b9a32c264", "datetime": "2025-06-06 20:41:21", "utime": **********.552828, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242479.831432, "end": **********.552871, "duration": 1.7214388847351074, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1749242479.831432, "relative_start": 0, "end": **********.334434, "relative_end": **********.334434, "duration": 1.5030019283294678, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.334456, "relative_start": 1.5030238628387451, "end": **********.552875, "relative_end": 4.0531158447265625e-06, "duration": 0.21841907501220703, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44776248, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02304, "accumulated_duration_str": "23.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4361482, "duration": 0.019420000000000003, "duration_str": "19.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.288}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.491331, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.288, "width_percent": 5.946}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.500299, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 90.234, "width_percent": 4.948}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5260298, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.182, "width_percent": 4.818}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1516000 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1516000\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1279578797 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242413154%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImV4WE5ENXBpWVVzbnhQQ0FNVTl3Q3c9PSIsInZhbHVlIjoicFpaVE9IRlcwTHJwcmc2U0phLzgxMllKZVZ0RWIvU21XOWJnendzaSsvdy82M2tGUzNvTU5PNWgrakxXT25VK1hBZVBlZ0d3Z3JpUHArMEc2a0pZRWhPVlNJMFhKSXlEckp0VFFleTlTblVJYUQrYlpib3JzVUYwNWJOcHA5bk9Ccm1PZXM1V0RxcVlIaTJIL2lqekFEUlRkeVBYSHlTNWtVUUlMM3VLNHFJRWVaQXU4S2dWRjlraGM1bkU5Wm5nVlA4VTBTZi9UaFhIUzZtdW5nTzFxTkVXTUxxMTZRYVRsdUcydWJQOFZndlN3TW1RM29mSWZsV0dwNHBaSTIxeGphQTh1ZXl0WXAvMk1NekdNbEU5M25QL1MwckFlT1dPWCt5NWVsYWhyWmdWcmZzZXdtZTViaTU4SzZXMXlxenVlbWc5aWZ4NlJDd0daSXR4Z2hrd25qRllnVGh2WDh2N1JBajg5RDc0c2VZS1cvc3QxMnNLbzBCNk96dXJIaHpMampBSG1VZTROZHpvcjVlY3RsVnZBUW5uWFFodmxJZDlrQ3QzdmQzTGdzZkVzMC8zM0oybldESUpFNmV5MmRkcTBrbWZ6NmR4UmpOdjVnR01FNDAzVVhvdmxKRGRuNjBKYjZ4OEwwYUxMVC9aTUZCc0w1MlRpMmpRSm13SmpwQUEiLCJtYWMiOiJjYWEwMTEyMDZkMDI4MTA3ODdkNGY4NjIwZTQ5NjM3OTBlMjY1NmZmODk4NjVlZTRiOTZjMzUyNTQyYjliMGFlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkUxL0o3U2w5alRuSzBBUlhWb3QrUHc9PSIsInZhbHVlIjoiTHl1RW5jVTJkUGtPOWRkQU5VeWpvem1Rb25NQk5ENThYQUw3TXZzcmgrZU5GaC9oUkdUd2NPcTN0aG1lWlh3UXJkRURxb0o4Vlo0b2JTMjBtYVVZRnFFU3d0RmdqNGt2Rjk2c1B4TGtPNis4ZFNKQ2ErQUtHR05LM2tjV2ZVSk15OEVYRTFmSGlDSXFzWFV1alhNdE9uTTlJN3grNWdwaDM0dGtyZzRWdHVDNC94UE8vWSszanJFRHdNd3FjakdCeGtZMmpQRjhlaHg2QnlHcXdYS3VXc21tZjVlRStLem1MMmtFS2xjbTlEL0lFcnIyUDJmNUdWemxZc3hRMFhMVlZuWUJ2TnJpQUlZMGRJeGFPMVgvbVFPTEl0UkszR05xNTZHNXRlOWdiYW5KMld0bWQ2V1hjOUJxRENCTkU3ZlJJS1VIR3BPdDR3UW8vZUY4K2paSXdhS3p5MzlQbmMxWDAwSmhtd1I4Z0Roc0hJNGZnN2lZR1c2QWFLRHhvZVNPeVVoOWpLTkpxVitUR1htKzB0ZC9HdDAwVWd4M3IvV0gzY1Jja0M1ZlpnVHFvanJva0lIOFZNb0Z6VUZXMVo3MDRiS0NKZWZ1alhxd2RIRk1FRmIvOStWWXhGVVJkU0o0Mi9XNU8wWldBT0pCS3lmT3JIblJ4LzRZS2J4ZVdLeUYiLCJtYWMiOiJiYTY2NTkxYmY1YTJiZTg1NTEwMWQwZmUzYTNhYmI5YzQxN2IyZDcwN2U5ZTgxMjdmYmQ3MzZlNTAwMWVlYTRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279578797\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1792127135 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6Wt4M4bI8NbCUmFc1THCmhEuEOoDYpn2bPL8rDvY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792127135\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1274949377 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:41:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9nVlZlYnYvZzdjQ0szbHN0bGJxR1E9PSIsInZhbHVlIjoiZlNzTnp4empzWHQrdlpFR1ZXdGZ1OVI0SlNOdGJBbWtoWXQyczFETVhIc1BZT0w0dm8yL01rR3pobWNsejMybzMrOTlCNi9qdlM5WUN0NGM4UmlraytOMmJyM2JVSmVRZnhPQWY4UU5ocEswNjkrTTRRRlZjeGJzWVBxUWI5elpvV1dkOXNVZ2FqbUhiOVZoK0dING9ZaXJZT1ZzWnR2bGhOclFRcXRQTExQQnFVeE9aQTN0M2NKcW5SMXNQZWVhcVdkVi9jUko5NHZEKytsK0xOS2dSTkNpQWlva2xpTi9nZG9uQmcxcVdmL2Z2QVEvN2VRZGYzckt4aHJWMUd1bGQwaGtUNlpIVzEyMS9VYWZvUCtuNlZLeUhCUUhRQ1VtVE80SHdkQzZUZHFLRGZkMStvd01OMFNpRnErVjFYZUZYdExwd2JDR3AwN09sdXFIZlF4Wk44YlFNeVlHWkxKY1o2emxxR1NIdWJtWXFGRkhCdlgrZW5mNFhyemx1allpN0RsS3VpUkE3Q3RtemI0N0s0Y3J0UmpTcytPTk5LZTE4WlB5QWVZUUZXSjgveEZxbm1LeDVNRUdxN1M2MGpWd3g2NjdZWWdXYmtBbWhMcm9nN29DSCt1QkNlUWNHT0xkZWNwMGlHTnZ0anpMTGQxc05XTnRpd0xYeDhneFNVak4iLCJtYWMiOiI1ZGFiYWFkODg1MTE0NWVlMDU4YmU3NTg2NDk4MDIyZTAzOTc2YzRkYjFhM2E5NGZhNTQzOGYyYjJmOTM5MDAxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklabVkxd0F5QXkxZVlaeFI0RnZDdkE9PSIsInZhbHVlIjoiZmRCZnh1eVRDekRFMnY3N21CV3VjNkJ2TkpZMDZ4bWg3aTQwUGtSZTNsYk92VTRRYVlyMG9sbHFkbEgwZTl4VkpnMm5vY2E4dzRRWGNMM2JKMnBjcUdqUWF6bXVXa0h5ekZoQS9GSmY5Vm9vdVlpeW0wdjdKbGFnVnB1UVFzQm1IVW5TVTRRL2Fhd2JiT2FLL3Vlc0FqS3lObnZIdHhPdHk3NWlkTHJWa3p6RGVTNmcxcmEwczNwTmxkV3lNVG5BblhINU1xY3ZWbk5uWjI0eVQxNGVnTi9sUzdaSW5HVVhYdTRHQmpqRVc4em44MUZqbmtkVjVxWGxTdVlvWnk3QlFBMjZWQWNQUEJnODd6SlBtQmpTY0xXRjBwMnA5VnBNM013cFNRYXJVckkxWlJ4SkNqQU9xSm1rTk10OENkVGtsVitUNzJ2c1hoZFkrVEd6b0EzTFMremF5SEtRVjFMR1FLaFZpVEtjY3FaeTNUQWdkY0hmNVhBbU1UVm5XUVpsZ3NuamdpVnlEZW43MHAxdHZoRjVjTTI4bVFUWHl1YXVQSkVmbC95TG5HTURINGhIYXdEQTJ6QndFZ1J5clh5OGIrUEpJalM2R0ZjbXN6bjJ4c1FqRUNZNkVLZFdtTy9lbkMrZVY5NWdrS1BuaG9NY1cvYXlOZlp5d0Jab1Q0dXkiLCJtYWMiOiIwZWYwZWU0MGNkY2E0MDgyN2RmOWJiOTUyMzY2NGY5NTgwNjkzNDhkMWQ0ODBkZGRjMWI2MmUwODhlNjkxODI3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9nVlZlYnYvZzdjQ0szbHN0bGJxR1E9PSIsInZhbHVlIjoiZlNzTnp4empzWHQrdlpFR1ZXdGZ1OVI0SlNOdGJBbWtoWXQyczFETVhIc1BZT0w0dm8yL01rR3pobWNsejMybzMrOTlCNi9qdlM5WUN0NGM4UmlraytOMmJyM2JVSmVRZnhPQWY4UU5ocEswNjkrTTRRRlZjeGJzWVBxUWI5elpvV1dkOXNVZ2FqbUhiOVZoK0dING9ZaXJZT1ZzWnR2bGhOclFRcXRQTExQQnFVeE9aQTN0M2NKcW5SMXNQZWVhcVdkVi9jUko5NHZEKytsK0xOS2dSTkNpQWlva2xpTi9nZG9uQmcxcVdmL2Z2QVEvN2VRZGYzckt4aHJWMUd1bGQwaGtUNlpIVzEyMS9VYWZvUCtuNlZLeUhCUUhRQ1VtVE80SHdkQzZUZHFLRGZkMStvd01OMFNpRnErVjFYZUZYdExwd2JDR3AwN09sdXFIZlF4Wk44YlFNeVlHWkxKY1o2emxxR1NIdWJtWXFGRkhCdlgrZW5mNFhyemx1allpN0RsS3VpUkE3Q3RtemI0N0s0Y3J0UmpTcytPTk5LZTE4WlB5QWVZUUZXSjgveEZxbm1LeDVNRUdxN1M2MGpWd3g2NjdZWWdXYmtBbWhMcm9nN29DSCt1QkNlUWNHT0xkZWNwMGlHTnZ0anpMTGQxc05XTnRpd0xYeDhneFNVak4iLCJtYWMiOiI1ZGFiYWFkODg1MTE0NWVlMDU4YmU3NTg2NDk4MDIyZTAzOTc2YzRkYjFhM2E5NGZhNTQzOGYyYjJmOTM5MDAxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklabVkxd0F5QXkxZVlaeFI0RnZDdkE9PSIsInZhbHVlIjoiZmRCZnh1eVRDekRFMnY3N21CV3VjNkJ2TkpZMDZ4bWg3aTQwUGtSZTNsYk92VTRRYVlyMG9sbHFkbEgwZTl4VkpnMm5vY2E4dzRRWGNMM2JKMnBjcUdqUWF6bXVXa0h5ekZoQS9GSmY5Vm9vdVlpeW0wdjdKbGFnVnB1UVFzQm1IVW5TVTRRL2Fhd2JiT2FLL3Vlc0FqS3lObnZIdHhPdHk3NWlkTHJWa3p6RGVTNmcxcmEwczNwTmxkV3lNVG5BblhINU1xY3ZWbk5uWjI0eVQxNGVnTi9sUzdaSW5HVVhYdTRHQmpqRVc4em44MUZqbmtkVjVxWGxTdVlvWnk3QlFBMjZWQWNQUEJnODd6SlBtQmpTY0xXRjBwMnA5VnBNM013cFNRYXJVckkxWlJ4SkNqQU9xSm1rTk10OENkVGtsVitUNzJ2c1hoZFkrVEd6b0EzTFMremF5SEtRVjFMR1FLaFZpVEtjY3FaeTNUQWdkY0hmNVhBbU1UVm5XUVpsZ3NuamdpVnlEZW43MHAxdHZoRjVjTTI4bVFUWHl1YXVQSkVmbC95TG5HTURINGhIYXdEQTJ6QndFZ1J5clh5OGIrUEpJalM2R0ZjbXN6bjJ4c1FqRUNZNkVLZFdtTy9lbkMrZVY5NWdrS1BuaG9NY1cvYXlOZlp5d0Jab1Q0dXkiLCJtYWMiOiIwZWYwZWU0MGNkY2E0MDgyN2RmOWJiOTUyMzY2NGY5NTgwNjkzNDhkMWQ0ODBkZGRjMWI2MmUwODhlNjkxODI3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274949377\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}