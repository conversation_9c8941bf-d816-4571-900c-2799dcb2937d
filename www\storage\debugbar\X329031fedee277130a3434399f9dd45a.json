{"__meta": {"id": "X329031fedee277130a3434399f9dd45a", "datetime": "2025-06-07 07:30:14", "utime": **********.391877, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.394227, "end": **********.391906, "duration": 0.****************, "duration_str": "998ms", "measures": [{"label": "Booting", "start": **********.394227, "relative_start": 0, "end": **********.24134, "relative_end": **********.24134, "duration": 0.****************, "duration_str": "847ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.241358, "relative_start": 0.****************, "end": **********.391909, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022269999999999998, "accumulated_duration_str": "22.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3123512, "duration": 0.01993, "duration_str": "19.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.493}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.353092, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.493, "width_percent": 4.311}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.374818, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.803, "width_percent": 6.197}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281412786%7C4%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlQxN1ZVUmNhQ3RkelNFK2EzVTBqR1E9PSIsInZhbHVlIjoiU1VZSnREQkxQOWpoTGlRQ3U0UWVzWjE5RitEclN6cldnRHdPakViOFVjUUY2WXQ3K2hBbHFFZk80ek43ZU5aTHJFdGd6aVl5VUt6blFsNmhMRERsK3hocmRTdmRFQWhEYTVFajhQWVJBU0JsamNjRi9vUUV0ejFYRE80bzJ6WDRrZC9idzBrNGI2SlNXOGw3d3JqZ3JCTEh5SGtlTlNVSWxSdkRtRDVWZ1VHcnE4Smlqa0U5Ym4vNEo4b1ZneUMzaWN2bTk2VmYvdHoyUkN3N0VsN0NDTTljbGdNMzBwNm1FOVZQTFlmM1VRWVYzZUdYWnBZai9zNEpJTzZsM1pXbXBGR2czMnF6cjlYVDM2YW00VHZpaTJndk4wcEtkbU9yVFE3Mk9EdnJ3TE1MZi9USENraHM4UGJ2QlhKSTFJRy9XS0cvamluRjJjZmlvdUVIOFpEVHhNdnlEa1AvZFRlMHRBTEVYcW0rd0sySVlvQUJid1RHOXFicnlpVVcwZmE3cDBzenBJUmszVGFsR01XTlZYaWQxbjdKc3pVOGlPTXZDOWtEenFjNDk5aVZ6SHBXRHkwbHBQWFg2a3k0cW94Q05ybGhqL0svT0pndXgrK05EaUYzbGNnOEJZclV6cGo0Q2V3TU5YRFVITHdFR080NXl3U3NXa0xrNTVmNU1aVlMiLCJtYWMiOiI2NDNkZGZiY2M5YjJjODRhZDgyYjE1MDg5ZTNjMzBlMWZlNGNjMTg4OWFhMmRmNzI5OWY1Y2ZiMTE1MjgwNzU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFLZU16QWMvQUdTVTVKVkJyMVdRSHc9PSIsInZhbHVlIjoiZFEyR3lUR2puT09ObW45U0Q2bzZTVE94RUQ1T3dKam0zbXlFdWliTnRyYURBd2lYOStqa2tzeEhGT3VzZ1U4RG1GaTY4cVVlQll6c3lUVXc4akJVZ2tuZHRxYnpxWnlqczlpUU93ZitLUTVYMWdiU21VanBJcElWREtiN3cwL3BHUDlQam5CK3VVVHBwWXpRTGRpbGNwdTFSUnVXMlAwVFBXUnV2RVZMT2dDNkVBMERaZm8zL1Zabmt6UWhqWExBMG01dENpUzJSQ1RxelZWTHBEUUE2YnBuTlJlTkMvODVHQkJDT0poTm9WaFgyYnZGVnZGTW5wb1JkMHgzOVpKdVFqYXZjYy9tU21oU2dUVmZSemo5NDduQ0RHaFExdDZ5dVk5bTF4dHhKcXQrVkQ2MkxXSFFvZWkwNkFMdmVNRlF5M1hXbjJOV2FGeTRZMGdGUEQ3cDZUb01yRzl2bitpMXlhNlBMRExWTjJxNWRaVEg3VXNFN29lWkNBdG8zaEFVRndJMFdnMjRXWVBmWGFIc2JsWUlKUmxxMlFZbHB1MTBQdXcvMStwcnhEckVibjVXRlVUdHRNTDBnMjN1UFFLWGRrYytSRHRLclA2K3FYMlpjWUk5UjF0QTJFdVltbXFYTVFWdUFCbENDbU0wY0Jsd0NXVVp5eE9XRmFkRmQ2VlEiLCJtYWMiOiJkNmRhZjg1NjQ4YzM4MmM2NzJhMTllNDMxNjdhNDlkZGZiMTNhNzg4ZGJkNzU3ODBmOTcwMjAyNDVjMTc5ZTI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1113721037 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YNJBgXOqtFU4b8z8Vx4XcGRnEfxLj9OGGtpNz79H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113721037\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1308389773 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhLa2RUM0tBdkkrL3V2SnpNRU84SlE9PSIsInZhbHVlIjoidVlJZzJkSjVpUnE1NjZZajFRNHhBeU9LamFXaVBCaGdsY2JjMGF6cUhBUzRKV0tSLy9DNVVhYTM5aHB3V1RsYVdpaHpuaVh1U1pIWGkvOG5PVkliV2lzOVdvdkRBNkErTXQ1ZklzTXZaczNEZzExZlJWdnV4ZHVDdTRtUVA5ZjI4ZXRBYjNvM2s3ZS9yUkNCU2pJMnQxbk1nQU9ZU2k2VWFKNDVPUERCYmdVSlluZnoycjFyTWpLYzcxeGxLZFc5bHA0ODZSWkdHT25rSVdJaTVTVnRLbXpLRXB3Zm02LzkzV1FBNWtWMS8waVNZclhCVCt1T2xEbW9jNVpyOWo5MEY4aWtBTkdjTnBCODUrbGlVTmE5N1U1M2xsMHQwa1pSeGN2MStYblZydzhreE4ycGJFT0t0WncyTjBHeDJnMW9ZNkJtUkg5T0FpVFFZRytTR3lqVGorSkRRdGwySG9KYXJXNE4wZFdpdTFoc1VSclIzU08vZzlYRUw5dHprMm5sMDRDN3Z1VGFsdlNGTm01cWdBSUVxL1dUVlUyR2xBS0JVT3RhZG1XS0laWFQ5bkd5S3RiR2FKWnFCQWIrenJSYmQzT1pBU2p2TmJpVzhCaXhoc3RPQmhqSlI2L3Y2VHJ2VjFFSzlUZVM3UTZNZXh4Z1U3OEVwK0x3dGh5clE1cDQiLCJtYWMiOiI4MzA3MGZiOTA1MzM3M2M0MjExMDhmNDJmMmU2NThhMTI4Nzk5MGM3NWM0YTM0MzcxZmEzYzU1YzU3Yjg1NGNiIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNHT3lXRDlYTUNXdmVoeCtkU2lCR0E9PSIsInZhbHVlIjoiaG1Ya3Q4WnFqWE1SSkFPdFY4L2swSWxKcEtzcG8vRU9DaHBWcHBEOEx0RlFTYnJiUkZDeHFwYUUrczBhWmgyVHYvRGttMkZLZjRmR3ZTRFpPajEvdTFwQWxNV1pGSUxGYURNVU1qTE9jL0lsUTExQVBwMERUcGFySzhsY3BrNG1zTllrR1FZaVlVNzhzMjhDWm9wdkNWWlE0SmxzM3djL054L0JxVUVLNllNS0sxLzQxSWRpWFNvUXRLYjdkMFB6V1VwOXVxU1FuU0diclBvamZuZEhablBoV2FIS2hJNFkvemdkVFhaVEdjb0dlVk9MZkN1VGNqamtPUTVUbDk2VFdLNG9zdFJSeGtSK04yMWNZMEN2dmdVQVRBemNuaTdUZmxuZWlZUXRGOW0wOGpXWUxVWldWNUtvMkptOFlHRzNyNFBBMlNpOWovTlhZWU93N25sRXJwZU9PM3Q3M0RzZy95VVhDZEtObTd2aEx2N3JBMllQdjJFZ1FsbHJZZ1VoNHMxQ2NBSjJWcUFpOURlUENoTGdoRXI5bm1qN2JZNmZDSnovU2Z1akgxc1VEcTZtMHoreG9EMG1sWXRkWm4xL0F6Q1VlbFkwRUxQNVlydFBZUzJCQXNGdVJLVGZpWDRsZk11bGhQSnpuVnpqL012UTFaS1JEL25HaU51bkg0eWsiLCJtYWMiOiI3ZTQyZDUyMmNhODgzMjgyYTg4MDg4MDg4YzM0Njc0YmRlN2QzNTI0MWEwNTYxYzY2MTUyOGQzM2I4NGQwMjljIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhLa2RUM0tBdkkrL3V2SnpNRU84SlE9PSIsInZhbHVlIjoidVlJZzJkSjVpUnE1NjZZajFRNHhBeU9LamFXaVBCaGdsY2JjMGF6cUhBUzRKV0tSLy9DNVVhYTM5aHB3V1RsYVdpaHpuaVh1U1pIWGkvOG5PVkliV2lzOVdvdkRBNkErTXQ1ZklzTXZaczNEZzExZlJWdnV4ZHVDdTRtUVA5ZjI4ZXRBYjNvM2s3ZS9yUkNCU2pJMnQxbk1nQU9ZU2k2VWFKNDVPUERCYmdVSlluZnoycjFyTWpLYzcxeGxLZFc5bHA0ODZSWkdHT25rSVdJaTVTVnRLbXpLRXB3Zm02LzkzV1FBNWtWMS8waVNZclhCVCt1T2xEbW9jNVpyOWo5MEY4aWtBTkdjTnBCODUrbGlVTmE5N1U1M2xsMHQwa1pSeGN2MStYblZydzhreE4ycGJFT0t0WncyTjBHeDJnMW9ZNkJtUkg5T0FpVFFZRytTR3lqVGorSkRRdGwySG9KYXJXNE4wZFdpdTFoc1VSclIzU08vZzlYRUw5dHprMm5sMDRDN3Z1VGFsdlNGTm01cWdBSUVxL1dUVlUyR2xBS0JVT3RhZG1XS0laWFQ5bkd5S3RiR2FKWnFCQWIrenJSYmQzT1pBU2p2TmJpVzhCaXhoc3RPQmhqSlI2L3Y2VHJ2VjFFSzlUZVM3UTZNZXh4Z1U3OEVwK0x3dGh5clE1cDQiLCJtYWMiOiI4MzA3MGZiOTA1MzM3M2M0MjExMDhmNDJmMmU2NThhMTI4Nzk5MGM3NWM0YTM0MzcxZmEzYzU1YzU3Yjg1NGNiIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNHT3lXRDlYTUNXdmVoeCtkU2lCR0E9PSIsInZhbHVlIjoiaG1Ya3Q4WnFqWE1SSkFPdFY4L2swSWxKcEtzcG8vRU9DaHBWcHBEOEx0RlFTYnJiUkZDeHFwYUUrczBhWmgyVHYvRGttMkZLZjRmR3ZTRFpPajEvdTFwQWxNV1pGSUxGYURNVU1qTE9jL0lsUTExQVBwMERUcGFySzhsY3BrNG1zTllrR1FZaVlVNzhzMjhDWm9wdkNWWlE0SmxzM3djL054L0JxVUVLNllNS0sxLzQxSWRpWFNvUXRLYjdkMFB6V1VwOXVxU1FuU0diclBvamZuZEhablBoV2FIS2hJNFkvemdkVFhaVEdjb0dlVk9MZkN1VGNqamtPUTVUbDk2VFdLNG9zdFJSeGtSK04yMWNZMEN2dmdVQVRBemNuaTdUZmxuZWlZUXRGOW0wOGpXWUxVWldWNUtvMkptOFlHRzNyNFBBMlNpOWovTlhZWU93N25sRXJwZU9PM3Q3M0RzZy95VVhDZEtObTd2aEx2N3JBMllQdjJFZ1FsbHJZZ1VoNHMxQ2NBSjJWcUFpOURlUENoTGdoRXI5bm1qN2JZNmZDSnovU2Z1akgxc1VEcTZtMHoreG9EMG1sWXRkWm4xL0F6Q1VlbFkwRUxQNVlydFBZUzJCQXNGdVJLVGZpWDRsZk11bGhQSnpuVnpqL012UTFaS1JEL25HaU51bkg0eWsiLCJtYWMiOiI3ZTQyZDUyMmNhODgzMjgyYTg4MDg4MDg4YzM0Njc0YmRlN2QzNTI0MWEwNTYxYzY2MTUyOGQzM2I4NGQwMjljIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308389773\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-937406879 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937406879\", {\"maxDepth\":0})</script>\n"}}