# دليل نشر ميزة مرتجع المبيعات في أوامر الاستلام

## 🎯 الهدف
إضافة خيار رابع "مرتجع مبيعات" في نموذج أوامر الاستلام، والذي يسمح بإرجاع المنتجات إلى المستودع من فواتير الشفت الحالي.

## ✨ المميزات الجديدة

### 📋 **الوظائف المضافة:**

1. **خيار مرتجع المبيعات:**
   - إضافة "مرتجع مبيعات" كنوع رابع في قائمة أنواع الأوامر
   - عرض قائمة منسدلة بفواتير الشفت الحالي للمستودع المحدد
   - تحميل منتجات الفاتورة المختارة تلقائياً

2. **حقول مرتجع المبيعات:**
   - رقم الفاتورة (قائمة منسدلة من فواتير الشفت الحالي)
   - إجمالي المبلغ المرتجع (يتم حسابه تلقائياً)
   - تاريخ الإرجاع (افتراضي: اليوم)
   - سبب الإرجاع (نص حر)

3. **معالجة المخزون:**
   - إضافة المنتجات المرتجعة إلى مخزون المستودع
   - تتبع العملية في سجل أوامر الاستلام
   - ربط العملية بالفاتورة الأصلية

## 📁 **الملفات المحدثة:**

### **1. واجهة المستخدم:**
```
📁 resources/views/receipt_order/create.blade.php
```

**التحديثات:**
- إضافة خيار "مرتجع مبيعات" في قائمة أنواع الأوامر
- إضافة قسم حقول مرتجع المبيعات
- تحديث JavaScript لمعالجة النوع الجديد
- إضافة دوال تحميل فواتير الشفت ومنتجات الفاتورة
- تحديث التحقق من صحة النموذج

### **2. الكونترولر:**
```
📁 app/Http/Controllers/ReceiptOrderController.php
```

**الدوال الجديدة:**
- `getCurrentShiftInvoices()` - جلب فواتير الشفت الحالي
- `getInvoiceProducts()` - جلب منتجات فاتورة محددة
- `processReturnOrder()` - معالجة أمر مرتجع المبيعات

**التحديثات:**
- تحديث التحقق من صحة البيانات لتشمل مرتجع المبيعات
- تحديث منطق المعالجة في دالة `store()`

### **3. النموذج:**
```
📁 app/Models/ReceiptOrder.php
```

**التحديثات:**
- إضافة حقل `pos_invoice_id` للـ fillable
- إضافة علاقة `posInvoice()` مع نموذج Pos
- تحديث دوال الحالة والألوان لتشمل مرتجع المبيعات

### **4. الروتات:**
```
📁 routes/web.php
```

**الروتات الجديدة:**
- `receipt.order.shift.invoices` - جلب فواتير الشفت الحالي
- `receipt.order.invoice.products` - جلب منتجات فاتورة محددة

### **5. قاعدة البيانات:**
```
📁 database/migrations/2025_01_20_000000_add_pos_invoice_id_to_receipt_orders_table.php
```

**التحديث:**
- إضافة حقل `pos_invoice_id` مع foreign key إلى جدول pos

## 🔄 **سير العمل الجديد:**

### **للمستخدم:**
1. **اختيار نوع الأمر:** "مرتجع مبيعات"
2. **اختيار المستودع:** يتم تحميل فواتير الشفت الحالي تلقائياً
3. **اختيار الفاتورة:** من القائمة المنسدلة لفواتير الشفت
4. **تحميل المنتجات:** يتم تحميل منتجات الفاتورة تلقائياً
5. **تعديل الكميات:** يمكن تعديل كميات الإرجاع (بحد أقصى الكمية الأصلية)
6. **إدخال البيانات:** تاريخ الإرجاع وسبب الإرجاع
7. **الحفظ:** إنشاء أمر الاستلام وتحديث المخزون

### **للنظام:**
1. **التحقق من الشفت:** التأكد من وجود شفت مفتوح للمستودع
2. **جلب الفواتير:** عرض فواتير الشفت الحالي فقط
3. **تحميل المنتجات:** جلب منتجات الفاتورة المختارة
4. **معالجة الإرجاع:** إضافة المنتجات للمخزون
5. **التسجيل:** حفظ العملية في سجل أوامر الاستلام

## 🚀 **خطوات النشر:**

### **الخطوة 1: رفع الملفات المحدثة**
```bash
# رفع ملفات الواجهة
scp resources/views/receipt_order/create.blade.php user@server:/path/to/project/resources/views/receipt_order/

# رفع الكونترولر
scp app/Http/Controllers/ReceiptOrderController.php user@server:/path/to/project/app/Http/Controllers/

# رفع النموذج
scp app/Models/ReceiptOrder.php user@server:/path/to/project/app/Models/

# رفع ملف الروتات
scp routes/web.php user@server:/path/to/project/

# رفع Migration
scp database/migrations/2025_01_20_000000_add_pos_invoice_id_to_receipt_orders_table.php user@server:/path/to/project/database/migrations/
```

### **الخطوة 2: تشغيل Migration**
```bash
ssh user@server "cd /path/to/project && php artisan migrate"
```

### **الخطوة 3: مسح الكاش**
```bash
ssh user@server "cd /path/to/project && php artisan cache:clear"
ssh user@server "cd /path/to/project && php artisan view:clear"
ssh user@server "cd /path/to/project && php artisan route:clear"
```

## 🧪 **للاختبار:**

### **1. اختبار الواجهة:**
```
✅ تسجيل الدخول بحساب Cashier
✅ الذهاب إلى إنشاء أمر استلام جديد
✅ التحقق من ظهور خيار "مرتجع مبيعات"
✅ اختيار مرتجع المبيعات والتحقق من ظهور الحقول المناسبة
```

### **2. اختبار تحميل الفواتير:**
```
✅ اختيار مستودع له شفت مفتوح
✅ التحقق من تحميل فواتير الشفت الحالي
✅ اختيار فاتورة والتحقق من تحميل منتجاتها
✅ التحقق من عرض إجمالي المبلغ المرتجع
```

### **3. اختبار المعالجة:**
```
✅ تعديل كميات الإرجاع
✅ إدخال تاريخ وسبب الإرجاع
✅ حفظ الأمر والتحقق من نجاح العملية
✅ التحقق من تحديث مخزون المستودع
✅ التحقق من ظهور الأمر في قائمة أوامر الاستلام
```

### **4. اختبار الحالات الخاصة:**
```
✅ اختبار مستودع بدون شفت مفتوح
✅ اختبار شفت بدون فواتير
✅ اختبار فاتورة بدون منتجات
✅ اختبار تجاوز الكمية المسموحة
```

## 📊 **البيانات المحفوظة:**

### **في جدول receipt_orders:**
- `order_type`: "مرتجع مبيعات"
- `pos_invoice_id`: معرف فاتورة POS
- `warehouse_id`: المستودع المستهدف
- `invoice_date`: تاريخ الإرجاع
- `notes`: سبب الإرجاع
- `total_amount`: إجمالي المبلغ المرتجع
- `total_products`: عدد المنتجات المرتجعة

### **في جدول receipt_order_products:**
- `product_id`: معرف المنتج
- `quantity`: كمية الإرجاع
- `unit_cost`: سعر الوحدة من الفاتورة الأصلية
- `is_return`: true (للتمييز كمرتجع)
- `notes`: "مرتجع من فاتورة #[رقم_الفاتورة]"

## 🔐 **الأمان والصلاحيات:**

- ✅ **التحقق من الصلاحيات:** نفس صلاحيات أوامر الاستلام الحالية
- ✅ **التحقق من الشفت:** فقط فواتير الشفت الحالي المفتوح
- ✅ **التحقق من المستودع:** فقط فواتير المستودع المحدد
- ✅ **التحقق من الكميات:** عدم تجاوز الكمية الأصلية
- ✅ **التحقق من الفاتورة:** التأكد من وجود الفاتورة وصحتها

## 📈 **المميزات:**

- 🎯 **سهولة الاستخدام:** واجهة بديهية لإرجاع المنتجات
- 📊 **تتبع دقيق:** ربط المرتجعات بالفواتير الأصلية
- 🔄 **تحديث تلقائي:** تحديث المخزون تلقائياً
- 📋 **تسجيل شامل:** حفظ جميع تفاصيل عملية الإرجاع
- ⚡ **أداء محسن:** تحميل فواتير الشفت الحالي فقط
- 🛡️ **أمان محكم:** التحقق من جميع البيانات والصلاحيات

## 🚨 **اختبار عاجل:**

بعد النشر، قم بهذا الاختبار فوراً:

1. **تسجيل الدخول** بحساب Cashier
2. **إنشاء أمر استلام جديد**
3. **اختيار "مرتجع مبيعات"** من قائمة أنواع الأوامر
4. **اختيار مستودع** له شفت مفتوح
5. **التحقق من تحميل الفواتير** في القائمة المنسدلة
6. **اختيار فاتورة** والتحقق من تحميل منتجاتها
7. **حفظ الأمر** والتحقق من نجاح العملية

إذا نجح هذا الاختبار، فالميزة تعمل بشكل صحيح!

## 📞 **الدعم:**

إذا واجهت أي مشاكل:
1. تحقق من وجود شفت مفتوح للمستودع
2. تأكد من وجود فواتير في الشفت الحالي
3. تحقق من سجلات الأخطاء في Laravel
4. تأكد من تشغيل Migration بنجاح
5. تحقق من صحة الروتات الجديدة
