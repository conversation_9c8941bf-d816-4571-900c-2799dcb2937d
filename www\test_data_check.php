<?php
// Quick test to check if data exists in the database

require_once 'vendor/autoload.php';

// Database connection (adjust as needed)
$host = 'localhost';
$dbname = 'your_database_name';
$username = 'your_username';
$password = 'your_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Advanced Cash Management Data Check ===\n\n";
    
    // Check shifts
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM shifts");
    $shiftsCount = $stmt->fetch()['count'];
    echo "Shifts: $shiftsCount records\n";
    
    // Check financial records
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM financial_records");
    $financialCount = $stmt->fetch()['count'];
    echo "Financial Records: $financialCount records\n";
    
    // Check receipt vouchers
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM voucher_receipts");
    $receiptCount = $stmt->fetch()['count'];
    echo "Receipt Vouchers: $receiptCount records\n";
    
    // Check payment vouchers
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM voucher_payments");
    $paymentCount = $stmt->fetch()['count'];
    echo "Payment Vouchers: $paymentCount records\n";
    
    // Check POS
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pos");
    $posCount = $stmt->fetch()['count'];
    echo "POS Transactions: $posCount records\n";
    
    // Check POS payments
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pos_payments");
    $posPaymentCount = $stmt->fetch()['count'];
    echo "POS Payments: $posPaymentCount records\n";
    
    echo "\n=== Sample Data ===\n";
    
    // Show sample shifts
    $stmt = $pdo->query("SELECT id, shift_opening_balance, is_closed, opened_at FROM shifts LIMIT 3");
    echo "\nShifts:\n";
    while ($row = $stmt->fetch()) {
        echo "ID: {$row['id']}, Balance: {$row['shift_opening_balance']}, Closed: {$row['is_closed']}, Opened: {$row['opened_at']}\n";
    }
    
    // Show sample receipt vouchers
    $stmt = $pdo->query("SELECT custome_id, payment_amount, status, date FROM voucher_receipts LIMIT 3");
    echo "\nReceipt Vouchers:\n";
    while ($row = $stmt->fetch()) {
        echo "ID: {$row['custome_id']}, Amount: {$row['payment_amount']}, Status: {$row['status']}, Date: {$row['date']}\n";
    }
    
    // Show sample payment vouchers
    $stmt = $pdo->query("SELECT custome_id, payment_amount, status, date FROM voucher_payments LIMIT 3");
    echo "\nPayment Vouchers:\n";
    while ($row = $stmt->fetch()) {
        echo "ID: {$row['custome_id']}, Amount: {$row['payment_amount']}, Status: {$row['status']}, Date: {$row['date']}\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    echo "Please update the database connection details in this file.\n";
}
?>
