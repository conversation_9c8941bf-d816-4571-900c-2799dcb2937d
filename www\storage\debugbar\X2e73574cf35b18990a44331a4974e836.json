{"__meta": {"id": "X2e73574cf35b18990a44331a4974e836", "datetime": "2025-06-07 04:32:57", "utime": **********.309222, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.815073, "end": **********.309251, "duration": 1.***************, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": **********.815073, "relative_start": 0, "end": **********.072881, "relative_end": **********.072881, "duration": 1.***************, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.072909, "relative_start": 1.***************, "end": **********.309255, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03681, "accumulated_duration_str": "36.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.17367, "duration": 0.03386, "duration_str": "33.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.986}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.245319, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.986, "width_percent": 3.043}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.281874, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 95.029, "width_percent": 4.971}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270763756%7C11%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlovMVBpMS8rYjZXTHZ2N08rellnZ2c9PSIsInZhbHVlIjoibE4vTElkVXhXbVlSc0NtK1BxWWNGb05zak1MRVFUNFl2MGZ1R3EzR2dHekQwM2lvNmxPdGQzSVpwWVEwQ0FDVmh2cXpucTNOWWdqY1QvcmE1ZTV2NWV1OHBoMUZxeHFPbjRtR0NZWUd4R3BaN2FKNVNPUHo0QU9UbzBrRm9lRnNzbXVCYXhPNnVpbDZWU1B0WVBZTlhaZWVNeFk1ZUJTQ05MUVArYXRhbERIQzhoZ2VnZmJGQlZ1dnRYWkd5LzBNVVZnb1g3aERqYnE5RVhIYnhyM1F0V21vRWxWSE56YlFCU29mR2dmMVdXVzMxVEV4L1p3VEt5bG1QNTR2NURpN0tzN3NlVTJ3cVhpN0o2bldZWVUwWUc4VjBPcXBKZExtQi9Hbi91d3RFdmlaTTNBUjJVL1dKVVJrVEtKWkFiditjbmlwMTROUEMyaVlyZER4c1I4UXpaalNScEF4bWZDYlhid1dwY3R1SEhjRm5vb0VqbGRFR294dXFyWnpmdTB4RlFwdmFXT01lcWM4dm1tZ29DZkpqRzJ1Qk1tM2JMR1pDcXZVQml2ZWlQbStqcVVyb1dMNEcxck1rUjYwc2FRREllQkVXeHZzbEJkMW5McFpCWEttdkRvNExrTCtPYzNCcDN3ak9aYkdpSHpBRFlUSm5VQWpadWZPK2ZXK0x2Z04iLCJtYWMiOiJmOWRiYjAzYzUyZDhkYjE4YzcyZGEyNTMwOWFhNzgyNTNlMmE1MGUwYjc1YTEwNzkwMGU0NWRhOWMzMjM3NmY2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ild6eGxVMHlwYUcyTXIwRms0STl0OEE9PSIsInZhbHVlIjoiSVBjUDdVQ2d6eGw3aDBMWHc0Nkkwa1ZINUliRFhTQ1BQV2YvWmE1emsxOFJ1emFZWHh4QW91MGtSd282TmkwZ09sbW5ua0JBOWtTTUc1T29FMDk5Q2F1V0JPVWVEbXFnZ0hhU012cFpiVWpTb3AwQkRLRVFFOUUrd29ZVkZZYVJ3emxhT2tTQUxRTGtLVVRYRmFRMFR4bGhVWUZFMzMyMnFqQTVGa09wMCtQQjNXNWtpby9iRjdxNEN2cng2ZFJhMjRGQWZJaUV4ZjF3UjQ2L0NUYTJZNHRxRUpsUzZneER0M3NhOWw4ZFdPM3JTNmVhMW9uWG0zdUpwdGVUaThQRU9iKzYvK1RjUzZSVVpLY3pzb1RXQnFZNnBVZnNTNHlvVm04OVZxT2dzdkhCdEZFa2s1bnhDYTZnaDBGc3FnZ2I1bEUzTlN2S05LbEFtelNEMWkvYVRFcFR6eG9ObFRuZ2twUGlFUlQzaUE5amdFQWgyanFSdFg2UUVuWUNLVkpHN2xnWG15VjBwdDFXUmxRTmJBVmpxZ0dyL3VGQlpVT3dEM0lSY0txMzNoUGdDV25HYVpkaUpBdTNrQmdXajVBbXEvNFU3V1FsSTVEbVZKeExENVJQYlFkYVhqckZXeitJbmdub3dSOVVPUDBDeUY4K2xMV1EyRHNZbEdnbnI1WmIiLCJtYWMiOiI2Y2JiNzA0ZDYwNzg4ZTVmNDhkYWIzODEzOTUyZmU2NjY4MDAzYzBhZmQwOWNkZmM2YzI4NDE3Mzc0OGExNDk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1988606290 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4BV3LLZZXyEMe10MCK9nf8wqbVP7f9AxZbbeIoE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988606290\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1250225835 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:32:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlY4ZzFJdnJ1SGE2Tm9pcHYrUkNFUVE9PSIsInZhbHVlIjoicDIxLzFlZFErd0R6SG90T24rTldQN3dIUVYrYmc2QWlaekNWWVZhTTgrcTNxZVg1d3VGWFB5SXJZQXFhV3dIanhiOHZtcEovSjRkUzk2c25xTEtoTVQrb05vZTl6dFBubUtQd1NCNjB0L1ZJa01kOGdKd1RWckxqRlBiQU5xcU8vMU04WlNjcGJXZUJzNlZaaFdINlE1dy9mS2NrY3Z4aGUwMk04QzhRLzlsNFg4Uko4TUMreVpNN3NEZk11bDZtb1J0V2UyVFlnRmowUm5SMnU0NTFqWnJqcWhLRlcrRXMxa2JEZUx0R2plNTdRMmlZbDdyUmtnRjhxeURaWW9ZVVJJUlEzVllEdzRtVWJUandlWWFqVjQ3bkxzYXdGcEsvMlRGZmRIWjFpK3hTUmNJUmE0dlUvb3Vzd3VmRXQrYmNYeUR6VDMvb3FIMUNkRHExSGFzOEtUUGcvZjIxT2dUd3lqZmJSZjdyT2dFWnNMcHJuZXRLSTFLUmZmSEMrWXBqMWRjWHBIS0M1cFg3aFQwUnMvWk9NTTV3YzZBRDg0eUlRS0NnWXpUUEcvTGtpUHZjRGxMMSt4UmhQb2VZc051VUpvOXB4TTQwUFBpdmJrS1ZLV2l6Z0RrM2luMXBqbG5XSEwwcVRvcWNOU0w1emkwYi9rQ1V3NVFNdHVjeGhVek8iLCJtYWMiOiIwNjFlYjQxNmQ4MzMxMzQ3ZDQyYTdjZTMwNGY3NTFiNjE3NmE3NTFiN2U1YWQxNmMxZmJhYTUzMTBjZGJmYTY3IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im85Y3Z3Nkc3bGhhWHdGSGlram96Q0E9PSIsInZhbHVlIjoiRzNFeVltVGVqR0h4aWVWT2JkTnBiR3c3NWRteDZuOWF1TWtTb1pJNlA4Y1JOYnV6cSt4dFpvcmsvV2dXU0ptb0ZnbVBKaE52enE1eW43eDZMWk0xYVB5OWlnUTZKREQrdzQrcEk3cUpuV2tNd2hrQjFnYVdOZWEweDgzdnc3Z3NEYnQ0ZWRTcEVYS0YwZ3dySCtodmMrbXZWSTM2Y2VDM0FyaVlVYUNpSy94YWwrMVVtaU5CZCtLd1VneENVRXl2T3NmQVp4cDhnZ3dsRVYweWJmOEo2V3ZJSE9ubDJibVpyK1Q1T0xyNEZZRTlaTE1EbVQyNitTekM2MmtOZk00VUZkUHRGVFBicGpZSGZOQ2tJdmxicjZ3MVNYa0FxSlJ6SXlwaGQ1cEkyU1ZFMkVpUkN6OWtaZ3RXeHZvQUV1cEkvQWxldWhmUk9FdXpTVTRIMmxvaEprbEhDMitiMVI0b3BUb3RNSVhjQ1J5WlRLMXVVWnRXSHFkYUp1VVJGNm5ibnlEeFRkTXQ3QnpaN05FaDVLOUxsNGpXcUdvUWRIOXpHU0t1aW5QaHJOTUVjQjJjdUNjaE5tVzg0S2RSbDJQR0kxMjZwcG1zL1FiUTRqRGZadTlucHlPN0wxQ2phaldoTEdPWlIvM280TGpyMWtzNGZqbXA0aEt3R3ptTkN1VTIiLCJtYWMiOiI1OGI1ODBhZGMzNWE3Y2NiZGE2NWJhMjU3ZWJhM2FmZmJlYWExYTFlNmRkNWFiNmE2NGIzM2EzZTQzN2JmNGZhIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlY4ZzFJdnJ1SGE2Tm9pcHYrUkNFUVE9PSIsInZhbHVlIjoicDIxLzFlZFErd0R6SG90T24rTldQN3dIUVYrYmc2QWlaekNWWVZhTTgrcTNxZVg1d3VGWFB5SXJZQXFhV3dIanhiOHZtcEovSjRkUzk2c25xTEtoTVQrb05vZTl6dFBubUtQd1NCNjB0L1ZJa01kOGdKd1RWckxqRlBiQU5xcU8vMU04WlNjcGJXZUJzNlZaaFdINlE1dy9mS2NrY3Z4aGUwMk04QzhRLzlsNFg4Uko4TUMreVpNN3NEZk11bDZtb1J0V2UyVFlnRmowUm5SMnU0NTFqWnJqcWhLRlcrRXMxa2JEZUx0R2plNTdRMmlZbDdyUmtnRjhxeURaWW9ZVVJJUlEzVllEdzRtVWJUandlWWFqVjQ3bkxzYXdGcEsvMlRGZmRIWjFpK3hTUmNJUmE0dlUvb3Vzd3VmRXQrYmNYeUR6VDMvb3FIMUNkRHExSGFzOEtUUGcvZjIxT2dUd3lqZmJSZjdyT2dFWnNMcHJuZXRLSTFLUmZmSEMrWXBqMWRjWHBIS0M1cFg3aFQwUnMvWk9NTTV3YzZBRDg0eUlRS0NnWXpUUEcvTGtpUHZjRGxMMSt4UmhQb2VZc051VUpvOXB4TTQwUFBpdmJrS1ZLV2l6Z0RrM2luMXBqbG5XSEwwcVRvcWNOU0w1emkwYi9rQ1V3NVFNdHVjeGhVek8iLCJtYWMiOiIwNjFlYjQxNmQ4MzMxMzQ3ZDQyYTdjZTMwNGY3NTFiNjE3NmE3NTFiN2U1YWQxNmMxZmJhYTUzMTBjZGJmYTY3IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im85Y3Z3Nkc3bGhhWHdGSGlram96Q0E9PSIsInZhbHVlIjoiRzNFeVltVGVqR0h4aWVWT2JkTnBiR3c3NWRteDZuOWF1TWtTb1pJNlA4Y1JOYnV6cSt4dFpvcmsvV2dXU0ptb0ZnbVBKaE52enE1eW43eDZMWk0xYVB5OWlnUTZKREQrdzQrcEk3cUpuV2tNd2hrQjFnYVdOZWEweDgzdnc3Z3NEYnQ0ZWRTcEVYS0YwZ3dySCtodmMrbXZWSTM2Y2VDM0FyaVlVYUNpSy94YWwrMVVtaU5CZCtLd1VneENVRXl2T3NmQVp4cDhnZ3dsRVYweWJmOEo2V3ZJSE9ubDJibVpyK1Q1T0xyNEZZRTlaTE1EbVQyNitTekM2MmtOZk00VUZkUHRGVFBicGpZSGZOQ2tJdmxicjZ3MVNYa0FxSlJ6SXlwaGQ1cEkyU1ZFMkVpUkN6OWtaZ3RXeHZvQUV1cEkvQWxldWhmUk9FdXpTVTRIMmxvaEprbEhDMitiMVI0b3BUb3RNSVhjQ1J5WlRLMXVVWnRXSHFkYUp1VVJGNm5ibnlEeFRkTXQ3QnpaN05FaDVLOUxsNGpXcUdvUWRIOXpHU0t1aW5QaHJOTUVjQjJjdUNjaE5tVzg0S2RSbDJQR0kxMjZwcG1zL1FiUTRqRGZadTlucHlPN0wxQ2phaldoTEdPWlIvM280TGpyMWtzNGZqbXA0aEt3R3ptTkN1VTIiLCJtYWMiOiI1OGI1ODBhZGMzNWE3Y2NiZGE2NWJhMjU3ZWJhM2FmZmJlYWExYTFlNmRkNWFiNmE2NGIzM2EzZTQzN2JmNGZhIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250225835\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1463629093 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463629093\", {\"maxDepth\":0})</script>\n"}}