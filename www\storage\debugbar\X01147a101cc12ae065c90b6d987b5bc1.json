{"__meta": {"id": "X01147a101cc12ae065c90b6d987b5bc1", "datetime": "2025-06-06 20:37:45", "utime": **********.383288, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242263.721126, "end": **********.383325, "duration": 1.6621990203857422, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1749242263.721126, "relative_start": 0, "end": **********.146286, "relative_end": **********.146286, "duration": 1.4251599311828613, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.146311, "relative_start": 1.425184965133667, "end": **********.383329, "relative_end": 3.814697265625e-06, "duration": 0.23701786994934082, "duration_str": "237ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.020690000000000004, "accumulated_duration_str": "20.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2535062, "duration": 0.01767, "duration_str": "17.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.404}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3113348, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.404, "width_percent": 5.268}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.326354, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 90.672, "width_percent": 4.06}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.350576, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.732, "width_percent": 5.268}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1466044622 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1466044622\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-721942943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-721942943\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-350451437 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-350451437\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-102435339 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/warehouse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242256718%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imp2QTQ4bThRM0FSenI5K05PMDZWSWc9PSIsInZhbHVlIjoiRlFVSEplcklWTmpFTW9ITVVqa2owMHQrdU9OTkZTTDVEQ2p0VWpMTlEyZGlJcEIxMDRTNDdmQzBIN2ZuL25YR2YzZ2hlK21HVkNDRmxqbHROZVVVTWRVcXdSV2VOaXB0OGlWaXpZbGExYnNOeXl1T04vVVRZeUsvVkhoVW9ibHVEQXlWV3psdlJFRjNmSk9NTWluemNPMTdtRlFSTlNYUjlWMDh6Q3p3ZkVRazFpQTdIMVc4WUszQlVlZ3d4ODRkYm9MRk5kZVh4V2ZIZ2dMOUtnM25hMFFhZm53dWhreDdsK3NsVmlDek1qV3VoN24vVzJmZGxpWnhQbjRjWGN1MGR5TWJvY2RYYUt0d0h6d3BtYWxlV1ZZanFoeDFvL3Q5R1hKenRpMm4vT2M2SXNBTG52T0owWHRnMmRiSk1uS0V3Q2w3WkJ6aVFGckpuTFc4Ty9PRzNpR1VvaG5sbFFCemJIWkpBbG5HV2R0cDNSQjBNdGRaTW5YOHh2QXF0UkdZc0RrVEJ6Zkc1b2RTbUFxcGhzRzdCaWZ3SmlrbCt3eFR0d1VzRjQ2UCtyQloxdDZnUlpPZWtaM21xRGNheXVwZGpNV0w5TWhaZVhkS0RXZGdLMFhzeXFPTG1ZMDZsUUpUTGlWWlJBcjJiampDMElxSGRkLzFJd0h2MDMvSHI3eWMiLCJtYWMiOiJiMWU2ZGFkNjY5ODM2NWExYTkwMWZhNmI0YTNiZTNhZmVmNmU0NzEyYTdjOTA4MzFmNGM1OTNjZTRkNzhiYmVkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImEveTM5bk81bkEvSmZqNzVtaVh3d3c9PSIsInZhbHVlIjoiRXUrVlMwSENBY29uSk1GeU16UkNzaXZMWHpkWlAxV1E2MTQ0c2tZVEh1aWdnUTQ5UGhRUTVWSXhtRi9ZWUpuUFFCMHRVWDJsb25aSkhHUjByOWtOQUNLc2Z0YnFraENYaENoTjBZVDZDY3BDYTJDa3U3WG1LQk92UlZ4WHZIbFJsajNyOGlSRXlzbUtJSHJsTnM1UERxMnpoWTlqTkxGN3BjeXVEa2UrS0dvbzZDSlczSFhSTUJZRkR4WVl6TWZkYmxtUEFhWW84ZllrRk9nRERPMERqcWFhSTVuNkFjYUtCdFd4bkNOZHlGelIvdzNFWGpoZHZEbHNaRWtQWmV2bXFrK0RsSFNzVkM3WittTFQzMVZSQTl3cE5jbndPR2M1KzkydmV5SVVXQVA2dDAyZlFiZnFCbS9uVW12blRmUWxleWQ4bGRIOHdpTzFNUUlYeDBValVXNDM5bDVFay9FN3ZHcGJIVzh1OTAwa0IrSWtsM0Fhek5LVE14OVRlUHBqMEpxZnRxTDV0UWJVbTljVVhyZHVKb0dtcWZZeTB1cnFub25UK2swUHFFQ2hUbG40VitCSC9BTThnREZ3NWM4T29TRDBWNXhqTkljY0RSYm4zNjM2RDQ0eWF5cmRoRUdlS3cxSTRReDQxT3ZuSVhTbjlRU1dnc1pFOVJ4aEpnQnYiLCJtYWMiOiIxMGNlMmNjMjQ4MmQ4NDJlZTcwMDA0ZDllNjJhOTJiZTkzZTJhZmYwMDJmYWE2NDVhZTA1MTBmZTA3Nzc3MTVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102435339\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1930863012 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930863012\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1503028371 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:37:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh3eWZNTlFmd1JDd1djMWJ4MHY4bFE9PSIsInZhbHVlIjoiM2hPMlFMTnc0SkhDTzRYeFdZbFlnd1FPSlg4TEVzNXhvSklrdmVaano0cW9QK1QwUHMyYUt4WFd6VUN5ZXIya3YxV04zZ1I0V2E5V3daMEtjdUtwK1E3dXN4ZDVlR1RUUGp5YU5pZ1VKY3JET01FVlpEelJUejB3YXhNRWZVSkMxQ29pK3h0OURUOG0wMnF6Q1VzdlN6VjQ1ZTUwdnYwOXU3TFM4UDM3cU9UV3RXT3ZFVnJVeTVTdVUrb3ZyVm9JVmtpVVZaNHBVZWhSNVV1bFpOK1RGUk1CNC8zREdvT1JQR2xTS3FacXBhbE9CTmlzSTRlODZFL21jNnpkdWdRK1B1WDJFY0xuRVNBMmY0bEs5V0VSbFZ2dGFFOG1Vait5RlJRZy9idWg0UTZmL0VvYnFnMHduNVVJdHk5RHJ6eDhiSHUxSWh0bjBsY2sxalVXVStxQUZIWTZjTUZpMXRpbzB6dEx5WUdLRGowdGdvbzRqKzZYditCUEVhY0VLQXQ1aGpidXRZUURCUXBkTm9DZFBUdlV6eHlBMDBRdXQ3VWp4K3E3MXR1MnJOaTYzaHoweFF6bDY1bTMrWDRHZENwNEkxTk9SYnZZVU1FUEdscEhKTzNSeVJjRkN3L1d5Zzg4SGZWUFJqN3VUVDQ1SEQvUTJUSURTUVZSc29hanJhamMiLCJtYWMiOiI0Y2MyNGNmYzRmN2ZiNTk5ZjI0OTA1MzQ0YzQyNjkxYzViM2RiMjQzMzU0ZmE4Y2YzMzQ4ZGZmNjAyNGRjNDg4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklDSE1nbXBZZ2F2L0tPK3VCWVp1UEE9PSIsInZhbHVlIjoiYll2TVZaemJ5WWNNcHZvNGpzaDJXRFJXaGxXbWxQVTFtNmVJakoySS9GdTJCUElkT1VxV01tb01KMDArWEhTQ1loOFlYT0tNQkpmKzRSVHJCODc3dGJIci85S2VQV2xGaEN2cDhrUFd6SVZmRDJtUG83d1BocE5KdGtUTWVGd201VGU5b2lZWmliSzFDQXdVMGpyUys2OWFGMm93M3F5S2gxSUF3VnpYY1Q3TmRtVmtpQ0I3ZmIzK3VxeUY1bGNEeHp0WVVyN255a0VncGtVcE52Mmc3bUMvS2t6ZTRsWHljNVBtTm5Xc1J5enZyTksxdmpNZ3pvcjNpa3JLUmp6WEdxdUpaRGZsVWtraWRCMzlOZ2VxbmtDODdlcHJRMW03cHNOaWNsc2djMCsyenZzT2RLRmZJckJnYytmbVdwN0R5azh5bWNMZk1RRFEzU2lwWXdvSStvUlU4VzZxRTU2MWdEVEF2ZS9jVjduSVZJSEQwVU9ZM1BDNXJtZHRQc0FpNEVUREllc1J3YmpuVnAvak91QTJEQmhEckpDbGVsS0FISktLTWhkRnZNQW4rNTVEKzNZMGs0d2hlQ3JQUy9uc1U1WGlOVTJUS2EzLzhjbGdQU0hQdXJZKzNKdGlrTjJIdDZPTXJvZXNIRldmVDhrcjNSVDBWS1VlcC9aOGs4V1IiLCJtYWMiOiI3MjU5YTNhNDAxNWFkNmZlM2JiZGE0MjBmYWE3ZjU0ZWEwNmI3OWI0YjdkNzg3NjhjNGQ2OTVkNzU5MDlhYjA1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh3eWZNTlFmd1JDd1djMWJ4MHY4bFE9PSIsInZhbHVlIjoiM2hPMlFMTnc0SkhDTzRYeFdZbFlnd1FPSlg4TEVzNXhvSklrdmVaano0cW9QK1QwUHMyYUt4WFd6VUN5ZXIya3YxV04zZ1I0V2E5V3daMEtjdUtwK1E3dXN4ZDVlR1RUUGp5YU5pZ1VKY3JET01FVlpEelJUejB3YXhNRWZVSkMxQ29pK3h0OURUOG0wMnF6Q1VzdlN6VjQ1ZTUwdnYwOXU3TFM4UDM3cU9UV3RXT3ZFVnJVeTVTdVUrb3ZyVm9JVmtpVVZaNHBVZWhSNVV1bFpOK1RGUk1CNC8zREdvT1JQR2xTS3FacXBhbE9CTmlzSTRlODZFL21jNnpkdWdRK1B1WDJFY0xuRVNBMmY0bEs5V0VSbFZ2dGFFOG1Vait5RlJRZy9idWg0UTZmL0VvYnFnMHduNVVJdHk5RHJ6eDhiSHUxSWh0bjBsY2sxalVXVStxQUZIWTZjTUZpMXRpbzB6dEx5WUdLRGowdGdvbzRqKzZYditCUEVhY0VLQXQ1aGpidXRZUURCUXBkTm9DZFBUdlV6eHlBMDBRdXQ3VWp4K3E3MXR1MnJOaTYzaHoweFF6bDY1bTMrWDRHZENwNEkxTk9SYnZZVU1FUEdscEhKTzNSeVJjRkN3L1d5Zzg4SGZWUFJqN3VUVDQ1SEQvUTJUSURTUVZSc29hanJhamMiLCJtYWMiOiI0Y2MyNGNmYzRmN2ZiNTk5ZjI0OTA1MzQ0YzQyNjkxYzViM2RiMjQzMzU0ZmE4Y2YzMzQ4ZGZmNjAyNGRjNDg4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklDSE1nbXBZZ2F2L0tPK3VCWVp1UEE9PSIsInZhbHVlIjoiYll2TVZaemJ5WWNNcHZvNGpzaDJXRFJXaGxXbWxQVTFtNmVJakoySS9GdTJCUElkT1VxV01tb01KMDArWEhTQ1loOFlYT0tNQkpmKzRSVHJCODc3dGJIci85S2VQV2xGaEN2cDhrUFd6SVZmRDJtUG83d1BocE5KdGtUTWVGd201VGU5b2lZWmliSzFDQXdVMGpyUys2OWFGMm93M3F5S2gxSUF3VnpYY1Q3TmRtVmtpQ0I3ZmIzK3VxeUY1bGNEeHp0WVVyN255a0VncGtVcE52Mmc3bUMvS2t6ZTRsWHljNVBtTm5Xc1J5enZyTksxdmpNZ3pvcjNpa3JLUmp6WEdxdUpaRGZsVWtraWRCMzlOZ2VxbmtDODdlcHJRMW03cHNOaWNsc2djMCsyenZzT2RLRmZJckJnYytmbVdwN0R5azh5bWNMZk1RRFEzU2lwWXdvSStvUlU4VzZxRTU2MWdEVEF2ZS9jVjduSVZJSEQwVU9ZM1BDNXJtZHRQc0FpNEVUREllc1J3YmpuVnAvak91QTJEQmhEckpDbGVsS0FISktLTWhkRnZNQW4rNTVEKzNZMGs0d2hlQ3JQUy9uc1U1WGlOVTJUS2EzLzhjbGdQU0hQdXJZKzNKdGlrTjJIdDZPTXJvZXNIRldmVDhrcjNSVDBWS1VlcC9aOGs4V1IiLCJtYWMiOiI3MjU5YTNhNDAxNWFkNmZlM2JiZGE0MjBmYWE3ZjU0ZWEwNmI3OWI0YjdkNzg3NjhjNGQ2OTVkNzU5MDlhYjA1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503028371\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1646105223 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646105223\", {\"maxDepth\":0})</script>\n"}}