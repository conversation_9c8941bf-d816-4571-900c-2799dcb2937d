-- 🔍 استعلامات للتحقق من البيانات الفعلية في قاعدة البيانات

-- 1. التحقق من جداول POS الموجودة
SHOW TABLES LIKE '%pos%';

-- 2. التحقق من بنية جدول pos
DESCRIBE pos;

-- 3. التحقق من بنية جدول pos_v2 (إذا كان موجود)
DESCRIBE pos_v2;

-- 4. التحقق من بنية جدول pos_payments
DESCRIBE pos_payments;

-- 5. التحقق من بنية جدول pos_v2_payments (إذا كان موجود)
DESCRIBE pos_v2_payments;

-- 6. التحقق من إجمالي الفواتير في جدول pos
SELECT COUNT(*) as total_pos_records FROM pos;

-- 7. التحقق من إجمالي الفواتير في جدول pos_v2 (إذ<PERSON> كان موجود)
SELECT COUNT(*) as total_pos_v2_records FROM pos_v2;

-- 8. التحقق من آخر 10 فواتير في جدول pos
SELECT 
    id,
    pos_id,
    customer_id,
    warehouse_id,
    pos_date,
    created_by,
    created_at
FROM pos 
ORDER BY created_at DESC 
LIMIT 10;

-- 9. التحقق من آخر 10 فواتير في جدول pos_v2 (إذا كان موجود)
SELECT 
    id,
    pos_id,
    customer_id,
    warehouse_id,
    pos_date,
    created_by,
    created_at
FROM pos_v2 
ORDER BY created_at DESC 
LIMIT 10;

-- 10. التحقق من المدفوعات في جدول pos_payments
SELECT COUNT(*) as total_payments FROM pos_payments;

-- 11. التحقق من المدفوعات في جدول pos_v2_payments (إذا كان موجود)
SELECT COUNT(*) as total_v2_payments FROM pos_v2_payments;

-- 12. التحقق من الفواتير للمستخدم الحالي (ID = 2)
SELECT 
    'pos' as table_name,
    COUNT(*) as count,
    MIN(pos_date) as earliest_date,
    MAX(pos_date) as latest_date
FROM pos 
WHERE created_by = 2

UNION ALL

SELECT 
    'pos_v2' as table_name,
    COUNT(*) as count,
    MIN(pos_date) as earliest_date,
    MAX(pos_date) as latest_date
FROM pos_v2 
WHERE created_by = 2;

-- 13. التحقق من فواتير اليوم للمستخدم الحالي
SELECT 
    'pos_today' as type,
    COUNT(*) as count
FROM pos 
WHERE created_by = 2 
AND DATE(pos_date) = CURDATE()

UNION ALL

SELECT 
    'pos_v2_today' as type,
    COUNT(*) as count
FROM pos_v2 
WHERE created_by = 2 
AND DATE(pos_date) = CURDATE();

-- 14. التحقق من المدفوعات مع الفواتير للمستخدم الحالي
SELECT 
    'pos_payments' as type,
    COUNT(pp.id) as payment_count,
    SUM(pp.amount) as total_amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2

UNION ALL

SELECT 
    'pos_v2_payments' as type,
    COUNT(pp.id) as payment_count,
    SUM(pp.amount) as total_amount
FROM pos_v2 p
LEFT JOIN pos_v2_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2;

-- 15. التحقق من فواتير اليوم مع المدفوعات
SELECT 
    p.id,
    p.pos_id,
    p.pos_date,
    p.warehouse_id,
    pp.amount,
    pp.date as payment_date
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2 
AND DATE(p.pos_date) = CURDATE()
ORDER BY p.created_at DESC;

-- 16. التحقق من فواتير POS V2 اليوم مع المدفوعات
SELECT 
    p.id,
    p.pos_id,
    p.pos_date,
    p.warehouse_id,
    pp.amount,
    pp.date as payment_date
FROM pos_v2 p
LEFT JOIN pos_v2_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2 
AND DATE(p.pos_date) = CURDATE()
ORDER BY p.created_at DESC;

-- 17. التحقق من المستودعات المتاحة للمستخدم
SELECT id, name FROM warehouses WHERE created_by = 2;

-- 18. التحقق من العملاء المتاحين للمستخدم
SELECT id, name FROM customers WHERE created_by = 2 LIMIT 5;

-- 19. فحص شامل لبيانات المبيعات
SELECT 
    DATE(p.pos_date) as sale_date,
    COUNT(p.id) as invoice_count,
    SUM(pp.amount) as total_amount,
    'pos_classic' as source
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2
AND p.pos_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
GROUP BY DATE(p.pos_date)

UNION ALL

SELECT 
    DATE(p.pos_date) as sale_date,
    COUNT(p.id) as invoice_count,
    SUM(pp.amount) as total_amount,
    'pos_v2' as source
FROM pos_v2 p
LEFT JOIN pos_v2_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2
AND p.pos_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
GROUP BY DATE(p.pos_date)
ORDER BY sale_date DESC;

-- 20. التحقق من وجود بيانات في الساعات المختلفة
SELECT 
    HOUR(p.created_at) as hour,
    COUNT(p.id) as count,
    SUM(pp.amount) as amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE p.created_by = 2
AND DATE(p.pos_date) = CURDATE()
GROUP BY HOUR(p.created_at)
ORDER BY hour;
