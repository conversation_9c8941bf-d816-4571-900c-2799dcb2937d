{"__meta": {"id": "X3f754c65931b47495d7b04fde978b57c", "datetime": "2025-06-06 20:41:04", "utime": **********.805179, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242463.398841, "end": **********.805238, "duration": 1.4063971042633057, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749242463.398841, "relative_start": 0, "end": **********.552097, "relative_end": **********.552097, "duration": 1.1532561779022217, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.552115, "relative_start": 1.1532740592956543, "end": **********.805241, "relative_end": 3.0994415283203125e-06, "duration": 0.2531261444091797, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45690904, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.694353, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.711438, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.779335, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.787978, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03174, "accumulated_duration_str": "31.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.628097, "duration": 0.01504, "duration_str": "15.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 47.385}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.649212, "duration": 0.01024, "duration_str": "10.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 47.385, "width_percent": 32.262}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.666667, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 79.647, "width_percent": 2.111}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.695859, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 81.758, "width_percent": 2.993}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.713331, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 84.751, "width_percent": 3.025}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7498431, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 87.776, "width_percent": 3.088}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.760343, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 90.863, "width_percent": 2.867}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7667649, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 93.73, "width_percent": 2.836}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.78178, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.566, "width_percent": 3.434}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "urdX1KlEV93dFessht7rRiSdbCNw6oZ0HPKGiTP4", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1437139070 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1437139070\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1013716573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1013716573\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1133469024 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1133469024\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-840201349 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1734 characters\">XSRF-TOKEN=eyJpdiI6IkplQWVQcEQwUHlHSmY5QXhtbVFFZUE9PSIsInZhbHVlIjoiZzFWSEFwM25lRTZ1eTF3dlk4Z2JhU1lGUDZSd3hsSVVDcm5wbGlISURQMEkrb2gvN1d2WjBzeExOR1hWNiszejN6KzBIYkV3VjYzcTZaM0EzS0N5V3RoRHE2bVJDaEpWeURzdG1vZHQ0b1BWQTR0MnliUmxBV09ObGNQOWU0UDYrcXhodUMvT2FXME5FSVgwVTY4WkhTNDVrWG91QkxWQzFnNEhDTmpwNklwQ1BtZTJscGdSK3lwTktXdVE1RnhIS0RjdTBxWWxFVVc1b3Vha0Z2eXJJeUhjcUk0bi9iam54WHRQeDc0Yk9TVllEMTBsMHpIc2dvZHJnR0pIL2ZqZEVGbGY5LzJmNExPQ3piYkZJNXpSZ2RDeG50dnI4TGd3TjhHcnNqYUVtWm16WFR3MUJHcjBNOWNtZnYwRFhRaytOUzkxOEQ2Q08vY0ZDUkN1K2VqNTUxVFFRRFdXbUthdE1tVVlGUEYveE1WSUl2NXFMUjRYWkZaV2FqSWtnaG1LanlpQlY0cUtRMVYyVUFrMWlXN3ZDRHEzRHhLZTNCQzRwcUZwdEpQckc4VExoS2RteVBkUDlNZHRTaGl3Z2lSK3hSUldBamFBd2p4MkhMWnhiMHdUa0drN0hWYWI4blZqSEVFcEVBbGhERXdZY2NFSFJyUndocWlvY3pHMTRmZUwiLCJtYWMiOiJjMzI3MDdjMTgxOTk3OGIzM2M4NDNkNzkzZTQ2OTVlNTM5MTNjYjg0ODRiODM4MzNkYmNlMTkwNDI2ZWExMGQzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklnWHlURzJpVjdDSHN3aXRMc0hXR1E9PSIsInZhbHVlIjoibUJpaVBPMmdqSVNDTVVjZEdQYmNjOVpmZzR4dStoYko0b215YlFlRnoxN293ejBwMlJFMVdUUXNjeFFqMWh1OFFta3djQkJpYmZOQ2lKT0tYVGFSam4xRjJtYWJFbklpZjVMSzd1UjFkY01yZzI0dENTL2twd3MxQWFTbUNBV1QvUzcxbFpCRUd6RW1KcjJpSFgwcEVyUDNWSldHM296UWZsaHUrOUVVUEdONElEVjNBS2ZIcTlUVUllS2VaSnJDRWlnRElQa0VBUExBOC9pTWhLZjNmWHJ6aUw2ZW84SnZWaE9sRUU5aVFMNEF6bk56RXRZY05NWURqQTQ1UnV0TG5Ma1YxMlJNa0lCRDRWamZqbkFoQjlyeXZRQ3phVmplcSsrb1JkR2o2clRrazE5Ynh1WVFiQTQxcHI4RHVRRkpHeVFoNlVIQzhzdjVURkpEb1hLSDRCK2FNSkc0cnhBMjZRalJTTElNeTUzMGRWcUdaY0NLMVp4aWgzYzZ3T2xRTjZpWlZSU3YydGlOTWhNVkdPbUZQdzd6Tmk4RWYzSkkvVGdESnRVQ1prazhoRXFON0VkSTdIakZoVHN5K0dFTWNMUHFQNWRCUVl6UWpjNXk2SitKdHp3aDZkOXptNnhVNUp1ZzNOZUZaZGkrU3NlcTY5UjJUZ0RRQXNPVmtpVkwiLCJtYWMiOiJiYzNiMDNlNTk4NzQ3MzBlYmZlYzdjODFjMjgwMmY3ZGJlN2I1MTE5MjBjMjhiOTU5NGFmN2Q5ZjBiZTMzZTVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840201349\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2014956600 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">urdX1KlEV93dFessht7rRiSdbCNw6oZ0HPKGiTP4</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WE1m90nZ6aBo3f7ssVuEMg9TBWIzaQIIH4j27yrF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014956600\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2133642154 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:41:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1WMlJMaGdLMzBGUmRPQ3dkMDlDUkE9PSIsInZhbHVlIjoidXRyMkRXbzh3NitrbVIyN2c3c0VaZzRVOGdab0Zwa01CdUt6VmdkZktuaUEzK3V1YkJUUUpPTGFHeHhZQ09ocENCTzQ1Vi9XeEtPckFGS1luRE53bzRFZEJYTTBxa252UW5jZS9qL0VBY3VaQy9jVEZ0d1JTa0gySlpiTStEb0RKZkJkcXl5TGQzL2poeVJGL2NXdnNueDFscTRMcGlEY29wcUg2TE1IZlZFZE5tcjNrR2ZwdmdYYktLcUZSQTRQZXYyazhIVitEYkdBeFEyV2o4WnpnbHdwdnIzMzhJVGxBMHZNNXRYSlNIc3BJZHl1OVpoNU5mTHRuc2h1bG1QVktyZVhpaTNLSVBPOCtPRzc5NFpYN2pHakVlemNhUG1iQkhBNHNNRFVmai9acFd4Qkt6V1ZEUTNlb3pwR0gycE82SWlCZDhVZE45eGtXUHNXdGE1YXNNenNjQmxJSXVyeVE5OWR3cnRxMG5ESDBDTWRFSXFoT2E1TnRyNk81dmRnZ25QU1VMT25kNmEvZjlDVFRNZjd4UHNIUFlMN1JTdlRueUVDaUd5TEFaVlJkNGZLU05zaWFzUlZLUE5QeC9tRVgwUS9URmxSelFyV3JkejlFczhNUFB2TkFnbnIxbzJrZ2ZGTlFUc0prYmxnbnFIYS84UUhvWkE1MytTMFNWd2QiLCJtYWMiOiJhNGE0ZWM3YmE3ZGQ5MDdhZTIxODBkM2IxOTFkYzg4MTNlNDE5NTA1Mjc2Mzg5ZjY3MjhkNzFhYTUyMmQ5Y2MxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlaSDU4YnFveDl4M1FxVHVCMC9mN1E9PSIsInZhbHVlIjoiMFo4Z01CcVA3VkYyMkVwbzIvRmZOK0gvNFNtQzcwejNNalJMeE1rUHZJbEFJV0tqWndzSjEweU5QRFhvT1pFakNJMDEwK1dPM1F1WmlEMEFldjhoOU5TV29vSTNrSUNwQ3RhWldJWm1IaG9sVjRRYzkvSzAvOENzbnFNR3lsY0xpYzB2Y3EzdHIxU2crd29pUDJ4RGZKeU9uSGEweERDTnpCNUpRR09OdzJWRUZCN0d2WGVQc3U1UnBjTTB0TDNkTHBJWUhYVDNKYWtRZ2Z3cWRoTzJuWk1YTUZUYlpSRGwyOURLR3ZTSENCNXJ5b1pKaXFrNWhiOEY0enNCTk54ZG5YS21pOUhEMUFrRWdkdXM3SGhkeUZmV1RTa2sxb2IydzFoSXRyTW8xRmNGUkhLUXNRRWNPRWhZeEFKYkxndVI3ME9uTnZGQVA4SFdtK0Z6NFFkZG5TQi9JdktLSlF6Y2ZjYjRKTW04RnJBMC9WNW5CUHhqUGE3LythNWdqNys0SUJ4eDlwVDg0QmhCZXR6TE45eXlrUlVHdlVWZEdBaFJVRTNndk9CMG90UkFlb1h0WnFQRjJrdTVuZThXUG9mbzlFT3hFNEZoNlJQWWUxMzNHTW9kQ0R3cWg2WEV6OGpqMno4ODRtUnJXWC80bUZFUVJsMmdyQ2JlY08xYlFzYi8iLCJtYWMiOiJhODJlN2VhZDgwYmNmN2QzYzdmOWQyZDhmZjgxOGUyMGJjN2EwOTlkMjJjYzA5MGNkMTI1MjdjYjk2NjNhOTgwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1WMlJMaGdLMzBGUmRPQ3dkMDlDUkE9PSIsInZhbHVlIjoidXRyMkRXbzh3NitrbVIyN2c3c0VaZzRVOGdab0Zwa01CdUt6VmdkZktuaUEzK3V1YkJUUUpPTGFHeHhZQ09ocENCTzQ1Vi9XeEtPckFGS1luRE53bzRFZEJYTTBxa252UW5jZS9qL0VBY3VaQy9jVEZ0d1JTa0gySlpiTStEb0RKZkJkcXl5TGQzL2poeVJGL2NXdnNueDFscTRMcGlEY29wcUg2TE1IZlZFZE5tcjNrR2ZwdmdYYktLcUZSQTRQZXYyazhIVitEYkdBeFEyV2o4WnpnbHdwdnIzMzhJVGxBMHZNNXRYSlNIc3BJZHl1OVpoNU5mTHRuc2h1bG1QVktyZVhpaTNLSVBPOCtPRzc5NFpYN2pHakVlemNhUG1iQkhBNHNNRFVmai9acFd4Qkt6V1ZEUTNlb3pwR0gycE82SWlCZDhVZE45eGtXUHNXdGE1YXNNenNjQmxJSXVyeVE5OWR3cnRxMG5ESDBDTWRFSXFoT2E1TnRyNk81dmRnZ25QU1VMT25kNmEvZjlDVFRNZjd4UHNIUFlMN1JTdlRueUVDaUd5TEFaVlJkNGZLU05zaWFzUlZLUE5QeC9tRVgwUS9URmxSelFyV3JkejlFczhNUFB2TkFnbnIxbzJrZ2ZGTlFUc0prYmxnbnFIYS84UUhvWkE1MytTMFNWd2QiLCJtYWMiOiJhNGE0ZWM3YmE3ZGQ5MDdhZTIxODBkM2IxOTFkYzg4MTNlNDE5NTA1Mjc2Mzg5ZjY3MjhkNzFhYTUyMmQ5Y2MxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlaSDU4YnFveDl4M1FxVHVCMC9mN1E9PSIsInZhbHVlIjoiMFo4Z01CcVA3VkYyMkVwbzIvRmZOK0gvNFNtQzcwejNNalJMeE1rUHZJbEFJV0tqWndzSjEweU5QRFhvT1pFakNJMDEwK1dPM1F1WmlEMEFldjhoOU5TV29vSTNrSUNwQ3RhWldJWm1IaG9sVjRRYzkvSzAvOENzbnFNR3lsY0xpYzB2Y3EzdHIxU2crd29pUDJ4RGZKeU9uSGEweERDTnpCNUpRR09OdzJWRUZCN0d2WGVQc3U1UnBjTTB0TDNkTHBJWUhYVDNKYWtRZ2Z3cWRoTzJuWk1YTUZUYlpSRGwyOURLR3ZTSENCNXJ5b1pKaXFrNWhiOEY0enNCTk54ZG5YS21pOUhEMUFrRWdkdXM3SGhkeUZmV1RTa2sxb2IydzFoSXRyTW8xRmNGUkhLUXNRRWNPRWhZeEFKYkxndVI3ME9uTnZGQVA4SFdtK0Z6NFFkZG5TQi9JdktLSlF6Y2ZjYjRKTW04RnJBMC9WNW5CUHhqUGE3LythNWdqNys0SUJ4eDlwVDg0QmhCZXR6TE45eXlrUlVHdlVWZEdBaFJVRTNndk9CMG90UkFlb1h0WnFQRjJrdTVuZThXUG9mbzlFT3hFNEZoNlJQWWUxMzNHTW9kQ0R3cWg2WEV6OGpqMno4ODRtUnJXWC80bUZFUVJsMmdyQ2JlY08xYlFzYi8iLCJtYWMiOiJhODJlN2VhZDgwYmNmN2QzYzdmOWQyZDhmZjgxOGUyMGJjN2EwOTlkMjJjYzA5MGNkMTI1MjdjYjk2NjNhOTgwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133642154\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2028524212 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">urdX1KlEV93dFessht7rRiSdbCNw6oZ0HPKGiTP4</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028524212\", {\"maxDepth\":0})</script>\n"}}