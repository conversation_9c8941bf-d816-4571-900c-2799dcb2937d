{"__meta": {"id": "X2b0c05bf83c513366ecc82e09488174b", "datetime": "2025-06-06 20:38:46", "utime": **********.865208, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242325.008863, "end": **********.86524, "duration": 1.856377124786377, "duration_str": "1.86s", "measures": [{"label": "Booting", "start": 1749242325.008863, "relative_start": 0, "end": **********.6051, "relative_end": **********.6051, "duration": 1.5962369441986084, "duration_str": "1.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.605127, "relative_start": 1.596264123916626, "end": **********.865244, "relative_end": 3.814697265625e-06, "duration": 0.2601168155670166, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03219, "accumulated_duration_str": "32.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.723967, "duration": 0.02647, "duration_str": "26.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.231}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.796817, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.231, "width_percent": 5.25}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8090172, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 87.481, "width_percent": 3.635}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.834467, "duration": 0.0028599999999999997, "duration_str": "2.86ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.115, "width_percent": 8.885}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/warehouse\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1961815441 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1961815441\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-57828543 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-57828543\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1674908588 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674908588\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/warehouse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242266823%7C13%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitHRGVFdk1HWW0zclpDdXZvOWppeUE9PSIsInZhbHVlIjoiekFIbFFiNkliMTBNdFRxWFNvNXZ6WXk4VnhNZWh3OGRkeEU4SzVybHlvZmhrVW96OXdmeXA1MXJ0M05Tc016bWdkSDdhemVNZ0pXL2FpKzhlWEJzZnZjd3hwY1k0TXp0MDd2K2hST2Q3NjZBUkFzRFVWM0JqRUhtTWxEenJDdGhrWHRDN2FwVm9GeGVSUG9TSWcwaHlBNEVTR1hOTkFSSFdBV2xCd0FJdXF1NHZQaVJHV2RhaG9VL21GZ2hkZnRZR0hVZ2J4SGt6MGFqVytNcExETmVSSWhRMS9CSmhRTy95OFJXSGI5eUUzUkxCTmhQNmREWHhSWUxsUTZzMTRaMHZpdU1PNkVSeHorZUJ1NEVQKzEwSENMR3lqWjBpOG1HeHdNWXk1emx4cnQzRC9PbG0xbkJkcVhqUzB3N29idFRZZ3g5Q2dmbDBzYXlVaHJHejJGUFRTcjhPK2hiRXQwT1huUzFmZTI4ZFZ2UXFBV29pRjJmSmg1L0J4N1haNlRSdHVDdDB6SXhCTmYzYjYvcDErZHg4c3YvZWVuc2NYcStrOEM1TjFkU0U0R056SGcxRklnRTRkMHVzd1NCK3FUSkRybStwZHVLWFhmZHFoR2t6TGd3dWhleEsvY0lmQllBSkFtTU1uMENiTGJaRXlYd0lZbGF5bEVQM3NEVVRIZSsiLCJtYWMiOiIyZGQ5ZGRmNjk0Yzg2NzVkZjhiYmMzODdiODFiNzZjMjQ0OWNjMTQ2ZjM2NzAzYzNkOTNiYmM5YmRmMzVkM2VmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjRKeVJZMHVpaGFyTEE5Y3p4R2dJTFE9PSIsInZhbHVlIjoiKzVxR3lJRmFMejd0K2VlMVluanlJRHM3VEtIVHlSeGlkOWs1QTYyQ2I2UmQrZmh3RVNGSXI2Y2tXTmdTeUh6SDBXVWhzckZndmRlbHdDUHNmN2NiU25QUTFjVDJJMXdFMnhObGltZWp1VTNCbGkzbjVkTm9uQlYwMmFnMGRlald1QmZpczF5UW5OL0dvVmZJUkl5V1BBRlgwcjJ5ZVlwVlkwd2loRmxUYlZVMVR6VUowR0xxeW5Vc1lRN1hFcTFDR0ZnK0g0ZkpnUmErQVFMSmdOai9oSVlwUEdtUittWnJEMm1ucnlXYm9rVGpSWWxYNDc5Sk82clgxRks0bFdrb05QS0xQMHBkQW5NWFg2TG50TllWOGdLR0RwMTlWVDN2Q0d0Nk5DdHpWTW41OUUyT0Z4WElKR0c2VnBXb3J3TVFURDlnZi9xSWQ0YVR3TG9DNVh1V1NXaW1JZWIwaEpvM3piNmloazlEM0xQSHZDdEorWUlkMk04SktWclhlcVdYbTFQVWxGcjJPVDJSV05WS08zMnZtcERSbElCbXNyWTl1QmdmbThSM3UzYTZaVXRhaktaOXNRVVR6N2I3MXEyZk9SYUJObEJ0QU9EM1BKLzNhY0dSQ3ZWZ0Nrbit3eEV0UlNMYzZOV0RjeUxtV29FRE4yc0ZKSkVYTmpabVhaNkQiLCJtYWMiOiIwZTcwYTczNzdmYTcxYjAyZjA2NzE1MDBkZDAzOTc2OTYxYzQ4ZmFmZmM1N2E1YTAxZjRkM2I2MWMzMWFlMjM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-280199883 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:38:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJHeWVyZ2hTdk02SGYwRzFiM0Uvcnc9PSIsInZhbHVlIjoiNVFESTJnWTBPSFIzWjgxeVhCQlROMVJrME1EdmdSWkMzcFFFakwrY2g1NnpVZXpjMkRaZTFsYit1TWtjYjJ1bU5qdEVDenlLMkZBVzBFSkJ4MklVU0p3Ylppc29UYlJ5eElsUVo1RFRsMDIwblIzcnIyQm5yQ3R4QjduRkRqWjFYZkRjWHZHa1BMU0RTNXF4em5nMVQvajZxRTA5aVpmSnFSOFlGOG4zVFZmSEtQeVM4MWxCeE0zbk1kb0tDdlVHczE0N2tIYjg4Y2xRbWtZQ1hxZVNRbVZocWlDcE1XemRoWFVoSEpOSWJWajJQc3hrSTk1Q1d5cUQrcFFJSmdqUzNyODRqRVZtNEd4NGJleVJ4ZSs4eWU2RFN3U1JsSTRpaHZmcHRqZ0c2a29yV3B5cFY1eGg4VERNUnk2ME50MHZDci80dEpTVnhtejBUZ3V5eDA2aEZNTjN5ZDlGaXNaQkhwems4YzBzRXFjS0h1OW9CMmRJRjg5SlJmeklHQW1RQU1JMGh3bUZlRU5nWFg4aVpsaXJqZ0ZjWWo2ejlnMzFCMHA3SGsvRkNEMitJN2FpanRQWFNGVnRmUjFybEppazRPdDdORGlKd3ZiYkptUEFQaWw5REtSSnJyRTMrb3BEUm5BUnVqUm45dVR2YlFZRENRc0YwMlk0cG5FTEtlNmoiLCJtYWMiOiJjMmQ0ZDc2YmQ4ZTFhZTEwYmFlYjhjZDY5MTZmYzlhNjJlMmM0NDNlZmNkMGQ2NmZiMzliNWNmNzk0YWFiODFiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:38:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjlIMjRsMGJZTzFNSHdydHhBZyt2V2c9PSIsInZhbHVlIjoiRjA0Zzhydng4blpFbnNyVmgrc3NQOWlweGg1RFdweXdtMHlJbmZjNFhPUUprN2xMVkFrNzFUVC9SZnJDTEVSQzMzNGlIZTVZWCtQeGZIclEveG5CeHZpbzNiT2dzekMwTnJUU2pPckdyU25oNWZGL05lRSt0L3lXdEtlOVpKOWxXbjdJaEdPU0czUzJ4d21UMUJpYjl1MUNVMmZXNHZtYXNndjllMGk2Q1dJVnI1eFlDeW9keC8wbzdVSFY4eS91c3dHcStGTVBvTU1kc2VablgxNCtadFNEcy9wZ2x2c2dvWHBKZi9QdTBaQ0N0NFZTZEhaNUNsMTJLRGUzeEl5cjcvVkxWNnJKODJNUVlVWEpFODEvUjZ1ZDZPV3llTGlLYXZDcEtPRzFyZ1hleFYxM1BMMDhGTlhDRFlzdTc2WkJzY2dDclphU1FIWXJZM1ZmV0lRSjNWSnpBUWMxcDJzRXlFUmIwbEdqdGJ4YkQvK3ZtVmxqWDVnd21hRDNkYWQ0dUxsNHdjNEtYWHZxVUduUzdPSmZMVTc0SXU4L2RZaVJaYUVQVnBvMnpiZTFQSm9PcEFNWUpVdEpBTUVsS2I1Z3B0V0o4TXZMNmt0MmZaN21xMlhOVWJXSlh4Nm1UV2VhTDcxT3NyanpCc3dEbFFGYVdaeUhOTnk2R2ZNUUgzQS8iLCJtYWMiOiIyNjE4MmFmZGRjMjI2MzBlYTM2YzY1YzM1MmVjZjkzODAxODgwNzE0MjNhM2QyNGIyYTdmOWQyMjUyMWYyNjM3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:38:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJHeWVyZ2hTdk02SGYwRzFiM0Uvcnc9PSIsInZhbHVlIjoiNVFESTJnWTBPSFIzWjgxeVhCQlROMVJrME1EdmdSWkMzcFFFakwrY2g1NnpVZXpjMkRaZTFsYit1TWtjYjJ1bU5qdEVDenlLMkZBVzBFSkJ4MklVU0p3Ylppc29UYlJ5eElsUVo1RFRsMDIwblIzcnIyQm5yQ3R4QjduRkRqWjFYZkRjWHZHa1BMU0RTNXF4em5nMVQvajZxRTA5aVpmSnFSOFlGOG4zVFZmSEtQeVM4MWxCeE0zbk1kb0tDdlVHczE0N2tIYjg4Y2xRbWtZQ1hxZVNRbVZocWlDcE1XemRoWFVoSEpOSWJWajJQc3hrSTk1Q1d5cUQrcFFJSmdqUzNyODRqRVZtNEd4NGJleVJ4ZSs4eWU2RFN3U1JsSTRpaHZmcHRqZ0c2a29yV3B5cFY1eGg4VERNUnk2ME50MHZDci80dEpTVnhtejBUZ3V5eDA2aEZNTjN5ZDlGaXNaQkhwems4YzBzRXFjS0h1OW9CMmRJRjg5SlJmeklHQW1RQU1JMGh3bUZlRU5nWFg4aVpsaXJqZ0ZjWWo2ejlnMzFCMHA3SGsvRkNEMitJN2FpanRQWFNGVnRmUjFybEppazRPdDdORGlKd3ZiYkptUEFQaWw5REtSSnJyRTMrb3BEUm5BUnVqUm45dVR2YlFZRENRc0YwMlk0cG5FTEtlNmoiLCJtYWMiOiJjMmQ0ZDc2YmQ4ZTFhZTEwYmFlYjhjZDY5MTZmYzlhNjJlMmM0NDNlZmNkMGQ2NmZiMzliNWNmNzk0YWFiODFiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:38:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjlIMjRsMGJZTzFNSHdydHhBZyt2V2c9PSIsInZhbHVlIjoiRjA0Zzhydng4blpFbnNyVmgrc3NQOWlweGg1RFdweXdtMHlJbmZjNFhPUUprN2xMVkFrNzFUVC9SZnJDTEVSQzMzNGlIZTVZWCtQeGZIclEveG5CeHZpbzNiT2dzekMwTnJUU2pPckdyU25oNWZGL05lRSt0L3lXdEtlOVpKOWxXbjdJaEdPU0czUzJ4d21UMUJpYjl1MUNVMmZXNHZtYXNndjllMGk2Q1dJVnI1eFlDeW9keC8wbzdVSFY4eS91c3dHcStGTVBvTU1kc2VablgxNCtadFNEcy9wZ2x2c2dvWHBKZi9QdTBaQ0N0NFZTZEhaNUNsMTJLRGUzeEl5cjcvVkxWNnJKODJNUVlVWEpFODEvUjZ1ZDZPV3llTGlLYXZDcEtPRzFyZ1hleFYxM1BMMDhGTlhDRFlzdTc2WkJzY2dDclphU1FIWXJZM1ZmV0lRSjNWSnpBUWMxcDJzRXlFUmIwbEdqdGJ4YkQvK3ZtVmxqWDVnd21hRDNkYWQ0dUxsNHdjNEtYWHZxVUduUzdPSmZMVTc0SXU4L2RZaVJaYUVQVnBvMnpiZTFQSm9PcEFNWUpVdEpBTUVsS2I1Z3B0V0o4TXZMNmt0MmZaN21xMlhOVWJXSlh4Nm1UV2VhTDcxT3NyanpCc3dEbFFGYVdaeUhOTnk2R2ZNUUgzQS8iLCJtYWMiOiIyNjE4MmFmZGRjMjI2MzBlYTM2YzY1YzM1MmVjZjkzODAxODgwNzE0MjNhM2QyNGIyYTdmOWQyMjUyMWYyNjM3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:38:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280199883\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/warehouse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}