<?php
/**
 * سكريبت إصلاح الفواتير غير المدفوعة
 * يجب تشغيل هذا السكريبت من خلال Artisan Command أو مباشرة
 */

// هذا الملف للإصلاح فقط - يجب حذفه بعد الإصلاح
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الفواتير غير المدفوعة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        h2 { color: #333; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .step { margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
        .sql-code { background-color: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; }
    </style>
</head>
<body>
    <h1>🔧 إصلاح الفواتير غير المدفوعة في POS Summary</h1>
    
    <div class="section info">
        <h2>📋 تشخيص المشكلة</h2>
        <p>الفواتير تظهر كـ "غير مدفوع" في POS Summary بسبب:</p>
        <ul>
            <li>عدم وجود سجل في جدول <code>pos_payments</code></li>
            <li>حقل <code>payment_type</code> فارغ أو null</li>
            <li>مشكلة في العلاقة بين <code>pos</code> و <code>pos_payments</code></li>
        </ul>
    </div>

    <div class="section warning">
        <h2>🔍 خطوات الفحص</h2>
        
        <div class="step">
            <h3>الخطوة 1: فحص الفواتير بدون سجلات دفع</h3>
            <div class="sql-code">
SELECT p.id, p.pos_id, p.pos_date, p.created_by, pp.payment_type 
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
WHERE pp.id IS NULL 
ORDER BY p.id DESC 
LIMIT 20;
            </div>
        </div>

        <div class="step">
            <h3>الخطوة 2: فحص الفواتير مع payment_type فارغ</h3>
            <div class="sql-code">
SELECT p.id, p.pos_id, pp.payment_type, pp.amount 
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
WHERE pp.payment_type IS NULL OR pp.payment_type = '' 
ORDER BY p.id DESC 
LIMIT 20;
            </div>
        </div>

        <div class="step">
            <h3>الخطوة 3: فحص إجمالي الفواتير والمدفوعات</h3>
            <div class="sql-code">
SELECT 
    (SELECT COUNT(*) FROM pos) as total_invoices,
    (SELECT COUNT(*) FROM pos_payments) as total_payments,
    (SELECT COUNT(*) FROM pos p LEFT JOIN pos_payments pp ON p.id = pp.pos_id WHERE pp.id IS NULL) as missing_payments;
            </div>
        </div>
    </div>

    <div class="section error">
        <h2>🛠️ حلول الإصلاح</h2>
        
        <div class="step">
            <h3>الحل الأول: إنشاء سجلات دفع مفقودة</h3>
            <p><strong>⚠️ تحذير:</strong> قم بعمل نسخة احتياطية قبل تنفيذ هذا الأمر!</p>
            <div class="sql-code">
-- إنشاء سجلات دفع للفواتير المفقودة
INSERT INTO pos_payments (pos_id, date, amount, discount, payment_type, created_by, created_at, updated_at)
SELECT 
    p.id as pos_id,
    p.pos_date as date,
    COALESCE((
        SELECT SUM(pp.price * pp.quantity) 
        FROM pos_products pp 
        WHERE pp.pos_id = p.id
    ), 0) as amount,
    0 as discount,
    'cash' as payment_type,
    p.created_by,
    p.created_at,
    p.updated_at
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
WHERE pp.id IS NULL;
            </div>
        </div>

        <div class="step">
            <h3>الحل الثاني: إصلاح payment_type الفارغة</h3>
            <div class="sql-code">
-- تحديث payment_type الفارغة إلى 'cash'
UPDATE pos_payments 
SET payment_type = 'cash' 
WHERE payment_type IS NULL OR payment_type = '';
            </div>
        </div>

        <div class="step">
            <h3>الحل الثالث: تحديث المبالغ الخاطئة</h3>
            <div class="sql-code">
-- تحديث المبالغ بناءً على منتجات الفاتورة
UPDATE pos_payments pp
SET amount = (
    SELECT COALESCE(SUM(ppr.price * ppr.quantity), 0)
    FROM pos_products ppr 
    WHERE ppr.pos_id = pp.pos_id
)
WHERE pp.amount = 0 OR pp.amount IS NULL;
            </div>
        </div>
    </div>

    <div class="section success">
        <h2>✅ التحقق من الإصلاح</h2>
        
        <div class="step">
            <h3>فحص النتائج بعد الإصلاح</h3>
            <div class="sql-code">
-- التحقق من عدم وجود فواتير بدون دفع
SELECT COUNT(*) as unpaid_invoices
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
WHERE pp.id IS NULL OR pp.payment_type IS NULL OR pp.payment_type = '';

-- عرض آخر 10 فواتير مع حالة الدفع
SELECT 
    p.id,
    p.pos_id,
    p.pos_date,
    pp.payment_type,
    pp.amount,
    CASE 
        WHEN pp.payment_type IS NOT NULL AND pp.payment_type != '' THEN 'مدفوع'
        ELSE 'غير مدفوع'
    END as status
FROM pos p 
LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
ORDER BY p.id DESC 
LIMIT 10;
            </div>
        </div>
    </div>

    <div class="section info">
        <h2>🚀 تنفيذ الإصلاح عبر Laravel</h2>
        
        <div class="step">
            <h3>إنشاء Artisan Command للإصلاح</h3>
            <p>يمكنك إنشاء أمر Laravel لتنفيذ الإصلاح:</p>
            <code>php artisan make:command FixUnpaidInvoices</code>
        </div>

        <div class="step">
            <h3>أو تنفيذ مباشر عبر Tinker</h3>
            <code>php artisan tinker</code>
            <p>ثم تنفيذ الكود المطلوب</p>
        </div>
    </div>

    <div class="section warning">
        <h2>⚠️ احتياطات مهمة</h2>
        <ul>
            <li><strong>نسخة احتياطية:</strong> قم بعمل نسخة احتياطية من قاعدة البيانات قبل التنفيذ</li>
            <li><strong>اختبار:</strong> اختبر على بيئة التطوير أولاً</li>
            <li><strong>مراجعة:</strong> راجع البيانات بعد الإصلاح</li>
            <li><strong>حذف الملف:</strong> احذف هذا الملف بعد الانتهاء</li>
        </ul>
    </div>

    <script>
        console.log('🔧 سكريبت إصلاح الفواتير غير المدفوعة محمل بنجاح');
        console.log('تأكد من عمل نسخة احتياطية قبل تنفيذ أي أوامر SQL');
    </script>
</body>
</html>
