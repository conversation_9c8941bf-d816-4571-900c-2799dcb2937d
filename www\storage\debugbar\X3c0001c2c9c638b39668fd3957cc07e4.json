{"__meta": {"id": "X3c0001c2c9c638b39668fd3957cc07e4", "datetime": "2025-06-06 20:37:09", "utime": 1749242229.015494, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242227.289998, "end": 1749242229.015531, "duration": 1.7255330085754395, "duration_str": "1.73s", "measures": [{"label": "Booting", "start": 1749242227.289998, "relative_start": 0, "end": **********.77627, "relative_end": **********.77627, "duration": 1.486271858215332, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.776296, "relative_start": 1.486297845840454, "end": 1749242229.015535, "relative_end": 4.0531158447265625e-06, "duration": 0.23923921585083008, "duration_str": "239ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.010750000000000001, "accumulated_duration_str": "10.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9103062, "duration": 0.0066, "duration_str": "6.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.395}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.953435, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.395, "width_percent": 12.93}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.962801, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 74.326, "width_percent": 11.442}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.988318, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.767, "width_percent": 14.233}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-517774832 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242217668%7C8%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtQNmVUUUY3a29VSmxxV0gyRmZmNVE9PSIsInZhbHVlIjoiMjlEdW54MG5PSnFMeUE1cFZRTW4waWlwR0libVhSYnhENjlReXM3cFZlUG1yQWhURGdDYWpWcE1teExpdGxEWWsvaWNLNFVuUC8yRDJpd2JjVFJOVGJQSkxNNDdiTkFSU3hQUlBFeXp5TGh4cFRKNWt3TnBCdzFPQmxrcit2eHBJM1hTMlRxSDM4V09EdHc1aW8vT2ZNSGs3UmRTNzhKZ2VYNXZLbGR4Q0RvQ1I5cVBrV0NqUVdJNWUvcjBGUFRsUk5SY1VIeFEyZDB6cVFSdkUxZkwyUzBqVFpEL2RxeWNOZGt1N2g4Z0JLc3EvRGRPK3ZVMjZVeC9aYWc5MnRmbkFBU2M3SlRzZkc1U3lmci9BTjUrdGErVjYwcnRuUk84TTBxclgvU0xxOEU0SHE4UE1nTVJwbHpqYkg0VWp2M3lHN3JUMjhMMm1VZCs0T2hFT2tQc0E0MklXaFRnTTRKUEI0dG1VaDN6S0JIbWN1MmhyQXVDWUxkdXhrVG5sa1NTeGFzSXJ1V2FFS25rd3lnSjcxVGthWHNyMUxvazhjdXpDdHp3eGwyZkhJYnV0a25SZGFtVWo5cHdxWjNEckptUHUrZVdTOTNCNWkrQkNFamdMZ0JNak5pOGNDRVJNU3RESzF0RGQxU29peEYwcnl3R3VYa1YyNmQ4L1NwTG5nSEYiLCJtYWMiOiJmMzdkOTVhZGE5MjNhYjBkODE3NTY0NGEyMTNiOWU3YjJjNDU3M2NmNTE2NWNkZDkyN2Y0MmQ1ZDQwYWM3MzViIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImoxeHhDcFVyaFBTYUplYUh1WTdBMkE9PSIsInZhbHVlIjoiN0IzcDk4UE1SY1kreUM4ZHZidFo4a05vQllJZHFGWnVOZCthc3dIazFnSHd2OCs4cm1YNm1xV2p6TGo0ZnhSb3hOa3U0U0pob2I0Mi81V2wremRPSHJjM3hsSkt3TjBWU2syRDBvVFA5SXZtdUZaQmtESlZQU29LNkV6UmxJWFN2akJNeDJYYzhRQ2NINkpIbXFuNWg1VHZpcXl5R3F3NEhYMUFHOEYwM1hIdFpqcWpHbExXdjFWd3p0bXpOZmNaYVNuOFl5bTRsQk03cHA2T3BoWSt1OHZoL3VuaGxpdmZkZHFiNko4Mi9adEQrVG9DRGoyUnl0K2w1eGpaMGVFWHZVTVQxUGRXQ3BCbzFTUWlCOE5BQnZPblNseXBhRTVZcWhTcFdqc1U1U2lSZ2x3SkhEdExnc3hpVVZUUGdFcmpVY1Z0eWRwc1J5REVUYXFwaDVlS1ZLaCt2dCt5Q3dkckx1L0xSNXJoZVlpR2RTYVF6NklIMlZvQ1hmYTRiQWlwckdLdzAyby9FNUNUbldId2lOQkpZaUhheEdXTm9MV0FYMWgwdW9tbzBFbTJTQjJHR1E2NkFTMGUzTmdMTXBTSk55aW11dW1uaWtET0xiOFh3dGp6V1drY0R3bWpONGlMN1JqamhFRWdTODZzR1lWNkIzS3ZKcTZvOEpTdzIwZGUiLCJtYWMiOiJlYWQ1ODA5ZDllMjVjOWY1MTM5Zjg3ZWNjZTBlY2RkMjI5YmZlNjVlYTQwMDZlODI3YzU1ZDc0NmIwNWY5ZjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517774832\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-94586270 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94586270\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1839013293 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:37:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjIrU3RaWmdNV2ZpM1Z3bU5Hc1JZMmc9PSIsInZhbHVlIjoiZ21obmZxcmQvNGtjQjFqNUszdjZjekZNdVJrQytHWWQyYzlqekppR1VweDVid1FoamNLR2g3L0k1Y3VrMThjNFM1aU02TnN6VFE4Y1FWT3ZXV0Z1OGlZY2hJbllZVFhlUzVMZGNRRStJVUIzQjZwNExUalFPeVNzbW8yK1cyclkzeEpvNklRbUlDb01zamY2b3p3WlNaZjJsN2llVmdsa0laenZNanFwbGNXdFR0dmlJd0pzeEcra3JNRkRTMGc0dFAxVHhOUDhKcnQ2bHlPZFdIWGQxOVdXdGdHcWNXdU5DVUUwNG1ZTFJxejV5OFoyVnFvVXllL0YzNnl3dW1ITXRJeUxma0paaXQxRnNIT2RRdTNmcC8rdVBIWnFyMjlBc3lvZ0JrTlFhYjBuK1JLTFZoNnhZa2RJTUhJUTQrN2RPZ1RtSVZZb1VPQU1jeGxjWE5MeXF3SUFub2lOaHgvY1g4bVplYnU0ZytNeWtYMDRsbm1EUnpnWVFCWHNxRHJZVFdybkVnWkxjN0EzL01rK1B0ZG9FU2lVZC8xT2JGUyt2bjBDblNkQ2xjZzVRamF1cEQ1QVcvc1JINFo5SlVWc0MwcVhaRmFkRk14c3BoY09jdENPdjlpZm9mdXg3T0pDT3R5ekhmYzRwMGRqUkhaQmtVTnlheDJoemJ4dDUzUXAiLCJtYWMiOiIwOWU2NDk2ZGQwZDVlNmExNDViNmNkMGFmMTY1ZmI4N2U2MWEzNjhmMDQ1NWEzYzNiY2ZiYTI4NzdhN2M3NzJjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVLY3duL0VTSlJpYjEwZDBVZ0M2Ymc9PSIsInZhbHVlIjoiY3MxM2F3N3pEY2p4dG9vcG56N04xa21KVjdLVnJBV3RFbXh4OFlwekxRUERsTmFPaElDSCtEMWVEa29wM1l1cHpIMHpGQ1F2ZFJ0ZWxiTjYvY0s2WWtjekNDSXJwMS85TkZMNzBxTzBTT2tqMFB5THRwMkJjOTJTVEpOak5uVlFpVWMwY3RpMUtBZnhGNE1KOE1rcjVFUlZ1emlraUlNWUpsZkhYZTd4ZEl1UUtLeitNNStzK0pNZjdIRTBhSEpDR0I2N09lSHlGejJrU1czN2N4bjI3Q3VlZyt2ZEd0RUdUWnh1Vm90UkY5d3VzSHh0WDBhb2twbHJCbVdHb1YwQlhOeHowemI4bkcyOVMzcW1uTUZhVjByRHA4WldHc1FucDNlelhlbVAxZy9NQmR2SjlndHVUVy95SGRKcVNUYW1GV1o3cVhFajA3N1FCWEdJUmh0NVJnZFgzK0I1aWdPMzZGT1oyYWlyQm1BYnd6U1dXSlVobG9NSEdRTXhjeUxlNzI4WWtVeU1lZmJ1cmx0THJlOFVXWE9BTEt1M3dCTGZlWUp3YUFRYktWZDFOcEFrYTZkcUJPQjdLckllbXJBRGJDdDVmV1E0Q0NkYWRrLzF0K1J5K2dISHBnVnBYbTFOZVg2VDZLWDV6Q0FYZXc4WHRzaWRRNEFRTGRxdGJVcjQiLCJtYWMiOiJkNTUzNWJjMzMxNWFiNWI2NWJkODc4ZjFhYTMyZDIxNDQxM2NkOGEwZjdmZDAyZjNjYzQyYWEyOGI5NzA2OGRlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjIrU3RaWmdNV2ZpM1Z3bU5Hc1JZMmc9PSIsInZhbHVlIjoiZ21obmZxcmQvNGtjQjFqNUszdjZjekZNdVJrQytHWWQyYzlqekppR1VweDVid1FoamNLR2g3L0k1Y3VrMThjNFM1aU02TnN6VFE4Y1FWT3ZXV0Z1OGlZY2hJbllZVFhlUzVMZGNRRStJVUIzQjZwNExUalFPeVNzbW8yK1cyclkzeEpvNklRbUlDb01zamY2b3p3WlNaZjJsN2llVmdsa0laenZNanFwbGNXdFR0dmlJd0pzeEcra3JNRkRTMGc0dFAxVHhOUDhKcnQ2bHlPZFdIWGQxOVdXdGdHcWNXdU5DVUUwNG1ZTFJxejV5OFoyVnFvVXllL0YzNnl3dW1ITXRJeUxma0paaXQxRnNIT2RRdTNmcC8rdVBIWnFyMjlBc3lvZ0JrTlFhYjBuK1JLTFZoNnhZa2RJTUhJUTQrN2RPZ1RtSVZZb1VPQU1jeGxjWE5MeXF3SUFub2lOaHgvY1g4bVplYnU0ZytNeWtYMDRsbm1EUnpnWVFCWHNxRHJZVFdybkVnWkxjN0EzL01rK1B0ZG9FU2lVZC8xT2JGUyt2bjBDblNkQ2xjZzVRamF1cEQ1QVcvc1JINFo5SlVWc0MwcVhaRmFkRk14c3BoY09jdENPdjlpZm9mdXg3T0pDT3R5ekhmYzRwMGRqUkhaQmtVTnlheDJoemJ4dDUzUXAiLCJtYWMiOiIwOWU2NDk2ZGQwZDVlNmExNDViNmNkMGFmMTY1ZmI4N2U2MWEzNjhmMDQ1NWEzYzNiY2ZiYTI4NzdhN2M3NzJjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVLY3duL0VTSlJpYjEwZDBVZ0M2Ymc9PSIsInZhbHVlIjoiY3MxM2F3N3pEY2p4dG9vcG56N04xa21KVjdLVnJBV3RFbXh4OFlwekxRUERsTmFPaElDSCtEMWVEa29wM1l1cHpIMHpGQ1F2ZFJ0ZWxiTjYvY0s2WWtjekNDSXJwMS85TkZMNzBxTzBTT2tqMFB5THRwMkJjOTJTVEpOak5uVlFpVWMwY3RpMUtBZnhGNE1KOE1rcjVFUlZ1emlraUlNWUpsZkhYZTd4ZEl1UUtLeitNNStzK0pNZjdIRTBhSEpDR0I2N09lSHlGejJrU1czN2N4bjI3Q3VlZyt2ZEd0RUdUWnh1Vm90UkY5d3VzSHh0WDBhb2twbHJCbVdHb1YwQlhOeHowemI4bkcyOVMzcW1uTUZhVjByRHA4WldHc1FucDNlelhlbVAxZy9NQmR2SjlndHVUVy95SGRKcVNUYW1GV1o3cVhFajA3N1FCWEdJUmh0NVJnZFgzK0I1aWdPMzZGT1oyYWlyQm1BYnd6U1dXSlVobG9NSEdRTXhjeUxlNzI4WWtVeU1lZmJ1cmx0THJlOFVXWE9BTEt1M3dCTGZlWUp3YUFRYktWZDFOcEFrYTZkcUJPQjdLckllbXJBRGJDdDVmV1E0Q0NkYWRrLzF0K1J5K2dISHBnVnBYbTFOZVg2VDZLWDV6Q0FYZXc4WHRzaWRRNEFRTGRxdGJVcjQiLCJtYWMiOiJkNTUzNWJjMzMxNWFiNWI2NWJkODc4ZjFhYTMyZDIxNDQxM2NkOGEwZjdmZDAyZjNjYzQyYWEyOGI5NzA2OGRlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839013293\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}