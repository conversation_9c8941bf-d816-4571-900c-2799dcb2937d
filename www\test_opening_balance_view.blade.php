{{-- ملف اختبار مؤقت: resources/views/pos/financial_record/test-opening-balance.blade.php --}}

<div class="modal-body">
    <div class="alert alert-success">
        <h5>✅ اختبار ناجح - الملفات تعمل بشكل صحيح</h5>
        <p><strong>المستخدم:</strong> {{ Auth::user()->name }}</p>
        <p><strong>المستودع:</strong> {{ Auth::user()->warehouse_id ?? 'غير محدد' }}</p>
        <p><strong>جلسة جديدة:</strong> {{ Auth::user()->is_sale_session_new ? 'نعم' : 'لا' }}</p>
        <p><strong>صلاحية POS:</strong> {{ Auth::user()->can('manage pos') ? 'نعم' : 'لا' }}</p>
        <p><strong>الوقت:</strong> {{ now() }}</p>
    </div>
    
    <div class="alert alert-info">
        <h6>معلومات تشخيصية:</h6>
        <ul>
            <li>Controller: FinancialRecordController موجود</li>
            <li>View: opening-balance.blade.php موجود</li>
            <li>Route: pos.financial.opening.balance يعمل</li>
            <li>Database: حقل is_sale_session_new موجود</li>
        </ul>
    </div>
</div>

<form method="POST" action="{{route('pos.financial.record.opening.balance')}}" accept-charset="UTF-8" class="needs-validation" novalidate>
    @csrf
    <div class="modal-body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="opening_balance" class="form-label">{{__('Opening Balance')}} (رصيد أول المدة)</label>
                    <input class="form-control" 
                           placeholder="{{__('Enter Opening Balance')}}" 
                           name="opening_balance"
                           type="number" 
                           step="0.01" 
                           min="0" 
                           id="opening_balance" 
                           required>
                    <div class="invalid-feedback">
                        يرجى إدخال رصيد أول المدة
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <input type="button" value="{{__('Cancel')}}" class="btn btn-secondary" data-bs-dismiss="modal">
        <input type="submit" value="{{__('Save')}}" class="btn btn-primary">
    </div>
</form>

<script>
// إضافة validation للنموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
