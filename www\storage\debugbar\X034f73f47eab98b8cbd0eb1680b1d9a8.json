{"__meta": {"id": "X034f73f47eab98b8cbd0eb1680b1d9a8", "datetime": "2025-06-06 20:39:00", "utime": 1749242340.067427, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242338.336608, "end": 1749242340.067474, "duration": 1.7308659553527832, "duration_str": "1.73s", "measures": [{"label": "Booting", "start": 1749242338.336608, "relative_start": 0, "end": **********.869999, "relative_end": **********.869999, "duration": 1.533390998840332, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.870025, "relative_start": 1.533416986465454, "end": 1749242340.067479, "relative_end": 5.0067901611328125e-06, "duration": 0.19745397567749023, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44778264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00998, "accumulated_duration_str": "9.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9655619, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 56.513}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749242340.0044599, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 56.513, "width_percent": 12.725}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749242340.017627, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 69.238, "width_percent": 17.735}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749242340.0399668, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.974, "width_percent": 13.026}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2079605862 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2079605862\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1054109338 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1054109338\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-616320323 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616320323\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1788014209 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242325348%7C14%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF4VHpLZ08rTHRSK1ZJemoySGRxa1E9PSIsInZhbHVlIjoib1pFNXVXY2pVMVk5L2Q4R0hCZ1lYcHNlbXFJWjZrV2RWT21MMm1valFHQ29LQjVJYTExS2dHam9rT29SN1Z3UkI1M1BrOVhuRnVNTm41TW1DbFowUlFFR2lhengwMnZWRStZQ2xoUmR2d0didFllSm8rSUtBMDVrUlNib0w4d2M5QVRvRGhzNHhwMDZESG50SEw0YnFGK2VDM0d6ci9pU0NLa2NENjNaVEhJYlN4ZldidTlSa080NnVxY3hucDErQXhwbDVoUGkwL0tmcjN0OVFLZHpiUEJvZkZJSFRoSUJoL0tkNlhGTFkybkZMeHBRdGIvYUNOZ1RaZk1XMmVBSVd0Tk5xSFVra0RBbGZEQTBMZ21xNTVjOGt3VUdnd0Faa0xNcFE1VVg3bnZ6d0hRWFVrcm1waWRZa2RrWUVxUjdlMjZ5QmNLZXd4eEoyOHRUZlhHUHZ6Wkt1TC9BNy9jaXNDR0VBZGUxbkJRZGI4NzNQQ2lXNkIzSElKNjBRNFR2RUFpM2ZGMUJQMFZML0NIV2RVTndDa2ZtbStRdFA5TTRmZUVxZm5YN0ZLL3JGMXhpY1E1VUpMcmR2OUIzN3VSamNVMU5QbjBvU0ppZ2RCSmUrL1RWSURQWmNzUklTbFhGOFpOMHpybmNoL0JOVDd5Z3pscUJsbUtiQUlTclNIVDIiLCJtYWMiOiI1NmZjNDljZmZjNGVhN2ExNzMwYzZlOTFlZmYyNTI1NzQ1YjljZTllZDZlMzM0YTVmZDZiODQ5MmE0MjU0NTA0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1lZVFYVkF4eXhSdG40ZTFsZHlmNXc9PSIsInZhbHVlIjoidWxyMXJtWGtwaHB5UlBvUE45SUN6YXVxSGIyOHUxRjVZZlFpZ0JDbWV1ZDBqZ3p5WlNRZGhDamd2UmErT0F4QW1aTFpzZW1HbENWK01XMFF6dU4vY0tlY3pnc09vai84OXhkQkV4VFkyMWR1OCtpM24xd1lqaTl0dkJZdURJVGRSNHdENkcyZHAyVk4razc2U2l1dXRBRllON2tMWXllTURuOUdDMkxublF5aDB4TWlSQ3Y5aWxkUkdsRDFoMVFSY3hPS2YvNXdDSVhoVVc0aldlN3JSUjh0R1NpLzROWnJNZ1BlWFJ2N0xuNkxhVndmL0V0eU9rcGZUTWUzclgzUHNKc1ZqVmgwamM3YUIraWd1SkJ6WnYyTXFvbTlWRS9qM3pDWXlsVnJmaEZmT0Z2OVNaOTdZWG1ac0Y4cE5UR0JuTG03cyszaGc1aDVUaExDNEVIbkwrb1dhNUhnYzY2cmZxdHRaVDlVeXpkd1dIaStGMkkrWTByOVBxSGt0eURGaHBQM25rRm5yRDRxNnZDMjgyNThBdWhpMXBCb29qWG55RHJkZ0NXYnFodXd1Qjh1RDd0TVlTUmxSU2p6UGFNT2Fnbm1sRDdHdzdPUUdCUk1FdWZCYVk5ZDVWQUErbUJUR1JRMUUvNWRpN0kxWSs3WGNFVFBZVGFOWGwydWdWMjQiLCJtYWMiOiI0NTljZjgxNGQ2OGE5ZmJiMzhhYjE2NTUzMDUzMzNmZThhN2RiMTgzZjkwYjg5MTkwZTQyMGM5ZDAzYTRkNWY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788014209\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-998304769 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998304769\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1417678426 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:39:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhoaEJRVTQyTHJ0bHdjN0Noa0dSS1E9PSIsInZhbHVlIjoiNmh1MkJhUmkrM3RXbXo0OXRySE1ORnNLZHZGVzFBK05QdjVPK1IwSkNKZHJzSjVZT0laN2lZeG9GUHpUdmthaHlsUlA0UitibTdSZXpCekVqaTZ3VHhFdGV5eEtWZytnbU16K3FoNzFhRVhnRW5qS3ozMUhVRzRZUW9KZ3FKNTY3ZXhTWG5aMm1YMnBheThxUnVPTk9vUVNiUHNZcGxrRVU2MnhiRUNBR1dmVCtnaVF0a0lSSVV6L2NxUmZKVE84dGdwb1JPdG5VZGt2KzNDejdUdjJ4TFNldkhVQ3NOdENYSnVOakpIYmVheHNRUEdZNHpBQVJYQTFlWmcvNFdrVFpWSUtsZTFqdmNUT2Y3UUdCbSsyS2NZMHNHUmNGVW85bUE2SWRDOTYyWXNQUUk4NkhWNnBJelo3ZFZ2V09wQm55VSs4TGZxd0pTU3RhRVZqSFF1NXhFU2RzWmFIWEVwci9HeHV1S1dST3FwblQ5UDVoRnkvT2Y4M3JZNUo0ck5PMVU2bS9tYU5Nbmkxb2NLMkZSbHprM0VGd25BM3UrcEdscDRKMHFpTWNsbU9LeU5Bb2x5V2ZHaXViL3B3Z0d4K0U3eCsybWFhM1d5SS9tMG9VSHhNWGFUYkYvcE1QSzNsVXN4WTVWRG1MS3dyejVoS1RqVmhhU1FVVkJmRWpjMXAiLCJtYWMiOiJkMWJjMmYzYWYxM2Q2ZTY0NGM3N2QyMjA3ZWI2YzhlNzg2ZDIxYjlhYzQ2NzEwZWFhNzkzYjI2Nzc0NjM3MDBhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9lV2Q3WHRKMy91YWVTVXBmQVNqRGc9PSIsInZhbHVlIjoiMmFTeUVxblczV3luckF4OUdkcjI0bThMcW14cUowR1FVemV1eVZNVGJqWUh2T1BXcGdDS2k5WW5yY2hTWnYvdUhoYWJnaU1GSFJBZWF0VklLSEw5VktoS2JKWWRHWGJzNzZqZENEa1RlWHhYNXR6UlFOMDFkMGhHZWZMbllQOUpYZkovNUZhenV2ZHlGN1IreC9KWkp4NEoxaG1iMzl2NGw0c0hiZExWeHAzT0F0RjFQR1JkbzIycFFqVFNFMVBHMFFkRlIxWFl3MU5TQ2lwcnZUNWpKVlIzWGt1My83UEFPNld3NmRxR1BCNVNqM3czdGs5ZFc2R3NxaURqelhsdzVleTl1dU9hSzJHdFU4V1Y1V1VMWUsrcC9VSFB1aWhMR2hoWkFGd01ZWkFNRzhzeEFMcWdCbGdvVGpDK0xqZlJFc3BxVzdxWWtGVDRDdW5CcENZeVNOTTgydHU5dkFHR01nZGpPV2lFMzloY3JteHErVDliZVhzQ3A4emZvTDR5c0t0c3dtRmVOajNJK0FKTThRUnBYTVA4ZFFDeVNRQ050L3NyOUtTaWtteU8zR3l4NHhjd3VRcWU1cHpzam5rcWZMZnREZyt3eksxNGN3bkJaTGVsNmdGczU2azVGTHNqQUpyZ0NQQTZKTHZ5dkJhY1ROR3QxYmlNY0p3djNaZ1UiLCJtYWMiOiJmM2YyZWQ4MTMwNzQyODVjYTNjY2QzNzJlMzJiYWY1YjA4MzQ2MWVhNzQ1ZjE0M2QwM2I4OTlkNDRmZmY4ODIzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhoaEJRVTQyTHJ0bHdjN0Noa0dSS1E9PSIsInZhbHVlIjoiNmh1MkJhUmkrM3RXbXo0OXRySE1ORnNLZHZGVzFBK05QdjVPK1IwSkNKZHJzSjVZT0laN2lZeG9GUHpUdmthaHlsUlA0UitibTdSZXpCekVqaTZ3VHhFdGV5eEtWZytnbU16K3FoNzFhRVhnRW5qS3ozMUhVRzRZUW9KZ3FKNTY3ZXhTWG5aMm1YMnBheThxUnVPTk9vUVNiUHNZcGxrRVU2MnhiRUNBR1dmVCtnaVF0a0lSSVV6L2NxUmZKVE84dGdwb1JPdG5VZGt2KzNDejdUdjJ4TFNldkhVQ3NOdENYSnVOakpIYmVheHNRUEdZNHpBQVJYQTFlWmcvNFdrVFpWSUtsZTFqdmNUT2Y3UUdCbSsyS2NZMHNHUmNGVW85bUE2SWRDOTYyWXNQUUk4NkhWNnBJelo3ZFZ2V09wQm55VSs4TGZxd0pTU3RhRVZqSFF1NXhFU2RzWmFIWEVwci9HeHV1S1dST3FwblQ5UDVoRnkvT2Y4M3JZNUo0ck5PMVU2bS9tYU5Nbmkxb2NLMkZSbHprM0VGd25BM3UrcEdscDRKMHFpTWNsbU9LeU5Bb2x5V2ZHaXViL3B3Z0d4K0U3eCsybWFhM1d5SS9tMG9VSHhNWGFUYkYvcE1QSzNsVXN4WTVWRG1MS3dyejVoS1RqVmhhU1FVVkJmRWpjMXAiLCJtYWMiOiJkMWJjMmYzYWYxM2Q2ZTY0NGM3N2QyMjA3ZWI2YzhlNzg2ZDIxYjlhYzQ2NzEwZWFhNzkzYjI2Nzc0NjM3MDBhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9lV2Q3WHRKMy91YWVTVXBmQVNqRGc9PSIsInZhbHVlIjoiMmFTeUVxblczV3luckF4OUdkcjI0bThMcW14cUowR1FVemV1eVZNVGJqWUh2T1BXcGdDS2k5WW5yY2hTWnYvdUhoYWJnaU1GSFJBZWF0VklLSEw5VktoS2JKWWRHWGJzNzZqZENEa1RlWHhYNXR6UlFOMDFkMGhHZWZMbllQOUpYZkovNUZhenV2ZHlGN1IreC9KWkp4NEoxaG1iMzl2NGw0c0hiZExWeHAzT0F0RjFQR1JkbzIycFFqVFNFMVBHMFFkRlIxWFl3MU5TQ2lwcnZUNWpKVlIzWGt1My83UEFPNld3NmRxR1BCNVNqM3czdGs5ZFc2R3NxaURqelhsdzVleTl1dU9hSzJHdFU4V1Y1V1VMWUsrcC9VSFB1aWhMR2hoWkFGd01ZWkFNRzhzeEFMcWdCbGdvVGpDK0xqZlJFc3BxVzdxWWtGVDRDdW5CcENZeVNOTTgydHU5dkFHR01nZGpPV2lFMzloY3JteHErVDliZVhzQ3A4emZvTDR5c0t0c3dtRmVOajNJK0FKTThRUnBYTVA4ZFFDeVNRQ050L3NyOUtTaWtteU8zR3l4NHhjd3VRcWU1cHpzam5rcWZMZnREZyt3eksxNGN3bkJaTGVsNmdGczU2azVGTHNqQUpyZ0NQQTZKTHZ5dkJhY1ROR3QxYmlNY0p3djNaZ1UiLCJtYWMiOiJmM2YyZWQ4MTMwNzQyODVjYTNjY2QzNzJlMzJiYWY1YjA4MzQ2MWVhNzQ1ZjE0M2QwM2I4OTlkNDRmZmY4ODIzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417678426\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-419857475 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419857475\", {\"maxDepth\":0})</script>\n"}}