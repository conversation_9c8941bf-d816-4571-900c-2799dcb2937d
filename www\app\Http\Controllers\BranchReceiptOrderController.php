<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\ReceiptOrder;
use App\Models\User;
use App\Models\warehouse;

class BranchReceiptOrderController extends Controller
{
    /**
     * Display receipt orders for branch operations management
     */
    public function index(Request $request)
    {
        if (Auth::user()->can('manage warehouse')) {
            $user = Auth::user();
            
            // Get warehouses for filter
            $warehouses = warehouse::where('created_by', $user->creatorId())->get();
            
            // Get all receipt orders from the new table
            $receiptOrders = collect(); // إنشاء مجموعة فارغة مؤقتاً

            // إذا كان الجدول موجود، جلب البيانات
            try {
                $query = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator'])
                    ->where('created_by', $user->creatorId());
                
                // Filter by warehouse if specified
                if ($request->has('warehouse_id') && $request->warehouse_id) {
                    $query->where('warehouse_id', $request->warehouse_id);
                }
                
                $receiptOrders = $query->orderBy('created_at', 'desc')
                    ->get()
                    ->map(function ($order) {
                        return [
                            'id' => $order->id,
                            'type' => $order->order_type,
                            'reference_number' => $order->order_number,
                            'vendor_name' => $order->vendor ? $order->vendor->name : ($order->order_type === 'نقل بضاعة' ? 'نقل داخلي' : 'غير محدد'),
                            'warehouse_name' => $order->warehouse->name ?? 'غير محدد',
                            'from_warehouse_name' => $order->fromWarehouse->name ?? null,
                            'creator_name' => $order->creator ? $order->creator->name : 'غير محدد',
                            'total_amount' => $order->total_amount ?? 0,
                            'total_products' => $order->total_products ?? 0,
                            'date' => $order->invoice_date ?? $order->created_at->format('Y-m-d'),
                            'created_at' => $order->created_at,
                            'status' => $order->status ?? 'مكتمل',
                            'status_color' => $order->status_color ?? 'success',
                        ];
                    });
            } catch (\Exception $e) {
                // إذا لم يكن الجدول موجود بعد، إرجاع مجموعة فارغة
                $receiptOrders = collect();
            }

            return view('branch_operations.receipt_orders.index', compact('receiptOrders', 'warehouses'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }
}
