{"__meta": {"id": "Xa570e664f3560d2cd03a6dffede9ecae", "datetime": "2025-06-06 19:27:00", "utime": **********.443274, "method": "GET", "uri": "/roles/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238018.636111, "end": **********.443315, "duration": 1.807204008102417, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1749238018.636111, "relative_start": 0, "end": **********.030823, "relative_end": **********.030823, "duration": 1.394711971282959, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.030856, "relative_start": 1.394744873046875, "end": **********.44332, "relative_end": 5.0067901611328125e-06, "duration": 0.4124641418457031, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53605200, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.create", "param_count": null, "params": [], "start": **********.369226, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/role/create.blade.phprole.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Frole%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.create"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.400962, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.create", "controller": "App\\Http\\Controllers\\RoleController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=31\" onclick=\"\">app/Http/Controllers/RoleController.php:31-57</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.019010000000000003, "accumulated_duration_str": "19.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.132647, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 26.355}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1687322, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 26.355, "width_percent": 5.208}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.178558, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 31.562, "width_percent": 7.943}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.249198, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 39.506, "width_percent": 8.995}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.256609, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 48.501, "width_percent": 6.839}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\RoleController.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2734902, "duration": 0.00714, "duration_str": "7.14ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:45", "source": "app/Http/Controllers/RoleController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=45", "ajax": false, "filename": "RoleController.php", "line": "45"}, "connection": "ty", "start_percent": 55.339, "width_percent": 37.559}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.create", "file": "C:\\laragon\\www\\to\\resources\\views/role/create.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.380224, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 92.898, "width_percent": 7.102}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 510, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 513, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-111479236 data-indent-pad=\"  \"><span class=sf-dump-note>create role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111479236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271566, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/create", "status_code": "<pre class=sf-dump id=sf-dump-1006440957 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1006440957\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-458371963 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-458371963\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1287218315 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287218315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238015655%7C29%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktjR1JUNm1vcnN5bXJ2ekF1VXV5ZlE9PSIsInZhbHVlIjoiNkJwWW8yK1NNQ1B6OHlzRFRocHdiZ2ozL2lyM1g4aFIrazhiSkZOT0tFdm0wSHg5M3ZYUWNTbnlUSUE0MVNia1pSZ3RjaUJBa25uZUFIenVhY0ZVclI4QS9wdnNUM1BjWWwvZnRyaDZIektmL0JFb3BhczVIN3ZrU2pCSnBkVGY3MktXVU1zZ1FHSmVldlc0bU5OSXByQ21yanBvQnZ6ZGhYUytMNklPUU5zaXBFdDBGaXlMOExtYjEvd0xFaE5ib2RxUGVoNHdLeUZWRkp1OWd2U0tieE9oMDUzMUpGbXJwRmxvU2hxd0s1YjJWZGFpNWgySEVscWFLRmg0V0R2bERneXZ3eVhGRlJvanR2WU5keTk4MkdtQTFnVXV0U1MrbDR4NnllWGQwMjlIMVB5QUNmWVY2WWlpWFpSeWVqeEFuMkxoNzhhUFVWQ1dVODZxaGZoYk9CT1pTQ0R6eTdrUElTZ0k3SHE5WnQ3UlplZ1lkN0tjQ2xFelFMd0R6YmozKzBpVUllU3p3bVFBaUpjZnZNblBpUFQ0UTI3MUkxdG9memV6ZHNaTWJIMDhXL00vellnRmZkTWdoQmlGdXZGVGU1TXkxV3pucmMxZWRzdWcyaDIyWTNyY29DakwyT3lNZCtuU0xFcW5QY1QrUm5HOVo2NHFOTkJOOUswZklieHoiLCJtYWMiOiIwNjM4YmRlNzdlMzljZWU5ZWNkZDYyZGNjNmM4MWU5NmMzM2FmZGRlMTIyZmI1MTMwMGE5MmRjZWFkZmNlMTY2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im9FbFAyVy9tN3B2QU9wZEg0U3lCNkE9PSIsInZhbHVlIjoiMXY4dFl2ZmtlNDJuWTArVlBWOGFlblRSTTk1UWZ0RkRyNnkyTmpYejRRZjdqS1FpTWJpMVJHWlhQNE5ORXl0NUxUWWVkeE82aUZPRXR1dU8vcUhmT0xvSTQvOVg1WjhjdS9SNUdWWGNaTHZRa3VDOUo2RjB6N0FYQ2pReEtZYSsybXhwZFFjQ2grQjdTbjdHK2R4c2J1UzlkaVlka1pLQXVNdEdwcFBiMkZsOGZpbTYvaGJFZlZXSTZjYktwMkREUEdwUkVBckZxV3h4OVZtSkRrcnVoa1BEcWxYSVlLck8vbmZ0d1FmeTg3S2huN3RQb3Q1djErNVZaUXhiaStRb1FQOG9HcTY3dWVKM2JnWGJEWFkvY2NnTmRYUkMwMFh3djFHbUp2M0wvKzhna3pkMEtob3YzOXRvN0dOMTFPeEdtM3VGVEd3ME5JN0g3SmpkTEgwNlRYcVA1NVIvZXhKS0hiNDh6WlFnNjB6VXJnZ1U2ell3UVAyc01MUUdIMFR2OHZESVRSUmZkZ3J2Tkx5QXJ2alRyNHNWSmxCV0lLdmtyeUpvN04ydkVTQUVFaFl2c2FnTGRkaTd2cW0wQmluMnpOSEQxRFFSTE5sRW5HLzQvcTljOHVlSDdocjhxTFhCS2U3VnpZZnc3Y1Q2R1V5NytabFNFTlpvSTFudXkza2QiLCJtYWMiOiI3ZWM2NTg5YjExNGMzNzYyNDI3YjBkYTMxNmFjZjUyYjhmZjM0M2I0ZTY0NjU4MWI1YzI1YzRmYTk1ODEwNDk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1350092392 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350092392\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-887133792 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:27:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkY4bUdJT3UydkQ3NXo2NmlaY0RaT3c9PSIsInZhbHVlIjoiK3krSWRBZEtNbS9HRDlZMmQ1RFFSWFhoaWVxK0RIYndNSUlzeld4ZUI1cXBSNnNQeXZGbUxVWGZxcHdZK1JRamtIK2dBM24zeUpkZzVRM3JMd1d0MWkwKzdzNkRNYXVKYzYwcnM1WjVFa1hKRDlXNys4dVA0UldycklVKzJpRVF2eHNRajIrVndXSXppUUFQazBBUGdmZ3UxWTM1QVkxWGtPdEJ3QkdEUG05Z1RUdGY1WEErcXFVRnJ6TVYyUnlubmFYcHRaZHdVNnhLQnNNY2ZhRlFmOHoxdEtIaC9VV2orTjdSaHdTOStKNVZKRjJVSHY3NGh4VXJvZTlJVEZWK1VJZE5zcjQzaWd2UXdPZVpCNFIybHRBM1NKKzZtS2FjRTFkWHBaTllUYWxxWm9HeTZZZllhSnRZU2d1cVhCWFZud0hDblBsVk1pcmhpMFJZbkZZTFpDQnppTWxEa3lOWXo1NkZGTmFFSWlOL1poYXVoclN1Mld5QXBobm9OdzFlQTlXOHRMc29QSHhhSHhrbXNhQWZGV1FHN0NQUkdSLzRKQTJFdVpEdHh0dmZpZkl0ZTBPOWNlQVBucXFzNk91b20yZ2IwcjJMRHhVWjhDekdJL1ptaHErOFZCZEtOczVjSGFxQ1pQRlJYQ1pUZlM2N0p5T0lUYUZiOW1HQ2YrbUoiLCJtYWMiOiI1MTdmMjE2MzA3MzVmNDc5NGNhNDQ1NDMwODM1MWFkNzQ2MmYzZTdiZDg4NWYyZGU2MDY3MWM1MjMwMzQwYzU0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:27:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InY5MTJxR0NIeEw5ejFNZGg0RVh1dXc9PSIsInZhbHVlIjoibkZleDVSdmg0Mm4vOU9ObDAxSW1NUm9aeWdrNUpXaUw2ZUk5UC9HRGIwU3Z3NU45Tk16S2Y5MHc2czUySkpNdy9mTTF1V1NoZU8yWUpCa01Xc2tPMkhMUk9tVVVkNXZmajV6S0l2ZEFIdXZRL2FzSmVZZ3JvY0d1WFBaN2lBYnl4eFc3NWZuNzMwMyt0OU9MWWhIbVpBcEk5SGRRUDZDQk1UM0RIQWJVNGVyOXREbkVuSFg5UHVNMGVSU1NiMi9NeWZYa2lML2dUck94WTVEaUtOUnZ0dDc5c2M4clFhQVZ4bkt3Zm13Uzh6RnhvVGQxa0NNcEFuNXpyZkFHL2FtY0RXUGJxamVibE40b2Fia0ZCVkJqVVY5RktZZEdTcWhsdlV2aGdIbjFpcDRyb1lNK2pMdlRsV0RZTm5LL05pTUIyWVB1OVlsa3ptVDBSMGNmVmlvVzZXTS9pNmVPS2ZUSERqbVhmbEVNZ3hQWk40Z1NmTEZ1SUJJaC9tckFiVWlDd3RJd0FuTmpOaVpvRDFGLzZjQ0s5TldWT1pvdzEyQzVSd0JHa3h3dWs1M0tMYjN3bG81UU1XSStaQVowaVZuYkpwM00zSFVaVDU4NUQ5cEJveVJWM3Z6Zkw3STdCSllLVkw0SWpYOFlrOFZtSjZJR2xsNmlSQ2VYYkpTQy96SjMiLCJtYWMiOiJlMmRkY2EyYzRlM2Q3YTI1MzQ4MzE5NTZjNzVlODU5OGMwNTlkNDE3MTU0MGRmYTExNzdmMDYwYzBkNTEzZDc4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:27:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkY4bUdJT3UydkQ3NXo2NmlaY0RaT3c9PSIsInZhbHVlIjoiK3krSWRBZEtNbS9HRDlZMmQ1RFFSWFhoaWVxK0RIYndNSUlzeld4ZUI1cXBSNnNQeXZGbUxVWGZxcHdZK1JRamtIK2dBM24zeUpkZzVRM3JMd1d0MWkwKzdzNkRNYXVKYzYwcnM1WjVFa1hKRDlXNys4dVA0UldycklVKzJpRVF2eHNRajIrVndXSXppUUFQazBBUGdmZ3UxWTM1QVkxWGtPdEJ3QkdEUG05Z1RUdGY1WEErcXFVRnJ6TVYyUnlubmFYcHRaZHdVNnhLQnNNY2ZhRlFmOHoxdEtIaC9VV2orTjdSaHdTOStKNVZKRjJVSHY3NGh4VXJvZTlJVEZWK1VJZE5zcjQzaWd2UXdPZVpCNFIybHRBM1NKKzZtS2FjRTFkWHBaTllUYWxxWm9HeTZZZllhSnRZU2d1cVhCWFZud0hDblBsVk1pcmhpMFJZbkZZTFpDQnppTWxEa3lOWXo1NkZGTmFFSWlOL1poYXVoclN1Mld5QXBobm9OdzFlQTlXOHRMc29QSHhhSHhrbXNhQWZGV1FHN0NQUkdSLzRKQTJFdVpEdHh0dmZpZkl0ZTBPOWNlQVBucXFzNk91b20yZ2IwcjJMRHhVWjhDekdJL1ptaHErOFZCZEtOczVjSGFxQ1pQRlJYQ1pUZlM2N0p5T0lUYUZiOW1HQ2YrbUoiLCJtYWMiOiI1MTdmMjE2MzA3MzVmNDc5NGNhNDQ1NDMwODM1MWFkNzQ2MmYzZTdiZDg4NWYyZGU2MDY3MWM1MjMwMzQwYzU0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:27:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InY5MTJxR0NIeEw5ejFNZGg0RVh1dXc9PSIsInZhbHVlIjoibkZleDVSdmg0Mm4vOU9ObDAxSW1NUm9aeWdrNUpXaUw2ZUk5UC9HRGIwU3Z3NU45Tk16S2Y5MHc2czUySkpNdy9mTTF1V1NoZU8yWUpCa01Xc2tPMkhMUk9tVVVkNXZmajV6S0l2ZEFIdXZRL2FzSmVZZ3JvY0d1WFBaN2lBYnl4eFc3NWZuNzMwMyt0OU9MWWhIbVpBcEk5SGRRUDZDQk1UM0RIQWJVNGVyOXREbkVuSFg5UHVNMGVSU1NiMi9NeWZYa2lML2dUck94WTVEaUtOUnZ0dDc5c2M4clFhQVZ4bkt3Zm13Uzh6RnhvVGQxa0NNcEFuNXpyZkFHL2FtY0RXUGJxamVibE40b2Fia0ZCVkJqVVY5RktZZEdTcWhsdlV2aGdIbjFpcDRyb1lNK2pMdlRsV0RZTm5LL05pTUIyWVB1OVlsa3ptVDBSMGNmVmlvVzZXTS9pNmVPS2ZUSERqbVhmbEVNZ3hQWk40Z1NmTEZ1SUJJaC9tckFiVWlDd3RJd0FuTmpOaVpvRDFGLzZjQ0s5TldWT1pvdzEyQzVSd0JHa3h3dWs1M0tMYjN3bG81UU1XSStaQVowaVZuYkpwM00zSFVaVDU4NUQ5cEJveVJWM3Z6Zkw3STdCSllLVkw0SWpYOFlrOFZtSjZJR2xsNmlSQ2VYYkpTQy96SjMiLCJtYWMiOiJlMmRkY2EyYzRlM2Q3YTI1MzQ4MzE5NTZjNzVlODU5OGMwNTlkNDE3MTU0MGRmYTExNzdmMDYwYzBkNTEzZDc4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:27:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887133792\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1339896010 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339896010\", {\"maxDepth\":0})</script>\n"}}