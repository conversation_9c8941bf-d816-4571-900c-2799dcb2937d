{"__meta": {"id": "X23423bd2d334f4bd14a47ddeaa815093", "datetime": "2025-06-06 19:25:38", "utime": **********.459862, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.82531, "end": **********.459892, "duration": 1.****************, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": **********.82531, "relative_start": 0, "end": **********.219134, "relative_end": **********.219134, "duration": 1.****************, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.219155, "relative_start": 1.****************, "end": **********.459895, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "241ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026580000000000003, "accumulated_duration_str": "26.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.329584, "duration": 0.02355, "duration_str": "23.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.6}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.386555, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.6, "width_percent": 4.138}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.431492, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.739, "width_percent": 7.261}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C**********164%7C24%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImYvc0pmRjFSMGorTXZ5L0poMC96MkE9PSIsInZhbHVlIjoiZWFtSURMRDI1RXJnVGpVLzJOdVhMeW03QUx2U1E4MVd1NXZPKys3VU5Ubi9rUklkSGtaQTZuc3dlN1p4QkE3T0pBbEJqc2R4VGFDK2hHTFc2aGNjb1BqTTAzdXRXZ2hOaFBQMEhrSlVHUXdQSWYzWDZlV2VPb0dzQ0FPUStoaDVKQXZ2ZWEyZkI4VzdxYlpaSW9IUXk3UnQ0SWc5KzJIRitGVHl3RGJ1UFB4YkJrZklHS2pIeGthK0JFTzcySUhXK3YzMlJtWFNsWTQ4dnlwcHZzMkpRMm82b21JSlpuejVaZ2Y4VmNFVXVTUDFTdUh0V1BleHlaVHF4eDNkRGp5cjdId3NEUk84ZDk5dXNzN1NMb0ZUdW9DRlpJdWdMeW1qSERQbFlQano5Z1crMU1qa01HMXd0NEpGZEJFcjIwMDJ2cnZQcnNUL1FJQm9UbVdHRUx1cXJTeWd5Z0Q1d2sxSkhpTDc0TnVXV1pObDFnblI4WUZmT1pBK0pzNDFGczh6MDhub2E4QU0wb2ZDQVg1VU1KQ0diTkcySWljQkdxL3R3YkVROG9LMFZVTjE3ZVpZYW1wRFgwTzFNbjA2VHY4R1l6UEVPRU9lVmlRUVh5eW1yS0MxMUJkZTUzYmFkYmxJV3FzNjZTOXpSNzJEZVBXc2IyWk9jaERMYnpqbWp2UHIiLCJtYWMiOiI4ZGM1MTYwYmI0YjJhYTA4ZGJlNmY5YzYzNTQzYmU5OTRiZjc0OTk2M2ZhZDkzYzdmNzZhYzA4ZDU3ZTlmNmQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJvYThmNkdzUjdPUkJ5eXRYMDFDUVE9PSIsInZhbHVlIjoiQ0VwVUplNVBNWmUvcVA5cXV4eUFWckluM1NQTzlMcVlpMWl6c1NaTGZjS2haWE9rSmxkcXJqRmw1LzVGSDVlTk9FUDh2cSt6clhNdFBKZC9iRDJaRm9rNmJlVjNsTnlPOXpyN0p6VnVJS21JQjRDWkJQOFRDSVcxM3psa0dsK1p4T2tXYXZkTGpmakdRVWVPTVVsRXMwd25GaVlBTDNGL28yOTVncnE4UklVN0JmN3laUisvYVpTT0Z2TW96bWk3WSthOXk0OFNhL2JwcVNZbnFhU3M0b0tpU0NkV3U4UElnY0xhRnhBLzQwYnI5L01BMTdpZzZTQlk4alpncGF4alZ5TGMwbEZ5SlhLdU1aNGNKS0ZWdDQ0YkRWejJKSGtSa0xuLzIrZ3BaNXRtTFd0YnA4QjhvWE42dDV4ODFCc1NhR3ZJUWNoaEJiTkRWOEgvcFk5VEloTUFJL3UvcVVVQnM3OVJYMFFaL1hGK0RiYjVjSXZiR2htLzBVTHlsclNnd0xEUWNFQXY4UkNvT1c3WHc0OGxuTmtkZzZXQ2Y4RTYxTnpHUkZWQ280cERpWW1aS3ZaZjk3TndiSUFXUysvTm9Ld3lYSmJOK1lMTzY4RitIZUowM1BoU2h0bC9PTzVsUTlabjV5b0VPenRRMmxwUXFSL1NjTUppZ25sQUFPRGsiLCJtYWMiOiI1MTNkNWZhYmEzNTA0NDYzZDQ5MDc3MTQ1MTZjMmI3MWJiZTg4ZTYxM2Q4NDAxOTdjMmYwNTgwNmQwNTdmZGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-726936606 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726936606\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-955794694 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:25:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJ3N2Nvek1WdDc3dHYvTEUvbU1BS3c9PSIsInZhbHVlIjoiM1hybXhJVWI5UHpKcTFPYlJyU2RzRzJacjBZTjRHT2tQMjdHVnZZSmZBeTRIWGpacS9sNFRzQUNvclBXSkdXT1RrZ0lmZHRnYW1McjY2QjdBbldoWGZXTjNyZUdQZHJWYmtsWWhvQTZKUCtVYnp5Y1FTWVhtMlY2RVBMZWEvekh0K2pCaXRkYVIyeHJ2VHpxbVhHYnVVa2NXQzliM2xvdTVhTktKS1FuZ256OThmQ08rWmtlQmttN1RGd0RnN3FKbVU3UFVFc3ZYdTA4VWZ5SjRHSDE3eW5Gby9aUzhwSVNMdC9SaHlnTnRaV3ZMck05dWU1WGJZZDdXNWlXTXorZnBuMTRRbk5pZlIxcEQxcUZVRDNLSkJ4djFQcExwM2ZDTy9nNndNSVpHbmU4dGVOZnZkZlJ3bXU5ajhucjJKREttZUxGWnd1N0NiVStsTElSUG9lVW9DL2JWSjdvS094NHkrajFHTmhQSmdQRi9FeU9pbnFpMjFxSjJlaGVob0grMmVCUlU0anhramZ0UDVBNzdaeVlaRlZoOVNFak94U2RKOXh6M29QVHpGY1Q0cnViWTFBYmZJVFcvR2tvd2pGeEUwNUdxWkMzNFBRMlc2TFk4TkhsdlIyV054NGVVckhwYVc2R3NYNktSeTdJSW03Mmc1dERUNThVOWJEMytWaGEiLCJtYWMiOiJjOWFmMTdhZWQ1YjZiMThiYWNjODY3YTM0MjEwODc0Yjk0MTgyZjdiZGI2OTNkNTI2ZDUxMTJmYzJhOTRhYTI3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:25:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpQdDdaT1BFRFBRRDI3NFFWSE12Vmc9PSIsInZhbHVlIjoiMFhWYlNlcElkcG5mNWhmZWtpNC9pblFJclh1Qzk0WjE3Z1FpU1B3RzdDalV2NEhXZG1xMkFEb2w2b2EwUE5zUzk4cGFjNm5zVzQxYk5CYlFNakVFalRUd0hwZ2ZWd0FDOXhNU1BPaGxGMFBad01kbVc3RWczYmpoSDVEdHBIZG0vVEliWGZmR2FUQ3d2QUF2NkkzMi9GWFpkVm5zQWw2OFJNR0ZNTTM5cGEwbW1FcGRuQW9zNUNNaG55eXE1d0k0ZHZmdDBnWmRXRjBTSENLbEtKMXM5VXBSUnN1TTRMSGFSdHk1SmgvaTlEK2NoR3FqeEUySHZ4ZnUwVDlrNWZXUDZUL3NBTzVBcGpuZEVOUWVLYVR0MHV2STNOZGRmOWo2Vk1yM0VRT3JWK1JncHBIekdiZGlEVW5RcTVwSXpUcDBoZDd0NXBnQ2Qrb0xVc0Uwc3F3cDFUT3F5RVM4TktneWN0dHI5SWxLdjBBREF4MTdRZkI3SERtMm5PZWN2STEzSmM2OHFOeS9XYWlCcElHWnRlL2VGM0M2OHIzeXZLVjh0T3dvVUVFL3hpK0FsWERrTFRLMVgxcEFwbDJ6UVcrSTlmbUNXQzVyZllwU2tOK2YvTk9tclJJYnJyRTB2aUhwTjBsRXdONngwUm9OVStWeTdYS1RCNWZxS2g4Q2JKSTciLCJtYWMiOiIyMDFlNGNlNGViNGVmNTdiYWY2YTA0ZmRmY2U0Mzk4YjVhYWQ1ODllYzlmMzQwYjIzZjEzMzIzNDA0ZDdiMjU2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:25:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJ3N2Nvek1WdDc3dHYvTEUvbU1BS3c9PSIsInZhbHVlIjoiM1hybXhJVWI5UHpKcTFPYlJyU2RzRzJacjBZTjRHT2tQMjdHVnZZSmZBeTRIWGpacS9sNFRzQUNvclBXSkdXT1RrZ0lmZHRnYW1McjY2QjdBbldoWGZXTjNyZUdQZHJWYmtsWWhvQTZKUCtVYnp5Y1FTWVhtMlY2RVBMZWEvekh0K2pCaXRkYVIyeHJ2VHpxbVhHYnVVa2NXQzliM2xvdTVhTktKS1FuZ256OThmQ08rWmtlQmttN1RGd0RnN3FKbVU3UFVFc3ZYdTA4VWZ5SjRHSDE3eW5Gby9aUzhwSVNMdC9SaHlnTnRaV3ZMck05dWU1WGJZZDdXNWlXTXorZnBuMTRRbk5pZlIxcEQxcUZVRDNLSkJ4djFQcExwM2ZDTy9nNndNSVpHbmU4dGVOZnZkZlJ3bXU5ajhucjJKREttZUxGWnd1N0NiVStsTElSUG9lVW9DL2JWSjdvS094NHkrajFHTmhQSmdQRi9FeU9pbnFpMjFxSjJlaGVob0grMmVCUlU0anhramZ0UDVBNzdaeVlaRlZoOVNFak94U2RKOXh6M29QVHpGY1Q0cnViWTFBYmZJVFcvR2tvd2pGeEUwNUdxWkMzNFBRMlc2TFk4TkhsdlIyV054NGVVckhwYVc2R3NYNktSeTdJSW03Mmc1dERUNThVOWJEMytWaGEiLCJtYWMiOiJjOWFmMTdhZWQ1YjZiMThiYWNjODY3YTM0MjEwODc0Yjk0MTgyZjdiZGI2OTNkNTI2ZDUxMTJmYzJhOTRhYTI3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:25:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpQdDdaT1BFRFBRRDI3NFFWSE12Vmc9PSIsInZhbHVlIjoiMFhWYlNlcElkcG5mNWhmZWtpNC9pblFJclh1Qzk0WjE3Z1FpU1B3RzdDalV2NEhXZG1xMkFEb2w2b2EwUE5zUzk4cGFjNm5zVzQxYk5CYlFNakVFalRUd0hwZ2ZWd0FDOXhNU1BPaGxGMFBad01kbVc3RWczYmpoSDVEdHBIZG0vVEliWGZmR2FUQ3d2QUF2NkkzMi9GWFpkVm5zQWw2OFJNR0ZNTTM5cGEwbW1FcGRuQW9zNUNNaG55eXE1d0k0ZHZmdDBnWmRXRjBTSENLbEtKMXM5VXBSUnN1TTRMSGFSdHk1SmgvaTlEK2NoR3FqeEUySHZ4ZnUwVDlrNWZXUDZUL3NBTzVBcGpuZEVOUWVLYVR0MHV2STNOZGRmOWo2Vk1yM0VRT3JWK1JncHBIekdiZGlEVW5RcTVwSXpUcDBoZDd0NXBnQ2Qrb0xVc0Uwc3F3cDFUT3F5RVM4TktneWN0dHI5SWxLdjBBREF4MTdRZkI3SERtMm5PZWN2STEzSmM2OHFOeS9XYWlCcElHWnRlL2VGM0M2OHIzeXZLVjh0T3dvVUVFL3hpK0FsWERrTFRLMVgxcEFwbDJ6UVcrSTlmbUNXQzVyZllwU2tOK2YvTk9tclJJYnJyRTB2aUhwTjBsRXdONngwUm9OVStWeTdYS1RCNWZxS2g4Q2JKSTciLCJtYWMiOiIyMDFlNGNlNGViNGVmNTdiYWY2YTA0ZmRmY2U0Mzk4YjVhYWQ1ODllYzlmMzQwYjIzZjEzMzIzNDA0ZDdiMjU2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:25:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955794694\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1796008128 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796008128\", {\"maxDepth\":0})</script>\n"}}