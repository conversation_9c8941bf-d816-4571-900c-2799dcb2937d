{"__meta": {"id": "X3ba7a18eac72c61052fe3bd41baf24ed", "datetime": "2025-06-06 20:41:42", "utime": **********.983956, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242501.391622, "end": **********.983992, "duration": 1.5923700332641602, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1749242501.391622, "relative_start": 0, "end": **********.772595, "relative_end": **********.772595, "duration": 1.3809728622436523, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772614, "relative_start": 1.3809919357299805, "end": **********.983996, "relative_end": 3.814697265625e-06, "duration": 0.2113819122314453, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761376, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02562, "accumulated_duration_str": "25.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.871703, "duration": 0.02164, "duration_str": "21.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.465}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.924438, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.465, "width_percent": 4.528}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.935887, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 88.993, "width_percent": 4.567}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9554958, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.56, "width_percent": 6.44}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile?15=\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-55461160 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-55461160\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-885994294 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-885994294\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2091926906 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091926906\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-648914653 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/profile?15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242484597%7C23%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV1cVFNVUo4RG9JT0ZzU2xKNkZKZEE9PSIsInZhbHVlIjoiK0FTVXZrdkUxR2RaMFB1R3dPVGJoQkE2Vkw5KzBJQzdOcTdyVVJRMGlkYVN5a3VHanNLUnE1cS9YQlhmeGxDRmdGNlFGOUVUc3JJYjQrR2FsWmR4VmtEOWJSWHBNU2hNN1gyQ0pXWlVHZmFCbmIwcVA3N2xPVnVwdGFIQi9STmoyeHA3Z1VXMER0T2FJMTJ4NGVQdU9oeUFDU1NvSG52MGZmQVFwVHo1NStEejRtd1p3bWZsdGx2azRYMWdCY09FakN0OHBaSEdFK2tRZ2hJWjZETTJIaXJBUlF1djB5UDVoQ2hLcmJhUUN5RC9TSFloTkxmRkhNY29nMGxBUzBpZHFIdkFhZUNTbUp2UUoza296MVJ2aXJaZm1ncy9XLzZiTG1tb1g2ZjBQczBOU3NrUUxjSUQ5a0VFZEFwS2orNUZidk1XaVl2djY4cy9GeDNVcHRvblVQV0xwMSs3bWYvU2pXOWxZR1h1Z0JhektGVWVYRkVMdXFmN282dmsvYUMvTStmWCtYS29qTVJoYW02VHhCdEtpZE5zbk0zY05WWFpiejAxUFNHd0gxbjBKckhXNnl5anZQdUNORkZKbHRBUENmUmdveWdJb3AxUkJOam15SXhKQWlCWEVXN3pRZmc1Y2dkK2o2QXV3NDlOL2JoR3JPblZGaGxML3dPdnRoczgiLCJtYWMiOiIzYmU4NDVhOTVjNDhkZGM0NmQwODJhYzdhZWI0NDFiZGQ0YTE2ZTNiYmRiN2IxYTY1YThhZGQwMGQxNmE0ZTMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5wV1dJeVAwdjlBZE1ZNjN1VDAxdWc9PSIsInZhbHVlIjoieFowaWo2K1hIMFZJUVhCNjZYUmE3WmxpZ3pkakNTdXNjZjBiTG5wYm0yVEhvQWRIcHJxVFNGZ1pyM0pPeVg2M200bVVBSGQzS2l3dHlaemQyMHJEeUp1WFNHK2RGVElsRmdZT3A3OFBHVFRtcEIxUGFJdTA3NFd3OWh2RGdyVjZjSXRBeUVBdGhya09qcXhHemZsMG5BRktWWS81T3dEbjVRVFFBT1ZxdkxLek5hc3JYTWtab1VOOTZmKytpZWhKQzhFMm80cC9MVlJ3VUszL2F0M045SFEwWVJ6VFV3a2RtR3B0dFM0eUY4NEFjME5DNFZHWDg4enZNSXU4bGp2OEJVc2FGTVpUT2RucVEzN2twSzN3NkQ2bkE3aktUTDhyS2ZEZjdZWFBHcTF5M1ZEY2tHRldyRlplcmg3dWdvSkIyUnJyRkIrSFlvbW0ySElGMVA2aENoeHRhdlJyNzlXK1B5bUEvb0tZS3dsQUZrZXBWeEo1ZGtuL0VJRS9Yb25oMUsvenBBQlUzMG9MbTcrTGpkdEtFSDE1UE0yVGIxK3NkT3JiTWc1bndPbnJLZmJDNVQ3VmNmektHTHl4NUlqSG1Hek5uNmFsUlJEOTY1TkkydmdrR2RSeHpCbDhld042M3o1dGRDREdkeE0xL0FDYk1iNnFldGpTQ3pFeXczVHkiLCJtYWMiOiJlYjUzZmY4NDBmYjgzZGNkYWRiNmU3NmFhY2E5ZDNiOWExNzczOWE5YTg1M2QxYzE3NDE3ODRmY2Q0OTA3Y2U0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648914653\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-274472506 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6Wt4M4bI8NbCUmFc1THCmhEuEOoDYpn2bPL8rDvY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274472506\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-867069783 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:41:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImgybGxjMytWelpyVDJSK1ZIVmlYdlE9PSIsInZhbHVlIjoiSGNDZlcrbTgrQ1BvcXVBRkdERFA4NDVyK1Fld0RtSHBnWjYxR2hsZkpTeUNiM2tVNlJ3bGI4eW8xS0F2T2lnUE1OcjJ2NnJTTGN0bnFFbUZzMVpZZE8yZkxTbWpzc1BlZnNJaytGK254Rkl3QlNnd0o1MUlBTTFyU2lmdDhJWG9NcUphWnBqT3dGOW5VWTd5bDVUSWNpSGxXTk9FeUoxUEJ5WjhxMWpaVjluSnhHZW5NdEY3WTNOVFRWbWRwNDE4anJ0YThIbjZPSkZHSDg3UmkyZWpYaENEbkRLVzRhV3U5UEUrMzZuNUlCcVJRUm90Q2JtdkEyMXhmdThFdXVnTmFrMFJwUFJJNjArSnNicmVOMU1STnN1Smsxc1BGV0lEd0NMVlJKSmRoZXVydnJIdGE2bkNTMk5RY0ZUWlpNMjFVcTdrNFZkWGE3VkFraGRyV2hkOFQvYUtnVVpJZFM0Q3paUnY0d2UzemYrelUxcjgzYkc0K29yUXdWYTd2Wk5yNDI0Vm9hTnNGN0NCVjVLckN0MEljOUt0alhvdmJzbVNTMFhmMjVNZ05zd0xBdk10Tk51cE5nMUZ2a05IUXFMcExoSGg4WDh5SldsdXkzRkMwK1hWRHh4UXVnSVdGY29TNXFDNExOUFFmNjBFaEVXY3F3cGxUYlZtL1dZem1QeVMiLCJtYWMiOiIxNDVjZDYxOTllODJkOGIzNjA2YjY5NmI4ODA3NjQ1ZmFmMjgxYzZiY2NhODUzMjU0OTA1NGZkY2YzYjQxOGY3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjlwbEQvRVNJTDJmODVmb0lTcUl2emc9PSIsInZhbHVlIjoiVHJZTzlrbFJkMFlvNmpNc0puV2xsTlRuOHJxdlJxdW50Q2lrY1k2V00rZWlrL1lNaVFWZDVycEpwTU9TaStHQzIwV1BBaHpkZ1h5RTYzR0o5U1lrcmxsdkJGSEY4S1NycjZxdmtQQmlZclFEVVJSTlpXZW5TUUM1cS9xZ3oxYU1Ca042Vi9HaEgrUVN5cmE2eHhtdzVuRHREdWw5Z1loK0tPMU1ydFFLQW13Zk1NdUl3MStOU0NoVkY0OTgzQzkxeFA1VmNFSEZZNlliRWNaejNwbXM1VCtIUWJ4dm9tZGR1QWJTYVNvTkV0NFhhM2FsQ0xVV0RYMU1MREZvcExWcG5SV05mQm1nWjhoK1BoNG1ORyttdmhSR01tVEYxbFNXQkRXQTFuRFE0QVZKRkhlZWVFRkgyTllTU2hYYSs2NkZLSFBoTDZBVjgzQ2N3RzBBdEdyL3o5dU9Tdk5qaE1LbG1Cc3V0eUxSZEFTTlNreG40aUtFYjR5Q1djZTRkekxUVm4rb1Uyc0JyaFlKSCt1OVo3SnZ0ZldGUFdIWVB2MWdvdVRTZHlIUWpQZk1GYTZUaEQrelhCYm9JVzJWV3FiWHNIcm9NWURPbFk4ZlErTXd0MkovSEFvRTFKR2lHSFBVQVZBU1BEQTZ1MHE4NnRBbU11cUxtZUtqWlNsWHlKeDYiLCJtYWMiOiI2OTY5NzhhNWFjZmMyNWU5MTljODc4ZGIzOTE0OTcwZjJlMDk1ZTc2Yzk2MmFhNGFlNGIyMTZkMzA5NDNlZWI5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImgybGxjMytWelpyVDJSK1ZIVmlYdlE9PSIsInZhbHVlIjoiSGNDZlcrbTgrQ1BvcXVBRkdERFA4NDVyK1Fld0RtSHBnWjYxR2hsZkpTeUNiM2tVNlJ3bGI4eW8xS0F2T2lnUE1OcjJ2NnJTTGN0bnFFbUZzMVpZZE8yZkxTbWpzc1BlZnNJaytGK254Rkl3QlNnd0o1MUlBTTFyU2lmdDhJWG9NcUphWnBqT3dGOW5VWTd5bDVUSWNpSGxXTk9FeUoxUEJ5WjhxMWpaVjluSnhHZW5NdEY3WTNOVFRWbWRwNDE4anJ0YThIbjZPSkZHSDg3UmkyZWpYaENEbkRLVzRhV3U5UEUrMzZuNUlCcVJRUm90Q2JtdkEyMXhmdThFdXVnTmFrMFJwUFJJNjArSnNicmVOMU1STnN1Smsxc1BGV0lEd0NMVlJKSmRoZXVydnJIdGE2bkNTMk5RY0ZUWlpNMjFVcTdrNFZkWGE3VkFraGRyV2hkOFQvYUtnVVpJZFM0Q3paUnY0d2UzemYrelUxcjgzYkc0K29yUXdWYTd2Wk5yNDI0Vm9hTnNGN0NCVjVLckN0MEljOUt0alhvdmJzbVNTMFhmMjVNZ05zd0xBdk10Tk51cE5nMUZ2a05IUXFMcExoSGg4WDh5SldsdXkzRkMwK1hWRHh4UXVnSVdGY29TNXFDNExOUFFmNjBFaEVXY3F3cGxUYlZtL1dZem1QeVMiLCJtYWMiOiIxNDVjZDYxOTllODJkOGIzNjA2YjY5NmI4ODA3NjQ1ZmFmMjgxYzZiY2NhODUzMjU0OTA1NGZkY2YzYjQxOGY3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjlwbEQvRVNJTDJmODVmb0lTcUl2emc9PSIsInZhbHVlIjoiVHJZTzlrbFJkMFlvNmpNc0puV2xsTlRuOHJxdlJxdW50Q2lrY1k2V00rZWlrL1lNaVFWZDVycEpwTU9TaStHQzIwV1BBaHpkZ1h5RTYzR0o5U1lrcmxsdkJGSEY4S1NycjZxdmtQQmlZclFEVVJSTlpXZW5TUUM1cS9xZ3oxYU1Ca042Vi9HaEgrUVN5cmE2eHhtdzVuRHREdWw5Z1loK0tPMU1ydFFLQW13Zk1NdUl3MStOU0NoVkY0OTgzQzkxeFA1VmNFSEZZNlliRWNaejNwbXM1VCtIUWJ4dm9tZGR1QWJTYVNvTkV0NFhhM2FsQ0xVV0RYMU1MREZvcExWcG5SV05mQm1nWjhoK1BoNG1ORyttdmhSR01tVEYxbFNXQkRXQTFuRFE0QVZKRkhlZWVFRkgyTllTU2hYYSs2NkZLSFBoTDZBVjgzQ2N3RzBBdEdyL3o5dU9Tdk5qaE1LbG1Cc3V0eUxSZEFTTlNreG40aUtFYjR5Q1djZTRkekxUVm4rb1Uyc0JyaFlKSCt1OVo3SnZ0ZldGUFdIWVB2MWdvdVRTZHlIUWpQZk1GYTZUaEQrelhCYm9JVzJWV3FiWHNIcm9NWURPbFk4ZlErTXd0MkovSEFvRTFKR2lHSFBVQVZBU1BEQTZ1MHE4NnRBbU11cUxtZUtqWlNsWHlKeDYiLCJtYWMiOiI2OTY5NzhhNWFjZmMyNWU5MTljODc4ZGIzOTE0OTcwZjJlMDk1ZTc2Yzk2MmFhNGFlNGIyMTZkMzA5NDNlZWI5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867069783\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-691959433 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/profile?15=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691959433\", {\"maxDepth\":0})</script>\n"}}