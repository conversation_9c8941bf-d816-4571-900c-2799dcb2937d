{"__meta": {"id": "X9e5ba3e7aaa208a5b7668b7003f6e4bf", "datetime": "2025-06-06 19:15:36", "utime": **********.867399, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237335.548556, "end": **********.867448, "duration": 1.318892002105713, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749237335.548556, "relative_start": 0, "end": **********.624105, "relative_end": **********.624105, "duration": 1.0755488872528076, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.624126, "relative_start": 1.0755698680877686, "end": **********.867453, "relative_end": 5.0067901611328125e-06, "duration": 0.24332714080810547, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45694472, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.767865, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.785825, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.842971, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.853699, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.035590000000000004, "accumulated_duration_str": "35.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.698593, "duration": 0.01659, "duration_str": "16.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 46.614}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.722541, "duration": 0.0114, "duration_str": "11.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 46.614, "width_percent": 32.031}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.741848, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 78.646, "width_percent": 2.501}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7702181, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 81.146, "width_percent": 3.737}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.788575, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 84.883, "width_percent": 2.978}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8161132, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 87.862, "width_percent": 3.709}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8254158, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 91.571, "width_percent": 2.669}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8319478, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 94.24, "width_percent": 3.203}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.845584, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.443, "width_percent": 2.557}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-935543290 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-935543290\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1782871980 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1782871980\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-585124756 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-585124756\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1492726970 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=f9etjo%7C1749173423509%7C39%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRNcnRqREQ5N25iTHVuM2pEVlZJRUE9PSIsInZhbHVlIjoidGZwMGFoejE5aExjaUE1NHdIclF1K21FVmlLZjR2QjBZaWJKcy9Lcjg3d0oydzB1SExlbmRjdzBzYTZXR1QzNVJDSldpaTlEWkloUDUrdzF2clVLblA1N1BYVkllbWpyRm9QaUlNamZrUmdDN3hpeWdLU0VjeFYwVEowOVBlWlIzek5JOFE4RVpaVUxweGV1SkE0KzJNcVRVckFjazEwZGp1dVZPTW5ZajdtbmliVWVDeWFhejlaWHhnQWRrOVdDRmNhdStOQUtPMHg4V3BIYlA0ZXJiVTMwS0FMQ1RsV3pLcllJWGFUQmN2RFh6c082bVgyL2paTVoySUNKczYxSTJyTE5IVHZPbGgzNDdNRkxnMXgzR3BhNnBvOC9OZm5wQk9xejRWSmwvKzhnTFhjRzBLWnRZbkYxS2FxVjhtVlUraEh3RnlWTzNZKy9kQjdPY24zTUNqMm9PWTBsdTZaU24wZ3FwcjZ2OVlPODBWOWZRSittaEdEVmN2S1VZV0hvVDRBMXFrSzZYakI3QVhGeW5BbjFpVTBOMzlTbGNJdDk1YndsUHNqNytxQlM5VGx2eW0zc2pLcGdOOCs2ek01OHBXOXAvZDNRZ3hzUUZYa3JFR0haSTFqT1R3TUh2Q2NCYUZHVkg4R21nTzFvdmhPR2pZeTkwRGJQS1lOc0UyVVkiLCJtYWMiOiJmNzBhMjM3N2Y4ZDkyMjBlZTc1OGVjMDM3OWI5N2I5ZDA5MGYwMDY3NWRkMThiZGJiZWFkYjc1NzQyNmFiOTYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdGekNtWFNrdTMzbkIrdWpJMkZBNXc9PSIsInZhbHVlIjoiYlM0cEJqVjNwUTN6ZkJRbW5UcEE4aVNxY3Nyby9uNXFXMEY2bEdqV0MyWnJ4YU10V2s4SHpZaTdOSmdoak5ZZWViWFhXRTgzZEJYRzNoaWxWaHJ3aVVpbXQ1cVNmOEhuc2hCRGpDVUVjSzZwMzZDeTFON1RnVUFqdG1USzkyWHMzS3pZT0NkMHAxcmF6Ty9PNXhUMkttcWZ5S0FiT29qUlZXL1ovZGxkWGpTdUJtbHN0WS9FeVloUFhoMmNrOG40OXlKUmp2bFFsK09lTnZCUFZWT0VSM1dBTnpmZTJ1M2REUzNTZndSeU9wSEZ0Zk0raEtqM0dHSElESnRmMmd6cjRLck1CVzlZZzVDdmtUbklVdFIvdjJnT21JSXV3bjlrSi9ZallYb1VxQXpKNWJLWnBMQWV5eUtUVFFsTjFkc0o4RGtUTVFqc3dMSEVjYlRBY05UelUzUWJSK2pSMmZBVGhiQjFZZWx5RHBBUWo3V3AyYXhOL2lDQWZ4MWdxWFNWVy9IUlJyeVBaRW8waFRwSEhWektwMFY4S3JDcHhrdThMdTJWNllBUThObGU0cjg2WWIwNW0ybHhHR0lKZEYxdGQ0TkFMb0wwb2kzWGw2dUZIQk5rdUEwVFZvcEM5Um5rWGxDeGdFOUwrRmM0WHZ0OUZVVEZ2S2g0YjlxZjN2cHIiLCJtYWMiOiJlNDFiYjk2MTlhMzQwYmY3ZGE3ZjM4ZDYzN2E4ZjI5OTBkZjFmNjIwNzBhYzE5NzcyNjY1YmJjNzViMmE2YzIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492726970\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1118742928 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ib82UhgW7QbmPWC6dET38U6gjVREpdbmeIYDPeql</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118742928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-584494139 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:15:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9sWjZXNjdQTU1QaUhnYXhiOWlpS3c9PSIsInZhbHVlIjoidnkvWDNiTlFja1VoQ3Zsd2J6V1BkSUpSa1B0b2JsTmFQNlcwMDBEbkJndUdCOENjd0p6cFNSZXdiNGV3N0hJZjhCUmhCNExpbkxjTGFycDIxOUQ2cFdSTFVjaVFzeDE5NThpbzJadnZZYjRkVWlObFNZbUtoQlNyNE9PSC84c2FWVXZTL1BuWXhZVElQS1hkOHRUaFoycGh5anp5cmd1a3NORk1uVENzWkF3ZHdPMTRuRUJ5a0ludnNmTWxWKzVhSVA0TlowUFFOS2ExaVcyOHlPSE52SGc0NWJGdnIraGdMZWlYcVd0WTZLQm5nM3RNQlZTNmJXMWZPTnFRTVh2Y1IrT1V4NTVwN1puMzFld05KWkpuQ2tCb3RZWHEvS0Y4S1k4WVYxMThDUlBvWER3S1k1Y0pVWDRZcXRIUEMxZmMyUFRaRVNOWEFTTGtuQVo1ckJKUDNLbERudUhiak0wbHJGcmd0aWhZbHZZVDVkK2tuZjlZNE9MMTlHazRTTmg1ZTFCSGJzbmlwUXhQcVhDcHJrUFFJa1ZMK1BrOG1pQ3UzczBxV3ROTXJDVVI2eVIxVEFneEsrZmwxbGFPc1VoZ2tNbUhHMHBVN2drNUVPaW13UDVrNUlYVk1SUnZWS05rbEw3dC8wV284YVRMSEc5ZDdWd2lGelJ3cHNPSXBmdXMiLCJtYWMiOiI1NmU5MjM5NjFkMTcwZDhlMGY1ZDAzZTJjZWUzZDNjY2M1NGJlY2YyNzI5NThjM2E3MGVmMzUzNTVlMzA4N2ZkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRGVDZCRFYzd2JZVDhVcXEyZThmMXc9PSIsInZhbHVlIjoiQlY4QVI1MkpmUTJySmlwVGJGUktDT0ZsUmlyTWpIQTBKY0RBUitsUW1KTzJ2VEpaQ0ZvK00xMnZDRGZZK0FNMGN1YUVuS2M5SXMvQm9iR2J2akRVNG5rUmY5M1dSd2hWNkxnUjhvd0h0anYrYmZnZlpiVVNVUGNMWU9xU0pYYkZlcHppak8zaDhDSUxCaGkwVm9tVXZ0bktzaEltTnllbk8vTCt3eHhnUVkxNDF2amU4YTRWeFRJc2c0M203TnFuaTNBVFBRUU9Zd0dLKzF6aDJ1ZkxOTEYwMjJmdjgxUWtDWDVTQm8vS3hLNjFMVkJiT2ZFaTBYVnp1MFlCUEZ6VVR6dzV5SHlDaEJVNGltSDZCNlJ5cmF3ckc2UGljb3pJSmhaV2VLbXRrNGhwZjYrM2s3RkZPMlZzclNqZzZiTmpoT0g2VXdMMHZqYmtZWUNad2I0SHpyMGI3L1dhek1WMURhbzRoVnp2eUN2WVlRSHp1a1FOVWVkR2JzNnhTZG5heHFwanlhQjl5dDZYcm5HNWxhRlpCOTNBU2VibHREb1VkREZJRERxdTBRRm45dk85dk1MbjRDNzUrbkloMVhlM05lREpEMDRmRGdEZTNtcmludGFzUmUzRzRWbVVCV05XeEdxaEJFZXZIVUpFelZDZDBXS2FsMTBqMFZWa01JcXAiLCJtYWMiOiJjNmVkMTRjMWNhYjZhOTM3NDQzZjU2YjY0MDQxY2ZiN2U0MTZlNWVjM2NlZTRkZTM5MWEyZjUwYmVlNDRmNmY0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9sWjZXNjdQTU1QaUhnYXhiOWlpS3c9PSIsInZhbHVlIjoidnkvWDNiTlFja1VoQ3Zsd2J6V1BkSUpSa1B0b2JsTmFQNlcwMDBEbkJndUdCOENjd0p6cFNSZXdiNGV3N0hJZjhCUmhCNExpbkxjTGFycDIxOUQ2cFdSTFVjaVFzeDE5NThpbzJadnZZYjRkVWlObFNZbUtoQlNyNE9PSC84c2FWVXZTL1BuWXhZVElQS1hkOHRUaFoycGh5anp5cmd1a3NORk1uVENzWkF3ZHdPMTRuRUJ5a0ludnNmTWxWKzVhSVA0TlowUFFOS2ExaVcyOHlPSE52SGc0NWJGdnIraGdMZWlYcVd0WTZLQm5nM3RNQlZTNmJXMWZPTnFRTVh2Y1IrT1V4NTVwN1puMzFld05KWkpuQ2tCb3RZWHEvS0Y4S1k4WVYxMThDUlBvWER3S1k1Y0pVWDRZcXRIUEMxZmMyUFRaRVNOWEFTTGtuQVo1ckJKUDNLbERudUhiak0wbHJGcmd0aWhZbHZZVDVkK2tuZjlZNE9MMTlHazRTTmg1ZTFCSGJzbmlwUXhQcVhDcHJrUFFJa1ZMK1BrOG1pQ3UzczBxV3ROTXJDVVI2eVIxVEFneEsrZmwxbGFPc1VoZ2tNbUhHMHBVN2drNUVPaW13UDVrNUlYVk1SUnZWS05rbEw3dC8wV284YVRMSEc5ZDdWd2lGelJ3cHNPSXBmdXMiLCJtYWMiOiI1NmU5MjM5NjFkMTcwZDhlMGY1ZDAzZTJjZWUzZDNjY2M1NGJlY2YyNzI5NThjM2E3MGVmMzUzNTVlMzA4N2ZkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRGVDZCRFYzd2JZVDhVcXEyZThmMXc9PSIsInZhbHVlIjoiQlY4QVI1MkpmUTJySmlwVGJGUktDT0ZsUmlyTWpIQTBKY0RBUitsUW1KTzJ2VEpaQ0ZvK00xMnZDRGZZK0FNMGN1YUVuS2M5SXMvQm9iR2J2akRVNG5rUmY5M1dSd2hWNkxnUjhvd0h0anYrYmZnZlpiVVNVUGNMWU9xU0pYYkZlcHppak8zaDhDSUxCaGkwVm9tVXZ0bktzaEltTnllbk8vTCt3eHhnUVkxNDF2amU4YTRWeFRJc2c0M203TnFuaTNBVFBRUU9Zd0dLKzF6aDJ1ZkxOTEYwMjJmdjgxUWtDWDVTQm8vS3hLNjFMVkJiT2ZFaTBYVnp1MFlCUEZ6VVR6dzV5SHlDaEJVNGltSDZCNlJ5cmF3ckc2UGljb3pJSmhaV2VLbXRrNGhwZjYrM2s3RkZPMlZzclNqZzZiTmpoT0g2VXdMMHZqYmtZWUNad2I0SHpyMGI3L1dhek1WMURhbzRoVnp2eUN2WVlRSHp1a1FOVWVkR2JzNnhTZG5heHFwanlhQjl5dDZYcm5HNWxhRlpCOTNBU2VibHREb1VkREZJRERxdTBRRm45dk85dk1MbjRDNzUrbkloMVhlM05lREpEMDRmRGdEZTNtcmludGFzUmUzRzRWbVVCV05XeEdxaEJFZXZIVUpFelZDZDBXS2FsMTBqMFZWa01JcXAiLCJtYWMiOiJjNmVkMTRjMWNhYjZhOTM3NDQzZjU2YjY0MDQxY2ZiN2U0MTZlNWVjM2NlZTRkZTM5MWEyZjUwYmVlNDRmNmY0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584494139\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2125157991 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125157991\", {\"maxDepth\":0})</script>\n"}}