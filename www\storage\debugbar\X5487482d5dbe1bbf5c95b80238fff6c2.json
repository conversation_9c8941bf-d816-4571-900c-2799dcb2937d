{"__meta": {"id": "X5487482d5dbe1bbf5c95b80238fff6c2", "datetime": "2025-06-06 20:39:56", "utime": **********.926876, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242395.20872, "end": **********.926914, "duration": 1.7181940078735352, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1749242395.20872, "relative_start": 0, "end": **********.682126, "relative_end": **********.682126, "duration": 1.4734060764312744, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.682148, "relative_start": 1.4734280109405518, "end": **********.926918, "relative_end": 4.0531158447265625e-06, "duration": 0.24477005004882812, "duration_str": "245ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44814960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02749, "accumulated_duration_str": "27.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7803428, "duration": 0.02289, "duration_str": "22.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.267}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.830877, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.267, "width_percent": 4.583}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.872787, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 87.85, "width_percent": 7.712}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.898035, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.562, "width_percent": 4.438}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1100889860 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1100889860\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2030668229 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2030668229\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1371966699 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1371966699\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242386558%7C18%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJpaFNyakQzNHczaTF6OGFsbVd1a0E9PSIsInZhbHVlIjoiVlNBM3kyVU9nbE1kT3I4VmFMM29EaDlubys5S292WUNwaHJXdTZIOHB2STNZTVFuSnF3Zmc4NjJNRHd2WmFVamJOaFE1K2pUQ1hERCtSd05MVDZ3dVoyQkJxTFBXa3VMbXROWTF2eWtPdkI1NE85ckhERG9sdTFEYkhUVXltT0tYYzlOTHlVd1NmK3lRYlpWNzQyOWhSUzdBTlkwUlZKMEFCRU9RaXNOZm5lTGJPNSthcUhjZ1ZQQTdmOFhlNE5lWnJPOHNhRDFHTU5La2dsK0RycFNoSEVxai9VTVhwcnh1cnpqTU5Pc2p3K29wcGpvUCtyTXl3QmZxQnVncDUzUnBZM0IzMVVBTlFBZlJkNElzUG9tRWcxUldwWkR2R3RGbnkwUGgrMnM1TmNSVTlIbUk0Y2tVMEIxR09mcVZQRGtXcWFwcVVjbWh4TXRwOThoaFMzWnpCRDcraWJ2T0M2MUhaTHJ6dzRJTTRDMWtteStxRjhDOXF2R3l0Yml1eDFsOUJyRE9sa2g5Z3RtVm1ia25tcllSaUtEU3JNdGhNNlVFNUFGRWxOV3paczgxeG5zOGY1b2hJYlRzV1dJbkFjSDFqb3pWbGw2eGRyUnR0SUpYaG0xUCtwSHVZbVR4bSt4SC9PNVpOdngxWTJ5M2F0cTQ3UDlqc21UQk5vZEZ4STYiLCJtYWMiOiJhOWVkNzcyNWNmYWI0NzA2ZDljMGJjMjc0NzdlYWI3Zjg3YTI2NjViYjQxMmZjMjQ1ZTYyYjZkNTk2MzAzMmIzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldOSVpnQzZRQXJWbjNCRU1GTHFqZ1E9PSIsInZhbHVlIjoiaFpzMFRkcENuSzlKYWdUMGNVemQ2NWw0NjRmRjdTUk5KWTJjVW80RlNIazg2Z1BkMllqbklwemdKdEs5V3JPTHcwM2lhRVRQaVRVSThQNlJFUkYzQnJHTFNXS2pMSzllTXd5TndZYVpBZFd1Ny9xTlBYTC9uVm50K2hpZzlvMExEMEMxYXVXUUUvZnk5ZVdQNGtwR0crNXMwdjdSQTYwQ2E1SVloV04wVWpsczZKOS95eXhuZWJlRUFXYjlWbWdWOVNEM3d6bms3djJPZ1pGdWV3ZjdiTFJHNzRqVEROazVBeU5ISWhYMXY4NHpWdjFQcUhiMTJwTGt3VVNiOThCVC9ZMHVlZzVUYmluR3h2UnhUcWt3a254Vm8zU3lkUVZHNjU4UXI4RDNHczN4UEpUdkNleU9vSWY5SjBNczhyM1hQVmJ5OWFiTnFRTHFMQk9rdG1sUCs0RTdJbldhRUxBMitRUlhnamZ5YlQwMEdpMGN5Kzd5R2JtcytpM0ZTOHE2b0ZBMG13cDBjOVBjRnQ1SXA1Z2hrQXhvZHU0d0lZZ1YwNmo2RDJvbWhIM0U3UFFDSFVJejV1bnkrNzd4VHA4c0lMUU93ZFFVY1loemJVdWFjRXFOVzJyM3ZKSWF3aGtPcDZrV2pCenlJaTF0YUVjMTBuZmpjTGtTSmh3cjQ1UWciLCJtYWMiOiI5YjhkZDNkNzNkZGY4YzM2YjBhMDAyM2MyMGYzMTBhNDIyMGZjODlmN2Y1NjNhNTY0ZjM0NTg1ZGUzODAxMGI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1035575077 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5SyoJjvx9ER6DwaeNylpmlrCKyunmBMNBrM7fYhk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035575077\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1727531526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:39:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVTMUd1K3lrdXZ0SXpURC9EeS95R2c9PSIsInZhbHVlIjoiazE0dHBmRC9ZaTVhNVlKMTJTa0ZFc3FyL3JmNklYYjBJV2VHWUFtZVJVczkySjZ2RjdOZnVmWGNFNXZvNUxzS2g3Nk5OUW5qb0t3T00vNW0xOWkzOTV4emhQRFFPWlJZSU1ucThELzlyWFdPaS83bTBJcVRXVTVIY095ZzlJM0gxMklYUXRlS28zS2UzUjZQalFLVHBkM1ZPTkJRUXNqRVJlQWZMTHM5VUp0WHdaQk8wTlNrQkFQME9mM2MxWS9XRnZWenN6akZlZkI1WXFYVmJXRGZjbUdXNXRlNUgzZ3M3em5FR3NaK3VOQlAvdXE1cDc0Y2VEakpReVVMa2YyczRmZGh2OUwxb1RBQmE0ZjdSOGo1RWZaanNWMUN6cmNodDdkaXdxRWJjVnlyQ0tWOUw0Q0taWmYxQis3Rzl0MGlKa0JxUlkybWtRUFNYZkNicGFpbFd6MEx3eGpWSWdKTlhxTGFVcXl6RVI5VDc3SjRGQ09mQjJCdU16bUVTUm5NSUhldlVJK1hoUkVyV21ZdVBkWWl1SmZvK2NMSExjQTN5N1VqU0w3ZEpJT3k4aWtGWXVMQ3pFUW1GNVgwT01BSnVPQ3k3ZGQ2OHNNNCtZYzVKQ2laRkdBbkxoRG1jbit1NmhDb1BwYmM1aHB4L0FDaDZST3kvMnNaVW1lanVqZkEiLCJtYWMiOiJjOTg1YjA4ZGVlZWY5Yzk4NzMzY2IzNzRjN2Y2N2FlNTRjYzhkODMyNDFlMzIyNWUyMWQ2NjY0NWZiNWQyZTUwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9zYzFwTFphNzZUdTVQRkZOb2hlVlE9PSIsInZhbHVlIjoiNE9tSys1R1VHRUlqai9ybVRGV2dMTk14cFdZMGxXOGFxblB1RDVPZEUxZ1RwdWNXeGR4VzlRbVM5VEhmektSRlY1bkJKU0dScGV2emVMR2p5RDhKUkgrOGlTRmJFSjJPTytFbzhQTENvMzIyZlJJenJBTTRtZlR1K28ySEh0MVBVOThwWElGWW0yZ2JPbE5PMHR3KzROVVZUQ1F6RlA0dkNRT1JrMVY4NUQwV3QvOWYrOTdRbkNhaDhTcUllVDBrOHVwcitsSEUrbUQ5SXNtRlg2cHhuKzcxVWZ6WW0wNnlrdWgrMHByUVJCWkNOOURnYm5mbURVM281SWZmMU83N0YxVVFmN3doMEJlay9FVVJlaU5RZk1lNER2Vjltei9HdThySVNtcVFFZXNCQXp4VktzZXdoU2VlVXBha2ZML2FoOGpIKzRKK3lVd2daVklDQXZqUDgzSExBRDlpc051bXlybUtUZDlDZjdCRys3RmlZbnczY1NGbFJxekROTGlyeHduTVRPUWNBQnRibFFnOU52WDQvQ3pZdnpCT2ZGQm9zcGszbjBjMFZSRFdSUDNjQUY2M2pzWUl5R01YaW5KVktCTUQwLzZ1U1dJcWFWUVJQQTdCNEdLWDBDMFRrMlp6T0JjV0JRRTBrdWk2ekpRMXp5MmFjdUllczYrWXNHdWoiLCJtYWMiOiIxZTA0NmNkZjFiY2ViNGEwNzc3MjBhMDcyNGIxYjNlNTFmY2I4YzI1ZTRhZjJkNmRhYzdmZThhYzU2MDE0OTI3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVTMUd1K3lrdXZ0SXpURC9EeS95R2c9PSIsInZhbHVlIjoiazE0dHBmRC9ZaTVhNVlKMTJTa0ZFc3FyL3JmNklYYjBJV2VHWUFtZVJVczkySjZ2RjdOZnVmWGNFNXZvNUxzS2g3Nk5OUW5qb0t3T00vNW0xOWkzOTV4emhQRFFPWlJZSU1ucThELzlyWFdPaS83bTBJcVRXVTVIY095ZzlJM0gxMklYUXRlS28zS2UzUjZQalFLVHBkM1ZPTkJRUXNqRVJlQWZMTHM5VUp0WHdaQk8wTlNrQkFQME9mM2MxWS9XRnZWenN6akZlZkI1WXFYVmJXRGZjbUdXNXRlNUgzZ3M3em5FR3NaK3VOQlAvdXE1cDc0Y2VEakpReVVMa2YyczRmZGh2OUwxb1RBQmE0ZjdSOGo1RWZaanNWMUN6cmNodDdkaXdxRWJjVnlyQ0tWOUw0Q0taWmYxQis3Rzl0MGlKa0JxUlkybWtRUFNYZkNicGFpbFd6MEx3eGpWSWdKTlhxTGFVcXl6RVI5VDc3SjRGQ09mQjJCdU16bUVTUm5NSUhldlVJK1hoUkVyV21ZdVBkWWl1SmZvK2NMSExjQTN5N1VqU0w3ZEpJT3k4aWtGWXVMQ3pFUW1GNVgwT01BSnVPQ3k3ZGQ2OHNNNCtZYzVKQ2laRkdBbkxoRG1jbit1NmhDb1BwYmM1aHB4L0FDaDZST3kvMnNaVW1lanVqZkEiLCJtYWMiOiJjOTg1YjA4ZGVlZWY5Yzk4NzMzY2IzNzRjN2Y2N2FlNTRjYzhkODMyNDFlMzIyNWUyMWQ2NjY0NWZiNWQyZTUwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9zYzFwTFphNzZUdTVQRkZOb2hlVlE9PSIsInZhbHVlIjoiNE9tSys1R1VHRUlqai9ybVRGV2dMTk14cFdZMGxXOGFxblB1RDVPZEUxZ1RwdWNXeGR4VzlRbVM5VEhmektSRlY1bkJKU0dScGV2emVMR2p5RDhKUkgrOGlTRmJFSjJPTytFbzhQTENvMzIyZlJJenJBTTRtZlR1K28ySEh0MVBVOThwWElGWW0yZ2JPbE5PMHR3KzROVVZUQ1F6RlA0dkNRT1JrMVY4NUQwV3QvOWYrOTdRbkNhaDhTcUllVDBrOHVwcitsSEUrbUQ5SXNtRlg2cHhuKzcxVWZ6WW0wNnlrdWgrMHByUVJCWkNOOURnYm5mbURVM281SWZmMU83N0YxVVFmN3doMEJlay9FVVJlaU5RZk1lNER2Vjltei9HdThySVNtcVFFZXNCQXp4VktzZXdoU2VlVXBha2ZML2FoOGpIKzRKK3lVd2daVklDQXZqUDgzSExBRDlpc051bXlybUtUZDlDZjdCRys3RmlZbnczY1NGbFJxekROTGlyeHduTVRPUWNBQnRibFFnOU52WDQvQ3pZdnpCT2ZGQm9zcGszbjBjMFZSRFdSUDNjQUY2M2pzWUl5R01YaW5KVktCTUQwLzZ1U1dJcWFWUVJQQTdCNEdLWDBDMFRrMlp6T0JjV0JRRTBrdWk2ekpRMXp5MmFjdUllczYrWXNHdWoiLCJtYWMiOiIxZTA0NmNkZjFiY2ViNGEwNzc3MjBhMDcyNGIxYjNlNTFmY2I4YzI1ZTRhZjJkNmRhYzdmZThhYzU2MDE0OTI3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727531526\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1032503629 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032503629\", {\"maxDepth\":0})</script>\n"}}