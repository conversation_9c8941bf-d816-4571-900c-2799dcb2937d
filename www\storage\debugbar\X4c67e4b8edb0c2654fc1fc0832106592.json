{"__meta": {"id": "X4c67e4b8edb0c2654fc1fc0832106592", "datetime": "2025-06-06 19:37:29", "utime": **********.137711, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238647.486182, "end": **********.137746, "duration": 1.651564121246338, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1749238647.486182, "relative_start": 0, "end": 1749238648.933305, "relative_end": 1749238648.933305, "duration": 1.4471230506896973, "duration_str": "1.45s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749238648.933326, "relative_start": 1.4471440315246582, "end": **********.13775, "relative_end": 3.814697265625e-06, "duration": 0.2044239044189453, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44775832, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.010620000000000001, "accumulated_duration_str": "10.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0245562, "duration": 0.00625, "duration_str": "6.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.851}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0728269, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.851, "width_percent": 21.94}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.083723, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 80.791, "width_percent": 10.075}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.112519, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.866, "width_percent": 9.134}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1429979565 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1429979565\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1473694077 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1473694077\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-31645703 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31645703\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1968191727 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238565066%7C50%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJmRTd3MTVFZTcrUGg2VGhpWE1MSnc9PSIsInZhbHVlIjoiZlJrbHY4TXV0dmtndFZKMUo5WTBPVjFBdWxscHdYYVlyeGVDV2o1cFlEQTRWQ2s0L1B1QWJkcE04VVZOZW5idVNySEhYdlV2UzhWd1o0eVVKb1c1L1ZHeDg5d2VtaDZIbklZT0Y3dzNmRFczdHhxT044cnVQVjlzdldrZFRKUG4rWFBEWkliVi9CWHNSamJOSWczeDRTVUF5VDJKc3lIMGdkcFBhRHpPTDhUUDhhNVNWZWVaazNSeFB5c0NRZFlMZkJOQjZzRTVLRkdrVzRYUU1TOGdzSTlRUWNXZzFLdHplbjFjN0dLTENoSk9BRC9SVWxCbyttMWhUaWpCT3gzU3BzVk13NHAzMURUWUFSYlpkSTN5UDMzVFVyenN4QnJKK01XaEVzWkE5UThnN1hCZEsvS1JGcllQdjk1amtoZGpwL0d3d3dPV0QzTWRyK1dTSmFVd1NweE1Ud3Zsb3dZeng0SUlyT1F6aTZHSDl1ZTRnK2VWdjNVWGVacFBxakNTVFAyKzFJUG0vcEd2RFlOS1FjZEhFV21JSUZUNWtpYnNEZFFMaUgvcmxJRzZUTTBaOElsZk1FbXlpWFBCU0QrME1BYjJMRmhteHp0NUZKbXNrR0s3MWRmU09ZN0NBZXg4YmR2OHlybFVab2VZSWZxVktldDI5MFhZczh6TFc1bHIiLCJtYWMiOiJmNTBhYWMzM2RmMjY1MWJiZWMyOGQ0NzVkZDRkNTljMjkzNmZlODJjZTA2NzEyMmZlOWIwMjkwMzU0ODBmMGE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFqUk16YUFBZGhMaDQzeFJRNWNJWGc9PSIsInZhbHVlIjoiaHphK0E2YVhFQ3YzdnVKa3YrQVloaUZRV1o2RDU0LytQN29XWFBaWGdRU1EyYjhPTXdZVTd2dFdUTzdJZzRHbkpaNkZudVhnOVk1bW5uMlhJZmlPR1hndWpNQXk0ejF6VGgzVTJZYmRoME1DR2JPQisyd1UyOUgrZFhFR2YybmVaM1BhOHdEeTJGTmRSM0NjWXJkRm45RTZoNnczdkdrWUJLNmREdCthVVpSN2NPYzZOTDdHemZLcEhlbysrbmVwWjJOeFJuTGg3aUh6ZDk3WnVTNG9tSjVtYmVEVkRkT1AwRTdJVW96MjlreUtoYWl1ZmhNQUkrOG1OK1dwLzVPbHMvUU9Wckk0V1FkMWZGcE5HYlIrNDE2by96OVJtY3RyNVV3enh4T3JWcDZaTjFSZVUwYk8wbFlEdlhsZUNpK3ErMHp5V084dnU3enNCMGYxTjdNbFFKQUxyZGRDUGNseXF3Sm15Zk9kajg1VGZleUx5UkN2QS9kYlRPN1NUem1nSVpOVWYyZHpvanZqRWRSTFhpQS91b1U1TTVVU1dwYjdvOGxCMW9QeFlOMU5vYzZJN1dMdWVhWXdCZnJpMVhuemsvOVpuUXB2NDI4cS8rZkRCZFFDUU1icW8rd1JIVFdpUXFIcDRtd2ZPVk9KZXQvK3p4c1ExNjBaUlJZSWozOHYiLCJtYWMiOiI2NzYzMGE5MGY2YmJkZjViYzhmNjhkYWEwYjMxYTE2YWM1YTM0ZDRjYWRjMTQzYTJiODczNDg2YTlkZjJlZmY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968191727\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-308705900 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-308705900\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:37:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpYdnN2YVhsaUMwWWFZQmdtUmdTWlE9PSIsInZhbHVlIjoibVIreXllMzVtV1kvOEtCMWE0a0lLeEhjanRMcU5IN0RzeU5yQ0U5VE53cnkramtxdmdFN0w5RFB0cStnam9EUzB3bHNqUnJJeGluQnY0SmQ4dEhkdm5Vd0xQaDRUTlpjeDNnM3p5R2NIKzRhY1U1ZDdNSTJoNkZoNFZHS1ptQmFOS0tRd2hZM3NnK1F4U0JFL3JUMlVPYS9wKzVnS3FLaUFiRklsNStkNkhHMU1Pb2V4V3BMWFJVMFN3WUIvN0NtMy9pOXQxMGFIQU14bVRUNFMraGNWK2w1NTMxTk5VK2JmWTE1OSs4UlNBZkFadEp1TnQ2TlNmeldLcURVL3R4T1lvOHViUmthODNGSkRydFByRXBCc0hXeUtpVGIyb3NtU3Y4UEFQaXc5QjdaOGN2ay9hQlkvazBVbzZ5bW15akxMNkNvS21mV0E1Tzk0V1NHWWhrZ1VGRXdzWk1vZ3I1UDA0bUpNNUVOa3NRTWtsVExDNDRuRFllVmpBS0RtYVFyQTNPMm1ZTXk5ZVhNTThUZjdBaVdCWUt4THRTK3d1T05jYVNVVXc3WlJYMXczSjh5MUtENTlNVEF4RlhVUTBieVJaYSs0NnpCYWZZRGt4a2tGc0lsdFdkM2JyRW1oOVpCN0FCeE9vRDZsM3A4WTR3UytJRm1EVFJ5c04yRDZLYk4iLCJtYWMiOiJkMDRjNjY1NzgwNGY3N2M5M2E1NTg2MDBiMzdkMmU5Yjk4N2QxMDMzZDY3Njk1YTM0NDMyOTc2Mjg0YzMzZTg3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9Ra2RLdTdiUTV5TW1UTGNpZ1ZqSkE9PSIsInZhbHVlIjoibkR3eGl1d2ExTUYyZXFqUUI2T0NVTUcwY2JNejZXekFwN0RnaTVVT2prR0JpeXNhTmxzUVpEOXEwUjMyVkNhSlhYRUVaVnFWcGkvRWR6SjBXN3dWaEtwZ0wvSWhPWGtKMnNmNDZCUThtRTZuUTZ4bE5rRlc3WGxWNFRVNk1FajBSNEZLb09IWlphS1RTK1ZBQzU4ZDdPeWp2QmRYOWdpV0thSWw0VWxXSFEza2tJeWZyenFvWGJwMGpOVmFBcjM1cUhoVDlzQU52K3FaYVJRMzVKcVh0ejBZWGpDNVdnSzMyYkE3eFhBNVVGM0M0V3FvWGY2VFFQckY1K0NRVTgvMGxPK1BoRWdYRXdQNGxSdjlMdHQzYlkvMDIrTFE1OTZDdXZpS2VGdVE3c2lSUHRhcExHMitKdWtMcWJtbGVTK2NvYnFUTGZlbTRMNENLKzhuNjd1K2Uvbi9qRzhkSU1ybXk0VkVtS3ZidXBZZzlST1pmeGVCazdVMjZLVjNIRUpQdnk3MVVWa2ZVWHYrdnltWU1YVzk4cE55clhQZFV2eWZzdndKd2U1MDRsUG9JTDZxNTNtWGh3dExGQmpTUzhlU3VRSEx1ci9sQm82RkQzTmxzZzNLV2pBbm9OOGJCMEV4aldONDZCQzBxcko1S2dWWWVodWNDbTBFOXhWYVJ2VHkiLCJtYWMiOiIxMDMyOTM2MmZmOTBmNjJmZGQ5NjYzYzkwOGFjZWU5ZjMwNDllNTNiNTc4MTZjYjk0Mzk5MDJjMTg4Y2U5NTI2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpYdnN2YVhsaUMwWWFZQmdtUmdTWlE9PSIsInZhbHVlIjoibVIreXllMzVtV1kvOEtCMWE0a0lLeEhjanRMcU5IN0RzeU5yQ0U5VE53cnkramtxdmdFN0w5RFB0cStnam9EUzB3bHNqUnJJeGluQnY0SmQ4dEhkdm5Vd0xQaDRUTlpjeDNnM3p5R2NIKzRhY1U1ZDdNSTJoNkZoNFZHS1ptQmFOS0tRd2hZM3NnK1F4U0JFL3JUMlVPYS9wKzVnS3FLaUFiRklsNStkNkhHMU1Pb2V4V3BMWFJVMFN3WUIvN0NtMy9pOXQxMGFIQU14bVRUNFMraGNWK2w1NTMxTk5VK2JmWTE1OSs4UlNBZkFadEp1TnQ2TlNmeldLcURVL3R4T1lvOHViUmthODNGSkRydFByRXBCc0hXeUtpVGIyb3NtU3Y4UEFQaXc5QjdaOGN2ay9hQlkvazBVbzZ5bW15akxMNkNvS21mV0E1Tzk0V1NHWWhrZ1VGRXdzWk1vZ3I1UDA0bUpNNUVOa3NRTWtsVExDNDRuRFllVmpBS0RtYVFyQTNPMm1ZTXk5ZVhNTThUZjdBaVdCWUt4THRTK3d1T05jYVNVVXc3WlJYMXczSjh5MUtENTlNVEF4RlhVUTBieVJaYSs0NnpCYWZZRGt4a2tGc0lsdFdkM2JyRW1oOVpCN0FCeE9vRDZsM3A4WTR3UytJRm1EVFJ5c04yRDZLYk4iLCJtYWMiOiJkMDRjNjY1NzgwNGY3N2M5M2E1NTg2MDBiMzdkMmU5Yjk4N2QxMDMzZDY3Njk1YTM0NDMyOTc2Mjg0YzMzZTg3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9Ra2RLdTdiUTV5TW1UTGNpZ1ZqSkE9PSIsInZhbHVlIjoibkR3eGl1d2ExTUYyZXFqUUI2T0NVTUcwY2JNejZXekFwN0RnaTVVT2prR0JpeXNhTmxzUVpEOXEwUjMyVkNhSlhYRUVaVnFWcGkvRWR6SjBXN3dWaEtwZ0wvSWhPWGtKMnNmNDZCUThtRTZuUTZ4bE5rRlc3WGxWNFRVNk1FajBSNEZLb09IWlphS1RTK1ZBQzU4ZDdPeWp2QmRYOWdpV0thSWw0VWxXSFEza2tJeWZyenFvWGJwMGpOVmFBcjM1cUhoVDlzQU52K3FaYVJRMzVKcVh0ejBZWGpDNVdnSzMyYkE3eFhBNVVGM0M0V3FvWGY2VFFQckY1K0NRVTgvMGxPK1BoRWdYRXdQNGxSdjlMdHQzYlkvMDIrTFE1OTZDdXZpS2VGdVE3c2lSUHRhcExHMitKdWtMcWJtbGVTK2NvYnFUTGZlbTRMNENLKzhuNjd1K2Uvbi9qRzhkSU1ybXk0VkVtS3ZidXBZZzlST1pmeGVCazdVMjZLVjNIRUpQdnk3MVVWa2ZVWHYrdnltWU1YVzk4cE55clhQZFV2eWZzdndKd2U1MDRsUG9JTDZxNTNtWGh3dExGQmpTUzhlU3VRSEx1ci9sQm82RkQzTmxzZzNLV2pBbm9OOGJCMEV4aldONDZCQzBxcko1S2dWWWVodWNDbTBFOXhWYVJ2VHkiLCJtYWMiOiIxMDMyOTM2MmZmOTBmNjJmZGQ5NjYzYzkwOGFjZWU5ZjMwNDllNTNiNTc4MTZjYjk0Mzk5MDJjMTg4Y2U5NTI2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-633534065 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633534065\", {\"maxDepth\":0})</script>\n"}}