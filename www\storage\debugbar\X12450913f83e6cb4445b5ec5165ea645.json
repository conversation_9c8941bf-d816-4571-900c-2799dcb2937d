{"__meta": {"id": "X12450913f83e6cb4445b5ec5165ea645", "datetime": "2025-06-07 04:41:56", "utime": **********.252367, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749271315.002279, "end": **********.252396, "duration": 1.2501170635223389, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1749271315.002279, "relative_start": 0, "end": **********.098587, "relative_end": **********.098587, "duration": 1.0963079929351807, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.098604, "relative_start": 1.0963249206542969, "end": **********.2524, "relative_end": 3.814697265625e-06, "duration": 0.15379595756530762, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44777840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.011049999999999999, "accumulated_duration_str": "11.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.181879, "duration": 0.00807, "duration_str": "8.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.032}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.214077, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.032, "width_percent": 11.765}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2313921, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.796, "width_percent": 15.204}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-436347926 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-436347926\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-190675293 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-190675293\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2050371957 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050371957\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-637062646 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=xq5jkj%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1p8bbqf%7C1749270939872%7C3%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InN5ZEp5REo3blJ5aHZ0TVExWFpEaXc9PSIsInZhbHVlIjoiaW5qUDRsb1duRUhVMEtnNWZ4VlpsUWE2TXF2amhRUjFYYlRuNFNIeDJqTWV3VHpxNGxodXM4UUZIU3l3b2ZYN1lDdU0xMmc1NitDWElyZ3FMZUFsNHVVVHE5ZGtZNEpjNGloMmYxcmR5cUN2R0c2K3lLTEJ4RHlaODdRb0xqdWdQY2cyNnBHZ0hSYlRWVllzT1dQcUhjK1o1anRQaTZKOFZJK1BFYU11bFN2OGtpblRmVEI0TXloeEFpeUUrd2pReVFXRDJ6T0dSQTNGMDdRdVJSbzFheVhDQjNYbXVjNEpqcVhNbTBqMmxXbVZBS1hocXVNdGJPc0tlZjRJUDNYMVlidGw3T3JFWDJiVVhIVFVCb2FPeW01S28xTVZsa0FtYUthTDhoRTk2ODE5MXBoTUNYRmNIWndoZXd1WmhGR1ZMSDVTQVhDRUExRitKNUc5dlFSeHJhelRBbkZmSm9DMlFaUWtIOUVXMVdIRHBRbDFLYTlEcFpzdEVmb1F2YmNIeFNYTjVLN3NWNmFBbGlxMzB6b0RKN0hraUErWHdIOHV5VUVWL1krc2ZvcWw3ekg1M0xEL1NaSVhiL1VEcU9qRGVqUVlpYlJSNlNoY2tRMXhFZUUxQ0VzZlQzemZnWld3ZElxUWd4RkUvNHJ6WFV6S09ZUll0TkxZczl6UFlqc0MiLCJtYWMiOiJjMGI2ZTdkMzNlNzAwYTFmYTA3YmYzYjlkYmJiZGYwNTgwYmI5ZTY0NDcxOGU2NmY0MDM2OWQwOWY0MzZhMTQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFLTzJmbmdIbmlLVWRSZFRiVy92THc9PSIsInZhbHVlIjoiV3AzMDd3THlLRkNrRDNYM2IwTmFCb2ZqZEpSRnB0UXJMY2FjTWRrKzIwMWFRTzhBL2VaU3Vodjc2V1g0QWwrbTN6OTc2amFiZzhxd2xKZFlqSDM0SFFWOTljV2w0R3dtOElIaTVtSzd6amYzejNXaXhMQ0hyZ2NLazl2R09KanpMY3oxMWlscWMxSHkwL2ZEbDB5aE8xYjIxSWFPWTA0YWJIeTJubW5MSGthZ2ptNkFmblFpS0p1MkliRmRuMmNGdUswN0ZJOUpVbjZBVE81SldEVk1vOXh4WlRtZWZ4YzRnYWZIQXlOb0t6ZjJoclRic2RzZU5Mckh4UXpDQ01sRnJ2aUZFTFEzOFozeXp2Wm91b2JDaFpwZS9sNFArS2FVV1ZraDRDWDJnRUhDdVl4dVJKY0EyNzlLQXphMXgvYWNrREk3VmcrUmlpck9FNHoyY0hkNSt1VnlKWnM0SEIxbEVKalJCWCtKMW1KMnFwN3FEMEhOa2pydnFXK2tUVmsyLzdZL25ucDlhQThZODVKVm5tdGUxYWo2b2FCWFdSK1c4UWFjYkpEQ0ZRbVVDVjdtNnVxWnltYk94eDJKdWY5LzJJZGkxV2ZJN0ZDVHp3aHQ1dE5Cd2xkWVUrOFpob0x1bEo0YTNkVlk2UzBTb2tyRDMxMGxDQ0l4ZXlsMTdLSjYiLCJtYWMiOiI2NmMzNWUwNTU2NzMzMWIxNWJmYWM5ZGY0ODVhODZlNmFmODBhOTY1MjkxMTIyMGQxNDk5NGViYzBlNzJiMzQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637062646\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1141487171 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z1ibAMoy16lMUkDWMem3ATVFxZVkTly8GBk5DGQF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141487171\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1936930372 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:41:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMxeHZFUFZDZ2l6alRVMUo3TkcvK2c9PSIsInZhbHVlIjoiY01pKzFKSGx3WS80d2h4R1FjWVYzdG1KeERJaEhtZ3VXV1hGUUN3dEY2ZStycmd4WXpkU0dFT0t5Wm1PdTZOWHE0dHBqNDA2M1JKWitVdzVXRDdxQTF0ZldNdzRZakRHRDFLYURVSkE3bVVVeDExU1A3SFBneE8zYkEzbVI4VW8yMU1COW8yYkZ4VzB6UTBRbEtPQy90NFFXaEhYcFNEYTRaeWxDUVppd0huVERSY0F5YTFIQ3N6M0RyRVJrUkJHa1QvWUltZW5WUExPcUdjMzFuV3pjQkhDN0VLSVNMdDRrdGV5Nlo5OERzMEdXKzdxU2t0OHllYkJGblFGb1oybjh4a29wWWM2M1ZoRnh5Y3pnMDVaenQweCt5NnR3RWpqeWVieERQdUlBOUdJUW5QWmU1c2VxWE9hOVh3d1g0UWpKN0tsWDhpUkxRd1JHZGVKUEFvSDJlSWV0UER3R2xmUVBYcTFvZFZsQTIyU3FYcUE3bXRNcTJaK1gvaGlGb3lrUXBNNGdGNVNuRFdkUTkwbGg1TG5RK056djJOSDBXZHZKZm9Ld2hENW13VCtCRzBKS2Q2VmlaQVZNdzJtMjRIM2tUMUVwMzMvQnNGcGRwUlMwelJTQWFXWFIxRDVNL05tQUxvZEJXTUduYzFGbGNubVFtSUxDcWU2dHRPRWlkRFMiLCJtYWMiOiI4NDllZTkxOWNjNWQ0NzMwMTc5NTI3YjcxMjEwNDU4OTc1ZDU2ZWFiMzRlOTNiNTUzYTlmYmNhMmYzM2MxZmU4IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:41:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFVaDdzc2VONXhPVE9jaDdybFAxTFE9PSIsInZhbHVlIjoieTZtSVVJOUZlRmo4U1A4ejRBMkJjQ1ZNSHBkSDhsQkRPMDZXL2RYL0d0aHhCQlh3WXBmYktZQU1rVTRVRDN4bVBEejVIS2QrT2U5dFVWZzl6aFhpRTdEVlZ5SGNnNHBrVFNPNHRFTkJ2RUVtU2ZBTVdBWk8veVp6UHVLeEg4Q0NJR2JtTFZpdGRpc2dlZ3NkNnBWT2Y3M3JLQlFIRlFUdXpiYWFJb3EvREZBdDhkYklDL1FYa3piY0NIWGk5UHgydzBGbTlpaGNld2E2WS9EeGlXSEdmYlM0d21RMVFsbytPckpGWWRnSEhkOGJkZzlvLytqUkFqVzdMZVVPVUloSElpVWJpOXVrZjF2SW5jL3VDQmlVZGdQbm9BbEFzeDd0NWZWaldoNkRDdjRVRy9qUVhXUmlsMm5VY2RCcmE2YkJPaStkNHJLK2w3dG03SGxmU0RlQjE3akhwd1ROdzFSSHlLNWJBY3oxcHV4WFRxckN4clJHaE1NcVAxS3orOEFLZXd0MEZQZkg0S0RGQW1UR284eVF0TitaM0dTczkrZEhqSlhlYjltc2d6VlByWEhmVHprb1Y0OXFmTVRGODFwZm1HV1dFUlFwc0l0cnV3M29sVElHd2dld0F5aytJMk43by8zT1RoaEV2LzZ3QXNpVEprYklqTnhmbHVYTndBNFUiLCJtYWMiOiI4OTMxOGYwZjBlYTViNjQ1NTI1Zjg1YjY1MTY4ZjkyYWZjNTRhM2U1YTAyNmVlZDNlZTBhYzQzZjU4ODQ5NGFmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:41:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMxeHZFUFZDZ2l6alRVMUo3TkcvK2c9PSIsInZhbHVlIjoiY01pKzFKSGx3WS80d2h4R1FjWVYzdG1KeERJaEhtZ3VXV1hGUUN3dEY2ZStycmd4WXpkU0dFT0t5Wm1PdTZOWHE0dHBqNDA2M1JKWitVdzVXRDdxQTF0ZldNdzRZakRHRDFLYURVSkE3bVVVeDExU1A3SFBneE8zYkEzbVI4VW8yMU1COW8yYkZ4VzB6UTBRbEtPQy90NFFXaEhYcFNEYTRaeWxDUVppd0huVERSY0F5YTFIQ3N6M0RyRVJrUkJHa1QvWUltZW5WUExPcUdjMzFuV3pjQkhDN0VLSVNMdDRrdGV5Nlo5OERzMEdXKzdxU2t0OHllYkJGblFGb1oybjh4a29wWWM2M1ZoRnh5Y3pnMDVaenQweCt5NnR3RWpqeWVieERQdUlBOUdJUW5QWmU1c2VxWE9hOVh3d1g0UWpKN0tsWDhpUkxRd1JHZGVKUEFvSDJlSWV0UER3R2xmUVBYcTFvZFZsQTIyU3FYcUE3bXRNcTJaK1gvaGlGb3lrUXBNNGdGNVNuRFdkUTkwbGg1TG5RK056djJOSDBXZHZKZm9Ld2hENW13VCtCRzBKS2Q2VmlaQVZNdzJtMjRIM2tUMUVwMzMvQnNGcGRwUlMwelJTQWFXWFIxRDVNL05tQUxvZEJXTUduYzFGbGNubVFtSUxDcWU2dHRPRWlkRFMiLCJtYWMiOiI4NDllZTkxOWNjNWQ0NzMwMTc5NTI3YjcxMjEwNDU4OTc1ZDU2ZWFiMzRlOTNiNTUzYTlmYmNhMmYzM2MxZmU4IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:41:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFVaDdzc2VONXhPVE9jaDdybFAxTFE9PSIsInZhbHVlIjoieTZtSVVJOUZlRmo4U1A4ejRBMkJjQ1ZNSHBkSDhsQkRPMDZXL2RYL0d0aHhCQlh3WXBmYktZQU1rVTRVRDN4bVBEejVIS2QrT2U5dFVWZzl6aFhpRTdEVlZ5SGNnNHBrVFNPNHRFTkJ2RUVtU2ZBTVdBWk8veVp6UHVLeEg4Q0NJR2JtTFZpdGRpc2dlZ3NkNnBWT2Y3M3JLQlFIRlFUdXpiYWFJb3EvREZBdDhkYklDL1FYa3piY0NIWGk5UHgydzBGbTlpaGNld2E2WS9EeGlXSEdmYlM0d21RMVFsbytPckpGWWRnSEhkOGJkZzlvLytqUkFqVzdMZVVPVUloSElpVWJpOXVrZjF2SW5jL3VDQmlVZGdQbm9BbEFzeDd0NWZWaldoNkRDdjRVRy9qUVhXUmlsMm5VY2RCcmE2YkJPaStkNHJLK2w3dG03SGxmU0RlQjE3akhwd1ROdzFSSHlLNWJBY3oxcHV4WFRxckN4clJHaE1NcVAxS3orOEFLZXd0MEZQZkg0S0RGQW1UR284eVF0TitaM0dTczkrZEhqSlhlYjltc2d6VlByWEhmVHprb1Y0OXFmTVRGODFwZm1HV1dFUlFwc0l0cnV3M29sVElHd2dld0F5aytJMk43by8zT1RoaEV2LzZ3QXNpVEprYklqTnhmbHVYTndBNFUiLCJtYWMiOiI4OTMxOGYwZjBlYTViNjQ1NTI1Zjg1YjY1MTY4ZjkyYWZjNTRhM2U1YTAyNmVlZDNlZTBhYzQzZjU4ODQ5NGFmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:41:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936930372\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1747848364 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747848364\", {\"maxDepth\":0})</script>\n"}}