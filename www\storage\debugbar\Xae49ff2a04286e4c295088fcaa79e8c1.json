{"__meta": {"id": "Xae49ff2a04286e4c295088fcaa79e8c1", "datetime": "2025-06-06 21:55:01", "utime": 1749246901.025343, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749246899.58205, "end": 1749246901.02537, "duration": 1.4433197975158691, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749246899.58205, "relative_start": 0, "end": **********.754089, "relative_end": **********.754089, "duration": 1.1720390319824219, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.754112, "relative_start": 1.1720619201660156, "end": 1749246901.025373, "relative_end": 3.0994415283203125e-06, "duration": 0.27126097679138184, "duration_str": "271ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45694936, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.91463, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.933174, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.996238, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1749246901.006034, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.039970000000000006, "accumulated_duration_str": "39.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.833467, "duration": 0.024079999999999997, "duration_str": "24.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 60.245}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.864594, "duration": 0.00877, "duration_str": "8.77ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 60.245, "width_percent": 21.941}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.882768, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 82.187, "width_percent": 1.776}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9162931, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 83.963, "width_percent": 2.452}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.935156, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 86.415, "width_percent": 2.452}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9671328, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 88.867, "width_percent": 4.103}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9771209, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 92.97, "width_percent": 2.277}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9843762, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 95.246, "width_percent": 2.427}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.999668, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.673, "width_percent": 2.327}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-806335763 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-806335763\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-83339345 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-83339345\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1099540717 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1099540717\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1602638210 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=v1wjex%7C1749242089260%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBBdWwybTNMZTZocEJlelNiM3NWcmc9PSIsInZhbHVlIjoicWdPZkFOcFlCbjU1ZWhNTGlYanNkMHdlNVRmcEc2M3BtL1BOdkhOKzhXZ3NIWWFJQSt0SXpNZm5ORVdhTVpIcCs1VnpDVWxEL3MvOWZsNE40blI0bjljMGtra1Q4N2NZWEtRekErUGswVW9BdFEzMjA1QkYxdzRMLzkrRGJNdzU3d0ZWQStGbG9lb0ZJZFJDSW0rR3BEaFJBRm9LcFdVTmZxaFRVckdwL3djZGRCTGNSOTZ0RTVCbDZBV2JTWUNuWFl0a3JQd3lya2RaNXV6SG5IZ2JhS2ZJTTRuMDBqcjRURlBkREhBazRQc1BCSFFuM0pwdHpqcGVGTVdCUDZCeFpDSUpjbkFkb1g0em1Wbi8wMEE4RTZCOGZ2UzFyMktDMitJU2VNdnRGMjdxYmZsRGtaSDJoS1laWnROOVM0UWRjTVpBZ0F1bHhvNk1qcmgvTE5kVmV1cWFLUE9jMkpTeDNWS1MrWUZhTnZCVFA4b3RaRmtYMEJIcUlGbFRMdWxWdGxKbmVYNVdnVTlpQ1FZWkp2bG04bXdQM3h4OVRGY2Y0eHMxT1lYNlpIMG9aelRkMnRyeFp4WGt1WitGQy9hZVVVamlicEJqT0tZZEovUDNEbU1uSUNRWEZpMU1CODhtOEVuVG5IekUxTzFEdWtuczlxbTM3QXR5bGdnV2RZOG0iLCJtYWMiOiI1N2NmOTljNDIxZWEzYWZmMGRmOGIwOGQ5YzlkOTVkMTBhMjRiOTE0NDgyODg4NTk0YzZmOWI1YjhjMmEyOTJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9WQ2JGbVh3RXR0OXV3cDRPZDkyS3c9PSIsInZhbHVlIjoiYm1CUWp0OEpnVDdQRGhSeXorR01KZ08xSHBoZmZOWGVGai9ndSsvTEY3dHhZbzAydy9qcEd0MFQxb3N6R3FlMzJGT1JKb1pWczJJVXl3YzVFSmI5QmV0MkFOTDFRYXkvSTN3bU4vWWQvRzByTzZXNDFxOXJsSURRZ2NnWEZ5K0FzVmJzVklhSzFVbVJsMENuamF1TktjR1o2VUxFTGh3OVIvdXl0WjU3S2VVNFpxMCsrVlFzUkI3NVJjcElQeVRoSjZGVmtnTWY1c2FIazdobFduQjFUYndGWkg0N25PM2pKRGJjMFFlSlhSeEhPSE1EM3UxQ3JsOVB4Qzg4bSs1alRVTVpYUW01V3Z2eVlZcVg2TWczYlJmcGF3M2xGMEFyUmcvZjNkWWV6UkFta2hjWUNGbkZnUzRFMUxpZXpPejZBUGtBN2NDRUJtT0N5a3ZHS3k2QTVvR2RwY0JDdXR1QXZQcXVqQ1ROUkJtUCt4L2N0MlBvQTVMOXllNXVBbFRBYXpuMDFrNUZnOGI3ak95OTU5TExMa1gvZjZHY3VVckhXUzVlUEtpNm1vMWg2NkhZbW9LRnFyVkRrMTFHSSs5Q1MwdVBYNHhZenRhelduRG5vekpxUG0wWXNDVXo2dmF0WmZJeW9acEkzNjB6c2Y2S1dQTDhJd2cxUjYwalAxdWEiLCJtYWMiOiJkMTQ1YWVjZmM2MmQzNzQ5ZGE5MTBjY2Y1OGNmYmVmNjg1ZjVmODVkZmU0YjRjNmFmNzNkZDk4MTQyMzljMTc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602638210\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-324407772 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ib82UhgW7QbmPWC6dET38U6gjVREpdbmeIYDPeql</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324407772\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-549903041 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 21:55:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRrQ1JEWlM4Ym5JOEk5UjBTSVJxV2c9PSIsInZhbHVlIjoiSmM1enZIWnFoYjREMkgrYWRtL1AzeHEwSlR4eFdxanNadUdCdHRpaTdIUW9EYTZLZ1JDYzNkNFNtVzFwTVlzbXROV2wwNStyMllhaExuMURaUCtycUY1TWhaSndJZFNEOUQyUWwxMkZuYTk2dG42R1hHMW5VN2JWdStoVjVCbnl1eU5SMG8zYXdsVUJvWW1BT1NnMFRBN0RQRlRHczl2NmlFcG5iS25sZysxc1AvSzUrR01iOS9NV3hKQTlBWFVERUdNUXMwLzlVeU9lZ29tOUxYWC81Q0hIVlZyOTcwbEFzemZheTdKYXIxYSs1bXBOUDI0QVNWQUh2NVVuaU1OOUlNOVVac3ZNeHh2THJ4ZWxqRHYvTzJacGlncTh4cGxpeGttdlJFeDY5STFzWUtFZlFFOXIwbk1uS1RLVjhyNDQyYXBQWnZSL1F1eEZIZE0yWkNnbHBMRjhRR0NVY3YzZjVXTk5Id2g5bndlSUlRaWZMQnNJRjdaUEtpR21JRWpQVXB6USs3RjhuVzIvcWRlemxuZThHTVV2emM1U1ZmajVNT2pOSk9vemROcktYaHBVa1p4d3o1eHVDdmxHTHgvVERkSjduZWpuZEQrNFJiQ2RDS1pnc1JaRmRiQzMxa1hSdmc2Z2NOOWpGWkdHbUpvejBkOHVDNFRFUU1pVWhENlciLCJtYWMiOiIyMWNjMThmZGQ0YzVjNDZhZjgzZmRiMzhlMGRhZWMzZGRmMDc5NGI3Y2QyYmRmMWNiMWNjYjlkNGQyNGFjODI4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNrZTNLR09sbUdjT2NnMG8rRllwR1E9PSIsInZhbHVlIjoiWTFsRlZXWlBVM1ljNFh2ODFQNDRnRWZKOHhFZ1RNcGFSa0lLZ2ZnTVFKWDlOR1ZBcmpwMTNTOTd6VEphaHp4T051Z0tLajFIcVE3OVJicVpBa2Q5TENGT1NGK0lvaWFYeEt4dTBTZjB4d3IvYStGaWZtaFZiTzd3MTliTi9lSkQ3cklVM3lNNXM1WkJFcWNZMUt1RFQ4QUt0ZlBnVjVrS21LZk5YN1c2SEtURDZRNTg1anRISjVwV1ptS01pN1hSbm1mQmhyUmQ3SkJxL0pEejZ3STRvUjcrWlhHU1h4SnNZdFJ6TzlBMFpjWWlMV2JyaUUzeXRORXJmZ2FuNEY4cjErWEhobVpuclBnNXZUMzIzcTNEMlFTSlZ2RnBtNEk5Q1FGc3pqV3J6bnB6L0Z6YURuejNxdk1VeXJyZWlnUHVDc2VreCtaa0o0d2xhY0FGejM3NUd0VXhOSGtlczhWU1c5V1cxQ1dDdkVaUE1QN3BDZXVzNTVYY1FOOXRDZ1UyKy9zRFAvbTV5clJJV3RETDlkeWRSeTZRYzN4N3RhYk1QT3RJcGQ3NFBsN0VKOGVKS25jTzRPZnM2MzJFMnJnZTVmeUhxZlRLQnB3Sy9vWlJKTmFLRWcrSWtVTGc5bUtHbGorVnZFMnFlUHE4UFBpdEF2VHQ0bnZhYURkWmVZc28iLCJtYWMiOiI1MTNlMzc0N2FjODk0N2M5YmUyMzYxM2VkNjFmNmM0ZmFkMWUwMjdjYTZhOTQ3ZDdlOGNiNzA5ZGJjZTBlZWRkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRrQ1JEWlM4Ym5JOEk5UjBTSVJxV2c9PSIsInZhbHVlIjoiSmM1enZIWnFoYjREMkgrYWRtL1AzeHEwSlR4eFdxanNadUdCdHRpaTdIUW9EYTZLZ1JDYzNkNFNtVzFwTVlzbXROV2wwNStyMllhaExuMURaUCtycUY1TWhaSndJZFNEOUQyUWwxMkZuYTk2dG42R1hHMW5VN2JWdStoVjVCbnl1eU5SMG8zYXdsVUJvWW1BT1NnMFRBN0RQRlRHczl2NmlFcG5iS25sZysxc1AvSzUrR01iOS9NV3hKQTlBWFVERUdNUXMwLzlVeU9lZ29tOUxYWC81Q0hIVlZyOTcwbEFzemZheTdKYXIxYSs1bXBOUDI0QVNWQUh2NVVuaU1OOUlNOVVac3ZNeHh2THJ4ZWxqRHYvTzJacGlncTh4cGxpeGttdlJFeDY5STFzWUtFZlFFOXIwbk1uS1RLVjhyNDQyYXBQWnZSL1F1eEZIZE0yWkNnbHBMRjhRR0NVY3YzZjVXTk5Id2g5bndlSUlRaWZMQnNJRjdaUEtpR21JRWpQVXB6USs3RjhuVzIvcWRlemxuZThHTVV2emM1U1ZmajVNT2pOSk9vemROcktYaHBVa1p4d3o1eHVDdmxHTHgvVERkSjduZWpuZEQrNFJiQ2RDS1pnc1JaRmRiQzMxa1hSdmc2Z2NOOWpGWkdHbUpvejBkOHVDNFRFUU1pVWhENlciLCJtYWMiOiIyMWNjMThmZGQ0YzVjNDZhZjgzZmRiMzhlMGRhZWMzZGRmMDc5NGI3Y2QyYmRmMWNiMWNjYjlkNGQyNGFjODI4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNrZTNLR09sbUdjT2NnMG8rRllwR1E9PSIsInZhbHVlIjoiWTFsRlZXWlBVM1ljNFh2ODFQNDRnRWZKOHhFZ1RNcGFSa0lLZ2ZnTVFKWDlOR1ZBcmpwMTNTOTd6VEphaHp4T051Z0tLajFIcVE3OVJicVpBa2Q5TENGT1NGK0lvaWFYeEt4dTBTZjB4d3IvYStGaWZtaFZiTzd3MTliTi9lSkQ3cklVM3lNNXM1WkJFcWNZMUt1RFQ4QUt0ZlBnVjVrS21LZk5YN1c2SEtURDZRNTg1anRISjVwV1ptS01pN1hSbm1mQmhyUmQ3SkJxL0pEejZ3STRvUjcrWlhHU1h4SnNZdFJ6TzlBMFpjWWlMV2JyaUUzeXRORXJmZ2FuNEY4cjErWEhobVpuclBnNXZUMzIzcTNEMlFTSlZ2RnBtNEk5Q1FGc3pqV3J6bnB6L0Z6YURuejNxdk1VeXJyZWlnUHVDc2VreCtaa0o0d2xhY0FGejM3NUd0VXhOSGtlczhWU1c5V1cxQ1dDdkVaUE1QN3BDZXVzNTVYY1FOOXRDZ1UyKy9zRFAvbTV5clJJV3RETDlkeWRSeTZRYzN4N3RhYk1QT3RJcGQ3NFBsN0VKOGVKS25jTzRPZnM2MzJFMnJnZTVmeUhxZlRLQnB3Sy9vWlJKTmFLRWcrSWtVTGc5bUtHbGorVnZFMnFlUHE4UFBpdEF2VHQ0bnZhYURkWmVZc28iLCJtYWMiOiI1MTNlMzc0N2FjODk0N2M5YmUyMzYxM2VkNjFmNmM0ZmFkMWUwMjdjYTZhOTQ3ZDdlOGNiNzA5ZGJjZTBlZWRkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549903041\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1604514577 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604514577\", {\"maxDepth\":0})</script>\n"}}