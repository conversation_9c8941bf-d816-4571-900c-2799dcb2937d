{"__meta": {"id": "X28a229f2ade43b43563445171b12ca0e", "datetime": "2025-06-06 19:31:14", "utime": **********.891452, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238273.352421, "end": **********.891496, "duration": 1.5390748977661133, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749238273.352421, "relative_start": 0, "end": **********.688696, "relative_end": **********.688696, "duration": 1.3362748622894287, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.688724, "relative_start": 1.3363029956817627, "end": **********.8915, "relative_end": 4.0531158447265625e-06, "duration": 0.2027759552001953, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02236, "accumulated_duration_str": "22.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7902129, "duration": 0.018850000000000002, "duration_str": "18.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.302}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.840509, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.302, "width_percent": 5.009}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.847323, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 89.311, "width_percent": 5.054}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.864969, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.365, "width_percent": 5.635}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-519837000 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-519837000\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1285453825 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1285453825\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1723171081 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723171081\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1347831279 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238255845%7C40%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1HaHdqeE5uM1pKZCs1TjNzQ21RQVE9PSIsInZhbHVlIjoiNlQ5MzFISi8vbHViTzVEN3N3TEdkMy8wQzZSNmhnWFZ2SkZCZTMycDVrVDZ0dWJTbm9GVFRUR2wvYTREYXhCYjh3aDNDd2I5R29OazhWZGowdS9pWWVqR1pJeDRKZWhsRHhTTWhMNmwzKzcweVhvQjZjdFd6MkpZS3RaRzNhTDgyRXM4OGJqeldZeDc1MVlmZjd1dWd6OUM1Wmt1QWk0VDlTeXNScGhTSGpwejFzTkUrWGRZODBwTXhBdzVrM3VTRnA4WHRUaFRHRm5qc2JiakZmMWdCK21UTjBvSmJGck5nTVRvNjBwcWxiOFFnZVdXZ2dVaTBPVkJnWWxmbnZXUFc1MVZtYkxMUmV5aGdla1lMY1JsZU1IaFFLMWwxcmV4dFlnMFppa0FmME5kcUVhY1VmN2ZBaUY4K1VBVWROY0VjY0E4c2Z1MnU0aWppalE0L0xBalNaREVWRXN5NFhJdys3VHRaWnBBYXN1TXRMOEk4YTAvMGlFeThwcTNPQjdBYUUrTHlpQmczRzhHa3N4YmYrWms4Q0V6b2VnK2ZRSFovYUVxd2lEczVpaVJMZEZqV2JYWXEwZi9TeEN3clpxclR2c1h6Vkx5NVhRTHZzVFZOeXFJUnJhbUd0dHYrcUhReTczUnptTUZJZ2dUckN1THg2NzV2UGlSVWx6VEkybUUiLCJtYWMiOiIyNjgwYWYzNDE3NzNlNjlkYmFjYThiMzlmNjAxNzI5OGE0N2EyYzkzN2UwNjMwYjkyZWI0ZDNmNjJkYWQ2YTY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZUaDFacmRQcVE4QSsrK0tLM1lCTEE9PSIsInZhbHVlIjoiUWlBYUVRUWF1KzdXU1FMZzJRb2haU2V2ZGszcFNxSE82ZXNic2FQTHVyR094YkRTMy9rRkpUYk84bHdHUEtIbTU1QmRNSDVvRk9PazdWVXRYMWw5WGFYVWtjZXlwNzNsZTZrUmh1TFRrMUpJM0U4TFI2eE5HeEh5MEMyeEVVQmFNMjhlOHVVSkE1Ti9tamJvZ1JZWWdxbkhGUDYzUTlWTTdTV2g4K2VhM1R1SjNRWnM0eTFGZ1VsVEJkOW1DM3JnRTdJRWRmRFlrM1N4NzhpV29uWEw2eVVScFJ6Kzl0SGtBN2pwckdYeHpJdVRCN3owUVZLbXJVWWx0K3l2am9sRFhBM1BJMjdILzBXMEduODYvN0M4WnN3UW9wQkRGclNaU2dYMG5RbWtMb3hPUFVOekxUbW9lY1kzWFFVZjNsVUFvREhpTllBZVVXY2hCd2JpcDBpV1hrTlNTeFBoRUh2TEh6T0gyaXVJOFRqT3NuUTFQNDVwZnJqeGdnTWVhS1FOQm1uYVFHZUdMU0pLU0tOVU1BS0p5bWQ5RnRUUVNDOEdqTHJUbVdqYlc5WTNUS3ppSUIvVCt1T2ZCNHRsYy9kZHBtN2JpNXZRcDlaWWNaVmtTTmVDc0t6NWlWT0d6ZUJ4bnJuQmZhaWd5K1ZWMFN2cS9IK2FHMVJ1RFQzM3paOWgiLCJtYWMiOiJlMzY3ZDVjN2U0ZWI1OGM1ZmUxODkwZDQ3NjM5YWM1NDY3YmNkNWM0NWE1N2Y0ZWEyZDgwOGYyMDU1ZDAzMzZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347831279\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1748855951 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748855951\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1370698669 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:31:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtIbXFhaUlXWDRrS0J6T2h3eDhUSVE9PSIsInZhbHVlIjoiOGtkSjMvM1lQdTE0Nk91U25mUVlWRERmYkxKMXJaYnNPTmFLU2liR3lyREtjcGd5TGN4QW5hWXJ3K3JCSHRHdld3SFFLeXNuSG1sQU9xckJhVkpDZ25KajlUL2Z6THFEWXNjSlZieTN5YmZOU2dkZ3MwR09NNjlWeGZZeEFTMUtHZXR1Ukt3dDNTMHdXM0R1K0Z4a2NwSS96dFZxLzZIV3l0SlNTWnY3UXdqWVRvenJBQ05wbWt1TUxPcVhmS21qRWNoQVJZQ1FleGwzREl0dFFIK0JkVzlneGpFOE85TmRlSUU0Kzh3RnpmL1JTVHA2VUhpQ2RtTWw5dkphdVd3dVNoaGMzVElVNTFCU1NFYUNPM0xxR0w0dEpmYmlEckorMXhtUEMrMkVlOFRvMERadjZ1VVJ4L0RzUkpSeFpxTmNIL2FHcFlRd29qMUpBbElrZlJTZitTenhmcm5CaDlRUkZ4UjNiQ2Y4UnJIOFEzOVZYQWphSWl3RGc3Qlp2NkkrL1NISVFHcWYvWkVVenlwOVZwZFVmVm9BZWlUOE81dURZd2h2cytvNFNMK1Jod0x4NlhJZDlQUGZVV0FnbGMyT0ZnYlB3Zkl4TTZzMCtBVUZheG5MQ1hrdFk3QndZNjBMOG5UNzF0R2lBTURKT1ZGd2tSYnplL05hRUZRRUk1cWIiLCJtYWMiOiIyOTA4ZjA4ZWU2YjUyNTJmMjgyNzEwMWRhZjIyMmZkMGRlNDEyMDZiZDAxZDZmZDg5ZTYwYzg1YWE4NDg2NTBhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:31:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhtQkI0TnFDcUgyZUdxTEY4cWhMVVE9PSIsInZhbHVlIjoiWmZSUlNCbnNOOEo0RGZaVnBJUWlyMHdINGt6dlBjYUZhTGhyVGRVNWJnNEYwcG5rVTZPcGtUWUhpYXA3TU1MTkxFVjFSTVQwL1VqRHl1akhZbzZpaHlnQjhtck5iOXV4aWdoRGFoNyswMHlUNlRJVmh6amh6TlVOc0RCSkJUV1U2V2E4NjU0Vk85Qk9xcjUvMm84bHZudDN0TjN5ejV1V0dmRlVyNGs4dmliWmdVMVVjRWhxVWhqRFhkQm5nRS91WEdZd0JLdjVjWC9kSndGYXpMOFNZWC83S1YxL3BPbklOOXlOZFJjNzhJc2JMbDdjWlJqZFFmN1J5STMxRzkvSzJEYUhvY05EdU9nUmZDWFU5dTVxV25wemJpVlVMcDdEWlZwcUVhUmxqZk81WnkxdWhrOXUxRzlKbVB1clhNWm5OcXB6dzZhVnZNNzBBbTZsaDFjUi9sTld5aDV6VlZzS2Z1L0l4WUJPamkraTdlMmczSEV6REl6MUg4ZWp2ODE2TkRCSS93WjRGZFFxaWxvSlEvemtaMXFMS29WNUpMbG9sTDVJR3p3VzkzUHBGMWwyeHZjTnJKOVNjMjlSNDcyRlhUMkpoZHczc0c5d2VrYmVZVEIrMTVPVHFxY3ZGTGZhbnZ3eFQxcDJpTlYxa1A5NHE2UjZwQmhTYVZzR2J0UjMiLCJtYWMiOiJkMDA4Njk2MWQ5NmY4ZGUzMmI2ZDQ2NDc4Y2FmNzhlZmUyMTBjOWIzMDY3ZDAzYjJjZGViODBkZjkyMjMxYzI0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:31:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtIbXFhaUlXWDRrS0J6T2h3eDhUSVE9PSIsInZhbHVlIjoiOGtkSjMvM1lQdTE0Nk91U25mUVlWRERmYkxKMXJaYnNPTmFLU2liR3lyREtjcGd5TGN4QW5hWXJ3K3JCSHRHdld3SFFLeXNuSG1sQU9xckJhVkpDZ25KajlUL2Z6THFEWXNjSlZieTN5YmZOU2dkZ3MwR09NNjlWeGZZeEFTMUtHZXR1Ukt3dDNTMHdXM0R1K0Z4a2NwSS96dFZxLzZIV3l0SlNTWnY3UXdqWVRvenJBQ05wbWt1TUxPcVhmS21qRWNoQVJZQ1FleGwzREl0dFFIK0JkVzlneGpFOE85TmRlSUU0Kzh3RnpmL1JTVHA2VUhpQ2RtTWw5dkphdVd3dVNoaGMzVElVNTFCU1NFYUNPM0xxR0w0dEpmYmlEckorMXhtUEMrMkVlOFRvMERadjZ1VVJ4L0RzUkpSeFpxTmNIL2FHcFlRd29qMUpBbElrZlJTZitTenhmcm5CaDlRUkZ4UjNiQ2Y4UnJIOFEzOVZYQWphSWl3RGc3Qlp2NkkrL1NISVFHcWYvWkVVenlwOVZwZFVmVm9BZWlUOE81dURZd2h2cytvNFNMK1Jod0x4NlhJZDlQUGZVV0FnbGMyT0ZnYlB3Zkl4TTZzMCtBVUZheG5MQ1hrdFk3QndZNjBMOG5UNzF0R2lBTURKT1ZGd2tSYnplL05hRUZRRUk1cWIiLCJtYWMiOiIyOTA4ZjA4ZWU2YjUyNTJmMjgyNzEwMWRhZjIyMmZkMGRlNDEyMDZiZDAxZDZmZDg5ZTYwYzg1YWE4NDg2NTBhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:31:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhtQkI0TnFDcUgyZUdxTEY4cWhMVVE9PSIsInZhbHVlIjoiWmZSUlNCbnNOOEo0RGZaVnBJUWlyMHdINGt6dlBjYUZhTGhyVGRVNWJnNEYwcG5rVTZPcGtUWUhpYXA3TU1MTkxFVjFSTVQwL1VqRHl1akhZbzZpaHlnQjhtck5iOXV4aWdoRGFoNyswMHlUNlRJVmh6amh6TlVOc0RCSkJUV1U2V2E4NjU0Vk85Qk9xcjUvMm84bHZudDN0TjN5ejV1V0dmRlVyNGs4dmliWmdVMVVjRWhxVWhqRFhkQm5nRS91WEdZd0JLdjVjWC9kSndGYXpMOFNZWC83S1YxL3BPbklOOXlOZFJjNzhJc2JMbDdjWlJqZFFmN1J5STMxRzkvSzJEYUhvY05EdU9nUmZDWFU5dTVxV25wemJpVlVMcDdEWlZwcUVhUmxqZk81WnkxdWhrOXUxRzlKbVB1clhNWm5OcXB6dzZhVnZNNzBBbTZsaDFjUi9sTld5aDV6VlZzS2Z1L0l4WUJPamkraTdlMmczSEV6REl6MUg4ZWp2ODE2TkRCSS93WjRGZFFxaWxvSlEvemtaMXFMS29WNUpMbG9sTDVJR3p3VzkzUHBGMWwyeHZjTnJKOVNjMjlSNDcyRlhUMkpoZHczc0c5d2VrYmVZVEIrMTVPVHFxY3ZGTGZhbnZ3eFQxcDJpTlYxa1A5NHE2UjZwQmhTYVZzR2J0UjMiLCJtYWMiOiJkMDA4Njk2MWQ5NmY4ZGUzMmI2ZDQ2NDc4Y2FmNzhlZmUyMTBjOWIzMDY3ZDAzYjJjZGViODBkZjkyMjMxYzI0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:31:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370698669\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-309820330 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309820330\", {\"maxDepth\":0})</script>\n"}}