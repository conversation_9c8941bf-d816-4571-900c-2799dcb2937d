{"__meta": {"id": "X90a6c79c2c50fb7961e4c5f4527ab5b2", "datetime": "2025-06-07 07:30:25", "utime": **********.731642, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749281424.72716, "end": **********.731675, "duration": 1.0045149326324463, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1749281424.72716, "relative_start": 0, "end": **********.597609, "relative_end": **********.597609, "duration": 0.8704490661621094, "duration_str": "870ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.597627, "relative_start": 0.870466947555542, "end": **********.731677, "relative_end": 2.1457672119140625e-06, "duration": 0.1340501308441162, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.029550000000000003, "accumulated_duration_str": "29.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6545641, "duration": 0.027370000000000002, "duration_str": "27.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.623}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.701981, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.623, "width_percent": 3.892}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.714807, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.514, "width_percent": 3.486}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281418691%7C5%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjM0TFNHMW5xNU5VOFpkTmF4WER2TVE9PSIsInZhbHVlIjoiZWR2WGdFVGxNdWVBVURTMkNPTWhUVTUrMDFtS1BrSW9GLy85U1dmLzFLSnpOQWV4KzJXM3lxdlNkVjJWeE4xdHZCOThSMFljNW9SUkpFdGFsckcvVmh3ckFaL0dURDJtQzBBVjNNdzRoblQyTERQM1E3dEFqbHpYUTN3cTJoeUxaMG9Mekh2VjZWaDhFU3htTXhPdExmWmFOZEszSlBjbDJwZzh5M0pzN29YM1V6MVBWOUNqYnZSc1JOTm1HTlNpM2UzV0tFZ3hjTHVHK1FEYk82a1cyeERiNndNNlBzOHgzNFZ1blJZNUxSVWwzQm5OR2xqY0N5MWkrd1haRkx4SFJENW84MWhaZENaUndubEszd2hTYnpTOGdieCtYMmpWMHI0MXoyc2VuODdQQlRmZGxzclR0ekhMamgvRitjanlDbUZHeUlPUys5Q1dQVWhobEg3NG1OenZtU2tPL2orT0RhY1Z4c3VDRzJDWDBEK3BhMGQ5NHM3LzMwSC9pbDVSS1ZSbVNoV2ZGblY2STc2cE9aVVBaS0R1NkJNZUtFU1Iyd0p0Z3h5ZVFSalExelQvNkxZTXRRNEZJdG1oYzF3NGJIZXMrTW1LREpzOXF2YmUxeE9DcGszVkIxM0pGTDhFOEVQTkRnSGlOSXg4KzFvOUxja3l6OVBVdWp0czFFbjAiLCJtYWMiOiI2NGNkNmM1MjkyOTg2YTAyMDlhNWVmY2U5NmI5MWY3MjJiN2E5MzlmZWRlNTZhNTdkYTY2NjM5MjI0NjMwNmIxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFHNnFpTU9OUGprRUdmQmxTRzRhSHc9PSIsInZhbHVlIjoiOVM3SkJTRXhzQmlwV0ljemxsMloxa29BN2FJdlNzT0J0aVZTVlpmTzF6SWlPeVJaVWFEZW1UNTVwNnhPbUFndldUai91N3hHTUNGbjdOc2JIdkVYcDJ1YVhRRW4ydWVTWVpQTFN5YWV6Wkc0cHhmY3lFWUJ1SmlsWnJGdHEwUjhpRVdGbW5yZU1TR05zVkhycEFFcnRZS21IaVVHSzdJM2pPeC96WDE2WGE0SFR4emFleVc2YlBVTW1kUDZSTkFqVnpIVU1iQk5meDFHZDJDRWx2cG9wRjhOMURjS2dZdUhwdk5hZHg1Yng3L2N4ZUo2RThqTHphTWIxc1RkbWNzS0pEd3ByTFE5azg1c0ZDTDliSlZ3dE1KMGFiQXJHWDNKTU83UVBJMDdVN2YxelN1MzROMkEvMGFGQWM3ZWw0dllRZE04blduQng4cGtIWWFjbGhMalpOcjYxQnM5MXdBWTNvblo0MklTbnVnUzFDRm9xaHpiYTZTa3ZmMnN5bHVJN0ZucDR2eTkzbWJCOVA0RjZUQnRENXpnd2NRTHRUQlZFSnNKWnFCTzFTSWVLbmFNWWgvVG5hOHpMVnUvMjZrZlZiVlFxbUFYejBlTmNJY1g5cUs2dlFSU0V5TjE1czJZMGQzVitWL0tmb1ZZbzJLa0ZYTzhpQjRjZHZDOGpocVUiLCJtYWMiOiJhZDFjMjU3MzVmZWM5ZDYyNWYwMGQ2ZjMxZmVmOWRlZjg4MTI5MGY4YzZjMTM5ZDgwN2I1YTBhMmY1MzUzOTJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bmHYo9T8TH5q6dUCnl8ZG1w46p5IENlShdAmx5wt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1388626431 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InF4OWtuNC9KUE1iL1RGdnpoV3ZIMEE9PSIsInZhbHVlIjoib3lMaS83TmswRHdPNjJ1YlVYb1lYTGhnL0E0OTZIdm9qRkZqOVBGdTZPMGhkc3pia1FCcXVreVh0ZllwY1RNdFFPWmw4M2R6V3Z5Qnp3UTAyL2ZtYlh6YmtIa2VRVDBrNDVOdko2ZUZXSnc3VjM4R3MxK09UL2hBQldaWFh1eWV3UWw0M2wvZXNZSUV1TEFETUxKbWVId2RiWGRTOWNaNmtRS1dwY2NxbXdrT2FPdVNZbHdUaDJNaThHam5uK2VCMk1sNkpSckpnS2cvUHNuY1g3dWRRSGFnOVZ1cW5QWHJkRnVKaE9nK3UvS2VSN1hSK0gyY2hoNWNNTXY5TUtBNXFUVkZyazFKUUszUGtNaVc5K3hMeHVQY2F4N0VhVGxacnVUN29oRGwyS1JjT1hnZXFnSm5Gek5JRTBzTDBGRjljL2NuZ0k1NXVpb1FmOWkyRTErak4veUlBRmFLTVpxS2hQUWFtZitnZkYyKzNRVnUzdE5peHN2aGR0OEpMK0hSTW9sQlVhSzZZVzVPSCswanVRT1BkQ2oyakUwTENkTGxkVEJMN2ZIN3YzdzUvNHhqRHNERkJ4bUdndzhoZHhKMW9NY3dxVlNQeUxnek80RUFCRXJKSzMwUzRweC91N0NMbUZid21Wc1hSeDFVUFdrTkxRV3paYkx6N3VNRTBBbEEiLCJtYWMiOiIzNDEwYTE5MGJhYzA2ZWVjOGI2ODkxYjIwYzRlMmZlYTg5NjM3MWY0NTE2MGNmNmY3MTg3Y2RiOTUxN2E4NGViIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldMekp4TlV4bm1zWGR3MVcyOFRDSlE9PSIsInZhbHVlIjoibnJHYS9ZMWNoYTRiNlZ5NWR4Vm1iandYS3lFUUk0dldYZmh4WHVyZTBveW5TMXUzK1VnSlkxL014OU5ZL0ZSVUJIclJOdVllVzY2eXJpdkw4akV4Rzk1VURhOW5RTmVpdkovQ1BxbDl0NTNsVHp0cmZzaVBqKzFkQStXODlBeFdaemtNSDRvcFlpUDJqNi8yLzJDdXVZeVVyOXFKQUl0R2FpNTdvNWlXR1RvWFpMMUJnZGVUbjB1RERiWUZrZUxDMEV6eTVDYnJmQ3k1dE91QklIK0s5WTZnOGV4SFZpMFRFUjd1R09DU2VyUVI3dmtsblZSUjkxdmVPd2JFK2dUQVU5RWJhZ1ZaalNCMjNVb0d4STV6TXRCWllUZThLRGM4TjhjWk5HRS9pM1YrQVBKQU81WndITHMrdVVTVXVQQ1hocWNZMGZ3ZVZPTDBEYW9ZTWV3TEF0TEw5NXJQNmQzT01lMnVoWUR6NS9yTW92TVlYTkZaTVNxVmJ3MVJONnRXeGg1NEppK3Vkd3V1YlZuSVRjWTlrOTNoOFE0eUo2S1ZpU0d2L201NnJCTENXRWZtdng3MVNKZmFnbDJxQXpha3U4bXZVWHIrNkdCQ1lnb2F5TlJ6eUdOTlVXM0x6WDZvK0dhR1pzbkZiY1ZheGtqS1h3WmlwS0pnNFZIbHV0OEMiLCJtYWMiOiI3NzAxMjEyMWQwNDhmYzhhN2E5OWUzNmVhOWJhMGM2MDcyYmE3ODY4NmI5ZDMzMzg1YWQzYTFjZmY1ZGZiMzk1IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InF4OWtuNC9KUE1iL1RGdnpoV3ZIMEE9PSIsInZhbHVlIjoib3lMaS83TmswRHdPNjJ1YlVYb1lYTGhnL0E0OTZIdm9qRkZqOVBGdTZPMGhkc3pia1FCcXVreVh0ZllwY1RNdFFPWmw4M2R6V3Z5Qnp3UTAyL2ZtYlh6YmtIa2VRVDBrNDVOdko2ZUZXSnc3VjM4R3MxK09UL2hBQldaWFh1eWV3UWw0M2wvZXNZSUV1TEFETUxKbWVId2RiWGRTOWNaNmtRS1dwY2NxbXdrT2FPdVNZbHdUaDJNaThHam5uK2VCMk1sNkpSckpnS2cvUHNuY1g3dWRRSGFnOVZ1cW5QWHJkRnVKaE9nK3UvS2VSN1hSK0gyY2hoNWNNTXY5TUtBNXFUVkZyazFKUUszUGtNaVc5K3hMeHVQY2F4N0VhVGxacnVUN29oRGwyS1JjT1hnZXFnSm5Gek5JRTBzTDBGRjljL2NuZ0k1NXVpb1FmOWkyRTErak4veUlBRmFLTVpxS2hQUWFtZitnZkYyKzNRVnUzdE5peHN2aGR0OEpMK0hSTW9sQlVhSzZZVzVPSCswanVRT1BkQ2oyakUwTENkTGxkVEJMN2ZIN3YzdzUvNHhqRHNERkJ4bUdndzhoZHhKMW9NY3dxVlNQeUxnek80RUFCRXJKSzMwUzRweC91N0NMbUZid21Wc1hSeDFVUFdrTkxRV3paYkx6N3VNRTBBbEEiLCJtYWMiOiIzNDEwYTE5MGJhYzA2ZWVjOGI2ODkxYjIwYzRlMmZlYTg5NjM3MWY0NTE2MGNmNmY3MTg3Y2RiOTUxN2E4NGViIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldMekp4TlV4bm1zWGR3MVcyOFRDSlE9PSIsInZhbHVlIjoibnJHYS9ZMWNoYTRiNlZ5NWR4Vm1iandYS3lFUUk0dldYZmh4WHVyZTBveW5TMXUzK1VnSlkxL014OU5ZL0ZSVUJIclJOdVllVzY2eXJpdkw4akV4Rzk1VURhOW5RTmVpdkovQ1BxbDl0NTNsVHp0cmZzaVBqKzFkQStXODlBeFdaemtNSDRvcFlpUDJqNi8yLzJDdXVZeVVyOXFKQUl0R2FpNTdvNWlXR1RvWFpMMUJnZGVUbjB1RERiWUZrZUxDMEV6eTVDYnJmQ3k1dE91QklIK0s5WTZnOGV4SFZpMFRFUjd1R09DU2VyUVI3dmtsblZSUjkxdmVPd2JFK2dUQVU5RWJhZ1ZaalNCMjNVb0d4STV6TXRCWllUZThLRGM4TjhjWk5HRS9pM1YrQVBKQU81WndITHMrdVVTVXVQQ1hocWNZMGZ3ZVZPTDBEYW9ZTWV3TEF0TEw5NXJQNmQzT01lMnVoWUR6NS9yTW92TVlYTkZaTVNxVmJ3MVJONnRXeGg1NEppK3Vkd3V1YlZuSVRjWTlrOTNoOFE0eUo2S1ZpU0d2L201NnJCTENXRWZtdng3MVNKZmFnbDJxQXpha3U4bXZVWHIrNkdCQ1lnb2F5TlJ6eUdOTlVXM0x6WDZvK0dhR1pzbkZiY1ZheGtqS1h3WmlwS0pnNFZIbHV0OEMiLCJtYWMiOiI3NzAxMjEyMWQwNDhmYzhhN2E5OWUzNmVhOWJhMGM2MDcyYmE3ODY4NmI5ZDMzMzg1YWQzYTFjZmY1ZGZiMzk1IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388626431\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}