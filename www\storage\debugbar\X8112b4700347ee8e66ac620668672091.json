{"__meta": {"id": "X8112b4700347ee8e66ac620668672091", "datetime": "2025-06-07 04:35:34", "utime": **********.554364, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270933.267788, "end": **********.554394, "duration": 1.2866060733795166, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749270933.267788, "relative_start": 0, "end": **********.402857, "relative_end": **********.402857, "duration": 1.1350691318511963, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.402878, "relative_start": 1.1350901126861572, "end": **********.554398, "relative_end": 4.0531158447265625e-06, "duration": 0.1515200138092041, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45107648, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007500000000000001, "accumulated_duration_str": "7.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.492643, "duration": 0.00455, "duration_str": "4.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.667}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.524686, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.667, "width_percent": 18.133}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.533304, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 78.8, "width_percent": 21.2}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1248232757 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1248232757\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-363025737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-363025737\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1073598554 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073598554\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-697970354 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=xq5jkj%7C2%7Cfwk%7C0%7C1984; _clsk=1p8bbqf%7C1749270908302%7C1%7C1%7Cs.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkZreDNwRFhIaW8vclFnQU5GU285dGc9PSIsInZhbHVlIjoiTlIxR1JmNHpRYkluczgwdnlwd1FLRmxGNGtOdzZQbnFqTmRZWjIrclI5TEZmV2ttWWdnSFk0ZmIrbVFMWHVmbVhEMTFVcVdUdWxFRUhmY3huSGQ0Ykl2bkUvQjFiZ0dkbUluTVlJRkN5VURkZXFDdkM4NFhkUldJSWxSbEJldVpnOXNnTTNmbjNHamMvaWlZUnlzbWVlOFFFYWtRZ2d1b3JSblNNZitQdy9Jck03SUcwTFpFUVBwUHVwY0lzRjBDV2xkOFlXbkk1QlMwVXVodlpFSEI0WWZSUEpzSE41MHVvNi9IRVR4KzZkdnJVcWZ6UFQ1bXhUUzZuMUNtVjAwc1UzcWNrcnFTT0pXRVh2UGRpZ2F0a25kWmFUUWJnOEd5MTJGclpFSEh2YVNzNlJzUllTd1NJTWxYemRZdW1BS1VKNWVuWkV6OTBxUXJiV0RyQy9zdDYrQk5LNm5kRnpMcmVmY1BvUEZmSFZ0UkZmT09hUGFxYU5pQzZKUlpqbnhuaXkwWHFCNElZUFBDaDNHeS9DdHFpZGZTczdNcTlHSG42Ylh0dXRRWVN0dWExNkxyYW9Hd0VCQUp6clNib2QzeFgrUFVCYUFHQkxjQmdnN3ZqcUQ0NU43SW9pZ0NURVBFOHluWC9SaGRWUmxVS3poWS80UjFwUDN6SXowdlZXem0iLCJtYWMiOiIyMjVjMmU3ZDJjOGQ4MTNiODFjMTRlNThmYjI1MzFiNGVkZTZmYjgzMGZhMzQ1MTQ1OTAyNjQxZDAxZGYwZmEyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkgrY21tczVCcm9RaElqeFRxT01McEE9PSIsInZhbHVlIjoiUWgzZmNuVnBRQTIyanIydVkwS1YwR2RFai9JYUs0UTJBVW9pNDMxZFZXMUNpY2lRS2VHRytNeFhHak1ubW1RRkdxRkZuaTZacVo5ZHdYV0lNWkF3dzNHdFZFMk9xT3UxU1A0VnllaTBMcGxKMCs2ai9QODBoRHBiQXFodjVTRlpMQlRCVC9EbDV5T2hUWWh2bjhEcllsVzIzUVFpODJDYWdxNHBWWTBmbmZBZkRIV1U3dDB4Q2pCbUx2bDlHMjljOHNPL0E4VnVpMFpSdWZ4M1g5VCt6ajZweXZEK0hTU2swdzN5VmVBRmlYUkVrQXU1Tkk3S1RlUmhRNVBLblEvdWZiYkd2b2NpTFFqQkN6anlXbVpHTWJFeHpDUHJkMTdQR3QyRkdDTW51dTdUbnZFZGk3aUJjdk9vZ0kxdDloM2Q0RWlHS3VCTWFkTzUwNFJ2VGdJNnlNYTA4Y2hoUll6M1cwMTk5SHNsc25udS9qOFRSVTBlazFHeVQxL3p3Q2NRWGQ4YlN2aU5WeG56UmpTdUxSNmd4ODNpMm9OeSt3RGJQd0RUS2QrTzhmdFdUcDkxU1UzeW1QVzMwTEJZZFR2N0VSQjNUMDIxa0tBNnk0enpnR2s0ci9KZTF2bmVDcm81TlRKNXpad2JzN0pxTXFDbWdJWEJBbUhQbStwSUwrblAiLCJtYWMiOiI5MDU0ZGUyNzljODE2MDA2NTMwMzFkNThiZDhkNmQxZjQzNjJmZDhkMTA0NDI4NWU2NjliZjRjMjU5ZWE0M2E5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697970354\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1459459070 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z1ibAMoy16lMUkDWMem3ATVFxZVkTly8GBk5DGQF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459459070\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:35:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpNKzB3M2orNHU5UlV1T3Z4aHlqdVE9PSIsInZhbHVlIjoiUHZtYy8zamorUXM4aGViOVRGMWY1WWRXY21Ib24yNk5uaGIrSjArOVJLNDNLbno1cDB4bkpqUEMxUkwyWUliTWxPZGNIMHpRdldTSFczUjJyczNYYlhIY0xSUVVvblVwbHFNbEh2d01XdHhoVExqRW9UOUVnd21ESHkxRmJzVWxOZkJUVWpqYUdsc0hOT1hxbnpobEpKVjQrbTB0OFBQVlpnVSsyK0dJMXV4enRJT2hYL21PcWh0SzhTSTBRVTI1aDFKT3NDUy9MRVJER1h0VUJZQ3FUeGpIUXBjQ0VGSW0zMktCUTh3MExhbXNEZnhsU09ZL2JEWHYvU0RCeDl4SDAzZHJzcjRMU1laOC91cDVYb3MrZUJiSFBlUnpJZ0ZNRnhiV3QxU3cyOW9vM0RZdjJRQmhURjZ5VkVNOUVka3B5S3BGQ2lHMGQwWEtOcWdFdEVUaWcrUHFrM25xallXQWZGT0pHMW5ubHYrTWZqMER1WEJwajNRU3UwODUzT1pxQXVlaGlydi9GS3NtYnVablVVSVNTb1E2dmgyWkJGV3JJbU93WGl5S0NuS0ZZUVNCdTBucUwxTWd6b0ZGYmQwSENTNnRlcEdRZElCVnRIMVlGZWRVTUd6VnAvMExibmFHVGdRamhnZnhuenpUMXpaazY1alVXZWhLQzJYUWgrZ24iLCJtYWMiOiIzZmVlOTNjOGZhY2MzOTlmODAzOWQyZTMzNDYzM2NhNTFjZGFjZDg4OTUwMjNhMGE4NTRhZWZiZmQ3YzI3ZTU0IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1XakQvMjRod245RXJxTmJHZmlpdUE9PSIsInZhbHVlIjoiZDFZN29pTDhrTzZZWEFpSDFidUxQMUErR0M1VUE1M2RkUW1HWDlLTG4wMFZodmlPbUlGSEsvZE0vMmhlcENFWVJTczJNVHpLTEk4RW5rejVKSGVFYThvcXhDWEhRTnNSVHFYWW9zRmErd3F1WGFqaDY1clJrSGo2NnZZbWtWMWY0VldtY3JoanpBRTIvQ24vOTdVSnlid3NHSGVlZ3BpcVUySE5DWFRsaUJmNVZHVURGR1FzbTBqMzhCbktKUjBBSnBTQXBpWVhqSlJYeDROc3ZtSExsSGN3TGZCa0dFRTFwLzdTRVZ5TDNFbS9ncXRqU3FWU1NoazFFYzBBb21TcDNYREV2cDNwRmNvZkUwOHI4dW9qOXdzWnFhZlJVSXZJQWlOdUVHTk9UUTd0THJaYld0VDlWcktkeStiYjJrM3pQMlFzNWJTY3d1MnA3Qmt2Vzc2UWk0VXhTVWVCdFpCMlpDWGx3SHM0cm9CNnBrTy8xeGFCaXhibmxBM3NSaVVUT3JudzFBQ0tNUHF4WEp4bkJSY2xxN3NFQkQvTTVxTThQN1pmWkR1czVzQWlWUE5Tdkx0U04rV1YvWTF2OXRqbWV4WXFiZmJuRWZsU2t6UnRTVzJHQ2pEWlNYZmJCTkVuZ2dDVzZmVGs5ejVSck4yWFhwc3lUMllBb081NFRvbWQiLCJtYWMiOiI3YzFkZjBlZjhkZTAxNTIzMzFiMDZjMjZjNGMwNmY1ODRmOTk1NDU3Y2Q3NTA5OTdiNzlkNjc0MWJjNjllYmUwIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpNKzB3M2orNHU5UlV1T3Z4aHlqdVE9PSIsInZhbHVlIjoiUHZtYy8zamorUXM4aGViOVRGMWY1WWRXY21Ib24yNk5uaGIrSjArOVJLNDNLbno1cDB4bkpqUEMxUkwyWUliTWxPZGNIMHpRdldTSFczUjJyczNYYlhIY0xSUVVvblVwbHFNbEh2d01XdHhoVExqRW9UOUVnd21ESHkxRmJzVWxOZkJUVWpqYUdsc0hOT1hxbnpobEpKVjQrbTB0OFBQVlpnVSsyK0dJMXV4enRJT2hYL21PcWh0SzhTSTBRVTI1aDFKT3NDUy9MRVJER1h0VUJZQ3FUeGpIUXBjQ0VGSW0zMktCUTh3MExhbXNEZnhsU09ZL2JEWHYvU0RCeDl4SDAzZHJzcjRMU1laOC91cDVYb3MrZUJiSFBlUnpJZ0ZNRnhiV3QxU3cyOW9vM0RZdjJRQmhURjZ5VkVNOUVka3B5S3BGQ2lHMGQwWEtOcWdFdEVUaWcrUHFrM25xallXQWZGT0pHMW5ubHYrTWZqMER1WEJwajNRU3UwODUzT1pxQXVlaGlydi9GS3NtYnVablVVSVNTb1E2dmgyWkJGV3JJbU93WGl5S0NuS0ZZUVNCdTBucUwxTWd6b0ZGYmQwSENTNnRlcEdRZElCVnRIMVlGZWRVTUd6VnAvMExibmFHVGdRamhnZnhuenpUMXpaazY1alVXZWhLQzJYUWgrZ24iLCJtYWMiOiIzZmVlOTNjOGZhY2MzOTlmODAzOWQyZTMzNDYzM2NhNTFjZGFjZDg4OTUwMjNhMGE4NTRhZWZiZmQ3YzI3ZTU0IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1XakQvMjRod245RXJxTmJHZmlpdUE9PSIsInZhbHVlIjoiZDFZN29pTDhrTzZZWEFpSDFidUxQMUErR0M1VUE1M2RkUW1HWDlLTG4wMFZodmlPbUlGSEsvZE0vMmhlcENFWVJTczJNVHpLTEk4RW5rejVKSGVFYThvcXhDWEhRTnNSVHFYWW9zRmErd3F1WGFqaDY1clJrSGo2NnZZbWtWMWY0VldtY3JoanpBRTIvQ24vOTdVSnlid3NHSGVlZ3BpcVUySE5DWFRsaUJmNVZHVURGR1FzbTBqMzhCbktKUjBBSnBTQXBpWVhqSlJYeDROc3ZtSExsSGN3TGZCa0dFRTFwLzdTRVZ5TDNFbS9ncXRqU3FWU1NoazFFYzBBb21TcDNYREV2cDNwRmNvZkUwOHI4dW9qOXdzWnFhZlJVSXZJQWlOdUVHTk9UUTd0THJaYld0VDlWcktkeStiYjJrM3pQMlFzNWJTY3d1MnA3Qmt2Vzc2UWk0VXhTVWVCdFpCMlpDWGx3SHM0cm9CNnBrTy8xeGFCaXhibmxBM3NSaVVUT3JudzFBQ0tNUHF4WEp4bkJSY2xxN3NFQkQvTTVxTThQN1pmWkR1czVzQWlWUE5Tdkx0U04rV1YvWTF2OXRqbWV4WXFiZmJuRWZsU2t6UnRTVzJHQ2pEWlNYZmJCTkVuZ2dDVzZmVGs5ejVSck4yWFhwc3lUMllBb081NFRvbWQiLCJtYWMiOiI3YzFkZjBlZjhkZTAxNTIzMzFiMDZjMjZjNGMwNmY1ODRmOTk1NDU3Y2Q3NTA5OTdiNzlkNjc0MWJjNjllYmUwIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}