<?php
/**
 * Route للتشخيص المتقدم - يجب إضافته في web.php
 */

// إضافة هذا الكود في routes/web.php

Route::get('/debug-forms', function () {
    $user = Auth::user();
    
    if (!$user) {
        return response()->json(['error' => 'المستخدم غير مسجل دخول'], 401);
    }
    
    // جمع معلومات المستخدم
    $userInfo = [
        'id' => $user->id,
        'name' => $user->name,
        'email' => $user->email,
        'type' => $user->type,
        'roles' => $user->getRoleNames()->toArray(),
        'created_at' => $user->created_at
    ];
    
    // جمع جميع النماذج
    $allForms = \App\Models\Form::with('creator')->get();
    
    // جمع النماذج المرئية للمستخدم الحالي
    $visibleForms = \App\Models\Form::getVisibleForms();
    
    // تحليل النماذج
    $formsAnalysis = [];
    foreach ($allForms as $form) {
        $canView = $form->canUserView($user);
        $formsAnalysis[] = [
            'id' => $form->id,
            'name' => $form->name,
            'type' => $form->type,
            'visible_to_roles' => $form->visible_to_roles,
            'creator' => $form->creator ? $form->creator->name : 'غير محدد',
            'can_user_view' => $canView,
            'reason' => $canView ? 'مسموح' : 'غير مسموح'
        ];
    }
    
    return response()->json([
        'user_info' => $userInfo,
        'total_forms' => $allForms->count(),
        'visible_forms_count' => $visibleForms->count(),
        'operational_forms' => $visibleForms->where('type', 'operational')->count(),
        'financial_forms' => $visibleForms->where('type', 'financial')->count(),
        'forms_analysis' => $formsAnalysis,
        'visible_forms' => $visibleForms->map(function($form) {
            return [
                'id' => $form->id,
                'name' => $form->name,
                'type' => $form->type,
                'visible_to_roles' => $form->visible_to_roles,
                'creator' => $form->creator ? $form->creator->name : 'غير محدد'
            ];
        })
    ], 200, [], JSON_UNESCAPED_UNICODE);
})->middleware('auth');

// Route لاختبار النماذج بصيغة HTML
Route::get('/test-forms-display', function () {
    $user = Auth::user();
    
    if (!$user) {
        return redirect()->route('login');
    }
    
    $forms = \App\Models\Form::getVisibleForms();
    $operationalForms = $forms->where('type', 'operational');
    $financialForms = $forms->where('type', 'financial');
    
    $html = "<!DOCTYPE html>";
    $html .= "<html dir='rtl' lang='ar'>";
    $html .= "<head><meta charset='UTF-8'><title>اختبار عرض النماذج</title>";
    $html .= "<style>body{font-family:Arial;margin:20px;} .card{border:1px solid #ddd;margin:10px 0;padding:15px;} .badge{padding:4px 8px;background:#007bff;color:white;border-radius:4px;margin:2px;}</style>";
    $html .= "</head><body>";
    
    $html .= "<h1>اختبار عرض النماذج للمستخدم: {$user->name}</h1>";
    $html .= "<p><strong>نوع المستخدم:</strong> {$user->type}</p>";
    $html .= "<p><strong>الأدوار:</strong> " . implode(', ', $user->getRoleNames()->toArray()) . "</p>";
    
    $html .= "<div class='card'>";
    $html .= "<h2>النماذج التشغيلية ({$operationalForms->count()})</h2>";
    if ($operationalForms->count() > 0) {
        foreach ($operationalForms as $form) {
            $html .= "<div style='border:1px solid #eee;padding:10px;margin:5px 0;'>";
            $html .= "<strong>{$form->name}</strong><br>";
            $html .= "منشئ: {$form->creator->name ?? 'غير محدد'}<br>";
            $html .= "الأدوار: ";
            foreach ($form->visible_to_roles as $role) {
                $html .= "<span class='badge'>{$role}</span> ";
            }
            $html .= "</div>";
        }
    } else {
        $html .= "<p>لا توجد نماذج تشغيلية متاحة</p>";
    }
    $html .= "</div>";
    
    $html .= "<div class='card'>";
    $html .= "<h2>النماذج المالية ({$financialForms->count()})</h2>";
    if ($financialForms->count() > 0) {
        foreach ($financialForms as $form) {
            $html .= "<div style='border:1px solid #eee;padding:10px;margin:5px 0;'>";
            $html .= "<strong>{$form->name}</strong><br>";
            $html .= "منشئ: {$form->creator->name ?? 'غير محدد'}<br>";
            $html .= "الأدوار: ";
            foreach ($form->visible_to_roles as $role) {
                $html .= "<span class='badge'>{$role}</span> ";
            }
            $html .= "</div>";
        }
    } else {
        $html .= "<p>لا توجد نماذج مالية متاحة</p>";
    }
    $html .= "</div>";
    
    $html .= "</body></html>";
    
    return $html;
})->middleware('auth');

// Route لاختبار إنشاء نموذج تجريبي
Route::get('/create-test-form', function () {
    $user = Auth::user();
    
    if (!$user || $user->type != 'company') {
        return response()->json(['error' => 'غير مصرح لك بإنشاء النماذج'], 403);
    }
    
    try {
        // إنشاء نموذج تجريبي
        $form = \App\Models\Form::create([
            'name' => 'نموذج تجريبي - ' . now()->format('Y-m-d H:i:s'),
            'type' => 'operational',
            'file_path' => 'forms/test_form.pdf', // ملف وهمي
            'visible_to_roles' => ['Cashier', 'Accountant'],
            'created_by' => $user->id
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء النموذج التجريبي بنجاح',
            'form' => [
                'id' => $form->id,
                'name' => $form->name,
                'type' => $form->type,
                'visible_to_roles' => $form->visible_to_roles,
                'creator' => $user->name
            ]
        ], 200, [], JSON_UNESCAPED_UNICODE);
        
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'فشل في إنشاء النموذج التجريبي',
            'message' => $e->getMessage()
        ], 500, [], JSON_UNESCAPED_UNICODE);
    }
})->middleware('auth');

?>
