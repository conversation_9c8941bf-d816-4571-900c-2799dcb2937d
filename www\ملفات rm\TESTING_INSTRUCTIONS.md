# تعليمات اختبار ميزات معالجة فواتير المستودع

## 🧪 خطوات الاختبار المحدثة

### الخطوة 0: اختبار المسارات والنظام
```bash
# في Terminal:
php artisan tinker
include 'test_routes.php';
exit
```

### الخطوة 1: فتح صفحة معالجة فواتير المستودع
1. انتقل إلى: `warehouse-purchase-processing`
2. **لاحظ الحقول القابلة للتعديل** - يجب أن تكون:
   - مميزة بخط متقطع أزرق سميك
   - تظهر أيقونة 📝 عند التمرير
   - تتغير خلفيتها عند التمرير
3. افتح **Developer Tools** (اضغط F12)
4. انتقل إلى تبويب **Console**

### الخطوة 2: فحص رسائل التشخيص
يجب أن تظهر الرسائل التالية في Console:
```
jQuery loaded: true
CSRF Token: [token value]
Editable fields found: [number > 0]
DataTable redrawn. Editable fields found: [number > 0]
```

### الخطوة 3: اختبار التعديل المباشر
1. **انقر على حقل المورد** (العمود الثاني - يجب أن يكون مميز بخط متقطع)
2. يجب أن تظهر رسائل:
   ```
   Editable field clicked!
   Field data: {field: "vender_id", type: "select", ...}
   Loading options for field: vender_id
   Options response: {success: true, options: [...]}
   ```
3. يجب أن يظهر **قائمة منسدلة** للموردين مع أزرار حفظ وإلغاء

### الخطوة 4: اختبار صفحة تعديل المنتجات
1. **انقر على أيقونة الحزمة الخضراء** (📦)
2. يجب أن تفتح صفحة جديدة
3. افتح **Developer Tools** في الصفحة الجديدة
4. يجب أن تظهر رسائل:
```
Edit Products Page - jQuery loaded: true
CSRF Token: [token value]
Editable fields found: [number > 0]
```

### الخطوة 5: اختبار تعديل المنتجات
1. **انقر على حقل الكمية** في أي منتج
2. يجب أن تظهر رسالة: `Product field clicked!`
3. يجب أن يظهر **حقل إدخال رقمي**

## 🔍 استكشاف الأخطاء

### إذا لم تظهر رسائل التشخيص:
- تأكد من أن JavaScript مفعل
- تأكد من تحميل jQuery
- تحقق من وجود أخطاء في Console

### إذا لم يعمل التعديل المباشر:
1. **تحقق من CSRF Token**:
```javascript
console.log($('meta[name="csrf-token"]').attr('content'));
```

2. **تحقق من الحقول القابلة للتعديل**:
```javascript
console.log($('.editable-field').length);
$('.editable-field').each(function(i, el) {
    console.log(i, $(el).data());
});
```

3. **تحقق من Event Listeners**:
```javascript
// في Console، اكتب:
$('.editable-field').first().click();
```

### إذا ظهرت أخطاء 419 (CSRF):
أضف في `<head>`:
```html
<meta name="csrf-token" content="{{ csrf_token() }}">
```

### إذا ظهرت أخطاء 404 (Route Not Found):
تحقق من المسارات:
```bash
php artisan route:list | grep warehouse-purchase-processing
```

### إذا ظهرت أخطاء 500 (Server Error):
تحقق من Laravel logs:
```bash
tail -f storage/logs/laravel.log
```

## 📋 قائمة التحقق السريع

### ✅ الملفات المطلوبة:
- [ ] `app/Http/Controllers/WarehousePurchaseProcessingController.php`
- [ ] `resources/views/warehouse_purchase_processing/index.blade.php`
- [ ] `resources/views/warehouse_purchase_processing/edit_products.blade.php`
- [ ] `routes/web.php` (المسارات الجديدة)

### ✅ المسارات المطلوبة:
- [ ] `GET warehouse-purchase-processing` ✓
- [ ] `POST warehouse-purchase-processing/update-inline` ✓
- [ ] `GET warehouse-purchase-processing/field-options` ✓
- [ ] `GET warehouse-purchase-processing/{id}/edit-products` ✓
- [ ] `POST warehouse-purchase-processing/update-product` ✓
- [ ] `POST warehouse-purchase-processing/add-product` ✓
- [ ] `DELETE warehouse-purchase-processing/delete-product` ✓

### ✅ JavaScript المطلوب:
- [ ] jQuery محمل ✓
- [ ] CSRF Token موجود ✓
- [ ] Event Handlers مسجلة ✓
- [ ] AJAX Calls تعمل ✓

## 🚀 اختبار سريع

### اختبار 1: التعديل المباشر
```javascript
// في Console:
$('.editable-field').first().trigger('click');
```

### اختبار 2: AJAX Call
```javascript
// في Console:
$.get('/warehouse-purchase-processing/field-options?field=vender_id')
    .done(function(data) { console.log('Success:', data); })
    .fail(function(xhr) { console.log('Error:', xhr.responseText); });
```

### اختبار 3: Form Submission
```javascript
// في صفحة تعديل المنتجات:
$('#addProductForm').trigger('submit');
```

## 📞 إذا استمرت المشكلة

أرسل لي:
1. **رسائل Console** (كاملة)
2. **Network Tab** (عند النقر على حقل)
3. **Laravel Logs** (آخر 20 سطر)
4. **Browser Version** و **Operating System**

## 🔄 إعادة التشغيل

```bash
# مسح Cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# إعادة تشغيل Server
php artisan serve
```

## 💡 نصائح

1. **استخدم Chrome أو Firefox** للاختبار
2. **امسح Cache المتصفح** (Ctrl+Shift+R)
3. **تأكد من عدم وجود Ad Blockers** تمنع JavaScript
4. **جرب في وضع Incognito** لتجنب تداخل Extensions
