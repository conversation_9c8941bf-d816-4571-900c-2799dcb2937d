{"__meta": {"id": "Xa8a1d8ed684b7856be5249ba797c3f3e", "datetime": "2025-06-06 19:40:22", "utime": **********.958492, "method": "PUT", "uri": "/users/17", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238821.42959, "end": **********.958528, "duration": 1.5289380550384521, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1749238821.42959, "relative_start": 0, "end": **********.59431, "relative_end": **********.59431, "duration": 1.164720058441162, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.59433, "relative_start": 1.1647400856018066, "end": **********.958533, "relative_end": 5.0067901611328125e-06, "duration": 0.36420297622680664, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51227888, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT users/{user}", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.update", "controller": "App\\Http\\Controllers\\UserController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=252\" onclick=\"\">app/Http/Controllers/UserController.php:252-315</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.05538, "accumulated_duration_str": "55.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.681003, "duration": 0.02804, "duration_str": "28.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 50.632}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.740639, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 50.632, "width_percent": 2.059}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.748719, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 52.691, "width_percent": 2.42}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.801115, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 55.11, "width_percent": 2.618}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.808332, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 57.728, "width_percent": 2.329}, {"sql": "select * from `users` where `users`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 284}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.823087, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "UserController.php:284", "source": "app/Http/Controllers/UserController.php:284", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=284", "ajax": false, "filename": "UserController.php", "line": "284"}, "connection": "ty", "start_percent": 60.058, "width_percent": 2.311}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>' and `id` <> '17'", "type": "query", "params": [], "bindings": ["<EMAIL>", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.870655, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 62.369, "width_percent": 1.914}, {"sql": "select * from `roles` where `id` = '17' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["17", "web"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 120}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 298}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8802059, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": "vendor/spatie/laravel-permission/src/Models/Role.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "ty", "start_percent": 64.283, "width_percent": 1.968}, {"sql": "update `users` set `warehouse_id` = '7', `users`.`updated_at` = '2025-06-06 19:40:22' where `id` = 17", "type": "query", "params": [], "bindings": ["7", "2025-06-06 19:40:22", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 301}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.888872, "duration": 0.0111, "duration_str": "11.1ms", "memory": 0, "memory_str": null, "filename": "UserController.php:301", "source": "app/Http/Controllers/UserController.php:301", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=301", "ajax": false, "filename": "UserController.php", "line": "301"}, "connection": "ty", "start_percent": 66.251, "width_percent": 20.043}, {"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 3059}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.907473, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3059", "source": "app/Models/Utility.php:3059", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=3059", "ajax": false, "filename": "Utility.php", "line": "3059"}, "connection": "ty", "start_percent": 86.295, "width_percent": 2.546}, {"sql": "update `employees` set `name` = '<PERSON><PERSON><PERSON>', `email` = '<EMAIL>', `employees`.`updated_at` = '2025-06-06 19:40:22' where `user_id` = 17", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "<EMAIL>", "2025-06-06 19:40:22", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 3061}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.919826, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3061", "source": "app/Models/Utility.php:3061", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=3061", "ajax": false, "filename": "Utility.php", "line": "3061"}, "connection": "ty", "start_percent": 88.841, "width_percent": 8.902}, {"sql": "select * from `model_has_roles` where `model_has_roles`.`model_id` = 17 and `model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.935805, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 97.743, "width_percent": 2.257}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-244588177 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244588177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.821672, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/17", "status_code": "<pre class=sf-dump id=sf-dump-663901453 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-663901453\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-638500962 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-638500962\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1571052997 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Sagheer <PERSON></span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571052997\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-634004561 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">141</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238801714%7C57%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJZa1YrK1AzUG9NZWRQdWZoZU54QWc9PSIsInZhbHVlIjoiNTR3dzdMZDNsNVpRQ203VVd0alowT3J1SXJrRjU3dE0xTWN0cFhmeVk1RHZlRlRtMlZXaXBMMTMxay8va2x2UllzdWRKWFFxYzhFUW1DODdvZHZnVmhTb3JpTndJVzhVeWVwb3d0eHdzWWk2eTR3by9RYkdNbDZ1ZHpqRXhIYVhGNmV6RCtIOWtlcXRqZW9OR2N2TkhoZnRQSjkvNGduekQrRUszeGttVEtnbW1SVjJocGxJS0g2Zm9BbkNSRUZvdVdnUnF4Y0JibzFidUVKK1BNaDI3d3d6dlZDWHQ5TFdJdTRKakdTOWhHbS9zRmU4em94K1JybXh4MExwbGQvaHBHUHNBRGQ1VmhNMEdNbUVnZ01YbDZscFlzemJTZE1qT0NBR05hSkdtZmZ2by9DeVNEaGh2cDdmT2dNem4xTCtHd3R6ZWNISUYrM2Q2TXhZcy92NmZoeEU2aVZuUEZhZ0hxTXEzbElKUFU2djJoQVowZ1V1WmZGLzVrYXd5RFdZaVZ2cXlRbll4WVlmN3doUGlNck05cm9vc0FwVjVLeFhjZEVWaDlITWZHdGYxdUNBdHg3YW5FVy96dkErTWkwNVlqakJpNnlmWTJnQkFBTVBQWE1wcHRuUFY1VldZS2cvSzlra0k5OEQ2YmRWTnRPM0lWZE5hVmM5VTJhcGhPTkciLCJtYWMiOiIwNzFlOGUzMDFiNWJlNDhmZGM5OTk5MjUwODQxZWEwZWJmNzYyYWQ4ODkzZDVkNWJkY2VlMTA1Yzg1ZWUwZjA5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlV0OEgxd2JlbWova0xld2Q1QURsU2c9PSIsInZhbHVlIjoiZGdJZlI0WkxFWGYwa2wxNDE4a25tWnhKN0c0MjRIai9PV0VLRG5oek4zbExnVlhmTGpqR01CNVUvMDQ2ekx1dDhKZ3VnUWFHM2syTnZ0VW1OZW0xZ2IzUEc1SnFvdngzVnkrTFRLUWFpM0R1bnNjcExYSGZaRmlJcG9EUUJ3YnNOajRpUWR6QlY0S09IMDRIOHVtSmxRR1c2Zk1KckJJcE5McEdCZHBWNlZoVlJWNXovOG1pL2JxMWZrQXhOa2gxTzZkbE9ZYWNFRk9wcis1bUhYeERCODBBeWRnUlZhZWJjNncxdFl4WWhpUlhSOEVCTFB0Z05BN2hjeU9SZ1ZqdXZNSWtzTTRLWHV3dHM2aDFQR3Y1bmVzcUV2RW16RHI4enByRCthZ1V6RjV3Z1FZZnVxUS9JZFlQNTE3M3BTNGR2N0hkdU9qOG1TSTZqSmx6WGlOWGlRUEVUSkVTL21lUjhmT1dKNS82VkhpZDRUOTdFOVB6UGJCY1JoYlVYWTZUUnBkTVFtTVdRQVlJNm94UHBOc3Jub1ZkTnd2M3JPVWE5eE8zME8zRkRlUGFYSU9GWWt2S05XRzM3TGN1VEZiZ2dZSVBFWDdENEprNDRROU1FT0pqUnUrRTlxTlkzWVZyWHk3NTdmaEFjeFZvRnZiTGZ3UXNNQnNmSU41eEhLREIiLCJtYWMiOiI4ZmFmYTMyMzcyMDMwYmQ1ZjYwOWExNGU1NTNlMGI5MDQ2NDIxMWY0ODY1YmIwNjQ1N2Q2NWU2NDJiM2M0NmMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634004561\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1736636203 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736636203\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-561977577 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:40:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjcwT0YvN3ZxK2lFaVdWLzlqZ3hYcnc9PSIsInZhbHVlIjoidmtuZ2NqRXNya0pMNnZLT3FYZEVxaEkzbEhhOGhvL3dOUUlMUEFtOEF3UXhFaTcwV2NhVkJNN0RSQ2M4Rm53V2lsY0svOWRGakdEbkI3UUJkZkY5aC96U2xEd3FCSXloRXhjTlVxN3dIdnJUblJxcEg5SEVNUk9oTDNvZDZUQ2ltbkpDOUxid1FMTE1Vekdsd2F2TnB6b0doRjBqT0loaHZSSWVySXppOTlIT2UwSnJRZElxbkF6RE53YzBqSnVkTlZ2bUQvZklDWmZTMTBKNS90VWE1enI4TzFMTUdxRnllNjd5cU9rSXZpL3dNdW9COHIzQ2hOSEFEVi9GSStVUGplYmdqcmNKZm1hNDJhVUxXN25uWUxIZjNwY1Z5bFVpblVJRkZKWk1ra1RGVlJKRzNmaXRhWjUzQnJjeUVNUFIwekFEVmxFak1nSURUdG45VnpHNE8wOEZ4bytmMFV5NlgwbHpNdlIrRm1MMGlNN2ZHbVRVT01pUURiTDNPdC8xRGMweHI3R0l3TitQYUgxRG85cTYxUzhxMnU1V2dYNTdOblZOUk0yMHd4TmszNmF0QnZhUEF4SmlUd2V1Q3hWWGNDbHZWZjZPc3h6TzBPcEZLSVdzQ2czNG1sak1WNWQ0SC84UUhOTE9iZWE4MTFPM0p0cVUwcWQrMGdhZ1BnekkiLCJtYWMiOiI0MDZhMmQ0OWMyMGJmNmYxMjBlYWIyZDhhNTRjZDMxNTVlOWRiYWI3MTkxMDNiMjhmYzJiMzQ1MTJmNmQyMWI3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:40:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndBYmlwLzRWdTlXUzdBVUE5eEF4amc9PSIsInZhbHVlIjoiTTc1aFdvUUpsYVZVSkR6QlZMWEJ3N3h2TDdHVmttbmt5dlFhNm5ZRFJaZkZyQW1SQ29LTnlKaFhkTGNTR0tPblFRTmpjZU9NdVVOQk9WNWcwbVlYeW5HSi9JSnlMVEFkWmFCNng0SllKaWt6cVBPRFlUQ1Q0N3c2dnU0VDVpeFA2cndsL0d6OXVNM1ZZTHBSb2VMTFhpU2I5dzlYa2VZdUhJUkwrMi9mZFV0ZSs5VDhhRFJUNEV4QWxzQlNrU0FnRXpGaTErZEdkcG4xWlNkcXVIdXUyMysxcFhyd3M0LzZ1MVlFYytsZE9QcTF5MVJFQkswY1V2aC9oTWlDdkxZcTIxYitreW5sN2dzMVI2R2Uxay9MYjd1bmZvemJVMXUrMm1QeFgrcEhWODZWZFRZTkcrbVJ2R3EwamI5ZDVSR2pWUTBab3lRdzRLR0daZ2k3enJBcVhsRzlMTHJJWXZobVlBR2tZNlJFbW1JQTRhdlh0dGF1UkZ6SmJNUVdYYVJqUEVubWdQUkprVWp3ai9mdDNXVlhLblVCVytRUlpTTlRpRXdFdlpadFlkV3RSZHo3Z21HbVBZeGFUVXVidjdidnI2Yk9haUNNakxSbktMMEVwYlBudHNoTGtEQkNiMnZXWEVqZS9zQnFlRmN0SDQwQWdKZExjUXVOcStkNXRXelQiLCJtYWMiOiJjNjI3MzlhNWFlYjg5NzM4YzM1OGViOTY1MDVhY2YxNjFhZjE1MGM2Mzk0NzZkNGFhNjE5ZjFiYzJhMGJkNmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:40:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjcwT0YvN3ZxK2lFaVdWLzlqZ3hYcnc9PSIsInZhbHVlIjoidmtuZ2NqRXNya0pMNnZLT3FYZEVxaEkzbEhhOGhvL3dOUUlMUEFtOEF3UXhFaTcwV2NhVkJNN0RSQ2M4Rm53V2lsY0svOWRGakdEbkI3UUJkZkY5aC96U2xEd3FCSXloRXhjTlVxN3dIdnJUblJxcEg5SEVNUk9oTDNvZDZUQ2ltbkpDOUxid1FMTE1Vekdsd2F2TnB6b0doRjBqT0loaHZSSWVySXppOTlIT2UwSnJRZElxbkF6RE53YzBqSnVkTlZ2bUQvZklDWmZTMTBKNS90VWE1enI4TzFMTUdxRnllNjd5cU9rSXZpL3dNdW9COHIzQ2hOSEFEVi9GSStVUGplYmdqcmNKZm1hNDJhVUxXN25uWUxIZjNwY1Z5bFVpblVJRkZKWk1ra1RGVlJKRzNmaXRhWjUzQnJjeUVNUFIwekFEVmxFak1nSURUdG45VnpHNE8wOEZ4bytmMFV5NlgwbHpNdlIrRm1MMGlNN2ZHbVRVT01pUURiTDNPdC8xRGMweHI3R0l3TitQYUgxRG85cTYxUzhxMnU1V2dYNTdOblZOUk0yMHd4TmszNmF0QnZhUEF4SmlUd2V1Q3hWWGNDbHZWZjZPc3h6TzBPcEZLSVdzQ2czNG1sak1WNWQ0SC84UUhOTE9iZWE4MTFPM0p0cVUwcWQrMGdhZ1BnekkiLCJtYWMiOiI0MDZhMmQ0OWMyMGJmNmYxMjBlYWIyZDhhNTRjZDMxNTVlOWRiYWI3MTkxMDNiMjhmYzJiMzQ1MTJmNmQyMWI3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:40:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndBYmlwLzRWdTlXUzdBVUE5eEF4amc9PSIsInZhbHVlIjoiTTc1aFdvUUpsYVZVSkR6QlZMWEJ3N3h2TDdHVmttbmt5dlFhNm5ZRFJaZkZyQW1SQ29LTnlKaFhkTGNTR0tPblFRTmpjZU9NdVVOQk9WNWcwbVlYeW5HSi9JSnlMVEFkWmFCNng0SllKaWt6cVBPRFlUQ1Q0N3c2dnU0VDVpeFA2cndsL0d6OXVNM1ZZTHBSb2VMTFhpU2I5dzlYa2VZdUhJUkwrMi9mZFV0ZSs5VDhhRFJUNEV4QWxzQlNrU0FnRXpGaTErZEdkcG4xWlNkcXVIdXUyMysxcFhyd3M0LzZ1MVlFYytsZE9QcTF5MVJFQkswY1V2aC9oTWlDdkxZcTIxYitreW5sN2dzMVI2R2Uxay9MYjd1bmZvemJVMXUrMm1QeFgrcEhWODZWZFRZTkcrbVJ2R3EwamI5ZDVSR2pWUTBab3lRdzRLR0daZ2k3enJBcVhsRzlMTHJJWXZobVlBR2tZNlJFbW1JQTRhdlh0dGF1UkZ6SmJNUVdYYVJqUEVubWdQUkprVWp3ai9mdDNXVlhLblVCVytRUlpTTlRpRXdFdlpadFlkV3RSZHo3Z21HbVBZeGFUVXVidjdidnI2Yk9haUNNakxSbktMMEVwYlBudHNoTGtEQkNiMnZXWEVqZS9zQnFlRmN0SDQwQWdKZExjUXVOcStkNXRXelQiLCJtYWMiOiJjNjI3MzlhNWFlYjg5NzM4YzM1OGViOTY1MDVhY2YxNjFhZjE1MGM2Mzk0NzZkNGFhNjE5ZjFiYzJhMGJkNmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:40:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561977577\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-482961721 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">User successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482961721\", {\"maxDepth\":0})</script>\n"}}