{"__meta": {"id": "Xacbe531e28b896133c01efa6ea824449", "datetime": "2025-06-06 19:24:37", "utime": **********.264491, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.940217, "end": **********.264522, "duration": 1.****************, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": **********.940217, "relative_start": 0, "end": **********.075643, "relative_end": **********.075643, "duration": 1.****************, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.075667, "relative_start": 1.****************, "end": **********.264524, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.029490000000000002, "accumulated_duration_str": "29.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1599681, "duration": 0.02628, "duration_str": "26.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.115}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.211772, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.115, "width_percent": 3.967}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2414322, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.082, "width_percent": 6.918}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237867275%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVvbndlaUNuNXdPanVuRStTRkNZMUE9PSIsInZhbHVlIjoicjNGWkZ0RHZwTUszYTJSWSsvRFd6QVVCNlg3Z3p6VGNIM2xKWFZHckNmN3U5S2l0cldiZ1czMkRHVjc5dzN2MkY1ZVFDRGV3eGl6MUZXYy9kRktzTGRsQkR4Y2NrS0l0RUZXSXRtRFhKUUpLSC9YUlFCaW5qWHd1SXRJVlg2YjlRT0JPM1RlYmZDZ05IWGM3bk1iMjJ4SllzTTdpdG93RDJKaVpMcEtWREEvZXd1VVlrRmZmMU1DcWN4NGtzaDhIVHoyZVpHNzFQTjhVcHB4QVNZSVRhR1BaZEY2djVtS1IrNE5PUHNPQzNXMk1kYjR4dURmMWxmR0Y3VysrMW9Eb1VMbWE2OUZVUFBCM0t1S2p4YUxacnBteGxwdXM0SjFOS3RhSjRRSnZYK3YwejRMRzVGTXVzTzcycnhXZ0VoMElNVndoQVNxK2dOSDFyMkVlNWZDNkx3NEp2WVpHZkxyTG8xb3RvRy81bnNqd2l1aVpjQVNhZDJkNEdQSnpNN0FlRU9kbjhpRmUvamdGM09tTmgwSVI4T3g4NkhUUlNweW1jRGdhVEtVRVJ3UWNpa2hqM3VyOFF6eWhvdWJUQTNzV2VlcisvSGFPTEhrc1FqYWJuQkxNaUFLbG1kZWlXQzNVWXJGeEw1RWZXNjc1MmthSXEyWUNnSFc5QVJJd3o2anAiLCJtYWMiOiJhNmYwNjM2ZTkwNzA5NDZhMDg1NDQ1OTNhZjk3OWU0NTVmYTIzYWM0Yjc3MmZkYjAzMTUyNjBiM2YyYWUwMDdhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNVdndNc0VJRE96TS84aUVtZWVHTnc9PSIsInZhbHVlIjoiVjJaVmxhQ3F4eVRUMldWaGw1VTJsbkdPS2h5eGU3Y285U3poNis1SDZNQWp4R2EwSjZBTXZIajVrS29CYWk2Z1VFaG0xUG1rWmNrNVY5NXhaRUlLdFdZcm1pMnZJZjRBY2QyRHVnc29kRWsrV0kyaGxaajBVanRkQVRDcndaWUVyS2N4Ykdzay9QUE4xS3JQNTk5ek8rNlNONlMvL3ZWTE9NOXNUcDlBM2prc1VlaWpyaXo1aUpvYXdTZkZYMnhiWlM3R0hoU2ZkNkZ1dzZUUEVhL3VCTEkzYTkyTGp3djhKSnVvWm5sUnh6RU04SFZicWxrNG1xS2xuTm9TSkhHaVN3b2l6L1hxMEhJeEdjR3laRDQ0MkVGTUo4N1N2QWU3SlRVazBmczFqNms2ZTZZUmZaaFp3aHl5cFZtcWRYcTFTd20veE5qRGdQNm1IRGdOVGQ0Ymhkc0dad2RBb0h5TFFXSm1vMjBrL25MZGg2eXAza3JZSWFLeW1QSWViejBIRDkxUnhXdUxaT3Y4OU5pMllSY3JMU0I1T2ZYR0ZweWhDcVd0Wnl4eDBiazN5d0l2TlI0Um5RL1BZcmtKditSbHRDblRJT056L0N0RlVxVVRJMkk4WmcweEF0MFJTSkx6cFFPMXN6bGxoMVV6dFhVK013R2R5Z1RDblozWU9aUkYiLCJtYWMiOiIxNDQyNWQ3ZjU0Mzg2OGNjMzgyNzc3ZDcyM2VmMmIzZGU5NTEyYzZmMjUyNGY2ZGFjNzMxY2QwMTdjZGY1YmI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1186614683 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bqimaRKNMYDaY70bMn4neav5Q7SUSQNahmKxKJLG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186614683\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-941863867 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:24:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlIxbmUzSU9kQXBKc1hHeE93RDhSV1E9PSIsInZhbHVlIjoiTXlXTGpCaTlYdU5wZmFsUnErdENvSDhZNXhRYys0YzVYWVlXbGNmVEZQaTBzU3IydTZ1STVabEoxVkg3Nml3UEpXbEZ4T3pJL1NDcnBPeC93ampNSDRWRW1SYXFXTXBieEhvck5tVnRDT3ZBcmZGY2c5ZDlzR3JZaEhKZ0t3NXFwN3REK0dCdW9YUFMzT0ZtT3lvaC9PSkxLclVCbDQrS09nL05XT24wSi82TWlCM1dGamZKMFp3cHVEZ3M2WXo1bllIRSs3V0tlV3JwZitVdWxCVUUrVGcxUUhWNDNDcjBtQlpLVEZ2UlhMR2F5MHA4YzVEcjJSYkM2a0J5bFJqSG9iZXlqajR6Mk0yRUkrenYwL2JKTFlaT2V3QjJ4NUVYYnpaUzB5bFh3c2s1aStUWkdMUVJDek1Da1Rtd2NLUFNEb1play9LZUVrR3hETzhGUWNtd2VXSUdRSUJJUEFUcGJHMHlKWjZQYUJBdmp1S1pxNU5NeXd5Z0htTWZhYjBUVnRCblpucWkzbHBscUVDWUxpTkZZMHZLSzFGVjhaN2N4TU5PMVRRblhSektTcy94N1VvV3d5bnlyTWljSmJzUDlCTU5FbXJyc2FmRmdNeEZycll3NEdQTEN3SS9QRmtWL0dWQzg3SG9MWlljOGNYMEkwSHBaRnBDZTdsUHlOT0ciLCJtYWMiOiI5N2Y2Mzc0M2I5NjU2ZWM4OWYxZGIyODYxZTQwNDY1MjIwY2YwMDY5YWE3ODY3OTlhY2I0MDA1Yjg1OThhNjVhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFCVWFTQ3ljdEh5bWRnSU11eHF2UEE9PSIsInZhbHVlIjoiQTFMNzhYNUhqc3VKSHdGQlRtTEtDZTZDbUo3ekRIYjNzdHd4c25XZC95bHhtUUdrV2Zqa0dreEhxUFBWREZuTStST3kwL3JjN1lYS3NNdCt2VW9iS3pZQTlXMjFyWVFCeGwzWWJRaU1yZGdRUFZOcEZoQ0R4T1dBSU1vbHA5M2pRNW9sbytMbVZ1OG5qYVlQQ1FBQUZaQ3RPV2U2VTdrMVdFQ3p2YXllYzBkK29JSDlPWHROMWF3U1NFSjVZdEttTGFMdTJTdGhxL001Q1Iyb01lUnhVOHpUZ2ZEbFJGOUFCN1FOaEhNNGZwd25oSzZHZm5BQnhHT2ZLQ2hKb0Npa2pHVHhXL1hrRDNINEdxdU5NdStxWG1Ba2hwaFBZY0RIZEFhNzRMblRxWmROUXkxVUwvSGFaY3JlMTBhQkFVVjJ1S0ppdU9Kb1VTdHFoVXVpNy9IK2pTamdZb1VCaVJPSnRNOS9SeXVvU1NOeEY1K29aa0FQR0ZMTm1Za3RVbHVoM2ZnclR1R0lIQlZtM3JmWjFxMnBhU3BHMGNydVY5K3hJdGhIWDhOQVZZM0dRVUxWeGxmelNPcW04eXJ0WVdDT3hNenlYeTk3Q3dzQmFxQWV4SGFoaGRpblhNSWwrVk5qVEdrazNoUC9WUFlXOGZ5MXBjaU1wd2FmWEhxSm5EUkMiLCJtYWMiOiIxZWY5Y2IwZDNjOWZhOGRjNDcyY2Q1N2QwNDgwNTkyY2JhNTdhNTAwZjcxZTMwZjU5MTAwOTNlMTI2MjViM2MxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlIxbmUzSU9kQXBKc1hHeE93RDhSV1E9PSIsInZhbHVlIjoiTXlXTGpCaTlYdU5wZmFsUnErdENvSDhZNXhRYys0YzVYWVlXbGNmVEZQaTBzU3IydTZ1STVabEoxVkg3Nml3UEpXbEZ4T3pJL1NDcnBPeC93ampNSDRWRW1SYXFXTXBieEhvck5tVnRDT3ZBcmZGY2c5ZDlzR3JZaEhKZ0t3NXFwN3REK0dCdW9YUFMzT0ZtT3lvaC9PSkxLclVCbDQrS09nL05XT24wSi82TWlCM1dGamZKMFp3cHVEZ3M2WXo1bllIRSs3V0tlV3JwZitVdWxCVUUrVGcxUUhWNDNDcjBtQlpLVEZ2UlhMR2F5MHA4YzVEcjJSYkM2a0J5bFJqSG9iZXlqajR6Mk0yRUkrenYwL2JKTFlaT2V3QjJ4NUVYYnpaUzB5bFh3c2s1aStUWkdMUVJDek1Da1Rtd2NLUFNEb1play9LZUVrR3hETzhGUWNtd2VXSUdRSUJJUEFUcGJHMHlKWjZQYUJBdmp1S1pxNU5NeXd5Z0htTWZhYjBUVnRCblpucWkzbHBscUVDWUxpTkZZMHZLSzFGVjhaN2N4TU5PMVRRblhSektTcy94N1VvV3d5bnlyTWljSmJzUDlCTU5FbXJyc2FmRmdNeEZycll3NEdQTEN3SS9QRmtWL0dWQzg3SG9MWlljOGNYMEkwSHBaRnBDZTdsUHlOT0ciLCJtYWMiOiI5N2Y2Mzc0M2I5NjU2ZWM4OWYxZGIyODYxZTQwNDY1MjIwY2YwMDY5YWE3ODY3OTlhY2I0MDA1Yjg1OThhNjVhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFCVWFTQ3ljdEh5bWRnSU11eHF2UEE9PSIsInZhbHVlIjoiQTFMNzhYNUhqc3VKSHdGQlRtTEtDZTZDbUo3ekRIYjNzdHd4c25XZC95bHhtUUdrV2Zqa0dreEhxUFBWREZuTStST3kwL3JjN1lYS3NNdCt2VW9iS3pZQTlXMjFyWVFCeGwzWWJRaU1yZGdRUFZOcEZoQ0R4T1dBSU1vbHA5M2pRNW9sbytMbVZ1OG5qYVlQQ1FBQUZaQ3RPV2U2VTdrMVdFQ3p2YXllYzBkK29JSDlPWHROMWF3U1NFSjVZdEttTGFMdTJTdGhxL001Q1Iyb01lUnhVOHpUZ2ZEbFJGOUFCN1FOaEhNNGZwd25oSzZHZm5BQnhHT2ZLQ2hKb0Npa2pHVHhXL1hrRDNINEdxdU5NdStxWG1Ba2hwaFBZY0RIZEFhNzRMblRxWmROUXkxVUwvSGFaY3JlMTBhQkFVVjJ1S0ppdU9Kb1VTdHFoVXVpNy9IK2pTamdZb1VCaVJPSnRNOS9SeXVvU1NOeEY1K29aa0FQR0ZMTm1Za3RVbHVoM2ZnclR1R0lIQlZtM3JmWjFxMnBhU3BHMGNydVY5K3hJdGhIWDhOQVZZM0dRVUxWeGxmelNPcW04eXJ0WVdDT3hNenlYeTk3Q3dzQmFxQWV4SGFoaGRpblhNSWwrVk5qVEdrazNoUC9WUFlXOGZ5MXBjaU1wd2FmWEhxSm5EUkMiLCJtYWMiOiIxZWY5Y2IwZDNjOWZhOGRjNDcyY2Q1N2QwNDgwNTkyY2JhNTdhNTAwZjcxZTMwZjU5MTAwOTNlMTI2MjViM2MxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941863867\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}