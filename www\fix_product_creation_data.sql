-- ملف إصلاح البيانات الأساسية لإنشاء المنتجات
-- تشغيل هذه الأوامر في قاعدة البيانات لحل مشاكل إنشاء المنتجات

-- 1. التحقق من وجود فئات المنتجات
SELECT COUNT(*) as category_count FROM product_service_categories WHERE type = 'product & service';

-- 2. إضافة فئة افتراضية إذا لم تكن موجودة
INSERT IGNORE INTO product_service_categories (name, type, color, created_by, created_at, updated_at) 
VALUES ('فئة عامة', 'product & service', '#fc544b', 1, NOW(), NOW());

-- 3. التحقق من وجود وحدات القياس
SELECT COUNT(*) as unit_count FROM product_service_units;

-- 4. إضافة وحدات قياس افتراضية
INSERT IGNORE INTO product_service_units (name, created_by, created_at, updated_at) VALUES
('قطعة', 1, NOW(), NOW()),
('كيلو', 1, NOW(), NOW()),
('متر', 1, NOW(), NOW()),
('لتر', 1, NOW(), NOW()),
('علبة', 1, NOW(), NOW());

-- 5. التحقق من وجود أنواع الحسابات
SELECT name FROM chart_of_account_types;

-- 6. إضافة أنواع الحسابات الأساسية إذا لم تكن موجودة
INSERT IGNORE INTO chart_of_account_types (name, created_by, created_at, updated_at) VALUES
('Assets', 1, NOW(), NOW()),
('Liabilities', 1, NOW(), NOW()),
('Equity', 1, NOW(), NOW()),
('Income', 1, NOW(), NOW()),
('Expenses', 1, NOW(), NOW()),
('Costs of Goods Sold', 1, NOW(), NOW());

-- 7. إضافة حسابات أساسية للإيرادات والمصروفات
-- حساب الإيرادات
INSERT IGNORE INTO chart_of_accounts (name, code, type, parent, created_by, created_at, updated_at)
SELECT 'مبيعات عامة', '4001', id, 0, 1, NOW(), NOW()
FROM chart_of_account_types WHERE name = 'Income' LIMIT 1;

-- حساب المصروفات
INSERT IGNORE INTO chart_of_accounts (name, code, type, parent, created_by, created_at, updated_at)
SELECT 'تكلفة البضاعة المباعة', '5001', id, 0, 1, NOW(), NOW()
FROM chart_of_account_types WHERE name = 'Costs of Goods Sold' LIMIT 1;

-- 8. التحقق من الصلاحيات
SELECT name FROM permissions WHERE name LIKE '%product%service%';

-- 9. إضافة الصلاحيات المطلوبة
INSERT IGNORE INTO permissions (name, guard_name, created_at, updated_at) VALUES
('manage product & service', 'web', NOW(), NOW()),
('create product & service', 'web', NOW(), NOW()),
('edit product & service', 'web', NOW(), NOW()),
('delete product & service', 'web', NOW(), NOW()),
('show product & service', 'web', NOW(), NOW());

-- 10. إعطاء الصلاحيات للأدوار الأساسية
-- للمدير العام
INSERT IGNORE INTO role_has_permissions (permission_id, role_id)
SELECT p.id, r.id 
FROM permissions p, roles r 
WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service', 'show product & service')
AND r.name IN ('super admin', 'company');

-- 11. فحص البيانات النهائي
SELECT 
    (SELECT COUNT(*) FROM product_service_categories WHERE type = 'product & service') as categories,
    (SELECT COUNT(*) FROM product_service_units) as units,
    (SELECT COUNT(*) FROM chart_of_accounts ca JOIN chart_of_account_types cat ON ca.type = cat.id WHERE cat.name = 'Income') as income_accounts,
    (SELECT COUNT(*) FROM chart_of_accounts ca JOIN chart_of_account_types cat ON ca.type = cat.id WHERE cat.name IN ('Expenses', 'Costs of Goods Sold')) as expense_accounts,
    (SELECT COUNT(*) FROM permissions WHERE name LIKE '%product%service%') as permissions;

-- 12. عرض البيانات المتاحة للتحقق
SELECT 'Categories:' as type, id, name FROM product_service_categories WHERE type = 'product & service'
UNION ALL
SELECT 'Units:' as type, id, name FROM product_service_units
UNION ALL
SELECT 'Income Accounts:' as type, ca.id, CONCAT(ca.code, ' - ', ca.name) as name 
FROM chart_of_accounts ca 
JOIN chart_of_account_types cat ON ca.type = cat.id 
WHERE cat.name = 'Income'
UNION ALL
SELECT 'Expense Accounts:' as type, ca.id, CONCAT(ca.code, ' - ', ca.name) as name 
FROM chart_of_accounts ca 
JOIN chart_of_account_types cat ON ca.type = cat.id 
WHERE cat.name IN ('Expenses', 'Costs of Goods Sold');
