{"__meta": {"id": "X819d5165070767ca22673582c1128c49", "datetime": "2025-06-06 19:32:25", "utime": **********.115431, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238343.616635, "end": **********.115471, "duration": 1.498835802078247, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1749238343.616635, "relative_start": 0, "end": 1749238344.919298, "relative_end": 1749238344.919298, "duration": 1.3026628494262695, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749238344.919317, "relative_start": 1.3026819229125977, "end": **********.115476, "relative_end": 5.0067901611328125e-06, "duration": 0.19615888595581055, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02472, "accumulated_duration_str": "24.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0141919, "duration": 0.0213, "duration_str": "21.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.165}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0629408, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.165, "width_percent": 4.409}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.071038, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 90.574, "width_percent": 4.531}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.09008, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.105, "width_percent": 4.895}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-650757049 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-650757049\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1491746219 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1491746219\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2116021080 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116021080\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1563284109 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238287313%7C42%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdRTzM3eEIxVmpEbjBFOFpGMklvRlE9PSIsInZhbHVlIjoiQ0s4VFNqR0EwdGQ4T2x1c2FFTWd1dVEvVHZ2NmlNMlRxMGRJRXkvWWcvOW5CUjNYNk96SElrUzhMZXJVUTlkTDRVWnZ5WTlkTzNXcXpvd2VCY3hrOTY4TXVKREh6ZTZ0Nk5MNFFCbExQelVhOUtFRS93RWsyRVNHSmFUYWszSXRuSXM5Y1dUdkF3RndqMTBXZjlCMHdUa3pvTitMRFJyenZyVUd3T2dFeUh6Z2IrTTY1NmVuR2Q1OTRwdWZ5WmZwdXR4am9CV0pzcUptZkhvblY1WWtJVlRrMjBLeWpycEsrTEhNNmF0ZGxFTEJZWS83QlI3WDh4Skw3MlNJdERBVGxlZ0thcFQ2NmNTTmJxNlBSd055MllkRHZJcldwdGtGTkI3NUZuOWJtaGlpRndSdFVlaFhoblRTZExiWERtakp3QlN6bFBXa3RndmFGLy93WTFicDFCZUcyYXJ2d2Z4WnFTNVJJb3J6c205Zll6UGx0WGg4QzhJSVk3RFc2OGxYd2lhQWYyZlFjbWxiUktXWWpFZDZiaHYzc1dOOStpMGYreFppVmJlMjNRVHlFeHVMVzYrVnF0ZHYyemxtQlFDNDA1OHNiWVBBZ0M0UElHZGZIQlRVQWxHNWRzbGE2c1d5RExRdENobkd6ajQ2MHZxZGZUWWM1ZWcvQm5tQWsxSzIiLCJtYWMiOiJiMmMxY2Q4MWQyZDNkY2NmZTk5NTdlOTBlMWU0OWQ5ZDZkYzIyMmI4NGFmMTRiMDZhNjRlYTIzN2M3Mjk3MzFiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ind0NE85MGdwWTdzZytDQW4yVFJyU0E9PSIsInZhbHVlIjoiT1p1L0ZMb3BVOHltM2dTVHJ2NmFZRDFWMkZLa3ZVamw4M1ZBTHNkRGgxSFRmS3dIK3FqNDJHTk9YNWg1NEVjL2hlajlQSXpybXJtWmFESmZxbHNCV2dqYU13dzczQXgzVlUzcmRSOE1pUGFBNUQyZ2tOR21EVVJyL29iWmhLbjg1VE1WWXk0WGpEcTh3TjhBWjdWSDllWmpYa29CTThEMXE2V1BlM0ZGOEhvUWEzK0dLcVJJY1pkUU9TL3p5YXIxRHBQWjZLM1gwSTB2cDJqMjhzVWQ2eW8rd2E1VmxlV1pnOURDajloVnFkQlhibWNpVEl6UEhKalBIKzFsVlVuUjl2bzhhOGZsYnRyQW1PK3RUT291S2ZiTFA4M2syZzlsbnExR0tWWnlqMlpIMXdXb1dCUEdwR29aVTVSU05HYm5mVmwrUXVxeVdHTUE3R011U3pDYzkwUWRWd3J6b0hjdEVBWG9wZENDQ0dSczZVazZNb3ZZVHQ5eTFlTnRoRklNZnl0MzlzektpcTRnMVYyTmVDS1BEQ09Na3RYaTR1VTh1RVMzTlFSNTVsWVY0S2dFZHNEOGxsOTlVTkNtV09DQlR1RlVTZUxYMzNnYldjS0V0Q3FRSWNLdjhsTE94dzFRSWtnbFM2NmlVMG9EaFB1Wkk0bkx0N1BmN2Z2b1BEWnMiLCJtYWMiOiJjNjg0YjkwNDdkZWNlNWUyOTRiMmQxZjkzZThlMzUxZjU5OWRjMmE5NTJjOTMzMDg0MjBlNWQ1MzU5MjA3M2FkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563284109\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1018351658 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018351658\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1611661013 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:32:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJsRzlzc3ByWFhUTGNSOTZTVy9CNWc9PSIsInZhbHVlIjoiRVdnUEZPbEkzMzBRS3JGelZ6am8zTHBaZThDTS9rRlhOZ2IyTlJwTnBLTUhqWEs4bzdsUmVsS1U3d1BaT1FST3FtVkZsa2F3OFJTUk9JWFRvUGdsTm1jcWJWL1FIM3k4RjJMdE9wMUYvall2ZUxtNThpVWEydjgxVHhFSHJvcG4zVWdjWHFyUlhlV2trQmRQK2lSZkVzVlNUVXRLc2grZk1POGowcWR6VDd4YkdFdCtuUGx6b08ycEg0cDZ5Y0xkSXptbmxiZldZdlV1blZ4MUl2T0xoeHVFRmxvV3V3UktpbWJLa2JCMzd1WGs3WWNJelNtdFlVVmNTY3Vsai9ZRnVhc2hGWkhwS0ZMWXdhemtXMTM5b1R6N240aS95a0RTMG1iZVRIRVpZNmZTYitpbTFhWTBpOXlJWVNTQVhQaVdZbFQzOThKVGcvSVdlalFvN1dDUTlGSjE0cHhBWVlBN0owMVJzU0ZSNVQvamk5T3R0Rjc1MXRCZCtTNFpuTnV0cENiTGFGVlAwYUtFcW43bW1NcElEa1JrRDVwOWVRMVRIdWcrWllFT2lEaUMzTS82a0ZMajVpTEJ0RlhyWDVSeUFCWXZyZTZzUXB5cXdDMjJEMEJVRjJCMjQ3N29FcWc2OG95dkh6SWF4eDl1VVFWNHVUWmxtWVNvTmlsQ0tXTHQiLCJtYWMiOiI0OGY4NzA1M2NlYWJjMTVjODRhNjNiNDJmYWFjMDJlMjk5OTZiMWY4NTFlMTA3NzAxNjExOTNkODQzZDQ2YjE2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:32:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlprZ0thYXh3alI1bXVSMXZtdG4xdVE9PSIsInZhbHVlIjoiTHJMQm1sRHBWNjY4S3FWRkJXUzJjTFNmZXVpNy9UM1JvYkZET240N3R2OVF6ZllqM1lEbHVsK3IxdE1kKzRmV3M4bk5sRUJrQjhJU3F2UmhmQTJmS1hTNmdwVElCaWxjV2krNEFIeVA5WVBrYnVZZFMzcWtmdlRFUUt5Q3Vqd092QXZKenJRRGptdW1FcmVRV3IxNnVBY0hibnB2VWh4WUhybGN6UCtDS3hPbDBJRU5hTnYzbUkxZlFPUXVMYWxkRVM2ZHY5RVcwMXhMTnNnWHNnSVFJQXNVRkRxMFk2NXNqWEtTS3p6eDVxeVNMZnhDU1QvSE9ULzhrVzNGNzUrSE5RNjZ3Q3NqZ2xjOVhhaVRkaWJHMU1scFg0UnNweEhYcEVvRXdseElOZnhuMk03b0kvZ2Z5SEZtK3FzZjg1OWFERytRSXhSbkdSNkRiU2ZHdm9QMVFMWGkyMXppb3plaml2VzlCTTdaaTA3WlJjcnVEN0hsbmJoS2JzUHI1NXRMYXdiZEgrdEo0N0tXZ3c0RmhES1BLMXhHTHJQbmtuanBhV3orRDhrWEE5TzczS3ZtL2VHSVFhbGlub3NYWWRPUFB1WHZ0MEoxdkhuTmVVSzBWemV4YVZsWkFZSFEweS9aQ0JBQVlIdUlWKytoL3JXMStiWWtmS3FjZGFHNi9sL0oiLCJtYWMiOiIxM2I4MTU3NWIwMTdjM2E0ZTQ3MmU1ZDhiMTViZjYzMTVmN2I4NDA5MmI3NTliMGMwZmJiYzAzZTk3NGViMjdhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:32:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJsRzlzc3ByWFhUTGNSOTZTVy9CNWc9PSIsInZhbHVlIjoiRVdnUEZPbEkzMzBRS3JGelZ6am8zTHBaZThDTS9rRlhOZ2IyTlJwTnBLTUhqWEs4bzdsUmVsS1U3d1BaT1FST3FtVkZsa2F3OFJTUk9JWFRvUGdsTm1jcWJWL1FIM3k4RjJMdE9wMUYvall2ZUxtNThpVWEydjgxVHhFSHJvcG4zVWdjWHFyUlhlV2trQmRQK2lSZkVzVlNUVXRLc2grZk1POGowcWR6VDd4YkdFdCtuUGx6b08ycEg0cDZ5Y0xkSXptbmxiZldZdlV1blZ4MUl2T0xoeHVFRmxvV3V3UktpbWJLa2JCMzd1WGs3WWNJelNtdFlVVmNTY3Vsai9ZRnVhc2hGWkhwS0ZMWXdhemtXMTM5b1R6N240aS95a0RTMG1iZVRIRVpZNmZTYitpbTFhWTBpOXlJWVNTQVhQaVdZbFQzOThKVGcvSVdlalFvN1dDUTlGSjE0cHhBWVlBN0owMVJzU0ZSNVQvamk5T3R0Rjc1MXRCZCtTNFpuTnV0cENiTGFGVlAwYUtFcW43bW1NcElEa1JrRDVwOWVRMVRIdWcrWllFT2lEaUMzTS82a0ZMajVpTEJ0RlhyWDVSeUFCWXZyZTZzUXB5cXdDMjJEMEJVRjJCMjQ3N29FcWc2OG95dkh6SWF4eDl1VVFWNHVUWmxtWVNvTmlsQ0tXTHQiLCJtYWMiOiI0OGY4NzA1M2NlYWJjMTVjODRhNjNiNDJmYWFjMDJlMjk5OTZiMWY4NTFlMTA3NzAxNjExOTNkODQzZDQ2YjE2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:32:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlprZ0thYXh3alI1bXVSMXZtdG4xdVE9PSIsInZhbHVlIjoiTHJMQm1sRHBWNjY4S3FWRkJXUzJjTFNmZXVpNy9UM1JvYkZET240N3R2OVF6ZllqM1lEbHVsK3IxdE1kKzRmV3M4bk5sRUJrQjhJU3F2UmhmQTJmS1hTNmdwVElCaWxjV2krNEFIeVA5WVBrYnVZZFMzcWtmdlRFUUt5Q3Vqd092QXZKenJRRGptdW1FcmVRV3IxNnVBY0hibnB2VWh4WUhybGN6UCtDS3hPbDBJRU5hTnYzbUkxZlFPUXVMYWxkRVM2ZHY5RVcwMXhMTnNnWHNnSVFJQXNVRkRxMFk2NXNqWEtTS3p6eDVxeVNMZnhDU1QvSE9ULzhrVzNGNzUrSE5RNjZ3Q3NqZ2xjOVhhaVRkaWJHMU1scFg0UnNweEhYcEVvRXdseElOZnhuMk03b0kvZ2Z5SEZtK3FzZjg1OWFERytRSXhSbkdSNkRiU2ZHdm9QMVFMWGkyMXppb3plaml2VzlCTTdaaTA3WlJjcnVEN0hsbmJoS2JzUHI1NXRMYXdiZEgrdEo0N0tXZ3c0RmhES1BLMXhHTHJQbmtuanBhV3orRDhrWEE5TzczS3ZtL2VHSVFhbGlub3NYWWRPUFB1WHZ0MEoxdkhuTmVVSzBWemV4YVZsWkFZSFEweS9aQ0JBQVlIdUlWKytoL3JXMStiWWtmS3FjZGFHNi9sL0oiLCJtYWMiOiIxM2I4MTU3NWIwMTdjM2E0ZTQ3MmU1ZDhiMTViZjYzMTVmN2I4NDA5MmI3NTliMGMwZmJiYzAzZTk3NGViMjdhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:32:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611661013\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}