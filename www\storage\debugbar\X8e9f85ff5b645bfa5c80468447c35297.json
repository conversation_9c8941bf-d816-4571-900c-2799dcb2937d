{"__meta": {"id": "X8e9f85ff5b645bfa5c80468447c35297", "datetime": "2025-06-07 04:35:05", "utime": **********.312506, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270903.947397, "end": **********.312556, "duration": 1.365159034729004, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749270903.947397, "relative_start": 0, "end": **********.10771, "relative_end": **********.10771, "duration": 1.1603128910064697, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.107732, "relative_start": 1.1603350639343262, "end": **********.312562, "relative_end": 5.9604644775390625e-06, "duration": 0.20482993125915527, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44765296, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028079999999999997, "accumulated_duration_str": "28.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.200345, "duration": 0.0249, "duration_str": "24.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.675}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.256733, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.675, "width_percent": 5.306}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.282901, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.981, "width_percent": 6.019}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2010978622 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2010978622\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1153128853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1153128853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-679738898 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679738898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1211140796 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270841236%7C17%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNQb3J1OUhianc3a2RlWFhaUnp3S2c9PSIsInZhbHVlIjoiWmZ5MzBEVHpiMWtnVlVqWHBueWl2QnVCTWpCWDlENE9wcHNaN2FjVFdMby9qbmU3bW5lL3g3Rld1aXNCdW1tZFc1UDhjbWRXL2JBay8zODFCejNLbFV0alpqbnQ5NTZxUi9ZckFiUy9ad1VMYUJkQVNqMzZMeUdHd3BjWExkdFBxaHN0TGRwRENYV2dyMzgrY2haRUVhRFlPMEI5Wkt6a3kwS2xkZ2R5cmlxOWc0N1h0ZlZmaFREYlh4UDYvWmxpWlEwT0J2TFVaSFhEU2N5OGZrZkRGeVZRbEI1SGVrYkR0N2grMnBsWTJPQlNCckRxOXd5bkt4MzFINFhUcDNFcUdreW5DZ0tPd3RTNjN4eUd3RUVGYW5nTlU4OVNNbjVud3k3Y2E5YldBMldXMU9yQWRNNXEzdGxPbHM3SXB6Z3ZvWUNvRTYxNDQ3Q1J2TG9SYnI4dzVVRXhqVUZvanNGaC9ZcStRV2dqeTdNLzVobFplMWZPOTMzWWRJdURqS1Bhd0czYUI4N29jUmxjSlZKcktVYTZsZ3UvcjZzQjFReDU3aWxaMW01NVVXVkhPTTFWelpuNXVTVzNvenJsa1VsSGhKcStjYVE0c0d3RkxqMndvRXFDN3h1UEd2KzBSMnJJcGZzOEcrYklGbHNIdVp3MDY2RHYvWWhVV2hLNzZCN2kiLCJtYWMiOiI4MDcxNmQzM2Q0MjAwOWQzNTkzM2Q0NDAzMmZkODkxOGZmNjBiMDU3ODYxODIwNDRjYjFmMzUyZGQxYjQ1ZmQ4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1jNkdtSVFEV3BXbW5tQmdrcFlaR2c9PSIsInZhbHVlIjoiWFF6SmNLSnVxcHFYNldqZ1lEdkhFSm1kVWZScXU1QWZVcDZXblhnM0ZkOGVCcFY0N0JHQTROMlhhNDhUSFh5WkVCK0hYYXBvaS9HY2xEdE9oOGkwV0NVTlJnMDQvRG0xQm03N3NQejgwNEc1RTVqNkdmK1BCK241U2JvMldKWEdya1ZUTmpYdkk0OHdVWnFyUm95ekRTVEhMZmRYMXpqdkVqNFFWcmkyR0I0d0xQUHUzTWFUc0dZQ2QyYjh4eUZQMjhCdXUxUlVKU3RCTk1WSktJY0pzcUEvMVlUUzJXMXlxdXpYd3RuZEs1V3ZaZXJpZXdiZ09BOXNKejNkM0RQdit5dnc4VEdURWpjR2ppVU5IV1c2M0I1Vlg5SUNQTXFIbmphTnh3aDJ2NWMvc1VmbkxrK3F0ZjJ6Mkg2Mzd4TXoyTVdFcWZBaStwTldBczJYampxUlZyNFpKREpINzB6KzRIOStFcEd5SXNjZ01oeGcrU2M3eWw1NVg4dTNrYzhlWVI3VzgxS3B5TGNvd2pZVGJpQmxkUjFZelNBQkl5d21tU0pIN1Z5a1F1SEhiWjd6QklBbitTV2tySTI0U1RiOWxobFFHdFZYU1dLdkVHUGhmdWQzWDN4QllZeWY4MGN0am4wOHFkMWtBczd4dUZYczhKTkdGQ2hCVEdwaTF2RXciLCJtYWMiOiI4YzFmOGZlZTQ2NmUxZmQwMjE5YjZmOGU4MGE0YmY1MjY1MTgwNzQwM2IyYWNkZmVlYjRhYjI1ZjIzYzdjZTUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211140796\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1530625844 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zyhWlMrUupyybJnMtPnBjJ716zpTnMkeCiqRAqWb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530625844\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1026110562 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:35:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlabEVFS29TRU5jbE1MK0E4K1BUcEE9PSIsInZhbHVlIjoiRk50cFE3VDdxVVJRRncrbFpDK0NXcEsvd2xmUU5SNjJJTVhCN2J3cGtVc0t5dzdKeitmZzVJWDM1WWdOQnk3SVUxQ0JkTjR0bzRjeU5NNktRaklTTWg4TmtMdHZNTmc2VW1UYi9EVkdYYmpkeHZDdWNYVWxJeHlBMklQYVV5VzZ5M0xaUnZRM3Y4ME02dllnVDNDRmpNR2pRckQyTHNUWExYV2l3aE85T09PenB4QXpZTDA3aVRZVjU0dFlldlkrZUVNU0VDOVMvaXVuZitTRDgxL2pwb05vS05uOVRIVlZ1ZCtUVU1CMGlPQ0Vmdkd2TmhxcXhLTmU2Y3ZwdTlFUjJia2VtdXY5WUd5TmdVZGI4Y1NITXdpZG95Yll1YkQ0WTdUSElBZ21XTmJlajUwUkUrdVowTWZkbk84SW1odkluREZSYzdaU25qM1pLQlZBQ3FXSk41VXdVMk9OTFVIc1V4Q2I5SWdVQ0JHV0hRc00wQTlTbmNVeDdNN1RRSG4zSFY1WTRkSmFUV1Q1bEs0UUdpNVlGTm44SnBtaS9lTkdmZkl3ekFqQW13dXU0Rm1xVUNuekcvWkh0a0t2RURQYlBheG5PZG8wZVowUHNwbkV3OTluMEErUE4zbzlxSjNSWGhJYmtXbGd1cGpuUXVuTlh1NGtvU2F1Sk1CNEd5NjQiLCJtYWMiOiJiMDk5N2FiNTkxOWUyOTJjNzU5NDMwZjg5NmMwMDZkODFkZjRjM2QyYWQ5NWQwMmU1NjZkYWZiMDRjYjUzN2ZlIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImQvVG0yOEQwckRDaW45TFNIT0FUTEE9PSIsInZhbHVlIjoiTFJad3ZWTjNEQ2Z5ZlJ2Q3FHZzdLSHZPMHlhUm5xOTFiN3pkWksxeWxZaTJQaEpxcG1CRVArcXR3eVFXZHBvcm5GRExva3pSOXJnY2JHcHpMSEM0S1RucGtZak9MdEtmSVlhSDQrVkNDN2k2a0dLZFpWeFRNZU8rSnlZZ1ZhNjlXbkpFWGxsTnByc1kyemdzb29CdXpJQUozcW5qYnBlNzZIekhpNlM0Yk1OSEpxazc5eFhYZ0JidjRVWFhkWU9ISC9HckZBYlZXZE11WjFqaW9tallUaTcweStDVlFobWJod2M2dDh2SXdvZGNaWUNzaXA4eitzUDdCby9qRmx3cjJmODFaRDZsM1ArM3grV0JQNWh0SiszZ24yOVRpZVMzTXMyRm42KzM3UjdZa1U5K3pQS3pvN0hKL21XaFNWVU4veWZPWklPODdoVWZIc24yQzlza2pSSll4cmFrRnlqNllNVjhaREtCZVVTMnhaMDdIN1pxd05UOURhUVdpZXQ1Z3UxYzFCWDNoakd1cXQxaitEYUxwTzdYeEVyemN3YzdZTlpoNk9LYU0rdjNCV281MzdvVStQajZXV1lxdFhpRWJENytpQWZvaTdBQUJyUGU4MEtKSlRKTzJtYVJjSUErQTVYMFFLLzUyenU3VVZOMlNjRmlEN0tERFFBcUwvbloiLCJtYWMiOiJhYjg3ODU3NGVjM2Q0NDAzYTk2MjA3NmI0NGI4NTVmMzg4OGIzMTg0NzExMzg3Y2VjOTY3MmQ4NTU4ODI0YmMwIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlabEVFS29TRU5jbE1MK0E4K1BUcEE9PSIsInZhbHVlIjoiRk50cFE3VDdxVVJRRncrbFpDK0NXcEsvd2xmUU5SNjJJTVhCN2J3cGtVc0t5dzdKeitmZzVJWDM1WWdOQnk3SVUxQ0JkTjR0bzRjeU5NNktRaklTTWg4TmtMdHZNTmc2VW1UYi9EVkdYYmpkeHZDdWNYVWxJeHlBMklQYVV5VzZ5M0xaUnZRM3Y4ME02dllnVDNDRmpNR2pRckQyTHNUWExYV2l3aE85T09PenB4QXpZTDA3aVRZVjU0dFlldlkrZUVNU0VDOVMvaXVuZitTRDgxL2pwb05vS05uOVRIVlZ1ZCtUVU1CMGlPQ0Vmdkd2TmhxcXhLTmU2Y3ZwdTlFUjJia2VtdXY5WUd5TmdVZGI4Y1NITXdpZG95Yll1YkQ0WTdUSElBZ21XTmJlajUwUkUrdVowTWZkbk84SW1odkluREZSYzdaU25qM1pLQlZBQ3FXSk41VXdVMk9OTFVIc1V4Q2I5SWdVQ0JHV0hRc00wQTlTbmNVeDdNN1RRSG4zSFY1WTRkSmFUV1Q1bEs0UUdpNVlGTm44SnBtaS9lTkdmZkl3ekFqQW13dXU0Rm1xVUNuekcvWkh0a0t2RURQYlBheG5PZG8wZVowUHNwbkV3OTluMEErUE4zbzlxSjNSWGhJYmtXbGd1cGpuUXVuTlh1NGtvU2F1Sk1CNEd5NjQiLCJtYWMiOiJiMDk5N2FiNTkxOWUyOTJjNzU5NDMwZjg5NmMwMDZkODFkZjRjM2QyYWQ5NWQwMmU1NjZkYWZiMDRjYjUzN2ZlIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImQvVG0yOEQwckRDaW45TFNIT0FUTEE9PSIsInZhbHVlIjoiTFJad3ZWTjNEQ2Z5ZlJ2Q3FHZzdLSHZPMHlhUm5xOTFiN3pkWksxeWxZaTJQaEpxcG1CRVArcXR3eVFXZHBvcm5GRExva3pSOXJnY2JHcHpMSEM0S1RucGtZak9MdEtmSVlhSDQrVkNDN2k2a0dLZFpWeFRNZU8rSnlZZ1ZhNjlXbkpFWGxsTnByc1kyemdzb29CdXpJQUozcW5qYnBlNzZIekhpNlM0Yk1OSEpxazc5eFhYZ0JidjRVWFhkWU9ISC9HckZBYlZXZE11WjFqaW9tallUaTcweStDVlFobWJod2M2dDh2SXdvZGNaWUNzaXA4eitzUDdCby9qRmx3cjJmODFaRDZsM1ArM3grV0JQNWh0SiszZ24yOVRpZVMzTXMyRm42KzM3UjdZa1U5K3pQS3pvN0hKL21XaFNWVU4veWZPWklPODdoVWZIc24yQzlza2pSSll4cmFrRnlqNllNVjhaREtCZVVTMnhaMDdIN1pxd05UOURhUVdpZXQ1Z3UxYzFCWDNoakd1cXQxaitEYUxwTzdYeEVyemN3YzdZTlpoNk9LYU0rdjNCV281MzdvVStQajZXV1lxdFhpRWJENytpQWZvaTdBQUJyUGU4MEtKSlRKTzJtYVJjSUErQTVYMFFLLzUyenU3VVZOMlNjRmlEN0tERFFBcUwvbloiLCJtYWMiOiJhYjg3ODU3NGVjM2Q0NDAzYTk2MjA3NmI0NGI4NTVmMzg4OGIzMTg0NzExMzg3Y2VjOTY3MmQ4NTU4ODI0YmMwIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026110562\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1917994068 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917994068\", {\"maxDepth\":0})</script>\n"}}