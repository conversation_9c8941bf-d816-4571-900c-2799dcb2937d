{"__meta": {"id": "X110dea027813c9b1f98c2c8c83797a8f", "datetime": "2025-06-06 19:13:11", "utime": **********.946552, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237190.376094, "end": **********.946588, "duration": 1.5704939365386963, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1749237190.376094, "relative_start": 0, "end": **********.730644, "relative_end": **********.730644, "duration": 1.3545498847961426, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.730666, "relative_start": 1.35457181930542, "end": **********.946591, "relative_end": 2.86102294921875e-06, "duration": 0.21592497825622559, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44799608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.011949999999999999, "accumulated_duration_str": "11.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.828249, "duration": 0.00636, "duration_str": "6.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 53.222}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.86481, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 53.222, "width_percent": 14.142}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.895894, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 67.364, "width_percent": 17.908}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.917452, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.272, "width_percent": 14.728}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1346725066 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1346725066\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-148675889 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-148675889\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1210555270 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210555270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1665283082 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVnMk5FdlEyYTV4dVF4VVpad3VjQnc9PSIsInZhbHVlIjoiRFFkRzhXNm05T3Q1MVVRaHdDdnhDai90YWw0TGtJZ3ZadHZsTlVmeG1GaXJFWWF6dUhrczZoV3AxN0dpY0xGd0R0anl0VFNFWE5ubndMaXBIUTNDdFdaR1JoS01WOWJocnJRN0VZcGlLWHV2QkExYkVpQURxSGZab1VwYmNYR3RsOWVOQ1JyZVltMEQwR0FTaWQzMS9HOFdXeXQvWE4rTE1ORlpQWGFWdVNYUlh6a1ZEbFR3OVN0bkVCYllqRFFveHBpTWFOK3hPWWpvZXRFV1hmVWMrbzNSbCtzNTdaQVNaQmhDQmw2WGJaUVQ5V0NFT0pZSFVZRXZWTkZUMWxuOGJrd01yTXhGZGwvSktIcFFmUlVuS25rZ0laRnN5MHAvYTFWeEV5U21TV2lqOTZKaXM2RnpZNWpZOTRsTGlYd1M5ZFlLdjNUWkJXTlJ0TlRKaURCTGdrK3lTbWFKODZSamdCZkl4bkdKTHFkSU1CK0hETmlYTnpOeXJsQWhvUWVPdENVMm1NNHN0b0hNOGN4dkJXelpodEZ1MlVlUDFBUml3TVJBbDZtMFRhTU1yaG85MDNUZm54SWlQd20yY1BxL0RkRlhJSER0VFNZZHBDaC9Lam1BVVNzajArQ2ZTQytTMXhoVVFJcEtnT2JCcHVwVjZURWFlNGh2bGVrU3lTMy8iLCJtYWMiOiJlMzMyYjJmYjE1MWJiY2EyZDU3N2M1NzQwZmJmYmVlZjBmMWY3MjhjN2FmMmJjMGI2NGYwODA3NTg1Mjg3NjVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtMYmFrbGdWQ09JVTJJMEpJYUZ3NHc9PSIsInZhbHVlIjoiQTdJd3ozQTR6aWpKeFQ4Y29XVG9PY0NEVU9TaW5EZlVTbHAyYzgzRTdIZ213ZXVZcjlqTDI4RDRRVDFTdkhNQnlpMEc1b1F0dmFaVnVPNUZDdjdoUVFtQ1hiYWZQMm1kSU42TWNIeTFBSWN5NE9pUnI0MWxwSzBmRTJma1VUUXRuSUp6b1U2SUI1VC9qemthdndOMzI3em1pZzBKU05FU2IxVnNpTUZPeEIyMlp6amY2cE5BSW1EbHN6TFBkYWRHTW9iY21ISlpnRUcvc2ZCRllBMHlFb3huZDZWeGdlaE1FS0drMDRwY1EwSzc3WmUzUlZJdGJIQVZzWU9WWHF0dUZxV0VETjVJZnZhL05IN2hNOFViYmRDcmphNWZidTNXS0xaZ1ZrMVBDNms4WnhkYTBDWjhjZmIxeWIrREZVdFEvMWx6Tk9xUVR0c0puSHZScXJPckpuZ0MvZWUvWGxxWHcrOVdEV0h6M1d6Z2RQTGJ5eUJ0QkhjUXFBdDIvZXg5QVA3eW4wQ1BnTkVXMU9NcFJwMDZKKzlsZFhPUXp6cmMweVBSSWFQdEtWVjZVNlZCdEUyR2NiVXo3MUdWbFIxL1hxYTBZa3VaRUdCb1dJclQ1eXpoSlVJeVVvZThnWXF5dHJ0Zy92bmFqRjRzdkkrWW1uQ1BEWlhUaHI0aGZlWjUiLCJtYWMiOiI4ODBkOGE4MmI4NjY5NDdiOWViZWFhMTRmNGJkMjc3MGM0MWI1MmJlOGQ4M2Y4YjZiODgxMmMyZDE0NmEwMGMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665283082\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1231450341 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PVt0h9icZb5VqFqziyPToWE7TqW2lYjvh7uvnOnw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231450341\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1884743335 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlcvNVlFVlJMQ1ZzeGd3NFVaSnlWWHc9PSIsInZhbHVlIjoiNGR6eGZxUGJBblA3SDVFL2hvWSt3SUdNcUtGdTZIVmNDdHNaOG9lRTFwYzVRRGgvSkpzK2pwd2xVZVluSnczUjhHOVduL2t6aExZcHYzTXFFRWpZSm12a3pXMnYremVLVlRSNk5STzczdDVUOGF5U3lMS3FFNXo4dXNOemlHK1M4YXQwb0Q5YmoxZ0cwTXhIMWxxNFBYU3ZuU0NSaTlBVHl2aXk5eE1QV0FHQm4xanBFL3ZMWW9hNVRrTU1rcitVb05hcU16bkhOYjlnYmRLYVBJUTBvZ3JrejI2bEdERFJMZ2YvREwvNzk5WUhZK2hQUlBtYVkza045TUNVdlBPLzdYZHRBdCtLd0NWc001d29GeEZBTXJqQ0p4RDV5OGxUemNaaVdhQVdNbnV1eUt5ZmZ5NEJ1V3pIWkd0cHFhOVpoUm9iWG1GU24rcEMxb00vRHVJM2ZSUmdWZGNZNWs3VkdOZmhBd1g5c01hL09FK1BGcjZuaDd5d3pDNUlaVDZYdGJDNkJUdThkZ0xidzJhdmI4VnMvOEdsOUJLMWRuQm1uNTY2aTNSMjFFWEJTdWVIL25uM2FIU3NKZzhMYjhabEZvMFZBUEpieXlhZkhJWHFIY3htUWRvaFZUTHBUR2hqSW9Vck52LzY2bzBwTDlyQVdacFVUUXY0OUQrMmJYdHUiLCJtYWMiOiJiOTZkNmIxNDM5MWY1ZGU0NWViNGI1NzY0MDc0MDgzZGExZjcwMWRjMjE0Y2E1ZGFjMGM4Y2Q3OWUzNmU3Yjk1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik8xc1ZCeWhKb25RMjFVaUcyQmRyYUE9PSIsInZhbHVlIjoicTgrTGlqU3hmcnN4dnZ1cEtCUnprRS9yNWhkUWNnYzBlN2xmS1FySmpGeGJxTUcrQ28xUVM3SHpQU0xEUWU2OU9kVGlyUFFOOW1veC9DcVdOT2w4eTE0bUFJS09NMzcwSGRoSWJCYU1yOER0WUdqTk5hd3Z5dHE4WEp4eGUwUG9aS1ZQQjFMdWdOc0dJZDFTV2tuMnBwaElpellPQm1WOTR4alNSY1c2ZWl2enN6clE3cDM1bFAzMmNYRS81Nmd2cjlhM1dha2JJK2VFbzdwNkRXSXAwSmUza21Ob0Jud0tWWDRoYTUxUmtUbkhvZk5xTWhmTk5Tc0UwSE9FczMzaTA2VkU0dG1CVDd4Rk13dkxHRWEvcWFxUTJlWjVlK2o0Tm5vODZoc2lRazFBNlZiUUJhOXlTMjlKdW5QVkhaTGZWa2VVTTRPeS80aGYyYTJDdXMvaW1kM3ZLQkprZU1YYkJhZ1U2QmRCaGFQbzcwdTFUZDNEK3E2bFZMd1NDcW9PYjdHMHhJOGdWVzJvRThkSnZodDlQM0RPY3ZvMWNoQ0l1T095TmhBMWdtZkJDUEtjWVlmV0xoUXo0cHpXNFIyd2VxUG9LTUpMNVBEVjg1SUpHQXNRSXc3aEZNOFVoK1MxTkdWRFh5aVhsdDBJS3dFVXp3ZUhibXltdnA3ZDRzalciLCJtYWMiOiI2MjExNzMzNjUyZTNhNDY3N2I4YzA0YTVmY2QwNTE0MWIzZTMxZTU1NjUzZTdlNjU0OTk2MDczMDUyM2JmMjcyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlcvNVlFVlJMQ1ZzeGd3NFVaSnlWWHc9PSIsInZhbHVlIjoiNGR6eGZxUGJBblA3SDVFL2hvWSt3SUdNcUtGdTZIVmNDdHNaOG9lRTFwYzVRRGgvSkpzK2pwd2xVZVluSnczUjhHOVduL2t6aExZcHYzTXFFRWpZSm12a3pXMnYremVLVlRSNk5STzczdDVUOGF5U3lMS3FFNXo4dXNOemlHK1M4YXQwb0Q5YmoxZ0cwTXhIMWxxNFBYU3ZuU0NSaTlBVHl2aXk5eE1QV0FHQm4xanBFL3ZMWW9hNVRrTU1rcitVb05hcU16bkhOYjlnYmRLYVBJUTBvZ3JrejI2bEdERFJMZ2YvREwvNzk5WUhZK2hQUlBtYVkza045TUNVdlBPLzdYZHRBdCtLd0NWc001d29GeEZBTXJqQ0p4RDV5OGxUemNaaVdhQVdNbnV1eUt5ZmZ5NEJ1V3pIWkd0cHFhOVpoUm9iWG1GU24rcEMxb00vRHVJM2ZSUmdWZGNZNWs3VkdOZmhBd1g5c01hL09FK1BGcjZuaDd5d3pDNUlaVDZYdGJDNkJUdThkZ0xidzJhdmI4VnMvOEdsOUJLMWRuQm1uNTY2aTNSMjFFWEJTdWVIL25uM2FIU3NKZzhMYjhabEZvMFZBUEpieXlhZkhJWHFIY3htUWRvaFZUTHBUR2hqSW9Vck52LzY2bzBwTDlyQVdacFVUUXY0OUQrMmJYdHUiLCJtYWMiOiJiOTZkNmIxNDM5MWY1ZGU0NWViNGI1NzY0MDc0MDgzZGExZjcwMWRjMjE0Y2E1ZGFjMGM4Y2Q3OWUzNmU3Yjk1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik8xc1ZCeWhKb25RMjFVaUcyQmRyYUE9PSIsInZhbHVlIjoicTgrTGlqU3hmcnN4dnZ1cEtCUnprRS9yNWhkUWNnYzBlN2xmS1FySmpGeGJxTUcrQ28xUVM3SHpQU0xEUWU2OU9kVGlyUFFOOW1veC9DcVdOT2w4eTE0bUFJS09NMzcwSGRoSWJCYU1yOER0WUdqTk5hd3Z5dHE4WEp4eGUwUG9aS1ZQQjFMdWdOc0dJZDFTV2tuMnBwaElpellPQm1WOTR4alNSY1c2ZWl2enN6clE3cDM1bFAzMmNYRS81Nmd2cjlhM1dha2JJK2VFbzdwNkRXSXAwSmUza21Ob0Jud0tWWDRoYTUxUmtUbkhvZk5xTWhmTk5Tc0UwSE9FczMzaTA2VkU0dG1CVDd4Rk13dkxHRWEvcWFxUTJlWjVlK2o0Tm5vODZoc2lRazFBNlZiUUJhOXlTMjlKdW5QVkhaTGZWa2VVTTRPeS80aGYyYTJDdXMvaW1kM3ZLQkprZU1YYkJhZ1U2QmRCaGFQbzcwdTFUZDNEK3E2bFZMd1NDcW9PYjdHMHhJOGdWVzJvRThkSnZodDlQM0RPY3ZvMWNoQ0l1T095TmhBMWdtZkJDUEtjWVlmV0xoUXo0cHpXNFIyd2VxUG9LTUpMNVBEVjg1SUpHQXNRSXc3aEZNOFVoK1MxTkdWRFh5aVhsdDBJS3dFVXp3ZUhibXltdnA3ZDRzalciLCJtYWMiOiI2MjExNzMzNjUyZTNhNDY3N2I4YzA0YTVmY2QwNTE0MWIzZTMxZTU1NjUzZTdlNjU0OTk2MDczMDUyM2JmMjcyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884743335\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-473373412 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473373412\", {\"maxDepth\":0})</script>\n"}}