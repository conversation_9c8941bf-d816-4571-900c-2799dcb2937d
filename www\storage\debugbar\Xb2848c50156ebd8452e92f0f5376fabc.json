{"__meta": {"id": "Xb2848c50156ebd8452e92f0f5376fabc", "datetime": "2025-06-06 19:28:12", "utime": **********.213016, "method": "GET", "uri": "/roles/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238090.488469, "end": **********.213043, "duration": 1.724574089050293, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1749238090.488469, "relative_start": 0, "end": **********.797959, "relative_end": **********.797959, "duration": 1.3094902038574219, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.797982, "relative_start": 1.3095130920410156, "end": **********.213046, "relative_end": 3.0994415283203125e-06, "duration": 0.41506409645080566, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53607144, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.create", "param_count": null, "params": [], "start": **********.145301, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/role/create.blade.phprole.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Frole%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.create"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.174595, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.create", "controller": "App\\Http\\Controllers\\RoleController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=31\" onclick=\"\">app/Http/Controllers/RoleController.php:31-57</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0326, "accumulated_duration_str": "32.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.890025, "duration": 0.018690000000000002, "duration_str": "18.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.331}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.943815, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.331, "width_percent": 3.221}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.953493, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 60.552, "width_percent": 3.16}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.013381, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 63.712, "width_percent": 4.479}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0213552, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 68.19, "width_percent": 4.724}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\RoleController.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.035981, "duration": 0.00745, "duration_str": "7.45ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:45", "source": "app/Http/Controllers/RoleController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=45", "ajax": false, "filename": "RoleController.php", "line": "45"}, "connection": "ty", "start_percent": 72.914, "width_percent": 22.853}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.create", "file": "C:\\laragon\\www\\to\\resources\\views/role/create.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.155112, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 95.767, "width_percent": 4.233}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 510, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 513, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1931833794 data-indent-pad=\"  \"><span class=sf-dump-note>create role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931833794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.033992, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/create", "status_code": "<pre class=sf-dump id=sf-dump-452074579 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-452074579\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1735274637 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1735274637\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1571307288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1571307288\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-915935624 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238087808%7C31%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImUydHVDY01zQXlaZURDMHFYREEyN0E9PSIsInZhbHVlIjoiK3JGYzd2bC9MOXJJRldtSmpZT2J1VHR3anMzT1FSWFo3R2pWRnNUUm1hMHdHQkErRnE2eStCdm9pV2QyVHptNUpQOG1yeUJLNlFGUzdGSnFiaGhEb1l3VGlZaXpMVHBnaStWemZBVWtxRk9IRU9KTmtmK1AvcW1taW1zMktmQyt3VGdPZW90ZmJ2ckFHeUFtWlhCVzU2NU9uRFJKQUp3OWd6NTlSakxGaDQ5YzQxZTlRWVpYMWtMN0NYRDNWbDlCMUF3R1VuS1FjSXdqVkxaNVg3YmJOSjU0d0VySDVEcnhFV0YrTzdXUFJJRVZoV2xUdzloQjRBdmFmZmlDSUovWHRkVkRFL2d4cEFFaUQrVHRtTEpTNWJLdWx3Nnl4azdyM1lUTy80UnpaRWdabWI5Skx1cUVjNWtEeTN0cExyMk5BbDJ0Sm9oNmFPVXdrTjJ3RDJjY0VpZ2xiOVpEVHllNTdJeGZqUkZ0dTdVL3EzdU1jTDFSa01sNkpxMjdZcWdNa0R5MnpNUGMwZ1I4TU5WZmg5bktUc1o3a01LTGt0bWJGcURtTDhyRzdoSUE0dXJKa1lJS0dhY1RZU1RCTWlDcTZOMnFiZTFUSUszZDRScWFubHZlTlFTSGJMelo4cWgvTHoxNkg5YWMvV1A2enE0eHBWTGpoWmdCc0trcmN0REoiLCJtYWMiOiJmZDRlZGIwMzBlOTUyZDY0OTBjZmZkYjM2ZDQ4NmNlNjFjNjkyMDM0YzBmNjBkOGFjMTM4OGU1OGE5NjhlMDE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklyWHhwMll3Z1pEYjhjZGxFOE9GVEE9PSIsInZhbHVlIjoibkdVVXZMdkNRS3l4OGNYbzc0WXNZVU15ZWtWb2hkUGZrbVhYTWhiUFpJRHA5bWZyOGRtSlBaVGF5S2tPTGVrYmh4Y2hMU3NTK2FHL0dubHJ4Zi8ya2NES1hwM1pBVmVNcitZQU5GcjZzU1pkUG1LMTFCRTNhd2VRYW5sR1QvL3pEU1ViQVFxaDJXZU9Zb3IrZFVQQjNuSksrM2JVWjJ3VUtYaFlUa1lhc25qLzBPTHlIOThHWXhkZFFKekNmNUpQSEl2b3VkQXNNYVdwUmNHbkhkYzVFVlZGRFIyNy9rWHMrOEJyWTFlWlFIdXlJZmU4Y2tZbmpRZElDZ1h2U0daNHlnekhzdStVeS9VcHl6dUx1bkFNSUJ2bFlRYW1VdGN6aGNQTHlpSHBTVld3MEw2MmVuVTIvVlE0QzZ2WGErNy90ekZwVkFldWJtbytWNXRsaE5LMXFZTnA2LzB3ZlhFdHRydngxV3MwSUR1c3pXRENTOEI1OVltZ3FXN0k0RFhOS0UwMm55bHJveXNCek5BZFp3Uy95NUZQUzczaDlMekpYTFBTRnhNaWgwMEhYMG9TdkptM1J0bHFrWVZJNktaSlRCdWlkbkhUTWFaNFIrR3JNKzNxVUdiVklZRkdMKzdzSHloUHhuRWhSbG00MnU5a3pNcUhJQjFhWDk2MTJUczciLCJtYWMiOiI4MDBhNzA3MWE0ZTI5ZjVjYmRlNmU1MWZiOTdhNjQ2NDYxM2EzZDA1OTgyYjk4ZTI0Y2RiNzRjMzBlMDZlNGRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915935624\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1280503874 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280503874\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2058109810 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:28:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZpVVZSbW9LR0htb2Vtei91Mllkemc9PSIsInZhbHVlIjoiN0p2cXBIckFuZTRBcm1NU0RoYnF1RE8zOVhGcFYxVWREeGU1NUlLZzVtTGloNnByMGhzZVU5bVdjVkl1YThmbStMVmN0V0YyZk15aTVNbmU1OGdNaXUyUnlqTk55VHU5dElsd0tndWFiR2szZU9WZ0VzUkJ0aTVvdUJaMjZ5Y1J5ZThHMGFKTzl6a2lNSkhldjF1NWc0Y004WEM0eGhHTDA3dENXZ3g1K3FENHh1Zk9EaU4yaUhoZWR3ZW82QVFkTGR1dEZjV2I1d2h1bzZIOFZjYVdDYmU1REZxNDZiMUdzek1KRkpkbFJLZ2pMN1FwenZ3R04zU3QxUWVmZUpneUxwVzNJMGFmakx3enZRWUJudDFKTnZUbTVZNUVaVDhjeUJyZWJZUExhMnpuVlphMFBSbGRLc3BkVitUOUpMK3MyM1FPaXpsOE5Xem1ueFU2dmVCb3ZQRmZHWExwYWZINHFtdnpYVnBCdno1TitmUlhRY1N2V2dSMnl6T0pDa25NNHZuZkpVMVhNdlErcDVsYmNjS1o3Zks4OHpGdkkyaSsrbWxOYkdEdUxOMmtSU2VJYjRvb2RnY0ludStlZFlDTG1PRjgwVUkvZGVOTGRVdnY2QVExV0IrVU1UVjY5UjJ0Z0RScElEVy9DYUJUVS9RSWdHTGYxcnQrN3BzallzdGEiLCJtYWMiOiIzYmYwOGViZmYyZTc2YjViMjQzMWFmMWQzN2FiMDdjZDEwNmM2NWYyNDQyZWI4YWVhZTA0ZjkwNTdjMDQ5M2RkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:28:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRZOExncEhyd2c0Nnh5SDlLdUJ3M1E9PSIsInZhbHVlIjoibzBBeGgrL0hmTXh0UHpoWkVOK0xsZklrdXJkZVdTMlpWNnd6RUtUcm9pTTFZWW1hZmFLUVR1UngzdERyUDgxYk1OeHd5R3lxeEx4SkgvWUFiYWhSNjZ2ZDFJQm9KOTNRQXJHbVVNSk5TNGRRa0plcWd2N2lrNk1UeHBXUnlySEhhTkVYb25jeGJvREVmdzR4VTVSc2FTeHEvMHRqdEdBeEpsTWhxaE8yZ3o3NTFwVm5NQWVYZDZ0M2p2UXl6MGFIQTFsVnRwVE1TdGY3SWpWWjZWblVpbEQ4R1ZRcXhTd0dsODFRS25pUjRMT3pWTE9SRE1BQkxDdy91dlR0VHFSd1dmZnFVRHhRYnRyWmxIQUVJbi9HKzk1blM1Uk9scmJyWURLZ1F2RHJGSXBsTWswdXBCa0k3UHY2Wk5hNEtEQi9tbytvNXlvckc1eEtWZTZKdkRSaWg2cUFvOEV1eWRGdjhHd3IvdmlGdXV6WHJ4TzZVeFozdDl0aXkxUWs1QjRqYU5mWjg3UGQ5RHNJc1Rrc2thWUc4ZWFqVW04b3EvOXJmakxwRGlJd1ZPZGhNT1ZxNzZBajFtRjJVNnBGY2E2TjgwSUFTTjBncXVtZ2JBUmdlSE1CS1E0TFVHdUFQMGRPR0ZpV2FjZnlaWFZCSTJiN2JtcDZTNWQ3dUpwaHJxTXEiLCJtYWMiOiJkMzUwZTY1MzhiZjZkZjI3Y2Q1MTgzZjlmMDAxOTlmNmUxZDk1ZDA3ZTJkNGY5NzFmODgzYjFlYWY3N2VkZjljIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:28:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZpVVZSbW9LR0htb2Vtei91Mllkemc9PSIsInZhbHVlIjoiN0p2cXBIckFuZTRBcm1NU0RoYnF1RE8zOVhGcFYxVWREeGU1NUlLZzVtTGloNnByMGhzZVU5bVdjVkl1YThmbStMVmN0V0YyZk15aTVNbmU1OGdNaXUyUnlqTk55VHU5dElsd0tndWFiR2szZU9WZ0VzUkJ0aTVvdUJaMjZ5Y1J5ZThHMGFKTzl6a2lNSkhldjF1NWc0Y004WEM0eGhHTDA3dENXZ3g1K3FENHh1Zk9EaU4yaUhoZWR3ZW82QVFkTGR1dEZjV2I1d2h1bzZIOFZjYVdDYmU1REZxNDZiMUdzek1KRkpkbFJLZ2pMN1FwenZ3R04zU3QxUWVmZUpneUxwVzNJMGFmakx3enZRWUJudDFKTnZUbTVZNUVaVDhjeUJyZWJZUExhMnpuVlphMFBSbGRLc3BkVitUOUpMK3MyM1FPaXpsOE5Xem1ueFU2dmVCb3ZQRmZHWExwYWZINHFtdnpYVnBCdno1TitmUlhRY1N2V2dSMnl6T0pDa25NNHZuZkpVMVhNdlErcDVsYmNjS1o3Zks4OHpGdkkyaSsrbWxOYkdEdUxOMmtSU2VJYjRvb2RnY0ludStlZFlDTG1PRjgwVUkvZGVOTGRVdnY2QVExV0IrVU1UVjY5UjJ0Z0RScElEVy9DYUJUVS9RSWdHTGYxcnQrN3BzallzdGEiLCJtYWMiOiIzYmYwOGViZmYyZTc2YjViMjQzMWFmMWQzN2FiMDdjZDEwNmM2NWYyNDQyZWI4YWVhZTA0ZjkwNTdjMDQ5M2RkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:28:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRZOExncEhyd2c0Nnh5SDlLdUJ3M1E9PSIsInZhbHVlIjoibzBBeGgrL0hmTXh0UHpoWkVOK0xsZklrdXJkZVdTMlpWNnd6RUtUcm9pTTFZWW1hZmFLUVR1UngzdERyUDgxYk1OeHd5R3lxeEx4SkgvWUFiYWhSNjZ2ZDFJQm9KOTNRQXJHbVVNSk5TNGRRa0plcWd2N2lrNk1UeHBXUnlySEhhTkVYb25jeGJvREVmdzR4VTVSc2FTeHEvMHRqdEdBeEpsTWhxaE8yZ3o3NTFwVm5NQWVYZDZ0M2p2UXl6MGFIQTFsVnRwVE1TdGY3SWpWWjZWblVpbEQ4R1ZRcXhTd0dsODFRS25pUjRMT3pWTE9SRE1BQkxDdy91dlR0VHFSd1dmZnFVRHhRYnRyWmxIQUVJbi9HKzk1blM1Uk9scmJyWURLZ1F2RHJGSXBsTWswdXBCa0k3UHY2Wk5hNEtEQi9tbytvNXlvckc1eEtWZTZKdkRSaWg2cUFvOEV1eWRGdjhHd3IvdmlGdXV6WHJ4TzZVeFozdDl0aXkxUWs1QjRqYU5mWjg3UGQ5RHNJc1Rrc2thWUc4ZWFqVW04b3EvOXJmakxwRGlJd1ZPZGhNT1ZxNzZBajFtRjJVNnBGY2E2TjgwSUFTTjBncXVtZ2JBUmdlSE1CS1E0TFVHdUFQMGRPR0ZpV2FjZnlaWFZCSTJiN2JtcDZTNWQ3dUpwaHJxTXEiLCJtYWMiOiJkMzUwZTY1MzhiZjZkZjI3Y2Q1MTgzZjlmMDAxOTlmNmUxZDk1ZDA3ZTJkNGY5NzFmODgzYjFlYWY3N2VkZjljIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:28:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058109810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-27254489 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27254489\", {\"maxDepth\":0})</script>\n"}}