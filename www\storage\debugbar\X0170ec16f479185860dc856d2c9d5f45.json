{"__meta": {"id": "X0170ec16f479185860dc856d2c9d5f45", "datetime": "2025-06-06 19:18:11", "utime": **********.838801, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237490.018985, "end": **********.838845, "duration": 1.8198599815368652, "duration_str": "1.82s", "measures": [{"label": "Booting", "start": 1749237490.018985, "relative_start": 0, "end": **********.596579, "relative_end": **********.596579, "duration": 1.5775940418243408, "duration_str": "1.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.596606, "relative_start": 1.5776209831237793, "end": **********.83885, "relative_end": 5.0067901611328125e-06, "duration": 0.24224400520324707, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44800120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.010369999999999999, "accumulated_duration_str": "10.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7100441, "duration": 0.00628, "duration_str": "6.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.559}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.75388, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.559, "width_percent": 13.018}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7894752, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 73.578, "width_percent": 16.201}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.81261, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.778, "width_percent": 10.222}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1493826291 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1493826291\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1340671020 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1340671020\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1282781781 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282781781\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1605914573 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVXbjNEdFVKeWxJMVRVUHN4ZW4rNnc9PSIsInZhbHVlIjoiMFdDVlZPemhNWEU0YWxNbllBVVE5SkZQVndXMXhDZmdxUUxWMjFualc3OEJ0YnB0ZHRmNG9sUFhqUU1WMk10bmR3RDdOTUxJd3dqeUo1RWZDUGRxNlBnZjM0UjhQZTI4bHlGbnJFR0p3NHVuUzJ1bkhKQzZpdFoycG1kY2NpUHN1bFRQKzJWTGs5TG04QmZ2SzZaSkVZTGJNTUkrVVJUeHJ3anFxVlJ5WXJSbDR0RTBvTFVuM1h2Y1NLU3gvMWJyKytMT0hXOXRUZ0lnNnEvMk9ieksrS1JOeHhBdFEvakQ2emUwV2x2VGhFNlV4Ym9HeCtsSUdNRE1NNFNpekNIanJLalVoYWpDTFdyRHpPRkNCc2trZ2tiUlNFQ1NiK0FFTGRPNHhJbDlOYTdEZkpYdUtmZHdFWXdNVEFTMUNjNDk2Tjc4YUdxQ1ovSnhFTkJLYlBEaVE4VTc4VlRBSHI4T0twdHN5ZUdyS21aMDFjZUEyVFY2Yk1HcXplMWZja0s2Z2ZNd1JPS3l1U0t3aDc5RFNWdUM4eFAzMjhsOXV5VmYxemhDaVNhOWUzVnR5STVLbTdUSUFOb3BvVG96aTk1OXZoOHppRE9OTTVOdnhHblB1djkxQWRiamd6WnJkZ0x5ODc2U2ZVNFBqcmxUbnM1V05jaUg3UURJRXZOY0drWjgiLCJtYWMiOiIxYmUzNjQ2NWQ1NDU2MzdkMTQzYjU0ZTVlN2U0N2U4MzA5OGUzMTVjMjJmNTA3OTNjNzUzNWVkZThhNzg0ZGE0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBwNm5DTXJNb0dFK2JqdG0ybDRBZnc9PSIsInZhbHVlIjoiOVQ1dVZjS013NVhKNHJvQlorTXA4ZGViWmZkckZxR1QweE1kM2hhRWxpMnlLZ0NOK1JnOW1ZcnJoTUo2NHgzQldEN3VDdXZDeTB1a0MyL05xUmltQnNCZ3FHM20vTTFNaE1XWTIwNmFXT1pydStmeVg2enJJZFpwaWI2a0h1RWdDSXBEQk8zTHpqTE5lVE1qaVhPVmMyR0NQQkNhTXpzdDk2cXZuWlliYWZsUktvVGtRMlZBbVhBNmhybE5PZXZWT2c5OUlNVVpkWEUzUUtXZXl6cEtPd3krVnlLNzQremY4eGhFSVg4OGg1azRMUExZQ0xlaGVaT1JhZkxEU3cyanNTeFAyQklKNmxKMjNDdnZZSEhaOHNld2FlZ2taYWM2NjBudG0zTmZxVVh6MndJWmVwRlo5VktDclVnVXp3Z3ZoOFBRTlp0MEhxNjNxdGhGdEVLZVpmdjFMVkYxZkFhMnk2RStTS210QUk1TXdlcTRESzYyMGUxNTBMdEo5VnhDQmZHM0RWUTU2d3dzcHl5Y0FlblN6MHcrSHZQWGwvZTUvNnFPUk9aVDFmWi8ydkF4bWZQbzBRb1N2TC9KWDg4VWxLcnhqQkduUnMrYkh5WEFPZnd2WnRCVnVPTU1kdERkNU40c29IUkRTNkszZ1JwNm40Q2tuU3FTNWpRWjVXVFQiLCJtYWMiOiI5N2QxNDc1N2MyMzFhNTY2N2QzMWQ1NGQ4N2Y4ZTgwNGEyNjg4NDZkYTc5NDdlYmQ0YjBkZDhjNGM2YjYwMWUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605914573\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">07zLQ1IzhcCwivgYmqVlKD2YgfwQ5DGdasvfNucP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1682696153 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:18:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdiSnB6RzJYOWEvaGNTa0NSY2pGdnc9PSIsInZhbHVlIjoiSzRraktPd0I1L25vczBCV2pNbFFDcDJXQzBLZXFyRjRQWnBXWVFLWDk4anZVS1BFVXF4U3ptS0Q3Vk1jZWtaTlhqeTVRbTdrWXp1UFNLaEp5ODhsRnRDUnQ0MUtYeVVMd2x6WGIzYU0xbmlmbkhBbk94WDJKQ1oremYxbGNWbDNpc3kxeXBsSHJZS3RwblBJeHpGR2VtcWZPNXQ0REJoNXdLQi9NT2JKd2FSUmdvbUNmMUxBWEl6ZFBVbzloMDk2Z1hSYzZaRFhsUU9SNE5PSUVVcXp0NTJveXFIMFB5a3hFVUwxUXRuTmJTR3lCNkFrMDduZGNxYnFzOHhTUXBJUEltMlpPSUREWHQyYndYN3djVXJzRjQzbFQ5aHlyQisxRVRXY1NvdUdocDF2VFhjWmpDY0hUMytZOWRlbVVtNG4zaGlnSUJlZmZ2YlB5WnZaaGExU1hleUFsTSsyQlJvL0JSRzZuZGd4R0JUMmJjSXRlOWhkN2pBaGEwajJzNVRBTGR3Z0tUcGxTRnh1c0tqNDdCazRzTEFFY01zNUdiT3UvTVRTUU9Cdm53K0FYRENvSGFDekF0UnAwV1g1eElTZGFEKzVqaGxFK2dleUtXamZ0Nm16YnBCSlJzV3VKS0dBWVhRakhVS20vZXdvZWZDVHFJOVJUckR0SVp4MW5YdTkiLCJtYWMiOiI3NWIxMWJiMTA0Mjc1MzU0MzUyNmQ0OTUzOGYxNzVjMjBmZTZhNDJkMDVkNzk0ZTg4ZDY5NDJmODEyOTE3ZWQxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikdzd2tMU2RGTEZPbU9oU0xLNWQvTkE9PSIsInZhbHVlIjoiY2xKK2FpSnlRS2ZmZUhMQzhBclg3VU1vdnhJSVdzaDBiZ2o3ZXJGejNKeEVyWGNUeWwxYlJuZ0xKbDM3K280eDM4T1pPazBuTHdRQ0dXdzZwaFEyNU1SUDBBY1cwY09vUnlCYlhXVkE2SkhCNXZrT3BIQ2hVOVVwVXlNL0VETVdrTmhNQ3JkejZ4RjZ2dFZITllEU0JaQmlnQVRQYm1rOFl4WVN1NnIxMFF0bTJPLzBmeEphcndzU01EZlRYaXBaUHI0REFhaWZKY2ZPS3NpVGZlYWMycjl3c0RvQ3hOait2dDVKRFJBOHZiZDVuR2NlUDBPc3cxNXF5VnAwd3Z0dGd2VkZIbThOL29JVmhpZVhHY1lObXlydjZveG9CUUpYNmpYR2VxaW5pa0g1em1XcUxFV0Y5dnJXYjhwWEMyZ3JTZFl2dE9MTWtoeXBnUkRqcVF5VjEzM3p5Q09jQjNQVFJXNVlnMDkxbVJQMVBOckxjN05Ea25GbExEVVpRN2p0cXRHNFM4SzJ3K0JXNHlpK3o1dGg2b0xqekYxa3RyMFJjd3RXVjJxSFJoN0JrSldvT0hER3piZXNYdWc3V0RIQW9RYSs4d0RzNGtyN3FkYmtkZUN5MUl5c1FMMFQ4MFM5Q1lwcEo5VFF2M2RLU1RnbDVCSWdDTERVMnFuajJVS28iLCJtYWMiOiIyZDAxZjQzOGRjZTQwOTVmZDY1YzI5YjM5YzdmMzIzODg5MDFmNzliNDMzZWU3YTY0N2NmMTJhOGNiYmUyZmUxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdiSnB6RzJYOWEvaGNTa0NSY2pGdnc9PSIsInZhbHVlIjoiSzRraktPd0I1L25vczBCV2pNbFFDcDJXQzBLZXFyRjRQWnBXWVFLWDk4anZVS1BFVXF4U3ptS0Q3Vk1jZWtaTlhqeTVRbTdrWXp1UFNLaEp5ODhsRnRDUnQ0MUtYeVVMd2x6WGIzYU0xbmlmbkhBbk94WDJKQ1oremYxbGNWbDNpc3kxeXBsSHJZS3RwblBJeHpGR2VtcWZPNXQ0REJoNXdLQi9NT2JKd2FSUmdvbUNmMUxBWEl6ZFBVbzloMDk2Z1hSYzZaRFhsUU9SNE5PSUVVcXp0NTJveXFIMFB5a3hFVUwxUXRuTmJTR3lCNkFrMDduZGNxYnFzOHhTUXBJUEltMlpPSUREWHQyYndYN3djVXJzRjQzbFQ5aHlyQisxRVRXY1NvdUdocDF2VFhjWmpDY0hUMytZOWRlbVVtNG4zaGlnSUJlZmZ2YlB5WnZaaGExU1hleUFsTSsyQlJvL0JSRzZuZGd4R0JUMmJjSXRlOWhkN2pBaGEwajJzNVRBTGR3Z0tUcGxTRnh1c0tqNDdCazRzTEFFY01zNUdiT3UvTVRTUU9Cdm53K0FYRENvSGFDekF0UnAwV1g1eElTZGFEKzVqaGxFK2dleUtXamZ0Nm16YnBCSlJzV3VKS0dBWVhRakhVS20vZXdvZWZDVHFJOVJUckR0SVp4MW5YdTkiLCJtYWMiOiI3NWIxMWJiMTA0Mjc1MzU0MzUyNmQ0OTUzOGYxNzVjMjBmZTZhNDJkMDVkNzk0ZTg4ZDY5NDJmODEyOTE3ZWQxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikdzd2tMU2RGTEZPbU9oU0xLNWQvTkE9PSIsInZhbHVlIjoiY2xKK2FpSnlRS2ZmZUhMQzhBclg3VU1vdnhJSVdzaDBiZ2o3ZXJGejNKeEVyWGNUeWwxYlJuZ0xKbDM3K280eDM4T1pPazBuTHdRQ0dXdzZwaFEyNU1SUDBBY1cwY09vUnlCYlhXVkE2SkhCNXZrT3BIQ2hVOVVwVXlNL0VETVdrTmhNQ3JkejZ4RjZ2dFZITllEU0JaQmlnQVRQYm1rOFl4WVN1NnIxMFF0bTJPLzBmeEphcndzU01EZlRYaXBaUHI0REFhaWZKY2ZPS3NpVGZlYWMycjl3c0RvQ3hOait2dDVKRFJBOHZiZDVuR2NlUDBPc3cxNXF5VnAwd3Z0dGd2VkZIbThOL29JVmhpZVhHY1lObXlydjZveG9CUUpYNmpYR2VxaW5pa0g1em1XcUxFV0Y5dnJXYjhwWEMyZ3JTZFl2dE9MTWtoeXBnUkRqcVF5VjEzM3p5Q09jQjNQVFJXNVlnMDkxbVJQMVBOckxjN05Ea25GbExEVVpRN2p0cXRHNFM4SzJ3K0JXNHlpK3o1dGg2b0xqekYxa3RyMFJjd3RXVjJxSFJoN0JrSldvT0hER3piZXNYdWc3V0RIQW9RYSs4d0RzNGtyN3FkYmtkZUN5MUl5c1FMMFQ4MFM5Q1lwcEo5VFF2M2RLU1RnbDVCSWdDTERVMnFuajJVS28iLCJtYWMiOiIyZDAxZjQzOGRjZTQwOTVmZDY1YzI5YjM5YzdmMzIzODg5MDFmNzliNDMzZWU3YTY0N2NmMTJhOGNiYmUyZmUxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682696153\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-909209474 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909209474\", {\"maxDepth\":0})</script>\n"}}