{"__meta": {"id": "X59d6794a4d7c7e9c7e4a3bfbba8637d7", "datetime": "2025-06-06 19:13:19", "utime": **********.689688, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237198.369822, "end": **********.689726, "duration": 1.319904088973999, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749237198.369822, "relative_start": 0, "end": **********.482661, "relative_end": **********.482661, "duration": 1.1128389835357666, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.482692, "relative_start": 1.1128699779510498, "end": **********.68973, "relative_end": 3.814697265625e-06, "duration": 0.20703792572021484, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44812536, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02766, "accumulated_duration_str": "27.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.571816, "duration": 0.02332, "duration_str": "23.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.309}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.618914, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.309, "width_percent": 4.664}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.650336, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 88.973, "width_percent": 6.218}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6691172, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.192, "width_percent": 4.808}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-947532780 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-947532780\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1700640164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1700640164\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-687332899 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687332899\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1159340717 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRXdTB0Y2V6dHhMTlpVdmRRODBhVlE9PSIsInZhbHVlIjoiQlZzRmJ4WTVwdXRobG5lMWd1cTlaQmw0WGdmdXZ0OFFvdmxvZDJhbk4vblR6RUVLb0lvaUlKRGVqQzZubFFna0dycmdnQVFyRnN2TWUyMlZSTXRuUERMOUd6UkEwMEVuNmdOZmhyN3FVN0sxcC9IQXZYWXNNeDkxM1BlOGQwdFBNVmt3b2FzZ0I4Y0V0VlBuckNnTU1oc25zN0UxaWZsd0QyZ1FrVnVSeEtZVWlkc1dZWEZJOGx4UjZRK29OQk94bmdRNmxFNCtSTHpDM1A3WDVpa3hXWjhOVjFyUkRLK05MdGZnKzJSRkRGS05GS3daMHdEcU1WSi9sK3V1TDcvc1VJcENtSGlYRGZ2SmgybHl0ZW04U05lcmRwUVRnTy9JQ01jenlDRExYVWRWOXpKUkNnblB3a3FvVytuT1dUSDVEQ25pRnFLNHovMHF3M0t4bmtXNG1xelhQYStvM09nTU1zclNsNmUwTERTaGxWdEdoZDhvejI0b3BWdUJFL21PV0dqL1ZpWDZJSnhxQW14UkxDV21tOWhSUHFmV1BqUXRQcHZ5TmVBcks4aHFnYy84T1BSQmMyMkx3eCthMVZJeW85TTVhcWpmM01sWkVvWWtvUmRwMUhCdUhlZmdNaWppek84ZS9wY3BBWFB0VW4xRmF2Z1pId3ZCNEdWUUNwaTEiLCJtYWMiOiIwYmNjMGI3YmM5ZjZjZTBhMDExNThhODdlNGIyNDBkZTM2NjVhNmVhNDg3NDE5YWY2ODg5NWM5ZWIwODBmMGZkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImE3MFZPTmlsUlo3WGRvNUgxc1VPZVE9PSIsInZhbHVlIjoiQWczK2xqSkpOTXIxSFZ5NitrMW5DREpkNkpXTUoxa0ZGSTI1eVc5R1o0b2Q0K3V1czM0R2dpSFBZUGdsV3hQaVpIQWFBS2x3ZG8rVFpPa3ptMFdmQ3krQW1iYSsxOWJHQW1jaU9JTFpLWTlTUFY0UVBsZmhjZy9Scno1TlR1V2VMam9pTmFwNmMzZGJydGNUWUNOWVVqVGdCbXk0VmJuTGp1WUVWcS9jRTlJQ0VTaDZXb3ZWOVl1azVtZXI1TUFjOTlWN2ZQWHVhdmJPUUFhc2svUEJyY3kvRWo0eVV4YnlmUFA3ejJEbDVxVFdqN1N3S1NPSUhsTUFYbXpyV3BrR3JyQUsxeGZRaUJuQVI5WmFxN3hhNWFoNlhOaWI0Z3VuZVMvODAzUHdrdHdzVWVsME5RdnZRRUJEeWo3cmtnYkkvOGYydG8yU2NSQUQxMzNnRUN3M3V1YUpBSkpHNHZzYWJDZlZnaC9NRDEzN1hlUXd3K0tqOWFHcXRacG1IZ1A5MUNzSkxMTGdxYVRoWVRGUDQxdzBoL3liSjBBei8xYlFnVk5ob0ZLTEU3b2xMb3EvRXhoM0ZibVp0eXMwaklzN20vekxjQlA1MXpuVG1wYTEvWEJYSWswTGlrdXBFWDB0YUluYXBCNUlpZ0JCbG5UaS9SSEdIdk96RWNFUkR4SjAiLCJtYWMiOiI0M2NjZWJiMWQyOGQwODNkMWI0YTFhYWFlZjRmNWI2NGI5MGQyZmI5MjQ5NGYzM2RhYzY3YjkyODYwN2E1OGZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159340717\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1444084417 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PVt0h9icZb5VqFqziyPToWE7TqW2lYjvh7uvnOnw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444084417\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-246225430 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxHS25oY0x0MCs5bDN5b2JXR2syUXc9PSIsInZhbHVlIjoiRDR6UHZ5Mnh0SHdvSGlKUHB0cG1pWUVNanRrUVFSVG4xVHc4N0kxVDdGbzk0MW9LZWhxRVY3MnlwbFdFeGhwRnlkSmtHNTZtalo3UTV3TjNBZFdPbkNXMHl6RU0zNlFENXE5YnJ5VXBGZHUxRldORUFKd0RYYW1ydDBYSEUvZndBNStyN3JYaWRULzBUK255bVhSU1FPaFJtdHVweHZ4aTBNOWh3TXRseHIwaWtUSVlMVEcvYTUrNWNFcDB4RDV2TXJBaGl4L3JuaWJueHlUeUhZSldWYkxpWkVDWmtNeVVqMCtrTGNGY3l3aDdreXhyS0o4OFNpSVpPNnltMDFVNlo3T3ZkTG1kVHlvWlBzak5xYWMwcDVTT0xXMTR4QWQ2ODdFZzc4cmdIUGc2MklWbjdmM2xyOTVkNXVzUkdNamswYkRlRFNvMFVuTVJWbmZ3d1E4dzZyemVISGpMWjg4cXlIaUtnNzhOS25jODQ5amVYK21zcGhtNy9tWDRuUVJkNXFOajRFc2ZCQ2UxN1d1MVRsZHQ3elFkK2thUFB4eVpNVUJKQ0pOWlJyVEdDZkR4TU9BaUQ2c2VUUjgrcmFYQ245RzZ4WDMrMFVHcjEzUEpaVklFRlFaZFYxQlFRdGpWUVJ5UXpGczAvN0tGdnd6Yk5vb21VVkZ1djZySm1iMTkiLCJtYWMiOiI3YWRlOGFjZWI5MmVjZGU1NDcxYmI4NTMyODVlNWM3MGUzM2M3MzAxZmI4YjU1NWFhZDgwZDJhZmM1MzNkNTFiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZnektTem5TT2YxYjZsQjA0eTVxOVE9PSIsInZhbHVlIjoiSk9wZFc5TlkxZkUyQlordHllN1lwSnhoWW1TK1RWMHpDbmowcG1tbFpuYzVaNDBEMTEvNG9vaWlHM1ByR0VjeU5QbkU5cGVXMzFreUI2MHNiVEtqRGtJZDFhdXp3MnJKN0VUTlV5OHl6M25KZGNaL0RBRkxUaUNFRi83VzBvK0NNczB2aUFkakpBeWIwVTlSaXoyQ1h6QkxZMHB0cWFSNDY0SkRKbmNVTkNsRS9yeWJHSGZtVGUvM2o3YWQ4UGw5ekNCbjhRVEVxM0tIMnJyYWUzSzdCb0Mwb1BRR0dhWXZqTkxEU3l6UndZQmNHVmNWYi9wZktsbkY0M3dTWG9HdTBTQm4xa2NWbENsT0Ira2RjTHZUMTdWTlladkdJei92enNuY0JCNTdMaHlXYUtmSkFkK0NvWWl6dUN3U3MzMzZzb1dUOGo3ZFNVeDIrVG1lR0xOTDRNZjk3Nm83aDJnZm01NVN6QlFqOFBJQ1pIRFZNaDdjVkNLWmE2a2ZxckVPeUlQQUh0TzRySnZRbCtEcDRwUDdCdzVxenJadXVTbXIyVEszbURjTzR3Mk0xdHEvMFpIMzE2SFFNRHJQYWNvOWFVZnZxbEtpcGErS2VubVdtVzBCdTRVeVBKUUJ1aURRbFdhMlovWWwvbE1YU1Ztd3BGYTFIWVk4bThIZURoUWEiLCJtYWMiOiI1ODNiMmY3ZTVlYmFjMzFhMDRhNzA1ODczZTYzNDIzMDBmNWFjZGQ4NDA5YmUxZmI1OTcyMDdlNWM2MTZlMWZjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxHS25oY0x0MCs5bDN5b2JXR2syUXc9PSIsInZhbHVlIjoiRDR6UHZ5Mnh0SHdvSGlKUHB0cG1pWUVNanRrUVFSVG4xVHc4N0kxVDdGbzk0MW9LZWhxRVY3MnlwbFdFeGhwRnlkSmtHNTZtalo3UTV3TjNBZFdPbkNXMHl6RU0zNlFENXE5YnJ5VXBGZHUxRldORUFKd0RYYW1ydDBYSEUvZndBNStyN3JYaWRULzBUK255bVhSU1FPaFJtdHVweHZ4aTBNOWh3TXRseHIwaWtUSVlMVEcvYTUrNWNFcDB4RDV2TXJBaGl4L3JuaWJueHlUeUhZSldWYkxpWkVDWmtNeVVqMCtrTGNGY3l3aDdreXhyS0o4OFNpSVpPNnltMDFVNlo3T3ZkTG1kVHlvWlBzak5xYWMwcDVTT0xXMTR4QWQ2ODdFZzc4cmdIUGc2MklWbjdmM2xyOTVkNXVzUkdNamswYkRlRFNvMFVuTVJWbmZ3d1E4dzZyemVISGpMWjg4cXlIaUtnNzhOS25jODQ5amVYK21zcGhtNy9tWDRuUVJkNXFOajRFc2ZCQ2UxN1d1MVRsZHQ3elFkK2thUFB4eVpNVUJKQ0pOWlJyVEdDZkR4TU9BaUQ2c2VUUjgrcmFYQ245RzZ4WDMrMFVHcjEzUEpaVklFRlFaZFYxQlFRdGpWUVJ5UXpGczAvN0tGdnd6Yk5vb21VVkZ1djZySm1iMTkiLCJtYWMiOiI3YWRlOGFjZWI5MmVjZGU1NDcxYmI4NTMyODVlNWM3MGUzM2M3MzAxZmI4YjU1NWFhZDgwZDJhZmM1MzNkNTFiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZnektTem5TT2YxYjZsQjA0eTVxOVE9PSIsInZhbHVlIjoiSk9wZFc5TlkxZkUyQlordHllN1lwSnhoWW1TK1RWMHpDbmowcG1tbFpuYzVaNDBEMTEvNG9vaWlHM1ByR0VjeU5QbkU5cGVXMzFreUI2MHNiVEtqRGtJZDFhdXp3MnJKN0VUTlV5OHl6M25KZGNaL0RBRkxUaUNFRi83VzBvK0NNczB2aUFkakpBeWIwVTlSaXoyQ1h6QkxZMHB0cWFSNDY0SkRKbmNVTkNsRS9yeWJHSGZtVGUvM2o3YWQ4UGw5ekNCbjhRVEVxM0tIMnJyYWUzSzdCb0Mwb1BRR0dhWXZqTkxEU3l6UndZQmNHVmNWYi9wZktsbkY0M3dTWG9HdTBTQm4xa2NWbENsT0Ira2RjTHZUMTdWTlladkdJei92enNuY0JCNTdMaHlXYUtmSkFkK0NvWWl6dUN3U3MzMzZzb1dUOGo3ZFNVeDIrVG1lR0xOTDRNZjk3Nm83aDJnZm01NVN6QlFqOFBJQ1pIRFZNaDdjVkNLWmE2a2ZxckVPeUlQQUh0TzRySnZRbCtEcDRwUDdCdzVxenJadXVTbXIyVEszbURjTzR3Mk0xdHEvMFpIMzE2SFFNRHJQYWNvOWFVZnZxbEtpcGErS2VubVdtVzBCdTRVeVBKUUJ1aURRbFdhMlovWWwvbE1YU1Ztd3BGYTFIWVk4bThIZURoUWEiLCJtYWMiOiI1ODNiMmY3ZTVlYmFjMzFhMDRhNzA1ODczZTYzNDIzMDBmNWFjZGQ4NDA5YmUxZmI1OTcyMDdlNWM2MTZlMWZjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246225430\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1347678836 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347678836\", {\"maxDepth\":0})</script>\n"}}