<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddShowInPosColumn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:add-show-in-pos-column';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add the show_in_pos column to the product_service_categories table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting column addition process...');

        try {
            // Check if the column already exists
            $hasColumn = Schema::hasColumn('product_service_categories', 'show_in_pos');
            
            if ($hasColumn) {
                $this->info("Column 'show_in_pos' already exists in the table.");
            } else {
                // Add the column
                $this->info("Adding 'show_in_pos' column to product_service_categories table...");
                DB::statement('ALTER TABLE `product_service_categories` ADD COLUMN `show_in_pos` TINYINT(1) NOT NULL DEFAULT 1 AFTER `color`');
                
                // Update existing records
                $this->info("Updating existing records...");
                DB::statement("UPDATE `product_service_categories` SET `show_in_pos` = 1 WHERE `type` = 'product & service'");
                DB::statement("UPDATE `product_service_categories` SET `show_in_pos` = 0 WHERE `type` != 'product & service'");
                
                $this->info("Column added successfully!");
            }
            
            // Verify the column exists
            $hasColumn = Schema::hasColumn('product_service_categories', 'show_in_pos');
            $this->info("Column 'show_in_pos' exists in the table: " . ($hasColumn ? "Yes" : "No"));
            
            return 0;
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }
    }
}
