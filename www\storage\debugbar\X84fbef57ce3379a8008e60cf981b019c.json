{"__meta": {"id": "X84fbef57ce3379a8008e60cf981b019c", "datetime": "2025-06-06 20:42:10", "utime": **********.898093, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242529.282022, "end": **********.898131, "duration": 1.6161088943481445, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1749242529.282022, "relative_start": 0, "end": **********.708828, "relative_end": **********.708828, "duration": 1.4268059730529785, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.708848, "relative_start": 1.426826000213623, "end": **********.898135, "relative_end": 4.0531158447265625e-06, "duration": 0.1892869472503662, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.009240000000000002, "accumulated_duration_str": "9.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8107522, "duration": 0.004860000000000001, "duration_str": "4.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52.597}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.846951, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 52.597, "width_percent": 9.632}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8539839, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 62.229, "width_percent": 15.26}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.870888, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 77.489, "width_percent": 22.511}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1968055116 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1968055116\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1355689534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1355689534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-729135475 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729135475\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-384564936 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=xo2xva%7C2%7Cfwj%7C0%7C1983; _clsk=8pzxed%7C1749242468246%7C1%7C1%7Ci.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImhWbEppU2lsYmMzZWh0V1VOREx3QlE9PSIsInZhbHVlIjoiNFU1RzFOUzBSS0N3NEMzM3ZXWXlUanFoRlY4UTMxZk8rRThhYmc4bHpHL2psdnA2RFZmSHNEQzBIRUkxRTN3R1h2cUt5WmUydnBIcWkvajcrdno1OHdJWFdOVzBpeHBpZklhNytYaEZBRXk0NEhPYkRUTmpmQ3U3NmtrRlFhbHZMR3BXVTBCanFiT3JJS1RxV1R1bnNVakwwWEFYeUFmRWs5Ui90a1NtZENFZWF0VHJHWm9reUxlRnBJOGxIZXJCTXVNSUR3NUd5Y2RabzFTY2Uyb2RETDYrVnpqcWRYQnFycVB1VHBZMnBodytPNm85RUMvbGxWbzVBcC9xQlBndnZET3FmNXM2dnNUcnVHVFl5bTdLM2Z1UXU3MEpFSzk1L2ZkWWF2aElzSENFQXFISzk5UmExK2ZTM2h1OC9RUlprTEFHQmxEbnVSUFVLOWs5NFZDeEswS2RVRWtaQmQ5dTBIV2R1Sk10blp3NHJlUSt5M09VMzFiTklsVVg2MGhYZUVVQmhVcUl0L2hBVW9CeEVBTEFhdmZYTlRTUGx3V2xScURLdFlUblZ1SXlOVHl6OVk5S3VmNHkvUUxnZitDMkRJcnhNVjJsUGJSOVlneVJUYkZGazRQN0NHemxwelBCMDlqdFZ2QVVyWG9sSTJHY3NWZVZ2Qjc2NExXRWFMY2kiLCJtYWMiOiIxMWU5MDdlZWMwZWY3ZGIxZWRjNjQ5OWQ1ZTE3MTZhM2RhZjVlNDUzZDJkZjYzM2IyZGEzYTFlYzcwMTE2YTFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFEc01neG5JWlN6UjNIVmU2dGxwalE9PSIsInZhbHVlIjoiQ3ZuSTUrV1B1TUZNMjFJRURQZkN3VWVTYytHaGtSYlZTQndzRXl5VS81Q2dMdlFtOUZZMVU0Q3crY3BnaFMyZGxYK1hVOUM4UHJEcnpUa0dJOUhnTTJ5aGJHdGFTNCtHMzZEVmg5OEdOWTI5Ry9KdXdWV21IYmJjeTlrZ1daVi9qaEY0S0Q0MnJCeElINlpiVHJsTHVhRDJYSDBmMzhaUXo0aTdpdTV0QnVxOWpsTk9JcEwrR040cDdvZTlsWE5lbndQQi9oeVBjMXRHeERJZml0OWcyWHRkUTEwRHpwS3Myc2txU1FYRHpIekNxU2NtcFFaL0FlblN3YlpieTg2ZHF2dzN5Y3p2VHo5TWdlRy95WjZOK1lDYndOMmdNQWhucGVjOG9mUkZ6LzJWbFlPTUpKd3F3UXgxMEVrbGNWa3NYS3NvT1VPZ05TSjViUUFNRnBnN1FSWkhuMSswYXIxR1FrU0hWc05wejhSVzVod1dPakxCUEYzeXRLbHZEODVzdjZoNWwvTnhRSjJEaGd1MlR2Ky8yTkZhN1Rzbk4zUkYzaU1zZDFvSFFFK3VpYkhmZjhVQzVyRmx0SitVLzc2RDBQcjF4RHdhdThydEltbURYRXBqT0hjRTVmaHlnSW5rUmFlVXV2ZTFBbUQvZ3M1WXFPa1ZCMkZqcTl2b2NmaW8iLCJtYWMiOiI4MWUxODFiMzRiOGI1YzIzNTAwYTEzZWYxYjFjZDhmZmUzYTBiOGZhMjIzNGVhMzgxMmU3MTZkY2I3MGM0MzM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384564936\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1Mgwy0E1kOADJg4wSzllo3ylSTAx8J3JTYC2bXcn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:42:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNVdXZLMEpMV2JDUkJCRWRmRXFsMVE9PSIsInZhbHVlIjoiRE0zS21VbE1sVHo1cVdWaTU3bTZYMnNoMDgrbG1TNytIdTd5dWx1UjErNFM3V1o4UnRla2orWlVZbm5mSkJzSXNkKytCSE5EdUNkajYwejRIYUdveGQrZ21zdndWQTUyc09wMGx6d1pCMStubUtRNU9Ea1NIcHlHRHEzdTBqNnhNRURGOTN5TjR1UnhPWlF3Y2trdmxGWktzdFNMVUttL0MycDRBVmo5NW8zUTZjWGM2TTlnYlZlMlA2VWpoWm96U21qS1NqbjVxSmdvcGkxa2ViRE45ZHJtd1ZRZWQrUlRueklCSk15SG9QSEVyZzRnMlh0VU0yM0l3WnZxaFcyRlRlenQxMXFzVVRwNUc2ZHFQaXRPdDd1TVgrZ1VFVVZiN051d29zcW1BQmduUWkzQXRSMTFuVjV4VG1xeVVPekRtM2RRb0RlMTFhOW9ITU5MRUF1ZVRVcTFxQldKeWxTa3BYVDU0VlRwRmNLbTRGMmEya3lURCtBbVpkSjNDbE5YbFZlWStIN242Wjk0VkVHVkx3bkxUaXl0cFBraU9NM21rZXJOcTR2cGtleC9Tdk5tZGdGZzJHU0YvYkI1ZmswK3k0cndNcStXcVVjL0ZocXBUVGR6WHo2bDZyaHpYWE9hcGk4SFpuL2c1NXpNcmhzQitQditoWG9kTGI0ZTIveGEiLCJtYWMiOiJjOThiYzMxNjg0ZmIyODE5YjQ2NDliODU2M2MzYjVjZDAwYjkxM2RiYmUyZGI1MzBmMmI0MmY3NjhhMjAyMzc5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:42:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZJUm03ek1paDhPK1lud0NKN2hsNlE9PSIsInZhbHVlIjoiQXlvc05iQ2NjemtINXBmbGEyTFZqRGE3czlqRkZKTkJXNjZuTmFOOXF0NFhIODhDeGlRMUIreHR2c3IzZUZnZkZvNWk2dVVVSm1GNVVUTGw1eWMzRVNpVG1TMFk2bm9DdkNYQ3lCSE85UzRqTEoxRW1oaWpGcHhtVWZNV0c2TDA0Y3YzdCtBSWZxLy9IQ3lmS2E3MmdTRjNraWhhTjdCT1R5Tlh2TUdZOVZqMk1DbWZaeEJPWTl6WXM3WDQxQUdzT1VRZW5EdVdITHMwZmoxMW41M090eWFjS3FYSEVHTTA4VVpwQlNQNjE5SEVyOENwVWZmdXJEZTY1QW4rNTAxUHcrWmk3RFZXcXlmdjZETTV5TGl3Z0tMOE9MUXZCeUM0djNaeC9rY3hqcVVhNkZuR01BVkt2M3VsbDNkZG0yWTRWUEp5RGdzSlZ2ejh4dUx3ek11T0s4SXp5T1Z1V0l1cVNVbGYwd0wzYW4vWTA0LzhKdy9FRHk2Zm5nU05ZL0l6bnFOVHBDcjJVRjBsVTBLakc1YnVTdmJVMGZiWVljMWUzdjBzZk5OQ0Nmd1JtWlVoeE4ySUdBRnhXUElOWlIwLzJVRWR5MmFsaUk2elJqVnlubDAwb2h3bm9MR1pMM01tN3A1Y1FZeWhpR1hXb2FqUE45ZG9tTit1VWd4a2wvNHkiLCJtYWMiOiI4YWQxNzc4OWUwOGQ2MDFhYzkyNzQyMDM2NTY1NGM4ZDZkMjA4NDAwMDA2YTNmMTlmMGFkNjkwOWFhNjEzZDBkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:42:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNVdXZLMEpMV2JDUkJCRWRmRXFsMVE9PSIsInZhbHVlIjoiRE0zS21VbE1sVHo1cVdWaTU3bTZYMnNoMDgrbG1TNytIdTd5dWx1UjErNFM3V1o4UnRla2orWlVZbm5mSkJzSXNkKytCSE5EdUNkajYwejRIYUdveGQrZ21zdndWQTUyc09wMGx6d1pCMStubUtRNU9Ea1NIcHlHRHEzdTBqNnhNRURGOTN5TjR1UnhPWlF3Y2trdmxGWktzdFNMVUttL0MycDRBVmo5NW8zUTZjWGM2TTlnYlZlMlA2VWpoWm96U21qS1NqbjVxSmdvcGkxa2ViRE45ZHJtd1ZRZWQrUlRueklCSk15SG9QSEVyZzRnMlh0VU0yM0l3WnZxaFcyRlRlenQxMXFzVVRwNUc2ZHFQaXRPdDd1TVgrZ1VFVVZiN051d29zcW1BQmduUWkzQXRSMTFuVjV4VG1xeVVPekRtM2RRb0RlMTFhOW9ITU5MRUF1ZVRVcTFxQldKeWxTa3BYVDU0VlRwRmNLbTRGMmEya3lURCtBbVpkSjNDbE5YbFZlWStIN242Wjk0VkVHVkx3bkxUaXl0cFBraU9NM21rZXJOcTR2cGtleC9Tdk5tZGdGZzJHU0YvYkI1ZmswK3k0cndNcStXcVVjL0ZocXBUVGR6WHo2bDZyaHpYWE9hcGk4SFpuL2c1NXpNcmhzQitQditoWG9kTGI0ZTIveGEiLCJtYWMiOiJjOThiYzMxNjg0ZmIyODE5YjQ2NDliODU2M2MzYjVjZDAwYjkxM2RiYmUyZGI1MzBmMmI0MmY3NjhhMjAyMzc5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:42:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZJUm03ek1paDhPK1lud0NKN2hsNlE9PSIsInZhbHVlIjoiQXlvc05iQ2NjemtINXBmbGEyTFZqRGE3czlqRkZKTkJXNjZuTmFOOXF0NFhIODhDeGlRMUIreHR2c3IzZUZnZkZvNWk2dVVVSm1GNVVUTGw1eWMzRVNpVG1TMFk2bm9DdkNYQ3lCSE85UzRqTEoxRW1oaWpGcHhtVWZNV0c2TDA0Y3YzdCtBSWZxLy9IQ3lmS2E3MmdTRjNraWhhTjdCT1R5Tlh2TUdZOVZqMk1DbWZaeEJPWTl6WXM3WDQxQUdzT1VRZW5EdVdITHMwZmoxMW41M090eWFjS3FYSEVHTTA4VVpwQlNQNjE5SEVyOENwVWZmdXJEZTY1QW4rNTAxUHcrWmk3RFZXcXlmdjZETTV5TGl3Z0tMOE9MUXZCeUM0djNaeC9rY3hqcVVhNkZuR01BVkt2M3VsbDNkZG0yWTRWUEp5RGdzSlZ2ejh4dUx3ek11T0s4SXp5T1Z1V0l1cVNVbGYwd0wzYW4vWTA0LzhKdy9FRHk2Zm5nU05ZL0l6bnFOVHBDcjJVRjBsVTBLakc1YnVTdmJVMGZiWVljMWUzdjBzZk5OQ0Nmd1JtWlVoeE4ySUdBRnhXUElOWlIwLzJVRWR5MmFsaUk2elJqVnlubDAwb2h3bm9MR1pMM01tN3A1Y1FZeWhpR1hXb2FqUE45ZG9tTit1VWd4a2wvNHkiLCJtYWMiOiI4YWQxNzc4OWUwOGQ2MDFhYzkyNzQyMDM2NTY1NGM4ZDZkMjA4NDAwMDA2YTNmMTlmMGFkNjkwOWFhNjEzZDBkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:42:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1267162668 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267162668\", {\"maxDepth\":0})</script>\n"}}