{"__meta": {"id": "X3f8c3728a6aecb2add07a8bb08527364", "datetime": "2025-06-07 04:15:10", "utime": **********.823396, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269709.278356, "end": **********.823428, "duration": 1.5450718402862549, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1749269709.278356, "relative_start": 0, "end": **********.597339, "relative_end": **********.597339, "duration": 1.3189828395843506, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.597363, "relative_start": 1.3190069198608398, "end": **********.823431, "relative_end": 3.0994415283203125e-06, "duration": 0.22606801986694336, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.011449999999999998, "accumulated_duration_str": "11.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.70155, "duration": 0.00635, "duration_str": "6.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 55.459}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.741078, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 55.459, "width_percent": 12.489}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.775639, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 67.948, "width_percent": 17.817}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7978098, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.764, "width_percent": 14.236}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-125903655 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-125903655\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1119355054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1119355054\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1857373489 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857373489\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-187781840 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269697042%7C2%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktaV3dWOU1XMU92OEg5eXd3SjBzMFE9PSIsInZhbHVlIjoiMmpjbit6NGpvdlpxd1FReXc5cnArd2dlWHlVQ2ZJQnJmWXprSFZ1eWIwTnVoUlVSajBBa0xGUHdxTWxYMWlOKzBQYW90VHlNV3JwcUgyQWYxTzFJRVdUMjlGdlg4SkU2UlpkZG5sdUpVMFY4YThabklIUXIvMjduVFdTRndyREtIdnFOazdzRmNQZzdZdUU0UTc4azlYd0YwMUp3WVk4NnVtWnA0Nm9PWXRtWUFXU0NKc3A4aEh4Sk9JMGxUQzMxWGczTmhQSnZmTzZrNGNCZUI3SnlHVVA1NVgvRjlGS1RLTGpLb0NzYk9aWG1CYmFNV1dWZjRGQjZNQXBMZys1QmlldFltd3JzOUhFS2YxWTg3R05JUVJqOXc3WFZMMnBPUEtVbTZ0TGl3eC9XRVBrMHhaOWh4R1RIeTRjc2VOeVduaHhsT3M2Qk5nWExsTktJSE0yY1BhVEJvMkc5TG8zaGMzcjJ2ZVhSMlpxOUlmcjJTenFQU0hRKzgrbEE2RDFvb0pyNi96Z1IxaEd2S0p5ZEtzdzc5MmdKemVMa0EyakdxSmhEeSsrSGl0Qkdwa1IxdTNOck80ckFBZzdVRW92WENSU0JmWGVkWm95VzdTc2NtTGZVbzZPSFQ2OUlNK2FCSTBXTkhUQ3lxMzQvT1dMeWlFSy90eWU3K2UwNVh3d3ciLCJtYWMiOiI2MDBlZDZlNzA1YmJhZTY1ZGE5MGIzNTNmNTE0MjcyOWMzYTAyNmFmYTllOWY4M2M3MDcyNTViNmUwNjNiMjIxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Inc5UUlqeDRkMXZwUVU1dVVlcU1DQ3c9PSIsInZhbHVlIjoiT2RlQmJ3VE1VOE5ncDhyU2hLV2sxU0c5YWludUtiTzEyZkJ2Ty9wZnRUdTdiN2NJek1VV0tZQkZTNWcxTS9IYjE2RHYyRHJydHUvaWdlQUd1Y3pLNnBLTDdydEcvSlI0R0FVNjVWV21VNkQrckpHcTBmZ0MzNitmTlltNGxUYlg1RjlXNU4wMy9zRWl3L25oYmpLdTAvNTdxVDY4VkpvM3UxT2U2dUx4MUpuNlk5c3RDQzE3NGpOaXRxNkFVdEY1ZmNrR1ZKTTV4V1VOZWxrMG44TWEzK1VYOFZmV1UySlBuNUVxVGVodzZzTzEweGxUYkdURWhqOTlOaXg1d1BYY0h6bGc5ODFaV09TYzE5bHIvMTNXbkZHVlh4OHA0TTNEeDhEVmh1RzdLZXdFamUyYUZVSUQ1RjRCbzd3aWx3Q2Y0b1JsNlRzRWd5dmszVWQ3RUdaNE96Wld6YW02SnlkRXhMWlNCcURkY3RLS1Q4elBtMTRjU0tHTXBNRVg3Q2lRREdwT0I4amNjQ1lKV0NIUmpxMDIzQW00S2lkeVBOSWcrSFNESmk3ejJ2Y0hrT080M3ZHN3NQN0JGSWhUNkJ0dFhOdC9ad1dSR0RTWFFFR0tmWVo0Q0ljR0RoN3NxSVN2V2xrbzlMY1NlSU1SemFzNGlmUStHTGVBOGdMbVMyVXAiLCJtYWMiOiJiYjkzY2ZhMzA2MDc1MjIyMDg0MGUwMmQ3ZjJjZGU4YWRjMDliYzgyMzY2ZDE2YmJkMGIwYTY0ZWZjMmE0NTY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187781840\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-260045130 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260045130\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-855855325 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:15:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZQRXJyRy9ObkxicUQ5dDBCQ2dYWkE9PSIsInZhbHVlIjoiclpFYiszY2pMcWxzUUJiSDNETXAzK3RYNXM1K2hPdmhXa3ltRGtQdXVneVFjZGwrQzhyYnJZSWd5a0YvMXlCVjZnNmlwRjJxSEpYc2FMeS9qSm1PL0dxdXFaWjQ0aGxhZ1NvN0JqSGg5UW9xbDN2RWZNRGlnbHgzWFo4andBQ0VJTWdFWDBLNFR6NWdESWdORHo2aldnQUFWdWkyTURSVTliNTFoSGU5OFVYK2JsTTNrcTVvN1EyMS8xYWxIZmpaU1ljd2kwazBFd3VOdHRqWHdab3FpbXVpN1JKQStmdmc0TERIWVFUUEJQQi8yK3pBS2ZVVE10SklneUtja0V4dU9admZtN3cyY3ppUThMQnpNUFFjT3VRdFQrUkxQOEJEZWF4TjVVN1hCWlkrWlFqUUZZT2V4TUpvVG8wRWFlY0x0NW1aMXpUZ3JoeWRJNlY1Q2FyaERvNGVNMGZTaFVSaUxVaVFqMkVGdmRtRG9LSHUwMDdxeDd6N0tSd09LQjNPWkM5QmhRWDRIWkVIZTlGajVBOW9qZTJwYXQrRWNkdWRNZ0V0VEs5RmZ4ekdLUjZTWmxodGg4bStLVHoxbFJFbGVjczlZUytBTTVHQVVGcGlOaGNjMW9zeW5jeXc3dlAxeVkybTdhaEM4RVJSSjFoWXB6VEc5ZCtrWEozdy9nbFYiLCJtYWMiOiJlZDQ1NzFmYTM3MTc3OTM2YzYzYmMzZTRkOTY3MWRiNjM5MDg4Mjg2NGQ4ZTRiNDVmYjIxMWYxZWNlOTA4ZDM0IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFmUHR2NXJNczZDQVpybGFJdFh1RGc9PSIsInZhbHVlIjoieVNvT2tRMUpRTWJTT01FdnI0QlBJbG1zd1kxaWs1QUFtekd0K1Y3Q1gvWmY3cDdCWFdQcEZFNkZrRHdBbnFmRVVOMHBvNU5NTHNyUENHejZaUUd4OU5DNEFDSEFUbUNuMEFianZNY3V1NjdPQld1akI2cGZCc0FzeG9sN01XZGp5K0t5TWhBcktLcUtqcW90djF1dkkzU3hQek9HYS9LYzNLTlcxeU92R3FrZVlLTnlEUnJFRzA0UDJqcmI4TSs0SHpXcmlVSkQvWGgyb3ZWZWg1QUxGbFZtSjFzaGlNZWF5NFdLNGdsN1JPNldDa001aGhwdURkdyszNUVLamdhMlFJbzU5QXJwcU01cEhoT3BiWU11VXYrSTZNN1NSVllBcEQrdGlQRUZLVW1kSlJ5N2ErVThmQ3VqWThtbG5MbEpBbUpJUHJGQ3JPV2pSbUZzaW5MR2FyZDJOTjI4Q0xHYitKRk44Z0p2OUhXdm5IcFZ4MDVrU1FnbUpRQWMyZUdvUWFCOWNtNTBlMkpoZkh2UitaNDB3NkRMUHgxSHpMQnhOWUxhS1RJcm9HOUlOUnhJNllxZVBNN3ZzaVMzV2pCUEVnK1lhUVB6cGZrL1ZhM2hkSlRRTTN3RlFwUjJUT0xIc201Wkt2UjlySmN0czdMU2lQenN6eW5XQS95RTZ2bFMiLCJtYWMiOiI0NzkxNGJkZWExMmY1NWQyMDNmNDJkOWNjMmIyNzA2ZTJmYjM3MDc3M2YxNjM5ODVjNzA3Y2JjOWYyMjQ0MDVhIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZQRXJyRy9ObkxicUQ5dDBCQ2dYWkE9PSIsInZhbHVlIjoiclpFYiszY2pMcWxzUUJiSDNETXAzK3RYNXM1K2hPdmhXa3ltRGtQdXVneVFjZGwrQzhyYnJZSWd5a0YvMXlCVjZnNmlwRjJxSEpYc2FMeS9qSm1PL0dxdXFaWjQ0aGxhZ1NvN0JqSGg5UW9xbDN2RWZNRGlnbHgzWFo4andBQ0VJTWdFWDBLNFR6NWdESWdORHo2aldnQUFWdWkyTURSVTliNTFoSGU5OFVYK2JsTTNrcTVvN1EyMS8xYWxIZmpaU1ljd2kwazBFd3VOdHRqWHdab3FpbXVpN1JKQStmdmc0TERIWVFUUEJQQi8yK3pBS2ZVVE10SklneUtja0V4dU9admZtN3cyY3ppUThMQnpNUFFjT3VRdFQrUkxQOEJEZWF4TjVVN1hCWlkrWlFqUUZZT2V4TUpvVG8wRWFlY0x0NW1aMXpUZ3JoeWRJNlY1Q2FyaERvNGVNMGZTaFVSaUxVaVFqMkVGdmRtRG9LSHUwMDdxeDd6N0tSd09LQjNPWkM5QmhRWDRIWkVIZTlGajVBOW9qZTJwYXQrRWNkdWRNZ0V0VEs5RmZ4ekdLUjZTWmxodGg4bStLVHoxbFJFbGVjczlZUytBTTVHQVVGcGlOaGNjMW9zeW5jeXc3dlAxeVkybTdhaEM4RVJSSjFoWXB6VEc5ZCtrWEozdy9nbFYiLCJtYWMiOiJlZDQ1NzFmYTM3MTc3OTM2YzYzYmMzZTRkOTY3MWRiNjM5MDg4Mjg2NGQ4ZTRiNDVmYjIxMWYxZWNlOTA4ZDM0IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFmUHR2NXJNczZDQVpybGFJdFh1RGc9PSIsInZhbHVlIjoieVNvT2tRMUpRTWJTT01FdnI0QlBJbG1zd1kxaWs1QUFtekd0K1Y3Q1gvWmY3cDdCWFdQcEZFNkZrRHdBbnFmRVVOMHBvNU5NTHNyUENHejZaUUd4OU5DNEFDSEFUbUNuMEFianZNY3V1NjdPQld1akI2cGZCc0FzeG9sN01XZGp5K0t5TWhBcktLcUtqcW90djF1dkkzU3hQek9HYS9LYzNLTlcxeU92R3FrZVlLTnlEUnJFRzA0UDJqcmI4TSs0SHpXcmlVSkQvWGgyb3ZWZWg1QUxGbFZtSjFzaGlNZWF5NFdLNGdsN1JPNldDa001aGhwdURkdyszNUVLamdhMlFJbzU5QXJwcU01cEhoT3BiWU11VXYrSTZNN1NSVllBcEQrdGlQRUZLVW1kSlJ5N2ErVThmQ3VqWThtbG5MbEpBbUpJUHJGQ3JPV2pSbUZzaW5MR2FyZDJOTjI4Q0xHYitKRk44Z0p2OUhXdm5IcFZ4MDVrU1FnbUpRQWMyZUdvUWFCOWNtNTBlMkpoZkh2UitaNDB3NkRMUHgxSHpMQnhOWUxhS1RJcm9HOUlOUnhJNllxZVBNN3ZzaVMzV2pCUEVnK1lhUVB6cGZrL1ZhM2hkSlRRTTN3RlFwUjJUT0xIc201Wkt2UjlySmN0czdMU2lQenN6eW5XQS95RTZ2bFMiLCJtYWMiOiI0NzkxNGJkZWExMmY1NWQyMDNmNDJkOWNjMmIyNzA2ZTJmYjM3MDc3M2YxNjM5ODVjNzA3Y2JjOWYyMjQ0MDVhIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855855325\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-715412445 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715412445\", {\"maxDepth\":0})</script>\n"}}