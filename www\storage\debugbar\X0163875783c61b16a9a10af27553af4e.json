{"__meta": {"id": "X0163875783c61b16a9a10af27553af4e", "datetime": "2025-06-06 19:15:16", "utime": 1749237316.076302, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237314.542459, "end": 1749237316.076334, "duration": 1.5338749885559082, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1749237314.542459, "relative_start": 0, "end": **********.886679, "relative_end": **********.886679, "duration": 1.3442199230194092, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.886701, "relative_start": 1.3442420959472656, "end": 1749237316.076337, "relative_end": 3.0994415283203125e-06, "duration": 0.1896359920501709, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44755096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01926, "accumulated_duration_str": "19.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.981123, "duration": 0.01687, "duration_str": "16.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.591}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749237316.032639, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.591, "width_percent": 7.217}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749237316.053793, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.808, "width_percent": 5.192}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1261879490 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1261879490\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2002808047 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2002808047\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-406477754 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406477754\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1009846751 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJodDBBcTZmVzJQWEpXNjVmems1RHc9PSIsInZhbHVlIjoiSkNoRDFNcDBsN2dGU0YwWkZJTElCWWFab2lNRy9Sd3A5UThPcGg2WjJlYzNudFZ3a25TWUJvWXRWbXVnRm9XUWRjNmxDcDlGNS9TUWxzU25sajJYSklxakYvYlA2L3M0Vis1L3BhT1NqU09JUVpLWFJzemkzcm5Ia0hWNDZaK1JnSUY0bnpPYlFHR2E0UXJCZWVYR0pFTlkvUjZqK3BLb1RkRThuSXk1QzZNSTdXVUU2RkkzN2tiRFY5M1NZYWh4Rk5NUkNBYllxZHhBSzNnNDR5aHFodHFONlc2bmpPTG9laTJ3dlQxdW85QkFYUkVXZ1V0ZUlwYzg0NTA0TVJEREFZcjBwN1RyclBZaWVJS3pMNjhvVkRxZkRvOUl3TU11dk5zSUNPa09aSlBlM05rUXRRLy84ZjRHV3pLVThGWitRZDFvVDdBeHozMERseE1JbDVrbkhXbGVCRlhMY1IyTFl6NFVRSnNLeDBkcGEyR20vVW56SmU5ZmZjbW5XNnRHb3hqUytlZUtTVHR2MUppajdTNUswTTVWOE1heWVvT0tFNXRWSnl6enpMRkNnM1I2eFJ0b1RxTngzNlcyOFo5U3FCRlJLNGViZm82akhGMmlLbFlDUW9Eb3lnZG5FdGt4TGREM1EySUFpVDE1QWJoU1VDd1FTZ0pPV0JLMHBqWWkiLCJtYWMiOiJkZjQ3OWViZDQyOTljZmJiOWVjNzlhNWYzMmRiMTJhMmViM2VmNWUwYjVmMjE0ZDZjNzUxYTkyNzg3OTY2ZGM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii95VTVBL2o3ckc1ZDVxaWFwRHkvcmc9PSIsInZhbHVlIjoiM2E1a3c3U3l6QThOZFJBOTJhTlVjSG1LZTlKOUpXZEN1WStxdlZTMzJPYnZiUFVRY1loZWZaSmRBRHRtWFhXZHdNS1c0ZUV3RTR3cE9xY1BZUmVqaExOTVNXRXBRUllya2hjN3IySlZycDJTcC9rNmdDQWUyNncrK285c3dHNk5ESXpQQyt3Y2RUMFEzNmJsWENGc003N3NxL2N1aWpnTi80RG9hZkpWUHJFRGxyMGpjbVdzTnRpZmpRYnFhdGNuc2RjeFdsWW5takszNFZhTWMwN2ZuamxUZUNBczVIR0JBZWlxVWpNcCtLSE4xN0lBZ1U2R0RLMVFlOE1nd0IwQ0k4cHhGeU1Cc2tDN1ZFU0ZhRFgwR202OU0vZEZ3cmJRM0E5R3ZPRFpPSTFRZDdJV2pqUER4ZWFQWDNnM2R3TkZtZmxKQXBXM1BBVEpKa3Vkekx2NUthNnZtZVBFbDdIQ0pSSGM5Z0FkY0ZpaXZJbm9KdERjSXFIVUduTG5XMjE4dmNFRGMzckFLNzZkWjN4ZGlrZ1dUYXZkVWhZMGxNZFkxU0YrYlR6THlWV0VVRzNmQ254ZnlvNk1WUTJXNmlqVWRHZ0oweDhNRE0yVjB2VDBmMXhwMlRwU1J1SWlKTFVSZExwRG1Da0ZwdDFNMlZMR1ZqaEpaS3ozYnpDL0VlczIiLCJtYWMiOiI1ZWI2N2Y4OWEzY2I2ZDBkMjJjY2U3NWM2OTAxNTY3OGQyMmY5OTc4OGE1Yjc4ZjRlNjkzNGUxMjJlYTk1MTQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009846751\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1254532346 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:15:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik44RmNJODQ4VXBTbi9mSTJwdTc5QWc9PSIsInZhbHVlIjoiN3ZENFQwaGkzb2R0bUNoM21RTWxJVld5eHpNSWVvYzN6cTZ6SXRmZHMrMWwydmtWZ0NKSmNPc3gxenZzSWxHNDdSd3QxL1VnM2VLN1V0ZFpKWkJKcDkyOXh6UGozckQ5cjZDcGtBL1RUa3EyRWtiUXVxUDBBajBvTEEydFJwclhlcTFYYmRJK2FDMVk1UTJwaVBQL0VvdmdBL3dHcGdka0ZpcTNCcUFnNm00ZXlqaytubmxxSlYwZy9yRmpDbWJrbFRPeG1hZ1NJVGlNTXJSRFFrdTdQNGs1RWEyVlUrODgwOE1DdWcyMXVaVlN2WkVPSU41TkhqTURDc3UzY1JHWENHemJZcTNzMVZ2N05JV3RkQzFKUGlscis0MVgwdDN5T0JyY2NYUG52NzVJZTU0d3FqcjdPWnZQWWZlK3h3aEhSa045Vlo0dm1XdEZEUXV1VkR0Z0NMMmpIbmswS2V0dGNHbGZCUmNDdktCSGZ3T0lxcVh2dnhBVTA2bUlKSlV4Tm9kUStUaDNkdG8ram11Z0t6dlJtRmd5T2dIN2VhdTVtSzg2YUNnNk9HZnNxME83Q3NYcVQyOUltL3pUODcvZk1BK3pDTGQwWThxWDFqL0RLekFsQmNoTlNpditvMTV1N0lEVXJETGJ6M0hwZ3k1NGZsZmxCWUNxOUxNY05ycXoiLCJtYWMiOiJhMWZjYzYzZGU0MWVlNTJmOTUzYTAyOTlhZWRiNTE3YTdkOGQzYTdmYTBiZmVhMzEyNWE1ODhkNWY5NjA3NmI2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikg3dys0b0JBc25qMXdaWk9rYll0Q0E9PSIsInZhbHVlIjoibFZjWm4wRHl5V2Joc1owU0FNRjZxUGQ4ajk1QlRpS2ZTdXQ0WTZmc2ZqUXorcUIyY3AvMExyTUxWNWlRSUt4YVQxbmFaNWp4cm83dENjZXpPVVp5d3VxV1o0Qllza0NMR3JLaWtrdjR1eUFEU29KNkJobTBFVnZ1bTlOM1d2TzVIU2xEbEpSSG5GS1M1c3N5bXlzU0t0bGo4OFczekxLNC9hV2FwbW96Uy83T0xISXhKOUZDblk1MlMwZzkvSE5hRlNTNzZCeUYyZUp4S3pKY2pENVk3RXZsaEJiaGFxby9EQldsNUNnWEtnU3htaWZ5cXdSeHlzQ1JQa2dCd3gvWFFUYk13VU1JdGJtQmQ1MWdUbm1VdndoMEtWQm1MZmtwMk5xQkZidis2aXd0d3NTckorNFVEZUR2aEluMFlFWTlGOGZwSTVNK1MySHVDQklxTlVNb0czZ3VIN0xzWmEra0pHclA3R2tIUkNTQ1krVnBMMlF4NEthcnFrZDZxV0F3ellLeS9VSENtRzYxMkQ1RUs3NWJadEVHb2xsTTc0OTMrMUcxWmJZU3dOYUdvSkNwbjFPYVdUTVpiYWJINGNNUFZmczZvajRaZkNCRGM1R2JxcTBUbGM0WUN0V2dxRGFPSDdyV0MrZkJ0T3lNK1pITEdGb01lcjZzTEdPb29jVE8iLCJtYWMiOiI5YTVjODZlMTExZTE5NTJhMzkwNzA3NGUyZTIwYzVjMWUxYmI0ZDI2YjE3NWRjYjlkMzJjZDBlYWJjOGFiZTRjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik44RmNJODQ4VXBTbi9mSTJwdTc5QWc9PSIsInZhbHVlIjoiN3ZENFQwaGkzb2R0bUNoM21RTWxJVld5eHpNSWVvYzN6cTZ6SXRmZHMrMWwydmtWZ0NKSmNPc3gxenZzSWxHNDdSd3QxL1VnM2VLN1V0ZFpKWkJKcDkyOXh6UGozckQ5cjZDcGtBL1RUa3EyRWtiUXVxUDBBajBvTEEydFJwclhlcTFYYmRJK2FDMVk1UTJwaVBQL0VvdmdBL3dHcGdka0ZpcTNCcUFnNm00ZXlqaytubmxxSlYwZy9yRmpDbWJrbFRPeG1hZ1NJVGlNTXJSRFFrdTdQNGs1RWEyVlUrODgwOE1DdWcyMXVaVlN2WkVPSU41TkhqTURDc3UzY1JHWENHemJZcTNzMVZ2N05JV3RkQzFKUGlscis0MVgwdDN5T0JyY2NYUG52NzVJZTU0d3FqcjdPWnZQWWZlK3h3aEhSa045Vlo0dm1XdEZEUXV1VkR0Z0NMMmpIbmswS2V0dGNHbGZCUmNDdktCSGZ3T0lxcVh2dnhBVTA2bUlKSlV4Tm9kUStUaDNkdG8ram11Z0t6dlJtRmd5T2dIN2VhdTVtSzg2YUNnNk9HZnNxME83Q3NYcVQyOUltL3pUODcvZk1BK3pDTGQwWThxWDFqL0RLekFsQmNoTlNpditvMTV1N0lEVXJETGJ6M0hwZ3k1NGZsZmxCWUNxOUxNY05ycXoiLCJtYWMiOiJhMWZjYzYzZGU0MWVlNTJmOTUzYTAyOTlhZWRiNTE3YTdkOGQzYTdmYTBiZmVhMzEyNWE1ODhkNWY5NjA3NmI2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikg3dys0b0JBc25qMXdaWk9rYll0Q0E9PSIsInZhbHVlIjoibFZjWm4wRHl5V2Joc1owU0FNRjZxUGQ4ajk1QlRpS2ZTdXQ0WTZmc2ZqUXorcUIyY3AvMExyTUxWNWlRSUt4YVQxbmFaNWp4cm83dENjZXpPVVp5d3VxV1o0Qllza0NMR3JLaWtrdjR1eUFEU29KNkJobTBFVnZ1bTlOM1d2TzVIU2xEbEpSSG5GS1M1c3N5bXlzU0t0bGo4OFczekxLNC9hV2FwbW96Uy83T0xISXhKOUZDblk1MlMwZzkvSE5hRlNTNzZCeUYyZUp4S3pKY2pENVk3RXZsaEJiaGFxby9EQldsNUNnWEtnU3htaWZ5cXdSeHlzQ1JQa2dCd3gvWFFUYk13VU1JdGJtQmQ1MWdUbm1VdndoMEtWQm1MZmtwMk5xQkZidis2aXd0d3NTckorNFVEZUR2aEluMFlFWTlGOGZwSTVNK1MySHVDQklxTlVNb0czZ3VIN0xzWmEra0pHclA3R2tIUkNTQ1krVnBMMlF4NEthcnFrZDZxV0F3ellLeS9VSENtRzYxMkQ1RUs3NWJadEVHb2xsTTc0OTMrMUcxWmJZU3dOYUdvSkNwbjFPYVdUTVpiYWJINGNNUFZmczZvajRaZkNCRGM1R2JxcTBUbGM0WUN0V2dxRGFPSDdyV0MrZkJ0T3lNK1pITEdGb01lcjZzTEdPb29jVE8iLCJtYWMiOiI5YTVjODZlMTExZTE5NTJhMzkwNzA3NGUyZTIwYzVjMWUxYmI0ZDI2YjE3NWRjYjlkMzJjZDBlYWJjOGFiZTRjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254532346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-777392709 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777392709\", {\"maxDepth\":0})</script>\n"}}