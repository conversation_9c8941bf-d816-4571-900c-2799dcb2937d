{"__meta": {"id": "X0069b9c898965efdb2861f7713ba39a7", "datetime": "2025-06-06 19:26:41", "utime": **********.387607, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237999.674291, "end": **********.387641, "duration": 1.7133500576019287, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": 1749237999.674291, "relative_start": 0, "end": **********.168422, "relative_end": **********.168422, "duration": 1.494131088256836, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.168448, "relative_start": 1.494157075881958, "end": **********.387645, "relative_end": 4.0531158447265625e-06, "duration": 0.21919703483581543, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44800096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01585, "accumulated_duration_str": "15.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.254279, "duration": 0.0114, "duration_str": "11.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.924}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.299755, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.924, "width_percent": 7.508}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.336348, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 79.432, "width_percent": 10.032}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3589702, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.464, "width_percent": 10.536}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-906423900 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-906423900\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1692067526 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1692067526\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-562855380 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562855380\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-816103278 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237988999%7C26%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA4dC9YNktPYVE5bHZWVDBlS21EQ1E9PSIsInZhbHVlIjoiZW55MzlhOHNoODJKS3QxL2gwUjkreElUQTF0ckMzblk3d1pzWXR1WXRrK3paK1l3WDV3ZlRjK0Y1MElmZGdRNStSSStyeFRTMWpuTG8xUWl1Qnd4QzBaUlZQYUJ6WXVBV3ZyT0xmbjI4elNpT3hYaEVWMWdwU0ozWlhMbmJCMCttR1ZzcVlwY3M5N2JvdThWU2RITld1dHdnUGs3NFZvT2QxL1ZpSStlQlZMQkczSEJCaGhkNE5oOFVNZnFxclh6NW5kNG9peVR6aitPZ2N2MG40cUJIL0JFMWN6aGVnU01ob0l3S0RnMHlIbjA3MzQzVDNqckpYUktXQmZNVGUydkRkeFlCNnlzU25XWG9GM0V0TmNwdURSSlFaZnpKYXJFd0JwSUJuRFJRVzA4VWVEOFdzZnhCM3hVS3hQNjlEY2RYUHJnK3JPODJoM2g3cGhEY3BKRStpODlaYVExTGh2dW9FcHNDR2w0UStkQzdQRVBDMW9OenlyQ25tOEtHejVONkpLakZWbit2ekJnOHBOV0xoa051bFd0aFN2QlVpSkdCVWI5ZDcyRmhrUzFyTmNvbkJMVFNZNUdmZ1Z1MTVKYk04eGhGdENPREZ5ajloM0dvalpwTmpGSUR3ZzlUNmJ3U1orL0ZQTDdiT2FRT0l2blRQN1BvcXZxUGd4R1NBVEgiLCJtYWMiOiIwY2YxNWUwYjAxMmQxOWQwODA4YWRlODg5M2ZhODg4ZmI3ZDAxNzc0MzI5ODg5NmNjNGE4NGU5NjBjOWE3ZjM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkM0dzNZaUxYbGo0Mm5KOGpLQTZTR2c9PSIsInZhbHVlIjoibzRGWi9QZlNYQjFueEl4RFltU3E2NDNZVlJvT3lEbTRON2FSS1pOV1FTemZkY2dIN1UzQmZDSXRJNFZIV2l5TTJORHVqR003THVLbDA5NThkbzNhOXlvQjRlcjY3V3Nod3NlRkJKamh6WFV1a2EyWGN6dEE2dXhtYUhvb0RtdGxyMW1VeElQUFhZMkIzWS9rSEVFKzB5T1VZK3E2dVFnWWhSVENtQUE1KzNuRy9jbzVXY24wRkUzTkRjNmpCSThaRDFqYVpMOHZRL1J6VXJMSEJPVUs4ZU0yYjhHQUsxalhvWXVyRyszRDFZcGc0UmFJWTR0Mm50d3lhUVpkTmQ4WDNPay9BUU1obHV3MkR4ZERKeEJsZ1FQLzgyVFlEODFHZERQMnJ2TFVLaDVKQ0NnOUVaM0RVMWRpSVBuWHM1M3N3dVRJTjNSWlA2MWNKYnh6UlZzVXlqeWE5STVuN2xkSEZMTFd0aTJjNURjdEgranRMcTl0MkNST2FtVzgwclpPSHJ5KzAvRmJFL2l6emZFS2tSYW95RHNZMHNSNDFicTJaeGFvL09GcDFGZ3ViSjBoL1VOZDlCRzRKRkxOcXdxWnVwblQ2eFNnYSsyNWlUaXdkeDNVVmU1Q2ZJRThvd3Iyd3RwS2MybUZjN0RkMko2d1hIWHV6RXU0WCt1K1lVbnYiLCJtYWMiOiJhMDQxODljNTdkMTYwMDhmZTkzOTYxYTdjM2Y2YzM1YTM3MmM0ODFiZTY5OTdlNjc0YTc5YjViMDA0MDgxODVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816103278\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-838409853 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838409853\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1250964559 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:26:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im02d0ZwMlJSa1IvajlkUUtobHM5T3c9PSIsInZhbHVlIjoiQ3NoZFo1Y1dCalVpMUVtQmN3YlNOY1pqNStFdnpoNzBLbldETURianJrTTVOYkN5enJGVytQU0hjb1l1R0szd1ZUSVU2RW4xWUw1bWZoaVZmS2RUcnB4U2FzQ29EaFpHUEErRCtFWE1UdElZdldVKzhSODhvL0llMFh6eXRNNDlRaFNtNTR1QnhsaDYvRWx0RWJCakRjeFEvRUpjZ21lM2lNVTJpSVRmQVhTU1hUZmZvT3M1NUVGaml6T3hxRmVJNE5BclJUWk8rRFNwblIrem03WjM5MjFLdTNiRFJHMXl2bnpsVzkxbjN5S3JqM3lGV01peHlrZzhBR1gwZG5hNURrVTkzTXJjYkp6OVpVeDhrMlNvRTlwY09TVGRRUDFTM1JTcEYwVlltUVBOcXpGS3ZRUVFOU01wYVlpa2JoeEIxOWNhWTU2YmtrTHRxSkdnakg0MUZhS1h3VnpJQTJOS25xbUFHazdCOVZLYldhR0ZrRWswb3RzOCsxbENKZ0w0U3RzbGxtRU1LRWJNdXIvTTYvcWE0MnB1UUhDNjBHUHhhVmd6SWpjemo5ZW1XMVc4QVR6VWRGd1F5YVBpTlZiaEY0Nkl5TUZHUXY5WW1vN3owSHI3clJhZk4zTHd5bWNIMW1nK0ovNjBVKy9sTit3QWFQbjZZcTB3d3pRTjZmR2IiLCJtYWMiOiIwYTMwMTRjZmJhMWExNDBiOGFlMjEwM2RlZmI1MjI0ZjYwYzYxNWRkZDFhZjk2ZTNmM2Y3OTljNTFiMjE4YjAyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjgvN3lubDZGNER2ZUVPRXlkVERjUVE9PSIsInZhbHVlIjoiSmZaZW9GNnFITlByOE5pVnoxTEtjNlFZWE90NVZQMFNWNEtLT1F3dzZpKzlnYmZvR05wYXk5YW9PL1VuY2k2RUVocG9vSVYzT3FTQW5DSi9XYnA1alZYMUZGcFJUQkR6MGVSenRzcUJCWEVnTEY4aGkrelBiNTJwZ2xDM2Q5L2ZiYm8wYVJCekVkOHAxc2xQWUR5SFR3cUlDS2t4ZVlYdWpKb3gyblNiN05nOG10Q2RKK0loaTM1S2lSSHVlczZWWDBhSmNBTk1UcW1zMnIyK0hoT3lrbzJueTF0MEdoNVJ5NU9uVDVTR25tTitKakZkaUYyWkRwM2lzNjdKckJKRTNDcHNrTm5TQWZ2M3Q0d2RXVTI5T2h5eUluTmpaM1lpdk0rc1lvREZQVUUvdDA3WjNVV1lGNElCdmdzUlpSMjkxbWtEak1FdW5iMldjamVGcFZCZUhzUkFsNWpLbjM1YUVSOFJGQUEwUDc2WVJ6UERwelB5bUdHK0xDZ01ObmdaWXd2UHNrY2tpNXZiMG9vYmcySi9qVGYyUzBPKzhPdGRhYW9kbTVTMkU5L1V5TzlmRThVaWxMU1B0a3FTQ1ZPNlBrVjVFempkUFQ1YW1NdUpIYkI1R1dtU3l3V3dOL05pYVdHT0hmWGpMVDArOE9zc01CdGZIUUxBZ2tUd2xHWFkiLCJtYWMiOiJjYTc4YzIzNzhjMGU0ZjAzODk2NDU4MmRmYzRkMWUxOTc5Mzc4M2RhMDNlOGNkN2ZhODE3MjhkNDg4ZGRhZGQ3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im02d0ZwMlJSa1IvajlkUUtobHM5T3c9PSIsInZhbHVlIjoiQ3NoZFo1Y1dCalVpMUVtQmN3YlNOY1pqNStFdnpoNzBLbldETURianJrTTVOYkN5enJGVytQU0hjb1l1R0szd1ZUSVU2RW4xWUw1bWZoaVZmS2RUcnB4U2FzQ29EaFpHUEErRCtFWE1UdElZdldVKzhSODhvL0llMFh6eXRNNDlRaFNtNTR1QnhsaDYvRWx0RWJCakRjeFEvRUpjZ21lM2lNVTJpSVRmQVhTU1hUZmZvT3M1NUVGaml6T3hxRmVJNE5BclJUWk8rRFNwblIrem03WjM5MjFLdTNiRFJHMXl2bnpsVzkxbjN5S3JqM3lGV01peHlrZzhBR1gwZG5hNURrVTkzTXJjYkp6OVpVeDhrMlNvRTlwY09TVGRRUDFTM1JTcEYwVlltUVBOcXpGS3ZRUVFOU01wYVlpa2JoeEIxOWNhWTU2YmtrTHRxSkdnakg0MUZhS1h3VnpJQTJOS25xbUFHazdCOVZLYldhR0ZrRWswb3RzOCsxbENKZ0w0U3RzbGxtRU1LRWJNdXIvTTYvcWE0MnB1UUhDNjBHUHhhVmd6SWpjemo5ZW1XMVc4QVR6VWRGd1F5YVBpTlZiaEY0Nkl5TUZHUXY5WW1vN3owSHI3clJhZk4zTHd5bWNIMW1nK0ovNjBVKy9sTit3QWFQbjZZcTB3d3pRTjZmR2IiLCJtYWMiOiIwYTMwMTRjZmJhMWExNDBiOGFlMjEwM2RlZmI1MjI0ZjYwYzYxNWRkZDFhZjk2ZTNmM2Y3OTljNTFiMjE4YjAyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjgvN3lubDZGNER2ZUVPRXlkVERjUVE9PSIsInZhbHVlIjoiSmZaZW9GNnFITlByOE5pVnoxTEtjNlFZWE90NVZQMFNWNEtLT1F3dzZpKzlnYmZvR05wYXk5YW9PL1VuY2k2RUVocG9vSVYzT3FTQW5DSi9XYnA1alZYMUZGcFJUQkR6MGVSenRzcUJCWEVnTEY4aGkrelBiNTJwZ2xDM2Q5L2ZiYm8wYVJCekVkOHAxc2xQWUR5SFR3cUlDS2t4ZVlYdWpKb3gyblNiN05nOG10Q2RKK0loaTM1S2lSSHVlczZWWDBhSmNBTk1UcW1zMnIyK0hoT3lrbzJueTF0MEdoNVJ5NU9uVDVTR25tTitKakZkaUYyWkRwM2lzNjdKckJKRTNDcHNrTm5TQWZ2M3Q0d2RXVTI5T2h5eUluTmpaM1lpdk0rc1lvREZQVUUvdDA3WjNVV1lGNElCdmdzUlpSMjkxbWtEak1FdW5iMldjamVGcFZCZUhzUkFsNWpLbjM1YUVSOFJGQUEwUDc2WVJ6UERwelB5bUdHK0xDZ01ObmdaWXd2UHNrY2tpNXZiMG9vYmcySi9qVGYyUzBPKzhPdGRhYW9kbTVTMkU5L1V5TzlmRThVaWxMU1B0a3FTQ1ZPNlBrVjVFempkUFQ1YW1NdUpIYkI1R1dtU3l3V3dOL05pYVdHT0hmWGpMVDArOE9zc01CdGZIUUxBZ2tUd2xHWFkiLCJtYWMiOiJjYTc4YzIzNzhjMGU0ZjAzODk2NDU4MmRmYzRkMWUxOTc5Mzc4M2RhMDNlOGNkN2ZhODE3MjhkNDg4ZGRhZGQ3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1250964559\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-675993122 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675993122\", {\"maxDepth\":0})</script>\n"}}