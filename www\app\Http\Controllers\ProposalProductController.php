<?php

namespace App\Http\Controllers;

use App\Models\ProposalProduct;
use Illuminate\Http\Request;

class ProposalProductController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\ProposalProduct  $proposalProduct
     * @return \Illuminate\Http\Response
     */
    public function show(ProposalProduct $proposalProduct)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\ProposalProduct  $proposalProduct
     * @return \Illuminate\Http\Response
     */
    public function edit(ProposalProduct $proposalProduct)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\ProposalProduct  $proposalProduct
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProposalProduct $proposalProduct)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\ProposalProduct  $proposalProduct
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProposalProduct $proposalProduct)
    {
        //
    }
}
