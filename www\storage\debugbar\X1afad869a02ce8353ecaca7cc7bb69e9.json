{"__meta": {"id": "X1afad869a02ce8353ecaca7cc7bb69e9", "datetime": "2025-06-07 07:30:22", "utime": **********.039256, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.113091, "end": **********.039283, "duration": 0.926192045211792, "duration_str": "926ms", "measures": [{"label": "Booting", "start": **********.113091, "relative_start": 0, "end": **********.927681, "relative_end": **********.927681, "duration": 0.8145899772644043, "duration_str": "815ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.927698, "relative_start": 0.8146069049835205, "end": **********.039286, "relative_end": 2.86102294921875e-06, "duration": 0.1115880012512207, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43525040, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.025650000000000003, "accumulated_duration_str": "25.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9866738, "duration": 0.024820000000000002, "duration_str": "24.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.764}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0185971, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 96.764, "width_percent": 3.236}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1283191423 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1283191423\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1438640890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1438640890\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-47568894 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-47568894\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281418691%7C5%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhTRFg3R0xJcGRTYVFXQjNDRlg4UHc9PSIsInZhbHVlIjoiMzBkOVgxWDhrZ3RIeTlqS2FLWEtSRC9wV05wTXVEUHE5dFlLbWQ1Q1B2NjNtdFZuWFhQc09oNCtKSTN0eVpuRmI2TEtSV0c0eWlJc1pHV2xCOXdWbnViQVlWdDVkemtiM1czZms1Q2FiUmFPM2xLL2hTbzcyQTM5WjVic0k5blhiODNvMXZJVEZnNi81K2dPR2NXS0NteXNQZkdMRXFBRzVDd1BNZmk5amdYTk5WKy9ycjVWUXdGTXNaeTFoeW9BdzdHYzBiTTJkYjNzTldyc25LS2Q3YURoUTZyMmR6cTFJZzRSM0J4M3dJQjJZeW5jUDIwU2U1dVgyMEg1enhxTThqbnlHZGFmUkNpUER0UUhvQzhVcnNUbHpONTBXSEhlZXdaUFYvM3FTWUNRSHdUVkN1QXRsM1VqU2RKTFBVUVRjVlFKYm5vYVJibmhLYnUwZEl4V0ZTREMrcUJuUm82eER2QmVjdjFtUWdpSTlpeFFDZGxGNVhxZ2xiWWE0bzdBckxhbHVpR0hLYU40RzduUEJ4TDdyakczbUlPSFZvNmxVbkV1MlBRTGtPMGkyWXYzWUlMVkRFTTNGS3E1R0praTZVVVlWVmZQT0tnWmhMSTNUZEU1Nis0eWpvRlpuL0w2dEJMeWhudzZvRFlvdXpKc0pHU1RJSndOUEVDV3FNTTAiLCJtYWMiOiI2YTMxOGNmZjU0NjlkODZhNWY0NWRiYzNlMDA2ODg0ZjUwNWQ5ZGYzMDVmNGU4NDhiNTQ4N2FlNTJmNjU5ZGEyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlEyWE10NXdwY0l5MmpNbFRCcVF1TEE9PSIsInZhbHVlIjoiZUVwbVdkL2I5OTlQVjlVMHp4TllxME8zKzBQem5FMEVxa1ZRY2JUb3VTVks2QnJkSlBYMUVtdFBFdEtvQXh2SmpRUnlSbGEwN1pPSUxkMTZ4enlPVVVnQ21acWR0cmlYY2J0ZXpRNEJwc05GM2g4a0RKVHh1MUhYcHlUaVRkdVAwU3ZIcFlJNFJQbW9pcHhHWktJYjlTZXRBRWdQenJKVDBVTURSaVgwWnV1dWpSZFkxdHlVVGs3QzdTajVOUllrQUlwc3QzcFFhVUtPZzdtejJBaUhhcjgzdXExZmdGajBNVTN5Z1VZMnNNeS9kZWYvN1ZZaWdOTFFRWXFaMWtXSndCYk1xYmNoNDNQbmdvbGdLVjJsTVV5SmllbEV1bmY3OTEzUjliTDAzSVo1aWJqWUs1bWs0UVc1bXNqaDArOU54c29WMFdyZFRzaHA0czlWOUlkNTUyWm5nbFVUWUIzbitWNzlGK3MwaFQ0RlFXcE44empibkFEaHNNMFdqZ1dVdWliOExZUnpTdzlvT09EUm9UOXZTYnBSSkJHUnFQWWpveXl0Rm5wL0lzdzA5RC9RazRSdWlqQStYN015Y1VEbW0xZ1ZvbGVObm1PS2c4RytvMVNNYk0wQUJwRjdSWUNieVU1WDh5eXMwc2FiVndoMlhCb0x1VkN0NFVLNFUyTTQiLCJtYWMiOiIxMTkyMWVjMjY2YTI4NWJjYjVmNzVjNGI2MmQ4MjU3M2QzMmNhYjNmZjhhOWMzNDkyZmNjOTIyMDBkZGQ5NTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1232539856 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YNJBgXOqtFU4b8z8Vx4XcGRnEfxLj9OGGtpNz79H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232539856\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-282401915 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFjTXJYUTRNN0tZaWZnSkVEdEkvekE9PSIsInZhbHVlIjoidUdabHprZzdtTkxaOFI0OThjaGZ0UGRucnVrNVovUy9wT09aS2swcVBOMCtsR3djdnA3Rnk4NmJlYk9vNnB6MnRNT09mZmFQdnU4MlhseVlOcTdwWW5mTUlzdmtaU1Z1SEFkQjZUWG1VdDN4UUJvWlg0MjBKZUxzRzQ4dlBqV0lQdlljRUpNR0s1K3JMRG9aQ1c1TDdYVjFSN1FQZTBETkovSHBOSEZ5SGFkdGQ4cHdWeWlNM3dudTRrV1grdEszN0F5UkdLL1lPVUhzN1VxMFMvalhLcjlGMGZPTkJDYk1OcDJCNkc2RXFjdGtBUy9BZXhrSkl1SDJVWVM5KzFHOXVESlN6TGF4bkd1UzlVTDJBcUxlWXhSQ3JGVk5UcS83ZzBCdExud0xoV1dnUlljL3drSGxuMUphaUF2d2xGVFhuMTh4VHZ3SXc5MnB0ZVM3SFRGV0FINFJjOVo3SVorR3lzSlhjaGFnR0hWbWRWaC9nNVBqZHBUOXEycTJoMlFoN0RtMDVYZ1p6K2VQRWVLWUtRUktHWHMzZHJqNzNJUVV0K0FIcHdqYzd2aUJHQ2dOeFlRSDAzSi9PbENlYnluVnZoL2hDeXR5SUp0a05SOXJZYXZDMm40QkN6YTJwYkN6VVFCT3pLV044UmF4aGZEcEovKzQwZ0xLeUdqdERPZjUiLCJtYWMiOiI1YTU4OWNkZmU0NGI3MWM0ZWFiMmM3NGZjNmIxMDhlY2ViMmI3YmUwNzQ4NmRkZjk1ODdlYzM3ZjZkZGVkOTY2IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkR0N0dOKzVoaXR6eWdUZi9FSzFpTUE9PSIsInZhbHVlIjoiQWxQaFlRbTZ0SEpTYmZMNEVvVjNvZHdES0Q0ajFac0xvbGp0NUpvcTlqdEwyTTBlUDhTVG93clEvTnB6b3RPa3ZVbFVIdEptejNBK1dJQVA3cFNIL2JqaVF0YU9nMStrVElIcHFteTJwQU5QejhNYXRuN3hpMEZGVTNmV29jQjViYnhQY1NmMGlnaUZhUGRsOFBvbmJ2bCt3dysycFc2YTZqYTFxUVNHVDA5dS85NlV1T3l3NU9WaTJTOFN0STd6UU04RmRPZko4QW9oZWlscFd0N0ZZaXQyeFhZOXcrU3pseHg1b0JxY2pORkw1TTd3Z0llenArRXF4cnNuYXF0eW1JYi9RbzhlYnFEZ0I2N0c0Mm9qSG84RnlVU05WWGZ2blkvTlpnazVJdFNuRWJsaGxiRkZsRnZkNlcxQitRcWF3VGZWa3E0SFQ4NGlEV3crWHlFU09YY2hOVjZaME12OEdEaHZFSnVXS21RU0srazFUZjh2UzJzcXBMYk55WVBqMHlyck01Z2lEQWNSMDNOWDl1VFk0U0VnS3RqNnBNTWVMcDA4OHk0YkZpRzNKd3hSckRkdDJyVDJ2NGJnNGdJd2JncGdad3ZiUEE0WVI4cW9sZmJBZmFHb0xYZFJJV21UdFdPUXc0c2pJMTNSTS9tVTU3Z2dSYzhRdlcxTHVHSDkiLCJtYWMiOiI3Y2NiYjUyMWQzZjBmODhlYTQ2ZDQyODQ0NjRmNTkzODI1YzA0M2I4MDFjZDVhN2NjMTI2OWU1YmMyZWJhN2UyIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFjTXJYUTRNN0tZaWZnSkVEdEkvekE9PSIsInZhbHVlIjoidUdabHprZzdtTkxaOFI0OThjaGZ0UGRucnVrNVovUy9wT09aS2swcVBOMCtsR3djdnA3Rnk4NmJlYk9vNnB6MnRNT09mZmFQdnU4MlhseVlOcTdwWW5mTUlzdmtaU1Z1SEFkQjZUWG1VdDN4UUJvWlg0MjBKZUxzRzQ4dlBqV0lQdlljRUpNR0s1K3JMRG9aQ1c1TDdYVjFSN1FQZTBETkovSHBOSEZ5SGFkdGQ4cHdWeWlNM3dudTRrV1grdEszN0F5UkdLL1lPVUhzN1VxMFMvalhLcjlGMGZPTkJDYk1OcDJCNkc2RXFjdGtBUy9BZXhrSkl1SDJVWVM5KzFHOXVESlN6TGF4bkd1UzlVTDJBcUxlWXhSQ3JGVk5UcS83ZzBCdExud0xoV1dnUlljL3drSGxuMUphaUF2d2xGVFhuMTh4VHZ3SXc5MnB0ZVM3SFRGV0FINFJjOVo3SVorR3lzSlhjaGFnR0hWbWRWaC9nNVBqZHBUOXEycTJoMlFoN0RtMDVYZ1p6K2VQRWVLWUtRUktHWHMzZHJqNzNJUVV0K0FIcHdqYzd2aUJHQ2dOeFlRSDAzSi9PbENlYnluVnZoL2hDeXR5SUp0a05SOXJZYXZDMm40QkN6YTJwYkN6VVFCT3pLV044UmF4aGZEcEovKzQwZ0xLeUdqdERPZjUiLCJtYWMiOiI1YTU4OWNkZmU0NGI3MWM0ZWFiMmM3NGZjNmIxMDhlY2ViMmI3YmUwNzQ4NmRkZjk1ODdlYzM3ZjZkZGVkOTY2IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkR0N0dOKzVoaXR6eWdUZi9FSzFpTUE9PSIsInZhbHVlIjoiQWxQaFlRbTZ0SEpTYmZMNEVvVjNvZHdES0Q0ajFac0xvbGp0NUpvcTlqdEwyTTBlUDhTVG93clEvTnB6b3RPa3ZVbFVIdEptejNBK1dJQVA3cFNIL2JqaVF0YU9nMStrVElIcHFteTJwQU5QejhNYXRuN3hpMEZGVTNmV29jQjViYnhQY1NmMGlnaUZhUGRsOFBvbmJ2bCt3dysycFc2YTZqYTFxUVNHVDA5dS85NlV1T3l3NU9WaTJTOFN0STd6UU04RmRPZko4QW9oZWlscFd0N0ZZaXQyeFhZOXcrU3pseHg1b0JxY2pORkw1TTd3Z0llenArRXF4cnNuYXF0eW1JYi9RbzhlYnFEZ0I2N0c0Mm9qSG84RnlVU05WWGZ2blkvTlpnazVJdFNuRWJsaGxiRkZsRnZkNlcxQitRcWF3VGZWa3E0SFQ4NGlEV3crWHlFU09YY2hOVjZaME12OEdEaHZFSnVXS21RU0srazFUZjh2UzJzcXBMYk55WVBqMHlyck01Z2lEQWNSMDNOWDl1VFk0U0VnS3RqNnBNTWVMcDA4OHk0YkZpRzNKd3hSckRkdDJyVDJ2NGJnNGdJd2JncGdad3ZiUEE0WVI4cW9sZmJBZmFHb0xYZFJJV21UdFdPUXc0c2pJMTNSTS9tVTU3Z2dSYzhRdlcxTHVHSDkiLCJtYWMiOiI3Y2NiYjUyMWQzZjBmODhlYTQ2ZDQyODQ0NjRmNTkzODI1YzA0M2I4MDFjZDVhN2NjMTI2OWU1YmMyZWJhN2UyIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-282401915\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1324654311 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324654311\", {\"maxDepth\":0})</script>\n"}}