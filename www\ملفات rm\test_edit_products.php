<?php
/**
 * ملف اختبار سريع لصفحة تعديل المنتجات
 * 
 * استخدم هذا الملف لاختبار الصفحة بسرعة
 * php artisan tinker
 * include 'test_edit_products.php';
 */

echo "=== اختبار صفحة تعديل منتجات الفاتورة ===\n\n";

// اختبار وجود الكونترولر
$controller = 'App\Http\Controllers\WarehousePurchaseProcessingController';
if (class_exists($controller)) {
    echo "✅ الكونترولر موجود: {$controller}\n";
    
    // اختبار وجود الوظيفة
    if (method_exists($controller, 'editProducts')) {
        echo "✅ وظيفة editProducts موجودة\n";
    } else {
        echo "❌ وظيفة editProducts مفقودة\n";
    }
} else {
    echo "❌ الكونترولر غير موجود: {$controller}\n";
}

// اختبار وجود النماذج
$models = [
    'App\Models\Purchase',
    'App\Models\PurchaseProduct',
    'App\Models\ProductService',
    'App\Models\Vender',
    'App\Models\Warehouse'
];

echo "\n=== اختبار النماذج ===\n";
foreach ($models as $model) {
    if (class_exists($model)) {
        echo "✅ النموذج موجود: {$model}\n";
    } else {
        echo "❌ النموذج مفقود: {$model}\n";
    }
}

// اختبار وجود المسار
echo "\n=== اختبار المسار ===\n";
try {
    $route = route('warehouse.purchase.processing.edit.products', ['id' => 1]);
    echo "✅ المسار موجود: {$route}\n";
} catch (Exception $e) {
    echo "❌ المسار غير موجود: {$e->getMessage()}\n";
}

// اختبار وجود ملف العرض
echo "\n=== اختبار ملف العرض ===\n";
$viewPath = resource_path('views/warehouse_purchase_processing/edit_products.blade.php');
if (file_exists($viewPath)) {
    echo "✅ ملف العرض موجود: {$viewPath}\n";
    echo "📏 حجم الملف: " . number_format(filesize($viewPath)) . " بايت\n";
} else {
    echo "❌ ملف العرض مفقود: {$viewPath}\n";
}

// اختبار قاعدة البيانات
echo "\n=== اختبار قاعدة البيانات ===\n";
try {
    $purchaseCount = \App\Models\Purchase::count();
    echo "✅ جدول purchases متاح - عدد السجلات: {$purchaseCount}\n";
    
    if ($purchaseCount > 0) {
        $firstPurchase = \App\Models\Purchase::first();
        echo "✅ أول فاتورة - ID: {$firstPurchase->id}\n";
        
        // اختبار العلاقات
        if ($firstPurchase->items) {
            echo "✅ علاقة items تعمل - عدد المنتجات: " . $firstPurchase->items->count() . "\n";
        }
        
        if ($firstPurchase->vender) {
            echo "✅ علاقة vender تعمل - اسم المورد: " . $firstPurchase->vender->name . "\n";
        }
        
        if ($firstPurchase->warehouse) {
            echo "✅ علاقة warehouse تعمل - اسم المستودع: " . $firstPurchase->warehouse->name . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: {$e->getMessage()}\n";
}

// اختبار وظائف Purchase Model
echo "\n=== اختبار وظائف Purchase Model ===\n";
try {
    if ($purchaseCount > 0) {
        $purchase = \App\Models\Purchase::first();
        
        // اختبار الوظائف
        $methods = ['getSubTotal', 'getTotalDiscount', 'getTotalTax', 'getTotal'];
        foreach ($methods as $method) {
            try {
                $result = $purchase->$method();
                echo "✅ وظيفة {$method} تعمل - النتيجة: {$result}\n";
            } catch (Exception $e) {
                echo "❌ وظيفة {$method} لا تعمل: {$e->getMessage()}\n";
            }
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار وظائف Purchase: {$e->getMessage()}\n";
}

// اختبار وظائف User Model
echo "\n=== اختبار وظائف User Model ===\n";
try {
    $user = \Auth::user();
    if ($user) {
        $methods = ['priceFormat', 'purchaseNumberFormat'];
        foreach ($methods as $method) {
            try {
                if ($method == 'priceFormat') {
                    $result = $user->$method(100.50);
                } else {
                    $result = $user->$method(1);
                }
                echo "✅ وظيفة {$method} تعمل - النتيجة: {$result}\n";
            } catch (Exception $e) {
                echo "❌ وظيفة {$method} لا تعمل: {$e->getMessage()}\n";
            }
        }
    } else {
        echo "❌ المستخدم غير مسجل الدخول\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار وظائف User: {$e->getMessage()}\n";
}

echo "\n=== انتهى الاختبار ===\n";
