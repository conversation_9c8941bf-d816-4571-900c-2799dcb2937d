{"__meta": {"id": "X814cb70fa63e4e72fa3fa0d161565d4a", "datetime": "2025-06-07 04:33:03", "utime": **********.607091, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270782.210579, "end": **********.60712, "duration": 1.3965411186218262, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749270782.210579, "relative_start": 0, "end": **********.376324, "relative_end": **********.376324, "duration": 1.1657450199127197, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.376352, "relative_start": 1.1657731533050537, "end": **********.607123, "relative_end": 2.86102294921875e-06, "duration": 0.23077082633972168, "duration_str": "231ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44825128, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01018, "accumulated_duration_str": "10.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4675958, "duration": 0.00532, "duration_str": "5.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52.259}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.52373, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 52.259, "width_percent": 11.297}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.565116, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 63.556, "width_percent": 21.415}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.585849, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.971, "width_percent": 15.029}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-736047 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-736047\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-29367459 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-29367459\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-70600560 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70600560\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270777599%7C12%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhlR0xTZzdWYWZxQjFmTUZ2bmowMUE9PSIsInZhbHVlIjoiQVRNSHo0V1UzM0ZieUNRbkRLVWhVMnRUWHA3YXlqVG9TdWdCRlNhdXZYK0pTWU9pYTMrQ2xXeDdpMlF2YWl3WmthbmtKTWtieHlsK1RDV1BQLytydTZIMGE2ek0vVm4rdE5zdUducm9zbVpIcTJZTVhQdGxORC85QnpPMVNvUitGV3owdkE3RFkzb05IanRtV0dhZ3poangyUElmbFVQTXdRcnM4V3U1S3N1cS9tQkhyYTZzWGkzTjRhMVVkRHltZDAzRnd3dUg1aXpOZzArRVBhczY4UkViMDJPajdJenROeVRlRTYrRm1RWDdkU1dCcXNuZEQvYWt1Z3NrY2h4TzhjSSthVDdKckY5SUczekpIbTRXYU5zY3FBTEt0TmllbGRsM3I5T0V4WnB4R1c5bW1nUkpKK0JhSGxnbHRNLzBEZHRFMVhMUVBHbG52T1VwOEVqM0xoVGxQdHNVS0V6VjZ1U2M0SWh0cjF3c2dlWFQ5T3RlMkk5OVZZWXdwSmVuYWtQemx6L214NkFia2dHZTd2T3VRUnNORFRzdVZ4cVBzRlB1UStvbit3T0tSUFVSWDdBZm8raEg5TnZWd0VNZm9TNmJ6RnEzZGsvL2pwa3p5S21UOTVBQmhXb3VOZE01dituRVFURmU0R3k5Wno4R0ROcnJvbzRWSGdwZVJBZkQiLCJtYWMiOiJiZWM2OWQyZDIwNGUyNmZlYWM5OTE2MjJkZjA2NTdiMzRiNWU1NTViY2QzY2Q2MjI0ZDVlNjhmYjIyNmZhZWRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBkcUtrQlVTd21XM3Y1aWVqZ1UwbGc9PSIsInZhbHVlIjoiUVZBYnc1UTc0RlRRcE5LallENjJnQ1FxMEpidEh4NmtybkFZMUgyK3ZWWW1uUms2aExGZ2FDOVpnL1ppRnBhbFhnR1JRc0YzZlQ1dEg0TFdjSkd2WW81S2hyeFVqWDNtNjBzTVV5UHphckhCZWNwdzhkQno3SE9YaEtoRWJTTndMS2g0S3dObnI2TlkrNXhXRkFGOUZBTVJJeVAraU92eFphRXlKNE1zOE5UQzI2Z0daNnBrZHVQQytpZ25GMzhaM2NPMzV0U0diTndOYlozaHdNZ1d1M3dLZWZHR0xtZWZ5ODZiVWIwbkRGdGQzeVlXc2Z3bzFONllKTFNGQzVWL2FpOEx0c2gyLzZsaTRzSTJoVTg2cWlFaVljNkgvSWhnVVNiMkpwdlFWVzdwM00rcDRoa1hmZzhyYS9NNUkySG1WclNoQzNnVmZBS1RXRUtROFMyR0V5SXJJKzUvUldnQUNIM1FoNEZ5ZlJGQ0tRRGNRQ29EVVVSYXpHdkg1RDNqN0pVTi84SGE2aVptNU9obTRnSVRqM3Ziam4vS1pGWk0zRE9HZEhTd0owUXlXL2QyZ3lvaXNrc1FJRlBkRDIvNTd4Vmp1aXEvUGpWSlpid1RrT1NONkkwN0FweFZEKzRkY3pxVXhwa0ZObTdrejZPWGFpRWFBTXFtbTRwV00wVWoiLCJtYWMiOiIzYTBhODc3NGVmNjYxZTI1OWQ2ZTg2MGI3MzM2ZDY3MTcyNzQ1ZTUwNjFkM2I1ZmY0Y2Y4ZDNmOGVmZTAzZWQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1059475153 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4BV3LLZZXyEMe10MCK9nf8wqbVP7f9AxZbbeIoE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059475153\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-480130401 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:33:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlllODdIeXdpVGdNaDRmMGlFaVUxblE9PSIsInZhbHVlIjoiSFNKbkhPMXp2ZVdTUFBHdGZMeHBsV2hWOW8wbkJTNkdkcE1nRE5YRlFRM0tLMkFpbUQ5OVh6VElGUkN1Mno3OGRFaFRtYnRBa0NEb2FDN3RSNmN3RDRFNWh6WWdweE15SE9wRGtObEw4UjNSMDloaTNOeWp4cFd1T01sM285bXVOY1JyM3pMeXJqeDhXVnJac3RBdG1SR0JDaUFjNUUvc0I5UVdkVjdyV3pITDZCcVVRSFJualpoM0dXd0ZMWlJ1bzFwMCtMS2wyRXdaaW5FelRNdkJGamNoREVEandRVldUL01UeFBsUlJLSTliNnBRcnh0Q2Jmc3E4bnMzQlBHQWh6MGVVa3A0VkViUjBkUmgxQXV0UkFrNWpUeDNJemp5elFTdGcvdno4K1JFTm9iOFJmeVlXUlAwUWtyeUJKbEdDb1ZvUDdUbkR4L3V5ZXcyYmpWaXVtK2dnVUZTQllWdVVNRWtpc0hSSG5tWFQzZWZiOExJT25WWExFY1V1dURqUldwbkNGWnZxYThxYTJXZHpSM0YwVVlVd3NwdWsweTg5MWpVbmI3WnBXZGVjMU9TNVNFbzZacmlOMmxZT2lkbWx3T0EvU0pqcG9lS3NwWnN0WHZQVy9FbFlQZzZRYUppN3J1c0tqVUNrYmY5dFRMN3JWRnE3YmNBVytYdFB1SjAiLCJtYWMiOiI4MjE2MjQ4NzhiM2FlNWFjYTQ5NGE4YWU4ZWM1ZjUxM2E5OGY0OWFkNWU0NGQ3MmVhYzM2MmRkNWUxMmRlNTE0IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlU1UUFZUXFLc0gzbklzcFJYKzVIOHc9PSIsInZhbHVlIjoiWDBRcGYxYmNKc1ZuSkxEZFlSZ1h1RjRYWGF6dnh1VGFWTGJ4Q2xTekcyVDhPNzFuRTdsa2MybllyeUpHVzJvTDdMdmNoMkFFbmhINWV4L0M2QVJvdVpabVpxZFZCQlQxNjhhRVNlME53VWNkVHNsV1YraUFnSFg0cUVEcHNURlRZSlBKQ2lnZ05DVkl5NWtJZkc2dXpkWDh1UlovOGxtT1FJY0NBVDhYcGdvOXlpZ1JlRW1rV0EwT1hiTUFWR1EwdkMrWWU5b3lINFpOMGoxZjhxOHhHZTY0RWdvbFd0RnBDZFdKMklhQ01vS0ZpRU1Fcm1iNitGVzFSWTVMZkllckZmeWRENUg0OEdpY2ZlWmNFaG9jOXpFeFd0R3dGU3p2Zk1XQ2hBMGI3cXd0ZW9jY0NsSko2UEJXNVlxUXRJYmVZZHc3aGJNMjFXZDZmRGdyMWVyam9HNnlsQzBEbXVkL05RM1A4QURWd09QOXVHdk1JWG05WjFtRXlkcGs5blVLeStKMWRjVm1IcXZDdjlBaEZvb3hFL2NPTDl1NS9TaWc0U0dZaUtzMWQrckJuMHNrcmszdUVkNEdrNVBuTnl0U1ZGVFJaUGtxR1N5VjZrNlM2M281b0M2MzJoVUd4Y3VvRVJLdlNwS0xxUDVSVnh1VXI0YW4rZDRzcFllZjZJNWoiLCJtYWMiOiIwNTZhZTQzZmM1NjU2NjNhNzYyNjJkNzZjNTYwYmQ3ZTYzYzNjMzhhOTY2YWUwMWY3YmU0OWI1MTJiZjc3M2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlllODdIeXdpVGdNaDRmMGlFaVUxblE9PSIsInZhbHVlIjoiSFNKbkhPMXp2ZVdTUFBHdGZMeHBsV2hWOW8wbkJTNkdkcE1nRE5YRlFRM0tLMkFpbUQ5OVh6VElGUkN1Mno3OGRFaFRtYnRBa0NEb2FDN3RSNmN3RDRFNWh6WWdweE15SE9wRGtObEw4UjNSMDloaTNOeWp4cFd1T01sM285bXVOY1JyM3pMeXJqeDhXVnJac3RBdG1SR0JDaUFjNUUvc0I5UVdkVjdyV3pITDZCcVVRSFJualpoM0dXd0ZMWlJ1bzFwMCtMS2wyRXdaaW5FelRNdkJGamNoREVEandRVldUL01UeFBsUlJLSTliNnBRcnh0Q2Jmc3E4bnMzQlBHQWh6MGVVa3A0VkViUjBkUmgxQXV0UkFrNWpUeDNJemp5elFTdGcvdno4K1JFTm9iOFJmeVlXUlAwUWtyeUJKbEdDb1ZvUDdUbkR4L3V5ZXcyYmpWaXVtK2dnVUZTQllWdVVNRWtpc0hSSG5tWFQzZWZiOExJT25WWExFY1V1dURqUldwbkNGWnZxYThxYTJXZHpSM0YwVVlVd3NwdWsweTg5MWpVbmI3WnBXZGVjMU9TNVNFbzZacmlOMmxZT2lkbWx3T0EvU0pqcG9lS3NwWnN0WHZQVy9FbFlQZzZRYUppN3J1c0tqVUNrYmY5dFRMN3JWRnE3YmNBVytYdFB1SjAiLCJtYWMiOiI4MjE2MjQ4NzhiM2FlNWFjYTQ5NGE4YWU4ZWM1ZjUxM2E5OGY0OWFkNWU0NGQ3MmVhYzM2MmRkNWUxMmRlNTE0IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlU1UUFZUXFLc0gzbklzcFJYKzVIOHc9PSIsInZhbHVlIjoiWDBRcGYxYmNKc1ZuSkxEZFlSZ1h1RjRYWGF6dnh1VGFWTGJ4Q2xTekcyVDhPNzFuRTdsa2MybllyeUpHVzJvTDdMdmNoMkFFbmhINWV4L0M2QVJvdVpabVpxZFZCQlQxNjhhRVNlME53VWNkVHNsV1YraUFnSFg0cUVEcHNURlRZSlBKQ2lnZ05DVkl5NWtJZkc2dXpkWDh1UlovOGxtT1FJY0NBVDhYcGdvOXlpZ1JlRW1rV0EwT1hiTUFWR1EwdkMrWWU5b3lINFpOMGoxZjhxOHhHZTY0RWdvbFd0RnBDZFdKMklhQ01vS0ZpRU1Fcm1iNitGVzFSWTVMZkllckZmeWRENUg0OEdpY2ZlWmNFaG9jOXpFeFd0R3dGU3p2Zk1XQ2hBMGI3cXd0ZW9jY0NsSko2UEJXNVlxUXRJYmVZZHc3aGJNMjFXZDZmRGdyMWVyam9HNnlsQzBEbXVkL05RM1A4QURWd09QOXVHdk1JWG05WjFtRXlkcGs5blVLeStKMWRjVm1IcXZDdjlBaEZvb3hFL2NPTDl1NS9TaWc0U0dZaUtzMWQrckJuMHNrcmszdUVkNEdrNVBuTnl0U1ZGVFJaUGtxR1N5VjZrNlM2M281b0M2MzJoVUd4Y3VvRVJLdlNwS0xxUDVSVnh1VXI0YW4rZDRzcFllZjZJNWoiLCJtYWMiOiIwNTZhZTQzZmM1NjU2NjNhNzYyNjJkNzZjNTYwYmQ3ZTYzYzNjMzhhOTY2YWUwMWY3YmU0OWI1MTJiZjc3M2ZmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-480130401\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-163345748 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163345748\", {\"maxDepth\":0})</script>\n"}}