{"__meta": {"id": "Xad8617abd6e9059247a8ead76d94810f", "datetime": "2025-06-06 20:37:38", "utime": 1749242258.015278, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242256.388022, "end": 1749242258.015318, "duration": 1.627295970916748, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1749242256.388022, "relative_start": 0, "end": **********.814223, "relative_end": **********.814223, "duration": 1.4262011051177979, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.814243, "relative_start": 1.4262211322784424, "end": 1749242258.015322, "relative_end": 4.0531158447265625e-06, "duration": 0.2010788917541504, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.022850000000000002, "accumulated_duration_str": "22.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.897604, "duration": 0.01946, "duration_str": "19.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.164}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.945214, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.164, "width_percent": 4.595}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.956551, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 89.759, "width_percent": 5.427}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.979037, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.186, "width_percent": 4.814}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-880198740 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-880198740\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-537936372 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-537936372\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-419681539 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419681539\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-46638430 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242246322%7C10%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9mOFVLcjYzaTZKaCtVbWJNOHB5RWc9PSIsInZhbHVlIjoicW9sWHdlM21yTkRWWWxGd25LRWZPUUlXYU4vSWRPVVhhWkE3K1NlenlHMWpXSjN0a1J0Nmkrdm5kRzNDUGZDbDJUNjNVOUgvRHQ1YUJuTDV0TEN6STRqckNSK01UU1VHVWJTZmJOTHBMeW5wcmZtUkhWU1Y2Tk15WjFGREo4eHBYKzlGVWxseXdMK0FRUkVmK29nejVUYzdEd1o4QVVmWmVrNkdpeStRbnl3REZRNy9MczZoUmVzU2V5WklGcHlPRFljaHpMakdINTZMT1dLRWswTWlYRGRvUWhUdC82bmxEbXNWVTZrT211UndFYjgwckVQc3RsbzhsbTVsUnFlWXlUSU8vTk4yeDZwcEZaL0R2bjk1VHNMcUdGMXJoNlpBakJrcmhRdzlIOTNBR01pVCtwT0t5RGxOOE1OYjlCcDBodWlBSFBva2F5L011c1ZLbmdKQnVjZFhrVFRGeVN1cXNGeE9Sdm9JMkV3TGpWTmVlYWpQRFJUTUVrK01zbm5kTEZVQzh0WkRIemdYMy9jaWpYdk03UGNrSHlGZ0F1dEZPZVBZNlpudmIrTUlYYkI2R256NTdPSWt2Q08ycnlFSWc0Ui9jK2o5WHdpNlpPdSt5UVlLZUpZZkVsakdmTWF1OVdKK3RLVHBRbEMvNDNzdzAzeUQwUFJQakc5cEdnT2EiLCJtYWMiOiI0YjBlOTUzYzVjODE1M2VkZTRiMzQyOTRhYjQ2OTg4NzRjNGRkODMzM2YyMTBiOTQ2MGE5M2RhZjhkMDE5YzUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjgxcVV4Y2hOOXNFeXNodmprZzUvcGc9PSIsInZhbHVlIjoiUmtaNlN5djUyR1pzQzZ1SXR3a1pWbFI2ckRkNndiT1ZTZjNqUzR1V1NwNlloRFQyK3krMUN2MzZxYnYwYTFpbzh3QkEzOVFWWjJSMFRCU2FVZ0V2T0VMOFNwLzFjeVBWVk10aGVlcmJkUHRRMlhHU0tFNEVKbEFxaUJmcnZkRmlXa3JYYWNla1BjUGJRSkduV01vbzBPRzZDbFBNSExkc3RBNVJodVlvWkxHNTZnT3pFQVErYWpqUlpyd3ZaYS94WittVGlWdk0xNzVZVkZzdDJ1cElXQlUwS2h6YzBOVEhEVXErcHJwT20rNmhWNDJPclRhS2hMaXlxOW9kMWRYZUM3ejI1K1I5dm16QThBWUdFTENpYzM0UnRIK0dyZCs1Nk1MTnRmbmgydTUwT0NtcXpBVkhNZGp4UjlaazVESmRVUUVUZmlUU3RmNENCbVR2ajRESGhzazVyc0FUeExDb3ZXSWQ0a2xRSUhDNTFXUlN6Ri9pekozVmdDbHJVclh3OXhWKzU1U1RuZlY3MGMwclF6bVh1WUErb0R2R1ZKV1pHS205VFRoUDJIWlRZNnhuTUY2WUI5QlFHUzh3L29tc3dwdnhFSFlHMjYwd08wd2d4NFlsMVFYZTNTU2J4QWxraXFncmwzYXZqRDQ3d1dDSEVzV1MxYWthTDZ6Q0xUTGciLCJtYWMiOiJmMDcxZGZiYjI1MWIwN2I2OWEyMGNlNGY2MmEyNWJiZDcxZDdjMjBhOGU0MDYzMzJiMmYzMjgwMWQyY2Y0NWNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46638430\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:37:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks4QmpEZ1pnUFcwM0thWWlkcHBJY1E9PSIsInZhbHVlIjoiOVBVandoZWNtZGN2QkZtVjMwaW9wMndGNUhlNnZiZkNuMGx2dGxXSmQ3bEFFNUFTc2kvcW5acjAyL1dIamUrT1E3UUVJQWVxenl6aHlwN2VmSUlrMnBjekhGTWVlZ3FHQXp3ODl3MVNSZWYxa0ZSRHF3TGxyMWFHdTYxOXJuM0lubm1NcElKL0ZpTC81cUlHSm9PbnNZUEJqSk9mTnM0VXdhQjd1bTB4cFFnRUlFVWVXc1c3UkFhc3RNSFdBRTYzQXRmaEVaVGF6YXp3ODJXVVRIYytyS21IREtVOGcyWGtvWGhxMHkvaGJJSGE3ZThPOWZBTmNZdW1sOVVXUDBiSEtQUXI0a1VueWY1Z3lMSWRhaWZzbUVPbDFTU1Fpc3dtM2dOTHZXb05GOUVGUGh1WEZ6cVdqSjlieXhLWlFTbmQyY2RYem9la0ZSQVFwR0hZV0lmcDlpQzBUdnBJb01aN3VsS0VkbG51VlhWamQ5NjM5U1kwUUlXY2kyMEpzL2lBb05vdGNiMmZzVi84L3g2dkNYcTFJRnhUWVV2Mk9FWFRUQ24yUWtLeDZyQTNGbnJadGxJb0lBMzNwTFdpRG9MVjIrSGJoUTJjbFRuVXZORVdzQ3RBNlp4cDJ6U0NsdncrUENXUC9xdkk3SVlJQ3Jsd0hPK2J3bHpFVzRoMXJZSTkiLCJtYWMiOiIyMTE3NWFhNWVhNzJlMDY3MjRmNmE1NjdmYzMyMGJkYmJkYzVmZWE2OGVmZmE0OTJiNGJkYTUxNjY0MDdmYjg0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlB2QnRma3N4b3I5RGR4M25YRTNqT2c9PSIsInZhbHVlIjoiakY4K0tWbjhIWGt3ZkgrWW9XSVZGdzRrS3pEYkQyVE5oc3dZMklrSzVDem5ucFlxRS9NQWFyU2FNei9kRERKZEZqM1lCQkpPU2pLVHU1Qm1NUGViSkh0UFhQd28zRHZoTkh6UVZoRE5kZlNoZHErTTZoN2xiODNnZkJ2OGFYNDQ1QUZGR1kwV1pPMzV5ZFZZdEdUSk1ZY1dTWGpweW95LzJON3ZrYzlNR0o4K2xiL2ZneWVPTExDVXdzYWtZRUdkaGlZMk1hbjZhejNnd0kwdHc4Q29FWWlYcDY1ZkY4bGVQZDZoUUV5NVZkSWQveC9ta0hwdjJRSytPTjA4TG9VemlTN2dRSURJeEFra2E2T0dWclV3SzNobWRFVFBFQ1ZmMlVqdWRqbFZlRXIwSjEyQWwxWEpiSjNtakhUT0FEQ3E4b0ZVRUMwMlYxbkwzakdEZW5GT0U3VG92L25iOXZUcXAvaks4ZGUweTBWY2FTWndCb2xBQ3QwQmRoVnNUeWxjYjY1ZURYWkhQOEhMbzV4VWdpaW5BRUNYRVJNVlZpZEx1UmltNHpVa25xMXgySHRhaUt3ZUNPN2loYnZEWFFqUXJ2dlQ1cG50aGtGTnEvR2NtQ0poeXlNbDVGbW1MK2loM2dDQlBJTXhOWmR0RDBEVlBlbW9za2h6WU51NVJIQzkiLCJtYWMiOiIzZWRjZWZmNDYwM2Q1YTNkNjcwNzc3ZGIzMTYzNjQ0ODFlMmE4ODcwNmZmYjU1NDIxNGMyNTJiMmVhMGRlMzNkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks4QmpEZ1pnUFcwM0thWWlkcHBJY1E9PSIsInZhbHVlIjoiOVBVandoZWNtZGN2QkZtVjMwaW9wMndGNUhlNnZiZkNuMGx2dGxXSmQ3bEFFNUFTc2kvcW5acjAyL1dIamUrT1E3UUVJQWVxenl6aHlwN2VmSUlrMnBjekhGTWVlZ3FHQXp3ODl3MVNSZWYxa0ZSRHF3TGxyMWFHdTYxOXJuM0lubm1NcElKL0ZpTC81cUlHSm9PbnNZUEJqSk9mTnM0VXdhQjd1bTB4cFFnRUlFVWVXc1c3UkFhc3RNSFdBRTYzQXRmaEVaVGF6YXp3ODJXVVRIYytyS21IREtVOGcyWGtvWGhxMHkvaGJJSGE3ZThPOWZBTmNZdW1sOVVXUDBiSEtQUXI0a1VueWY1Z3lMSWRhaWZzbUVPbDFTU1Fpc3dtM2dOTHZXb05GOUVGUGh1WEZ6cVdqSjlieXhLWlFTbmQyY2RYem9la0ZSQVFwR0hZV0lmcDlpQzBUdnBJb01aN3VsS0VkbG51VlhWamQ5NjM5U1kwUUlXY2kyMEpzL2lBb05vdGNiMmZzVi84L3g2dkNYcTFJRnhUWVV2Mk9FWFRUQ24yUWtLeDZyQTNGbnJadGxJb0lBMzNwTFdpRG9MVjIrSGJoUTJjbFRuVXZORVdzQ3RBNlp4cDJ6U0NsdncrUENXUC9xdkk3SVlJQ3Jsd0hPK2J3bHpFVzRoMXJZSTkiLCJtYWMiOiIyMTE3NWFhNWVhNzJlMDY3MjRmNmE1NjdmYzMyMGJkYmJkYzVmZWE2OGVmZmE0OTJiNGJkYTUxNjY0MDdmYjg0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlB2QnRma3N4b3I5RGR4M25YRTNqT2c9PSIsInZhbHVlIjoiakY4K0tWbjhIWGt3ZkgrWW9XSVZGdzRrS3pEYkQyVE5oc3dZMklrSzVDem5ucFlxRS9NQWFyU2FNei9kRERKZEZqM1lCQkpPU2pLVHU1Qm1NUGViSkh0UFhQd28zRHZoTkh6UVZoRE5kZlNoZHErTTZoN2xiODNnZkJ2OGFYNDQ1QUZGR1kwV1pPMzV5ZFZZdEdUSk1ZY1dTWGpweW95LzJON3ZrYzlNR0o4K2xiL2ZneWVPTExDVXdzYWtZRUdkaGlZMk1hbjZhejNnd0kwdHc4Q29FWWlYcDY1ZkY4bGVQZDZoUUV5NVZkSWQveC9ta0hwdjJRSytPTjA4TG9VemlTN2dRSURJeEFra2E2T0dWclV3SzNobWRFVFBFQ1ZmMlVqdWRqbFZlRXIwSjEyQWwxWEpiSjNtakhUT0FEQ3E4b0ZVRUMwMlYxbkwzakdEZW5GT0U3VG92L25iOXZUcXAvaks4ZGUweTBWY2FTWndCb2xBQ3QwQmRoVnNUeWxjYjY1ZURYWkhQOEhMbzV4VWdpaW5BRUNYRVJNVlZpZEx1UmltNHpVa25xMXgySHRhaUt3ZUNPN2loYnZEWFFqUXJ2dlQ1cG50aGtGTnEvR2NtQ0poeXlNbDVGbW1MK2loM2dDQlBJTXhOWmR0RDBEVlBlbW9za2h6WU51NVJIQzkiLCJtYWMiOiIzZWRjZWZmNDYwM2Q1YTNkNjcwNzc3ZGIzMTYzNjQ0ODFlMmE4ODcwNmZmYjU1NDIxNGMyNTJiMmVhMGRlMzNkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-447421420 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447421420\", {\"maxDepth\":0})</script>\n"}}