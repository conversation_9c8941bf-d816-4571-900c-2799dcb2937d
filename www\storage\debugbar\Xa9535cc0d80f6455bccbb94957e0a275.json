{"__meta": {"id": "Xa9535cc0d80f6455bccbb94957e0a275", "datetime": "2025-06-07 07:30:39", "utime": **********.334817, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749281438.397297, "end": **********.334847, "duration": 0.9375500679016113, "duration_str": "938ms", "measures": [{"label": "Booting", "start": 1749281438.397297, "relative_start": 0, "end": **********.199449, "relative_end": **********.199449, "duration": 0.802152156829834, "duration_str": "802ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.199469, "relative_start": 0.8021721839904785, "end": **********.334849, "relative_end": 2.1457672119140625e-06, "duration": 0.13538002967834473, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44776200, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03069, "accumulated_duration_str": "30.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2555308, "duration": 0.02869, "duration_str": "28.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.483}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.302725, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.483, "width_percent": 3.421}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.317613, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.905, "width_percent": 3.095}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1760357676 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1760357676\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1408671337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1408671337\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-576878097 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576878097\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1701126054 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281425150%7C6%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdtQlNGeXpEeThHZnFGWDc4WCtpOEE9PSIsInZhbHVlIjoiSllzQmU0WlFQTm9SbkdlejlHb3hBNmJLRVRjNjlmVytLWmJGRnZqK05ncGNSSXdpbmRUZnZKUDFwOVpYM2paYlRtV2QrNHhiZVcrUklDc2JTWmRwN2JYdEJ0aENCZlhHYzJlU0ZrSzUzQ01YUEFCQnNQMW9GaGVLUFdHRzJSenNvQVpkeGNRa2NqTm9SYmVRcVJXcVVIbFY2citVajlrNGJYUjB3MzV0ZTR2Z1RBME1GSnJib2NQbkN5ZFJDeWUzWWhTL2FqT2xlWnBlam1rTmdYdlltT1c1QmJxaG95ZXhhL0hOY0NXaTBGVDZvUW5ZaGN5Wmc0L0paZktWZ1Fia1FGU0o5Q2NWcGxZMkZDOWUyMHZrQXJIOXVTcTRqNEVNL1AxU0d0WlVTSk5jOXFxZ0ZnWVBHRXlUVXlJT2ZjcER5TFFQa2VVRHNPdUxuNGJIVStaZEhXdVlhOTQ5V0pIb0R0dVkyVEVxL3dsek5MdUZaSFNFazd5TFRrT0pVNG9tWU5lUHZOS1c3TWFxWDArdkltS1JvTDE0OXhESHQ1MEdFVU1JRTBidUpnM1F5aUpVZWNlQTNZMnd4aGRZZXE1K0ZJOFk0aGs1aUtMTzhhd2FxblhFRTBtc2tCNGpEU1lKZmM3UHFWU1dDei91RHo5bEpOTDF5OHlnUDRNY0x6RE4iLCJtYWMiOiJlZmNhNDVlNzljYTBjZTc4NDlmMmI1N2I4YzczNjc1NWUyYTU1ZTVjOTJlY2EzNjAyNTFjZTFjOTdkNDE3NTY5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJjT3JmemlsdjVUMnRTT21PblQyVUE9PSIsInZhbHVlIjoieTVoYjBsNzgyU2xrSnlEdnRXMzJtc3FvMGIvVFh3aTV5c1JIZ1ljT2diNzNSb3pWckc4WXZ2ZGZtcU9FanJ6bjFUQ2hmbTlPSGV5N1A3N2NYQ1h1VGtCMTdvT3djcVdxSU1IeGQyS1N6UGR3aDNnWUJjL3dDWkFmMUJYQVhOQzAwV2RXMy9MNWlqOE9qaENQWXpiSlB3V0FubW9GNlZYQ0VZZFp6OVZmNHYvOHFNbnlpOEtJdlEybjQ2RDhici95UzJ5UnU1ZzBCcThhYVhoZDZnVkJpYU9yZzhvc1hVOEZ3MGM0MkNMbDB6ZmxEUlM2c2ZmUy91cXB2ajljSDBHZVBZTk1udmtEbW1EWDhKRUZOV0VkbERiSHZ3bGtMczdrNERLZk9YK2EvZDl1c3FSSy9IcUIyc3NDZkliQ3ltVERuc1lVNnBReTdJUTFjVnFhWWVSMVE2a0xXbmEvenNVcmNIdExWblduLy9PQmx6U3lsUDlteE8zbHArQlhTTjN5RFNWaUFlQkJEbWZVNGxyU1BOTEZqS3k0TkwydTJCOXlFbjFYcnpaK1FPZEJwU3ZRTkh0VHFKOHMwSXBzdkIzYkltOEY5c1pzK2JDMC9zYURLRVB5SXRBRW1Oc3NMb0xhS2FML0NmcUlLcC9xTDFwYVRSUTFqQjhBeVhRT1BQTE4iLCJtYWMiOiJmNWQ1ZjU3MjE2NzY2OTNlMGYxMjUwZGZlZTZkNzI5NTRmMWVmMDE1YWQzMDFkYzUzNTk4MGFkZTY4ZDA4OThkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701126054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1148898055 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bmHYo9T8TH5q6dUCnl8ZG1w46p5IENlShdAmx5wt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148898055\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1763107037 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpwUU1JcDAxVkdYRWRTVmdmRXF3QXc9PSIsInZhbHVlIjoiYVFrbUhQTUUwclBvd1h2WHdKVmc5Z2h0OWY3SVcrL0NiRnNQTy9XQ2liZ2s0STZGbEUvbS96RDFPTDA2SU1CR0pTRzZ1cWFCUjR3N0RZbWdFaGZjZXpQby9qN1ZhZml4QlMrellSWG1KUEg4SUNqM3BNcGlkNWZyRmNLUTQrb3JmS2piTUJmUXpVb0NtMC8ybWdZVkZtM2ZwYlpLcjhZdGo5MTF5Nm1ma2VwK08zaGIwQVdXcDB2SUs1Y1c2TklHN0VGbExCQnBTb1llTWNZV2RneG5sUFd2UlRyb282MnJUOCtUdmpPYURPS3Z2b0lJemx4MDlsUEc1ZVZUenJMMloveEZaM0tTdzlLQTByUFRMNzNTSVUzWnBhbHRNY1lHNWtRVm1hdnFIREpPeWpKSnRmaWZJUFk0N2lSVjNqdFUzMzI2OEtCTndOa0E5L01wSkJRb1I2SENZM1F0bWV2RU9CVzZpNGdxNVhFQU1IN005L3N2Mmc2aUl2Z0kxbmYvYjB2K1BkQ0ZCZGk3VUtzd2Uyc09NTjRsRTRnNWxNMDRnTVVzMTJYZlF2NVUrc012clQ2Wi9MSksyclBEek93NFZMdWlKOVRIYnlXUHI1T1dhNDdNNmtWMTJEbEt4empDT09qeUZrVUtSaDhDYklCMjZ1aEJhY1RCb0psN2VqcisiLCJtYWMiOiI3ZDg1MTc5MWFkMWE1NDJlMGQxNjI5MWI0NDQ5ODY4NmNkMTE4NjJhODY2N2FmYmJmNGM2M2ZmMGVkYzY2M2E0IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InB6b1FxVUdXeEc4WWpLQUZ5SjhINGc9PSIsInZhbHVlIjoiYzFpdWx3T2wweTBrOXBwMUs3azRobGRYbXUzWkNOSytxN3NxYzR0VTNuUVlKblZJdlY2M0Q4T2RMR2ZXSEd3ZEFyMElrZjhxZ2hFOGoxMU5BeWdOYStKa1Q4UWs4SHFWZTFCZVJyUEMrV2VPWHB0ZzUrVVBiVXpmRDhjenJtdE0wbkEyS0tjVjloTGdqU255UVEwbmhZTDVYNHJJM24wN3l6RTZuTmZHVUxkL2czeEpOVHZlL0ZiWUxQK2g4TDRKZXNldXZXVFJyaHFzQVBkcXdGWFNNZTNIenFJMGd5dmZrc1QxbkF4Y1NBNEM1NXZjRkVpSW1IR3RpWDRLMWtFcnBITHh5dVVvTW9BdGhSRkRleVkwS0pSNDJKWnF0K0NsTjl4ZjYzeTg5TXRPTWVPYVlaZENabmRrbHpSdGZUSnI0c3grbU5ZVzdnV0tNUlY4amQ0RG5weUxSWjE5b0RCRENWd1N2WHBlcGE2SzN0cXZEOFhtVTU4OG5JaTdUYUxsWE5sU25vYllZdmFqSzJyOUxKYTViQ0cxZnBlc1lUL203eHNvNHk2eVE5RC8yWCtuNlI2VURpdHl6dXRveTBESXVrR0ZueXNKYk16MFAvM0lsUjN2L3ZSRUs4NjIxOEhQb2tMd0kwZ1FHSmxYVU9ocU5XZGZBclhNYUZkN0MxeG4iLCJtYWMiOiI2ZDAxMDdiZGY2NmNlNGZjYzE1ZGZmMDQ2MTcyN2FlOWIyNWJjZjVjNjdjNzM0MDA3MTY3MzRiYTcyOWJlNmE3IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpwUU1JcDAxVkdYRWRTVmdmRXF3QXc9PSIsInZhbHVlIjoiYVFrbUhQTUUwclBvd1h2WHdKVmc5Z2h0OWY3SVcrL0NiRnNQTy9XQ2liZ2s0STZGbEUvbS96RDFPTDA2SU1CR0pTRzZ1cWFCUjR3N0RZbWdFaGZjZXpQby9qN1ZhZml4QlMrellSWG1KUEg4SUNqM3BNcGlkNWZyRmNLUTQrb3JmS2piTUJmUXpVb0NtMC8ybWdZVkZtM2ZwYlpLcjhZdGo5MTF5Nm1ma2VwK08zaGIwQVdXcDB2SUs1Y1c2TklHN0VGbExCQnBTb1llTWNZV2RneG5sUFd2UlRyb282MnJUOCtUdmpPYURPS3Z2b0lJemx4MDlsUEc1ZVZUenJMMloveEZaM0tTdzlLQTByUFRMNzNTSVUzWnBhbHRNY1lHNWtRVm1hdnFIREpPeWpKSnRmaWZJUFk0N2lSVjNqdFUzMzI2OEtCTndOa0E5L01wSkJRb1I2SENZM1F0bWV2RU9CVzZpNGdxNVhFQU1IN005L3N2Mmc2aUl2Z0kxbmYvYjB2K1BkQ0ZCZGk3VUtzd2Uyc09NTjRsRTRnNWxNMDRnTVVzMTJYZlF2NVUrc012clQ2Wi9MSksyclBEek93NFZMdWlKOVRIYnlXUHI1T1dhNDdNNmtWMTJEbEt4empDT09qeUZrVUtSaDhDYklCMjZ1aEJhY1RCb0psN2VqcisiLCJtYWMiOiI3ZDg1MTc5MWFkMWE1NDJlMGQxNjI5MWI0NDQ5ODY4NmNkMTE4NjJhODY2N2FmYmJmNGM2M2ZmMGVkYzY2M2E0IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InB6b1FxVUdXeEc4WWpLQUZ5SjhINGc9PSIsInZhbHVlIjoiYzFpdWx3T2wweTBrOXBwMUs3azRobGRYbXUzWkNOSytxN3NxYzR0VTNuUVlKblZJdlY2M0Q4T2RMR2ZXSEd3ZEFyMElrZjhxZ2hFOGoxMU5BeWdOYStKa1Q4UWs4SHFWZTFCZVJyUEMrV2VPWHB0ZzUrVVBiVXpmRDhjenJtdE0wbkEyS0tjVjloTGdqU255UVEwbmhZTDVYNHJJM24wN3l6RTZuTmZHVUxkL2czeEpOVHZlL0ZiWUxQK2g4TDRKZXNldXZXVFJyaHFzQVBkcXdGWFNNZTNIenFJMGd5dmZrc1QxbkF4Y1NBNEM1NXZjRkVpSW1IR3RpWDRLMWtFcnBITHh5dVVvTW9BdGhSRkRleVkwS0pSNDJKWnF0K0NsTjl4ZjYzeTg5TXRPTWVPYVlaZENabmRrbHpSdGZUSnI0c3grbU5ZVzdnV0tNUlY4amQ0RG5weUxSWjE5b0RCRENWd1N2WHBlcGE2SzN0cXZEOFhtVTU4OG5JaTdUYUxsWE5sU25vYllZdmFqSzJyOUxKYTViQ0cxZnBlc1lUL203eHNvNHk2eVE5RC8yWCtuNlI2VURpdHl6dXRveTBESXVrR0ZueXNKYk16MFAvM0lsUjN2L3ZSRUs4NjIxOEhQb2tMd0kwZ1FHSmxYVU9ocU5XZGZBclhNYUZkN0MxeG4iLCJtYWMiOiI2ZDAxMDdiZGY2NmNlNGZjYzE1ZGZmMDQ2MTcyN2FlOWIyNWJjZjVjNjdjNzM0MDA3MTY3MzRiYTcyOWJlNmE3IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763107037\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1447410618 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447410618\", {\"maxDepth\":0})</script>\n"}}