{"__meta": {"id": "X45fdc43fc25d7cab6d6456538dafbd40", "datetime": "2025-06-07 07:30:13", "utime": **********.383838, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749281412.403088, "end": **********.383866, "duration": 0.9807779788970947, "duration_str": "981ms", "measures": [{"label": "Booting", "start": 1749281412.403088, "relative_start": 0, "end": **********.261645, "relative_end": **********.261645, "duration": 0.8585569858551025, "duration_str": "859ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.261662, "relative_start": 0.8585739135742188, "end": **********.383869, "relative_end": 2.86102294921875e-06, "duration": 0.1222069263458252, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44823344, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00779, "accumulated_duration_str": "7.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.31465, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 54.044}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.33448, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 54.044, "width_percent": 14.121}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.355457, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 68.164, "width_percent": 16.945}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.369235, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.109, "width_percent": 14.891}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-753021015 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-753021015\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1814095376 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1814095376\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1477839170 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477839170\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-821227888 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281407057%7C3%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlM5K3B3NU5mc05VblAvM2ZCd0gzY1E9PSIsInZhbHVlIjoiRVpuODFCUDB2OWlYbUQxcGlqR3kxOU1EUUVnblZNb0ZKV045aTVTekdmbmVFdlZNcFJHME1NbzFjb1ZiWm9zdHlnR2V4VUhEVzZ3YkJQZWdOU2ZwZS9OdlpBYms3L08wYU9IeUlLbE56azU1L21DYWpmWjBNVTdOdHkzWitLYlFEeXdiZGRGRzNZSWI3QVhsRWt2UXN1Nlg1NEdXaFJqeElxSnJzdENBYjFyZGx6WlA1d0ZBN0h4WXpEVlRkS0xLOXlzRVFoaFJMdmJ4RVlpVEV4M1poYk5zQmRDcGxDOUg4dlVPMm94cUF3ejQyYWxuVmpSa2xmWVpZYWZDZWFIY3ZwaXFRNVpCUHVPYWNGM0ZjenJJcEhvcnlaQkdQNlJvNnF6citCMzJmYkE0VkZTbnRqUTI2SDRkVVpEV2V3RHYwRGJPQlVsVDZ6ZHg0d1gwaHMzeU5kWEhIZlRLUDd3bmNEVUJYNmNQSm1lS3lMNGIwbTlsazlwQzBhRitTUkpUMFY0Y2lKVFdoSTV5cjZ6b0I3c3B0ejNEcnM2MlQxTnVuUVBRM3Vvb0xxZjRDNHNSajUzQlNTbEZKcjFtSDVBMUtEdWxFVlQzbmZVVTNKcjJvMGxVL1ljUXU5U3Z4RFgySGhxZ1lLWGtXcmxJai9iN1cvN1h2d3FvSXM1TFlXNTQiLCJtYWMiOiI3ZDkyODJjZjQyY2Y5ZjY0MzVkNWViZGM1MWIzMWYyMjRiNjFiZDI2OWY4NDA3YWFkMDg2NGZjYmYxOTdiNWZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVaS1QzTEFLRE4rTHgvRmQwdTZ1dkE9PSIsInZhbHVlIjoiakJkNkFQNVV4a2hIN3dhY1NsWnZrSStvaWk4ODdRdEc3N1h4UUdUbFlkUW1FU04yWEozQU5Fc20rZTExY3E5NTlIYkFoblhPRGVIc1pNaU5wWVUvSkJORUwzcUIwNHRXd0YzLytPaC9oTEVBSVF4YVduR0NGWWR1YU12K05PVDNvcjgyalMrckFFSnFoRFR0SXpubE9ZWjRweTJWb00rak1JWjVJdDJSaFV1NCtwbU82ZndrZkZMYUhhai93bE0xUVVUMzcwY2pNVzhpKzVBYmlmUHFLeExMU3hXeDBvTkhUMmZxaGRZWDVCSEFSdGFrd2phUnl3MVdrTjN6ZWJxWHUwUXFuQld6NW44anhXYkNGVkFsRXFIZmwveTRmQ1ZGQ1JhSkR4S1hQc0V1VDlVVk5ZWkRxZEtma2cvaFp5RE0yMk9qcGpEWXlNUjZNcUNjekk4Z3NkSW56WlVGV3RIeFdUSnhudEFWU29sclRrc3pjcGEyRTI0VDlLSThCUlJLdGpVK3JrYnAxN3FXa2h1SGo1NTNJNTRqbUhDMVBQSzNYdEROTU5Sc1JKK0RqRVZsc0dSYlpvSzRGbEJIRmxWZ3hZKzY3ekVPSlI5cjg1Y2pzRlBDZlpzb1djL2prbytIUkMwMGx3SDZCc0Y1UkFqT0FHVlNMYWY3S1lhbXVWUE8iLCJtYWMiOiJiNmM5YTdlNzVkMTk0NzUzMWE3MTRiMGZkMzEwNmM2MTZmOWIxNjFhZWM3NGEwOGM3NzFmNjViYmY1ODVkZDc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821227888\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1010240843 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YNJBgXOqtFU4b8z8Vx4XcGRnEfxLj9OGGtpNz79H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010240843\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2133495253 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQxN1ZVUmNhQ3RkelNFK2EzVTBqR1E9PSIsInZhbHVlIjoiU1VZSnREQkxQOWpoTGlRQ3U0UWVzWjE5RitEclN6cldnRHdPakViOFVjUUY2WXQ3K2hBbHFFZk80ek43ZU5aTHJFdGd6aVl5VUt6blFsNmhMRERsK3hocmRTdmRFQWhEYTVFajhQWVJBU0JsamNjRi9vUUV0ejFYRE80bzJ6WDRrZC9idzBrNGI2SlNXOGw3d3JqZ3JCTEh5SGtlTlNVSWxSdkRtRDVWZ1VHcnE4Smlqa0U5Ym4vNEo4b1ZneUMzaWN2bTk2VmYvdHoyUkN3N0VsN0NDTTljbGdNMzBwNm1FOVZQTFlmM1VRWVYzZUdYWnBZai9zNEpJTzZsM1pXbXBGR2czMnF6cjlYVDM2YW00VHZpaTJndk4wcEtkbU9yVFE3Mk9EdnJ3TE1MZi9USENraHM4UGJ2QlhKSTFJRy9XS0cvamluRjJjZmlvdUVIOFpEVHhNdnlEa1AvZFRlMHRBTEVYcW0rd0sySVlvQUJid1RHOXFicnlpVVcwZmE3cDBzenBJUmszVGFsR01XTlZYaWQxbjdKc3pVOGlPTXZDOWtEenFjNDk5aVZ6SHBXRHkwbHBQWFg2a3k0cW94Q05ybGhqL0svT0pndXgrK05EaUYzbGNnOEJZclV6cGo0Q2V3TU5YRFVITHdFR080NXl3U3NXa0xrNTVmNU1aVlMiLCJtYWMiOiI2NDNkZGZiY2M5YjJjODRhZDgyYjE1MDg5ZTNjMzBlMWZlNGNjMTg4OWFhMmRmNzI5OWY1Y2ZiMTE1MjgwNzU1IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFLZU16QWMvQUdTVTVKVkJyMVdRSHc9PSIsInZhbHVlIjoiZFEyR3lUR2puT09ObW45U0Q2bzZTVE94RUQ1T3dKam0zbXlFdWliTnRyYURBd2lYOStqa2tzeEhGT3VzZ1U4RG1GaTY4cVVlQll6c3lUVXc4akJVZ2tuZHRxYnpxWnlqczlpUU93ZitLUTVYMWdiU21VanBJcElWREtiN3cwL3BHUDlQam5CK3VVVHBwWXpRTGRpbGNwdTFSUnVXMlAwVFBXUnV2RVZMT2dDNkVBMERaZm8zL1Zabmt6UWhqWExBMG01dENpUzJSQ1RxelZWTHBEUUE2YnBuTlJlTkMvODVHQkJDT0poTm9WaFgyYnZGVnZGTW5wb1JkMHgzOVpKdVFqYXZjYy9tU21oU2dUVmZSemo5NDduQ0RHaFExdDZ5dVk5bTF4dHhKcXQrVkQ2MkxXSFFvZWkwNkFMdmVNRlF5M1hXbjJOV2FGeTRZMGdGUEQ3cDZUb01yRzl2bitpMXlhNlBMRExWTjJxNWRaVEg3VXNFN29lWkNBdG8zaEFVRndJMFdnMjRXWVBmWGFIc2JsWUlKUmxxMlFZbHB1MTBQdXcvMStwcnhEckVibjVXRlVUdHRNTDBnMjN1UFFLWGRrYytSRHRLclA2K3FYMlpjWUk5UjF0QTJFdVltbXFYTVFWdUFCbENDbU0wY0Jsd0NXVVp5eE9XRmFkRmQ2VlEiLCJtYWMiOiJkNmRhZjg1NjQ4YzM4MmM2NzJhMTllNDMxNjdhNDlkZGZiMTNhNzg4ZGJkNzU3ODBmOTcwMjAyNDVjMTc5ZTI5IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQxN1ZVUmNhQ3RkelNFK2EzVTBqR1E9PSIsInZhbHVlIjoiU1VZSnREQkxQOWpoTGlRQ3U0UWVzWjE5RitEclN6cldnRHdPakViOFVjUUY2WXQ3K2hBbHFFZk80ek43ZU5aTHJFdGd6aVl5VUt6blFsNmhMRERsK3hocmRTdmRFQWhEYTVFajhQWVJBU0JsamNjRi9vUUV0ejFYRE80bzJ6WDRrZC9idzBrNGI2SlNXOGw3d3JqZ3JCTEh5SGtlTlNVSWxSdkRtRDVWZ1VHcnE4Smlqa0U5Ym4vNEo4b1ZneUMzaWN2bTk2VmYvdHoyUkN3N0VsN0NDTTljbGdNMzBwNm1FOVZQTFlmM1VRWVYzZUdYWnBZai9zNEpJTzZsM1pXbXBGR2czMnF6cjlYVDM2YW00VHZpaTJndk4wcEtkbU9yVFE3Mk9EdnJ3TE1MZi9USENraHM4UGJ2QlhKSTFJRy9XS0cvamluRjJjZmlvdUVIOFpEVHhNdnlEa1AvZFRlMHRBTEVYcW0rd0sySVlvQUJid1RHOXFicnlpVVcwZmE3cDBzenBJUmszVGFsR01XTlZYaWQxbjdKc3pVOGlPTXZDOWtEenFjNDk5aVZ6SHBXRHkwbHBQWFg2a3k0cW94Q05ybGhqL0svT0pndXgrK05EaUYzbGNnOEJZclV6cGo0Q2V3TU5YRFVITHdFR080NXl3U3NXa0xrNTVmNU1aVlMiLCJtYWMiOiI2NDNkZGZiY2M5YjJjODRhZDgyYjE1MDg5ZTNjMzBlMWZlNGNjMTg4OWFhMmRmNzI5OWY1Y2ZiMTE1MjgwNzU1IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFLZU16QWMvQUdTVTVKVkJyMVdRSHc9PSIsInZhbHVlIjoiZFEyR3lUR2puT09ObW45U0Q2bzZTVE94RUQ1T3dKam0zbXlFdWliTnRyYURBd2lYOStqa2tzeEhGT3VzZ1U4RG1GaTY4cVVlQll6c3lUVXc4akJVZ2tuZHRxYnpxWnlqczlpUU93ZitLUTVYMWdiU21VanBJcElWREtiN3cwL3BHUDlQam5CK3VVVHBwWXpRTGRpbGNwdTFSUnVXMlAwVFBXUnV2RVZMT2dDNkVBMERaZm8zL1Zabmt6UWhqWExBMG01dENpUzJSQ1RxelZWTHBEUUE2YnBuTlJlTkMvODVHQkJDT0poTm9WaFgyYnZGVnZGTW5wb1JkMHgzOVpKdVFqYXZjYy9tU21oU2dUVmZSemo5NDduQ0RHaFExdDZ5dVk5bTF4dHhKcXQrVkQ2MkxXSFFvZWkwNkFMdmVNRlF5M1hXbjJOV2FGeTRZMGdGUEQ3cDZUb01yRzl2bitpMXlhNlBMRExWTjJxNWRaVEg3VXNFN29lWkNBdG8zaEFVRndJMFdnMjRXWVBmWGFIc2JsWUlKUmxxMlFZbHB1MTBQdXcvMStwcnhEckVibjVXRlVUdHRNTDBnMjN1UFFLWGRrYytSRHRLclA2K3FYMlpjWUk5UjF0QTJFdVltbXFYTVFWdUFCbENDbU0wY0Jsd0NXVVp5eE9XRmFkRmQ2VlEiLCJtYWMiOiJkNmRhZjg1NjQ4YzM4MmM2NzJhMTllNDMxNjdhNDlkZGZiMTNhNzg4ZGJkNzU3ODBmOTcwMjAyNDVjMTc5ZTI5IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133495253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1866935608 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866935608\", {\"maxDepth\":0})</script>\n"}}