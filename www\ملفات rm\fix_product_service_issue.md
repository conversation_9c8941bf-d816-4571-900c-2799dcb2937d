# حل مشكلة Product Service - خطأ 500

## 🔍 تشخيص المشكلة

المشكلة: خطأ 500 SERVER ERROR عند الوصول لصفحة Product Service
السبب المحتمل: عدم وجود صلاحية `manage product & service` للمستخدم

## 🛠️ الحلول المرحلية

### الحل 1: فحص الصلاحيات في قاعدة البيانات

```sql
-- فحص وجود الصلاحيات
SELECT name FROM permissions WHERE name LIKE '%product%service%';

-- فحص صلاحيات المستخدم (استبدل USER_ID برقم المستخدم)
SELECT u.name, r.name as role, p.name as permission
FROM users u
LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id
LEFT JOIN roles r ON mhr.role_id = r.id
LEFT JOIN model_has_permissions mhp ON r.id = mhp.model_id
LEFT JOIN permissions p ON mhp.permission_id = p.id
WHERE u.id = USER_ID AND p.name LIKE '%product%service%';
```

### الحل 2: تشغيل Migration للصلاحيات

```bash
# في السيرفر
php artisan migrate
php artisan db:seed --class=UsersTableSeeder
```

### الحل 3: إضافة الصلاحيات يدوياً

```sql
-- تشغيل ملف fix_product_service_permissions.sql
-- أو تشغيل هذه الأوامر:

INSERT IGNORE INTO permissions (name, guard_name, created_at, updated_at) VALUES
('manage product & service', 'web', NOW(), NOW()),
('create product & service', 'web', NOW(), NOW()),
('edit product & service', 'web', NOW(), NOW()),
('delete product & service', 'web', NOW(), NOW());

-- إعطاء الصلاحيات لدور company
INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT p.id, 'Spatie\\Permission\\Models\\Role', r.id
FROM permissions p, roles r
WHERE p.name IN ('manage product & service', 'create product & service', 'edit product & service', 'delete product & service')
AND r.name = 'company';
```

### الحل 4: إنشاء Route تشخيصي

```php
// أضف هذا في routes/web.php للتشخيص
Route::get('debug-product-service', function() {
    $user = Auth::user();
    
    return [
        'user_id' => $user->id,
        'user_name' => $user->name,
        'user_type' => $user->type,
        'roles' => $user->getRoleNames(),
        'has_manage_permission' => $user->can('manage product & service'),
        'has_create_permission' => $user->can('create product & service'),
        'all_permissions' => $user->getAllPermissions()->pluck('name'),
        'product_permissions' => $user->getAllPermissions()->filter(function($p) {
            return strpos($p->name, 'product') !== false;
        })->pluck('name')
    ];
})->middleware('auth');
```

### الحل 5: مسح Cache

```bash
# في السيرفر
php artisan config:cache
php artisan route:cache
php artisan permission:cache-reset
php artisan cache:clear
```

## 🔧 خطوات التنفيذ

### الخطوة 1: فحص سريع
```bash
# زيارة route التشخيص
https://your-domain.com/debug-product-service
```

### الخطوة 2: تطبيق الحل المناسب
- إذا كانت الصلاحيات مفقودة → تشغيل SQL
- إذا كانت موجودة لكن المستخدم لا يملكها → إعطاء الصلاحيات للدور
- إذا كان كل شيء صحيح → مسح Cache

### الخطوة 3: اختبار النتيجة
```bash
# زيارة صفحة Product Service
https://your-domain.com/productservice
```

## 🚨 حلول طوارئ

### إذا استمر الخطأ 500:

#### 1. فحص Laravel Logs
```bash
tail -f storage/logs/laravel.log
```

#### 2. تعديل مؤقت في Controller
```php
// في ProductServiceController.php - السطر 35
// استبدل هذا:
if (\Auth::user()->can('manage product & service')) {

// بهذا مؤقتاً:
if (true || \Auth::user()->can('manage product & service')) {
```

#### 3. فحص قاعدة البيانات
```sql
-- فحص جداول الصلاحيات
SHOW TABLES LIKE '%permission%';
SHOW TABLES LIKE '%role%';

-- فحص محتوى الجداول
SELECT COUNT(*) FROM permissions;
SELECT COUNT(*) FROM roles;
SELECT COUNT(*) FROM model_has_permissions;
SELECT COUNT(*) FROM model_has_roles;
```

## ✅ التحقق من النجاح

بعد تطبيق الحل:

1. **زيارة صفحة Product Service** - يجب أن تعمل بدون خطأ 500
2. **فحص القائمة الجانبية** - يجب أن يظهر رابط Product Service
3. **اختبار الوظائف** - إنشاء/تعديل/حذف منتج
4. **فحص الصلاحيات** - التأكد من وجود جميع الصلاحيات

## 📞 إذا لم تنجح الحلول

أرسل لي:
1. نتيجة `/debug-product-service`
2. محتوى Laravel logs
3. نتيجة فحص قاعدة البيانات
4. دور المستخدم الحالي ونوعه

وسأساعدك في حل المشكلة المحددة.
