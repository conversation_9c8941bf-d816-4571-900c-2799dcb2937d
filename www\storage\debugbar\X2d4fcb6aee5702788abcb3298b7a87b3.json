{"__meta": {"id": "X2d4fcb6aee5702788abcb3298b7a87b3", "datetime": "2025-06-07 04:33:11", "utime": **********.63066, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270790.287278, "end": **********.630693, "duration": 1.3434150218963623, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749270790.287278, "relative_start": 0, "end": **********.454763, "relative_end": **********.454763, "duration": 1.167484998703003, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.454785, "relative_start": 1.1675071716308594, "end": **********.630696, "relative_end": 3.0994415283203125e-06, "duration": 0.17591094970703125, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767504, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0092, "accumulated_duration_str": "9.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5439901, "duration": 0.00595, "duration_str": "5.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.674}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.581625, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.674, "width_percent": 15.978}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.606167, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.652, "width_percent": 19.348}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2100683306 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270782662%7C13%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkphVzhnOGVER215bzZhbVZwN1Uyenc9PSIsInZhbHVlIjoibTh5dVh1R0FMRWpSalRTSWZHRkUvMFAwTTB4NDA1QVVQRng1M0NoUW5VUmVNUGQvS3k1a3MzNFBDTnphQllmQzVZZ3BzbHNLazY0NGJTNHBhcFFhWEJwdmcrK2d0TzR4WlpHTTZBeW0xRDBxeDU0WFQxQ3VtR0FwaEFxWUE5bThxNG9oLzV6SjFmZzBwYWJZQU5zanB1WGI3b3NBOTdySVMyL1dITVpieit3a1duVWhScitmTGJFSkhGT05DVDhodmRVYVZaQXZKUnFSOW5JbHozUHIreDduYTV6RkF1cmtDK3UwRzM5Ui9ZK2lsVS84ZDV3cmNyblR5cThDZnpEeHliM2NVNVJ3VzhEcHVYcUVSRWpoRitqMHhIS3NUN1FXWXNPNCt4WFg4alZFcExENG8xMjRKejVzVjNNVTJodVR0YmRXZ2QyaTMxcnVadkNjU1NqWWkwejM0ZHpYTVZBbHRpbkhVbWgreHJYUjZ3aGNBUW9sREQvSTFUMERhMlFvcU14TEh0MUt3RzhoZm5aNnJpekcxV3dTc3F3UEd4VldFTkxVY1dFc0NpZG9zV2J3MzRRem52WHVlekxKT21kUnk5b29ybW5yajZuVVR4ZE1pZStuRmJWb3ZxSVlsd0pseEhac3lLUGZrb1pWQ3FuWmQveVU4bllXU2ZwRHFMQTAiLCJtYWMiOiI0Njg3Yjg4NTEyYzQ0ZTRjNTNkZmY4ZGI0MGU5MmEwMjk2NmU0MWNhODUyM2U4YTVhZGQ1OGNkN2MzZDljMWQyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxHUTNRQ1U2Ymwwa2FaYTBjL2JIcEE9PSIsInZhbHVlIjoiWVpySnZoYzc3TUhIeS9sVVRZdUREc1IwdXlvdjJ6aGtNQ2NSeWdjU3NGeHNDbDhrQkUvcnZXL0ZVMEJhTFYwNEZ4YTdsZ0YyakNrdDRjR1Z1SXpxb1RUbWtTM0lUVTl0cXJ2QzJpczVJTlVjeVRxWTlWSlVNNnloTlladUVzVXlqb0M3ZllXTnFZK1ExUzRhQ0FpNDdWZUJrUmZiSlllaXJub2ZxRGsvcktDcGplU25scEhMZkZUOE9nOWduc3JYTW50R0lVU0FkN1pWbGdOTjFCZDdjUnNnVTdvWTFpYlpjVVhnSVQ1WDNwa0dIbkI4Ny9Mb2JSbDVsZW53anpFZDJiUzQzZnlGUWdoeHhrTno1UG0wYmcwYmJ6Q1JmTUxJVVpOYlRsY2x0VlNuQWlmM3EySlJwTkhEa2hoQ3YwREVReDc5UVZ2RHl1YVBKSW1ZVCsxYU8yQmRnMTlWTHAzVnBtcEhoMWticHpUeGk3Kzh6b0hVYythckljRTd6NEkyWk54TmRpQVFYbjI3QWFJN1JSVGwzWUsyQm00elBPb3B4VmYzOFFBWmRGci81emxvYnltMjdzSERTQ21DNzBpeEd0ME1mb2lBY3RUMjdDODkrTy9kY0s0SzltNHhmbmJEQUZxSHl3SmtKZzhnSXk0T3JhdmN6YUtlOXRXQVhwcmUiLCJtYWMiOiIzOThlNGI5NGExOWNkOTdjNTY3NjY3ZTYyNzA3M2QzYjM3OGQzYzM4OGFjOTRlOThmZWUxNGM2NmQyYzQzMzc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100683306\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1833745134 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zyhWlMrUupyybJnMtPnBjJ716zpTnMkeCiqRAqWb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833745134\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-670959390 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:33:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlYrdVZmN2pQR1laWHV6NjZidnlTZkE9PSIsInZhbHVlIjoiRDhZVWp5ZU96K3lpemVxUEhxbjhyRzlReXRRYngvbUxweE5hbjRldTdKckVpR3RWeGZ0MFpoblZPT3JFRERiNVpJcCtMNFEzOFhJV3ZCS0E2Y2FreS9ZMlByWk93elEzK3ZIOTQ0M0dQaVVoQTUyTU55cDlJRWk3TFdoZExJMkd3d3MvQXI1aXJYYmZqMFoyOHR1M0JQK3dJSVNCTmpmZ015aTZGYWkrZmdMVkN5SHVxYWZQS3g4dE8xYkd5dEhtOG42TW9XWTRlMmhNRHlXcDAwNUthT3BnTjRia3VEWlZ5dVYyK3IwS2RSWnRSSW5ZRjRWT3JiaEw4WWphSUwzTnlOblpEdUxRNXZNTHIxMFlLeHFnZFB4NEJSbnljRlp0VXkyQUhrUjhOS3F5alltcjdYRmV5ellzWDgxMWQ3eENaemx4UUx2d3JmZktoaktqczd6d0p1MWIyN1RiQU9WZ28yN0x6eUtrR0t4UGZYVFV1R011SWtKZzUxR2lCdU5lT3NRRlB1RWJtMWRmQ3BDK2d3QWdMaWhPTVRhSTNUcmtITXBtaFpUUVpGWjdaWkJZNWhDMlN1Umd3dS95VDN1MnRUYXBVcVpqOTZ5Q25lUVpPZ0ZXSkFVczZaTFMwbE5iR2I0UDNEdEdOT1djQnBiVTBMQThXblNFVnhDaDBoMEQiLCJtYWMiOiJhODUyYzZmZmRhOTg5NWQxMTM5Y2NkNWIxZTAwM2YzNWU4NzI5MGYzNTM5OTlhNTY0M2RlOTQ5NGFiZjU4ZjhmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1pZmlnSjZOYUVKWE5CemRESG0xc0E9PSIsInZhbHVlIjoiYkdQUTNYUWJLVFJsd0YveXNFSGtGa291dXpWK3UyVTl5VDkvbUVodHRYL1U0V2hqdk9BWUxGeXo2SHEyWmxEeENBSUl3OTNSeFVOSmt5Ti9pKzJxL0dTRHJPUXBxME1TYy81M0VHVlZnL3hYMVNSd2VaLzR5OXVXZUMzanB2b2gzMkNwWU1EYWh4K0xTSys3eHp4MEpiZ2YzdXlCTmNNblBxUWdwZDNKUHJ6NnZ6SitIcHg2TkFiRlpSekY2Ykt2cXJuL0dUY0RqTmowakhZbWpZazVtNUYyaWVKdmZ2OGJvbnJyU2ErNGI5N0Iya3pDTm8yOU9BcEo2Y3BVMUYyYjZpZ0VsQm4yektZZm5LUmFjcUREQ3V1ck5mVnhuTlVWZjU4WGF4ZFdQcVBQNHAwbUtaUlBGVVVLNnQvc1NucHFDRnUrMVI0SFhONnVTNTRXRDh5M0hGUXR5TlF2TWZrbUVuVnRnTlJNSXdtczBMZmZZZlluZGNrZGhzeDBkRjBEOVhHU3RLWFRYTlVYQTJEZlN1MS9LT1VaLytwU1hkWjBMaHdrNjNMaUpSb3ozUkN1M21sbCtiQldJYzQ1eUo5bi8xN2dJTm4wVUVQcW95RlZEQm45WG5vbmc1bnJkb29qWTQ2MWp3YmlRc1lOcGgzZHR5MlgvNjB4SkpVNVFSR04iLCJtYWMiOiI0NmU2ZjU1MmEwNjlhZGI0ZmZmYjBlZDIyNjE2Y2VkNWZkODc2N2E1NTZhMjlmMDFlZmNmNjJiN2RjNWQ0ZGZmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlYrdVZmN2pQR1laWHV6NjZidnlTZkE9PSIsInZhbHVlIjoiRDhZVWp5ZU96K3lpemVxUEhxbjhyRzlReXRRYngvbUxweE5hbjRldTdKckVpR3RWeGZ0MFpoblZPT3JFRERiNVpJcCtMNFEzOFhJV3ZCS0E2Y2FreS9ZMlByWk93elEzK3ZIOTQ0M0dQaVVoQTUyTU55cDlJRWk3TFdoZExJMkd3d3MvQXI1aXJYYmZqMFoyOHR1M0JQK3dJSVNCTmpmZ015aTZGYWkrZmdMVkN5SHVxYWZQS3g4dE8xYkd5dEhtOG42TW9XWTRlMmhNRHlXcDAwNUthT3BnTjRia3VEWlZ5dVYyK3IwS2RSWnRSSW5ZRjRWT3JiaEw4WWphSUwzTnlOblpEdUxRNXZNTHIxMFlLeHFnZFB4NEJSbnljRlp0VXkyQUhrUjhOS3F5alltcjdYRmV5ellzWDgxMWQ3eENaemx4UUx2d3JmZktoaktqczd6d0p1MWIyN1RiQU9WZ28yN0x6eUtrR0t4UGZYVFV1R011SWtKZzUxR2lCdU5lT3NRRlB1RWJtMWRmQ3BDK2d3QWdMaWhPTVRhSTNUcmtITXBtaFpUUVpGWjdaWkJZNWhDMlN1Umd3dS95VDN1MnRUYXBVcVpqOTZ5Q25lUVpPZ0ZXSkFVczZaTFMwbE5iR2I0UDNEdEdOT1djQnBiVTBMQThXblNFVnhDaDBoMEQiLCJtYWMiOiJhODUyYzZmZmRhOTg5NWQxMTM5Y2NkNWIxZTAwM2YzNWU4NzI5MGYzNTM5OTlhNTY0M2RlOTQ5NGFiZjU4ZjhmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1pZmlnSjZOYUVKWE5CemRESG0xc0E9PSIsInZhbHVlIjoiYkdQUTNYUWJLVFJsd0YveXNFSGtGa291dXpWK3UyVTl5VDkvbUVodHRYL1U0V2hqdk9BWUxGeXo2SHEyWmxEeENBSUl3OTNSeFVOSmt5Ti9pKzJxL0dTRHJPUXBxME1TYy81M0VHVlZnL3hYMVNSd2VaLzR5OXVXZUMzanB2b2gzMkNwWU1EYWh4K0xTSys3eHp4MEpiZ2YzdXlCTmNNblBxUWdwZDNKUHJ6NnZ6SitIcHg2TkFiRlpSekY2Ykt2cXJuL0dUY0RqTmowakhZbWpZazVtNUYyaWVKdmZ2OGJvbnJyU2ErNGI5N0Iya3pDTm8yOU9BcEo2Y3BVMUYyYjZpZ0VsQm4yektZZm5LUmFjcUREQ3V1ck5mVnhuTlVWZjU4WGF4ZFdQcVBQNHAwbUtaUlBGVVVLNnQvc1NucHFDRnUrMVI0SFhONnVTNTRXRDh5M0hGUXR5TlF2TWZrbUVuVnRnTlJNSXdtczBMZmZZZlluZGNrZGhzeDBkRjBEOVhHU3RLWFRYTlVYQTJEZlN1MS9LT1VaLytwU1hkWjBMaHdrNjNMaUpSb3ozUkN1M21sbCtiQldJYzQ1eUo5bi8xN2dJTm4wVUVQcW95RlZEQm45WG5vbmc1bnJkb29qWTQ2MWp3YmlRc1lOcGgzZHR5MlgvNjB4SkpVNVFSR04iLCJtYWMiOiI0NmU2ZjU1MmEwNjlhZGI0ZmZmYjBlZDIyNjE2Y2VkNWZkODc2N2E1NTZhMjlmMDFlZmNmNjJiN2RjNWQ0ZGZmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-670959390\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}