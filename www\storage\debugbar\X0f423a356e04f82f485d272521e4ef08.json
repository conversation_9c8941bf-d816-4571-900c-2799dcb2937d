{"__meta": {"id": "X0f423a356e04f82f485d272521e4ef08", "datetime": "2025-06-06 20:45:06", "utime": **********.101745, "method": "POST", "uri": "/business-setting", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242704.497934, "end": **********.101791, "duration": 1.6038568019866943, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749242704.497934, "relative_start": 0, "end": **********.649859, "relative_end": **********.649859, "duration": 1.1519248485565186, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.649895, "relative_start": 1.151960849761963, "end": **********.101796, "relative_end": 5.0067901611328125e-06, "duration": 0.4519009590148926, "duration_str": "452ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52918720, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST business-setting", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\SystemController@saveBusinessSettings", "namespace": null, "prefix": "", "where": [], "as": "business.setting", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=366\" onclick=\"\">app/Http/Controllers/SystemController.php:366-498</a>"}, "queries": {"nb_statements": 19, "nb_failed_statements": 0, "accumulated_duration": 0.08109, "accumulated_duration_str": "81.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7462041, "duration": 0.01397, "duration_str": "13.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 17.228}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.79079, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 17.228, "width_percent": 1.332}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7991002, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 18.56, "width_percent": 1.455}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.850755, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 20.015, "width_percent": 2.146}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.858319, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 22.161, "width_percent": 1.418}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 382}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.87208, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 23.579, "width_percent": 1.431}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('15-logo-dark.png', 'company_logo_dark', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["15-logo-dark.png", "company_logo_dark", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 389}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.928243, "duration": 0.00985, "duration_str": "9.85ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:389", "source": "app/Http/Controllers/SystemController.php:389", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=389", "ajax": false, "filename": "SystemController.php", "line": "389"}, "connection": "ty", "start_percent": 25.009, "width_percent": 12.147}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 408}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.945287, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 37.156, "width_percent": 1.677}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('15-logo-light.png', 'company_logo_light', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["15-logo-light.png", "company_logo_light", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 415}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.956479, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:415", "source": "app/Http/Controllers/SystemController.php:415", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=415", "ajax": false, "filename": "SystemController.php", "line": "415"}, "connection": "ty", "start_percent": 38.833, "width_percent": 4.489}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 436}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.96851, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 43.322, "width_percent": 1.591}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('15-favicon.png', 'company_favicon', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["15-favicon.png", "company_favicon", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 442}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.979007, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:442", "source": "app/Http/Controllers/SystemController.php:442", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=442", "ajax": false, "filename": "SystemController.php", "line": "442"}, "connection": "ty", "start_percent": 44.913, "width_percent": 6.018}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('شركة  تسوق بسرعة التجارية', 'title_text', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["شركة  تسوق بسرعة التجارية", "title_text", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.991703, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 50.931, "width_percent": 5.599}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('شركة تسوق بسرعة التجارية . ذات مسؤلية محدودة', 'footer_text', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["شركة تسوق بسرعة التجارية . ذات مسؤلية محدودة", "footer_text", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.002821, "duration": 0.01315, "duration_str": "13.15ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 56.53, "width_percent": 16.217}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('en', 'default_language', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["en", "default_language", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.022507, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 72.746, "width_percent": 4.575}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('#d53434', 'color', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["#d53434", "color", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.034549, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 77.321, "width_percent": 4.649}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('true', 'color_flag', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["true", "color_flag", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.045302, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 81.971, "width_percent": 4.994}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'SITE_RTL', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "SITE_RTL", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0561619, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 86.965, "width_percent": 4.193}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'cust_theme_bg', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "cust_theme_bg", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.067133, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 91.158, "width_percent": 4.587}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'cust_darklayout', 15) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "cust_darklayout", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.077579, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 95.745, "width_percent": 4.255}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage business settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage business settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage business settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870908, "xdebug_link": null}]}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "Brand Setting successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/business-setting", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>title_text</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1588;&#1585;&#1603;&#1577;  &#1578;&#1587;&#1608;&#1602; &#1576;&#1587;&#1585;&#1593;&#1577; &#1575;&#1604;&#1578;&#1580;&#1575;&#1585;&#1610;&#1577;</span>\"\n  \"<span class=sf-dump-key>footer_text</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1588;&#1585;&#1603;&#1577; &#1578;&#1587;&#1608;&#1602; &#1576;&#1587;&#1585;&#1593;&#1577; &#1575;&#1604;&#1578;&#1580;&#1575;&#1585;&#1610;&#1577; . &#1584;&#1575;&#1578; &#1605;&#1587;&#1572;&#1604;&#1610;&#1577; &#1605;&#1581;&#1583;&#1608;&#1583;&#1577;</span>\"\n  \"<span class=sf-dump-key>default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">theme-3</span>\"\n  \"<span class=sf-dump-key>custom_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#d53434</span>\"\n  \"<span class=sf-dump-key>custom-color</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>color_flag</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>company_logo_dark</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#169</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"10 characters\">php81C.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"10 characters\">php81C.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"44 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\php81C.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>company_logo_light</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#3531</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"10 characters\">php81D.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"10 characters\">php81D.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"44 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\php81D.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>company_favicon</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#3530</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"10 characters\">&#1588;&#1593;&#1575;&#1585; 2.png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"10 characters\">&#1588;&#1593;&#1575;&#1585; 2.png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"10 characters\">php81E.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"10 characters\">php81E.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"44 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\php81E.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1125379038 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">42767</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryTOX11C8IP05py27X</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; laravel_session=eyJpdiI6Ik1DVzgzaVM3SFpra2RiV0dWWkdHeEE9PSIsInZhbHVlIjoiSW03UWU1TzJPK3hTWTIxQXVrbnhvVXF6VlBpU2lkdGhvUTR1VmxDay9EcEIzL0pxTXIzamsxZ1RSMU9GemJKVU94YVd3bXRsdGFSTTJmNWZyLzVOcjJFQ3ZoVVh2Und3OUJGSmN2dWo4VnpwNS9Xei9Sc2hUQmxKWGVSMC9rVWRtMlY0MUxFcVYydUQ5RzJ0SSt6K3krbHh0TDVUV2VTOFF1TDNZRnBub01GaXIrSlM0ODJaS3BKNkdVWWxMVTMyakJiTm5Cb3J4TFI3VWV0U1IwNno3R2g1bjR5QUg4Q0FXcHIzVVAwdzFGNnZaTXZTY2RDY2R5L1hOQ1JSdzc3cUlOdW1iR2NQNEpvd21XeDNXVFJ4MklMWmJBUGtIQzJBRnc2LzVFbWNNOGZ5QkU4bkJWS1Z2aEtJY3ZSd1plclQrTXk0VXc3Y0JpWHNXUkFrUWIvcUk5M3FtNEdHYUNIRjJnY0dBbmlaNUVJdWtER3RJam5ic1NsblhPUnhjdUE0aXhOdktSYitUWVRhdTNoNmtOcTU4dnhaUGxQYTFQY2RGSFZ2aHJQTDlyTVUzZzdIN20yM1dPOHA5dnN6Wi9hdVhtMlZJT0t5SW9zSGZtV1NWNWRuR08yVDFVL2JxM1pFeDloeTlXTXhicGRGSjNlOGhGb0hTcE1QSWhOSmlqSTAiLCJtYWMiOiI2ODE0NWI5ZGVjZWNjMjI0YTFlYWVjMjk4OGFiMjE3MWI3YzZkODY2NDhjOGQ1NGRiNTgzYzFkYzY2M2Q2NDBkIiwidGFnIjoiIn0%3D; _clsk=1i6cij5%7C1749242670077%7C27%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtnS011UzJyYnYvL25xc2NNNVhVUUE9PSIsInZhbHVlIjoiN0d6L3ppci92bEVzclVibG5Eak9BcmVRUTBBUEVYZTVldXNNT2E2VW93eEw5cUllT2lLR2tYbTVqY2x1eGZuclUzQWJSWlM4amV4WW82REV0d2xGbGQrU2QvTm5VcWtBUlVndkoxWXU0VVVvZG92UDR5alhEdU5RekluUlFOZHVKS1U1MGxyOWZqMklpNW1GNmFnd3BEdkpnNGVReWdsK1djUlJFZHVKdkcvK1ByWDdmbThxNGpSbGRRYTVJZ0szL3dEYm8vRk1sVzUyWEJhU1NtdDFTTTlnZzR5LzhDcytTL0lubzF5bEg2NDZML3FudVdZR3hhOW1oRkYrL2ZDendsUkY2RFBSRlRSSndvOWZIS21mdkdDd0xpZGlnSGNINWtqRXJLdHJvV3g3RHk2bVFuYWRwSWozVUNvWkZyR054c0dzNEl3WEJCbjJuVDQzTGF2NlZzM0diZzdOQ2RsVjQzOEN2ZytONXlYOUtiYUUrVnpjamIwbFMrWlhUS2FJSkIyQTlKLy9rNXY4UVFDL3ErWXRNRisrNzlkNmFXY1BEMlkvTDJDdDYyMnhqUXBEd0FqaHBuTldCNGZENnlLTnYrSjNmNk8xczRZaTNjWTNXcDQ1WlNLYnU1enhSWFp6YnpIbStPMW9wRk1ZQTdvWUNWVlU0dFlYRXJWRktIdVUiLCJtYWMiOiI5OGE5NjI2OThhMGEzMTNjZDMzMzQyNzU1OGYzOTczYzczMWJkNWEyYzZkNTNjZGE3YWY5MWRlYjhlMmVkYjMxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBnU3prMnIrTFFSeVRZSVNha3BleVE9PSIsInZhbHVlIjoicHJZMGRYb1JNTE1rSTl4QTZPd0d6clpiWk4wRVNGM2F2R05FRVlkbGdlckxMbzcxWHFJVjJxb0dxU3F0K0JSZXUwWDcvdUhFM0FvRml6MVRHUVM0NmVHSnhDK2I3aUhhK3hyRGZPVTNiVVRzbVJUNERLQkdwNk91OHBqWnVqTnd4VUNDbFJibnE0MHhZSWxybTBHaGtoMGRkbVVmcFk0bUJrcXBoY1QyVXlyVXJaMmNvRWF2N0wvdC9wNW9ZRVpJMi8zZkxxYWNFZ1MzTTBGRjNwVkpRL210QU5acGVSSHYrRi9xcHBrNUxGUkN4MU1ZbmYrR1ErdWJTRkZBY3BuSy9zN0kvSVpyTjk1ZnhKZ2xsNDJkdnNWbkp5MDVsYnlQRXNJb1lrVkdqdmlUVHRsQUNZTVRLUFZOMkduY2xlTkxMT0cySzYwYUhxbzhyNE5USVIwUmtDbUlVRGFxS0dDY0FWMzJyZFNmaDcxUGx3NmdlR0JoWGNBaDUxVEpiYUlJcTE4eG1mUUlqNHF1ZmVkUWhXV0lqZnVGUlFPSTdGSkRzSE5BQ1NneGwxcityaTM1ZGFMa29jeUlyUnp0UWFRN3ozWE5LOUdLT21tTXlMb2l6MEJ1NE0xQlRCZ1RWYXl2WHhuakNOQ1BwekFWU3hpV3A0WHNaaEJYSWx4ZVR5cFUiLCJtYWMiOiI1Nzc4NjFjODdkZWU1NjdhOWZhM2EyNmUxYTU1YTdjOWFmMzQ1ZTQ3ZmJmMmU0MDM3M2EzZDg0NDJmYWNkN2NiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1125379038\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-279752580 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">19o8GZJlmCRhkVQqQRnrzKTy9LwoYzgyFnAzuAOO</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6Wt4M4bI8NbCUmFc1THCmhEuEOoDYpn2bPL8rDvY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279752580\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-314949253 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:45:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iis1WVNrbGZRZCtLRWk2NjhHbHM2blE9PSIsInZhbHVlIjoiRjlhazdMbHZJY2pscDNKSGd5S3pyMytqbTM5ME5ZMEdKU0dtK0pXYlFUc2tzMUJiMTJ1T0liMXVMT3N2UmRQNnZuaW9rcy9BUCtJM3ZOMkI1TG94dkpSdU54dWxFbS9OT3lHNUZNbENzV0xuOFdwbmpUVmZ3cXRCYmxjRXdZMCtlU2Vsb1RVak1GSFhCL2JpRDByVWhvNUM3bzVVczR1T29Ea2hKSXdEbGFvdXVjTEhIajh5VVV2RU5jM3hNbmJvYngwanlPN1BJcWk3Zkxha0s5bXo5cTVKQ05jVTdJSldkV2pvVDZwQXlLbFJhb01YRHg5Uy8zKzdFd2gzd09idDR5Qm1JaXZGcmdZRnQ0dFl1d0E0MnJLaHViMUU0WWFRZ0pjNVZZVXVIVzh2RmswSDB6ZURIUy9PQWovVW11Znozbi9jOENTWjkzZEtFTUpFQ3lDb1Y0Q1pHdmVRNy91YjRESTVNMkNjbGhPMk5xMjBxWnZ4aEtJR2ZKOXFtRTdjQlVUUHBmdTRERjJTUW1RL2VCd2UxZC82QTlkWDBjc21ldG1kNDZJVWwzYUw3aHZFWjRlajF3c1JXeFI2aWQ3T0ZTV3ZUb2EyandHS3lteldXYlVhUlN0b3ZrYmlCL2NrTHpHaHVlc05kUVNkSDBDb0J5dmFFT09wR1pxanJBTHMiLCJtYWMiOiJkMThjNmRjMmU3Nzg1M2VkNGJkN2Y4ZmIwY2JiMGY4YjdmMzJiMWNhODUwMTY2NmY3MjA1MmNhMzBlNDQ1MjU4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:45:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRCQitDeVo3NVg3eFUwak0wc2tIUnc9PSIsInZhbHVlIjoidjRLdTJQcFR6TU1rM0tEY1dSRTZmRzlob2ZweWsrbmNGQ1BvY1dpQ2xNeDhsZDFndFdmd09EaXNmb0kydkNQWU1YbjhGdWFrV2E3OGx2OXYvenVJbVJaRk1ZcHBRUW42N0hDZ0JNUEo3cnYzSTNneHVCZmk5azk4MlRVNFVvQWFYcjN1NE1kSzBRS0RDaHNvdE9YWitGZDZ3YVdaMlAzeU5vYjM1NTJzRi93aUpNSnRzUUFHZHNCWndiOFlBSm84dlZCekZIem80T3ByVVlyQkErdTRzak1wOTJEMzQ4ZFlLa1VaUjRTMnc5TWZoNjN6RXFMdnc3Z2hKZGdyZzF4L2dxUFRBeWJMVEtDV05jeGFlVHdGaG5vRW5yeDdtTjlUakdoNE54eWcwVGxMRE1TYWlXK25iYzIreEk0T3JKL0xyN0dYekVQMVlVZ0ZRaE9TWGViT212NDFNczNCUHVISGN0ditXcjE5TFZZRm5ic21sVk03TTR4dXp4bDhGUnhIRWJRdDhhSWh5VXFpdW1aRFU5cit5NCtiVXNnWFZYQjk0c2taZUhzKzc3VTQvSG9HNlhlajM2U0kyS0xSdEExT284dktOY09FL3RzY2NldWlCcGRSVlF5Z3MxVU11YVRUb1JCamwwTGFMckhjRDBZcXZqajVFU1Z6TERNSk5nNjkiLCJtYWMiOiI1NjZmZmM1ZThiY2MwNmY1OGQ4NDM1NDgxNjg2MTc0MDk1ODg4ODgzZmFkMDNjNDE5MzczMDc4MDFlN2VhN2Q1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:45:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iis1WVNrbGZRZCtLRWk2NjhHbHM2blE9PSIsInZhbHVlIjoiRjlhazdMbHZJY2pscDNKSGd5S3pyMytqbTM5ME5ZMEdKU0dtK0pXYlFUc2tzMUJiMTJ1T0liMXVMT3N2UmRQNnZuaW9rcy9BUCtJM3ZOMkI1TG94dkpSdU54dWxFbS9OT3lHNUZNbENzV0xuOFdwbmpUVmZ3cXRCYmxjRXdZMCtlU2Vsb1RVak1GSFhCL2JpRDByVWhvNUM3bzVVczR1T29Ea2hKSXdEbGFvdXVjTEhIajh5VVV2RU5jM3hNbmJvYngwanlPN1BJcWk3Zkxha0s5bXo5cTVKQ05jVTdJSldkV2pvVDZwQXlLbFJhb01YRHg5Uy8zKzdFd2gzd09idDR5Qm1JaXZGcmdZRnQ0dFl1d0E0MnJLaHViMUU0WWFRZ0pjNVZZVXVIVzh2RmswSDB6ZURIUy9PQWovVW11Znozbi9jOENTWjkzZEtFTUpFQ3lDb1Y0Q1pHdmVRNy91YjRESTVNMkNjbGhPMk5xMjBxWnZ4aEtJR2ZKOXFtRTdjQlVUUHBmdTRERjJTUW1RL2VCd2UxZC82QTlkWDBjc21ldG1kNDZJVWwzYUw3aHZFWjRlajF3c1JXeFI2aWQ3T0ZTV3ZUb2EyandHS3lteldXYlVhUlN0b3ZrYmlCL2NrTHpHaHVlc05kUVNkSDBDb0J5dmFFT09wR1pxanJBTHMiLCJtYWMiOiJkMThjNmRjMmU3Nzg1M2VkNGJkN2Y4ZmIwY2JiMGY4YjdmMzJiMWNhODUwMTY2NmY3MjA1MmNhMzBlNDQ1MjU4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:45:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRCQitDeVo3NVg3eFUwak0wc2tIUnc9PSIsInZhbHVlIjoidjRLdTJQcFR6TU1rM0tEY1dSRTZmRzlob2ZweWsrbmNGQ1BvY1dpQ2xNeDhsZDFndFdmd09EaXNmb0kydkNQWU1YbjhGdWFrV2E3OGx2OXYvenVJbVJaRk1ZcHBRUW42N0hDZ0JNUEo3cnYzSTNneHVCZmk5azk4MlRVNFVvQWFYcjN1NE1kSzBRS0RDaHNvdE9YWitGZDZ3YVdaMlAzeU5vYjM1NTJzRi93aUpNSnRzUUFHZHNCWndiOFlBSm84dlZCekZIem80T3ByVVlyQkErdTRzak1wOTJEMzQ4ZFlLa1VaUjRTMnc5TWZoNjN6RXFMdnc3Z2hKZGdyZzF4L2dxUFRBeWJMVEtDV05jeGFlVHdGaG5vRW5yeDdtTjlUakdoNE54eWcwVGxMRE1TYWlXK25iYzIreEk0T3JKL0xyN0dYekVQMVlVZ0ZRaE9TWGViT212NDFNczNCUHVISGN0ditXcjE5TFZZRm5ic21sVk03TTR4dXp4bDhGUnhIRWJRdDhhSWh5VXFpdW1aRFU5cit5NCtiVXNnWFZYQjk0c2taZUhzKzc3VTQvSG9HNlhlajM2U0kyS0xSdEExT284dktOY09FL3RzY2NldWlCcGRSVlF5Z3MxVU11YVRUb1JCamwwTGFMckhjRDBZcXZqajVFU1Z6TERNSk5nNjkiLCJtYWMiOiI1NjZmZmM1ZThiY2MwNmY1OGQ4NDM1NDgxNjg2MTc0MDk1ODg4ODgzZmFkMDNjNDE5MzczMDc4MDFlN2VhN2Q1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:45:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314949253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Brand Setting successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}