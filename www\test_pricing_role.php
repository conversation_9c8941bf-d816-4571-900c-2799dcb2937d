<?php
/**
 * ملف اختبار دور التسعير
 * يمكن تشغيل هذا الملف للتأكد من إنشاء دور Pricing بنجاح
 */

// تضمين ملف Laravel bootstrap
require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

try {
    // بدء Laravel application
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "🔍 اختبار دور التسعير...\n\n";

    // 1. التحقق من وجود الصلاحيات
    echo "1️⃣ التحقق من الصلاحيات:\n";
    $pricingPermissions = ['manage pricing', 'show pricing', 'edit pricing'];
    
    foreach ($pricingPermissions as $permission) {
        $exists = Permission::where('name', $permission)->exists();
        echo "   - $permission: " . ($exists ? "✅ موجود" : "❌ غير موجود") . "\n";
    }

    // 2. التحقق من وجود دور Pricing
    echo "\n2️⃣ التحقق من دور Pricing:\n";
    $pricingRole = Role::where('name', 'Pricing')->first();
    
    if ($pricingRole) {
        echo "   ✅ دور Pricing موجود (ID: {$pricingRole->id})\n";
        
        // التحقق من الصلاحيات المرتبطة بالدور
        $rolePermissions = $pricingRole->permissions->pluck('name')->toArray();
        echo "   📋 الصلاحيات المرتبطة:\n";
        foreach ($rolePermissions as $permission) {
            echo "      - $permission\n";
        }
    } else {
        echo "   ❌ دور Pricing غير موجود\n";
    }

    // 3. التحقق من المستخدم التجريبي
    echo "\n3️⃣ التحقق من المستخدم التجريبي:\n";
    $pricingUser = User::where('email', '<EMAIL>')->first();
    
    if ($pricingUser) {
        echo "   ✅ المستخدم التجريبي موجود\n";
        echo "   📧 البريد: {$pricingUser->email}\n";
        echo "   👤 الاسم: {$pricingUser->name}\n";
        echo "   🏷️ النوع: {$pricingUser->type}\n";
        
        // التحقق من الأدوار
        $userRoles = $pricingUser->roles->pluck('name')->toArray();
        echo "   🎭 الأدوار: " . implode(', ', $userRoles) . "\n";
        
        // التحقق من الصلاحيات
        $userPermissions = $pricingUser->getAllPermissions()->pluck('name')->toArray();
        echo "   🔑 عدد الصلاحيات: " . count($userPermissions) . "\n";
    } else {
        echo "   ❌ المستخدم التجريبي غير موجود\n";
    }

    // 4. اختبار منطق القائمة الجانبية
    echo "\n4️⃣ اختبار منطق القائمة الجانبية:\n";
    
    if ($pricingUser) {
        // محاكاة تسجيل الدخول
        $isCashier = $pricingUser->hasRole('Cashier');
        $isDelivery = $pricingUser->hasRole('Delivery');
        $isCompany = $pricingUser->hasRole('company');
        $isHR = $pricingUser->hasRole('Human resources');
        $isAccountant = $pricingUser->hasRole('accountant');
        $isSuperFiesr = $pricingUser->hasRole('SUPER FIESR');
        $isPricing = $pricingUser->hasRole('Pricing');
        
        $isPricingOnly = $isPricing && !$isCompany && !$isAccountant && !$isSuperFiesr && !$isCashier && !$isHR && !$isDelivery;
        
        echo "   🔍 نتائج التحقق:\n";
        echo "      - isPricing: " . ($isPricing ? "✅" : "❌") . "\n";
        echo "      - isCompany: " . ($isCompany ? "✅" : "❌") . "\n";
        echo "      - isAccountant: " . ($isAccountant ? "✅" : "❌") . "\n";
        echo "      - isPricingOnly: " . ($isPricingOnly ? "✅" : "❌") . "\n";
        
        if ($isPricingOnly) {
            echo "   🎯 النتيجة: سيظهر قسم التسعير فقط\n";
        } else {
            echo "   📋 النتيجة: ستظهر القائمة العادية\n";
        }
    }

    // 5. إحصائيات عامة
    echo "\n5️⃣ إحصائيات عامة:\n";
    $totalUsers = User::count();
    $totalRoles = Role::count();
    $totalPermissions = Permission::count();
    $pricingUsers = User::whereHas('roles', function($q) {
        $q->where('name', 'Pricing');
    })->count();
    
    echo "   👥 إجمالي المستخدمين: $totalUsers\n";
    echo "   🎭 إجمالي الأدوار: $totalRoles\n";
    echo "   🔑 إجمالي الصلاحيات: $totalPermissions\n";
    echo "   💰 مستخدمي التسعير: $pricingUsers\n";

    echo "\n✅ انتهى الاختبار بنجاح!\n";

} catch (Exception $e) {
    echo "❌ خطأ في الاختبار: " . $e->getMessage() . "\n";
    echo "📍 الملف: " . $e->getFile() . "\n";
    echo "📍 السطر: " . $e->getLine() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📝 ملاحظات:\n";
echo "- إذا ظهرت أخطاء، تأكد من تشغيل Migration أولاً\n";
echo "- يمكن تشغيل: php artisan migrate\n";
echo "- يمكن تشغيل: php artisan db:seed --class=PricingRoleSeeder\n";
echo "- للاختبار اليدوي: تسجيل الدخول بـ <EMAIL> / 1234\n";
echo str_repeat("=", 50) . "\n";
?>
