{"__meta": {"id": "X10db645b0a2f9618bd1d56123ca3df4e", "datetime": "2025-06-06 20:36:05", "utime": **********.773279, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242164.520326, "end": **********.773307, "duration": 1.252981185913086, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1749242164.520326, "relative_start": 0, "end": **********.667712, "relative_end": **********.667712, "duration": 1.147386074066162, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.667731, "relative_start": 1.1474051475524902, "end": **********.773309, "relative_end": 1.9073486328125e-06, "duration": 0.10557794570922852, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43123064, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00449, "accumulated_duration_str": "4.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7493272, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1145636471 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1145636471\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2060598463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2060598463\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2063828355 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242138186%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdlWm5SdWhvNjNXV3BsZjhsVm42V2c9PSIsInZhbHVlIjoieVFabWd4QVVnU3ZCSDNYT1gyMkVUMnYxUUE2V1RVN3JDWEdvd2ZtUXMxQ3k2d3huVXlYeGw5RFAwTjlKd25HeEVBTC9GSkowRlNGNytxRmNqWldsNW4weGs2azBqYXRPQkNtcVlibVVxRjJhSVd5a2daL1BMRllkSGNzbUZiY2M2ZEhRR3hjRnMwcG1lU01YQzJGaTdOUTdkbHN1eUZuU2hRZDNWak5DVW5STlo1eDYxYlVVOEJWSGg5M3YyQWpjZnR0WkMralBGWmJwT1BHQTJETmlSN2xKQmJuTkIreklCaHIwdFF5QWF1Y1g1ZmFXZFl3b3JZUmhLVW5iQUVEeDdSekNIZHdSb0hiUFBudFdLV3c3a3NxUlAzU0Q2LzhNaUR5dE54aFZvWUtRY0xidGRNUWxOM3JjSnhLOXEzRUJGRlhxbCt6QTcvWDJUb3BXVmZ0R0hwcGo1Tm5QeW9XZ0t0aTgzRmtKTTRZSndNRnRDTFVPLzBDaEQzSnN6d1dKdkxkKzUxeVg3OVZBUDN4V0dEMFg0aCtwMVRvVzEwRDY0eUp2bUViajJuWmdpbS9mb2RDMTNMcDNwcDZiRTFRaGNBS2kweGs3ZW5NR0hhU2NlSFgraFhDSWVOWGg5L3FzZlczeTdPN21GSDB3dTJEcGVPSkx6Mk9LL01ESE45YzciLCJtYWMiOiI5YzhiYzQ4Yjg3OGE2ZTcwZGFmYTkwZTcyMWNhODk2YTlmYmYzMTYxNmM0YjBhOWQzZWZiNTg1NDAxODc0OTZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtINFhNbzJ1UE9VYmJaWHppNFBLbkE9PSIsInZhbHVlIjoiZUJnMVBpc2l5YXdtSmd4VGY3QUVNRUZvTGQyZkJLQUU3NVNHZmhoZ0hZKy9GTk9DSG4vYzlCbWJFOWFMMkgyTWFxdzVpcW1XOHhHdGNqNWtsMno0dHlyelR2R2N2MHJXa2t0M1JJL3E5STlIVmNsekNoWkRBclB1b3ZLNWQ2cHFQckdrZWxVUDl0QmtQN1h4Z0tNejRFZE05VlhHSWx3N2g1akg3WVN6Y3kvUS9JdGZPdTB1SjQ4M1dCRE5mWTFJTzN1ekVvMVJBRHB6cWdzWDZRYk9LNmN0dFpCeDhUK05DbUVncHZBWEljOEIrdHRmdHd6K0RWbktCMndqTk1zNjlraDVTYU1sQ016MHIxN3pJdml1YkNSZTB3RERhU3RRd2F3VDE5YjVkN0VaWlpHRkFTMldvYkJ0RnNTalhya0JCQmM0bVFLYkpsRTArRUk2Y0dwaElCTW8xSUxQQUM3T1FXTzZ3ZVd0d1hYcWFDYlVrcUdzSHpmRFF5TEpidTJTTHI1MmhET2djVVJZWXNkUkRaYUl2WkxoSDh4N2NTUHVBQWRDaUwvR2g1MWtyc2krYkpjdytrRFNGblJ1cnU1NmxuU1BKUllwdEY4NVhYb0xzcnIwbzFqZHhuc2MrUTBRZ2tDQUVTMHVDODRtNVA4bzYreFV3YmtNSEUvOHhST2UiLCJtYWMiOiIxYTkzZDU0NjcxYmZkZjMyOWZmYTFjODdhMDJmYTZmYWM3OGFhZmQxOTM4ZDA4YjUyNmFmZTQ5NzViZjQ3YjAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063828355\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2060426379 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tLaYiGHNkG5boJ6Cz4Xh75W1LbW3TwRWDQTQ1jy7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2060426379\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1629480667 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:36:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFRWTdhalF3UVgwTEdLaUhiZmFrWWc9PSIsInZhbHVlIjoiTVdEL0xuSEozQVpMTjRuQlgxbC83YjE3Mzc3SnJVMVRqSDFmcWh1dFZtVWs4ZUsxemdLTTNMT1FkL1VNU3JlNmFuYUxOT0pia0t1SmdaRU1MazRwblJBSGYvRzRlMjU3QTJoUTdtODkyRytDdXh6aHhMYWxueUlyTWlPNTZ2eG1ycTRTSW9YTFJLT2txSzE5T0s0Y2dqcit0Nm5xY01NaGtQbEplL0RmNmpnN3dLOU5GaVlkNmd6dzQwSU5aVjZtT0F5UmEzblhTbVVYMXd5V29Ec0pxcGZmaXZ6Rlg2MnlaL2FjazlqVFVqMmtKaXJQaDFjY1p0ODlWZGZKY1RWY1l2Sm9zTHdsTnlGdUhDOW56RnRTL0JRNGViUWVkWEtaRDdlWmQ4NmlvTVlPdHpBZnZqWFVTZlNKNDZKcE1lcmRtWnpJeGF1YXpnQ3IwWWVoSnBaZGNJOWhKZXBqOHZDSjV3TDFhL1RMWExMNTRSS05KTGxsRGd2OVFHN21JRXVJWnh5cXNYOFlLSEhpUW50aHE2TUVHTTN4V251ckJ2WERneUdUVUtaZkhJWEx3enVzVlNCZDVad0lNaHRSd1ZpTlJsUVl4MEhjRGg0T3ZIT1J5TkkvdTVSVFhYTTZlbHR1K0VGak0zNWdiekNvZklGZEZ2ejFUVEVSUXVEZWJNQ3UiLCJtYWMiOiI0YTMwMmZlNmNkNzg3MTc5ZTQxM2MxN2ZhZDQ5OGQ4NmY3MWM4YjJhNWFlNzVmNzU5YjZiMjk2NjQ3MzlmMmNiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9NcVY0a3VucXNjK0o0dlN3RFVTd0E9PSIsInZhbHVlIjoiQ1FMR2hvVW1yeUhoNHNKZ21FeDI0Wm9wcXhnTzl5Ykh6Tk01OVJ3aDdTbVRoVTJJWThkSFdFUlJ0RG9LRkRwVkpwVzJ0djdoazhqbkI3cTlrbjAzWnFRL3QwdlBHYzZOaVE4bThET0VjUjc4V28xM3MvcDlKUUFHaWkzZGs3Q0cvS3lwQTBSdFVMdWlmQlJPbFFydmNoSFB1SXJSSUxGN1JBejQ4OSt0ZFl1a3I0ZjA3bzBDcmRFcWdTS0gzNFFCY3N2d3RweG9zNlc5S21aTVJ0UzVSTjNKZW9xQ3F6eExzTWZibGlJYVJIVldlQ1VKNVlqK0VxeFFnZXYvbitreTlHQ1A4TFBtMGsxUUVrR0dlcFhLZEtoOXplemxxbHhvVWNpblB6OUxmME1lZGpFKzRPdFVxU2ducFo1Nkdtak5GbU03SlA2WFVyVkZtNmlUU3ZoeWZzYTNmY3NIWEt6eEpHRkRIQlhjM0ZvOXlGS2xLSlI5RXpTSlFqOVBWc3hyWTFlazBaRW16RFVEams4ME54VGdYci9XK0NsRzRGM0QzaE1CYTNodFV5Ty9hSkVqRjBRMFMzUm9qYW93cHNjbVNacXY3bEhpckhpaktCNGYzMG9MQkVkUXhvcy9zdUFQbkNyRDluUUlXbVNFVFhMUTBYYUlSWFF0VzkwM3hjWlUiLCJtYWMiOiIwZTgyNzliMWRkOTgwZGRkNGI0YmZkMzE1Nzc1ODZmMWUxOTljNDVkY2M0ZTI5NWM5OWU0ZDRhZGIyYTcwOWEyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFRWTdhalF3UVgwTEdLaUhiZmFrWWc9PSIsInZhbHVlIjoiTVdEL0xuSEozQVpMTjRuQlgxbC83YjE3Mzc3SnJVMVRqSDFmcWh1dFZtVWs4ZUsxemdLTTNMT1FkL1VNU3JlNmFuYUxOT0pia0t1SmdaRU1MazRwblJBSGYvRzRlMjU3QTJoUTdtODkyRytDdXh6aHhMYWxueUlyTWlPNTZ2eG1ycTRTSW9YTFJLT2txSzE5T0s0Y2dqcit0Nm5xY01NaGtQbEplL0RmNmpnN3dLOU5GaVlkNmd6dzQwSU5aVjZtT0F5UmEzblhTbVVYMXd5V29Ec0pxcGZmaXZ6Rlg2MnlaL2FjazlqVFVqMmtKaXJQaDFjY1p0ODlWZGZKY1RWY1l2Sm9zTHdsTnlGdUhDOW56RnRTL0JRNGViUWVkWEtaRDdlWmQ4NmlvTVlPdHpBZnZqWFVTZlNKNDZKcE1lcmRtWnpJeGF1YXpnQ3IwWWVoSnBaZGNJOWhKZXBqOHZDSjV3TDFhL1RMWExMNTRSS05KTGxsRGd2OVFHN21JRXVJWnh5cXNYOFlLSEhpUW50aHE2TUVHTTN4V251ckJ2WERneUdUVUtaZkhJWEx3enVzVlNCZDVad0lNaHRSd1ZpTlJsUVl4MEhjRGg0T3ZIT1J5TkkvdTVSVFhYTTZlbHR1K0VGak0zNWdiekNvZklGZEZ2ejFUVEVSUXVEZWJNQ3UiLCJtYWMiOiI0YTMwMmZlNmNkNzg3MTc5ZTQxM2MxN2ZhZDQ5OGQ4NmY3MWM4YjJhNWFlNzVmNzU5YjZiMjk2NjQ3MzlmMmNiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9NcVY0a3VucXNjK0o0dlN3RFVTd0E9PSIsInZhbHVlIjoiQ1FMR2hvVW1yeUhoNHNKZ21FeDI0Wm9wcXhnTzl5Ykh6Tk01OVJ3aDdTbVRoVTJJWThkSFdFUlJ0RG9LRkRwVkpwVzJ0djdoazhqbkI3cTlrbjAzWnFRL3QwdlBHYzZOaVE4bThET0VjUjc4V28xM3MvcDlKUUFHaWkzZGs3Q0cvS3lwQTBSdFVMdWlmQlJPbFFydmNoSFB1SXJSSUxGN1JBejQ4OSt0ZFl1a3I0ZjA3bzBDcmRFcWdTS0gzNFFCY3N2d3RweG9zNlc5S21aTVJ0UzVSTjNKZW9xQ3F6eExzTWZibGlJYVJIVldlQ1VKNVlqK0VxeFFnZXYvbitreTlHQ1A4TFBtMGsxUUVrR0dlcFhLZEtoOXplemxxbHhvVWNpblB6OUxmME1lZGpFKzRPdFVxU2ducFo1Nkdtak5GbU03SlA2WFVyVkZtNmlUU3ZoeWZzYTNmY3NIWEt6eEpHRkRIQlhjM0ZvOXlGS2xLSlI5RXpTSlFqOVBWc3hyWTFlazBaRW16RFVEams4ME54VGdYci9XK0NsRzRGM0QzaE1CYTNodFV5Ty9hSkVqRjBRMFMzUm9qYW93cHNjbVNacXY3bEhpckhpaktCNGYzMG9MQkVkUXhvcy9zdUFQbkNyRDluUUlXbVNFVFhMUTBYYUlSWFF0VzkwM3hjWlUiLCJtYWMiOiIwZTgyNzliMWRkOTgwZGRkNGI0YmZkMzE1Nzc1ODZmMWUxOTljNDVkY2M0ZTI5NWM5OWU0ZDRhZGIyYTcwOWEyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629480667\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2007356612 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007356612\", {\"maxDepth\":0})</script>\n"}}