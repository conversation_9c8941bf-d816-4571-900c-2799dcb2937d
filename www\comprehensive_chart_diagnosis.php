<?php
// فحص شامل لصفحة شجرة الحسابات وارتباطها بقاعدة البيانات
echo "<h1>فحص شامل لصفحة شجرة الحسابات</h1>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>1. فحص الراوت (Route)</h2>";
echo "<div style='background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff;'>";
echo "<p><strong>الراوت المستخدم:</strong> <code>Route::resource('chart-of-account', ChartOfAccountController::class)</code></p>";
echo "<p><strong>URL:</strong> <code>http://localhost/chart-of-account</code></p>";
echo "<p><strong>الطريقة:</strong> GET</p>";
echo "<p><strong>الكنترولر:</strong> ChartOfAccountController@index</p>";
echo "<p><strong>المتطلبات:</strong> auth, XSS, revalidate middleware</p>";
echo "</div>";

echo "<h2>2. فحص الكنترولر (Controller Logic)</h2>";
echo "<div style='background-color: #f8f9fa; padding: 10px; border-left: 4px solid #28a745;'>";
echo "<p><strong>الصلاحية المطلوبة:</strong> 'manage chart of account'</p>";
echo "<p><strong>منطق الكنترولر:</strong></p>";
echo "<ol>";
echo "<li>فحص صلاحية المستخدم</li>";
echo "<li>تحديد نطاق التاريخ (افتراضي: بداية السنة إلى اليوم)</li>";
echo "<li>جلب أنواع الحسابات للمستخدم الحالي</li>";
echo "<li>جلب الحسابات المرتبطة بهذه الأنواع</li>";
echo "<li>تجميع الحسابات حسب النوع</li>";
echo "<li>إرسال البيانات إلى العرض</li>";
echo "</ol>";
echo "</div>";

echo "<h2>3. فحص قاعدة البيانات</h2>";

// فحص المستخدمين
echo "<h3>أ. المستخدمين في النظام</h3>";
try {
    $stmt = $pdo->query("SELECT id, name, email, created_by FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>منشئ بواسطة</th><th>Creator ID</th></tr>";
    foreach ($users as $user) {
        $creatorId = $user['created_by'] ?? $user['id'];
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['created_by']}</td>";
        echo "<td style='background-color: #ffffcc;'><strong>$creatorId</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في جلب المستخدمين: " . $e->getMessage() . "</p>";
}

// فحص أنواع الحسابات لكل مستخدم
echo "<h3>ب. أنواع الحسابات حسب المستخدم</h3>";
try {
    foreach ($users as $user) {
        $creatorId = $user['created_by'] ?? $user['id'];
        $stmt = $pdo->prepare("SELECT * FROM chart_of_account_types WHERE created_by = ?");
        $stmt->execute([$creatorId]);
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>المستخدم: {$user['name']} (Creator ID: $creatorId)</h4>";
        if (count($types) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 15px;'>";
            echo "<tr><th>ID</th><th>الاسم</th><th>منشئ بواسطة</th></tr>";
            foreach ($types as $type) {
                echo "<tr><td>{$type['id']}</td><td>{$type['name']}</td><td>{$type['created_by']}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>✗ لا توجد أنواع حسابات لهذا المستخدم</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص أنواع الحسابات: " . $e->getMessage() . "</p>";
}

// فحص الحسابات لكل مستخدم
echo "<h3>ج. الحسابات حسب المستخدم</h3>";
try {
    foreach ($users as $user) {
        $creatorId = $user['created_by'] ?? $user['id'];
        $stmt = $pdo->prepare("SELECT * FROM chart_of_accounts WHERE created_by = ? LIMIT 5");
        $stmt->execute([$creatorId]);
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>المستخدم: {$user['name']} (Creator ID: $creatorId)</h4>";
        if (count($accounts) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 15px;'>";
            echo "<tr><th>ID</th><th>الكود</th><th>الاسم</th><th>النوع</th><th>مفعل</th><th>منشئ بواسطة</th></tr>";
            foreach ($accounts as $account) {
                echo "<tr>";
                echo "<td>{$account['id']}</td>";
                echo "<td>{$account['code']}</td>";
                echo "<td>{$account['name']}</td>";
                echo "<td>{$account['type']}</td>";
                echo "<td>" . ($account['is_enabled'] ? 'نعم' : 'لا') . "</td>";
                echo "<td>{$account['created_by']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>✗ لا توجد حسابات لهذا المستخدم</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص الحسابات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. محاكاة منطق الكنترولر</h2>";
echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<p><strong>محاكاة ما يحدث في الكنترولر:</strong></p>";

// محاكاة منطق الكنترولر لكل مستخدم
foreach ($users as $user) {
    $creatorId = $user['created_by'] ?? $user['id'];
    echo "<h4>للمستخدم: {$user['name']} (Creator ID: $creatorId)</h4>";
    
    try {
        // الخطوة 1: جلب أنواع الحسابات
        $stmt = $pdo->prepare("SELECT * FROM chart_of_account_types WHERE created_by = ?");
        $stmt->execute([$creatorId]);
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>1. أنواع الحسابات الموجودة: " . count($types) . "</p>";
        
        if (count($types) > 0) {
            // الخطوة 2: جلب الحسابات
            $typeIds = array_column($types, 'id');
            $placeholders = str_repeat('?,', count($typeIds) - 1) . '?';
            $stmt = $pdo->prepare("SELECT * FROM chart_of_accounts WHERE type IN ($placeholders) AND created_by = ?");
            $stmt->execute(array_merge($typeIds, [$creatorId]));
            $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p>2. الحسابات الموجودة: " . count($accounts) . "</p>";
            
            // الخطوة 3: تجميع الحسابات حسب النوع
            $chartAccounts = [];
            foreach ($types as $type) {
                $typeName = $type['name'];
                $typeAccounts = array_filter($accounts, function($account) use ($type) {
                    return $account['type'] == $type['id'];
                });
                $chartAccounts[$typeName] = $typeAccounts;
                echo "<p>3. النوع '$typeName' يحتوي على " . count($typeAccounts) . " حساب</p>";
            }
            
            if (array_sum(array_map('count', $chartAccounts)) > 0) {
                echo "<p style='color: green;'><strong>✓ النتيجة: ستظهر البيانات في الصفحة</strong></p>";
            } else {
                echo "<p style='color: red;'><strong>✗ النتيجة: لن تظهر أي بيانات في الصفحة</strong></p>";
            }
        } else {
            echo "<p style='color: red;'><strong>✗ النتيجة: لا توجد أنواع حسابات، لن تظهر أي بيانات</strong></p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في المحاكاة: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}
echo "</div>";

echo "<h2>5. التوصيات والحلول</h2>";
echo "<div style='background-color: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>";
echo "<h3>إذا كانت الصفحة فارغة:</h3>";
echo "<ol>";
echo "<li><strong>تأكد من تسجيل الدخول:</strong> يجب أن تكون مسجل دخول</li>";
echo "<li><strong>تحقق من الصلاحيات:</strong> يجب أن تملك صلاحية 'manage chart of account'</li>";
echo "<li><strong>تحقق من البيانات:</strong> يجب أن تكون البيانات موجودة للمستخدم الصحيح</li>";
echo "<li><strong>تحقق من created_by:</strong> يجب أن تطابق قيمة created_by معرف المستخدم أو منشئه</li>";
echo "</ol>";

echo "<h3>خطوات الإصلاح:</h3>";
echo "<ol>";
echo "<li><a href='seed_chart_accounts_direct.php' style='color: #007bff;'>تشغيل إنشاء البيانات الأساسية</a></li>";
echo "<li><a href='http://localhost/chart-of-account' target='_blank' style='color: #007bff;'>اختبار الصفحة</a></li>";
echo "<li>إذا لم تعمل، تحقق من تسجيل الدخول والصلاحيات</li>";
echo "</ol>";
echo "</div>";
?>
