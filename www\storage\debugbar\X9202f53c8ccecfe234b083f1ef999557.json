{"__meta": {"id": "X9202f53c8ccecfe234b083f1ef999557", "datetime": "2025-06-07 04:14:56", "utime": **********.356261, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269694.988209, "end": **********.356293, "duration": 1.3680839538574219, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749269694.988209, "relative_start": 0, "end": **********.117689, "relative_end": **********.117689, "duration": 1.1294798851013184, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.117708, "relative_start": 1.1294989585876465, "end": **********.356296, "relative_end": 3.0994415283203125e-06, "duration": 0.2385880947113037, "duration_str": "239ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45697320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.260377, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.275941, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.333439, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.342896, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.026219999999999997, "accumulated_duration_str": "26.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2042282, "duration": 0.00575, "duration_str": "5.75ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 21.93}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.215532, "duration": 0.012119999999999999, "duration_str": "12.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 21.93, "width_percent": 46.224}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.234942, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 68.154, "width_percent": 4.805}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2618158, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 72.96, "width_percent": 4.31}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.277822, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 77.269, "width_percent": 4.386}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3070881, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 81.655, "width_percent": 5.301}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.316117, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 86.957, "width_percent": 4.195}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.324046, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 91.152, "width_percent": 4.272}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.3356102, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.423, "width_percent": 4.577}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HjXxoHcTyQnXHMNgWIK18Kup7wtTlguM2jElQVHa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-193258002 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-193258002\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-743828639 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-743828639\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-666608127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-666608127\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1591924949 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; XSRF-TOKEN=eyJpdiI6IkF1a0pBYUFBbnFtNHRnYWk2eVNXd2c9PSIsInZhbHVlIjoiV2dlRjRaODMvaG84dnNjb3AvWnUrNmF4MlBhMENWNG9xcVltM09tQ1NMelJXVjY1dmpMbUlEZWNTL0t3cnk3L0tDOW9WcUFlcVlLNjd3QTNtakVvMG1JUWhnYm54MDk4NU5xcUhrTTJXeWpiN1B3b2FpTDNPekhMNmh6S1RjZnU3enFRaXhMQzRRNGFBOG42bVpFbnoyeGhVQldXcVI0U2ZKYmZXUWZIU2c0aE5aeG1qblF1YXRucTR4NXFTbGUwMFcxQXIvWHJ0TDF1NjNZdGNlNmFmZHJTS21pbThHSzZEN2hIbnF6aVhyU2VjRTRaSzlxcU1ObjRJdG96MTEyZS9PZFhJOHlrdDlYMEgvK01GZTVrT2F5ZCtRblJURUdtblNXVTdkWHRud2tsWGprcjAvMHcrNjg4REZLTEFYVTdKTzliTHlIY0oyeWw0ejcyT25LVDNHSVU4L25naVVuM21tYk95RnllN1FIMGdPOUw0QnZtNWtFV21YYll6M1Z3c1VkcnU2dzJ3VHhiRjNpSExYZ2wrazhVbWpkQTNzMi9IMytCK2NVY3hpblVMVFk2SVU0Tk5VWEhaRElpcUpqTnMxb1ZtQ3MrMFFOQU81VHkvWjg5cms1RVNxRGlpTGEzTWU1YVZhOC9iaEdFSzJMenEvczV4VC85d29XM1hMZFgiLCJtYWMiOiIzN2UyMWRiMmU5NjZlNTY3NDliMGIxMDg2ODVhODJhYTI0ZDA3N2M3NzQ4ZjE5MDlkYTk3MjBiN2VkMjM0ZDhmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkkwT1ozbmVrUVJuVnJKb1hoYW82SXc9PSIsInZhbHVlIjoiSzZHSjVVRGIvSDdGc3EzNmtxQnBSdkhKSlhXVjBuT1o1RmlUQXFYQTNNenJnOTVvOVJ6V1pYUnJoNHlHaDdYL3JBZHd6ZysvamgzeDdycGtFbFR6NlI1bmIvb1ZrV1IrVFhZOSsvT3Z6b25IdjdvY2NXY21HaHJXQkl0WDVWQjdxdHM4Tm9MRlVoWE1Zd2JNSzJpaFZpemVjZjZJMjc2R2lhcjVhMXJWbk5wRUdsLzI2TWxhOXBGcHMzT1ZhQ1Q2VkVoYXZNbW9XNlNsYWZkVEp1bE1ERnRkS3phbmF2UWJxQWM1d3Z3MW40L1RvaG9vcCtNcm5yMTdOVHRSVUJ3aVRadWt6WHc3Q28vL1RSbk8wZzZ3anh4cUUyR1BKYWx1VGYwZTlFVm13K0pBbzd4bktYeDNCa0RhbWhMNXVLNkE4SFNvekVEU1dEMDdrOThiYm92NzRUOXBJcjRpTCtQd0NYRTdLZ1FTZ1o3cG1URFhsUHFYemg1T2RTZ0xJTkwxMXE0Mm5uTjRrWXhNMjFtL0NvNW5TWFRUMktzME5vSjhVVmpqaXMvU2pzTkJrajVnN2lCTnVJUXpXbmpkLzhNaXUzVWVYYVRMT2ExNkMrU3JyVHRIOEkrVkpjWkE5UHBnT2JCWjVQWGU0dEJnamhwNG0zRndQbGJKVjB4cjIwdlAiLCJtYWMiOiIwNzI4MWQ3MTIyZDRjODc4ZDAzZDk5MTdjOGFkM2ZkNTVhODMyNDdjYjFmZjgzMjQ1ZDNjNzhhZjEzMDIxNzc5IiwidGFnIjoiIn0%3D; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269684503%7C1%7C1%7Cs.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591924949\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1277700266 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HjXxoHcTyQnXHMNgWIK18Kup7wtTlguM2jElQVHa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4gyzHSRcZmygV63EDHIuIrwnK5l2jBXuJ4M26Vli</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277700266\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1519828284 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:14:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjcxMW84di93YXhHVTl2b3Q0Z0JCbUE9PSIsInZhbHVlIjoiMkF1SVIvWXY2QW5UMk5TTUl0NEhpN1VvQ2xWWWxnRHBYZUxmajIvUUFuNHpnQ0tVRE84RlV5MG9UTks2dkc0WFZ3LzF4Sndsb1IxWXlJNXZuSDdJNnJaL3ZIM2FUbjFHR3I2c2hya0pzTW9wN0RKK2JkbHpoU2NOWkFVY29BNmZ4a1IweTNKWGFXUTk4VzJGRXVKUUtPOTNGWmxua0FQb2p3V1RBK0pZclVQdUhwSllhZm9sMUI2OTgzL3R6cHFUK09SbFd5TCtSaWRXL0x1NmlvQmIrdEZOUXRFbkRwMndjemF5VjdBZitVdHNsSHRjOThEM3lUYzlkcVpCSy8xMG1UemJicmJ6cTdST0FXNVRRSE5oeDFvdU91Q1VhbDYwQmg0SlMwTEgxelNzTE92ZlI4MUpXTUNPREFIN0l1S1ZEdEVaaE5uMzRSdmJZWFhVZjh1MmNidStlMGNmb3RjQkFSUldXT281VjR6T3lyREhqeWI4Wi93Z0w3OENxMUJJdUxmYkdPTjJIaExwaWdLOHZjM2V0VmtiZkJNQlRlb29HZG9CTlZ1ck5TUVN6RGNWTHZoS2JZSXI4SmlRaXN2YUpudWpNWE9udWxTZDJiNmlicVdJZkNOVUpQTlZKOFZ5eVdSNUliZnJCdEpJYXNLbkpJUFphUGR1NW81ZTVSL3UiLCJtYWMiOiIwNzJjYWY1ZGRkOTA5NDQzZTYyZGUyMzNmNDJhNDAxMGU0OTEzODNkMzE3NmE5YjI3OWYzZTZlZDA1ZGIwMDRhIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:14:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNlRCsxSUwvTVc0bVBSSUZTcDNwMHc9PSIsInZhbHVlIjoiODRSd2hobllEZ0ZpVVFBMWR4Q3JZK2RMVWlrVTBIYmx5UHRHU0s2UGZGLzJBTHozTm1yaGVwei9VdHQ5VlFFTm1oc3c1ZkVxUmJGWGhzZzhwcjkrVEI1Q2RCcGdyRDQxOW1VMVFlNHRYWlBGWmhJM3RGeGIxcFErbXNVMG9oaWNXR2Y2WEdncDVySVFzNHRBcFRmSnJqV0NHRlJWbHhnbytxT3E4eTZVRWUyWXNJZ0JJUEd1MmI1eU5HcTEyV3lYb0RCcDZCTWYyb1p5eHBFNTZIMFc1OUkxQ1hpM1JCUUw3cGQwZ3A1ZENJWmwvWDJBNVluZjNGT01sSzhOWktPSWk5eHVFSVZjaDJnaW8yckp4cHdRaGZlMHpyc0IwN3FQaEdEdFZtS0o5TS85WXRwVlgvM29GajRYWENKSXUyMmlNb1I2L2FxV21wRnZuYkk5UmwvVGdmUFZGeVB5SDhOM2w3RXBBNldaR3U2djE1dGRGLzd6eFgyRGtpdzl6cGYxQWxFaVdhelhBZXJDSGF2T3NLNWEzdlVOUFdlS2FTYmgwdmRRc2lYVWwwSWNCOEU4VmlvbVkyZXkrVVovbHB5R3FtTGRRV1ZRcCtJNUh0TjdtTGx2RFZHWmVkV1ZlK0xQd3VWdUhVdHpZck9tYVFJSFlWNkpPTlZXL29KNjBxbkEiLCJtYWMiOiIzYWEyOGU2Mzc0ODczNGU0MDNkYmYxYzA0MTNmZDQwZTJmNTA3NjgzYTg5YmI1ODAwYTQ4ZDVhMWNjNWEzODA3IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:14:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjcxMW84di93YXhHVTl2b3Q0Z0JCbUE9PSIsInZhbHVlIjoiMkF1SVIvWXY2QW5UMk5TTUl0NEhpN1VvQ2xWWWxnRHBYZUxmajIvUUFuNHpnQ0tVRE84RlV5MG9UTks2dkc0WFZ3LzF4Sndsb1IxWXlJNXZuSDdJNnJaL3ZIM2FUbjFHR3I2c2hya0pzTW9wN0RKK2JkbHpoU2NOWkFVY29BNmZ4a1IweTNKWGFXUTk4VzJGRXVKUUtPOTNGWmxua0FQb2p3V1RBK0pZclVQdUhwSllhZm9sMUI2OTgzL3R6cHFUK09SbFd5TCtSaWRXL0x1NmlvQmIrdEZOUXRFbkRwMndjemF5VjdBZitVdHNsSHRjOThEM3lUYzlkcVpCSy8xMG1UemJicmJ6cTdST0FXNVRRSE5oeDFvdU91Q1VhbDYwQmg0SlMwTEgxelNzTE92ZlI4MUpXTUNPREFIN0l1S1ZEdEVaaE5uMzRSdmJZWFhVZjh1MmNidStlMGNmb3RjQkFSUldXT281VjR6T3lyREhqeWI4Wi93Z0w3OENxMUJJdUxmYkdPTjJIaExwaWdLOHZjM2V0VmtiZkJNQlRlb29HZG9CTlZ1ck5TUVN6RGNWTHZoS2JZSXI4SmlRaXN2YUpudWpNWE9udWxTZDJiNmlicVdJZkNOVUpQTlZKOFZ5eVdSNUliZnJCdEpJYXNLbkpJUFphUGR1NW81ZTVSL3UiLCJtYWMiOiIwNzJjYWY1ZGRkOTA5NDQzZTYyZGUyMzNmNDJhNDAxMGU0OTEzODNkMzE3NmE5YjI3OWYzZTZlZDA1ZGIwMDRhIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:14:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNlRCsxSUwvTVc0bVBSSUZTcDNwMHc9PSIsInZhbHVlIjoiODRSd2hobllEZ0ZpVVFBMWR4Q3JZK2RMVWlrVTBIYmx5UHRHU0s2UGZGLzJBTHozTm1yaGVwei9VdHQ5VlFFTm1oc3c1ZkVxUmJGWGhzZzhwcjkrVEI1Q2RCcGdyRDQxOW1VMVFlNHRYWlBGWmhJM3RGeGIxcFErbXNVMG9oaWNXR2Y2WEdncDVySVFzNHRBcFRmSnJqV0NHRlJWbHhnbytxT3E4eTZVRWUyWXNJZ0JJUEd1MmI1eU5HcTEyV3lYb0RCcDZCTWYyb1p5eHBFNTZIMFc1OUkxQ1hpM1JCUUw3cGQwZ3A1ZENJWmwvWDJBNVluZjNGT01sSzhOWktPSWk5eHVFSVZjaDJnaW8yckp4cHdRaGZlMHpyc0IwN3FQaEdEdFZtS0o5TS85WXRwVlgvM29GajRYWENKSXUyMmlNb1I2L2FxV21wRnZuYkk5UmwvVGdmUFZGeVB5SDhOM2w3RXBBNldaR3U2djE1dGRGLzd6eFgyRGtpdzl6cGYxQWxFaVdhelhBZXJDSGF2T3NLNWEzdlVOUFdlS2FTYmgwdmRRc2lYVWwwSWNCOEU4VmlvbVkyZXkrVVovbHB5R3FtTGRRV1ZRcCtJNUh0TjdtTGx2RFZHWmVkV1ZlK0xQd3VWdUhVdHpZck9tYVFJSFlWNkpPTlZXL29KNjBxbkEiLCJtYWMiOiIzYWEyOGU2Mzc0ODczNGU0MDNkYmYxYzA0MTNmZDQwZTJmNTA3NjgzYTg5YmI1ODAwYTQ4ZDVhMWNjNWEzODA3IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:14:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519828284\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-294441894 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HjXxoHcTyQnXHMNgWIK18Kup7wtTlguM2jElQVHa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294441894\", {\"maxDepth\":0})</script>\n"}}