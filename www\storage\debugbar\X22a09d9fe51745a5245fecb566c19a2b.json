{"__meta": {"id": "X22a09d9fe51745a5245fecb566c19a2b", "datetime": "2025-06-06 19:19:48", "utime": **********.748389, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237587.276055, "end": **********.748417, "duration": 1.4723618030548096, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1749237587.276055, "relative_start": 0, "end": **********.561217, "relative_end": **********.561217, "duration": 1.2851619720458984, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.561236, "relative_start": 1.2851808071136475, "end": **********.74842, "relative_end": 3.0994415283203125e-06, "duration": 0.18718409538269043, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44755096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02902, "accumulated_duration_str": "29.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.657497, "duration": 0.02721, "duration_str": "27.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.763}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.711894, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.763, "width_percent": 2.998}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.728159, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.761, "width_percent": 3.239}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-579268544 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-579268544\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-237143740 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-237143740\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-242694089 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242694089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-495870308 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237522217%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJieWFIbGFBeVJLbDlJUHR1MUpCaUE9PSIsInZhbHVlIjoiam9wNFNVeVJ6NzRueGZnL1lQSjJGNVdXSC9GT1YwcTcxS29jUTZuNU5ueDM4NElBcmtEalF3akk0dkpkandNUlR3MXZZdTJDKzZGVENHTHIvZ1lxVFoxcXlKZjVPVUVlbVRrWkhidW1ZZ0RwWTZEWkNGTkI3Mll0QVpIUVVXVllpb1RQSlNDQ1hmeTJ6Z24xMytFeHNDazFscXAxZHRuZGR4K28zN2NMOHNMSTl2SVF6aFFGRGRyckY0VDRpZ09hM0ZoeUNiVDZPK3RxUVJhVlZhTEFSRGFRNzhUTkdZSkV6VzdoTHFxbklHRGU0cEVOOUlIdkhUK3c5bXZBU3pjeitOd2dnNzZaRUZYSzRNRGc2WStvVkc3ZHRIRGdzTTRWcTNmQzM0R29CRWUxdFQ1YkRpYk1yRXpjSnBrUmlEU1R4RlZQTU5YL0x3QVRMcFlYcGhSMEpxMTZNTW9hMmdCSkU0MFFqcnZEOTJaK1RPRHZLMGdEekF5Q2JiaVZkeko0cG5BdzZKenJHUVA2TDBDMnJEaVRoZ3I4ZGlFdGhiRDIrSWdxcjZFVWdvVVgvOUZub2JOZXVCM2tSd1hoTU54TXo1UnhsZXZJRGYyRVJNUGVKaFBaRk1kbDk4N1BaaEVsVXo5N1Q3M3N2U0dVcS94eUJLdFNld2t5dkNjNUdLaFkiLCJtYWMiOiI1Y2I2YmI3Y2U4Nzg5YzNlZmQ1MzY3NWE4NmU4NDJlNGRiOWM1ZWFjNWU3ZTMwY2NhZDUyYzBmMzZhNjViZmJhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkY0NWg1ZlVvaVoyK0EwdzY3Z2ExL2c9PSIsInZhbHVlIjoiUnRFRWFwTDhkemFoSEhIMExQRTBTb3htQmpBdkRYQkdaTk1ZTlgwemIzYXoxQTVWdnRncXZBWlVUZjFBK3ovVXhHaUJoVzF5alRsRzd1MnRkMVlaQzlSZTZiaVNWVWdONTlKWHhyWFlpYVZCR1M0QXV1Vk84NG54SEd6N3FZNG5qODc0TXJWOHlsK2x5WUNoSkNBT09BWXpnUE5RMzdpMDdiSm1UR1IrWDFyVUNQMFRyUXBPb0s3ejBmZUpaZzZiYXdzdE5rNjAyZ2syMWovZVI1c1FPdU41MHN5cVhOdlRQTnVkby9UbGp4c2MvQUJ1U1ZYamtSdCtTc1dkWGdDWFJscW9WYkhhUVhMRHlEaWp3NmRBeDQwREJTekVXamxNOEtESnBTbUg3Z3EvSHNPSmdmU1VaeDlzNmVoZUNRd2p2NzlSamxzdlBZYldLQVdaTmREcHVTLzVpa0RRR1hvN3AvVlZXT014V0U5cVdaVDdGUlVkQjIwRjZhZXhhTmVZTXEwN3lUM0Q1dXlVaVJlV05YT0VrNXhhS2FXbzRJS3p2b3pKc0w3N2pQY25FUU9RYnpiWUlaRGErRWloMUFUOHllSmFudms5bWJhOThVRG9ac0cweVZHL3BtUEUxOFVxM0F5S2hJcFhFREt2YTlSRDlBK2ZzWXM1ejVUclRPV2ciLCJtYWMiOiI1YmI4ZGI2NTEwNjhlZjRkZjNhYTdhNjQ5Y2M2MDg1OGFiMTAzODFiMzg2NTJmN2Q2MDE4MDRkOGQ4NjBhZjNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495870308\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:19:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNoNVpKUllxczZhMnlZNDFET0ZJK1E9PSIsInZhbHVlIjoidmtURFhSSERFOVZLNCtmZ096bHp5UklpaG9lM3NYUWFEZmdZc0ZxZ2NRS1I1bWhCeFRrT0RhcXRnSEIycnJkRGxyV29XeVh0QUhoOGprbm1aWmk5QjJmbEIzd09uc0RVckpNVVRvY1Yza1dIemU2SkNYdGtVdFJJL2VnZDZFU21EMi85U2x1eTZwcDY5OXowZ0JRMmNwWHo1bG1lY00yNzUwdk5SbWZxVlk3MEwvOEZGcWxKdlpaRWJqVnA3by8ralNtLzVwZVAxa1hsTE1XRE40SnVlVG9sM3pvd3BuNXV5ckVjVnYvcytxUkI4RThZeEtFdWp0bVdMVUo0d1k0WG5UQzFiSldPSjZQMDQzK1dlQURRL3MzbzJIYzRtbzhweWptUWMzK3dZWkFFZ2FHdENCWjVTNUNPV29oTGJ0UkNVY3Q2OEdmZHl3ZXRBbEFFa2RHdVJTTFZmT1Z1Q05Yb2NmN0lCOU9wUTFwR1Zna2QrUHlma3F5Z2htN3JET25tdFo1TUc5aVdqbTQvb3ZxR3pYV0pkcXBucUFEbDc5RlZoWmpCMjlQbW0wckpzaCtmL0VsSnVBa0p2eGplZ1RManNWTWZzYTRKS2tzSllCdEg2amtYdHZIdUNpVE5ldU5sSER1R1JRNXcvbCtJS01QYjdsMEZjUXpGNXNXbGdqQXUiLCJtYWMiOiI0ODJhNWFlOTNiZDJmMGZhMWY5YzM3OTI0YTYxY2VjYjFiMDNkOGMwYzIzOWUzMDMxOWM3MzQxMWI3YTJkMDg3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:19:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdDekxkb3ZRcVJYd09CZi9iemczUmc9PSIsInZhbHVlIjoibS9uU3FDd2tMa2ZwODB5UEg1TXBjK09IcnQ2WXU4d3NZbHJGbkUraFpuNHpYWCtrWnE1VnJVa1dBNXBSWHZ1NFJVRGFoWUw3L1RCeGlmUUtPbDYyWmU4UktRdVJ2TjVWNUVSQUV4aGRvMWVLV0lTL1dYNys5bUVRVHR6WHQ2bkNuRGpuYUhKQW9TcnFoNjdMclgvbHhlbStzclE5cnk4MFFxcmlmQzBzci9Lb2o1UnZsb3NEdmhsb0xmMm9GaHk4TjJnVC93V01SOThGbElFSjRNM3Y1NXlOMzgvM2w1V2FWSCtndEpjTGNkbWxnc3I0WjhMVllFYlpBelhsMFRXSit5aVZzdDFGOUxkYW1vOHFGMGVuYnhoUW9iNiswcjZ6WnkxWjBHdUhsWjlIaGFSY0hlV29xYkVVdUVmcWwxRXZSN2NuRE1yZURvNmhzUmdVN2lZWllFRjVZMzlnRVBjVDZkUGlDaXNDTnV2OHpJTGd4VWpVNUVPaFAvK2NjenRXQjhIcUx6V3VzL1oyQlF4VW5lNk4rUFlhZWovNjkybkx0V3d0T1ZVczBGSXQ3Sk9BVHlzQUt6TXF6NXA3cldUWXhPdjBBcExpS3RWNFdlc0tyRU40WnRaZ1I0UjRpcHVuc01vZXA1VmJhM2NsaXViQkdCbmZwSHRJbDMzeU92NVoiLCJtYWMiOiI5MWM2ZWJmMTAxYTRjMWFkYTZhYTEwYjY2ZWU5YmUwMzYyZGY3ZjdmZDRhNjRhOGJhYjA1YWU0ZWQ5ZjBlZTZmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:19:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNoNVpKUllxczZhMnlZNDFET0ZJK1E9PSIsInZhbHVlIjoidmtURFhSSERFOVZLNCtmZ096bHp5UklpaG9lM3NYUWFEZmdZc0ZxZ2NRS1I1bWhCeFRrT0RhcXRnSEIycnJkRGxyV29XeVh0QUhoOGprbm1aWmk5QjJmbEIzd09uc0RVckpNVVRvY1Yza1dIemU2SkNYdGtVdFJJL2VnZDZFU21EMi85U2x1eTZwcDY5OXowZ0JRMmNwWHo1bG1lY00yNzUwdk5SbWZxVlk3MEwvOEZGcWxKdlpaRWJqVnA3by8ralNtLzVwZVAxa1hsTE1XRE40SnVlVG9sM3pvd3BuNXV5ckVjVnYvcytxUkI4RThZeEtFdWp0bVdMVUo0d1k0WG5UQzFiSldPSjZQMDQzK1dlQURRL3MzbzJIYzRtbzhweWptUWMzK3dZWkFFZ2FHdENCWjVTNUNPV29oTGJ0UkNVY3Q2OEdmZHl3ZXRBbEFFa2RHdVJTTFZmT1Z1Q05Yb2NmN0lCOU9wUTFwR1Zna2QrUHlma3F5Z2htN3JET25tdFo1TUc5aVdqbTQvb3ZxR3pYV0pkcXBucUFEbDc5RlZoWmpCMjlQbW0wckpzaCtmL0VsSnVBa0p2eGplZ1RManNWTWZzYTRKS2tzSllCdEg2amtYdHZIdUNpVE5ldU5sSER1R1JRNXcvbCtJS01QYjdsMEZjUXpGNXNXbGdqQXUiLCJtYWMiOiI0ODJhNWFlOTNiZDJmMGZhMWY5YzM3OTI0YTYxY2VjYjFiMDNkOGMwYzIzOWUzMDMxOWM3MzQxMWI3YTJkMDg3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:19:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdDekxkb3ZRcVJYd09CZi9iemczUmc9PSIsInZhbHVlIjoibS9uU3FDd2tMa2ZwODB5UEg1TXBjK09IcnQ2WXU4d3NZbHJGbkUraFpuNHpYWCtrWnE1VnJVa1dBNXBSWHZ1NFJVRGFoWUw3L1RCeGlmUUtPbDYyWmU4UktRdVJ2TjVWNUVSQUV4aGRvMWVLV0lTL1dYNys5bUVRVHR6WHQ2bkNuRGpuYUhKQW9TcnFoNjdMclgvbHhlbStzclE5cnk4MFFxcmlmQzBzci9Lb2o1UnZsb3NEdmhsb0xmMm9GaHk4TjJnVC93V01SOThGbElFSjRNM3Y1NXlOMzgvM2w1V2FWSCtndEpjTGNkbWxnc3I0WjhMVllFYlpBelhsMFRXSit5aVZzdDFGOUxkYW1vOHFGMGVuYnhoUW9iNiswcjZ6WnkxWjBHdUhsWjlIaGFSY0hlV29xYkVVdUVmcWwxRXZSN2NuRE1yZURvNmhzUmdVN2lZWllFRjVZMzlnRVBjVDZkUGlDaXNDTnV2OHpJTGd4VWpVNUVPaFAvK2NjenRXQjhIcUx6V3VzL1oyQlF4VW5lNk4rUFlhZWovNjkybkx0V3d0T1ZVczBGSXQ3Sk9BVHlzQUt6TXF6NXA3cldUWXhPdjBBcExpS3RWNFdlc0tyRU40WnRaZ1I0UjRpcHVuc01vZXA1VmJhM2NsaXViQkdCbmZwSHRJbDMzeU92NVoiLCJtYWMiOiI5MWM2ZWJmMTAxYTRjMWFkYTZhYTEwYjY2ZWU5YmUwMzYyZGY3ZjdmZDRhNjRhOGJhYjA1YWU0ZWQ5ZjBlZTZmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:19:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-87896084 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87896084\", {\"maxDepth\":0})</script>\n"}}