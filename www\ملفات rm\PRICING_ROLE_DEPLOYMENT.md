# 🎯 دليل نشر قسم التسعير مع دور Pricing

## 📋 ملخص التحديثات

تم إنشاء قسم جديد اسمه **"التسعير"** يظهر **فقط** للمستخدمين الذين يحملون دور **"Pricing"** حصرياً، وتختفي جميع الأقسام الأخرى من القائمة الجانبية لهؤلاء المستخدمين.

## 🔥 **الميزة الجديدة: عرض قسم التسعير فقط**

- المستخدمون الذين لديهم دور **"Pricing"** فقط سيرون قسم **"التسعير"** فقط في القائمة الجانبية
- جميع الأقسام الأخرى (Dashboard، HRM، Accounting، إلخ) ستختفي تماماً
- المستخدمون الآخرون سيرون القائمة العادية كما هي

## 🚀 الملفات المحدثة

### 1. ملفات قاعدة البيانات
```
database/seeders/UsersTableSeeder.php (محدث)
database/seeders/PricingRoleSeeder.php (جديد)
database/migrations/2024_01_15_000001_add_pricing_permissions.php (جديد)
```

### 2. ملفات التحكم
```
app/Http/Controllers/PricingController.php (محدث)
```

### 3. ملفات العرض
```
resources/views/partials/admin/menu.blade.php (محدث)
```

## 🔧 خطوات النشر

### المرحلة 1: رفع الملفات
1. رفع جميع الملفات المحدثة والجديدة إلى الخادم
2. التأكد من صحة المسارات

### المرحلة 2: تشغيل Migration
```bash
php artisan migrate
```

### المرحلة 3: تشغيل Seeder (اختياري)
```bash
php artisan db:seed --class=PricingRoleSeeder
```

### المرحلة 4: مسح Cache
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

## 🎭 الأدوار والصلاحيات الجديدة

### دور "Pricing"
- **الاسم**: Pricing
- **الوصف**: مدير التسعير
- **الصلاحيات**:
  - `manage pricing`
  - `show pricing`
  - `edit pricing`
  - `manage product & service`
  - `edit product & service`
  - `show product & service`

### الصلاحيات الجديدة
1. **manage pricing** - إدارة التسعير
2. **show pricing** - عرض التسعير
3. **edit pricing** - تعديل التسعير

## 👤 المستخدم التجريبي

تم إنشاء مستخدم تجريبي للاختبار:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 1234
- **الدور**: Pricing

## 🔍 كيفية الوصول للقسم

### 🎯 **للمستخدمين الذين لديهم دور "Pricing" فقط:**
1. تسجيل الدخول بحساب له دور "Pricing" فقط
2. ستظهر **قسم "التسعير" فقط** في القائمة الجانبية
3. **جميع الأقسام الأخرى ستختفي تماماً**
4. النقر على قسم التسعير للوصول لصفحة التسعير

### 👥 **للمستخدمين الذين لديهم أدوار متعددة (مثل Company + Pricing):**
- سيرون القائمة العادية الكاملة
- سيظهر قسم التسعير ضمن الأقسام الأخرى

### 🚫 **للمستخدمين الآخرين (بدون دور Pricing):**
- لن تظهر أيقونة "التسعير" في القائمة الجانبية
- في حالة محاولة الوصول المباشر، سيتم رفض الوصول

## 📊 **مقارنة السلوك**

| نوع المستخدم | الأدوار | ما يظهر في القائمة |
|-------------|---------|-------------------|
| **مستخدم التسعير** | Pricing فقط | قسم التسعير فقط |
| **مدير الشركة** | Company | جميع الأقسام |
| **مدير مختلط** | Company + Pricing | جميع الأقسام |
| **محاسب** | Accountant | أقسام المحاسبة |
| **مستخدم عادي** | بدون Pricing | الأقسام العادية |

## 🎨 تصميم القسم

### الأيقونة
- **الرمز**: `ti ti-currency-dollar`
- **اللون**: حسب تصميم النظام
- **الموقع**: بعد قسم "نظام المراسلات بالشركة"

### الرابط
```
/pricing
```

## 🔐 الأمان

### التحقق من الصلاحيات
```php
// في PricingController
if (!Auth::user()->hasRole('Pricing') && !Auth::user()->can('manage pricing')) {
    return redirect()->back()->with('error', __('Permission denied.'));
}
```

### في القائمة الجانبية
```php
@if($isPricing || Auth::user()->can('manage pricing'))
    <!-- قسم التسعير -->
@endif
```

## 🧪 اختبار النشر

### 1. اختبار الوصول
- تسجيل الدخول بحساب <EMAIL>
- التأكد من ظهور قسم "التسعير"
- النقر والتأكد من الوصول للصفحة

### 2. اختبار الأمان
- تسجيل الدخول بحساب آخر (غير Pricing)
- التأكد من عدم ظهور قسم "التسعير"
- محاولة الوصول المباشر `/pricing` والتأكد من الرفض

### 3. اختبار الوظائف
- التأكد من عمل جميع وظائف التسعير
- اختبار التعديل المباشر
- اختبار البحث والفلترة

## 🚨 استكشاف الأخطاء

### خطأ: "Permission denied"
**الحل**: التأكد من أن المستخدم له دور "Pricing" أو صلاحية "manage pricing"

### خطأ: لا يظهر قسم التسعير
**الحل**: 
1. مسح cache: `php artisan cache:clear`
2. التأكد من تشغيل migration
3. التأكد من وجود المتغير `$isPricing` في menu.blade.php

### خطأ: "Route not found"
**الحل**: 
1. التأكد من وجود routes في web.php
2. تشغيل: `php artisan route:clear`

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من أخذ نسخة احتياطية قبل النشر
2. **البيئة**: اختبر في بيئة التطوير أولاً
3. **الصلاحيات**: تأكد من وجود صلاحيات المنتجات قبل تشغيل Migration
4. **المستخدمين**: يمكن إنشاء مستخدمين جدد بدور "Pricing" من لوحة إدارة الأدوار

## ✅ قائمة التحقق النهائية

- [ ] رفع جميع الملفات
- [ ] تشغيل `php artisan migrate`
- [ ] تشغيل `php artisan db:seed --class=PricingRoleSeeder` (اختياري)
- [ ] مسح جميع أنواع Cache
- [ ] اختبار الوصول بحساب Pricing
- [ ] اختبار الأمان بحساب آخر
- [ ] اختبار جميع وظائف التسعير

## 🎉 النتيجة المتوقعة

بعد النشر الناجح:
- سيظهر قسم "التسعير" للمستخدمين الذين لديهم دور "Pricing"
- لن يظهر القسم للمستخدمين الآخرين
- ستعمل جميع وظائف التسعير بشكل طبيعي
- سيتم حماية الوصول بنظام الصلاحيات
