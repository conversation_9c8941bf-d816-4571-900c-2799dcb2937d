{"__meta": {"id": "X0cf5098c17c515755c703e674368f560", "datetime": "2025-06-07 04:19:11", "utime": **********.352017, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269950.009028, "end": **********.352049, "duration": 1.3430211544036865, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749269950.009028, "relative_start": 0, "end": **********.146045, "relative_end": **********.146045, "duration": 1.137017011642456, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.146068, "relative_start": 1.137040138244629, "end": **********.352052, "relative_end": 2.86102294921875e-06, "duration": 0.20598387718200684, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44768064, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03047, "accumulated_duration_str": "30.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.262763, "duration": 0.02836, "duration_str": "28.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.075}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.314595, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.075, "width_percent": 3.085}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.331579, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.16, "width_percent": 3.84}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1591153233 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1591153233\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-12468569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-12468569\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2042591140 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042591140\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-325208150 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269926377%7C7%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InArTmptWEk2OTVGa3p4dloydklVcGc9PSIsInZhbHVlIjoieE4vamxJSS9Md3hEQmFFcFFLUURHRnM2My9lSWc0blJwOWMwdDltMlV5bFM5Zk5Wd1pwc1Zib2creVhRVzRIR2xST0JMV1h5ZG1QY0pGTGJ3dVJtZE9yZEFyUDIrQ3JRWHdCTTdZak1ka09LNDlpK0JmMHdtSGdWT1VNbHFBa0t1bEgrVlZwTkJ5SGxHdGN3UVplMzkyY0VIdGxFQ2M4eGovdDBpSm5oM2thYmFpbDFLbUJzeXdlcDJZeHdjV2FDM0NNQzVvZGtPK2ZpUEtYSW5PNkxTKzFlY25XY2JlTU1qRThRNG13bjJncjMwTTVxZjdWN1JYbk9IK0pMb1VuMFZyQzRTRFR3aVJSV0s2MExQY3J2eHBqK0g4bUJzTkxES3ZtUGl4dkJmbDhvUG8zUjZPTkFsV1FMYU43eUxTZzR2aTJ4RiszNVVRY1lCT3pJRlJYV2pTYUtLeXZ1MlFDSnNNNDRsVFFPL3plRUEweksyVndleGZxaEhFaGlRSkF1elVqUkR2cDU0eU0veHF1d0ZMSEFIc1hxWVptL0d3ekF0b1gydFU2ZEZPNmpnb0h2cmFiSEYvTFlmdXJoaWtWWVJvcGdIRStoeEM2d2hkVWdUTEFxS0pudW1YZ1BpcHY3ZGppSTVwQnJxcHpDUXZWVEs3SzF5ZWNKMGdNZStteWIiLCJtYWMiOiI0NWZhNmZlMGY0OTRmMzg5MmE5ZmZjZTc3YTM5MzYzYzExODNkZmM3Nzk3ZjQyMjAyYTA0MzhjMzE5OTZhMzY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlhoQlExQ2g3NjJlTnU0NjFLRHRWekE9PSIsInZhbHVlIjoiR2x1NlFoOUEyT3oxU3JxMC9tMFJqYjl0MXNnTkU0WlZudDJIK3ZxZlNmY0F5OFRhQkpxMzZUdTNMckRzMlYxV1FncTBKS3ByUTRtQ0dwWG5WcGtpVUdkVEVta3p3NjlFT0dGN0c4MVkrUzI4TE93dGphK2hHQVZjWGdCOWo4Wmpjb0lrQmRjc3hyVGpxWlhjdC9QcjdQWnlwczd4V2F3bFUzWVltNDlZWFIvc1BFbC8yT2E5Wm9FNDhuSStiRXhuL0sxcEtZU0lIOGxMZHZDbWN5WmFaVTZNVkJXZjRvNXVkSnFFVnRieExaWmpieUxtc2ZxQ1RUU3p5UHU3eHI4dTY4Z3J5MExPblJRNDVIaVloVHNVL2xvbWxsUS9zMnVoNlgrZVh0ZWV4cVF0cmpUNkZhd3pBWWM4NmRGYkZJZ0VmTWg4Y1I2b2JQS2htSlhWbjFndGkrcWZ0aDRiTkJQSnlxdGlMdk1MV2JPdE8yck9ubmlxSUZVOTVhQkNzRVhWYWszbVg5b1VxYWVJOERZZGtacXAxdTJ4Vm5oeFArWFJqbXMrT1U2K1BEcUF3ZDZQSkZ4eTBLZlhvZEtLM3k3REJwWHRwakROY05XTVlXRUNvM2NJeDRDZ04xYTgyRXZDS2pNT3NWZ1lzVmZBZ094OHpzZVVVTU56YVdldWluSXEiLCJtYWMiOiI0M2I1YzBiMjhjNTE2NGFjNjM2NDU1Y2NmMTUwNTdhMmVhYWMxYmVlYzYxNjk2ZjE3NTgxOGM5ODMwNGI0MjJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325208150\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1487390920 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QF4lxftbqb9bEiJQUWBNsJwytQRufosJhQtAJ0pY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487390920\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1612367404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:19:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijc0MkRzOVN3ZWFiOUFtaXpkdnR5ZkE9PSIsInZhbHVlIjoibVpxRDJpWlFzdkJmM01saXBUaUU1NUlPSVArbmorbFF5OTV2NGZDK2lDNmxYaVQ2dkxKdVUwZ3ZJc0ZxYjdrSnRQYUNqZFltN00xTkFteWpLOVhZVDZ5RER1RTJUV05xSlVPNFoyOFRmUHVTdjd3RE52Y05YZ1h5dW9rL1JiY2sza1RkQ0JqNlNLZHFneTY4ZVd6Sy9raDRsS2Q3V2hQZ1kxR2Y0V2xzYXcrRGlyWUhCazMzL2NvMVF0aExrZlpLQTNVWi9WRU9oZ1V2eGlxUWsybE45T3ZwVExnYzc0QlpOcGlJSzNIeEJIRHdsRC8vUmZLeHg1UnlwNHg2OW5JZWkyU2k3UGZOWWtEUTBrZXJRYXBiMjJHWnBleWwya3BNTlRHaElaQzZQc3J5QVBvTGZWdWRtMHFUd05wSzRlQUxlMjJFWWdpRjJqVEJrWmJlUGNlSXRqcC9EZTFhdW5pNlNVVjBDQ1A2WWgzTDF4cDdIdllablRNS1EvcmE0SlFpSEpyQmsrT1JxWnZGczA0eEczQjk1VFo3TytQVk5zMGRhY3B2U2tNbzZDZ2RBRDhQS3FoNFQ4YTByYXorM1EzU0hJSHpIc0xaMGdSdDJlRnJJS1hrTVRQeTZBOEptMnJxWWF2SDZQcW1rSVhMZjJRWnBKckExL09qc3VYS2lXb0MiLCJtYWMiOiI2ZjdmYzI0ZmVhNWMxMTA0ZmVjOGRmMzE5Zjc1ZWQ1NWYwMDY5OGMwOTBjZjk3NThiNjY0ZjJjNDcwYWRhNTA1IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:19:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpQc0xrMU9NSm50UzI1OStYSDZUQ1E9PSIsInZhbHVlIjoidVEvU0h0dUkxVXhWZE4xdGljbEpoWUsxV0xMNFlKamNPL0xNcnIxZDkzY09wVkszeTk5VWpwSm5rZnpwYkt6VVFGd3d0SVgydjFEbThoaWhPQUtGeS81WGkxc29QcEgvMXcvUkJjTmQ2RThidUJjNzM1SDVuMzBoTnIrQ1kyVmtjbUlHeWc1UG8wWGlzMEZFQmlaNC9iN3hOSUdiQkduTklpTkRhdFdXUnZZLzJHbmJ2c2MrMVBFNDlkeHd4d1N3VWgwdldweFhoeW9mR2pQMEVISDRVMWxKV2tLZ3J4N0V3UVZPNXgydWlyZ1o1a0pLQXNEOG5rN21XSWNLeFREODFVTGxoZDdBT1h1eHZzRE1iYzVubFNFclpNYzBMZFc2QTRwVUNFRU92eG1Ta1lBYWNTNGlFNTN3MjBoVDdjanlMRENFWUdNS09pcHlOS25pRmloWG92ZFlKQnBIeTJDZFFjZkNZV01MWFdSM2tHQ3h1ZjdkeEJCWEJnRDBjQ2RabG9Wd3JiV0pJaDlYLzNyc2k1NHNwV0JpaS9Nc1ZuK2tRakZTU3NCaFlTa1pyb1BwRXZHcHdEUkEzbElmQWg1eFZkUXVTOVpjSFl0UVFST3Nnck1UMEtoNXNMZ000QmR1VFFNMFRobVRzR3JwUHphSElKR2lQdGdjNmFvTHVwVEkiLCJtYWMiOiIwYjFhOGNiMzg2YWY5OTk5YjlkMmQ3YjA5Mzk2YWNkMzRlZTgwOWEyNjc2ZjZiZjliYTMyMzJjYTA3OTgxZmU2IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:19:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijc0MkRzOVN3ZWFiOUFtaXpkdnR5ZkE9PSIsInZhbHVlIjoibVpxRDJpWlFzdkJmM01saXBUaUU1NUlPSVArbmorbFF5OTV2NGZDK2lDNmxYaVQ2dkxKdVUwZ3ZJc0ZxYjdrSnRQYUNqZFltN00xTkFteWpLOVhZVDZ5RER1RTJUV05xSlVPNFoyOFRmUHVTdjd3RE52Y05YZ1h5dW9rL1JiY2sza1RkQ0JqNlNLZHFneTY4ZVd6Sy9raDRsS2Q3V2hQZ1kxR2Y0V2xzYXcrRGlyWUhCazMzL2NvMVF0aExrZlpLQTNVWi9WRU9oZ1V2eGlxUWsybE45T3ZwVExnYzc0QlpOcGlJSzNIeEJIRHdsRC8vUmZLeHg1UnlwNHg2OW5JZWkyU2k3UGZOWWtEUTBrZXJRYXBiMjJHWnBleWwya3BNTlRHaElaQzZQc3J5QVBvTGZWdWRtMHFUd05wSzRlQUxlMjJFWWdpRjJqVEJrWmJlUGNlSXRqcC9EZTFhdW5pNlNVVjBDQ1A2WWgzTDF4cDdIdllablRNS1EvcmE0SlFpSEpyQmsrT1JxWnZGczA0eEczQjk1VFo3TytQVk5zMGRhY3B2U2tNbzZDZ2RBRDhQS3FoNFQ4YTByYXorM1EzU0hJSHpIc0xaMGdSdDJlRnJJS1hrTVRQeTZBOEptMnJxWWF2SDZQcW1rSVhMZjJRWnBKckExL09qc3VYS2lXb0MiLCJtYWMiOiI2ZjdmYzI0ZmVhNWMxMTA0ZmVjOGRmMzE5Zjc1ZWQ1NWYwMDY5OGMwOTBjZjk3NThiNjY0ZjJjNDcwYWRhNTA1IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:19:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpQc0xrMU9NSm50UzI1OStYSDZUQ1E9PSIsInZhbHVlIjoidVEvU0h0dUkxVXhWZE4xdGljbEpoWUsxV0xMNFlKamNPL0xNcnIxZDkzY09wVkszeTk5VWpwSm5rZnpwYkt6VVFGd3d0SVgydjFEbThoaWhPQUtGeS81WGkxc29QcEgvMXcvUkJjTmQ2RThidUJjNzM1SDVuMzBoTnIrQ1kyVmtjbUlHeWc1UG8wWGlzMEZFQmlaNC9iN3hOSUdiQkduTklpTkRhdFdXUnZZLzJHbmJ2c2MrMVBFNDlkeHd4d1N3VWgwdldweFhoeW9mR2pQMEVISDRVMWxKV2tLZ3J4N0V3UVZPNXgydWlyZ1o1a0pLQXNEOG5rN21XSWNLeFREODFVTGxoZDdBT1h1eHZzRE1iYzVubFNFclpNYzBMZFc2QTRwVUNFRU92eG1Ta1lBYWNTNGlFNTN3MjBoVDdjanlMRENFWUdNS09pcHlOS25pRmloWG92ZFlKQnBIeTJDZFFjZkNZV01MWFdSM2tHQ3h1ZjdkeEJCWEJnRDBjQ2RabG9Wd3JiV0pJaDlYLzNyc2k1NHNwV0JpaS9Nc1ZuK2tRakZTU3NCaFlTa1pyb1BwRXZHcHdEUkEzbElmQWg1eFZkUXVTOVpjSFl0UVFST3Nnck1UMEtoNXNMZ000QmR1VFFNMFRobVRzR3JwUHphSElKR2lQdGdjNmFvTHVwVEkiLCJtYWMiOiIwYjFhOGNiMzg2YWY5OTk5YjlkMmQ3YjA5Mzk2YWNkMzRlZTgwOWEyNjc2ZjZiZjliYTMyMzJjYTA3OTgxZmU2IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:19:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612367404\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-820254607 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820254607\", {\"maxDepth\":0})</script>\n"}}