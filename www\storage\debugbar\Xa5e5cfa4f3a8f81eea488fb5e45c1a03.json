{"__meta": {"id": "Xa5e5cfa4f3a8f81eea488fb5e45c1a03", "datetime": "2025-06-07 07:30:09", "utime": **********.448652, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.507878, "end": **********.448679, "duration": 0.****************, "duration_str": "941ms", "measures": [{"label": "Booting", "start": **********.507878, "relative_start": 0, "end": **********.310588, "relative_end": **********.310588, "duration": 0.****************, "duration_str": "803ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.310612, "relative_start": 0.****************, "end": **********.448683, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01839, "accumulated_duration_str": "18.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.370832, "duration": 0.01583, "duration_str": "15.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.079}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.406939, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.079, "width_percent": 4.676}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.432358, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.756, "width_percent": 9.244}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281407057%7C3%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InYwQ1FNRk56djNHUjRLbWNIcHdJenc9PSIsInZhbHVlIjoiTlcySVRrbUU5aGs4Y2dKa0NZUVRVN1B4M3h6NDZsNEdiaWpQWUIvWTdEeEJ1QlUwcEpIQjJUOTlFZmd3YllIMzJsR0IvSVhXTnhCT2hDdTNmdlVZMHN5TkJKZE9Za2ZtRTJpa3ptT3NaQXRLajFBL2YxUDQ0ZEc2dksxVkszeWRhVXNGWUFJRHdUclN0c3ZqYWVjejgxVEFPVTdETHJxczNQYjFZU1RBMis1c3YwYWlyM0xzK0s5UTJKOXVjMEJVelZ3alJZSFp6STZLV0VHdzNRc3Y3OEpONjdxeXB1cGhhb0pDWDhnczllNUh6Rmg2bzhsQ0lFWjJCV0VxTS9HK1RtRkhvWHBnTStkZTVvVFRLVVB0VnlsY2J4ZHNEdkk1aGdYZXp0MEdaQnQ5T3Zvb2phRjA0QUk0SlZnVGwyVnNoWFlEalN0WGlwZjRHSHFQUER2NEZRNUVnS0gvTHg1SnBuSVREQUxoUzVZamNreTBTbHBzay9hS3pzdnRIMmYybExYRUdqNmtmZFhFbFROWWh0bTJVZHQwTXpsQjBqWnJVUkJuaFRBNThiRXpwNVdXVVYwenpET0FyVXJhTVl3U2Rzcm9FWTVrUjg3bUxSYVRSKzQydG10aW1wcStpYmFSdzgrVjF0RFNQTXltQ0VaZnRlMmlHWVBZRHgrYmllYzciLCJtYWMiOiI0ZjJiMzFhZWE0ZjI5ZjZlZmE1Yzc3OGUxYjNhYTcyNWYyNDcwMWRhZDlkZDk0YTc1NmVhMTYxYzBiMjY4ODcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlLa04xME1WUTJWeGg3VG5hYWZGOEE9PSIsInZhbHVlIjoiNmxXaHBQTUVhNDJvcW1sdFpnVURLK2M0YXduNVRXa2ZXanc4ZkVlYnJEUHk0Tk04cVlOZkpKSzBTa0p5cHhvbzJsT1pzb0ZFOFpjcHlyMVpubzZ4ZDE0a29Lbm1vQXlwYi9xRnpKcC9CbjBVdUNHMURJVDJFdzdTbGRNUnF2bW4xSlZzNW91cEpReHB3S2FDTTRWa0F1eFlHY3RjS1NNQ3piM1VWWkNYeHcySncwNk05dy9wYWU2ekRPdy9pVVdGeFRmamhadXNTTzBNQkR1TmpJUGtYb1NheDVjOHhvTWJQNWQ1RWtHcURMNzRqd3Uxcjk1SXNjSlBJS3FYQkRRbzVBcVlrcXBCY3pGc1lucS80SVpBK0tWOUZZbDVkZ0JPMDVOTjBDMHZVQUFtY2M2RW93N1hvSTVYK2RON09RcEh2WXR2TXBJQ2pGa2c4U1ZwMWVrUzdGS28wcEZVdWZlTHEzUFNocVRNNHc2ZmJxSU9Malo4dklOUGdIRnBZTEhDRXJJWmlDa3UrMU5sbFVvaThzSUc4UVNrQ2xqQzhINjZub25sVlZqWnVsTU1kQkxpTTNJbTByTjRBZUtMaDNndSs0WXlGK1Vlb1AzSzFBaGQwNW5VKzBVMFFTekJmdjJaWkg1THUwUVoyaUJ2UkZzMHFwMFVqaWdpMGprZ0hZWlkiLCJtYWMiOiI4NDczMjJiNTM5YTNiZGY0MTEyNjhhNTQwZjUwM2VlOGQzZTdiNWQ1YTU0MWEzOGNhZmZjYzAxNWE4MDEzNWM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-869314445 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YNJBgXOqtFU4b8z8Vx4XcGRnEfxLj9OGGtpNz79H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869314445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9RZFd4ODZIR1NRdjNXR0NaV1RTSVE9PSIsInZhbHVlIjoicjJacjM0NGJzWDdMRG5YYlZWMHFVK3N1YXpsUFJscUJ3Tm9FWWJTaEx2QmZVZmp3Q21EWThoRkFEblJQNUttbExQM3hyRy9oN2NLWGxZcndKUDh1WUh2YmwrUjFtNUtEbm5IWUhLQ1I0OE55YU43enNNak01T3JyMnBsZWhmOG9JclkxTHFrTFlSQThhRXhtOVNORUJ0Uk1iVm9hN1dpM0VBSHJpcWJpdUx6NHRLcktOSWpKa1AxbzVDaXJVbXVVMlBMZHJLRFRaRjFiWEl5Q0ZaZStCRDFReHhZR2w1ZXJJT0ZOaW1qT0dYUkp1akF2bDV3alAreFV3cERNWGUrMkp3WmFoczlwc1p2SCtBRnFpM2dKL2p4a2ptdWhjRFpuVSszd2dCNjh2TUpDdUNqbmVIT3ZzeFpXUGZteXE0R01ndGRZMVNLK240SHZNYVZ4RFpsdytHeFNpVStON1BYTUZyNVlwK2dWVHY1UVZMOTRSSmNFRGxCdHFGTjFHMzJYNGlhcFRpd0NZYVJDRGhtbDlTem9NMWlhRlBJdXJFR1ZidG1xR3VOdjlsNk5EcS9rRlFQdVpEa1ZVWlJxVnhoeTVIYW0yTzh5TkFNOGxZNzlRN1V4bG4vTkUyTFZMNEFVNnc3dVVWOEYvL1ZBSTVJVVk4bFpaa0N2THVQWHVteSsiLCJtYWMiOiI5ZmVkMTI2Njc5MzVmNTAxMWVlNGM5OTgwMWQwN2VlNzE1ZjQ5OWQ5NGI1NDk3M2NiZTA1ZjYyNTBmMDgyZjQ5IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InBERWJkVm8xRkdlSjNlZDhLMGFRaEE9PSIsInZhbHVlIjoiTC8xS0loZWZkQmdwVTRaZGQrSkVqWW1YcHZlQTJtSmpnK2VncjRCMVFKakpWbUxhY0N4bEo3d3BZTU1RdTZ6WEhyQWFZZVRBeExSZ3ZLbHFFcGhGbDhpYitOaEt2UUxVWlM4RXZ0bW03d0dPQ2ZCVU10d1Uwa1MxaHlWaklPM0lqTTJ5R21tc1o1NllwcE9Oc1U3M3V5YWhOS1BvMXJXL1YrL2VFOW1CV20rR0M3UENNckp0blI3S0ZIY0RtS0JpVDA3aDVOcklSbFhJRnpHNSsxWXlOcVEzcFA3aGsrRU5ERlNiNWpPSUlYU05qQ28wZmlpMTVQc0FucEkyVFpZd2diVVBqQ05ySFFoQklBSjdESk96TE4wZFZJRVdTN0VhY2QvTVYrVG0yUHFwamJQMC9vZW5sOVlEbDhTa3hWdXFKNzV5dlJvbTRYYzE1OFhOaC9hUkNqRk1KNnFtMWxzTkFwVzYwcEZPem5kMXJIZFJDVUdPL2p3NWxtQ1BQZDUvbGt3L0JGMGJFNVp2MTdORGVLeXdRYlBYV1krYzljZW13QWpYYjF6bjJOMnAyZ2xJRHpRQldGUmxWR0VPYVJNakl3OTZaNENjR2xoYXZrVHdLT3podGEzTTUwd1I1TEhQd2NldmxteFlCSHdIcXZaeU5sTUpLdFlVMWRzVGt0MkciLCJtYWMiOiIzMzg0ODQ3MzRmZTU1YmIwYmJlYTQ2NmE2YmFjN2QzM2RiZmMwMzMyMzQxMTNiOTc0NGI1ODFhOWMwYTAyOTNmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9RZFd4ODZIR1NRdjNXR0NaV1RTSVE9PSIsInZhbHVlIjoicjJacjM0NGJzWDdMRG5YYlZWMHFVK3N1YXpsUFJscUJ3Tm9FWWJTaEx2QmZVZmp3Q21EWThoRkFEblJQNUttbExQM3hyRy9oN2NLWGxZcndKUDh1WUh2YmwrUjFtNUtEbm5IWUhLQ1I0OE55YU43enNNak01T3JyMnBsZWhmOG9JclkxTHFrTFlSQThhRXhtOVNORUJ0Uk1iVm9hN1dpM0VBSHJpcWJpdUx6NHRLcktOSWpKa1AxbzVDaXJVbXVVMlBMZHJLRFRaRjFiWEl5Q0ZaZStCRDFReHhZR2w1ZXJJT0ZOaW1qT0dYUkp1akF2bDV3alAreFV3cERNWGUrMkp3WmFoczlwc1p2SCtBRnFpM2dKL2p4a2ptdWhjRFpuVSszd2dCNjh2TUpDdUNqbmVIT3ZzeFpXUGZteXE0R01ndGRZMVNLK240SHZNYVZ4RFpsdytHeFNpVStON1BYTUZyNVlwK2dWVHY1UVZMOTRSSmNFRGxCdHFGTjFHMzJYNGlhcFRpd0NZYVJDRGhtbDlTem9NMWlhRlBJdXJFR1ZidG1xR3VOdjlsNk5EcS9rRlFQdVpEa1ZVWlJxVnhoeTVIYW0yTzh5TkFNOGxZNzlRN1V4bG4vTkUyTFZMNEFVNnc3dVVWOEYvL1ZBSTVJVVk4bFpaa0N2THVQWHVteSsiLCJtYWMiOiI5ZmVkMTI2Njc5MzVmNTAxMWVlNGM5OTgwMWQwN2VlNzE1ZjQ5OWQ5NGI1NDk3M2NiZTA1ZjYyNTBmMDgyZjQ5IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InBERWJkVm8xRkdlSjNlZDhLMGFRaEE9PSIsInZhbHVlIjoiTC8xS0loZWZkQmdwVTRaZGQrSkVqWW1YcHZlQTJtSmpnK2VncjRCMVFKakpWbUxhY0N4bEo3d3BZTU1RdTZ6WEhyQWFZZVRBeExSZ3ZLbHFFcGhGbDhpYitOaEt2UUxVWlM4RXZ0bW03d0dPQ2ZCVU10d1Uwa1MxaHlWaklPM0lqTTJ5R21tc1o1NllwcE9Oc1U3M3V5YWhOS1BvMXJXL1YrL2VFOW1CV20rR0M3UENNckp0blI3S0ZIY0RtS0JpVDA3aDVOcklSbFhJRnpHNSsxWXlOcVEzcFA3aGsrRU5ERlNiNWpPSUlYU05qQ28wZmlpMTVQc0FucEkyVFpZd2diVVBqQ05ySFFoQklBSjdESk96TE4wZFZJRVdTN0VhY2QvTVYrVG0yUHFwamJQMC9vZW5sOVlEbDhTa3hWdXFKNzV5dlJvbTRYYzE1OFhOaC9hUkNqRk1KNnFtMWxzTkFwVzYwcEZPem5kMXJIZFJDVUdPL2p3NWxtQ1BQZDUvbGt3L0JGMGJFNVp2MTdORGVLeXdRYlBYV1krYzljZW13QWpYYjF6bjJOMnAyZ2xJRHpRQldGUmxWR0VPYVJNakl3OTZaNENjR2xoYXZrVHdLT3podGEzTTUwd1I1TEhQd2NldmxteFlCSHdIcXZaeU5sTUpLdFlVMWRzVGt0MkciLCJtYWMiOiIzMzg0ODQ3MzRmZTU1YmIwYmJlYTQ2NmE2YmFjN2QzM2RiZmMwMzMyMzQxMTNiOTc0NGI1ODFhOWMwYTAyOTNmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}