<?php
/**
 * ملف اختبار سريع للمسارات
 * 
 * استخدم هذا الملف لاختبار المسارات بسرعة
 * php artisan tinker
 * include 'test_routes.php';
 */

echo "=== اختبار مسارات معالجة فواتير المستودع ===\n\n";

// اختبار المسارات
$routes = [
    'warehouse.purchase.processing.index',
    'warehouse.purchase.processing.update.inline',
    'warehouse.purchase.processing.field.options',
    'warehouse.purchase.processing.edit.products',
    'warehouse.purchase.processing.update.product',
    'warehouse.purchase.processing.add.product',
    'warehouse.purchase.processing.delete.product'
];

foreach ($routes as $route) {
    try {
        $url = route($route, ['id' => 1], false);
        echo "✅ {$route}: {$url}\n";
    } catch (Exception $e) {
        echo "❌ {$route}: {$e->getMessage()}\n";
    }
}

echo "\n=== اختبار الكونترولر ===\n\n";

// اختبار وجود الكونترولر
$controller = 'App\Http\Controllers\WarehousePurchaseProcessingController';
if (class_exists($controller)) {
    echo "✅ الكونترولر موجود: {$controller}\n";
    
    $methods = [
        'index',
        'updateInline',
        'getFieldOptions',
        'editProducts',
        'updateProduct',
        'addProduct',
        'deleteProduct'
    ];
    
    foreach ($methods as $method) {
        if (method_exists($controller, $method)) {
            echo "✅ الوظيفة موجودة: {$method}\n";
        } else {
            echo "❌ الوظيفة مفقودة: {$method}\n";
        }
    }
} else {
    echo "❌ الكونترولر غير موجود: {$controller}\n";
}

echo "\n=== اختبار النماذج ===\n\n";

// اختبار النماذج
$models = [
    'App\Models\Purchase',
    'App\Models\PurchaseProduct',
    'App\Models\Vender',
    'App\Models\Warehouse',
    'App\Models\ProductService'
];

foreach ($models as $model) {
    if (class_exists($model)) {
        echo "✅ النموذج موجود: {$model}\n";
    } else {
        echo "❌ النموذج مفقود: {$model}\n";
    }
}

echo "\n=== اختبار قاعدة البيانات ===\n\n";

try {
    // اختبار الاتصال بقاعدة البيانات
    $purchases = \DB::table('purchases')->count();
    echo "✅ جدول purchases: {$purchases} سجل\n";
} catch (Exception $e) {
    echo "❌ جدول purchases: {$e->getMessage()}\n";
}

try {
    $purchaseProducts = \DB::table('purchase_products')->count();
    echo "✅ جدول purchase_products: {$purchaseProducts} سجل\n";
} catch (Exception $e) {
    echo "❌ جدول purchase_products: {$e->getMessage()}\n";
}

try {
    $venders = \DB::table('venders')->count();
    echo "✅ جدول venders: {$venders} سجل\n";
} catch (Exception $e) {
    echo "❌ جدول venders: {$e->getMessage()}\n";
}

try {
    $warehouses = \DB::table('warehouses')->count();
    echo "✅ جدول warehouses: {$warehouses} سجل\n";
} catch (Exception $e) {
    echo "❌ جدول warehouses: {$e->getMessage()}\n";
}

echo "\n=== اختبار العلاقات ===\n\n";

try {
    $purchase = \App\Models\Purchase::with(['vender', 'warehouse', 'items'])->first();
    if ($purchase) {
        echo "✅ علاقة Purchase->vender: " . ($purchase->vender ? $purchase->vender->name : 'null') . "\n";
        echo "✅ علاقة Purchase->warehouse: " . ($purchase->warehouse ? $purchase->warehouse->name : 'null') . "\n";
        echo "✅ علاقة Purchase->items: " . $purchase->items->count() . " منتج\n";
    } else {
        echo "❌ لا توجد فواتير شراء في قاعدة البيانات\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في العلاقات: {$e->getMessage()}\n";
}

echo "\n=== اختبار الصلاحيات ===\n\n";

try {
    $user = \Auth::user();
    if ($user) {
        echo "✅ المستخدم الحالي: {$user->name}\n";
        echo "✅ معرف المنشئ: {$user->creatorId()}\n";
        
        // اختبار الصلاحيات
        $permissions = ['manage purchase', 'create purchase', 'edit purchase', 'delete purchase'];
        foreach ($permissions as $permission) {
            if ($user->can($permission)) {
                echo "✅ صلاحية {$permission}: متاحة\n";
            } else {
                echo "❌ صلاحية {$permission}: غير متاحة\n";
            }
        }
    } else {
        echo "❌ لا يوجد مستخدم مسجل دخول\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الصلاحيات: {$e->getMessage()}\n";
}

echo "\n=== انتهى الاختبار ===\n";
