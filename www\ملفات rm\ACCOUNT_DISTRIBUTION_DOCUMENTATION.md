# 📊 توثيق شاشة توزيع الحسابات

## 🎯 الهدف
إنشاء شاشة "توزيع الحسابات" تحت قسم العمليات المالية لإدارة حسابات الدخل والصرف للمنتجات والخدمات.

## 📁 الملفات المنشأة/المحدثة

### 1. ملفات جديدة
```
app/Http/Controllers/AccountDistributionController.php
resources/views/account-distribution/index.blade.php
ACCOUNT_DISTRIBUTION_DOCUMENTATION.md
```

### 2. ملفات محدثة
```
app/Models/ProductService.php
routes/web.php
resources/views/partials/admin/menu.blade.php
```

## 🔧 التفاصيل التقنية

### 1. Controller (AccountDistributionController.php)
**الوظائف الرئيسية:**
- `index()` - عرض الصفحة الرئيسية مع البيانات
- `updateInline()` - تحديث الحسابات عبر AJAX
- `getChartAccounts()` - جلب قائمة الحسابات

**الميزات:**
- ✅ التحقق من الصلاحيات
- ✅ CSRF Protection
- ✅ التحقق من صحة البيانات
- ✅ فحص ملكية المنتج للمستخدم

### 2. View (index.blade.php)
**المكونات:**
- جدول قابل للترتيب والبحث (DataTables)
- قوائم منسدلة للحسابات
- تحديث مباشر عبر AJAX
- مؤشرات بصرية للحالة الحالية

**الأعمدة المعروضة:**
- الاسم
- SKU
- سعر البيع
- سعر الشراء
- حساب الدخل (قابل للتعديل)
- حساب الصرف (قابل للتعديل)
- الفئة
- النوع

### 3. Model Updates (ProductService.php)
**العلاقات المضافة:**
```php
public function saleAccount()
{
    return $this->belongsTo('App\Models\ChartOfAccount', 'sale_chartaccount_id');
}

public function expenseAccount()
{
    return $this->belongsTo('App\Models\ChartOfAccount', 'expense_chartaccount_id');
}
```

### 4. Routes (web.php)
**المسارات المضافة:**
```php
Route::get('account-distribution', [AccountDistributionController::class, 'index'])
    ->name('account.distribution.index')->middleware(['auth', 'XSS']);
Route::post('account-distribution/update', [AccountDistributionController::class, 'updateInline'])
    ->name('account.distribution.update')->middleware(['auth', 'XSS']);
Route::get('account-distribution/chart-accounts', [AccountDistributionController::class, 'getChartAccounts'])
    ->name('account.distribution.chart.accounts')->middleware(['auth', 'XSS']);
```

### 5. Menu (menu.blade.php)
**الموقع:** قسم إدارة العمليات المالية
**الرابط:** توزيع الحسابات
**الصلاحية:** متاح لمستخدمي company و accountant

## 🎨 الميزات

### 1. واجهة المستخدم
- ✅ تصميم متجاوب مع Bootstrap
- ✅ جدول قابل للترتيب والبحث
- ✅ قوائم منسدلة تفاعلية
- ✅ مؤشرات بصرية واضحة

### 2. التفاعل
- ✅ تحديث مباشر بدون إعادة تحميل
- ✅ مؤشر تحميل أثناء الحفظ
- ✅ رسائل نجاح وخطأ واضحة
- ✅ عرض الحساب الحالي تحت القائمة

### 3. الأمان
- ✅ التحقق من الصلاحيات
- ✅ CSRF Protection
- ✅ التحقق من صحة البيانات
- ✅ فحص ملكية البيانات

## 🚀 كيفية الاستخدام

### 1. الوصول للصفحة
1. تسجيل الدخول كمستخدم company أو accountant
2. الذهاب إلى قسم "إدارة العمليات المالية"
3. النقر على "توزيع الحسابات"

### 2. تعديل الحسابات
1. اختيار المنتج المطلوب
2. اختيار حساب الدخل من القائمة المنسدلة
3. اختيار حساب الصرف من القائمة المنسدلة
4. سيتم الحفظ تلقائياً

### 3. البحث والترتيب
- استخدام مربع البحث للعثور على منتج معين
- النقر على رؤوس الأعمدة للترتيب
- استخدام الفلاتر المتقدمة

## 📊 البيانات المعروضة

### من جدول product_services:
- `name` - اسم المنتج/الخدمة
- `sku` - رمز المنتج
- `sale_price` - سعر البيع
- `purchase_price` - سعر الشراء
- `sale_chartaccount_id` - حساب الدخل
- `expense_chartaccount_id` - حساب الصرف
- `category_id` - الفئة
- `type` - النوع (منتج/خدمة)

### العلاقات:
- `category` - فئة المنتج
- `saleAccount` - حساب الدخل
- `expenseAccount` - حساب الصرف

## 🔄 التحديثات المستقبلية المقترحة

### 1. ميزات إضافية
- تصدير البيانات إلى Excel/PDF
- استيراد تحديثات الحسابات من ملف
- تطبيق تحديثات جماعية
- تاريخ التغييرات

### 2. تحسينات الأداء
- تحميل البيانات بالصفحات (Pagination)
- تحسين استعلامات قاعدة البيانات
- إضافة فهارس للبحث السريع

### 3. تحسينات واجهة المستخدم
- فلاتر متقدمة حسب الفئة والنوع
- عرض إحصائيات سريعة
- إشعارات في الوقت الفعلي

## ✅ اختبار الميزة

### 1. اختبارات أساسية
- [ ] الوصول للصفحة بنجاح
- [ ] عرض البيانات بشكل صحيح
- [ ] تحديث حساب الدخل
- [ ] تحديث حساب الصرف
- [ ] البحث والترتيب

### 2. اختبارات الأمان
- [ ] منع الوصول للمستخدمين غير المخولين
- [ ] التحقق من CSRF Token
- [ ] التحقق من ملكية البيانات

### 3. اختبارات الأداء
- [ ] سرعة تحميل الصفحة
- [ ] سرعة التحديث عبر AJAX
- [ ] استجابة الواجهة

## 📞 الدعم والمساعدة

في حالة وجود مشاكل أو استفسارات:
1. التحقق من ملفات السجلات (logs)
2. التأكد من الصلاحيات
3. التحقق من اتصال قاعدة البيانات
4. مراجعة هذا التوثيق

---
**تاريخ الإنشاء:** {{ date('Y-m-d') }}
**الإصدار:** 1.0
**المطور:** Augment Agent
