#!/bin/bash

# سكريبت إصلاح خطأ 500 في الداشبورد
# يحل المشاكل الشائعة بعد رفع الملفات الجديدة

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# متغيرات - عدل هذه القيم حسب خادمك
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"

# دالة لطباعة الرسائل الملونة
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                   إصلاح خطأ 500 في الداشبورد                  ║"
echo "║                Fix Dashboard 500 Error                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_info "بدء عملية إصلاح خطأ 500..."

# الخطوة 1: مسح جميع أنواع الكاش
print_status "🧹 الخطوة 1: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan view:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan config:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan optimize:clear"

# الخطوة 2: إعادة تحميل autoload
print_status "⚡ الخطوة 2: إعادة تحميل autoload..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && composer dump-autoload"

# الخطوة 3: فحص صلاحيات الملفات
print_status "🔐 الخطوة 3: ضبط الصلاحيات..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && chmod -R 755 app/"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && chmod -R 755 resources/"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && chmod -R 755 storage/"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && chmod -R 755 bootstrap/cache/"

# الخطوة 4: فحص سجل الأخطاء
print_status "📋 الخطوة 4: فحص سجل الأخطاء..."
print_info "آخر 10 أخطاء في سجل Laravel:"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && tail -10 storage/logs/laravel.log" || print_warning "لا يوجد سجل أخطاء"

# الخطوة 5: اختبار الاتصال بقاعدة البيانات
print_status "🗃️ الخطوة 5: اختبار قاعدة البيانات..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status" || print_error "مشكلة في قاعدة البيانات"

# الخطوة 6: فحص المسارات
print_status "🔗 الخطوة 6: فحص المسارات..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:list | grep dashboard" || print_error "مشكلة في المسارات"

# الخطوة 7: إعادة تحميل الكاش
print_status "⚡ الخطوة 7: إعادة تحميل الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan config:cache"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:cache"

print_status "✅ تم الانتهاء من خطوات الإصلاح الأساسية"
print_info "إذا استمر الخطأ، يرجى فحص سجل الأخطاء للحصول على تفاصيل أكثر"
