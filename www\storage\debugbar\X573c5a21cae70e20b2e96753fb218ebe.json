{"__meta": {"id": "X573c5a21cae70e20b2e96753fb218ebe", "datetime": "2025-06-06 19:18:41", "utime": **********.696692, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237520.082334, "end": **********.696722, "duration": 1.6143879890441895, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1749237520.082334, "relative_start": 0, "end": **********.495534, "relative_end": **********.495534, "duration": 1.4131999015808105, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.495551, "relative_start": 1.4132170677185059, "end": **********.696726, "relative_end": 4.0531158447265625e-06, "duration": 0.20117497444152832, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767816, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02085, "accumulated_duration_str": "20.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5986848, "duration": 0.01868, "duration_str": "18.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.592}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6550078, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.592, "width_percent": 5.276}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.674668, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.868, "width_percent": 5.132}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-808335533 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237512792%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZqcCtyWlJ4cG0vQ2wwRmsxcVVEd0E9PSIsInZhbHVlIjoidmY4Ti9pZ0ZTckxTUk1keXdNMjBRcThCVGlkbDRLVFMxd2FUOTFzeFFZWG9RWWVLaFJ2amV3YXZIRjVhYVIyMUhkUEJyZlVjTU1GbnE5R01LbDc4U2NoYklRc1Y4djhCVmtVQW5DZnBUM2RhNG05NENvcDFNSjNiMVF5S2o0L3RMemVkUUMwYzNNQXFicUhWcnVQUVpqMDBvdW9yZjdESUlwNWw0MXRkUjJ4VSs0ajJKMFUyU042TmNLajBBd0t5WFhiWjFqN2RHcm0vRjFadFpkdElYWnhyNStaa29HeUZ6aEVPY052UUE4RmZ4RmxuM1I1dWhLK1pJMEtpWWRBSGxLZVE2eEM0YUJoNk9iKzRGRTBBclFGdGloTWVBYVJ3WVU3VkVwd2lrUDRZRkUxdjhIcURVa3NVWXQ1NWhJQVNXbW13N0R5VnVXOHIxSGpWZUFsVkxNRWdPVldHdnA4OXM4YlNmbjNDUGk1OUtOU0VLSXlCWkpSRlRlWGFBWDQwME1ic1dYTTdsc2EwQXFVU3pibG1UK0c1VUViV0dCcmlQaVRDS3FFRUZVWWdSKzdHVm12NFp3U2gwVnUvaTY3eWJlSXQ3WUZTT3ZaZUVJcytCQklocExwZVpqK1YyRTB0Ty9NNzFsNDRNd2RBRlliM3BmRkxmYWNQd3J5TlZWMXciLCJtYWMiOiI1ZjlmZjBhMGU5OTFmYmYwMmQ1NjI1ZDFiMWVmOWEzNjhhYjM3YTBmNGYxZWUxZGViMTJkMTkzMWRmZGYxMWFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhBVDlvc1RJR09sTUV0bHdHSG55a0E9PSIsInZhbHVlIjoiNEhzYUxMZEtpdjRrM1N2Rmhvb3BSVzJ0NzFCMHdVc0JGYjFNY3RkcmxpUWk3SERzeEZOUGpRV0RpQm9VZ1JMTlA2ZVBwcDRhancyQVdGZ3djeXNLckRpazVPRFZDeVA1N1BDSkpQN1o3ampUMWdtNnBBYmFXYzIrOXR5YUZoVEJ4a1oyVTA0ZzV4Z3h2OHBDR05GcTJIaFQvMk9veDFWb29wVW14UTJxYXlpWnVBZjBoZzVwWm5ObUVrK1UrY1ZhN1p4VVY0SGNRUEE2MWl0UVNkZGo1UWNZdnlkZmlsY3ZZb3oyWEJKUFFWZktXOWp3UlNRSkFoQjZWTE1leFlUVU9DSnpQSm15WmI4QlFLZm1PbExEM2lXV1YySUQvd2wzWGJ4dlZ1aDQ0SzF2eUxoWE15cnlGcXpGYXBrYUpBSHNNUSsxOVJKQ1JDcDVJRFBhN2NrUlA3dzlsYy9ETUFaRzdDdkFHOFFtK2VpbXJYdVM0amdMaVRtemRFVUR4RmN5dGhpS0NuOU1ISFE2a0V3UDl3UXc3THQzK0F3NktCVml0dDZ2OXFaUWJZNVV1bmNsbGZnL1VkOVlQVjAxRkF4cGNza29mQWR3WllSRElSV2F1Vk5ZOHNQdzBoU0E4M1NNcHNZZ2pYYjIwMWNFRFZ4KzdFbmZQQkxpQ0pKakQrSzUiLCJtYWMiOiI4MzU2MTY4NDI5OTY1ZWU4Mjg3YmI5YmViNzA1ZWU5NTYyOTg5YWM2ZTMwOThiYmRhNTQ4MzI2NmMyNmVjNGQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808335533\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-403337124 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403337124\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1298138949 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:18:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im10elROTlpqSllWbXplYU5UbEdoa0E9PSIsInZhbHVlIjoiNitzN2pKT3hyYkh2TE5SbVlZbWJ6dUNLZTAxLzE4SHZQV0hRRTcrR1dsRDdoQXcrcmY5YkdnMHhpRTNDNjdNQ3g4ZWxHTTIxTkFjb1RXYXAvVjQzNHJ6Y1kva2Q1d0FHZDU5QlMwNXhQQTVIdHRyRXJlSXYzV1BqdlE1NkkvbXJKQUswUklNOHV6VzZzV2crYkxqOXRFR1g1T0ZOd1NwMjJkazZQNjB0Z2p4TGJXdFcySFpQYWNBNUkyYmd4eXdGN1FweWIzblBSNUJSQ09jbk56Ykh1ZUNjWXJPQlkwQlhNQ2ttZ1J4SnowUlJjZnZvUjdrcVRjMUc0QzhGamlzZzA5UDZGM3BFc3RLd3FzSDFOZUpyRkFtTG5WeUJ1NjMzR0ZvcFprNXRqcFNsZFZpcktHMnRxdm5DNHFIQWI3aFFkT1NKOXVOcW9SR0hvcHZIOGN1em1mU20yK21EM2ZBeld6K2pyK05xdXpTMDBVcGdhZDc2YWdFZzFSaVZha2IrZGpmU2ltNW91UFZFU1BXVDFEZHlQeWZFNDhMMWYwN3l0dVJ3bGovOUpGSDgyMWVrQzVaOXFEaEEwUU42N0ZZTW1xREp6Y2hHNURiZTI0bUdiMXk1dlR1OVBGNlFkbmJ6ZG1PYlFxYWRHWEJHZHJhTE9Zdlk3TE82dU5qUFhTSmMiLCJtYWMiOiIyY2VkN2U2NDU4ZDBkNDFjODM5N2VlNjFjM2ZlYmQ4NWFiZTRkZGQ3NGZjNmQ5ZTIyNjI0YjM1YjU2MmFjYzdiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFBd1JqSlNRL2h0N29XSTNnNmtDL3c9PSIsInZhbHVlIjoiTXFhRFYzVVFNc01oN3A0aUFYTDZWNDdmL3hWZUtnUGYveWpKZW1iNyt4TjdtamlNdm45ZGhpK3hFSlpLeHBRcXc4SW5vblppNXJIamdveU5rZ3lScEt3dU9RKzlUMnNqaURNblpyQlpOTnhWOHZSSG1HVXRMU1VGdExrUXB0WUdPY3VxSEt1aEFidFRZZEl0Z1liOFZZdnRhemNDa0FLcFYycWRYN3YxYTYrSVM1VkNsQVBUS3NkZ3hKR0Y3RFB4bm1nU2dJVjM5NDlWK1NTYkc0UVVjd0tJaVkyYmhJbFU5bndKZi9FOC9WOExyZy91QWJkVmtBdG56YzE5OSt4Z0xPTmJwZE9nTVkxUFEvS1VVdmZ0a2k2OU45T2xWRnF4d3BadkZvczlKR3pZQ2pRMHpWRmhNOU9MREx4TzhEVmFkWmI4ZGw2ZjlGMUI5OWQzTFdGZzVYQXVES2MwdW9qd2U4L2RNeWRQWHg2NXdJSzJURXVNVG5ySU9oR2pLTFdqRThTUEhNaHRzZ2JmOUkySjBZamUzRlVJaUJTSTdUVjJ3RThCWjJsdmIyZU5sQ3d2TldZcGZaVFRiUDIwakFjQTJUSTFUekVUNDJKTE8zN1llY1cvblZxZ0dRNUx5d0FpZTJwSytIR1MweFZsNWQ0KzJHeEthMFN2bFF1Ymlxb2EiLCJtYWMiOiJkNjViMTAyNGFmOGQ1YmM1YTZiZWRjNzY4OTQ2NGMzZGUwNTI2MGI1YTNhYzdmNDUyYTY2OTZiZmY3N2I3NTQ0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im10elROTlpqSllWbXplYU5UbEdoa0E9PSIsInZhbHVlIjoiNitzN2pKT3hyYkh2TE5SbVlZbWJ6dUNLZTAxLzE4SHZQV0hRRTcrR1dsRDdoQXcrcmY5YkdnMHhpRTNDNjdNQ3g4ZWxHTTIxTkFjb1RXYXAvVjQzNHJ6Y1kva2Q1d0FHZDU5QlMwNXhQQTVIdHRyRXJlSXYzV1BqdlE1NkkvbXJKQUswUklNOHV6VzZzV2crYkxqOXRFR1g1T0ZOd1NwMjJkazZQNjB0Z2p4TGJXdFcySFpQYWNBNUkyYmd4eXdGN1FweWIzblBSNUJSQ09jbk56Ykh1ZUNjWXJPQlkwQlhNQ2ttZ1J4SnowUlJjZnZvUjdrcVRjMUc0QzhGamlzZzA5UDZGM3BFc3RLd3FzSDFOZUpyRkFtTG5WeUJ1NjMzR0ZvcFprNXRqcFNsZFZpcktHMnRxdm5DNHFIQWI3aFFkT1NKOXVOcW9SR0hvcHZIOGN1em1mU20yK21EM2ZBeld6K2pyK05xdXpTMDBVcGdhZDc2YWdFZzFSaVZha2IrZGpmU2ltNW91UFZFU1BXVDFEZHlQeWZFNDhMMWYwN3l0dVJ3bGovOUpGSDgyMWVrQzVaOXFEaEEwUU42N0ZZTW1xREp6Y2hHNURiZTI0bUdiMXk1dlR1OVBGNlFkbmJ6ZG1PYlFxYWRHWEJHZHJhTE9Zdlk3TE82dU5qUFhTSmMiLCJtYWMiOiIyY2VkN2U2NDU4ZDBkNDFjODM5N2VlNjFjM2ZlYmQ4NWFiZTRkZGQ3NGZjNmQ5ZTIyNjI0YjM1YjU2MmFjYzdiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFBd1JqSlNRL2h0N29XSTNnNmtDL3c9PSIsInZhbHVlIjoiTXFhRFYzVVFNc01oN3A0aUFYTDZWNDdmL3hWZUtnUGYveWpKZW1iNyt4TjdtamlNdm45ZGhpK3hFSlpLeHBRcXc4SW5vblppNXJIamdveU5rZ3lScEt3dU9RKzlUMnNqaURNblpyQlpOTnhWOHZSSG1HVXRMU1VGdExrUXB0WUdPY3VxSEt1aEFidFRZZEl0Z1liOFZZdnRhemNDa0FLcFYycWRYN3YxYTYrSVM1VkNsQVBUS3NkZ3hKR0Y3RFB4bm1nU2dJVjM5NDlWK1NTYkc0UVVjd0tJaVkyYmhJbFU5bndKZi9FOC9WOExyZy91QWJkVmtBdG56YzE5OSt4Z0xPTmJwZE9nTVkxUFEvS1VVdmZ0a2k2OU45T2xWRnF4d3BadkZvczlKR3pZQ2pRMHpWRmhNOU9MREx4TzhEVmFkWmI4ZGw2ZjlGMUI5OWQzTFdGZzVYQXVES2MwdW9qd2U4L2RNeWRQWHg2NXdJSzJURXVNVG5ySU9oR2pLTFdqRThTUEhNaHRzZ2JmOUkySjBZamUzRlVJaUJTSTdUVjJ3RThCWjJsdmIyZU5sQ3d2TldZcGZaVFRiUDIwakFjQTJUSTFUekVUNDJKTE8zN1llY1cvblZxZ0dRNUx5d0FpZTJwSytIR1MweFZsNWQ0KzJHeEthMFN2bFF1Ymlxb2EiLCJtYWMiOiJkNjViMTAyNGFmOGQ1YmM1YTZiZWRjNzY4OTQ2NGMzZGUwNTI2MGI1YTNhYzdmNDUyYTY2OTZiZmY3N2I3NTQ0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298138949\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2********\", {\"maxDepth\":0})</script>\n"}}