{"__meta": {"id": "X0efcb4e7cbe48992ce19ac0fb15f2f91", "datetime": "2025-06-06 19:37:10", "utime": **********.873226, "method": "GET", "uri": "/user-reset-password/eyJpdiI6IlJRaXMxZUN4cEN2eVkvdW52bjBGYkE9PSIsInZhbHVlIjoiVmtEWS9XaHdnYWtKTHJLblc2T2VqQT09IiwibWFjIjoiZWM3NDY2ODhiNWY0Y2MwNzJkMTdjMjljNjExMDc5NGRmN2VlMTI0MjJjMjA4MWQxMzY4YzE4MWM5OWQ4MzdkOSIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238629.430353, "end": **********.873274, "duration": 1.4429211616516113, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749238629.430353, "relative_start": 0, "end": **********.684075, "relative_end": **********.684075, "duration": 1.2537221908569336, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.684097, "relative_start": 1.253744125366211, "end": **********.873279, "relative_end": 5.0067901611328125e-06, "duration": 0.18918204307556152, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44558880, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x user.reset", "param_count": null, "params": [], "start": **********.850726, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/user/reset.blade.phpuser.reset", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fuser%2Freset.blade.php&line=1", "ajax": false, "filename": "reset.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.reset"}]}, "route": {"uri": "GET user-reset-password/{id}", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\UserController@userPassword", "namespace": null, "prefix": "", "where": [], "as": "users.reset", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=575\" onclick=\"\">app/Http/Controllers/UserController.php:575-582</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.017889999999999996, "accumulated_duration_str": "17.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.789648, "duration": 0.016489999999999998, "duration_str": "16.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.174}, {"sql": "select * from `users` where `users`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 578}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.819571, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "UserController.php:578", "source": "app/Http/Controllers/UserController.php:578", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=578", "ajax": false, "filename": "UserController.php", "line": "578"}, "connection": "ty", "start_percent": 92.174, "width_percent": 7.826}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/user-reset-password/eyJpdiI6IlJRaXMxZUN4cEN2eVkvdW52bjBGYkE9PSIsInZhbHVlIjoiVmtEWS9XaHdnYWtKTHJLblc2T2VqQT09IiwibWFjIjoiZWM3NDY2ODhiNWY0Y2MwNzJkMTdjMjljNjExMDc5NGRmN2VlMTI0MjJjMjA4MWQxMzY4YzE4MWM5OWQ4MzdkOSIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-995736253 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-995736253\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1155572982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1155572982\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1149482018 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1149482018\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1448291595 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238565066%7C50%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVLVGFxYjE5TkpoWGQ0bjBmd3dmRlE9PSIsInZhbHVlIjoiektRRVFVOEd0YUg0bXBMYlRnSjh5QjhDakRMUERvd095elgyWnovQTVCS3NhczBvMGtCa1pYOEhPellKWlN2RGpiQ2xmRmxlRzIxSGtqTHFaTVQ3T0xuYUNjbktBM3lTbmxaUCtyejc0d0d6ZVNhaGlBRnFINlBCaE1Xd2RVUUhnbjRZL2MrbERENXE0SzFhaWVFMTFzSG9JRlpvaE10VExNajdZOTBwNW9zZURQM3UwUngxMk95OTN5cEVEVFNjMjQvTUVnK2dqN0hIc0Jjdm9ydXVHNGw4K2pSTDlRL29NSEE4QXhVQmZtSFFuNjhuaXhTNUdZdnlNNjlzaUorWCtBQU5wakp0Q1NaTDJlVnZTeUp2WW1hR1RZdTdWaW5PSDNLVnVTUmhmVlh3cVhGZlF5VGZwcVpEUjZVUk9lV0txWVJaN0d5UUdDa1VnWlFoTkp0QXdSQ0IxcUtZU2Y5WDR6eHpKaGwwVVU5VHNBQk5EeDR3YmgxT2UyQXNTc05yN0VsRHZMdEk2RVFWdkg1T0VCSGRIVmpCYVRZVUNWWjFpV1pSMGRTdjdNN1RCVXoyaEZzMnNIeHJ3bWZsY24xYzFlNmRvbEVCQVJjSGN1NFlBRm5xSHV6ZFNIUnRpa1lWSHVFQ25yM3NycTlkS2YrUkNhU3NjU25JdkxqM0ZVbmIiLCJtYWMiOiI3MGVkOGUwYTYxNWNmMDEyZjMwMGM5ODBhYWE2N2MyNjZiYjhkOGJkY2NiMzYyMmIzMDE0ZGFmOTc2ZTRlNTU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklIc0RWS1pqZzFPOXZDYzNjNFVYQ2c9PSIsInZhbHVlIjoiZm5PdjFNbUkwcEpBc2ZJd3g3WkZoK3ViN21oWDJnNmFJNHpLVHdOU3oxeDZYc05ZcHRFWU90TllMbkNaNVcxVUx2dWc4VnVnR0J6VlpIRkp6UkVwQmt6MHljMk16KzJrbGV5YjhqcVNiQlBDWFVmNmRqVGlyTS9DQmxGdGFCaWNNWmhSbmFjNVBkRDZLVGZuTk5ra2hhM1dwZ3QwcjVOQjlGaEFvOVovbVpyMmh5eUNiZUU0ZEZMbFRHdGh0MjQ4MkFidEVKUjZwR1g0WG9aWERHM3QzYTBqVC9EMzNGQm1vcy9KYzU0cThGWXBYSlc3VUhNM2pmd0Jxb3ZWWTZWR2FmRDY4L2h5TkJEUGNQbFFObG1QdFpSRSs5UjVJWU9vbWFFVVBsVUVGYjhnellnWHAzdVpyUE9GM1kxU0FEWGhoUFhGK3cvU01OZDAxTWhUMC91NTFWdjV3L1hoeHdaYkxaNEkwdys0cHJxSzV0cElrYVloYlR1TndTbEVhRGRNUmRhaXVXK0dKbFdSR3NXUGZrVzhMMFVZSnkwem16SW5qazhIU055RGQyMjdGdW9zTHBhOXZCeGVUbVdRczZsV095dTdRSmRTak9sOFB4QmNEMlpMTjdkK2Z0UWRpeE0zeC9tWjRGUEtJSlJMbXVxdXhzUTB0NzY5RW0rZkN4TGciLCJtYWMiOiJmNzhhMzcyYjE5NWQxOWM4OWQwNGJiMzkwYjdiNGMwMWRhOTU0NzgzZmQ4ZDc2YzRhMmYzZmM4Y2YwYjFkMDRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448291595\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1073789989 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073789989\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-33190733 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:37:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpyWG95QzZ6WUNwenc2OWlLV2tJaUE9PSIsInZhbHVlIjoiYlBYT0VXK3BjUVdWOEFXSUpxTkRiejlnYXN4MkxRS0NjRndTcXhzQkloUitGY3pFWEJpWGVEdkUxd1MzOXFhcHoyQVpxOERGQnFKRWJuSmVqMnNSNmZPZE1BMjZMRW5IcFNYWEVmbGlJTlZVTHl2QVl0UDU1NHpSRjY1ZUtyYVNPZU1KSUtlUlUrWGxKanpwS0Z3bGtrTXZwd3hLMTVqbnMyWUl4Q0JuR0dMVjRraEZPekVRQlNBVVowdDg1czZod0phaFBKU1JKMXp3dGRCMWlBcDEwUEdKSVMvWEVkc3dJZUI1R29CMlQ3cUw5RVJ5NGRKU3MySW5FMXRJQks0VU5Ed3VkSjdWUW16RkRGNFdmejRvdFRIdlo5MTlzU0ZDOVZFL09Pem5PTTZoaGs3Sy82WFhtVm5XRm9WYnBxdEc1WEhKa0RFNXZtdDFEQ1oyQTNHN3ExVGVONE1wTlBUNUpPL3JOcVkyWWtkK3kyTGxEZnlxSWdYU1Y2K2hOWlBmSmtrbmJUdkVKVVpuV203MVppWWZzNmxESXJ0SFBjTDNqcmY2d0YzaFdJUTZwcThjK1BIM3I3NVl4dmpEZ3VOaXgwdHZiVGFGaXUwWGZuQ0dvREJhYnE0cWl6WjBGd1gvVHd2RXVZcU40dFJsNDJydUd4bFhMeDRoVWM4eW1hd3MiLCJtYWMiOiI4ZDQwZmM0MThkZGYyYjBmZWYwNDkwNjEyMDU0MTQzMjAzZjE2ZmM3MmI4MzAxZGJmNDMzZTU0ODU2MGNkNTE0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBFaFM3UzFMMmtJcndweUxqbHkzTlE9PSIsInZhbHVlIjoiUEJReEthUDR2NG5PaHdZaDJUYlBJam03OEMrTlhwNjI3YjNhWFZTVjZ3SWlUYWNFcUVLL1V1b0U3Y2RmdW91RGN4SmhNN2hIeFEwbWhiK1V1UmZlRmVFbGVaczlTTXRMM2RCWWdKcjFNT3haVWJyM1ZQQitxVlFseW9RNzU2VTA2b0tUd0tNWXZtQTB4ZzA0V096SmtRVSs5aWZKc1Z3R0IwUzBJbWozU0xrQlFpdHlBUWxuNGx3enFNMjdwQ2haKzFOSWJNQVRjamJkUWVTSkxMRmtweHpudk85SVNBTGhwY2RraGc0SjljU256K2NjUmNhQTZ6cFZHYWV5V1ExWWY2YjZ2QVdoSDdJRlhMdTMvd0hlaHJ0bGhvbHJLQ2ZlMWx1Zmo3Z20weHN2SGJjTkxJTVBBeW93QWdWaHB5MTRvSlRUOUp3UjZrTTQxVzNKY0ZOdi9SeGNIeTlPWllDa2o0S29pVzRLTWZIUklGY0JUWnlkOWxxVDVvaGIyOU5zV2VHcnk2azEwVTJ2NUMzMnNKVnBBWmxnV1dzbVhDMFg2LzZob0tmaHoxeVVMcU1ocHl1Z2lxZEJTYzVNS0YvOVdZcnJ0REs3blBvYi9jSWg5Mjg2d0hpL0t4ZUdQcWplV3VucUQ2cHNYeWgrcHFTYlo5Nng5QnV5Nld3UTh4bysiLCJtYWMiOiJmN2QwNzk4N2QzNzc2ZWUyNTFkM2U3ZDIzNGE1NGU4MDJhNDdlNWFlZTkzMzc3ZGJmMGMxMjU2ZWJlMDJhYWNmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpyWG95QzZ6WUNwenc2OWlLV2tJaUE9PSIsInZhbHVlIjoiYlBYT0VXK3BjUVdWOEFXSUpxTkRiejlnYXN4MkxRS0NjRndTcXhzQkloUitGY3pFWEJpWGVEdkUxd1MzOXFhcHoyQVpxOERGQnFKRWJuSmVqMnNSNmZPZE1BMjZMRW5IcFNYWEVmbGlJTlZVTHl2QVl0UDU1NHpSRjY1ZUtyYVNPZU1KSUtlUlUrWGxKanpwS0Z3bGtrTXZwd3hLMTVqbnMyWUl4Q0JuR0dMVjRraEZPekVRQlNBVVowdDg1czZod0phaFBKU1JKMXp3dGRCMWlBcDEwUEdKSVMvWEVkc3dJZUI1R29CMlQ3cUw5RVJ5NGRKU3MySW5FMXRJQks0VU5Ed3VkSjdWUW16RkRGNFdmejRvdFRIdlo5MTlzU0ZDOVZFL09Pem5PTTZoaGs3Sy82WFhtVm5XRm9WYnBxdEc1WEhKa0RFNXZtdDFEQ1oyQTNHN3ExVGVONE1wTlBUNUpPL3JOcVkyWWtkK3kyTGxEZnlxSWdYU1Y2K2hOWlBmSmtrbmJUdkVKVVpuV203MVppWWZzNmxESXJ0SFBjTDNqcmY2d0YzaFdJUTZwcThjK1BIM3I3NVl4dmpEZ3VOaXgwdHZiVGFGaXUwWGZuQ0dvREJhYnE0cWl6WjBGd1gvVHd2RXVZcU40dFJsNDJydUd4bFhMeDRoVWM4eW1hd3MiLCJtYWMiOiI4ZDQwZmM0MThkZGYyYjBmZWYwNDkwNjEyMDU0MTQzMjAzZjE2ZmM3MmI4MzAxZGJmNDMzZTU0ODU2MGNkNTE0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBFaFM3UzFMMmtJcndweUxqbHkzTlE9PSIsInZhbHVlIjoiUEJReEthUDR2NG5PaHdZaDJUYlBJam03OEMrTlhwNjI3YjNhWFZTVjZ3SWlUYWNFcUVLL1V1b0U3Y2RmdW91RGN4SmhNN2hIeFEwbWhiK1V1UmZlRmVFbGVaczlTTXRMM2RCWWdKcjFNT3haVWJyM1ZQQitxVlFseW9RNzU2VTA2b0tUd0tNWXZtQTB4ZzA0V096SmtRVSs5aWZKc1Z3R0IwUzBJbWozU0xrQlFpdHlBUWxuNGx3enFNMjdwQ2haKzFOSWJNQVRjamJkUWVTSkxMRmtweHpudk85SVNBTGhwY2RraGc0SjljU256K2NjUmNhQTZ6cFZHYWV5V1ExWWY2YjZ2QVdoSDdJRlhMdTMvd0hlaHJ0bGhvbHJLQ2ZlMWx1Zmo3Z20weHN2SGJjTkxJTVBBeW93QWdWaHB5MTRvSlRUOUp3UjZrTTQxVzNKY0ZOdi9SeGNIeTlPWllDa2o0S29pVzRLTWZIUklGY0JUWnlkOWxxVDVvaGIyOU5zV2VHcnk2azEwVTJ2NUMzMnNKVnBBWmxnV1dzbVhDMFg2LzZob0tmaHoxeVVMcU1ocHl1Z2lxZEJTYzVNS0YvOVdZcnJ0REs3blBvYi9jSWg5Mjg2d0hpL0t4ZUdQcWplV3VucUQ2cHNYeWgrcHFTYlo5Nng5QnV5Nld3UTh4bysiLCJtYWMiOiJmN2QwNzk4N2QzNzc2ZWUyNTFkM2U3ZDIzNGE1NGU4MDJhNDdlNWFlZTkzMzc3ZGJmMGMxMjU2ZWJlMDJhYWNmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-33190733\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1637244216 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637244216\", {\"maxDepth\":0})</script>\n"}}