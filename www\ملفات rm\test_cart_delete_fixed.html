<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح حذف المنتجات من السلة</title>
    <meta name="csrf-token" content="test-token">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .cart-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .product-row {
            transition: all 0.3s ease;
        }
        
        .product-row:hover {
            background-color: #f8f9fa;
        }
        
        .btn-danger {
            transition: all 0.3s ease;
        }
        
        .btn-danger:hover {
            transform: scale(1.05);
        }
        
        .total-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .total-row.final {
            font-size: 20px;
            font-weight: bold;
            border-top: 2px solid rgba(255,255,255,0.3);
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .status-indicator {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .hidden-form {
            display: none;
        }
        
        .fade-out {
            opacity: 0;
            transform: scale(0.8);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🛒 اختبار إصلاح حذف المنتجات من السلة</h1>
        
        <div class="test-instructions">
            <h4>📋 تعليمات الاختبار:</h4>
            <ol>
                <li>انقر على زر "حذف" 🗑️ بجانب أي منتج</li>
                <li>تأكد من ظهور رسالة التأكيد مع اسم المنتج</li>
                <li>اختر "نعم، احذف" للمتابعة</li>
                <li>تحقق من حذف المنتج المحدد فقط</li>
                <li>تأكد من تحديث المجاميع تلقائياً</li>
                <li>تحقق من بقاء المنتجات الأخرى</li>
            </ol>
            <div class="alert alert-info mt-3">
                <strong>✅ الإصلاح المطبق:</strong> تم تحديث معالج حذف المنتجات ليعمل بـ AJAX بدلاً من إعادة تحميل الصفحة
            </div>
        </div>

        <div id="status-indicator" class="status-indicator" style="display: none;"></div>

        <!-- محاكاة جدول السلة -->
        <div class="cart-table">
            <table class="table table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>الصورة</th>
                        <th>اسم المنتج</th>
                        <th>الكمية</th>
                        <th>الضريبة</th>
                        <th>السعر</th>
                        <th>المجموع الفرعي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="tbody">
                    <!-- منتج 1 -->
                    <tr class="product-row" id="product-id-1">
                        <td>🍟</td>
                        <td class="name">شيبس ليز</td>
                        <td>
                            <input type="number" name="quantity" value="2" min="1" class="form-control" style="width: 80px;">
                        </td>
                        <td><span class="badge bg-primary">ضريبة (15%)</span></td>
                        <td class="price text-center">5.00</td>
                        <td class="subtotal text-center">11.50</td>
                        <td>
                            <div class="action-btn">
                                <a href="#" class="btn btn-sm btn-danger bs-pass-para-pos"
                                   data-confirm="هل أنت متأكد؟"
                                   data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟"
                                   data-confirm-yes="delete-form-1"
                                   title="حذف" data-id="1">
                                    <i class="ti ti-trash text-white">🗑️</i>
                                </a>
                                <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-1" class="hidden-form">
                                    <input name="_method" type="hidden" value="DELETE">
                                    <input name="_token" type="hidden" value="test-token">
                                    <input type="hidden" name="session_key" value="pos">
                                    <input type="hidden" name="id" value="1">
                                </form>
                            </div>
                        </td>
                    </tr>

                    <!-- منتج 2 -->
                    <tr class="product-row" id="product-id-2">
                        <td>🥤</td>
                        <td class="name">عصير برتقال</td>
                        <td>
                            <input type="number" name="quantity" value="1" min="1" class="form-control" style="width: 80px;">
                        </td>
                        <td><span class="badge bg-primary">ضريبة (15%)</span></td>
                        <td class="price text-center">3.50</td>
                        <td class="subtotal text-center">4.03</td>
                        <td>
                            <div class="action-btn">
                                <a href="#" class="btn btn-sm btn-danger bs-pass-para-pos"
                                   data-confirm="هل أنت متأكد؟"
                                   data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟"
                                   data-confirm-yes="delete-form-2"
                                   title="حذف" data-id="2">
                                    <i class="ti ti-trash text-white">🗑️</i>
                                </a>
                                <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-2" class="hidden-form">
                                    <input name="_method" type="hidden" value="DELETE">
                                    <input name="_token" type="hidden" value="test-token">
                                    <input type="hidden" name="session_key" value="pos">
                                    <input type="hidden" name="id" value="2">
                                </form>
                            </div>
                        </td>
                    </tr>

                    <!-- منتج 3 -->
                    <tr class="product-row" id="product-id-3">
                        <td>🥛</td>
                        <td class="name">حليب نادك</td>
                        <td>
                            <input type="number" name="quantity" value="3" min="1" class="form-control" style="width: 80px;">
                        </td>
                        <td><span class="badge bg-primary">ضريبة (15%)</span></td>
                        <td class="price text-center">4.00</td>
                        <td class="subtotal text-center">13.80</td>
                        <td>
                            <div class="action-btn">
                                <a href="#" class="btn btn-sm btn-danger bs-pass-para-pos"
                                   data-confirm="هل أنت متأكد؟"
                                   data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟"
                                   data-confirm-yes="delete-form-3"
                                   title="حذف" data-id="3">
                                    <i class="ti ti-trash text-white">🗑️</i>
                                </a>
                                <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-3" class="hidden-form">
                                    <input name="_method" type="hidden" value="DELETE">
                                    <input name="_token" type="hidden" value="test-token">
                                    <input type="hidden" name="session_key" value="pos">
                                    <input type="hidden" name="id" value="3">
                                </form>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- قسم المجاميع -->
        <div class="total-section">
            <h4 class="mb-3">📊 ملخص السلة</h4>
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span class="subtotalamount">29.33</span>
            </div>
            <div class="total-row">
                <span>ضريبة القيمة المضافة (15%):</span>
                <span class="taxamount">3.83</span>
            </div>
            <div class="total-row final">
                <span>الإجمالي:</span>
                <span class="totalamount">29.33</span>
            </div>
        </div>

        <div class="mt-4 text-center">
            <button class="btn btn-success btn-lg" onclick="resetTest()">🔄 إعادة تعيين الاختبار</button>
            <button class="btn btn-info btn-lg" onclick="showTestResults()">📈 عرض نتائج الاختبار</button>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.all.min.js"></script>

    <script>
        // محاكاة الكود المُحدث من custom.js
        var deletedProducts = [];
        var testResults = [];

        // إعداد CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // معالج حذف المنتجات المُحدث
        $(document).on("click", '.bs-pass-para-pos', function (e) {
            e.preventDefault();
            
            var formId = $(this).data('confirm-yes');
            var form = document.getElementById(formId);
            var button = $(this);

            if (!form) {
                showStatus('error', 'خطأ: لم يتم العثور على النموذج');
                return;
            }

            var productIdInput = form.querySelector('input[name="id"]');
            var sessionKeyInput = form.querySelector('input[name="session_key"]');

            if (!productIdInput || !productIdInput.value) {
                showStatus('error', 'خطأ: معرف المنتج غير موجود');
                return;
            }

            var productId = productIdInput.value;
            var productName = $('#product-id-' + productId + ' .name').text();

            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: "سيتم حذف " + productName + " من السلة. لا يمكن التراجع عن هذا الإجراء!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    button.prop('disabled', true).html('⏳');
                    
                    // محاكاة طلب AJAX (بدلاً من الطلب الحقيقي)
                    setTimeout(function() {
                        // إزالة المنتج من الواجهة
                        var productRow = $('#product-id-' + productId);
                        productRow.addClass('fade-out');
                        
                        setTimeout(function() {
                            productRow.remove();
                            
                            // تسجيل المنتج المحذوف
                            deletedProducts.push({
                                id: productId,
                                name: productName,
                                time: new Date().toLocaleTimeString()
                            });
                            
                            // التحقق من وجود منتجات أخرى
                            var remainingProducts = $('#tbody tr:not(.no-found)').length;
                            
                            if (remainingProducts === 0) {
                                $('#tbody').html('<tr class="no-found"><td colspan="7" class="text-center text-muted">لا توجد منتجات في السلة</td></tr>');
                            }
                            
                            // إعادة حساب المجاميع
                            updateCartTotalsAfterRemoval();
                            
                            // عرض رسالة نجاح
                            showStatus('success', 'تم حذف ' + productName + ' من السلة بنجاح!');
                            
                            // تسجيل نتيجة الاختبار
                            testResults.push({
                                action: 'delete',
                                product: productName,
                                success: true,
                                time: new Date().toLocaleTimeString()
                            });
                            
                        }, 300);
                    }, 500);
                }
            });
        });

        // دالة إعادة حساب المجاميع
        function updateCartTotalsAfterRemoval() {
            var subtotal = 0;
            var totalTax = 0;
            
            $('#tbody tr:not(.no-found)').each(function() {
                var row = $(this);
                var subtotalText = row.find('.subtotal').text().replace(/[^\d.-]/g, '');
                var itemSubtotal = parseFloat(subtotalText) || 0;
                
                subtotal += itemSubtotal;
                
                var taxRate = 15;
                var itemTax = (itemSubtotal * taxRate) / (100 + taxRate);
                totalTax += itemTax;
            });
            
            $('.subtotalamount').text(subtotal.toFixed(2));
            $('.taxamount').text(totalTax.toFixed(2));
            $('.totalamount').text(subtotal.toFixed(2));
            
            if (subtotal === 0) {
                $('.subtotalamount').text('0.00');
                $('.taxamount').text('0.00');
                $('.totalamount').text('0.00');
            }
        }

        // دالة عرض الحالة
        function showStatus(type, message) {
            var statusDiv = $('#status-indicator');
            statusDiv.removeClass('status-success status-error');
            statusDiv.addClass(type === 'success' ? 'status-success' : 'status-error');
            statusDiv.text(message).show();
            
            setTimeout(function() {
                statusDiv.fadeOut();
            }, 3000);
        }

        // إعادة تعيين الاختبار
        function resetTest() {
            location.reload();
        }

        // عرض نتائج الاختبار
        function showTestResults() {
            var resultsHtml = '<h4>📊 نتائج الاختبار:</h4>';
            
            if (deletedProducts.length > 0) {
                resultsHtml += '<h5>المنتجات المحذوفة:</h5><ul>';
                deletedProducts.forEach(function(product) {
                    resultsHtml += '<li>' + product.name + ' - ' + product.time + '</li>';
                });
                resultsHtml += '</ul>';
            } else {
                resultsHtml += '<p>لم يتم حذف أي منتجات بعد.</p>';
            }
            
            var remainingProducts = $('#tbody tr:not(.no-found)').length;
            resultsHtml += '<p><strong>المنتجات المتبقية:</strong> ' + remainingProducts + '</p>';
            
            Swal.fire({
                title: 'نتائج الاختبار',
                html: resultsHtml,
                icon: 'info',
                confirmButtonText: 'موافق'
            });
        }

        // رسالة ترحيب
        $(document).ready(function() {
            setTimeout(function() {
                showStatus('success', 'مرحباً! جرب حذف أي منتج من السلة لاختبار الإصلاح الجديد');
            }, 1000);
        });
    </script>
</body>
</html>
