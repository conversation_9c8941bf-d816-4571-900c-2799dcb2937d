{"__meta": {"id": "Xa332de006947333215a78a87e2a19c88", "datetime": "2025-06-06 20:34:15", "utime": 1749242055.018911, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242053.641486, "end": 1749242055.018954, "duration": 1.3774681091308594, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749242053.641486, "relative_start": 0, "end": **********.785112, "relative_end": **********.785112, "duration": 1.1436259746551514, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.785147, "relative_start": 1.1436610221862793, "end": 1749242055.018959, "relative_end": 5.0067901611328125e-06, "duration": 0.2338120937347412, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45695792, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.921296, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.938078, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.995508, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1749242055.004397, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.022099999999999998, "accumulated_duration_str": "22.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8669171, "duration": 0.00467, "duration_str": "4.67ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 21.131}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8774161, "duration": 0.01034, "duration_str": "10.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 21.131, "width_percent": 46.787}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.894766, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 67.919, "width_percent": 3.122}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.922682, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 71.041, "width_percent": 5.701}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.939895, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 76.742, "width_percent": 5.656}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.969587, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 82.398, "width_percent": 4.434}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.978016, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 86.833, "width_percent": 3.982}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9863918, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 90.814, "width_percent": 4.344}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.998088, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.158, "width_percent": 4.842}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1765985999 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1765985999\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-514120494 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-514120494\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1569321395 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1569321395\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1903607074 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=v1wjex%7C1749242043878%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxOOGRUS1BBZ0FxemtUTExJVnhRZ1E9PSIsInZhbHVlIjoiUWRRNlhpczZ2RjFSKzA3dm9CeXFGVVh5OWlDZE1yQmUrTFkrNW1wQVhGTFRsOVk0Wmp4Zjl1ZkZiVmRobHM1SHVzTWRmbU5WOXBreG1UM0ZIQStYVGVnNm1jelhGSHZ6T0ZudDB0My9BN0FKYkp4WDZCdjYwWEZpVFdOSStPNDJ3dnVnQ0ZwaGdMTDlUSkZXa1BodWRhUGl0dDhKUE1ncDhnVmN1SEtkSjlUU1FDcGZpeHI0dEZabnJJN0ZXRHJkbUlzWEJZRHlpV3drYWE5bzRTRHRsQkFqdEJLdERLOWdrVUlUUHlmQ2hOQ0VWdk5IaG92TlVHcHI1b2xBanFYTmYrNFk0MTNyMzNMWGJYM3JCQ2l3cDhtQWI2ZjZiQURZZnBVcnFzcDh3YmxxQVNHR200NW1ZVlVJRENSUlNZVzQ4SFZuZ0p1NXB4Ymh0SHQrOTRjeTA3aHV5ZUovcUppT3Q2YisyQnhhNDJnUzBFMHQvVmJ0Sm11Y3lxQlNhQXFGeDh0ZEREejlrR2Jna3JBSXQzNEJwbFZTSEd1QzBKWERtTy9tdHBUMDBwOThVbjRnN21mdjBDQ2d0bGx1VTNjKzB1QzVyZTZIdjJkaThtQ0pTOTlSSm94NjJOT3hoNHVrZGE1bFZacTlnRmJmaWdUd0xSNmtqcUVKM1pOMHJsTHoiLCJtYWMiOiIzODUyMmU0MDllMWZlNTQwYWRiNTZmZDBmMTljZTllY2QyMjA0NDFhM2JmNmI1MjlmY2E5Njg1Nzc4ZWE1MTExIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxZNEYyOFdSb0lFZkpyZXpXSTFhdGc9PSIsInZhbHVlIjoiVFhabTBIM3IxVXpSMk00b3hOSEJ1MG5xZHRuU0JudmpHbUNPZlRCVVp3WmV1aVUzMTg0c0pWU3phK3pNOVhVNS9pandSTmoyaEhZK0tpM2ZoZGgwKzM2K1RnZ0oralVlWlZ4aW9ZQkpNN0VsUnVkUitCaFl0b2JyaHNIM0RLRjVsNlZjMEFOM2hhcWJNSzJYU2ptNTIxZnF1UE04WTI1VTZUWjdUajJNMzVjaFE2NVQ1LzhkWVdnTmg3UjJjNUt2UkJnRmVnRGNsUWI3SWJabTZsNUROaTEwK2EwNEorZlpmSEdQMVRRd1RLYUhmSmxNRkFHNWNtWEV1Y3g5NklRbnRKa0d6dTE5T3NjcEdHcE1IREw3aGl4cTVoTmJjazhjRFpvcS96TTdXZFMxcS9qaUpxbTNYd0xqVk8vcTRjak5VUkYvczcxSVlFUzM3OHJxR1pOOUhHVktLMmFKMnBiVzRzYTgvUGFxdFpwYWNTcnlNeXhQVmUvQk5CK3VCOWVBelc4Q0VWeWZmSTAxN2lxc29McHIxcnVlRmkvU0U1RTdrSFVSRVR1QWExWTVaNGtZM3ViRXdNbHNyeGVkc1FFTHBGVjI1WU9Wa0FrbEs2SEs3YXJNZWpoM0MwZGNKWjJaVUpjWVgvTUQ0TjU0NVRUeU5ncVFZNk5WYzVhcFZoNzIiLCJtYWMiOiJjOGQ5MzdjYjVhYjEwYjNhMjNiYTBhNWUzMTE2NjNlNWI1OTMyY2M0MzMxMWUyZDVkZTZkYzFiNDVmNWVhMWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903607074\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2064016444 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ib82UhgW7QbmPWC6dET38U6gjVREpdbmeIYDPeql</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064016444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1994155438 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZEYzBjUzhDeVJvV0RvdXhCSWIrU3c9PSIsInZhbHVlIjoiWXdtMkI2KzZGOE1RSStjQlkxTWhQL1crdHREcU9UV09yR003dSt2RXpzVys4WGlKelJUK3FRVC9GVS9GRlR0OEpLZk41V2RUaVpTbWVjcHhRMDdsbXlwLy84Nm1JNVIyUVJjdzA1RnJGeU9ONWVyRDJZMWZMRHBkazc5SExaRmtsSjRaUVFzT05TUXp3MWxudlpDcHhOMEtXZDBuSUxZOUQ4RjJtQkhxb0hrRXQ4VmtFQWlrbVd5aER5alFkcldKaHdmZGpDZWpGRE12MWJLTzRkVlhOd2QybWhjUlFyalR4TzM1b3I5bGNPTHRKSGJoS2RPbEc5N0ViT2tORUpGL3QrMGtUeGVINXVVYXk1YmhyRDRBd1pvT0QyY2dtZWlCQ0hqdGpnelpWdytYSVl2cVlJMGIzTzVTQ0ZzVTBLNysvNHNXWTlBMnYyclFCajRKQWwvWk5Pc2pwc2J6eE5pRS9HUWVDOGlKcUZQYjExbUxOQ0wyVmh2dHhBRXordFVxTkVmU1hmODYrOElSSW4vN3I2ajNTbkhyUys5VVc1a0RxME8xUU15Q1NhbzUrVFhDSzBLTjFwcUtUREJBQjQyUHdYenBDazRsNUJHeHJCcEczM3c3Y1hSOUpVNEp3bTVjYlRRQ2tBbk5XaTVQS1pyUVJublQyLzZGdzBRR21Wc2ciLCJtYWMiOiJkNTFiYzBhNjFkNGZiYTQxYzNmOTdjYWY1YWY3ZjkwYjE2YjAzZjlhYTI4ZmYxNmI0MzU5YzIxZmU0MDkzYzQ5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktQdy9YSUl6aUM3MG5XUWZ1dWEzUnc9PSIsInZhbHVlIjoiS0ZadGtiUmcvSHhjcWYzZGxVRzdBUnFRb2pzcGhieVc1UCtIVTFPaGx0RGkrTUR6Vnp3SldYZGVhTVY0SkxXMW9oU3RoYXlMb3JRS2t0NkxmdmppY0RsazNZbnltSnRzN0RZS1h1YXl2d1pDeHFQUlVCSXJsZjdvQnEwbGtwbS9uRnBnOXY3RVlPT3RHL0tKOCs2RUx6RERYUUF3djF4ZHBnVnZJNHgxdGhSYlB0L3VseVJMbU9pbDJ4YUU5cXBpbEVzU0l1QmRkbHJFOHZkTE9rSzgxb3R1TWxDY0RNZCs1bWx5STVweCtmdk9nOUtoRlcyeXhmU1JyaWJGN2svdS96UGcwT3c4K1FtdjlraWV4Y3pLUnhpMmxlL0lmL2lsOHNnNms2SmducnNBSTRIOVpjaEtGZUROblI4UnZkRUQ2RlYrOHJYU05HdkZZQXJZNllJVWxuaWg2LzFBSThqZklXMUdLVk5NVk1yQXRZZzkwYmlmNmJMVWx0Yy93Q08rbUdqMjlUaVdUL0J3WTlwTXZKQ291N25JSW1RbHE1ZndLTHFNK0gvM2I0cnNvWkowOHhrZVhnOGFrdjR2UStCb3I1UHgxTmFSM0dCanRUL2t0QWhKSlpPeHNNeVl0aTAweS96dkVxRGFFTWZMTGdlWnMyVERXMDRvSjUySXFuQkUiLCJtYWMiOiJiZDczNTU3ZmNmOTAxOThhZDk5ZDI3YTk0NTQ2NTk3YTcxOTVkMzA3NzhlNzczZDE5MGQwZGQ3ZTA5YTA5NGI5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZEYzBjUzhDeVJvV0RvdXhCSWIrU3c9PSIsInZhbHVlIjoiWXdtMkI2KzZGOE1RSStjQlkxTWhQL1crdHREcU9UV09yR003dSt2RXpzVys4WGlKelJUK3FRVC9GVS9GRlR0OEpLZk41V2RUaVpTbWVjcHhRMDdsbXlwLy84Nm1JNVIyUVJjdzA1RnJGeU9ONWVyRDJZMWZMRHBkazc5SExaRmtsSjRaUVFzT05TUXp3MWxudlpDcHhOMEtXZDBuSUxZOUQ4RjJtQkhxb0hrRXQ4VmtFQWlrbVd5aER5alFkcldKaHdmZGpDZWpGRE12MWJLTzRkVlhOd2QybWhjUlFyalR4TzM1b3I5bGNPTHRKSGJoS2RPbEc5N0ViT2tORUpGL3QrMGtUeGVINXVVYXk1YmhyRDRBd1pvT0QyY2dtZWlCQ0hqdGpnelpWdytYSVl2cVlJMGIzTzVTQ0ZzVTBLNysvNHNXWTlBMnYyclFCajRKQWwvWk5Pc2pwc2J6eE5pRS9HUWVDOGlKcUZQYjExbUxOQ0wyVmh2dHhBRXordFVxTkVmU1hmODYrOElSSW4vN3I2ajNTbkhyUys5VVc1a0RxME8xUU15Q1NhbzUrVFhDSzBLTjFwcUtUREJBQjQyUHdYenBDazRsNUJHeHJCcEczM3c3Y1hSOUpVNEp3bTVjYlRRQ2tBbk5XaTVQS1pyUVJublQyLzZGdzBRR21Wc2ciLCJtYWMiOiJkNTFiYzBhNjFkNGZiYTQxYzNmOTdjYWY1YWY3ZjkwYjE2YjAzZjlhYTI4ZmYxNmI0MzU5YzIxZmU0MDkzYzQ5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktQdy9YSUl6aUM3MG5XUWZ1dWEzUnc9PSIsInZhbHVlIjoiS0ZadGtiUmcvSHhjcWYzZGxVRzdBUnFRb2pzcGhieVc1UCtIVTFPaGx0RGkrTUR6Vnp3SldYZGVhTVY0SkxXMW9oU3RoYXlMb3JRS2t0NkxmdmppY0RsazNZbnltSnRzN0RZS1h1YXl2d1pDeHFQUlVCSXJsZjdvQnEwbGtwbS9uRnBnOXY3RVlPT3RHL0tKOCs2RUx6RERYUUF3djF4ZHBnVnZJNHgxdGhSYlB0L3VseVJMbU9pbDJ4YUU5cXBpbEVzU0l1QmRkbHJFOHZkTE9rSzgxb3R1TWxDY0RNZCs1bWx5STVweCtmdk9nOUtoRlcyeXhmU1JyaWJGN2svdS96UGcwT3c4K1FtdjlraWV4Y3pLUnhpMmxlL0lmL2lsOHNnNms2SmducnNBSTRIOVpjaEtGZUROblI4UnZkRUQ2RlYrOHJYU05HdkZZQXJZNllJVWxuaWg2LzFBSThqZklXMUdLVk5NVk1yQXRZZzkwYmlmNmJMVWx0Yy93Q08rbUdqMjlUaVdUL0J3WTlwTXZKQ291N25JSW1RbHE1ZndLTHFNK0gvM2I0cnNvWkowOHhrZVhnOGFrdjR2UStCb3I1UHgxTmFSM0dCanRUL2t0QWhKSlpPeHNNeVl0aTAweS96dkVxRGFFTWZMTGdlWnMyVERXMDRvSjUySXFuQkUiLCJtYWMiOiJiZDczNTU3ZmNmOTAxOThhZDk5ZDI3YTk0NTQ2NTk3YTcxOTVkMzA3NzhlNzczZDE5MGQwZGQ3ZTA5YTA5NGI5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994155438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2025109706 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025109706\", {\"maxDepth\":0})</script>\n"}}