<?php
/**
 * فحص اتصال قاعدة البيانات ومشاكل الجداول
 * تشغيل: php artisan tinker
 * ثم: include 'test_database_connection.php';
 */

echo "=== فحص اتصال قاعدة البيانات ===\n\n";

try {
    // فحص الاتصال الأساسي
    $pdo = DB::connection()->getPdo();
    echo "✅ الاتصال بقاعدة البيانات: نجح\n";
    echo "   نوع قاعدة البيانات: " . $pdo->getAttribute(PDO::ATTR_DRIVER_NAME) . "\n";
    echo "   إصدار الخادم: " . $pdo->getAttribute(PDO::ATTR_SERVER_VERSION) . "\n\n";
    
} catch (Exception $e) {
    echo "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n\n";
    return;
}

// فحص الجداول المطلوبة
$requiredTables = [
    'product_services',
    'product_service_categories', 
    'product_service_units',
    'chart_of_accounts',
    'chart_of_account_types',
    'taxes',
    'users',
    'permissions',
    'roles',
    'role_has_permissions'
];

echo "=== فحص الجداول المطلوبة ===\n";
foreach ($requiredTables as $table) {
    try {
        $exists = Schema::hasTable($table);
        if ($exists) {
            $count = DB::table($table)->count();
            echo "✅ {$table}: موجود ({$count} سجل)\n";
        } else {
            echo "❌ {$table}: غير موجود\n";
        }
    } catch (Exception $e) {
        echo "⚠️ {$table}: خطأ في الفحص - " . $e->getMessage() . "\n";
    }
}

echo "\n=== فحص أعمدة جدول product_services ===\n";
try {
    $columns = Schema::getColumnListing('product_services');
    $requiredColumns = [
        'id', 'name', 'sku', 'sale_price', 'purchase_price', 
        'quantity', 'tax_id', 'category_id', 'unit_id', 'type',
        'sale_chartaccount_id', 'expense_chartaccount_id', 
        'description', 'pro_image', 'created_by', 'created_at', 'updated_at'
    ];
    
    foreach ($requiredColumns as $column) {
        if (in_array($column, $columns)) {
            echo "✅ {$column}: موجود\n";
        } else {
            echo "❌ {$column}: غير موجود\n";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص أعمدة product_services: " . $e->getMessage() . "\n";
}

echo "\n=== فحص القيود والفهارس ===\n";
try {
    // فحص الفهارس
    $indexes = DB::select("SHOW INDEX FROM product_services");
    echo "الفهارس الموجودة:\n";
    foreach ($indexes as $index) {
        echo "   - {$index->Key_name} على العمود {$index->Column_name}\n";
    }
} catch (Exception $e) {
    echo "⚠️ خطأ في فحص الفهارس: " . $e->getMessage() . "\n";
}

echo "\n=== اختبار إدراج بيانات تجريبية ===\n";
try {
    // اختبار إدراج بسيط
    DB::beginTransaction();
    
    $testId = DB::table('product_services')->insertGetId([
        'name' => 'منتج تجريبي',
        'sku' => 'TEST_' . time(),
        'sale_price' => 100.00,
        'purchase_price' => 80.00,
        'quantity' => 10,
        'tax_id' => '',
        'category_id' => 1,
        'unit_id' => 1,
        'type' => 'product',
        'sale_chartaccount_id' => 1,
        'expense_chartaccount_id' => 1,
        'description' => 'منتج تجريبي للفحص',
        'created_by' => 1,
        'created_at' => now(),
        'updated_at' => now()
    ]);
    
    echo "✅ نجح إدراج البيانات التجريبية (ID: {$testId})\n";
    
    // حذف البيانات التجريبية
    DB::table('product_services')->where('id', $testId)->delete();
    echo "✅ نجح حذف البيانات التجريبية\n";
    
    DB::rollback(); // التراجع عن التغييرات
    
} catch (Exception $e) {
    DB::rollback();
    echo "❌ فشل إدراج البيانات التجريبية: " . $e->getMessage() . "\n";
    echo "   تفاصيل الخطأ: " . $e->getTraceAsString() . "\n";
}

echo "\n=== فحص إعدادات قاعدة البيانات ===\n";
try {
    $config = config('database.connections.' . config('database.default'));
    echo "نوع قاعدة البيانات: " . $config['driver'] . "\n";
    echo "اسم قاعدة البيانات: " . $config['database'] . "\n";
    echo "الخادم: " . $config['host'] . ":" . $config['port'] . "\n";
    echo "المستخدم: " . $config['username'] . "\n";
    echo "الترميز: " . ($config['charset'] ?? 'غير محدد') . "\n";
    echo "المحرك: " . ($config['engine'] ?? 'غير محدد') . "\n";
} catch (Exception $e) {
    echo "❌ خطأ في قراءة إعدادات قاعدة البيانات: " . $e->getMessage() . "\n";
}

echo "\n=== فحص صلاحيات قاعدة البيانات ===\n";
try {
    // فحص صلاحيات المستخدم
    $grants = DB::select("SHOW GRANTS");
    echo "صلاحيات المستخدم:\n";
    foreach ($grants as $grant) {
        foreach ($grant as $grantText) {
            echo "   - {$grantText}\n";
        }
    }
} catch (Exception $e) {
    echo "⚠️ لا يمكن فحص الصلاحيات: " . $e->getMessage() . "\n";
}

echo "\n=== خلاصة التشخيص ===\n";
echo "إذا ظهرت أخطاء أعلاه، يرجى:\n";
echo "1. التحقق من إعدادات .env\n";
echo "2. تشغيل: php artisan migrate\n";
echo "3. التأكد من صلاحيات المستخدم في قاعدة البيانات\n";
echo "4. فحص مساحة القرص الصلب\n";
echo "5. فحص ملف error.log في MySQL\n";

echo "\n=== انتهى الفحص ===\n";
?>
