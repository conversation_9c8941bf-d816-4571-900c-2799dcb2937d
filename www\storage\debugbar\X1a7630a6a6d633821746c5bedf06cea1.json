{"__meta": {"id": "X1a7630a6a6d633821746c5bedf06cea1", "datetime": "2025-06-07 04:33:19", "utime": **********.8757, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270798.601078, "end": **********.875737, "duration": 1.2746589183807373, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749270798.601078, "relative_start": 0, "end": **********.730652, "relative_end": **********.730652, "duration": 1.1295740604400635, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.730673, "relative_start": 1.1295950412750244, "end": **********.875741, "relative_end": 4.0531158447265625e-06, "duration": 0.14506793022155762, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767208, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00692, "accumulated_duration_str": "6.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.807723, "duration": 0.00451, "duration_str": "4.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.173}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.836328, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.173, "width_percent": 18.208}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.852256, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.382, "width_percent": 16.618}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1902517081 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1902517081\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1673819907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1673819907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1171889932 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171889932\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1703013885 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270790773%7C14%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBiODF6MWsvZ3lGM2NJWGJaUytHRFE9PSIsInZhbHVlIjoiN0lZeklmK0VZQmxySDVZbm5GUm5CY3VESFljSkZ1NkZGRXE2TWhGRC9lRHRDeURqWWRCajVUY1d5RlJTQ1RZek9GZHp6dGtHajZsTFZPTm55S09VTDZTVXR2UjBQMG9MdmRVeE9odDNndDZ4T3BRcFlUNU4zQVhnZUowZXRLL1NPVXowMDIvbVM0TS9wVm53OEpGUGU0cGlXWGJodzNTQ2RPZ2c3VXNEUGliWWhQVFJjRjgyYTZMNlBKQzVONjkxSTN6OUZHbEhzb20yVXE3bDArRTlqeENFQnd5eHV4RjJ2MTVXbVFOYVp4eWJEdHpDVXhXOVpoMTBzcVluQVJxZ2tLSnd2M3JzTmp6QXVKcjdVQkY4dFUrRXNHZzR6S1dUeWlUNE1HMktMZjRNbGhXSGdkNFRYNmF4VzcvWk9JTnJWOG9mbncwOFdoaUdiZDQvdHFhcFFFUW1Sa2pwU3EyWVE1MWZDRUFMSWVKWTNJUVM5d0F0QkNSZnVqa3ZJTitMclhCczBkMDdyNmdZdVEzclg2ckFzblBtQkg1SUJ3VG5ibTVuSnlkOEJqSzVoSGM1MnBoZjhwUlNza3JET0k3SGFPRFhTYUVCWWwwUDdaRkU4eWJoek51UURCdzZTTXM1L1RQOXVXa0d3dFk2bngvcjBpT2daMDZOVTBpaXRXTWQiLCJtYWMiOiIxNmEwMjkyMzkyMjc1MTViNWM4Y2U2ZTczMTMwZTU1ODIxZWU0MzMxOGZlOWZkZjhjNzc3YWJlNjU5MDIwZTg3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlErbzk5VW5CRHozWk1SMnQwOHB3a1E9PSIsInZhbHVlIjoibkRHb3hiT1Y2YkFFUUNpaEdPbkNNalA5K0Jvb2hpQ0NSWExKVG0vZ1RFdkgzaU5WMnJpcVNKUGNJYkwrNFYxeHRyRTZoMWZaVmxsVm45L1J4NGtXL0JlU3hCYkd2eDBsMkR4TFZDR1oxcjhYYU13THRiU0hkd1RqN1lVSHB0elRTUEp1WmhDT3BUMVlUMlJxNE10dENFVnZEYzU1cVhsejVEQVlSWW1uNGxTT0RROUk4UTBESEZoUDd4emRuUzJVS01DSGN1bXB3RHlveUV2cHA3WDRyQThna28xR054MGVpYks3anZMUHFFcG00WjNhaE5teXN6YjJ1S3A1ODl1aWhkK3YzeXBQRmNhaVRxdC83Q2YrRUNqSnNmTGFzZkhaU2s5K2tRMDFaT0tlTis2cEUrcXRCZGFFZ1NsN1hMUTJFWGMwME5KTG5aR2lQbUZzQ3hCaHU5TnZNNVJsRXA4Wi9UQ3NWMUpxclhlZWxqMkRnQWIyY2lwUVh0VkxBeXlhK3JnOWpscU1DU3VETnVNa1o4R005MnFTTXhCcDJ3aHR4eXZBeFFtdjhLTmNXQmhXdWl4UE93YlQwZERWUXVnS05sRGZIUEZNRTdhdHZCZHlUNnlTOXV3MnVISnhFMWNZb1VabGJFeXBYRnEzRzdMSVBBRkJkK2FEaGRxSmtIOGEiLCJtYWMiOiJiN2RkOGJjYTQyNmU0MzBjMjAzM2Q0OWNlYzg5ZWI3MjMyYzQzYTk4YjM0ZTViOWYzZjc5YTQ0N2RlNmRiMWI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703013885\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-107345198 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zyhWlMrUupyybJnMtPnBjJ716zpTnMkeCiqRAqWb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107345198\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1552580287 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:33:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdaaVl4bnJXKzVaUGlWbU4yaUNjUHc9PSIsInZhbHVlIjoiNnRLTyt0VjlZbExoaDdmeTFpQ2F5eUVwblFqMHhhczNQSm1FYnhTQlZwRDF0QmpmRWJMbXJKcXc0RFdCWFF2eVh0alBxWExQOURCVXlWckNvOFZMZUxISFVGU0JDbHJsZmRJVmZ3M2dYUmhvQlAyWHF5blNyNXZpQlRKTnVHTEZrQ0F6eWxIVmtEdGVINVVrcHhCdExJYjVXNlNnUFdYdHdqc2NvR0NDVE9ieHE5K3M1dWZlaE9VcVZRUVgrd1E3Q1EyS2EvSUpPM3BXTllMb2NxanpTTWlMM01GcEVSbVhyMlp2V21LSGI4SFl6Q3ZVc2ZKamF6S1praHBsSFZYWGtqRGZ3cGFZYzhyMXR1dS9tWmRFT0M0VzBhbCtjditkU1RVeTdndU5jMHZnbm5ZRmw0M05zTThpRmNzYllSdEJtcXRkalV5WTdIRFN3R3JiUy8rdzJWRE9ySzE3YWhya3BhUGNJVjV5dGNWekJrZHVxbFV3S29YQWRRbXB5RGpWQ3ZqOHNKTHVZdE5SUkR5MXk2OVJPdzIzRG9NY1ZiNlo4aG9kd1ByLzJzWjlvN2xzM0kxcHkzYkczdklrcDhGd0s3VTVJSWp6WFlqRmxyRkNIc1QyUWl3WTlQU1g1WUR0V2F3NEttb1A2MnN1YVFJckZOczJ6dHM3WkpUL0F4cisiLCJtYWMiOiJhZTUxZjlhY2JhNDYwNDMwZDhiNmU4ODYxZWEyMjM1NDBlNjg0NmVjMTNjNDcwODM3ODI4Mzg2ZWZkZmNiNWM1IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpyOEVQNGJGVnQvSjNXT3k1U0o0UVE9PSIsInZhbHVlIjoicnRLODRuQ0hQOE9BL3QxcXBtb2ZITHl2NHZYYUdCU3FoRzVLdktSTnJva0liSjNXUWJiVEJ5T2taajR5Y3hrQTFGMXJzRExOZldkamJmbnBQdDJFdkw1UmxoaVlRSWcxVXY2aHBnUjhicGtBeWZaNWxpSFo5TUxPSXljRnplbURwcFprVHFyZk1qMTd0U2hGK2hLQStWU1I0SEtkYkNaR1lXekI0VWdteXZSeFp2YXJMV1BCRkhybDZWU0ErTWRUNXkySmdrREF1ZUdhUzJBNU0zTVBpNVJhR2F4aXFDNWhYOUVnMi9QUjAraXZqY3ZBeGN0YjhoUHd3MVd1U2NzTUNYcmZkb0xXcVV2dEJLN2srVCtVU1Njd2tLbVc5VlU0ZTBVT0FtU0xwdml1Q09LLzViY0IyMU1naFZPa2ZQME5nVFp5RUdONHI0NmxqRWhqK3k2T1FnbVRvcUFETVVHUjZSVzV0RWNTeEo1dG94TFV5UUZtTEZNODF2TU1tUUx3SGpiWU1IN0dMQnZBK0tBWFArZEdRTTBpcWJid0gvTmxOcFFNT3NKTTRVWG5Yb252NVFGMkpCcTdMNVlXYmhrZmY3dTByazF0R2xJUm8xZTJEV214d3NyYnQzRUpsZU5sRnhNOE44UmZWMXgrMmJQYWtMNVp4S0NUV1R5Q1cwa2UiLCJtYWMiOiIyOGI5NWEyYjViZTBkZDZiZjU5ODhjZTM2ODdlMGFkNzhmMWU1OTJmMDNjODFlOTE0NTcxMTdmMmIwZDQ1ZmJhIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdaaVl4bnJXKzVaUGlWbU4yaUNjUHc9PSIsInZhbHVlIjoiNnRLTyt0VjlZbExoaDdmeTFpQ2F5eUVwblFqMHhhczNQSm1FYnhTQlZwRDF0QmpmRWJMbXJKcXc0RFdCWFF2eVh0alBxWExQOURCVXlWckNvOFZMZUxISFVGU0JDbHJsZmRJVmZ3M2dYUmhvQlAyWHF5blNyNXZpQlRKTnVHTEZrQ0F6eWxIVmtEdGVINVVrcHhCdExJYjVXNlNnUFdYdHdqc2NvR0NDVE9ieHE5K3M1dWZlaE9VcVZRUVgrd1E3Q1EyS2EvSUpPM3BXTllMb2NxanpTTWlMM01GcEVSbVhyMlp2V21LSGI4SFl6Q3ZVc2ZKamF6S1praHBsSFZYWGtqRGZ3cGFZYzhyMXR1dS9tWmRFT0M0VzBhbCtjditkU1RVeTdndU5jMHZnbm5ZRmw0M05zTThpRmNzYllSdEJtcXRkalV5WTdIRFN3R3JiUy8rdzJWRE9ySzE3YWhya3BhUGNJVjV5dGNWekJrZHVxbFV3S29YQWRRbXB5RGpWQ3ZqOHNKTHVZdE5SUkR5MXk2OVJPdzIzRG9NY1ZiNlo4aG9kd1ByLzJzWjlvN2xzM0kxcHkzYkczdklrcDhGd0s3VTVJSWp6WFlqRmxyRkNIc1QyUWl3WTlQU1g1WUR0V2F3NEttb1A2MnN1YVFJckZOczJ6dHM3WkpUL0F4cisiLCJtYWMiOiJhZTUxZjlhY2JhNDYwNDMwZDhiNmU4ODYxZWEyMjM1NDBlNjg0NmVjMTNjNDcwODM3ODI4Mzg2ZWZkZmNiNWM1IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpyOEVQNGJGVnQvSjNXT3k1U0o0UVE9PSIsInZhbHVlIjoicnRLODRuQ0hQOE9BL3QxcXBtb2ZITHl2NHZYYUdCU3FoRzVLdktSTnJva0liSjNXUWJiVEJ5T2taajR5Y3hrQTFGMXJzRExOZldkamJmbnBQdDJFdkw1UmxoaVlRSWcxVXY2aHBnUjhicGtBeWZaNWxpSFo5TUxPSXljRnplbURwcFprVHFyZk1qMTd0U2hGK2hLQStWU1I0SEtkYkNaR1lXekI0VWdteXZSeFp2YXJMV1BCRkhybDZWU0ErTWRUNXkySmdrREF1ZUdhUzJBNU0zTVBpNVJhR2F4aXFDNWhYOUVnMi9QUjAraXZqY3ZBeGN0YjhoUHd3MVd1U2NzTUNYcmZkb0xXcVV2dEJLN2srVCtVU1Njd2tLbVc5VlU0ZTBVT0FtU0xwdml1Q09LLzViY0IyMU1naFZPa2ZQME5nVFp5RUdONHI0NmxqRWhqK3k2T1FnbVRvcUFETVVHUjZSVzV0RWNTeEo1dG94TFV5UUZtTEZNODF2TU1tUUx3SGpiWU1IN0dMQnZBK0tBWFArZEdRTTBpcWJid0gvTmxOcFFNT3NKTTRVWG5Yb252NVFGMkpCcTdMNVlXYmhrZmY3dTByazF0R2xJUm8xZTJEV214d3NyYnQzRUpsZU5sRnhNOE44UmZWMXgrMmJQYWtMNVp4S0NUV1R5Q1cwa2UiLCJtYWMiOiIyOGI5NWEyYjViZTBkZDZiZjU5ODhjZTM2ODdlMGFkNzhmMWU1OTJmMDNjODFlOTE0NTcxMTdmMmIwZDQ1ZmJhIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552580287\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1165958243 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165958243\", {\"maxDepth\":0})</script>\n"}}