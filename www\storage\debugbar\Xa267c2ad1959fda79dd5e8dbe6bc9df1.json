{"__meta": {"id": "Xa267c2ad1959fda79dd5e8dbe6bc9df1", "datetime": "2025-06-06 19:38:48", "utime": **********.989398, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238727.332014, "end": **********.989437, "duration": 1.6574230194091797, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": 1749238727.332014, "relative_start": 0, "end": **********.76702, "relative_end": **********.76702, "duration": 1.4350059032440186, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.767045, "relative_start": 1.4350309371948242, "end": **********.989441, "relative_end": 3.814697265625e-06, "duration": 0.2223958969116211, "duration_str": "222ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.030170000000000002, "accumulated_duration_str": "30.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.877072, "duration": 0.026690000000000002, "duration_str": "26.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.465}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.936144, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.465, "width_percent": 4.011}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.94525, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 92.476, "width_percent": 3.977}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.96632, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.453, "width_percent": 3.547}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1773638444 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1773638444\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1775384983 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1775384983\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2114165035 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114165035\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1631782356 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238717359%7C54%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQvR1dkR3FjNWVqbzlFVi9ydUZVR1E9PSIsInZhbHVlIjoieTNkQ0VWRWV1azdwb1RaTGNtMS8xeVJReUFuaE01NFM5Wi9DNGpqVjc5YU94OUJ6N2FxT21NMXJVN1ZZNE9jWEd4N1lmYUg1U0RMd2ZhMWJFMUY0b0xnMjhnSTVHRFdmUjJUa29sRFFtNlFZQWFjRDBDLzh1YzJJRmRFUjV2eUZqV0hzQVJNS043RmxPaEFSdGFqR3ptclNlYXpTTGZuZnNwaUpONUJKc1U2bXpQUlRZU2dhTUdIdGdFRUhmUDhXL0wyaEpYV05ZQzVGNWplV3VXZFVaS2RNendZSEM0Y1p1SlptMGNUUndZY3JGODNBdGJZUUxOVmxTMWFpTWR3TEhJd3VuVzBKT25lVS93MFhLdThwYVJYQjVqKytwOTk0aWp1NTRKYkFxa0lZOUVzbllwaHJUSjJNYlgwUFV0T1N2Wm9lQmZWTXNoNWgva3lXdVRxTk5VbzdGRjFKa0M3V3hpUmlVeVRrbm1WeTFYQVZmZjNaY0UvSTRSb1c4K3daSVJwbWlFWEhNTEpBZlRlY0YyS0RNWStNQzNxTm9vZ3NPS0l3TW9JVjRWSWt3cjJRSlU5NENXRE5Dd3R0L3FYMjlZbXZhbTFsTklabjUwTUVZbVJvdkptV1lsTjB4RGhoYm8rU3ltNGVuYmxPdmtrQ05wV1VSZk4xVmJ4Um1wK2giLCJtYWMiOiI2N2ZkOWJmNDM4YjU2MTMzYTYyZDRiYmQ4YjZjYjcxYzdkZGNjYjVhODBmZjIzMTFlZmRlOGYzMzAyMGI4NDdlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZteldodGdudkNxS2MzZTFEaldVMGc9PSIsInZhbHVlIjoiMG5VTkpHRmFrZXM5NytWdk1ycURVU3ZPQzB6TFliWW9IWm9sQVhMb0J0THhwVUFmZXdsSzdBUUVMUzc0SVVBTEppeFJZeGpaZFBHNzNNUE5pRkhvLzRzNkRmb0pzeW13MFVqKzZRUkkrdi9XZk53RThUUVBOTkxrU1cyRlVmUnJVQnNVamNCUnNCQ1ovS1EySkFCdVRmVEFYT0Z5MWpEb2Z0V1FGZmFZZlJ2WXBhbi9GeFlvTE5wWmZjVXJiS0dBYmNKUTMyVmtxYzFMcUIxK3YyYmNSRmtBRjJqaDdwQWhDb2pKTkFySjZQT3FBaHd0dDRuajluVU1HQ0ttN0RZYlVWN2xRaFNPMGlVMHJ4QTRqRnVFQWJEdVhiMnp2QjFoYU1mQnVzcStyUzF0U1lQTVlwWVlEWTI0K01GNWFZakF4Z0JWZFp0MVREeVRWeU11RjBrLzZVbFpPTk9KMi85UFRJcEJoSDdidmhaNVAwclBCY2xDaytmNm9TWHh6SDRFU05NUy9xLzRpNVoyL0FBd2doZkl0YThtSTl5UlYyZlBFaWpQY1k5MzNhNE9tU2N6R2NRVGhHcHgwT213UEFhb0thY0wyaTZYZXRFZ2s0czYyMkFiR1JjRGlvSWpjQytucTJNMjhseG9rV2JhQmFHTlQrV2toVXFiVFlwaFFwMnAiLCJtYWMiOiJlZmQ1NzI1ZTI5YjdiYTMyNzQzODZkYjIyZWZkODhmMDRjNWZhMjU2Y2E3MmVjMDcwMWU0NzU5M2U5OTRhMzViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631782356\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1170902334 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:38:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRsN0JReFFRR1hMcXJZVkNZRGlGRXc9PSIsInZhbHVlIjoiK0c4OVJ1aUtzU1ZQamdEYW9kV2tnbUVYSVdXa2xyMG8xYmh1ci8xeHNVWHF4WGlxQVA0TldkVVhUNkZiUW15UW5pamdra29NbTVkVExDSGV1VnlLMGNXalRUWXQ0MU5DVktWZWhDNVhXUnMwZk9XUkc2OTBjZ0ZaOFU5ZS8wTStYYWNkVVl1OTk0QjJVclhKdTlVbDcyalltVnpDYS93RWxPUlJBN2Vxb2xURjRnWTZpMXVNVW1RKzlvNU9udXRNcHFHQjAvUnY1VzJTRHVSazVIdVVKNHVDb1ViUndMT0NlUHk4OE5QNVZpQWpxTXhwdFQ5S1o3S1NrSzVEbGdGQkxaMjVBUXNocFdLWE44R256NXVoc1ZoWjVaKytrUlVXN1dOTHZvUFl0TUh6ZVVkU1hhU3R2VlZ6bVlzdzBDd21ZUjdHWWV2ckI0Z0IwUFB5K2VjZ1IxSkE4T0lEcnJoNXp1VmwrV0RJSzRWYWtkOUNQMGlDd2dQZjl0OW54OUxJMnlvRmR0QmxCcXFrM05LM2tURmMya2I5dG5GQ3hQdnJ0dXpLelBsbFVzZW0ra1RVY3l2N1NHL1VwT3JOMEJ5ZUJON3c1THNiNmt0dmgvcmd0SmZzVGhHb0p5UXhmcnhLRzZjMmFlMk0xcnQvM2ozS3J6ZDl3c3gzUng4RGZlL1IiLCJtYWMiOiJmODgwYTI2MDg3NjFiNjAwZmYxNTFhYjQyM2U0MDE5ZDNkYWQzYzcxZTJhN2Y3ODVjNGFiMzE2ZTFhYmRmNzc0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFTV1Y3c0dLVzJLNllZYlhYRzZsSlE9PSIsInZhbHVlIjoidXgwRVRUVmRLRkRURmMzTnVPdVo2cElWL0MxUy94L3BRZXkyREtaU0tuRmtvMmE4VmxLR0RKVmlhWXUzMFBINFF0VVFwYmRyVVBEUW5nMGQ1bHorUmgzbHp3WW1zRUFFR0svY2RTazFVWkZ5U3JmRUxIWHNaOEJUYzY0cVpSWkZBdE4xRE9DQW5PQWtaNnlObEd0U1o1Z09sdjgza3diZkgvUmdRSi8zRWRqNWl1SSt5RTJPLzBuM0d2d01uS2Y1OHc1VDZMM2lVdXJKU1BJUnNqaFByOWV4MHQwWWF5VnNjV3gzS2kvQlRmeFNWREJiQlJwL1hDWjRRTzFEWnBNVzFuNjR5OVVucE9WeWROU1Y2WEZRY0pONWtFUVZGemhpUEQ0VUtZQVdzcEhiTTFKZTZwaTlmV3gwQzdGcmxRM05KRCtJQllSQVY2K05yajU2N2lwaW8xS0szZm5VUm1NVlJ5OWpCSHg0VWNqVDg5QU50TjVwbG4zSHk1SEhacnY4Y2JNbHFETDI0ZHRISytJcVRYZkVqU3EwK1RlY1hTT2M3TFlwUlJpc3FXendCUmorSzNqakk4cXdWN2tnYTR6dThtdUVwR2dNWTVUdTdIUUptbU5jeXZucHprdDBtOXpqZzlaSDFFdnhJNzliSkd6dnF6NDYwOGNwVWJpWVBEQ3ciLCJtYWMiOiJhN2NjYzlkMTcxMzcxOGNmMjllY2Q4ZTljYzk0ODYwMWFlZWQ4YjVkYTY3MDRmNDdjYzkxZWUwMjZiZDAwZjgyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRsN0JReFFRR1hMcXJZVkNZRGlGRXc9PSIsInZhbHVlIjoiK0c4OVJ1aUtzU1ZQamdEYW9kV2tnbUVYSVdXa2xyMG8xYmh1ci8xeHNVWHF4WGlxQVA0TldkVVhUNkZiUW15UW5pamdra29NbTVkVExDSGV1VnlLMGNXalRUWXQ0MU5DVktWZWhDNVhXUnMwZk9XUkc2OTBjZ0ZaOFU5ZS8wTStYYWNkVVl1OTk0QjJVclhKdTlVbDcyalltVnpDYS93RWxPUlJBN2Vxb2xURjRnWTZpMXVNVW1RKzlvNU9udXRNcHFHQjAvUnY1VzJTRHVSazVIdVVKNHVDb1ViUndMT0NlUHk4OE5QNVZpQWpxTXhwdFQ5S1o3S1NrSzVEbGdGQkxaMjVBUXNocFdLWE44R256NXVoc1ZoWjVaKytrUlVXN1dOTHZvUFl0TUh6ZVVkU1hhU3R2VlZ6bVlzdzBDd21ZUjdHWWV2ckI0Z0IwUFB5K2VjZ1IxSkE4T0lEcnJoNXp1VmwrV0RJSzRWYWtkOUNQMGlDd2dQZjl0OW54OUxJMnlvRmR0QmxCcXFrM05LM2tURmMya2I5dG5GQ3hQdnJ0dXpLelBsbFVzZW0ra1RVY3l2N1NHL1VwT3JOMEJ5ZUJON3c1THNiNmt0dmgvcmd0SmZzVGhHb0p5UXhmcnhLRzZjMmFlMk0xcnQvM2ozS3J6ZDl3c3gzUng4RGZlL1IiLCJtYWMiOiJmODgwYTI2MDg3NjFiNjAwZmYxNTFhYjQyM2U0MDE5ZDNkYWQzYzcxZTJhN2Y3ODVjNGFiMzE2ZTFhYmRmNzc0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFTV1Y3c0dLVzJLNllZYlhYRzZsSlE9PSIsInZhbHVlIjoidXgwRVRUVmRLRkRURmMzTnVPdVo2cElWL0MxUy94L3BRZXkyREtaU0tuRmtvMmE4VmxLR0RKVmlhWXUzMFBINFF0VVFwYmRyVVBEUW5nMGQ1bHorUmgzbHp3WW1zRUFFR0svY2RTazFVWkZ5U3JmRUxIWHNaOEJUYzY0cVpSWkZBdE4xRE9DQW5PQWtaNnlObEd0U1o1Z09sdjgza3diZkgvUmdRSi8zRWRqNWl1SSt5RTJPLzBuM0d2d01uS2Y1OHc1VDZMM2lVdXJKU1BJUnNqaFByOWV4MHQwWWF5VnNjV3gzS2kvQlRmeFNWREJiQlJwL1hDWjRRTzFEWnBNVzFuNjR5OVVucE9WeWROU1Y2WEZRY0pONWtFUVZGemhpUEQ0VUtZQVdzcEhiTTFKZTZwaTlmV3gwQzdGcmxRM05KRCtJQllSQVY2K05yajU2N2lwaW8xS0szZm5VUm1NVlJ5OWpCSHg0VWNqVDg5QU50TjVwbG4zSHk1SEhacnY4Y2JNbHFETDI0ZHRISytJcVRYZkVqU3EwK1RlY1hTT2M3TFlwUlJpc3FXendCUmorSzNqakk4cXdWN2tnYTR6dThtdUVwR2dNWTVUdTdIUUptbU5jeXZucHprdDBtOXpqZzlaSDFFdnhJNzliSkd6dnF6NDYwOGNwVWJpWVBEQ3ciLCJtYWMiOiJhN2NjYzlkMTcxMzcxOGNmMjllY2Q4ZTljYzk0ODYwMWFlZWQ4YjVkYTY3MDRmNDdjYzkxZWUwMjZiZDAwZjgyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170902334\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}