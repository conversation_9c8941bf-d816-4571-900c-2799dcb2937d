{"__meta": {"id": "X32a639682484ef20605b8d8ea458f7a0", "datetime": "2025-06-06 20:35:30", "utime": **********.574346, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242129.308491, "end": **********.574393, "duration": 1.265902042388916, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749242129.308491, "relative_start": 0, "end": **********.427102, "relative_end": **********.427102, "duration": 1.1186110973358154, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.427132, "relative_start": 1.1186408996582031, "end": **********.574399, "relative_end": 5.9604644775390625e-06, "duration": 0.14726710319519043, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43338200, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01414, "accumulated_duration_str": "14.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.53478, "duration": 0.01414, "duration_str": "14.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-76322756 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-76322756\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1116883225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1116883225\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-854829349 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242076252%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9GTHZGN20rVDN2c3oyaTRINnpIdXc9PSIsInZhbHVlIjoibjcyNCtYZ0tPMzJPRkxhU0RsVmtTWE9mVWwrZ01paS9NdWcvN2YwNDBLWWM1RTZGaExPTlFrMURCOCtGRmhteTRZWG9iYW9FdzRoQWN4cHFJckFQa3BtL0xZMmc0OHFZSHNVejF1QWJYZjVOQWhNc2w1ZDg3ZFJTT0k4KzQvcHpwakltQlBjOEtzTWtMLzBERWxza3ZqZXc0dzhKMEJ1OXdMNVdpZjF4UU8vUlNHd3F0emZvZDYyT21CWG5KakJ5VnZFbXV2ZS9CMUJna3gyNWxobXNQWU5TeUNtaFd6Q0NHcHRlZUY5VWJzYjJuNmFROVZHTXkrRlVTTWxndVM3UjVKcDlPeTM3MFlxTGMreWlCb2REYTZnR1BCOThxM1grVFFEa3lYYjYvTmJNbUhoN1dBbnNIdFQyTVhYT0Q4QjZhNU0rTHNLOHYzTkg4RCtuUGx1ZkNyV3NnV3R2SlBnbzBRSk4zK25Rdm1rMFBTaWxTdUVubm1uUzJuMUVULzB0NG5tWjhaa3ZGOSs0V09lU1Y5NnpQQzZ6TWVJV1BUZEp3NURRV1dLRW0waHhkTzVNNnFrdzErczR2NmVyWUx6SFBKUEpHWldpN2JCb0FITGYvMjc3WVo3MFNaZk9qV3h5U0I3OXo5UitIdlJSamRLQzVkQnMwNU1SYi9aaGhnNm4iLCJtYWMiOiIzYmYyZGY5Mjc3YjA1MDY2NzlmYmQ3YjFjOWZjODM2NDMyNTk1MDczYTZiNzIwYTk3MDA5MmQxMjk4OWY1ZTMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkVXV3R5N1JqUnpzM0pUdkxVUEVLTnc9PSIsInZhbHVlIjoiTVl2a0pEd1JZQjJtdXUrcHhRd2czaE55ZTVxa1Zwc3gzMjVEb2FSN09zUkhCOWVkMitLZUJFRjhEYVhmZXdzejk0NkxDMHh1dlFMOCtjWTBuelllU1ZxNGUza3JWdFhJd1o1ZDZlSDI2OG9MOHJzYzZKaUJQUy9VN1NPWlc2dWxjYWw0ekZSVXpVYXVQcktLN3FqenRXbXhRM2dLdXlkWFhzSmx2ai9aeGpHcjA4R0paNmxIZk5wekd5Uit5TUd6Q2JyMjVld3RPTy9BR3RTdUdrdmlQdWR6RHQ2Z012RzNWZXJoVmtpMkhmNmxJTGRHUTUwVUx1U3c1ODhPb0d3ZjFQS3IxTllHZHd4akpTdi9lY3lNaTNxd2xsZnI5R051T0V1Rk5NcjF6Nm05eWlxczdLNE9RbUlLcnVKZGE1aUlmM1FDR3U0UGdtYUlxd1NlWGZhNDhoSHowWWF2SGxrNEVBZThNa1JVZjhFZzUwV09uY0VOYzFIcmRtaGVlMVVWVmpWbG1lM0lFQ0lBRWVIYjhISkpnMHhrUEp5SXZraVdiMTJCbW5wZzM2SFl5TXV2bGRuaWlEUGRkOUVTTDN3WmFnTk1xTkhWbnIxNVkrci9qeVc3N0NmaEp3SE1rMlM5UjVSalMwanc3V2dGSTB4UHdOOFA1NVczT2xDakc2dUgiLCJtYWMiOiJhOGU0MmFjZGZlYmFiZDA1Y2NlMTBkNGRiMzJkMzdkMDVhMDk3NjVjMjk3MmE1N2E4NGVjZGJmZGM5NjcyNTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854829349\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1066525965 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066525965\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1468314444 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:35:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZwZzRwdVIxbVh4UHBSMEo3NEYrbEE9PSIsInZhbHVlIjoieFBIWGovak1mUGxHMG51WUtycGJiV1NDdWN5SHNDT3RXdmlPdUt0eEJka0JvK3lNZUE4bnRZbkxRU25uWEZmQzVOWlgxdG4rNkE3eHJ5NFJRZXFXTzd1M3dBY05jMDdYcnE3K2hJUzZBQjRSYll4dG5SNVFteUpOSWpheThid1RraGZJZjVheEp5b0pIZnR2aXdJb3V2akFKSjlHYkFmdUppYTFDQXNDWkx3eXR4UER0b0ZzM2RXeDIrbmdjTjM2amVTZzlOZFc4MmJFQ2UwSFp6Z0dkYm9JbldKbjFEdTFXNXFLYmU0MEsyNVhoRVdoRWUzc2pRRmxmQlMyWHBXRDVaelV3czAyRzk2S3diZzU3bU42T05nZ2N4UCtXTmVhbnAxZlRCUzZpWG5ubkhLL1RWL1FnWDlWZ2YxbEtnanB0cHh4VlNzNHo0N0RhbFpxLzJMczIxZjlOY25nTlFmS1V1Q0VYZW51R2ZVeVIwZWxyVWI1K3pBSXY2T1laWDFydUk2ZTdsZVRuV2dFa0VlWHhrN0dvMWEyOXYzQ01SUmg5MjNVanJzbEN5cVZ4VzBMalhLNkhjOS85VWx5S0ZKYnF3OWVNWlBXWVBmVC9iYzh2VG1FWWJtSkZCZG5zaHM4YkRaQXpsa2NFa1Fpem1qYUsrOExjZXNzWUJwMmx0bTMiLCJtYWMiOiIyZjM1ZmVkOGMzMTBmN2JmMDU4OGZlNzBmYzk1MjYyZjRkMmM0MDlkZDVmYTFjMDgyZjdhODQxOGFiMzBjMTNlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:35:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilhkcjc1bFZKb0RMbWJXcFNpSXFGMVE9PSIsInZhbHVlIjoiTnVGK2I1WER5aEVzL2MyZmpqUzRBMVdXblZVMTFYSUxxSlIvdzVVc2hNRmNJSEpaYitTa2QzS3NUZVk3UXlSMXhBMFBqWEgwT2xOWVVJSHFxSmRxbDMwdzVNTDc1SStzclliZ2w3UjZ0ZUtDUzlWc211V0wvajZTSGJoaHBUbkh3RTZiNWxrNVRJbDRITjBRWjBCV0RhMmNkSndMMTlKOXBWVGkzTnN3dzh0UFdEOUd0amM2OE9QVG5wTjJETkFDVVlRakF3cjBHZTByaFE1Qm53S1ZuSzhDYjhVYnppQ1E4SmVwc3Y5eWZUb2owaFdyV1JucVI5eVNPaHQybkpZZGZMaVZJOXZnYXJVQmEwM2NwZW5rTHBPV056UnRHbDFWUGpTRUdwYk1Na2d5Q0N3RmtsQUN0cjhhZkpFbmhJRzNjeStOMUNmRVZpRGZ4LzlvR2d5MVlha2Z0VStGSkgyZmhvRDU0Um9ONGVUMkJlMlpkSzhNSnVUZHVZTzNiT1VMdEkrQlBMT2tHa0RQL3IrK0pKQ2tnS0V0c1J6cTJTSGZmNmE1UW04YjFUeHQzQWVSVUtMdm44ZWhJMDEzTy92OFNtcUlnNjNqdXU2UjEzY1p4K1crcXJlZWk3U0xRN3Y1bms4dkJRbVA4N1pYanBPUmVWNkhDM21pT3RGcWVYRXoiLCJtYWMiOiIxNWQ4MjcwMjg0ZmFhMjdhMGZiMDhmYmVkNTE0ZWNjYjczOTllYThlMzkxNGMwYzM4ZjdlMGE4MTAxZWVkYmExIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:35:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZwZzRwdVIxbVh4UHBSMEo3NEYrbEE9PSIsInZhbHVlIjoieFBIWGovak1mUGxHMG51WUtycGJiV1NDdWN5SHNDT3RXdmlPdUt0eEJka0JvK3lNZUE4bnRZbkxRU25uWEZmQzVOWlgxdG4rNkE3eHJ5NFJRZXFXTzd1M3dBY05jMDdYcnE3K2hJUzZBQjRSYll4dG5SNVFteUpOSWpheThid1RraGZJZjVheEp5b0pIZnR2aXdJb3V2akFKSjlHYkFmdUppYTFDQXNDWkx3eXR4UER0b0ZzM2RXeDIrbmdjTjM2amVTZzlOZFc4MmJFQ2UwSFp6Z0dkYm9JbldKbjFEdTFXNXFLYmU0MEsyNVhoRVdoRWUzc2pRRmxmQlMyWHBXRDVaelV3czAyRzk2S3diZzU3bU42T05nZ2N4UCtXTmVhbnAxZlRCUzZpWG5ubkhLL1RWL1FnWDlWZ2YxbEtnanB0cHh4VlNzNHo0N0RhbFpxLzJMczIxZjlOY25nTlFmS1V1Q0VYZW51R2ZVeVIwZWxyVWI1K3pBSXY2T1laWDFydUk2ZTdsZVRuV2dFa0VlWHhrN0dvMWEyOXYzQ01SUmg5MjNVanJzbEN5cVZ4VzBMalhLNkhjOS85VWx5S0ZKYnF3OWVNWlBXWVBmVC9iYzh2VG1FWWJtSkZCZG5zaHM4YkRaQXpsa2NFa1Fpem1qYUsrOExjZXNzWUJwMmx0bTMiLCJtYWMiOiIyZjM1ZmVkOGMzMTBmN2JmMDU4OGZlNzBmYzk1MjYyZjRkMmM0MDlkZDVmYTFjMDgyZjdhODQxOGFiMzBjMTNlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:35:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilhkcjc1bFZKb0RMbWJXcFNpSXFGMVE9PSIsInZhbHVlIjoiTnVGK2I1WER5aEVzL2MyZmpqUzRBMVdXblZVMTFYSUxxSlIvdzVVc2hNRmNJSEpaYitTa2QzS3NUZVk3UXlSMXhBMFBqWEgwT2xOWVVJSHFxSmRxbDMwdzVNTDc1SStzclliZ2w3UjZ0ZUtDUzlWc211V0wvajZTSGJoaHBUbkh3RTZiNWxrNVRJbDRITjBRWjBCV0RhMmNkSndMMTlKOXBWVGkzTnN3dzh0UFdEOUd0amM2OE9QVG5wTjJETkFDVVlRakF3cjBHZTByaFE1Qm53S1ZuSzhDYjhVYnppQ1E4SmVwc3Y5eWZUb2owaFdyV1JucVI5eVNPaHQybkpZZGZMaVZJOXZnYXJVQmEwM2NwZW5rTHBPV056UnRHbDFWUGpTRUdwYk1Na2d5Q0N3RmtsQUN0cjhhZkpFbmhJRzNjeStOMUNmRVZpRGZ4LzlvR2d5MVlha2Z0VStGSkgyZmhvRDU0Um9ONGVUMkJlMlpkSzhNSnVUZHVZTzNiT1VMdEkrQlBMT2tHa0RQL3IrK0pKQ2tnS0V0c1J6cTJTSGZmNmE1UW04YjFUeHQzQWVSVUtMdm44ZWhJMDEzTy92OFNtcUlnNjNqdXU2UjEzY1p4K1crcXJlZWk3U0xRN3Y1bms4dkJRbVA4N1pYanBPUmVWNkhDM21pT3RGcWVYRXoiLCJtYWMiOiIxNWQ4MjcwMjg0ZmFhMjdhMGZiMDhmYmVkNTE0ZWNjYjczOTllYThlMzkxNGMwYzM4ZjdlMGE4MTAxZWVkYmExIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:35:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468314444\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2146875700 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146875700\", {\"maxDepth\":0})</script>\n"}}