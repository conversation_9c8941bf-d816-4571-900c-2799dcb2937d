{"__meta": {"id": "X049dbdbe4fc501d013a4484b33f19bab", "datetime": "2025-06-06 19:12:36", "utime": **********.354325, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237154.012916, "end": **********.354361, "duration": 2.341444969177246, "duration_str": "2.34s", "measures": [{"label": "Booting", "start": 1749237154.012916, "relative_start": 0, "end": **********.097805, "relative_end": **********.097805, "duration": 2.0848889350891113, "duration_str": "2.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.097853, "relative_start": 2.0849368572235107, "end": **********.354365, "relative_end": 4.0531158447265625e-06, "duration": 0.2565121650695801, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43104800, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.05172, "accumulated_duration_str": "51.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.263843, "duration": 0.05172, "duration_str": "51.72ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XVsuooi9r4Xbc3TiFLlAVnTr0BIPLZg0Hyn0zo5r", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1536463212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1536463212\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2035191235 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2035191235\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1895159582 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"202 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895159582\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1897072064 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897072064\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-569808815 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:12:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBFWlBVd3RsVVpXcmJ5RkNXNERINlE9PSIsInZhbHVlIjoiTEplL0JKUzluVjZ5R3E3QzJLNVVmeVpOTkdrbGZUeTNpNDVYUFlhUi85VVkzV3ZEU216UHQzOGpVdzl5bVhMWlpJUFZIY21RejQvTDJxOUl3WFd2NjQyQW50MTdaSCtzTlk0MVBwZWZFSXhLSU51Q2gyYnNQME4yWWJSVm0vcXZvUVpSdzZqZEtqSDdMWHZjYisrelFkajJQU05SRDVXSEJUTEE1RjFyKzc0MzJPRVU3NEh5L1FFK0FZMGdHdnNSSU5qT1FjeXR4dWRpOFVTUG4yS3cyTXlhVGdqeVBBK3VUcVVQUVk0K2ZyTWg4UjVNckQrMThmWXVoUWZHR2JYYUFoTGN4K1FuTUhFanY4RzlSNnlGMHNhNldBa0hQMDN2Q0VlazhkSGx6WDdMbVl4QWIwakxYSG1zNFNJZ2Y0VW1HYk96WnNXNnlOS1J5VlRGT3JEemJRSTdTdHY0d3VUZEd4STVrcFBwSVVuVjloVGtzbjRpRDV4aVNDU25vM0xCdTl2NS9pRm1ZQlNMOXdCcGdFNE9NSkw4bWtDREhUKzFHVDZKNDNENTlSRGRaNmQ5TjBQK3ZKaC9tcytwcTBFTGRzNXRtUnl1akpYN2dPTnRLRkJxUyt3cFlpaVdjVUFHOTBldUpCQ0ViRDFBTGluWVhtam1BaHppbHp3NE9VTGEiLCJtYWMiOiJjOWY4Yzc5YzY0YzIzYjZiZjNmN2RkMTYzNDdmYTgyODgwYWRkNGMzYTMyM2E1Y2Q5YTE3M2Q4YmVmODRkYzcyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:12:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtlN05uMmdQRy8wdG1HNm9LSUhIclE9PSIsInZhbHVlIjoidXR3NVlkaFVIMnVoT2o4a003N0N5ckRDRDdDRy9GU3BGNWhta0xPM3dRMDlPbTBhUzUxN1FzUlZtcHE2NnExbFhSOTVadXA3OUh0WjB6dzU2UnR1ZnEwY1crVWVFd0JMSnF2Z1E5ZGwyVE1JMGowYnpYd2hTcHNHa3grWkR1SmdPcEV5dksxOGpaTjhEUExDRUxIV1BYbWRDb2t6eHU3amNBQmZhRW1hTmM3bnNqTU1jTDVCUU1BOVlySVdxeFZUL01jeWlYSEJiWjNTZVd1aXYraTd4WFZnM3pBM3pSaDJvcGxqd0NLR2F4M2xqSlJFWGdMY0cxalZBcmNhb1c3a1lTYXJwbDlKVFZ4SytMVkx1UkZKeUkyS01DandWNXRmeTB1b1ZMMnlDc3dGY0hxbUw1a2d5TEh5S2s3MFk4dXNPMGpHQmovenZ4WngzZVQwQmVEVWo1K002Q3hhMWxjSEMzTnZFK1kzWmtjb0xuR2xwVmpkR1pBRG44b1pwc3JFeklTT29uWUs4YkZ0QnVYMXN6b2k5bTJGWjJiNmROakNVM1dDOUlEWjJ4cUppUDNqZmN2b0d5QlJ4QlNUVDFGUk1XSEZ1SVplS2NXRTltTVN0MG55RkhmVE5Hak90QXdzV1pGVVE5dkhDQUg3cklGZ2RkcmVjWThsaEtEaW1ocGwiLCJtYWMiOiJlYzRiZTM2ZjEyNGJmNWNiYmYzYTlmMTYwNzNhZjg5ZGY1NGQxYTc2MmJiZWM1NjVmZmQyOGE3NTBiMmRkNzZiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:12:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBFWlBVd3RsVVpXcmJ5RkNXNERINlE9PSIsInZhbHVlIjoiTEplL0JKUzluVjZ5R3E3QzJLNVVmeVpOTkdrbGZUeTNpNDVYUFlhUi85VVkzV3ZEU216UHQzOGpVdzl5bVhMWlpJUFZIY21RejQvTDJxOUl3WFd2NjQyQW50MTdaSCtzTlk0MVBwZWZFSXhLSU51Q2gyYnNQME4yWWJSVm0vcXZvUVpSdzZqZEtqSDdMWHZjYisrelFkajJQU05SRDVXSEJUTEE1RjFyKzc0MzJPRVU3NEh5L1FFK0FZMGdHdnNSSU5qT1FjeXR4dWRpOFVTUG4yS3cyTXlhVGdqeVBBK3VUcVVQUVk0K2ZyTWg4UjVNckQrMThmWXVoUWZHR2JYYUFoTGN4K1FuTUhFanY4RzlSNnlGMHNhNldBa0hQMDN2Q0VlazhkSGx6WDdMbVl4QWIwakxYSG1zNFNJZ2Y0VW1HYk96WnNXNnlOS1J5VlRGT3JEemJRSTdTdHY0d3VUZEd4STVrcFBwSVVuVjloVGtzbjRpRDV4aVNDU25vM0xCdTl2NS9pRm1ZQlNMOXdCcGdFNE9NSkw4bWtDREhUKzFHVDZKNDNENTlSRGRaNmQ5TjBQK3ZKaC9tcytwcTBFTGRzNXRtUnl1akpYN2dPTnRLRkJxUyt3cFlpaVdjVUFHOTBldUpCQ0ViRDFBTGluWVhtam1BaHppbHp3NE9VTGEiLCJtYWMiOiJjOWY4Yzc5YzY0YzIzYjZiZjNmN2RkMTYzNDdmYTgyODgwYWRkNGMzYTMyM2E1Y2Q5YTE3M2Q4YmVmODRkYzcyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:12:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtlN05uMmdQRy8wdG1HNm9LSUhIclE9PSIsInZhbHVlIjoidXR3NVlkaFVIMnVoT2o4a003N0N5ckRDRDdDRy9GU3BGNWhta0xPM3dRMDlPbTBhUzUxN1FzUlZtcHE2NnExbFhSOTVadXA3OUh0WjB6dzU2UnR1ZnEwY1crVWVFd0JMSnF2Z1E5ZGwyVE1JMGowYnpYd2hTcHNHa3grWkR1SmdPcEV5dksxOGpaTjhEUExDRUxIV1BYbWRDb2t6eHU3amNBQmZhRW1hTmM3bnNqTU1jTDVCUU1BOVlySVdxeFZUL01jeWlYSEJiWjNTZVd1aXYraTd4WFZnM3pBM3pSaDJvcGxqd0NLR2F4M2xqSlJFWGdMY0cxalZBcmNhb1c3a1lTYXJwbDlKVFZ4SytMVkx1UkZKeUkyS01DandWNXRmeTB1b1ZMMnlDc3dGY0hxbUw1a2d5TEh5S2s3MFk4dXNPMGpHQmovenZ4WngzZVQwQmVEVWo1K002Q3hhMWxjSEMzTnZFK1kzWmtjb0xuR2xwVmpkR1pBRG44b1pwc3JFeklTT29uWUs4YkZ0QnVYMXN6b2k5bTJGWjJiNmROakNVM1dDOUlEWjJ4cUppUDNqZmN2b0d5QlJ4QlNUVDFGUk1XSEZ1SVplS2NXRTltTVN0MG55RkhmVE5Hak90QXdzV1pGVVE5dkhDQUg3cklGZ2RkcmVjWThsaEtEaW1ocGwiLCJtYWMiOiJlYzRiZTM2ZjEyNGJmNWNiYmYzYTlmMTYwNzNhZjg5ZGY1NGQxYTc2MmJiZWM1NjVmZmQyOGE3NTBiMmRkNzZiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:12:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569808815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1299668173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XVsuooi9r4Xbc3TiFLlAVnTr0BIPLZg0Hyn0zo5r</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299668173\", {\"maxDepth\":0})</script>\n"}}