{"__meta": {"id": "X16df792e7772b3983e55d0d8dfbdac92", "datetime": "2025-06-06 19:24:52", "utime": **********.975153, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237891.335537, "end": **********.975185, "duration": 1.6396479606628418, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749237891.335537, "relative_start": 0, "end": **********.776157, "relative_end": **********.776157, "duration": 1.440619945526123, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.776175, "relative_start": 1.4406380653381348, "end": **********.975189, "relative_end": 4.0531158447265625e-06, "duration": 0.19901394844055176, "duration_str": "199ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44800096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00962, "accumulated_duration_str": "9.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.850022, "duration": 0.00554, "duration_str": "5.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.588}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.891743, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.588, "width_percent": 10.915}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.923958, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 68.503, "width_percent": 19.647}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.947984, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.15, "width_percent": 11.85}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1848974971 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1848974971\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1986658056 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1986658056\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-389440575 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389440575\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1405656709 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237880080%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldiR3l5ampneVR6ZlRVZmpNR2pxMEE9PSIsInZhbHVlIjoiakluQjZEK3hmRmo4bE8yam1iMGZ6ZTExbTFXUUVMU3k0dkFNcDVsYzlUMzM2RzU0OHdyNjAwV0krL0hvVWhVQUNINVpRWkRDanNxR2pHdlhVRDArUVNvWUptYUZVYVNBdDE0UW9PNStjdkV2QkNmWlFHdVRXVEFmYTJNWitKbGV4VlNZZjk3L3dVbkMzTW9hMHFheHR5NGMxWmRZZlVVd1d1Z1A3cS81WmJ0bHUwd3NyS2ZRd1Bwc0NIOGpZZ1R3ZFRVMFFQc09FV0hIYmlRRFp6SnlBOWNtVDdndVk3a3FTdVVITEhqQTVNbjZ6RVZTanpNWHM4Umc3dVlUcnRvbHBMV1Q2SFdOTkVnRDl4NFpSKzg3dTVySDgyQzNNVnd4N1VMcy9SQzVxbmFlV2gyM2NMcHJUVVY1eXVkTzR4elRBWVp3Z2ZoZkM2b245TXBnbjJLSXEzUzJ2NWVob1VtaTVEQ0J1THpGbjVpeWxiU2VmamRqN25wbzg2UnFZRUVpZllYMmFDRnhUaEVGakZpZXlXcEhDa2dTYkMyWWtKcWVpVlJxQXdxeXc3VmVENVBDZTZWeXNrcjNxVVQ1cjVzNEJCcTQyTUZmSndBNmpKdldwbnRSRkpEOGd6dnFmMXpHUUFSeVVOTDVTTk5oNzdqUzBYU1ZnKzE5MmtLZDUrcUQiLCJtYWMiOiIxY2ZkNTIyMjczNWZmYTJjZjIwMDhiZGMwNzEwMDgwNTYyNjhlMDk4MjdjNzZkNWEyYmM4ZTdjNmYzMjhhMjJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImlRZEpaS2tUWnpJN3VNWTZOMjBpZ3c9PSIsInZhbHVlIjoicGNteWVJcTlGTHllWEJDTTNFRmczN1hnSGdsOG1vRnZ1eEhYYlFNaGpQWkVuZlZmblAxL0EzTmdGTDdPM0hwTExMTFhUYjE1LzdrSTh4S3BQUHg1U09yRnllcXlJc3NVbVB3ZUZGZzAya1hpM2ZROHJhRnBVUjZsNndkTlQ3WEEreEFmVTFLcGxaRU5oZ0xjQy9QR281dDYrRFM0OE03Q3pBcWdaUmd2ZzBHaFRVSjZuS1pIOWhuR1AyRUxScWpKc2U2N1NaRXlUNGQycFl4U0U0VFVyS3kzRmZNU0dCSHgvb204dDJ5TlZIL3B0WDcwRkZjZlBLZENpM25rNXYvamN2a1NxRk5Gd1VtUFNWZXBOVUYraFlNODNBTWFJUjdNempTTjNNNnFiRC9QOWFiSFluanVxSGxDdGxWK1h6aE5od0k4VnFoeWNRc1VUU2FhUnNnZlJpaTBtanU1VE0zNGV5dWNSWGVtWUcrSlc5K2FqRXJuandKWGJtMWJXc3FrcThIOFdrbE9PQXF2RUsrYlgwL1V3VnEwZmFNd0pJa3pRQmlkendmNXRLSGJOUDBZaFp5c24ybHF5Q2dFNHJzL0oyMFE3RUhId2g5QmVXM3daNmdwS1FoS1pTNzB2Rnh4UzM4UW03c1pEOG1nSnFUcm8yK3JJZldLMURyZXJ5a3QiLCJtYWMiOiI2ZTYzMTEwMDAzZTc4MWYwNjJjMGJiNjg4ZDZjZjc2ZWVhZjU3ZDcyNjliMmJmOWY3ZGVjYWRhNDA1OGM0ZTYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405656709\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1358491277 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bqimaRKNMYDaY70bMn4neav5Q7SUSQNahmKxKJLG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358491277\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-768078036 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:24:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdhNmNIK0tQTVY4enZ2SEJQcmQyRXc9PSIsInZhbHVlIjoiMmNON0ExNnF3Z0lYMlNUV2dVQUxUMVBrUVgxbGptTU1QVWowWU82SjhkZTF5akZ4aXd0RG5vck5NTXRyOHpWTXFvSTVuaTdwdFRnblpaeU9RMHB1TFZXbUcrQkx6WkVwNzJOSlljMldxckw4M1BpSEZ2SERUSEM0WXBLeGgyUnlMQ1g5cU5WYzMrTlZjblhuK3BvVU1aSzhVc0dVZUtHaGMwQ3NkeWI4RUpTaFRTL0NiWUtWbkNhZnRGVVlVcHZvYlE0cXFUV3YzYXZ6YzVvc0tvM0dTY0Q2Y1loa3hBY3E3RVV0UnZHd2l1V3dBV1JQUmdWWll5dkVtNmxPNUdDR0tndnI2TEdlbVcrQ0MyaW5NQXEzRm45YlhjMXBnSW9DQXQ5TDdzUWdOQVhTZkE3ZUhXajN3SG90MUY3aGxjVzRSUVRhWmNwbnRzWnFSSEZrTnpDcVd5TktaQk5hcTNnajIzSHJ1cEdVbmJCYlJDQTdEUkNZeURhellBelRsV01vYlhEN29kWFl6eUY3cFRvQVArbFc0NXBZdmN0Q05LU29qUk9FTi91eTVJaEdhNElQY1dvU2l5enVmbW8rek5yMkdVVkF4WHNIdy83SS9sS3p1ZEYvSkE4anhiVUhkeDd6UkxQM25vMys5YXJkMHFGZElMMXVuRk5IQWhRVFhWQ1giLCJtYWMiOiIwM2IyNzEwNjljOWY2MDI1ZTNlNTg0MDg2ZGViNGJkY2U3NGFlNzI1NGIwN2M4YmMwZTI3OTNjZWNkMzYxZWUwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFtUVIvNnNhSHVQUVBDY1JQQVlKTHc9PSIsInZhbHVlIjoiOVhsdXBUK0hkTVlNTldoUk9RQlp5M3I3YXlYTlh4NTJVUko1SDNsYWZ5RGdCc0s4TDdiT2JRUXcvNDdSajFmSUFCK0FYWmxuMXl0eTNsVk1uZmNIOGM5SE5vOW5yRUhVNm9BSERoeE44allZU2VhTTdVUWU2YVE0aWdDSmJ3SlVaMzRLZUUvK1M0a0EvV1pCK1JleTRMVHVZUWthR2lkREFoaW9OSU1JZFkxR1N0T2F5YnB5ekNWb3piNXVmRmtTSGlWY0swdlljM2ZiU2JzMVZsNENxYkU0anBqY1pXV0tpNTgzWUphdlVtL3gzaWR4RGE2bzF3TW0rVFdXTEpPelBYc3lQYUozMzd4UjZUSlFTclVQRXRLcU5sd1hBV3YrdDRIengvUnpDbEtlQ3NyLyt0Y0EvWW9IbWtqVHlZTFB0VXdqYlRKVHI4WlZpVEVtOWRVK1hldVFwNnMzQjVHcmRqRC9ubEFyRmNXYkNyaU5sUU9XcHJXYWtscmg0dlJtME5lRTZReW5Sa3djYmFnSGt1d2NaajNqb2htM2VPZDU2NUM3LzBTdHpqY2NDcFRHVHlkcGNCc3duVWNhZVlaa1A0ZXovKzFjNXROVXN5VmRMaHdPOC9VQ0gzczBVWi83ZEFZVzJnS1dzR1N0M1RpaW03UUVMempzc0JabVRZWWEiLCJtYWMiOiJmYTgwNDYyODNlNWMzZjVlNjBmM2E2NzU0MmE1OTczYTQwNzliYmI0MWNkYTcxNjZiYzQ2Yjk5YWQwNTFkM2YyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdhNmNIK0tQTVY4enZ2SEJQcmQyRXc9PSIsInZhbHVlIjoiMmNON0ExNnF3Z0lYMlNUV2dVQUxUMVBrUVgxbGptTU1QVWowWU82SjhkZTF5akZ4aXd0RG5vck5NTXRyOHpWTXFvSTVuaTdwdFRnblpaeU9RMHB1TFZXbUcrQkx6WkVwNzJOSlljMldxckw4M1BpSEZ2SERUSEM0WXBLeGgyUnlMQ1g5cU5WYzMrTlZjblhuK3BvVU1aSzhVc0dVZUtHaGMwQ3NkeWI4RUpTaFRTL0NiWUtWbkNhZnRGVVlVcHZvYlE0cXFUV3YzYXZ6YzVvc0tvM0dTY0Q2Y1loa3hBY3E3RVV0UnZHd2l1V3dBV1JQUmdWWll5dkVtNmxPNUdDR0tndnI2TEdlbVcrQ0MyaW5NQXEzRm45YlhjMXBnSW9DQXQ5TDdzUWdOQVhTZkE3ZUhXajN3SG90MUY3aGxjVzRSUVRhWmNwbnRzWnFSSEZrTnpDcVd5TktaQk5hcTNnajIzSHJ1cEdVbmJCYlJDQTdEUkNZeURhellBelRsV01vYlhEN29kWFl6eUY3cFRvQVArbFc0NXBZdmN0Q05LU29qUk9FTi91eTVJaEdhNElQY1dvU2l5enVmbW8rek5yMkdVVkF4WHNIdy83SS9sS3p1ZEYvSkE4anhiVUhkeDd6UkxQM25vMys5YXJkMHFGZElMMXVuRk5IQWhRVFhWQ1giLCJtYWMiOiIwM2IyNzEwNjljOWY2MDI1ZTNlNTg0MDg2ZGViNGJkY2U3NGFlNzI1NGIwN2M4YmMwZTI3OTNjZWNkMzYxZWUwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFtUVIvNnNhSHVQUVBDY1JQQVlKTHc9PSIsInZhbHVlIjoiOVhsdXBUK0hkTVlNTldoUk9RQlp5M3I3YXlYTlh4NTJVUko1SDNsYWZ5RGdCc0s4TDdiT2JRUXcvNDdSajFmSUFCK0FYWmxuMXl0eTNsVk1uZmNIOGM5SE5vOW5yRUhVNm9BSERoeE44allZU2VhTTdVUWU2YVE0aWdDSmJ3SlVaMzRLZUUvK1M0a0EvV1pCK1JleTRMVHVZUWthR2lkREFoaW9OSU1JZFkxR1N0T2F5YnB5ekNWb3piNXVmRmtTSGlWY0swdlljM2ZiU2JzMVZsNENxYkU0anBqY1pXV0tpNTgzWUphdlVtL3gzaWR4RGE2bzF3TW0rVFdXTEpPelBYc3lQYUozMzd4UjZUSlFTclVQRXRLcU5sd1hBV3YrdDRIengvUnpDbEtlQ3NyLyt0Y0EvWW9IbWtqVHlZTFB0VXdqYlRKVHI4WlZpVEVtOWRVK1hldVFwNnMzQjVHcmRqRC9ubEFyRmNXYkNyaU5sUU9XcHJXYWtscmg0dlJtME5lRTZReW5Sa3djYmFnSGt1d2NaajNqb2htM2VPZDU2NUM3LzBTdHpqY2NDcFRHVHlkcGNCc3duVWNhZVlaa1A0ZXovKzFjNXROVXN5VmRMaHdPOC9VQ0gzczBVWi83ZEFZVzJnS1dzR1N0M1RpaW03UUVMempzc0JabVRZWWEiLCJtYWMiOiJmYTgwNDYyODNlNWMzZjVlNjBmM2E2NzU0MmE1OTczYTQwNzliYmI0MWNkYTcxNjZiYzQ2Yjk5YWQwNTFkM2YyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768078036\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-96859272 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96859272\", {\"maxDepth\":0})</script>\n"}}