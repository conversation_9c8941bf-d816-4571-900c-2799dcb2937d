{"__meta": {"id": "X1e4d52c5d432757b0f08a554da53b442", "datetime": "2025-06-07 04:32:44", "utime": **********.870851, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270763.36551, "end": **********.870879, "duration": 1.505368947982788, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1749270763.36551, "relative_start": 0, "end": **********.638644, "relative_end": **********.638644, "duration": 1.2731339931488037, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.638664, "relative_start": 1.2731540203094482, "end": **********.870883, "relative_end": 4.0531158447265625e-06, "duration": 0.23221898078918457, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767208, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02213, "accumulated_duration_str": "22.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.747879, "duration": 0.019969999999999998, "duration_str": "19.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.239}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.820556, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.239, "width_percent": 5.648}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8444319, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.888, "width_percent": 4.112}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1038884581 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1038884581\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1960419662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1960419662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1540394736 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540394736\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1563619513 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749270085073%7C10%7C1%7Cs.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkJZbzRsY0lNUGVWc1EwOFNublZwdnc9PSIsInZhbHVlIjoiUlhxdDdwbUd5V0ViZWREYTUwS0MwcFFPeWo0aFErUWhxSGhjSjdndVkvNGJVMk05R0FwdW0xaGJhWTN6NXJQMU9WL005Mk5oNjFBU25UTnpnM3ArZzBtakU1bXkrRVRKUDNWY2NaV2NlL1dPbndKR2R0dmFwMmJOWjIvR0JFSzg4Q2lhL3Q1RGhERDN5bzgxQ05VV09ndFl5MGYrc3I0RWJLRzZnbU1aQmJiUzNCZEFsNG94K04vMDlBcHBuazlzNllkYXhsM2hOaXcxdU56RHNuTk4zQkZIUnVzeG5QVnRaZE4zaVVCMWthSXlhSGZYTDNGLy9pVjRqcUlMek1IWHEzVnFxU0pJQVpTMXMyUFFjZXVHaGlySkRDWnRTVS84dFZGWUNwTWZQS216QVRIR0hRbVRkTFBudEQ4dUlNQW5idlAwa1BnVFJsL3A5MEdLVzRCVC9ieXNycDRZK1lWUmtOK25udW9Hb3V4OGF5NGkrbjhZcFB3dmhZSVd3VFZYOC8rV1B3L0R2TzdkeFp5bzFBc0NEYnNGbklIYUZhQm9HbFZYeTE3cWpYTHowTmV5ZEVVYTJlZ0JsaGdmTC9RSXhPK2JOdVFUaXJVNlREL25qS21Ca2JBK2JmSG5tSjA4anA0Ri9HZGJxQTd4UW9JLzFIQ29kZjFPL2pSa01lNDEiLCJtYWMiOiIwZDAyNjk2YTBmYmFiMzFkYmFiYWUwMTc2NjY1ZGYzYjcxZTUxMjliNTgxY2RiNDEwNjU5MjY4MzJiZDBlMTFkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRqOUNQYW8zcnlVNmtBcmFHQ0FZYkE9PSIsInZhbHVlIjoiaW5uQTdxQStzamlCSXgzd2VsVHY4bHpkVzdqcVNVRlBTZWNiVTNBSkJUdkNubWtiVHBNUXRoMDExWVZsSHFlcXRzNTlmd281NjdQUHhQSy9GcWlBd1JML3NwOWpQMEJOUTNFYlJyM3lOaTl3eG5rN3VnR2s5UEVvQTlvWDFnbXpYTjRDUUxuSDdJZE9pNjdEZFVST1pXVWViTXBNYUZHRjZ5eDR4ZlNORm5nU3NUZVZ0ZkdQMFhRb3dvcTRNZG1aZEd1U0hFdFd2ZEg5VFlNSCt3R0tqVTRqR0lZQWJ3cTJQSmFIeG53MVFoeDdCdjBBWDd4c203ZVJGRnJtWk13NzV4SzlxMEJMb2F6cmZRM0gzRnZId2dqMEJjV0I5Z1JEVW0xV09BT2MzQzZjbHR4cHZ2cElpUVVUdGFjY1M5Y0RDZ0Q5TjRQNlQ0Q3VkYmFQTW82bVVzS3B5VTBGVlBxL3owNmhiZHRpWVNvWnpLNnFsdUd4NFM2VmpHZTBoOThkODYwajgvLzBPODJMUDhRZ1drcUN0M2dtY3MvbUM1RHM3TXlCTmt6VDNpYnEzUEdvWXJVd3RRL0VNQlFjQUppRlVIckhybTVURitqamZ4dGpTU0FiNThLZjdKV0ZvQzB4aXNLOHBRdzkwb2xpUndNYjluZXhCM0o1dzNNK3JaNUMiLCJtYWMiOiIzZTMyYTcyZTU1OTlhN2Q5YTYxZTJiMDJhZmQ4ZDkzNTAzMGU4NDRmMjVhMmJjOGFlYWI1Y2FiMWE4M2E1NzJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563619513\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1282834062 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QF4lxftbqb9bEiJQUWBNsJwytQRufosJhQtAJ0pY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282834062\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1233808033 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:32:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFVWGg0Ynp6M2t6RFBTZkg4cjJ0NWc9PSIsInZhbHVlIjoiOHAvWUswQWtYQjA3WFYzRU8wQ1hoY2tpcDByajBpaUhxU3k0RFBIcmUxeXZwc2x4Z2JMTE5SYmx2VnNubEk2RWhEV0Rjb2lTOFZCV2xSYzdTb0R1YnlOMTdWYzB0WkEvcnJ2Qm5nUi9VLzNkQkNsUFhWQ1U0dG1Ed2tJUG1ib1dOaHVaZURVaEtibW9LUTBrZ3h4MEd4ZXhNOExvTFJCQjdpTVVrQmMyamV5eGd5eGZ0V2gvQkMvUlV0RFc3N1ltb3ovM3R1Nm8zQ2thWDAyajlwdHlLQW1CWGZWMnJ6K2lrQVQ5L1NwMWxOYTVBUkhDUEpIcnVvbTZnU2ZXbERMM0ZqY3FtY0RZalBOT2FjYjRjUmJWZC85U1JZZnhRdEkxZzd1WXpYYjJwc2pIbFpRem1uZzRtOGhJU0h5QUp6L2RBUWZ0OFpsSWMyL2puVjF0bE53ajFvV0FoeXZPREU0ajdzcnNQZE16MVBTOUc0aEN3QzF1dFpVTUJRUHorMysyTUtIclUvNS9WYUhXaTdFcXJSZ09DeDZyN0FQVWF1Qm1QM0tVSC9wWDNVcWt5SGFLVXlEOWtXbkVJK0NDUlBrYXpLT1hoSFVxVTd6OUg3MHdIOG9CVkhHSWNEdis2WVdnYzAxdDAxbXhqVzEvUWprcUZ5UnBPc0JEeE83WTFURzAiLCJtYWMiOiJmMWUxNDZkZjFkOWYwODdjODUwN2MwODVhOTEwZGM3MWJhYmExYWYzZjUxZmIzZjk5ZjdkMTk5NzE1MDY2YmYxIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdOUk56d3JSakJSbUFXbGg4Nm9jdmc9PSIsInZhbHVlIjoiQ2tPMFhpanA5U0FBa3BiRHJ1NGlYcVdRcWV3WGRhM3F2NEJqR1E0ZjJGcHZEdGVXc2RSWnAxY0llZ0c5UFJoWnBDSzhFS2hXaU5xWnNxOXYxSjMvOWExQzArR3RUQmZXWmVnTHdJZUJWNVBEM0JZUW1uNzdqSUdOMzJFaHFraWdRM2dzRkVZTGdFcHhPWmVFYzdSRk1vMmlhb2VIMVlzczBYbXYzWkJsdnJpUDkzTXovaERZVjdKNktzOTlVUmpBdVJ1Nk52TTNlRE15MENmZkNLL0QxSDJkaU50YnJ5d1ZaU29pdkdHcTBCT0Q2OHlNWUtUaFV3aTUxeXFodlR1bTZZdjlXNUt3NmlSZWE5d25jVWNlMUI2MFp0V29WYWYwaFhkYkFybTFMNTU2RlVId1ErUmFBZlI1Vi9MVGNDMGJ1eGlEWmNFK2tjNnBMOXNGbnlvaTQzWmhOVEl6QUI2eWxzYzQ5RThYOXgwdFBaVE1weGtDMVA5elFvWkVKYzFOWEV2dU9nYnlPMTZ4Z1hXekcvNmtVSnBnMnZtN3FQbG5XOEhzTkVtMHN0QTlLZmhWSlpQVWdtRlA3cEhMcVY5K2tWZm16cWlaRU5kTzVFNitPb0QraDgvSGUzclpJeXEvKzRRUkM2aXVha2RPdlIxQy9ZZFB3UWxOTGhHT0J2TEoiLCJtYWMiOiI5YTk0MmE0YzRiMGMyODZmMzMzN2FjMDY0MDU2ZmJkZmMwODY2OGE3NzE5YjQxODQ3MmNjZTg5NGJmZWYyYWQxIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFVWGg0Ynp6M2t6RFBTZkg4cjJ0NWc9PSIsInZhbHVlIjoiOHAvWUswQWtYQjA3WFYzRU8wQ1hoY2tpcDByajBpaUhxU3k0RFBIcmUxeXZwc2x4Z2JMTE5SYmx2VnNubEk2RWhEV0Rjb2lTOFZCV2xSYzdTb0R1YnlOMTdWYzB0WkEvcnJ2Qm5nUi9VLzNkQkNsUFhWQ1U0dG1Ed2tJUG1ib1dOaHVaZURVaEtibW9LUTBrZ3h4MEd4ZXhNOExvTFJCQjdpTVVrQmMyamV5eGd5eGZ0V2gvQkMvUlV0RFc3N1ltb3ovM3R1Nm8zQ2thWDAyajlwdHlLQW1CWGZWMnJ6K2lrQVQ5L1NwMWxOYTVBUkhDUEpIcnVvbTZnU2ZXbERMM0ZqY3FtY0RZalBOT2FjYjRjUmJWZC85U1JZZnhRdEkxZzd1WXpYYjJwc2pIbFpRem1uZzRtOGhJU0h5QUp6L2RBUWZ0OFpsSWMyL2puVjF0bE53ajFvV0FoeXZPREU0ajdzcnNQZE16MVBTOUc0aEN3QzF1dFpVTUJRUHorMysyTUtIclUvNS9WYUhXaTdFcXJSZ09DeDZyN0FQVWF1Qm1QM0tVSC9wWDNVcWt5SGFLVXlEOWtXbkVJK0NDUlBrYXpLT1hoSFVxVTd6OUg3MHdIOG9CVkhHSWNEdis2WVdnYzAxdDAxbXhqVzEvUWprcUZ5UnBPc0JEeE83WTFURzAiLCJtYWMiOiJmMWUxNDZkZjFkOWYwODdjODUwN2MwODVhOTEwZGM3MWJhYmExYWYzZjUxZmIzZjk5ZjdkMTk5NzE1MDY2YmYxIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdOUk56d3JSakJSbUFXbGg4Nm9jdmc9PSIsInZhbHVlIjoiQ2tPMFhpanA5U0FBa3BiRHJ1NGlYcVdRcWV3WGRhM3F2NEJqR1E0ZjJGcHZEdGVXc2RSWnAxY0llZ0c5UFJoWnBDSzhFS2hXaU5xWnNxOXYxSjMvOWExQzArR3RUQmZXWmVnTHdJZUJWNVBEM0JZUW1uNzdqSUdOMzJFaHFraWdRM2dzRkVZTGdFcHhPWmVFYzdSRk1vMmlhb2VIMVlzczBYbXYzWkJsdnJpUDkzTXovaERZVjdKNktzOTlVUmpBdVJ1Nk52TTNlRE15MENmZkNLL0QxSDJkaU50YnJ5d1ZaU29pdkdHcTBCT0Q2OHlNWUtUaFV3aTUxeXFodlR1bTZZdjlXNUt3NmlSZWE5d25jVWNlMUI2MFp0V29WYWYwaFhkYkFybTFMNTU2RlVId1ErUmFBZlI1Vi9MVGNDMGJ1eGlEWmNFK2tjNnBMOXNGbnlvaTQzWmhOVEl6QUI2eWxzYzQ5RThYOXgwdFBaVE1weGtDMVA5elFvWkVKYzFOWEV2dU9nYnlPMTZ4Z1hXekcvNmtVSnBnMnZtN3FQbG5XOEhzTkVtMHN0QTlLZmhWSlpQVWdtRlA3cEhMcVY5K2tWZm16cWlaRU5kTzVFNitPb0QraDgvSGUzclpJeXEvKzRRUkM2aXVha2RPdlIxQy9ZZFB3UWxOTGhHT0J2TEoiLCJtYWMiOiI5YTk0MmE0YzRiMGMyODZmMzMzN2FjMDY0MDU2ZmJkZmMwODY2OGE3NzE5YjQxODQ3MmNjZTg5NGJmZWYyYWQxIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233808033\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1739338630 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739338630\", {\"maxDepth\":0})</script>\n"}}