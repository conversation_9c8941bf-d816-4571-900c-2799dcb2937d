{"__meta": {"id": "X303e0ddd0e7106c2503ff195f28e38bc", "datetime": "2025-06-07 04:35:40", "utime": **********.774213, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270939.521113, "end": **********.774266, "duration": 1.2531530857086182, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1749270939.521113, "relative_start": 0, "end": **********.635274, "relative_end": **********.635274, "duration": 1.1141610145568848, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.635293, "relative_start": 1.114180088043213, "end": **********.77427, "relative_end": 4.0531158447265625e-06, "duration": 0.13897705078125, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761088, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006019999999999999, "accumulated_duration_str": "6.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.709715, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.429}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.73736, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.429, "width_percent": 13.953}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.753523, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.382, "width_percent": 14.618}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-272078883 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-272078883\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-205375307 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-205375307\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1040793908 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040793908\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1024635656 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=xq5jkj%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1p8bbqf%7C1749270933532%7C2%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InF3RXRSVnIxaWpwMG1ud1p1WFYySmc9PSIsInZhbHVlIjoiYkJPRDd2d0xWRDhLUGNvaVNIZ3lZTWlrMldTQzdvMDdZZXY0bHhiOFR1MGtWelJ0czBYcElWSkZjUE9PMUxrRWFPUGVhUlFoYnNaV3RZbTdhRVkvM1hRMmVmWFFhaEhJSTVvUzFvWkY2aCt5cUMyUE1hWHlBdXI2UXMwTkVjQ08vZWkrZk1SQjBNWG96ckU5bm9CeW9zNHFZYUdyZzN5RlZyeklFbUhNRC9jRVptOUlhaVY4U2lENEdiR3lhV3pKR083M3doTmN4a1NqZTl2MWMwUExxb1lERXZKeXowbmZ3MzdZb1A3U0JBM1M1blR0ak5va3RqektGNUZwMVVrMGVzcEdDRWEyckpNWFZ6YmhLajJWRFVIUW1rMzJxTGg1T3hkOUljTERjcklHblVLQkV3dUUrRktqbmc4YTZlZmgxTnJuZVdrU2hoaDhNY0ZIVFBJRXR0YzliZGl2T1p4RmhYYzFSWk9XbThFNkFvamVudmsxS1JsVjQzSmZqdmQwazExcXVaN3QvclhqZVVhYWFCczRmY3Y3SDB5YW50L0hVNFFxL2ppaXVZSkRrcVdzbm1VUGNZUlgvUmVkSDF0ZHpxeUR3Unh5R2NBaFBBZ3FSZ04vdU5tN3lZekxzSk5yeExZYVVpVkdmd2hlZDltWlVKK1UyTkUxZUNGUDFHWTUiLCJtYWMiOiIyMGM3MWQwNTA0MTM4ZTc3NTcxN2Y3NjM0ODViNjc4M2ZkMTA4NTNkNzA0YjM3ZTAyYWE3MDA3M2E5NTY5YTIxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJGMGtEYVdIYXdCTjE3OXZMcDBQZXc9PSIsInZhbHVlIjoidlR4YlRiYXd4c2xSWGREQ0VoendqZCs4R0h2dndpRldKd29VWmczdjBYY0cvU2NXZjNvanRQODJ5SGlzNVRKekdQcnFUVnVMbFIxWUtKa1NuMyt6dlB1ck9nVUVQYXVEU3ZxYi9LSTZkbGdtaVU5NGM4bVY1YnBxU0hxeUh6cGRIcE1hMnVlYnBlYUppNzNOQUZrbGVTZjZuQitlbEVWbVJrS0UvQmxhcS9kcExRZjZCY2tpYzVDY21lVFNNUGd3d0dRTnVSZk9mOXFodFJKRnRGVHhZMHNNQXRIMTlEejVYWWZ5QlMrcXJjVlJ0UVBCOFI3dkJSMzZVYUwvU1Q3R0hJQWt0ZjltVEVXamJ6VGx6RmZydFdob3hGbzNpeld0cHk1RDhaR1ZLbE9KTmdCMldNUE45amladWdDNFdpQ3UrSFhiaTlYbjYvVFpJS3l0SVJSQitlSTJYd2NnbXphZjNMcUNweUZYZWk1eitma1MzWE5MRkY0cTl2QjAvL3NzeFpPejBPakc1dDRaaWIzRnppYzFzUFhqeGlJUFhPZDdEK1dUZCt2dkdXa094QVoyMCt4OXJIcTdDcm1MM1hPejlOSDZONXYwdVVFSk5oVlhrTUw1R2hEYWNkQlJidG5mTThTbkRtNUN2YlFnTVBIMnFnWk9YVVU1cjYzcUdIUE4iLCJtYWMiOiJkN2Y5OTljNjIzZGM3MTMyZGJhNTg2NWFhOWQ5NDI1NTFmZjkyNjRjYTZmNTI0NjM4YmQzOWZiYjAzYjU1NDU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024635656\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-855562001 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z1ibAMoy16lMUkDWMem3ATVFxZVkTly8GBk5DGQF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855562001\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1751379091 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:35:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlXZ0tmY2xmeVZhYXdmTHAvOW9hL0E9PSIsInZhbHVlIjoiR0taQy8yKzBVdSttV2ttb2s5cGVveUJ0d0tFd2F5elNXRnI2RHBlQTh5QmtFRmNzYWlQLzh2MXJlQkU0eGFiWmwwUFhYUjZIdWNwUnk0M2I3cndDV0RBTXNOUmM0S2tXbWF4WVpPUFg5Szdxenl6c2R2Z21nazNFcmlWeFpZSXFvV0ZYajVBQUJ6REk1eC9PN29RNW5YTk5VN0ZGWlVycGdlNEVWY2xrbFh2VE41SnZtSGRkeDR5RFdtVnpUanNlalNwTjlkVmdWdVQ0SjVpb1pjL2d1YUE1ODdTTDdraHR5VGhyaEE0ckhiQTIwYUVUQVJMUWhJTm5sVVhtRFF3MWovUklvcFBoUFFkWUFTLzNQYXdZMW0vMHU3aWxscXB0TG5ORVQ3bjBrRlVEVzBOY05XQjBqMUJCNzhxTE4vZVJ3TmZDcUVmd3ZtekI1SWFOTjFJdVhFUGRENGlLL3RjOXk0SSt5SGR4UUlZdENzd2hzSlNwcDhvb0VwVTYraVVRZ1VWQWcvVEt1TCt0M0NhV1JJREoybkZicTZEeHpTZ3hIME5CMFlpMitTeE01ZWhnSDREN29jVHkvdHBzK3lXd0w5b09iQXNhUDU3YklWaG9MU2lKQXpZYW9RYjlpVXlkQ2hhaFBGREZSQ3NIcHVTcmVxSnk2bXNTNElDSWNHeXMiLCJtYWMiOiI4ZDc3YjNlOTJkOWVhYWQ3MTJiODdmNmFmMjA2NTQ5ODUxMjMyNmI0NjFhYzYzYWQxOTBiZTMwOGUxM2E4YzUzIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlcvelVzdzRpUWFyUVhrYlRSVTB3MXc9PSIsInZhbHVlIjoieFNlWjlrWEUwRDJSeHk0SEpqREVUNzBxc0dvdWdUNjk4WUF3REQvU3pmWmVyNUUzODlnK2dRRU85S3RDRUFib2tLSmk1VVowc2FucWc2Z3hpVkE4K09ZLy9iNkRiZWJIU0srVzNEYnpVUmlCUjkrTkUxTTN1d1hPekZldi9qQ2o2eHY5TXovVHgxWWFORXFXNFFIZGNQUzRnRlg2eC81S2hMU0FubzNmdDRWTC9STi9tZ2RVUXdyWFBLeWVNQ2Y3Qi9qK2N4ZEtvMkw1bGZ6aDcxYWxMckFieDlycmxJTmJJL3lBTUJHKy9SczhkT05wT3NlVHFWZ25ReFFvd0l0WDBJU25La1ZlWDNwclFRR3NMOHcyb2FlNUZQNEZ4OG9YNDJvNmFqQjgrZExqcm81bW0rblR3bCt6NTJNM2xWUDljZWhveFh1SmxsMkhOSkgxcS9US200ZWhUZmlyRHZhM1UvUzFxb21QZmdIRExrZk9qcFZuZHFZSjhFRTdxdkFzWi9Pa0Jpcm5OaVN6allOdFBKM0pzV1FxN3hYcVljdEFJZXZjUVVvSGtUK1RGWngyM3BJSXFJVDVxZ0x6NFhvN09LbFBjanR3cnJNVHU1eHFCN1JNRXBOa2d6c0dvOUpMb2ZSK2hTMVpDdUs2UFI2NHFERC9GcG1lelAxc0crOHIiLCJtYWMiOiIyYjlkYTg0NjU3NWEyZjhhYzk0NTFhMGE1MzJlZjQwOGY0NTk0NTFlOWU2ZjBiN2QxYjdmZjk4MTMyNWVjZjlhIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlXZ0tmY2xmeVZhYXdmTHAvOW9hL0E9PSIsInZhbHVlIjoiR0taQy8yKzBVdSttV2ttb2s5cGVveUJ0d0tFd2F5elNXRnI2RHBlQTh5QmtFRmNzYWlQLzh2MXJlQkU0eGFiWmwwUFhYUjZIdWNwUnk0M2I3cndDV0RBTXNOUmM0S2tXbWF4WVpPUFg5Szdxenl6c2R2Z21nazNFcmlWeFpZSXFvV0ZYajVBQUJ6REk1eC9PN29RNW5YTk5VN0ZGWlVycGdlNEVWY2xrbFh2VE41SnZtSGRkeDR5RFdtVnpUanNlalNwTjlkVmdWdVQ0SjVpb1pjL2d1YUE1ODdTTDdraHR5VGhyaEE0ckhiQTIwYUVUQVJMUWhJTm5sVVhtRFF3MWovUklvcFBoUFFkWUFTLzNQYXdZMW0vMHU3aWxscXB0TG5ORVQ3bjBrRlVEVzBOY05XQjBqMUJCNzhxTE4vZVJ3TmZDcUVmd3ZtekI1SWFOTjFJdVhFUGRENGlLL3RjOXk0SSt5SGR4UUlZdENzd2hzSlNwcDhvb0VwVTYraVVRZ1VWQWcvVEt1TCt0M0NhV1JJREoybkZicTZEeHpTZ3hIME5CMFlpMitTeE01ZWhnSDREN29jVHkvdHBzK3lXd0w5b09iQXNhUDU3YklWaG9MU2lKQXpZYW9RYjlpVXlkQ2hhaFBGREZSQ3NIcHVTcmVxSnk2bXNTNElDSWNHeXMiLCJtYWMiOiI4ZDc3YjNlOTJkOWVhYWQ3MTJiODdmNmFmMjA2NTQ5ODUxMjMyNmI0NjFhYzYzYWQxOTBiZTMwOGUxM2E4YzUzIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlcvelVzdzRpUWFyUVhrYlRSVTB3MXc9PSIsInZhbHVlIjoieFNlWjlrWEUwRDJSeHk0SEpqREVUNzBxc0dvdWdUNjk4WUF3REQvU3pmWmVyNUUzODlnK2dRRU85S3RDRUFib2tLSmk1VVowc2FucWc2Z3hpVkE4K09ZLy9iNkRiZWJIU0srVzNEYnpVUmlCUjkrTkUxTTN1d1hPekZldi9qQ2o2eHY5TXovVHgxWWFORXFXNFFIZGNQUzRnRlg2eC81S2hMU0FubzNmdDRWTC9STi9tZ2RVUXdyWFBLeWVNQ2Y3Qi9qK2N4ZEtvMkw1bGZ6aDcxYWxMckFieDlycmxJTmJJL3lBTUJHKy9SczhkT05wT3NlVHFWZ25ReFFvd0l0WDBJU25La1ZlWDNwclFRR3NMOHcyb2FlNUZQNEZ4OG9YNDJvNmFqQjgrZExqcm81bW0rblR3bCt6NTJNM2xWUDljZWhveFh1SmxsMkhOSkgxcS9US200ZWhUZmlyRHZhM1UvUzFxb21QZmdIRExrZk9qcFZuZHFZSjhFRTdxdkFzWi9Pa0Jpcm5OaVN6allOdFBKM0pzV1FxN3hYcVljdEFJZXZjUVVvSGtUK1RGWngyM3BJSXFJVDVxZ0x6NFhvN09LbFBjanR3cnJNVHU1eHFCN1JNRXBOa2d6c0dvOUpMb2ZSK2hTMVpDdUs2UFI2NHFERC9GcG1lelAxc0crOHIiLCJtYWMiOiIyYjlkYTg0NjU3NWEyZjhhYzk0NTFhMGE1MzJlZjQwOGY0NTk0NTFlOWU2ZjBiN2QxYjdmZjk4MTMyNWVjZjlhIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751379091\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-998953971 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998953971\", {\"maxDepth\":0})</script>\n"}}