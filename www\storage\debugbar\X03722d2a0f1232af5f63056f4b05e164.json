{"__meta": {"id": "X03722d2a0f1232af5f63056f4b05e164", "datetime": "2025-06-07 04:41:56", "utime": **********.388114, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749271314.998542, "end": **********.388142, "duration": 1.3896000385284424, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1749271314.998542, "relative_start": 0, "end": **********.223266, "relative_end": **********.223266, "duration": 1.2247238159179688, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.22329, "relative_start": 1.224747896194458, "end": **********.388145, "relative_end": 2.86102294921875e-06, "duration": 0.1648550033569336, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761088, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.008270000000000001, "accumulated_duration_str": "8.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.311082, "duration": 0.005690000000000001, "duration_str": "5.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.803}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3467221, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.803, "width_percent": 12.213}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.365892, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.016, "width_percent": 18.984}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1407785146 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1407785146\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2027993468 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2027993468\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1566990430 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566990430\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1379743126 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=xq5jkj%7C2%7Cfwk%7C0%7C1984; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1p8bbqf%7C1749270939872%7C3%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InN5ZEp5REo3blJ5aHZ0TVExWFpEaXc9PSIsInZhbHVlIjoiaW5qUDRsb1duRUhVMEtnNWZ4VlpsUWE2TXF2amhRUjFYYlRuNFNIeDJqTWV3VHpxNGxodXM4UUZIU3l3b2ZYN1lDdU0xMmc1NitDWElyZ3FMZUFsNHVVVHE5ZGtZNEpjNGloMmYxcmR5cUN2R0c2K3lLTEJ4RHlaODdRb0xqdWdQY2cyNnBHZ0hSYlRWVllzT1dQcUhjK1o1anRQaTZKOFZJK1BFYU11bFN2OGtpblRmVEI0TXloeEFpeUUrd2pReVFXRDJ6T0dSQTNGMDdRdVJSbzFheVhDQjNYbXVjNEpqcVhNbTBqMmxXbVZBS1hocXVNdGJPc0tlZjRJUDNYMVlidGw3T3JFWDJiVVhIVFVCb2FPeW01S28xTVZsa0FtYUthTDhoRTk2ODE5MXBoTUNYRmNIWndoZXd1WmhGR1ZMSDVTQVhDRUExRitKNUc5dlFSeHJhelRBbkZmSm9DMlFaUWtIOUVXMVdIRHBRbDFLYTlEcFpzdEVmb1F2YmNIeFNYTjVLN3NWNmFBbGlxMzB6b0RKN0hraUErWHdIOHV5VUVWL1krc2ZvcWw3ekg1M0xEL1NaSVhiL1VEcU9qRGVqUVlpYlJSNlNoY2tRMXhFZUUxQ0VzZlQzemZnWld3ZElxUWd4RkUvNHJ6WFV6S09ZUll0TkxZczl6UFlqc0MiLCJtYWMiOiJjMGI2ZTdkMzNlNzAwYTFmYTA3YmYzYjlkYmJiZGYwNTgwYmI5ZTY0NDcxOGU2NmY0MDM2OWQwOWY0MzZhMTQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFLTzJmbmdIbmlLVWRSZFRiVy92THc9PSIsInZhbHVlIjoiV3AzMDd3THlLRkNrRDNYM2IwTmFCb2ZqZEpSRnB0UXJMY2FjTWRrKzIwMWFRTzhBL2VaU3Vodjc2V1g0QWwrbTN6OTc2amFiZzhxd2xKZFlqSDM0SFFWOTljV2w0R3dtOElIaTVtSzd6amYzejNXaXhMQ0hyZ2NLazl2R09KanpMY3oxMWlscWMxSHkwL2ZEbDB5aE8xYjIxSWFPWTA0YWJIeTJubW5MSGthZ2ptNkFmblFpS0p1MkliRmRuMmNGdUswN0ZJOUpVbjZBVE81SldEVk1vOXh4WlRtZWZ4YzRnYWZIQXlOb0t6ZjJoclRic2RzZU5Mckh4UXpDQ01sRnJ2aUZFTFEzOFozeXp2Wm91b2JDaFpwZS9sNFArS2FVV1ZraDRDWDJnRUhDdVl4dVJKY0EyNzlLQXphMXgvYWNrREk3VmcrUmlpck9FNHoyY0hkNSt1VnlKWnM0SEIxbEVKalJCWCtKMW1KMnFwN3FEMEhOa2pydnFXK2tUVmsyLzdZL25ucDlhQThZODVKVm5tdGUxYWo2b2FCWFdSK1c4UWFjYkpEQ0ZRbVVDVjdtNnVxWnltYk94eDJKdWY5LzJJZGkxV2ZJN0ZDVHp3aHQ1dE5Cd2xkWVUrOFpob0x1bEo0YTNkVlk2UzBTb2tyRDMxMGxDQ0l4ZXlsMTdLSjYiLCJtYWMiOiI2NmMzNWUwNTU2NzMzMWIxNWJmYWM5ZGY0ODVhODZlNmFmODBhOTY1MjkxMTIyMGQxNDk5NGViYzBlNzJiMzQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379743126\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1873735513 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z1ibAMoy16lMUkDWMem3ATVFxZVkTly8GBk5DGQF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1873735513\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1872623615 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:41:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1lOFJBcFdXOHh6Q1FnbzNLUWpjUkE9PSIsInZhbHVlIjoiZC9TWmdFTmdzY2JJZm5HZ3ZZUHNBTUZwQTR4OUEzUDRzSnIyd0ppNG5TYkhHSVNKZTRpbU5VZElWQVhwbldzWTY4eDRzeHI3anJHT2RPUHMzcFYwWDBZb29tK3h5MUtYK0c2NVQ2VTl3WHFxSVpKUUhDS3RFUTBZWFNyUUgrZjVRVzdZL1VKL0VVS2MzSEg1Nml0TzRkcDN6M0VNcUgzaVgyVXVyZFVpZ2xmYjVFUExtaVhMQkJmVUR6bU53VVRxamZmcEViaHVDZDNMMlhWYU5pWjErQW95MWlqL3RaU3FWY25CWE1peDdtbVNuNmY3b2pWQ3FIbnV2ZjByOWI2SUJhSGJsUjZrSldobndoVnZzbXNzWXl1MXRsdXQraHFsZUVBUzdGVmpieU9EcDlNNTM4YTAwRHBFVVJSa2Q4WG5LSFo3UEY2ZGZyVXZlL1NQQmNhcWRxMEJHdGF0NG5MSzRtR1FKSEJQeEwwUXhtTmdYQjltQk5NS25jTlhscHRoVHMxdnFYR2dsMHI2ZnR3c0ZsbldkTnNjUC9zTzdudlM3OHdRZTJUK0N0VGNLMnZOWnVsc0ZkN2ZRZ3IvVS9rN3RnSUVOUU1xc3hEM3NnMCtmS3RkbnpTOE8yQmJPaHRZeWZ2bTRza1JsSnJxV1ZZRW1WcGlHMmcrU2NaSERTVmQiLCJtYWMiOiJlMzQ1ZDM5MDFhNDg3Zjg5NWE3MGI3NjMyYzM2YTM0ODdmODk0YTcwM2I3NDBkZmM3Yjk0OGU0NGFhYjA2NGM4IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:41:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imx5WVNtWEhqNU5mVHI2NlFwbXJQS3c9PSIsInZhbHVlIjoia1hXTDI0TVBIYU5LWlNQSEl3VW1rOWRUbmlLcENHbGpoNWhDaVpkc0FKL1B5OTl6dTEzTlZIQlBBSU5HV3RGQ09DdFh3aUtIRy9DQ1o5MExnZkFyYzJSN0hNTi9pcGMraFd3UnE3WnplRWJpVGlzRVBKM20xQm5vdHI0aWUzV2Vhb1NPODIxTGZuVjNSSk5nWUpUaXJlUkh1bk8zNnJZbUxYMitzU1VTVW1MUnpReHdRNGtPSUk2cGo4ZXQ3N3ZmUzRwRlBOSG1sdXFvRjFxRTV1MUg0WW40U1ZFNjFHM1JtWERwU0pqdGFLaGVrRUhEQmFEQjRjNEt6RldVN1VNUVVNWGJhMG9aZGpJd2JsNkRSQ3FUakdQMTVhQ0QvMDR3cHJFVmZ6NlVaR084Nm4rMzhpM3B1NmwrUGFsUTBoSnc3SEx3SkxSRURFNU1icXcxSWVnLytYSTJ3STVhd1RjVHpxWlo2N2M4Z0FnaGFWMnRrUVAzTTZzam5kZkNVVDIrdVR3MDJLeEQ1K2VsU1NhVUFGV3NZZEM4UHVkNWtVbkJFS2c3N2QwclFRTUNIY0xxWThCYmN4WGQ3SDBiQzJzUjdWanZNTlppL1N4UDRzTCsrSXJZWWhKNzN6NmZTVmNWK0ZCQVVPUm56M3NDb2lxWmdaS0cwb0NwNVE3RWhDV2kiLCJtYWMiOiI3MTFjNTc5MGFmMTFmNTQzNWZkZjA5MTQ3NmFkOGZjZjE4NjRlZDU2MzVkN2YxZjJmNzc4OGNjNTYzMjU0YzNjIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:41:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1lOFJBcFdXOHh6Q1FnbzNLUWpjUkE9PSIsInZhbHVlIjoiZC9TWmdFTmdzY2JJZm5HZ3ZZUHNBTUZwQTR4OUEzUDRzSnIyd0ppNG5TYkhHSVNKZTRpbU5VZElWQVhwbldzWTY4eDRzeHI3anJHT2RPUHMzcFYwWDBZb29tK3h5MUtYK0c2NVQ2VTl3WHFxSVpKUUhDS3RFUTBZWFNyUUgrZjVRVzdZL1VKL0VVS2MzSEg1Nml0TzRkcDN6M0VNcUgzaVgyVXVyZFVpZ2xmYjVFUExtaVhMQkJmVUR6bU53VVRxamZmcEViaHVDZDNMMlhWYU5pWjErQW95MWlqL3RaU3FWY25CWE1peDdtbVNuNmY3b2pWQ3FIbnV2ZjByOWI2SUJhSGJsUjZrSldobndoVnZzbXNzWXl1MXRsdXQraHFsZUVBUzdGVmpieU9EcDlNNTM4YTAwRHBFVVJSa2Q4WG5LSFo3UEY2ZGZyVXZlL1NQQmNhcWRxMEJHdGF0NG5MSzRtR1FKSEJQeEwwUXhtTmdYQjltQk5NS25jTlhscHRoVHMxdnFYR2dsMHI2ZnR3c0ZsbldkTnNjUC9zTzdudlM3OHdRZTJUK0N0VGNLMnZOWnVsc0ZkN2ZRZ3IvVS9rN3RnSUVOUU1xc3hEM3NnMCtmS3RkbnpTOE8yQmJPaHRZeWZ2bTRza1JsSnJxV1ZZRW1WcGlHMmcrU2NaSERTVmQiLCJtYWMiOiJlMzQ1ZDM5MDFhNDg3Zjg5NWE3MGI3NjMyYzM2YTM0ODdmODk0YTcwM2I3NDBkZmM3Yjk0OGU0NGFhYjA2NGM4IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:41:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imx5WVNtWEhqNU5mVHI2NlFwbXJQS3c9PSIsInZhbHVlIjoia1hXTDI0TVBIYU5LWlNQSEl3VW1rOWRUbmlLcENHbGpoNWhDaVpkc0FKL1B5OTl6dTEzTlZIQlBBSU5HV3RGQ09DdFh3aUtIRy9DQ1o5MExnZkFyYzJSN0hNTi9pcGMraFd3UnE3WnplRWJpVGlzRVBKM20xQm5vdHI0aWUzV2Vhb1NPODIxTGZuVjNSSk5nWUpUaXJlUkh1bk8zNnJZbUxYMitzU1VTVW1MUnpReHdRNGtPSUk2cGo4ZXQ3N3ZmUzRwRlBOSG1sdXFvRjFxRTV1MUg0WW40U1ZFNjFHM1JtWERwU0pqdGFLaGVrRUhEQmFEQjRjNEt6RldVN1VNUVVNWGJhMG9aZGpJd2JsNkRSQ3FUakdQMTVhQ0QvMDR3cHJFVmZ6NlVaR084Nm4rMzhpM3B1NmwrUGFsUTBoSnc3SEx3SkxSRURFNU1icXcxSWVnLytYSTJ3STVhd1RjVHpxWlo2N2M4Z0FnaGFWMnRrUVAzTTZzam5kZkNVVDIrdVR3MDJLeEQ1K2VsU1NhVUFGV3NZZEM4UHVkNWtVbkJFS2c3N2QwclFRTUNIY0xxWThCYmN4WGQ3SDBiQzJzUjdWanZNTlppL1N4UDRzTCsrSXJZWWhKNzN6NmZTVmNWK0ZCQVVPUm56M3NDb2lxWmdaS0cwb0NwNVE3RWhDV2kiLCJtYWMiOiI3MTFjNTc5MGFmMTFmNTQzNWZkZjA5MTQ3NmFkOGZjZjE4NjRlZDU2MzVkN2YxZjJmNzc4OGNjNTYzMjU0YzNjIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:41:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872623615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-950750125 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950750125\", {\"maxDepth\":0})</script>\n"}}