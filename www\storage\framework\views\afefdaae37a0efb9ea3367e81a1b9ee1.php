<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('النماذج')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('الشاشة الرئيسية')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('النماذج')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-button'); ?>
    <?php if(Auth::user()->type == 'company'): ?>
        <div class="float-end">
            <a href="<?php echo e(route('forms.create')); ?>" class="btn btn-sm btn-primary">
                <i class="ti ti-plus"></i>
                <?php echo e(__('إنشاء نموذج جديد')); ?>

            </a>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <?php if($forms->count() > 0): ?>
                
                <?php $operationalForms = $forms->where('type', 'operational') ?>
                <?php if($operationalForms->count() > 0): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="ti ti-settings text-primary me-2"></i>
                                <?php echo e(__('النماذج التشغيلية')); ?>

                                <span class="badge bg-primary ms-2"><?php echo e($operationalForms->count()); ?></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('اسم النموذج')); ?></th>
                                            <th><?php echo e(__('تاريخ الإنشاء')); ?></th>
                                            <th><?php echo e(__('منشئ النموذج')); ?></th>
                                            <th><?php echo e(__('الأدوار المسموح لها')); ?></th>
                                            <th><?php echo e(__('الإجراءات')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $operationalForms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-file-text text-primary me-2"></i>
                                                        <strong><?php echo e($form->name); ?></strong>
                                                    </div>
                                                </td>
                                                <td><?php echo e($form->created_at->format('Y-m-d H:i')); ?></td>
                                                <td><?php echo e($form->creator->name ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <?php if(in_array('all', $form->visible_to_roles)): ?>
                                                        <span class="badge bg-success"><?php echo e(__('الجميع')); ?></span>
                                                    <?php else: ?>
                                                        <?php $__currentLoopData = $form->visible_to_roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <span class="badge bg-info me-1">
                                                                <?php switch($role):
                                                                    case ('Cashier'): ?>
                                                                        <?php echo e('Cashier'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('accountant'): ?>
                                                                        <?php echo e('accountant'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('delivery'): ?>
                                                                        <?php echo e('delivery'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('SUPER FIESR'): ?>
                                                                        <?php echo e('SUPER FIESR'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('SUPER FIESR BIG'): ?>
                                                                        <?php echo e('SUPER FIESR BIG'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('all'): ?>
                                                                        <?php echo e('all'); ?>

                                                                        <?php break; ?>
                                                                    <?php default: ?>
                                                                        <?php echo e($role); ?>

                                                                <?php endswitch; ?>
                                                            </span>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="action-btn bg-primary ms-2">
                                                        <a href="<?php echo e(route('forms.show', $form)); ?>" 
                                                           class="mx-3 btn btn-sm align-items-center" 
                                                           target="_blank"
                                                           title="<?php echo e(__('عرض النموذج')); ?>">
                                                            <i class="ti ti-eye text-white"></i>
                                                        </a>
                                                    </div>
                                                    <?php if(Auth::user()->type == 'company' || $form->created_by === Auth::id()): ?>
                                                        <div class="action-btn bg-danger ms-2">
                                                            <form action="<?php echo e(route('forms.destroy', $form)); ?>" 
                                                                  method="POST" 
                                                                  class="d-inline"
                                                                  onsubmit="return confirm('<?php echo e(__('هل أنت متأكد من حذف هذا النموذج؟')); ?>')">
                                                                <?php echo csrf_field(); ?>
                                                                <?php echo method_field('DELETE'); ?>
                                                                <button type="submit" 
                                                                        class="mx-3 btn btn-sm align-items-center"
                                                                        title="<?php echo e(__('حذف النموذج')); ?>">
                                                                    <i class="ti ti-trash text-white"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                
                <?php $financialForms = $forms->where('type', 'financial') ?>
                <?php if($financialForms->count() > 0): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="ti ti-currency-dollar text-success me-2"></i>
                                <?php echo e(__('النماذج المالية')); ?>

                                <span class="badge bg-success ms-2"><?php echo e($financialForms->count()); ?></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('اسم النموذج')); ?></th>
                                            <th><?php echo e(__('تاريخ الإنشاء')); ?></th>
                                            <th><?php echo e(__('منشئ النموذج')); ?></th>
                                            <th><?php echo e(__('الأدوار المسموح لها')); ?></th>
                                            <th><?php echo e(__('الإجراءات')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $financialForms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-file-text text-success me-2"></i>
                                                        <strong><?php echo e($form->name); ?></strong>
                                                    </div>
                                                </td>
                                                <td><?php echo e($form->created_at->format('Y-m-d H:i')); ?></td>
                                                <td><?php echo e($form->creator->name ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <?php if(in_array('all', $form->visible_to_roles)): ?>
                                                        <span class="badge bg-success"><?php echo e(__('الجميع')); ?></span>
                                                    <?php else: ?>
                                                        <?php $__currentLoopData = $form->visible_to_roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <span class="badge bg-info me-1">
                                                                <?php switch($role):
                                                                    case ('Cashier'): ?>
                                                                        <?php echo e('Cashier'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('accountant'): ?>
                                                                        <?php echo e('accountant'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('delivery'): ?>
                                                                        <?php echo e('delivery'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('SUPER FIESR'): ?>
                                                                        <?php echo e('SUPER FIESR'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('SUPER FIESR BIG'): ?>
                                                                        <?php echo e('SUPER FIESR BIG'); ?>

                                                                        <?php break; ?>
                                                                    <?php case ('all'): ?>
                                                                        <?php echo e('all'); ?>

                                                                        <?php break; ?>
                                                                    <?php default: ?>
                                                                        <?php echo e($role); ?>

                                                                <?php endswitch; ?>
                                                            </span>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="action-btn bg-success ms-2">
                                                        <a href="<?php echo e(route('forms.show', $form)); ?>" 
                                                           class="mx-3 btn btn-sm align-items-center" 
                                                           target="_blank"
                                                           title="<?php echo e(__('عرض النموذج')); ?>">
                                                            <i class="ti ti-eye text-white"></i>
                                                        </a>
                                                    </div>
                                                    <?php if(Auth::user()->type == 'company' || $form->created_by === Auth::id()): ?>
                                                        <div class="action-btn bg-danger ms-2">
                                                            <form action="<?php echo e(route('forms.destroy', $form)); ?>" 
                                                                  method="POST" 
                                                                  class="d-inline"
                                                                  onsubmit="return confirm('<?php echo e(__('هل أنت متأكد من حذف هذا النموذج؟')); ?>')">
                                                                <?php echo csrf_field(); ?>
                                                                <?php echo method_field('DELETE'); ?>
                                                                <button type="submit" 
                                                                        class="mx-3 btn btn-sm align-items-center"
                                                                        title="<?php echo e(__('حذف النموذج')); ?>">
                                                                    <i class="ti ti-trash text-white"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="ti ti-file-text text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3"><?php echo e(__('لا توجد نماذج متاحة حالياً')); ?></h4>
                        <?php if(Auth::user()->type == 'company'): ?>
                            <p class="text-muted"><?php echo e(__('يمكنك إنشاء نموذج جديد بالضغط على الزر أعلاه')); ?></p>
                            <a href="<?php echo e(route('forms.create')); ?>" class="btn btn-primary mt-3">
                                <i class="ti ti-plus me-2"></i>
                                <?php echo e(__('إنشاء نموذج جديد')); ?>

                            </a>
                        <?php else: ?>
                            <p class="text-muted"><?php echo e(__('لا توجد نماذج متاحة لك في الوقت الحالي')); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\up20251\resources\views/forms/index.blade.php ENDPATH**/ ?>