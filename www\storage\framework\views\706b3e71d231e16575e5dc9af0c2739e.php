
<div class="col-12">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="ti ti-file-text me-2"></i>
                <?php echo e(__('النماذج')); ?>

            </h5>
            <?php if(Auth::user()->type == 'company'): ?>
                <a href="<?php echo e(route('forms.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="ti ti-plus"></i>
                    <?php echo e(__('إنشاء نموذج جديد')); ?>

                </a>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <?php if($forms->count() > 0): ?>
                <div class="row">
                    
                    <?php $operationalForms = $forms->where('type', 'operational') ?>
                    <?php if($operationalForms->count() > 0): ?>
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="ti ti-settings me-1"></i>
                                النماذج التشغيلية
                            </h6>
                            <div class="list-group">
                                <?php $__currentLoopData = $operationalForms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-file-text text-primary me-2"></i>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($form->name); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo e(__('تم الإنشاء في')); ?>: <?php echo e($form->created_at->format('Y-m-d')); ?>

                                                </small>
                                            </div>
                                        </div>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('forms.show', $form)); ?>" 
                                               class="btn btn-outline-primary btn-sm" 
                                               target="_blank"
                                               title="عرض النموذج">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            <?php if(Auth::user()->type == 'company' || $form->created_by === Auth::id()): ?>
                                                <form action="<?php echo e(route('forms.destroy', $form)); ?>" 
                                                      method="POST" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا النموذج؟')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-outline-danger btn-sm"
                                                            title="حذف النموذج">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    
                    <?php $financialForms = $forms->where('type', 'financial') ?>
                    <?php if($financialForms->count() > 0): ?>
                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="ti ti-currency-dollar me-1"></i>
                                النماذج المالية
                            </h6>
                            <div class="list-group">
                                <?php $__currentLoopData = $financialForms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $form): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-file-text text-success me-2"></i>
                                            <div>
                                                <h6 class="mb-0"><?php echo e($form->name); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo e(__('تم الإنشاء في')); ?>: <?php echo e($form->created_at->format('Y-m-d')); ?>

                                                </small>
                                            </div>
                                        </div>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('forms.show', $form)); ?>" 
                                               class="btn btn-outline-success btn-sm" 
                                               target="_blank"
                                               title="عرض النموذج">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            <?php if(Auth::user()->type == 'company' || $form->created_by === Auth::id()): ?>
                                                <form action="<?php echo e(route('forms.destroy', $form)); ?>" 
                                                      method="POST" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا النموذج؟')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-outline-danger btn-sm"
                                                            title="حذف النموذج">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="ti ti-file-text text-muted" style="font-size: 3rem;"></i>
                    <h6 class="text-muted mt-2"><?php echo e(__('لا توجد نماذج متاحة حالياً')); ?></h6>
                    <?php if(Auth::user()->type == 'company'): ?>
                        <p class="text-muted"><?php echo e(__('يمكنك إنشاء نموذج جديد بالضغط على الزر أعلاه')); ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.list-group-item {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem !important;
    margin-bottom: 0.5rem;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.btn-group .btn {
    margin-left: 2px;
}
</style>
<?php /**PATH C:\laragon\www\to\resources\views/forms/dashboard-section.blade.php ENDPATH**/ ?>