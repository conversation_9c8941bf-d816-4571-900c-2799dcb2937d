{"__meta": {"id": "X9077e73abab82fbc536ed97d501e946f", "datetime": "2025-06-06 20:44:23", "utime": **********.42589, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242661.830764, "end": **********.425922, "duration": 1.5951578617095947, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749242661.830764, "relative_start": 0, "end": **********.238267, "relative_end": **********.238267, "duration": 1.4075028896331787, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238289, "relative_start": 1.4075250625610352, "end": **********.425926, "relative_end": 4.0531158447265625e-06, "duration": 0.1876368522644043, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767520, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00914, "accumulated_duration_str": "9.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.33152, "duration": 0.00538, "duration_str": "5.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.862}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.363824, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.862, "width_percent": 10.284}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.372006, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 69.147, "width_percent": 13.239}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.395783, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.385, "width_percent": 17.615}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1962446602 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242514188%7C25%7C1%7Ci.clarity.ms%2Fcollect; laravel_session=eyJpdiI6Ik1DVzgzaVM3SFpra2RiV0dWWkdHeEE9PSIsInZhbHVlIjoiSW03UWU1TzJPK3hTWTIxQXVrbnhvVXF6VlBpU2lkdGhvUTR1VmxDay9EcEIzL0pxTXIzamsxZ1RSMU9GemJKVU94YVd3bXRsdGFSTTJmNWZyLzVOcjJFQ3ZoVVh2Und3OUJGSmN2dWo4VnpwNS9Xei9Sc2hUQmxKWGVSMC9rVWRtMlY0MUxFcVYydUQ5RzJ0SSt6K3krbHh0TDVUV2VTOFF1TDNZRnBub01GaXIrSlM0ODJaS3BKNkdVWWxMVTMyakJiTm5Cb3J4TFI3VWV0U1IwNno3R2g1bjR5QUg4Q0FXcHIzVVAwdzFGNnZaTXZTY2RDY2R5L1hOQ1JSdzc3cUlOdW1iR2NQNEpvd21XeDNXVFJ4MklMWmJBUGtIQzJBRnc2LzVFbWNNOGZ5QkU4bkJWS1Z2aEtJY3ZSd1plclQrTXk0VXc3Y0JpWHNXUkFrUWIvcUk5M3FtNEdHYUNIRjJnY0dBbmlaNUVJdWtER3RJam5ic1NsblhPUnhjdUE0aXhOdktSYitUWVRhdTNoNmtOcTU4dnhaUGxQYTFQY2RGSFZ2aHJQTDlyTVUzZzdIN20yM1dPOHA5dnN6Wi9hdVhtMlZJT0t5SW9zSGZtV1NWNWRuR08yVDFVL2JxM1pFeDloeTlXTXhicGRGSjNlOGhGb0hTcE1QSWhOSmlqSTAiLCJtYWMiOiI2ODE0NWI5ZGVjZWNjMjI0YTFlYWVjMjk4OGFiMjE3MWI3YzZkODY2NDhjOGQ1NGRiNTgzYzFkYzY2M2Q2NDBkIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImYvUWpNeU1BVVpvRmZnSmJ3a2tWUkE9PSIsInZhbHVlIjoidXFXbC9WVDk3Qyt2bjNDbEpPZTEzNVBXN3JHTStCTXNoK05jR2lTUEprdDlQSFdnY3ZvTW9qTStlU2pzUTk0S2Vkdm94T3dmUFp3UkFkTUl4Zkd4WFZBdjRRN1YzSmZaN1AvK2JYUEhMR0JxN2tOaitZcDJPRGU2TzkrZUg0czlXaWs4czg1VWVZWnFzd0ZWYWtLZy9qNktpV3Y2endFSjB6ZFl6Y3lXUzlGMTVLTEU4UDBQQmlJbERyYTVnY1ViZis3WTNGSUtMZGw3UUFEZXZlaThienhnUDhUMkRWb2UzRnZKSzdNcXMvSElIUVZjNnhBOXE1K0RaeFk3L1E1S2JlUHZuWjY3SEtJaHp3dDQ5dzNRUVpPZEtaTU9YbXNWK1o5UjE1OG4yS1p4UXFVejBOU0xUUmR3U1pBOTBYVVRGeEpkNzI4YStvT3FQeXFnaVFlRkN2U2lOTjJEd0VmTStGblVlbjdLRzBjWjRNNnNFcFpPQmRMR1RaMUVUR2lTWEhhSForWGNRQkVRblhGaVFPN0Z3S21xc0NDakNzQlh3VUNCUUE2TlBsQ05sQ2FGYVVRT3kxMW9BZGJnOXhibFBtelIxTE5sVjRCQXVvd0hxS0gyUlA5OXp4WlAvaEJRQzJrRjEyU0dXMUNwclM5dWZLVmFIRnhSS20wUTNoL24iLCJtYWMiOiJmZjMzMDAwMDI0OThmODE0MzBjNDhiNjhlNTE1NjVhODA2YzRlMzJhZTZiM2UzNDM3MTUxYjM4MTYwYmQwMzU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlI0VXZycWF4Wmx6R1ZSeVBaWEd6SFE9PSIsInZhbHVlIjoiWEF1SlB1SHFWZjI0UUJlMXNRbTBsMXJUaFpWV1hjM1U5UTVzZVJ3dFhZUEIvblRIT3E1NWpCbWMyd3dId0JDSnZkazhma0hYUHphL05nOEEwbmFFTEhjdkg2RVRneTFJZEJIeVdRbUprUk4yQUc2cTNQYzhPWWUxNHpDYXNnSncwcDhPb1pZZ3hYaGNDYjRvNWZaTktNR1g2OUVoWG1JeVMzbUhyYU9XSVU0ZzA0TlFacWJHZXNEU2t6VWc2eHhxVFlHTVh4Y3dHVWpjU0gzSmxxSFJUM1c5eWR0L2RGdlhEQTdueUlzWjZSYTc2VEJHcVBuZXpKWFZHTE1xTkc5a3M4UVVhbWVuVkJyYjRESytZK0VoTkpCOUtid2M4a09KYkhKa090aXB5c1BHUFZxdDlsM1pGUjNaakFLNnBpQWNaczBhWWVBcjMvWEJ6ZTlwMVdaQW1CdlU0bFlaeXp0dUN2elM0VCtoUHNUZVIxQWk3a2tERlM3T1dMQU91TG9lOGFhMkNlZmREMldOR25oSTl0Nm1MdEs3U1pIcjFLR1RWV0VBaGlTMEV0RW8yWFRXQzI0QWFvR25RbTZjdUVocENLN1FJREV0OE5UU1NFRmZrNDRaNGJ2MUpYOVhTQm5nRDdmNTE0WWd3NGg4STlMMU1qRjYyeVJvYWlsNTZVMm4iLCJtYWMiOiI3ZTNhZmIyNDU5ZjExNDBlNWFjYmMxMjRlMWVjMzY5NTQ0YWZlNGViZDBlNWRhM2JlOThiMDc2MjVmYTM0NDY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962446602\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1968812128 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">19o8GZJlmCRhkVQqQRnrzKTy9LwoYzgyFnAzuAOO</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6Wt4M4bI8NbCUmFc1THCmhEuEOoDYpn2bPL8rDvY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968812128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-182360636 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:44:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBBenJoaC9tU284eDFLMmFxcnpRQlE9PSIsInZhbHVlIjoiSzVHS3lDY1k5RTVraURaQ0Jabi9ubzJIalB1T09QRUJaMkphT0ZxUVplazdOZ3cyU01wWENsZHRKYkoraEk4Ym1jeW1PYUd6V1NKd1A0djlGZCtVSE5UMTlJUkQyUXdVd0VUYjU1T2t6ZnBzR1U0b25XaG1DTWExOXpVTmZxMGtuZi9GcDJua3NzRlh4R1VyRVlQRWNUVXdndmNuQlJsK2lFME5zcEtWeTh2ZUQ2Q1YrUWNSMEZFVUFaQUlQbGk0cnFxTExwZXliWVU1Qzh4Q1YxQmY1Tk1oUWZ5ZjNRZnM4aWo0WnBGV3J2TkwzdXo5ZjlDdWlCbnZPNyszVDRxMVRaZ0lBZUtFeXBKZGxWQlVpUjF1S2FYNUNRU0w3ZW0rVkthbUs3cWI2b3NXMyt5SkdZUGpoc2Rmc0tzK1NJN0RhK0dzN2xBbUtrSmo3SjAvRlFSS0lEaUsrejBkTUZ0alRsN1Vrb1VoOElBUU5PZ1pOODY3aTNGVUN3SCtzRmZTVkpMeXp6R0hSZnhYTitNd3BMTmNFQzVQTDZxNERHSGg0di8zaVFZMGNUb29CSGdIaU9Ub0NpeThxLzd2R0k0UEErbW9iY0dBYkpFY0FCZlZiUVppdHhxc3h6VTk2dXNQWFNSVDdzL3V1TTdINldTOWhIYk5MUG04UFBNY24ydXMiLCJtYWMiOiI3ZGI3ODY2MTlhYWE3YTA3ZmM5ZTk1Y2RjMzRhMWM2MGE4MjI4NGNhYjJlZTEyODk3ZjFkOWNkZWM5NzFmZGE1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:44:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlAvaFFBcTNVLzNIWG9wWWQyTFZ3OUE9PSIsInZhbHVlIjoiZDdRTWJodlNMbktPSXZhSDJ2Q0FwMm5yM0NIWDV5TUlqYVJWQ2s3aFNsTmJsMWhQYXFEZFVST0tHMzNyMmRlQnhHSnVqSkoxdU82WDF5UEQ1NTd5SkhTZ0JjMzNVcGhBay92clhKeGpIZWJhSlYxUE5LckFlR3RXWERsbi81N3FiNXhLMjVLN0szeHBtWGRVM2l4eTFwMzJkdnBxd2MzaC90Wlc1RDZwajhUK2JYSHJLcWpRZmJmTzdBd042Y1VLVnFuL0E5NitDQ29MMmgvVnM0VmZiazY5UEFLZS9LSndkM3NrQWlEWjVDay81dkNSbXZwZ3dkRFVveHgzMWhTUDhhRDVOY3k4cGlDLzN2KzJIaXhoYXlCWkZwUjRMcWlqTWlRTWVBWWduUngzenFYdlRCakZlTHgyb1B2WDZ1M0RPMldVTzdzQmZNaFJicVhDdmVWWUZrYmw4M25USkdnREpaUStXUWdyMHkvb0tHakRlcGdLZ2FVUVU4dGg4ZFEyM2hTTHd0bHhkSDBCS2RlcVFIalozbHhsUU85MTJCZEc4c3JvNTdWQ3hpakpONHYvcGJIK2dIcm9nOGNJQWt6TS9TNzE3bGRUYUlzV21SNnZtMXNVZWZvMWphNmFRN3dEMmlNaStXbFNQUjMzeVdld3lnWmphdmdCb295dWYzdUgiLCJtYWMiOiI4N2U0YjIzZTc5NTY0ZTkxMDZjZTIwNjJjZjEyNDBjOTliNmQ4NDNlZjVlMGJhYmUxNDU2NjNkM2E2NjgyOWMxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:44:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBBenJoaC9tU284eDFLMmFxcnpRQlE9PSIsInZhbHVlIjoiSzVHS3lDY1k5RTVraURaQ0Jabi9ubzJIalB1T09QRUJaMkphT0ZxUVplazdOZ3cyU01wWENsZHRKYkoraEk4Ym1jeW1PYUd6V1NKd1A0djlGZCtVSE5UMTlJUkQyUXdVd0VUYjU1T2t6ZnBzR1U0b25XaG1DTWExOXpVTmZxMGtuZi9GcDJua3NzRlh4R1VyRVlQRWNUVXdndmNuQlJsK2lFME5zcEtWeTh2ZUQ2Q1YrUWNSMEZFVUFaQUlQbGk0cnFxTExwZXliWVU1Qzh4Q1YxQmY1Tk1oUWZ5ZjNRZnM4aWo0WnBGV3J2TkwzdXo5ZjlDdWlCbnZPNyszVDRxMVRaZ0lBZUtFeXBKZGxWQlVpUjF1S2FYNUNRU0w3ZW0rVkthbUs3cWI2b3NXMyt5SkdZUGpoc2Rmc0tzK1NJN0RhK0dzN2xBbUtrSmo3SjAvRlFSS0lEaUsrejBkTUZ0alRsN1Vrb1VoOElBUU5PZ1pOODY3aTNGVUN3SCtzRmZTVkpMeXp6R0hSZnhYTitNd3BMTmNFQzVQTDZxNERHSGg0di8zaVFZMGNUb29CSGdIaU9Ub0NpeThxLzd2R0k0UEErbW9iY0dBYkpFY0FCZlZiUVppdHhxc3h6VTk2dXNQWFNSVDdzL3V1TTdINldTOWhIYk5MUG04UFBNY24ydXMiLCJtYWMiOiI3ZGI3ODY2MTlhYWE3YTA3ZmM5ZTk1Y2RjMzRhMWM2MGE4MjI4NGNhYjJlZTEyODk3ZjFkOWNkZWM5NzFmZGE1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:44:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlAvaFFBcTNVLzNIWG9wWWQyTFZ3OUE9PSIsInZhbHVlIjoiZDdRTWJodlNMbktPSXZhSDJ2Q0FwMm5yM0NIWDV5TUlqYVJWQ2s3aFNsTmJsMWhQYXFEZFVST0tHMzNyMmRlQnhHSnVqSkoxdU82WDF5UEQ1NTd5SkhTZ0JjMzNVcGhBay92clhKeGpIZWJhSlYxUE5LckFlR3RXWERsbi81N3FiNXhLMjVLN0szeHBtWGRVM2l4eTFwMzJkdnBxd2MzaC90Wlc1RDZwajhUK2JYSHJLcWpRZmJmTzdBd042Y1VLVnFuL0E5NitDQ29MMmgvVnM0VmZiazY5UEFLZS9LSndkM3NrQWlEWjVDay81dkNSbXZwZ3dkRFVveHgzMWhTUDhhRDVOY3k4cGlDLzN2KzJIaXhoYXlCWkZwUjRMcWlqTWlRTWVBWWduUngzenFYdlRCakZlTHgyb1B2WDZ1M0RPMldVTzdzQmZNaFJicVhDdmVWWUZrYmw4M25USkdnREpaUStXUWdyMHkvb0tHakRlcGdLZ2FVUVU4dGg4ZFEyM2hTTHd0bHhkSDBCS2RlcVFIalozbHhsUU85MTJCZEc4c3JvNTdWQ3hpakpONHYvcGJIK2dIcm9nOGNJQWt6TS9TNzE3bGRUYUlzV21SNnZtMXNVZWZvMWphNmFRN3dEMmlNaStXbFNQUjMzeVdld3lnWmphdmdCb295dWYzdUgiLCJtYWMiOiI4N2U0YjIzZTc5NTY0ZTkxMDZjZTIwNjJjZjEyNDBjOTliNmQ4NDNlZjVlMGJhYmUxNDU2NjNkM2E2NjgyOWMxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:44:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182360636\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}