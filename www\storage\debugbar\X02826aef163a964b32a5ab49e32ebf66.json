{"__meta": {"id": "X02826aef163a964b32a5ab49e32ebf66", "datetime": "2025-06-06 20:37:05", "utime": **********.587164, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242223.949339, "end": **********.587213, "duration": 1.6378741264343262, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749242223.949339, "relative_start": 0, "end": **********.451628, "relative_end": **********.451628, "duration": 1.502289056777954, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.451659, "relative_start": 1.5023200511932373, "end": **********.587217, "relative_end": 4.0531158447265625e-06, "duration": 0.1355581283569336, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43085872, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00529, "accumulated_duration_str": "5.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.556959, "duration": 0.00529, "duration_str": "5.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SUUVTu80AHiedXP391HuTFHrIH0VO8njAH49hdss", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-676934352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-676934352\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-957202831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-957202831\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-365737787 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365737787\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1374968743 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1374968743\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1977059886 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:37:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVhU3JWMUpFbXlERU5ONFdmVngyd3c9PSIsInZhbHVlIjoicXZiWUJmblZsYTdVOFZJZ0JYVXNNWWZnamJCaElhZFNRU0ZsRlE4VXl5VHRITHEyQXBkc1QrTS9BWjVYQ095RHhsTElndENMTEhxVFppaEtkODdHRi8raUZIaVZMajVmbkh4OXJOTVlSdGRnUDNlRFJRSU1ocTRyNGRkZTAwNyswK1MyOUdCdWc1Z09WbWZvWFpLRUJ3YlpWRXBtWlNXcDM3N1ptd1M2aWxRckNlSnpLY2tCd0VMTENwN04vQXhEbzJueUpTYkF5WjFaWkVid0dhVDR5eTdSR1YrY3VES3hlbUFWc2RVemp6N1QwcFFMTFNXMjhrdGNoY2xEbjZiVVFsSHdoMkVJYStTQjl1Mm1WMVA2dHpOdlN3S21NY2hLSXVpWGkvZzhmem51YmdUend3MTZnNm9BMEhJVXY1M1Y4RS9BamtZR3kyNTloNmRmUDdHTHJDZTNYVVZkMFdMcjJsTlNUMTIxRWJUU2FwWUpwUHJlcHJKdnlpc3NzbHZVUk5yTmpqV21qMkR4VzZjYm8xTk5WaGhZMTBxczd5UVR3U3dOcG5LNytPbURySXJWQTBCVkV3c2xOWmtFQUNpaGpUbVMxRjIzTHVIN3E5Z0pGU211dXIrS0VqQlpVYU03Tk5vYk9pbGNyS0xJNlBUdXJKblhBdFRvdWVwMWtaWHkiLCJtYWMiOiI2YmIzMzE3MTdmOWU5NDBmMDY1NjdmNDczOWE1ZmYzYjQyNGE2YTEwY2M0NzU5YzRjNTFhMTc2Nzc0NzFlOTk0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkcwWitPYTdvejlLWTZzZ05mV0Y5dFE9PSIsInZhbHVlIjoibENTM1lOTk9HUHg5aWFYOG8wOGplZzJicmJ1d0hKMDhXZ2RIYWpoeWtYMjIvS3BkN3Bjc1JjWU1CWDBRUkZuYS9ZRnV4K2RYZUxYMUJjV2w2L2wxZHRxSmMwenljUzIzenFzelNjNjRiYkVRM2dGS0NITTJOblNwNlkrY2lUYnNXMW4xNmx5UHA5dmdtN2U0OEF2U0pKYU9jK2gyTFdmUzdvS1d1Y0hSQUpXZCtXQ2dHQUhHMVhxOWhGTis4REYyMWcxTjJGWDV0bDdGWVBidnM1OCsvMlJCMEpXWTlYZHV0WUFsc1YrTGo1eVdpNisyOUhKVmE3dDJSbVJFR2xDVTZtQTBudXlqRnNKYmk0UjJFTDd2NTU0R1RKaVZ0NWsySjBHQmhEYlR1SEhmUjllOGpFVllobkx2Y2RzdFRzRkRVUlkwM2FySkppcWVWNU5OVlcvQUVsU2UvaHNzd2w2RzF4RnBNS04vZldtUWxycUhXUlBLbG1YRzB2QVJGVVhzb2dEaHNwWmk0SWlHQ1RMWmVIajZoaWFtQlVUK0F1NVRnTWxrTGFrSXlnQ3lJVklpQStOdzZEWW55U2lNbFJPc1RjcU55TEFnbzRHaXlBamNjTFJLUTV4R2I5Qnc2SXhtQWd4NXlldllFbzVSYkgvU3BTRWY5NUhxN0VZYVdmNVkiLCJtYWMiOiIzMWE2YjEyNWMwNzk2N2ZhMGIyZmVlYzc4NDA4YmIyY2YxZGU2YzZmZGIzOTEzZjhkZjg5ZTNiZjYxZGNkMzkwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVhU3JWMUpFbXlERU5ONFdmVngyd3c9PSIsInZhbHVlIjoicXZiWUJmblZsYTdVOFZJZ0JYVXNNWWZnamJCaElhZFNRU0ZsRlE4VXl5VHRITHEyQXBkc1QrTS9BWjVYQ095RHhsTElndENMTEhxVFppaEtkODdHRi8raUZIaVZMajVmbkh4OXJOTVlSdGRnUDNlRFJRSU1ocTRyNGRkZTAwNyswK1MyOUdCdWc1Z09WbWZvWFpLRUJ3YlpWRXBtWlNXcDM3N1ptd1M2aWxRckNlSnpLY2tCd0VMTENwN04vQXhEbzJueUpTYkF5WjFaWkVid0dhVDR5eTdSR1YrY3VES3hlbUFWc2RVemp6N1QwcFFMTFNXMjhrdGNoY2xEbjZiVVFsSHdoMkVJYStTQjl1Mm1WMVA2dHpOdlN3S21NY2hLSXVpWGkvZzhmem51YmdUend3MTZnNm9BMEhJVXY1M1Y4RS9BamtZR3kyNTloNmRmUDdHTHJDZTNYVVZkMFdMcjJsTlNUMTIxRWJUU2FwWUpwUHJlcHJKdnlpc3NzbHZVUk5yTmpqV21qMkR4VzZjYm8xTk5WaGhZMTBxczd5UVR3U3dOcG5LNytPbURySXJWQTBCVkV3c2xOWmtFQUNpaGpUbVMxRjIzTHVIN3E5Z0pGU211dXIrS0VqQlpVYU03Tk5vYk9pbGNyS0xJNlBUdXJKblhBdFRvdWVwMWtaWHkiLCJtYWMiOiI2YmIzMzE3MTdmOWU5NDBmMDY1NjdmNDczOWE1ZmYzYjQyNGE2YTEwY2M0NzU5YzRjNTFhMTc2Nzc0NzFlOTk0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkcwWitPYTdvejlLWTZzZ05mV0Y5dFE9PSIsInZhbHVlIjoibENTM1lOTk9HUHg5aWFYOG8wOGplZzJicmJ1d0hKMDhXZ2RIYWpoeWtYMjIvS3BkN3Bjc1JjWU1CWDBRUkZuYS9ZRnV4K2RYZUxYMUJjV2w2L2wxZHRxSmMwenljUzIzenFzelNjNjRiYkVRM2dGS0NITTJOblNwNlkrY2lUYnNXMW4xNmx5UHA5dmdtN2U0OEF2U0pKYU9jK2gyTFdmUzdvS1d1Y0hSQUpXZCtXQ2dHQUhHMVhxOWhGTis4REYyMWcxTjJGWDV0bDdGWVBidnM1OCsvMlJCMEpXWTlYZHV0WUFsc1YrTGo1eVdpNisyOUhKVmE3dDJSbVJFR2xDVTZtQTBudXlqRnNKYmk0UjJFTDd2NTU0R1RKaVZ0NWsySjBHQmhEYlR1SEhmUjllOGpFVllobkx2Y2RzdFRzRkRVUlkwM2FySkppcWVWNU5OVlcvQUVsU2UvaHNzd2w2RzF4RnBNS04vZldtUWxycUhXUlBLbG1YRzB2QVJGVVhzb2dEaHNwWmk0SWlHQ1RMWmVIajZoaWFtQlVUK0F1NVRnTWxrTGFrSXlnQ3lJVklpQStOdzZEWW55U2lNbFJPc1RjcU55TEFnbzRHaXlBamNjTFJLUTV4R2I5Qnc2SXhtQWd4NXlldllFbzVSYkgvU3BTRWY5NUhxN0VZYVdmNVkiLCJtYWMiOiIzMWE2YjEyNWMwNzk2N2ZhMGIyZmVlYzc4NDA4YmIyY2YxZGU2YzZmZGIzOTEzZjhkZjg5ZTNiZjYxZGNkMzkwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977059886\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-293904865 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SUUVTu80AHiedXP391HuTFHrIH0VO8njAH49hdss</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293904865\", {\"maxDepth\":0})</script>\n"}}