<?php
/**
 * Branch Cash Management System
 * 
 * This file serves as a standalone page for the branch cash management functionality.
 * It displays a list of shifts with their financial records and allows for filtering
 * and detailed viewing of each shift's financial data.
 */

// Include necessary files and initialize the application
require_once __DIR__ . '/vendor/autoload.php';

use App\Http\Controllers\BranchCashManagementController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

// Check if user is authenticated
if (!Auth::check()) {
    header('Location: login.php');
    exit;
}

// Create a new request instance
$request = Request::capture();

// Initialize the controller
$controller = new BranchCashManagementController();

// Get the action from the URL parameter
$action = isset($_GET['action']) ? $_GET['action'] : 'index';
$id = isset($_GET['id']) ? $_GET['id'] : null;

// Handle different actions
switch ($action) {
    case 'show':
        if (!$id) {
            header('Location: branch-cash-management.php');
            exit;
        }
        $response = $controller->show($id);
        $view = $response->getOriginalContent()['view'];
        $data = $response->getOriginalContent()['data'];
        break;
    
    case 'update':
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $id) {
            $field = $_POST['field'] ?? '';
            $value = $_POST['value'] ?? '';
            
            // Create a request with the POST data
            $updateRequest = new Request([
                'field' => $field,
                'value' => $value
            ]);
            
            $response = $controller->updateFinancialRecord($updateRequest, $id);
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode($response->getOriginalContent());
            exit;
        }
        break;
    
    case 'index':
    default:
        // Pass GET parameters to the controller
        $request->merge($_GET);
        $response = $controller->index($request);
        $view = $response->getOriginalContent()['view'];
        $data = $response->getOriginalContent()['data'];
        break;
}

// Include the appropriate view template based on the action
if (isset($view) && isset($data)) {
    extract($data);
    include_once __DIR__ . '/resources/views/' . $view . '.php';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Cash Management</title>
    <!-- Include CSS files -->
    <link rel="stylesheet" href="public/css/app.css">
    <link rel="stylesheet" href="public/css/datatable/buttons.dataTables.min.css">
    <style>
        /* General Styles */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, #6259ca, #8567f7);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }

        .card-body {
            padding: 25px;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 10px;
            border: 1px solid #e1e5ef;
            padding: 10px 15px;
            transition: all 0.3s;
            box-shadow: none;
        }

        .form-control:focus, .form-select:focus {
            border-color: #6259ca;
            box-shadow: 0 0 0 0.2rem rgba(98, 89, 202, 0.25);
        }

        /* Table Styles */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            padding: 15px;
            vertical-align: middle;
            border-bottom: 2px solid #e9ecef;
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
            border-top: 1px solid #e9ecef;
        }

        /* Status Badges */
        .badge {
            padding: 6px 10px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .badge-success {
            background-color: #28a745;
            color: white;
        }

        .badge-danger {
            background-color: #dc3545;
            color: white;
        }

        /* Summary Cards */
        .summary-card {
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        .summary-card .title {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .summary-card .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
        }

        /* Back Button */
        .back-button {
            display: inline-flex;
            align-items: center;
            margin-bottom: 20px;
            color: #6259ca;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
        }

        .back-button:hover {
            transform: translateX(-5px);
            color: #5349b5;
        }

        .back-button i {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Content will be loaded from the included view -->
    </div>

    <!-- Include JavaScript files -->
    <script src="public/js/jquery.min.js"></script>
    <script src="public/js/bootstrap.bundle.min.js"></script>
    <script src="public/js/datatable/jquery.dataTables.min.js"></script>
    <script src="public/js/datatable/dataTables.buttons.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('.datatable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    'copy', 'csv', 'excel', 'pdf', 'print'
                ],
                pageLength: 10,
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Search...",
                    lengthMenu: "_MENU_ records per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ records",
                    infoEmpty: "Showing 0 to 0 of 0 records",
                    infoFiltered: "(filtered from _MAX_ total records)"
                }
            });

            // Format number function
            function formatNumber(number) {
                return new Intl.NumberFormat('ar-SA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(number);
            }

            // Parse formatted number
            function parseFormattedNumber(formattedNumber) {
                return parseFloat(formattedNumber.replace(/[^\d.-]/g, ''));
            }

            // Make cells editable
            $('.editable-cell').on('click', function() {
                var field = $(this).data('field');
                var id = $(this).data('id');
                var formattedValue = $(this).text().trim();
                var value = parseFormattedNumber(formattedValue);

                $('#editField').val(field);
                $('#editId').val(id);
                $('#editValue').val(value);
                $('#editModal').modal('show');
            });

            // Save changes
            $('#saveChanges').on('click', function() {
                var field = $('#editField').val();
                var id = $('#editId').val();
                var value = $('#editValue').val();

                $.ajax({
                    url: 'branch-cash-management.php?action=update&id=' + id,
                    type: 'POST',
                    data: {
                        field: field,
                        value: value
                    },
                    success: function(response) {
                        if(response.success) {
                            // Update the cell value with formatted number
                            $('[data-field="' + field + '"][data-id="' + id + '"]').text(formatNumber(response.data.value));
                            
                            // Close the modal
                            $('#editModal').modal('hide');
                            
                            // Show success message
                            alert('Updated successfully!');
                            
                            // Reload the page to refresh all calculations
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseText);
                    }
                });
            });
        });
    </script>
</body>
</html>
