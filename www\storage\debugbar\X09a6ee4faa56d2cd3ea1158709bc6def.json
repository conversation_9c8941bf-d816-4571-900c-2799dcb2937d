{"__meta": {"id": "X09a6ee4faa56d2cd3ea1158709bc6def", "datetime": "2025-06-06 19:26:56", "utime": **********.941925, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238015.325359, "end": **********.941956, "duration": 1.6165969371795654, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1749238015.325359, "relative_start": 0, "end": **********.742349, "relative_end": **********.742349, "duration": 1.416989803314209, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.742374, "relative_start": 1.4170148372650146, "end": **********.94196, "relative_end": 4.0531158447265625e-06, "duration": 0.1995861530303955, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.020630000000000003, "accumulated_duration_str": "20.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.839033, "duration": 0.01742, "duration_str": "17.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.44}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.884258, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.44, "width_percent": 5.235}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.894618, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 89.675, "width_percent": 5.817}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.917191, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.492, "width_percent": 4.508}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-890787407 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-890787407\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1781988464 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1781988464\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-815940791 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-815940791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-274866917 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238009820%7C28%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijd3K3pFYmdBVnBKalk0ZnI1dzNTZWc9PSIsInZhbHVlIjoiWTkvcCt2WS8xUUdxVVk1bVpSNjY0cVQwV3BNbkdLcnFKTmZpWHZ4Wko0a0hkU2gyWDNNM0lja0JVQnhFVkFjMHRNQXQxTGxIWnVsT1RRSHB3YzJ5M1J4UnQwbTJXbkNma2VkWS9oZDRYWWFmU2NLWXJibnVGZ0kxenNDZ0NJU1lTTStLYVlOd09ZNnVNZUN0UTlzOXJsanIxenI1MDlZcmJBYmxDUnVxR1NwT1hERUpSNU9yU3o2OER2UHp1SU1yMEJsRDFVaFNvNHl1c3Mycit4Qk5YbFJjeEFQZ0FJUTZEcHNMdkJUL2lrcll4Zjd3eGRObXBIVzNjeE81YlVWSmh1RmIzV1VkR0pyaDJXYzhraUlhU25ZbUxXaDUvSEZ1YkZNa0VrWnR5bHpvb21DSzYxRk1aVVpERFg1TkZyaTAvK0QrZHR2RFhBME5HMFpNc0RZODlUclR3MURuemVhZ1hIRUZEZ000bm9xK05jQzB3dUpUSHZkUXpab3Y4U0VnRU5UalJKV0dUa2k0eUM3Uk5rR3ZudnBYd1JHYkdpVm5wekk1ZytpVGlzaWw0cEMvOVp0Nk9rK0RvNktiZk5wNXIxWStsb2dUVFlXeVdoZ3MvV2hETUNhK25EcGJvWDF0cjA1dUx5Vk1NTlVqQlR2LzNWT1dpSWJ2elNIM0VwMloiLCJtYWMiOiJhMTBlMzQ5YWMwYjVhNWE3ZGYzOTI0YWJiMmIwMjNhN2U2NWNkNmQxYTY5Nzg1Nzc3NGI1NjI2NTA0MWRiMTIzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImcvVXZBNzdNOG8vUXBzNlpWa2FsaUE9PSIsInZhbHVlIjoibTd0aHY4UURnUVNNTVVrQUJUSERmNnlkMEs5bTZpRjlzMGNsM2E5MnRmN3YxOE9TOXQ0a2MzYnpWdmsveis5aExOSTNqR3VOZm93cExpYjFFb2NzNVAzSWF2OW01UEJRVURlSE91L1FLb3c3UnFhcU8xM20yRGp2aHQ4VE9HZURPVzVxVk0rRVk3WGtJWXZVZ3BVcEJxWTZQemYybi9jOFNDWmFGVnlSL2E4YUpEeStiUUhESjQ5Z3dOcThZTEM0ZGFvUVNFZzNrRlcrVWFTUlNRVmNwQW1VR0JRNE03MUkrVHNQRGI5RHdBV2xGbUxqVlQybHNMUko0dUxyR0doWGc4TS95SkVlTk1nOEUvVVNrWkxnYUxFNE9kZ0ZPd0F5bVNzRkpVMGExUmttQVVzaHBnQ1h2WHVpNm5pK004bEhlZFBTWlNsSTFETUo1OEcwMi9sN05tRFVjb2p5OVFYK1ZYUmp0cWVTN0R3R0Y5NDR0dEVCWDNUOVhlYmhuaHdRTXVvZG5uMDBOQ0pwK0YxazByVHdpbk13VXV3eWViZEdEemw1dW9zcVNkSnQwS1JzYjJxSXg5Tjc0aXJRZ3FhYmIxelFJdm9RSEhiMkFIZjJuU0VLT0NJNU5mVEtsVTRtMXhwVVZVcHFKSHJ0UkpkK3ZBYTZaZWNiZDhtMjNNQXgiLCJtYWMiOiIyYzZkZWM2MWFiYzAzMzM2NmRkOTI0OWJiNzM5MzU5Mzg3MmRjYWM2OTUzYmRiM2Y5ZGI1MjI1ZTljM2ZmNDlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274866917\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1280906404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280906404\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1291980321 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:26:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZMWmMrYURaeGt5T0tTS0FyeVBiWFE9PSIsInZhbHVlIjoiampRNHJYc2hoQ3k0UFJTZ3NrczUwV2hVNFRCYWhwZmgzOXA3RGFuV0E3cmFkbVl6Y1FiaC9QWUhPWEZxWTM4MHpxN1FlRG5iek5Xbm52dEROdWZGODlxZEY0L3BFRFFLbDZGQnNSdVdtVk5kNXBKMjBrNlQwR3JsbFdldStWNUFnTE4wbkdpR09WeC9zZDc4akF0MkJRUnZpN2NYSXpMdjdERU5zTUtnQkh3QXpuWThZN1BuYnZUelRQZkM2ZHdPMytBN2dyTUtGVEV5SEYwdTIvVFJTSnFMRDI4MVU0ZVRESm1zSkUzVjZDTnNwbkZjTEttWHY1YmNEQWNnZVhvT2UxZTNwblhBMXFobHZrME1sZDRZeTVzM2x2UnMrOEpud21Ebi83NmxIWlJ5bTRiWG1sTlU4Zm91WTJ3QmVQNE5LVmdxdWxxZVU0Rm1GK01qdnBObjhrRXVmdld0alNNbXFYWG1GWjBmYWhTaEtHMFZITysvRGs4WWJlTE1lOWlwV3puTFBremdsbE9ka2hqK0NEYkVjUVVFM3ZFQzBUeHJMVU02elR2bGtoUVVOS0FUZGpkT1F5SjRNUGxtMmdCOHhLZ0JuR0hhTE1PWHhlc0FTWTJ5R2RsNlBTZDVFQ2MrOGVKdGpRREFpUzlVeFNTekE5dFNJSnQ5eFZ1ZllSRGIiLCJtYWMiOiIxNmZlODVkZjE2NzQ0MTNmYTI0MzRhYTg3NTBlNGRiZDZiYTY2NTI3YWJmN2ZjNDU4MDhlNGY5MTBjM2M0OGJkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImIrM0JaZ09STnVKa2JpZ2IzOHEza2c9PSIsInZhbHVlIjoiVUU2NTE3NGRpZDBiYzJlU0hHa2RydXZrQkt5KzFVY0xjTi9rU3dQZ0IxRExBQnVwYXlGb2tXRnRuUE5VNzdUOE5TY01IQXhOM3VHSFpRV3RlSDFuaHdTcHJjbGJwUFZjNVU0MFljYVhEZ1ArNHFxMXBLU0tMaUZseW9ScElyRklZeXpaU250NGNSM3E5U1hxN3FGSkMwUHByZk1hKzZLWDNUQlJOdkE4VllXa0tZS2lEWnZtZzRTMWZIWElvS1VTSWZoTzI0TmFrRGtlTWpLMkNyRW4vQVlsVFFsQ2R0cWkvWUZVcTVhUWl1eGtGa2JPYzhnK3ZEWHhONDhFbGFHOFBOSXVPT2VZWGgvME1HMDE4MGZQUXFWUjMvQy9ZSzJtKzdzSFVaQnlhTDJWSVk3NlV0b0V1UUFPWUhjRU9uai91Nnh4UlZ5S0tTYVBDTGZGdjRSSXdCMUhOOHNma01YQjhnVW5GeTRzNjNrTzlGRHhEUjNhc25SdXViN3Y3R1hYQTNrQzNNSFY2TTVCcm5sdXR4bzVLd2JxaG01UHBBdHBrWW1DSTdRb05JdkRFT0xGY2tyRHdnakw1TTgreVhuQ2g3QmdBMTEyTHVXTG1DS1d5YSs4VmJYRVBKQlVmUFpYQ3hzOHhHTlFHSTBOMzEzN0xKUUUzQzhmbVVxOENHckEiLCJtYWMiOiI2ODMzYTA1NDI2MTlmYTZmNDU4ODk0M2ZlY2I5M2U5Mjc0MzkyNmUwZmIwZmI4NmFiYzAzNjVhNTEyZDQ4MDljIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZMWmMrYURaeGt5T0tTS0FyeVBiWFE9PSIsInZhbHVlIjoiampRNHJYc2hoQ3k0UFJTZ3NrczUwV2hVNFRCYWhwZmgzOXA3RGFuV0E3cmFkbVl6Y1FiaC9QWUhPWEZxWTM4MHpxN1FlRG5iek5Xbm52dEROdWZGODlxZEY0L3BFRFFLbDZGQnNSdVdtVk5kNXBKMjBrNlQwR3JsbFdldStWNUFnTE4wbkdpR09WeC9zZDc4akF0MkJRUnZpN2NYSXpMdjdERU5zTUtnQkh3QXpuWThZN1BuYnZUelRQZkM2ZHdPMytBN2dyTUtGVEV5SEYwdTIvVFJTSnFMRDI4MVU0ZVRESm1zSkUzVjZDTnNwbkZjTEttWHY1YmNEQWNnZVhvT2UxZTNwblhBMXFobHZrME1sZDRZeTVzM2x2UnMrOEpud21Ebi83NmxIWlJ5bTRiWG1sTlU4Zm91WTJ3QmVQNE5LVmdxdWxxZVU0Rm1GK01qdnBObjhrRXVmdld0alNNbXFYWG1GWjBmYWhTaEtHMFZITysvRGs4WWJlTE1lOWlwV3puTFBremdsbE9ka2hqK0NEYkVjUVVFM3ZFQzBUeHJMVU02elR2bGtoUVVOS0FUZGpkT1F5SjRNUGxtMmdCOHhLZ0JuR0hhTE1PWHhlc0FTWTJ5R2RsNlBTZDVFQ2MrOGVKdGpRREFpUzlVeFNTekE5dFNJSnQ5eFZ1ZllSRGIiLCJtYWMiOiIxNmZlODVkZjE2NzQ0MTNmYTI0MzRhYTg3NTBlNGRiZDZiYTY2NTI3YWJmN2ZjNDU4MDhlNGY5MTBjM2M0OGJkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImIrM0JaZ09STnVKa2JpZ2IzOHEza2c9PSIsInZhbHVlIjoiVUU2NTE3NGRpZDBiYzJlU0hHa2RydXZrQkt5KzFVY0xjTi9rU3dQZ0IxRExBQnVwYXlGb2tXRnRuUE5VNzdUOE5TY01IQXhOM3VHSFpRV3RlSDFuaHdTcHJjbGJwUFZjNVU0MFljYVhEZ1ArNHFxMXBLU0tMaUZseW9ScElyRklZeXpaU250NGNSM3E5U1hxN3FGSkMwUHByZk1hKzZLWDNUQlJOdkE4VllXa0tZS2lEWnZtZzRTMWZIWElvS1VTSWZoTzI0TmFrRGtlTWpLMkNyRW4vQVlsVFFsQ2R0cWkvWUZVcTVhUWl1eGtGa2JPYzhnK3ZEWHhONDhFbGFHOFBOSXVPT2VZWGgvME1HMDE4MGZQUXFWUjMvQy9ZSzJtKzdzSFVaQnlhTDJWSVk3NlV0b0V1UUFPWUhjRU9uai91Nnh4UlZ5S0tTYVBDTGZGdjRSSXdCMUhOOHNma01YQjhnVW5GeTRzNjNrTzlGRHhEUjNhc25SdXViN3Y3R1hYQTNrQzNNSFY2TTVCcm5sdXR4bzVLd2JxaG01UHBBdHBrWW1DSTdRb05JdkRFT0xGY2tyRHdnakw1TTgreVhuQ2g3QmdBMTEyTHVXTG1DS1d5YSs4VmJYRVBKQlVmUFpYQ3hzOHhHTlFHSTBOMzEzN0xKUUUzQzhmbVVxOENHckEiLCJtYWMiOiI2ODMzYTA1NDI2MTlmYTZmNDU4ODk0M2ZlY2I5M2U5Mjc0MzkyNmUwZmIwZmI4NmFiYzAzNjVhNTEyZDQ4MDljIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291980321\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2109462725 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109462725\", {\"maxDepth\":0})</script>\n"}}