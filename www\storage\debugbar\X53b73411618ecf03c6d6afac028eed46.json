{"__meta": {"id": "X53b73411618ecf03c6d6afac028eed46", "datetime": "2025-06-06 19:15:22", "utime": **********.161711, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.93184, "end": **********.161737, "duration": 1.****************, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": **********.93184, "relative_start": 0, "end": **********.995331, "relative_end": **********.995331, "duration": 1.****************, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.99535, "relative_start": 1.****************, "end": **********.16174, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00798, "accumulated_duration_str": "7.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0738258, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.15}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.111482, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.15, "width_percent": 17.669}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.141431, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 77.82, "width_percent": 22.18}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ink5MytYZlJUemdLdDRwVUVOYUxremc9PSIsInZhbHVlIjoiWWZ2dFJhdEtZMmNMdU8rT3ZpNzRaUGVRTTJ6ZGJINDM3M0kybXl2M0xGb2JYUVRDS2dTTFJRNmhoQ3NlWittbmZQbU12UEpKK2RBdDUvRnNjWW90RU4xTzFVREVLVXVQUXUwaEtSTHk4MFJ3U1JncjdYYml2NXN6OGxqZkRKNS9vOXR3cHBNRDg4ZnE4enh4bE1WMUFBQjlsU2ovN3d4bkRiMHY3bC9XcFdNd0IvZmVOeUx2Ykk3T1pVTWRkWktWZkJ0SjNGNUtFVnpValpYYzYwQmxGVzEvMFBRazd2M2orMktNOFpMWmFlNWdPOUNjNjdqOWp2cHVWUUZwYndNZjRoODVKcjE5dFB6STUvck9aYzFOT3k5cjM5SjZ6enVzSytXUm1wZ1Bzb3ovWi9sMHNFYXRoMFJaMFFjdkg0bitjZldZVzJtNGg0UzU0eFdxa3pBc2U2VDc3dEVJbFZLTFlGZSt6ZmY4SHhXOTROTHJFNHhCdGZsRllXTEUxbHo2YURoYmg5Q2k4VjIrVnYvYXdVNW5tMjl0UlNPU2hVTFh1clgzc0hYYi80NUpScHk4WkNmbWIvQnZHcHduZCtWQ1pDNmpoaVU5VWNvVGVJaU1kMXE4V0tRRmJKT25STFVzaFhObU12d1lOQXVaQW8xOFp0NThjYkVEN3RTMUVSaHoiLCJtYWMiOiIxMWVlMmEzMzc2M2U1ODIxZjY3OTBiYWVjMmE4YTY5YTI5YzY1YzJlMWIzNmZiYzU2YjA2MzRkODkwMzYwZDBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNSd3huU3VaRFJEaVptaGxRMFFpa1E9PSIsInZhbHVlIjoiMFRIL3p4UE5oYUZSbU1ZOEZIT2M4VERRbEJQVjBndkI0d1J3cnBVQ09hTENrT2dUaEZPK1hlYWE2TkJCZmpzSWhNMFpLQVRhbEkyMkFZQXFQSHpGdEs4RU80SVdNVnlCTnZsUk8wQ1VmdTg3dWEwZTJBOUZFbUxFb0lwMVZSSUp4TWk5U2pUMjZzUXBqVmZUejczdTRjYkhzdzZWWk1ENU90dnNpZXZUajdtQmdKc243czdWVmxRUWZzRitkQVZZaFQ4ckVGR3pKZGtNbEtSTHBzeFBYZE56QTZWaENzbU9GSW1JWEcvS2tVYk1DaGF3cWRPVEg3aHNFc0dUR1JVdUlITzFKRFEzSHhrei8zaWxsdHk4aWJtTWtCV3BINXB3YkRXUktZNjQzcnpBV0hYOEpKMi9mc3FMZmlTSzFTUHRnUzYvb29qQ3NsRDBPQTZSaDVyTm1rb3dEQkZ1RHlvOWgrMElQZGhXb09lbXF6dEtxUDIvZDFqbkUzZ3gyNWNvdnF4OFdaVk10aVlUakJ0ZDNSaWRRSlhiTnJXdVpaUUZYb0tIYWxuQ1V6WFpzZjZpSWRZSVpCYUZ6c21SY0dpYVp1YUtIMHZTcjBySFdKbmRHaUVreDB2d2lkVHFueU91WjJmRGF6VHM5WE15NjB3aXZ2T0RnVTFhRlFTZzZQOWkiLCJtYWMiOiI2N2U3NTlkMDQ0Njk1NTllNTc1NTA5N2RmYTYwODEzOTU4MmY0MTJkNjIyYmU1YjI2OWM0NzMwMzI3OGIwMzFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-983558418 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">07zLQ1IzhcCwivgYmqVlKD2YgfwQ5DGdasvfNucP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983558418\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-69642274 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:15:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdiVTBvYXFxMnFsb0JnYUZPdnI3Znc9PSIsInZhbHVlIjoiOWFJbzUrbmw1ZVc3RGJDQ1NzMzVLdmc2OVBsbzNBSE05STR3dUN6WDhBNlE3YWx0UTc0U3oxZVhjOUtHRjhUc3lvcWhtWVlJTHNtL2dKZFdMTmsrQm93SC8rMW05RkU4STQwelo1bGZYbURxM0RJRS9Fbkp4YjVFTVdOb0RDNG80bXREeFpocVdPcklucmpoMnpZRWVRRUwrUEI0a3YwQWw3MmdON1Q3bm1rWkxZMEFmWVk0Z1NCRDA1ZmtYRE5oMkxLT0N0c3ZGSks0ZlBZeWp4cStYL3ZMdlBSeG9LOUtUVGRtNTg1QkZKQmFDY2FUbkthSzN6cEEreXljdGRYZldXa0ZYVmxFNUdiVXE2cUN6WTJ1SVVnY2xWZnRWdU43aGVkVWJOaTE2NkJNQlFCODNPQkdKRDZiT3o4dUtEQ09DL0Vta2QvREUvRmI4VGsyMFhpQVhBVlAxdXYwY2FQVjRyWDZ5b3pYWGl3MDhVK1lwZWJUdlBvNkcrRGdCRDdZOTlUVnEzRERKT1ZGRFRvMk4wc2FMdUNKbnpJTWh6M0EvSWs0aGJ4L09vbU42MU40YTlmdndaMFNRbUR4Y0FYQkphT0ZzaXhyY3c3UC8vM1ZQd0hjV2hGNXFiY3NlSEdkeGxBKzlQMXJHdG9VM3M4SHRwWGs5YnBBZlltUlViZ0ciLCJtYWMiOiJmMDc5YWUyYTc2ZDk0NWVjYWM0ZjBiNjk5NGMyMzcyYTRkNDNjYWJlMTk2MmE3ODcxMzAxNzUyZmVhNmQxZDFkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhRUnVDb3R5ME5YQ1FiSmEvSDl3d3c9PSIsInZhbHVlIjoiUllKOXg2UmtLYU9kNHpIaS9NcU5OL0oxbDc5VWc3blNvQU1vNi9FeE5kUW1aMEtpWWlPdjFIWm50WHVlY2RyYTlIemRQejJwNkorNDkyK09iajZaMlhWbzRMM2xNdTc5aHRrL2k1TFZQcWp1S0NzRE04YmNDVTdxaW5jSW5RZWNjMzFrcUFVeWtLNkNlOEtDSTV3NmJlVWJDbldNcEpFakUyWXNJVytyQnkveGNkeUVxb0liOW5KYVdqV0tqLzFSbkdBNGhidCt3MjJHOEpHaDZ6U2RKazBSeGE2WnRIbDBUTHlWcnRLOTNhZUMvdlBOL3MrNFM4MTJndDRSa0ZWdmVCelNnd3kzSms1cCttWkd4MDFubWZ1cVljRWgzRW96bnBtTGNuRzN4eFErZ2xQR2JqQlF2Umh6MStHeWhPY1I0L09PV1BwamFPdWN4bWFJSUFsemlZak8yQWdEQUtyK3docEZvdGYvamdEN1FjMEZDeXJSVGo5MmVVQ1cwMGErYVVGM2Y1RzJRS05LVnFFcVBRc01CanFkOXpoaVFQbm5Bbk1zblZwT29mSzk2ak9LVm9HMDhRS0d6VzNVT2NCZEhiVXp2dXhkNmhHdmF1ei9VWlQ3SlpXZXN1dWJmRG16SmpTUjR5cXl2ckVIRlRoaU4yUTkwVEFYYzZ3c01ORlAiLCJtYWMiOiIwZjRmNTU2ZTkzZWNhNDM2MGY5MTZkZDRlZDg0YjRhN2U3NjYzN2Y5MTIyZGI5NjQ1OTI0NDlkMGZkZmIwZDI0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdiVTBvYXFxMnFsb0JnYUZPdnI3Znc9PSIsInZhbHVlIjoiOWFJbzUrbmw1ZVc3RGJDQ1NzMzVLdmc2OVBsbzNBSE05STR3dUN6WDhBNlE3YWx0UTc0U3oxZVhjOUtHRjhUc3lvcWhtWVlJTHNtL2dKZFdMTmsrQm93SC8rMW05RkU4STQwelo1bGZYbURxM0RJRS9Fbkp4YjVFTVdOb0RDNG80bXREeFpocVdPcklucmpoMnpZRWVRRUwrUEI0a3YwQWw3MmdON1Q3bm1rWkxZMEFmWVk0Z1NCRDA1ZmtYRE5oMkxLT0N0c3ZGSks0ZlBZeWp4cStYL3ZMdlBSeG9LOUtUVGRtNTg1QkZKQmFDY2FUbkthSzN6cEEreXljdGRYZldXa0ZYVmxFNUdiVXE2cUN6WTJ1SVVnY2xWZnRWdU43aGVkVWJOaTE2NkJNQlFCODNPQkdKRDZiT3o4dUtEQ09DL0Vta2QvREUvRmI4VGsyMFhpQVhBVlAxdXYwY2FQVjRyWDZ5b3pYWGl3MDhVK1lwZWJUdlBvNkcrRGdCRDdZOTlUVnEzRERKT1ZGRFRvMk4wc2FMdUNKbnpJTWh6M0EvSWs0aGJ4L09vbU42MU40YTlmdndaMFNRbUR4Y0FYQkphT0ZzaXhyY3c3UC8vM1ZQd0hjV2hGNXFiY3NlSEdkeGxBKzlQMXJHdG9VM3M4SHRwWGs5YnBBZlltUlViZ0ciLCJtYWMiOiJmMDc5YWUyYTc2ZDk0NWVjYWM0ZjBiNjk5NGMyMzcyYTRkNDNjYWJlMTk2MmE3ODcxMzAxNzUyZmVhNmQxZDFkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhRUnVDb3R5ME5YQ1FiSmEvSDl3d3c9PSIsInZhbHVlIjoiUllKOXg2UmtLYU9kNHpIaS9NcU5OL0oxbDc5VWc3blNvQU1vNi9FeE5kUW1aMEtpWWlPdjFIWm50WHVlY2RyYTlIemRQejJwNkorNDkyK09iajZaMlhWbzRMM2xNdTc5aHRrL2k1TFZQcWp1S0NzRE04YmNDVTdxaW5jSW5RZWNjMzFrcUFVeWtLNkNlOEtDSTV3NmJlVWJDbldNcEpFakUyWXNJVytyQnkveGNkeUVxb0liOW5KYVdqV0tqLzFSbkdBNGhidCt3MjJHOEpHaDZ6U2RKazBSeGE2WnRIbDBUTHlWcnRLOTNhZUMvdlBOL3MrNFM4MTJndDRSa0ZWdmVCelNnd3kzSms1cCttWkd4MDFubWZ1cVljRWgzRW96bnBtTGNuRzN4eFErZ2xQR2JqQlF2Umh6MStHeWhPY1I0L09PV1BwamFPdWN4bWFJSUFsemlZak8yQWdEQUtyK3docEZvdGYvamdEN1FjMEZDeXJSVGo5MmVVQ1cwMGErYVVGM2Y1RzJRS05LVnFFcVBRc01CanFkOXpoaVFQbm5Bbk1zblZwT29mSzk2ak9LVm9HMDhRS0d6VzNVT2NCZEhiVXp2dXhkNmhHdmF1ei9VWlQ3SlpXZXN1dWJmRG16SmpTUjR5cXl2ckVIRlRoaU4yUTkwVEFYYzZ3c01ORlAiLCJtYWMiOiIwZjRmNTU2ZTkzZWNhNDM2MGY5MTZkZDRlZDg0YjRhN2U3NjYzN2Y5MTIyZGI5NjQ1OTI0NDlkMGZkZmIwZDI0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69642274\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1241259898 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1241259898\", {\"maxDepth\":0})</script>\n"}}