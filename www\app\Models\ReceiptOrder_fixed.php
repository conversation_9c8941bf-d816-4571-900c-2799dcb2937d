<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReceiptOrder extends Model
{
    use HasFactory;

    protected $table = 'receipt_orders';

    protected $fillable = [
        'order_number',
        'order_type',
        'vendor_id',
        'warehouse_id',
        'from_warehouse_id',
        'invoice_number',
        'invoice_total',
        'invoice_date',
        'has_return',
        'total_products',
        'total_amount',
        'notes',
        'exit_reason',
        'exit_date',
        'responsible_person',
        'created_by',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'exit_date' => 'date',
        'invoice_total' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'has_return' => 'boolean',
        'total_products' => 'integer',
    ];

    /**
     * العلاقة مع المورد
     */
    public function vendor()
    {
        return $this->belongsTo(Vender::class, 'vendor_id');
    }

    /**
     * العلاقة مع المستودع الهدف
     */
    public function warehouse()
    {
        return $this->belongsTo(warehouse::class, 'warehouse_id');
    }

    /**
     * العلاقة مع المستودع المصدر (للنقل)
     */
    public function fromWarehouse()
    {
        return $this->belongsTo(warehouse::class, 'from_warehouse_id');
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع منتجات الأمر
     */
    public function products()
    {
        return $this->hasMany(ReceiptOrderProduct::class, 'receipt_order_id');
    }

    /**
     * إنشاء رقم أمر تلقائي
     */
    public static function generateOrderNumber()
    {
        $year = date('Y');
        $count = self::whereYear('created_at', $year)->count() + 1;
        return 'RO-' . $year . '-' . str_pad($count, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Boot method لإنشاء رقم الأمر تلقائياً
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->order_number)) {
                $model->order_number = self::generateOrderNumber();
            }
        });
    }
}
