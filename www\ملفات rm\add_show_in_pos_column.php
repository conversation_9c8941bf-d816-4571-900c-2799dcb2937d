<?php
// This is a standalone script to add the show_in_pos column to the product_service_categories table
// Save this file in your Laravel project root and run it with: php add_show_in_pos_column.php

// Load Laravel environment
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "Starting column addition process...\n";

try {
    // Check if the column already exists
    $hasColumn = Schema::hasColumn('product_service_categories', 'show_in_pos');
    
    if ($hasColumn) {
        echo "Column 'show_in_pos' already exists in the table.\n";
    } else {
        // Add the column
        echo "Adding 'show_in_pos' column to product_service_categories table...\n";
        DB::statement('ALTER TABLE `product_service_categories` ADD COLUMN `show_in_pos` TINYINT(1) NOT NULL DEFAULT 1 AFTER `color`');
        
        // Update existing records
        echo "Updating existing records...\n";
        DB::statement("UPDATE `product_service_categories` SET `show_in_pos` = 1 WHERE `type` = 'product & service'");
        DB::statement("UPDATE `product_service_categories` SET `show_in_pos` = 0 WHERE `type` != 'product & service'");
        
        echo "Column added successfully!\n";
    }
    
    // Verify the column exists
    $hasColumn = Schema::hasColumn('product_service_categories', 'show_in_pos');
    echo "Column 'show_in_pos' exists in the table: " . ($hasColumn ? "Yes" : "No") . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "Process completed.\n";
