#!/bin/bash

# سكريبت إصلاح سريع لجدول منتجات أوامر الاستلام
# يحل مشكلة فشل تثبيت الجدول الثاني

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# متغيرات - عدل هذه القيم حسب خادمك
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"
DB_USER="database_username"
DB_NAME="database_name"

# دالة لطباعة الرسائل الملونة
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              إصلاح جدول منتجات أوامر الاستلام                ║"
echo "║           Fix Receipt Order Products Table Issue            ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_info "بدء عملية إصلاح جدول منتجات أوامر الاستلام..."

# الطريقة الأولى: استخدام Laravel Migration المبسط
print_status "🔧 الطريقة الأولى: استخدام Laravel Migration المبسط..."

# نقل الملف المبسط
print_info "نقل ملف الهجرة المبسط..."
scp database/migrations/2024_01_15_000002_create_receipt_order_products_table_simple.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/2024_01_15_000002_create_receipt_order_products_table.php

# محاولة تشغيل الهجرة
print_info "محاولة تشغيل الهجرة المبسطة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate"

if [ $? -eq 0 ]; then
    print_status "✅ نجحت الطريقة الأولى!"
    
    # التحقق من الجدول
    print_info "التحقق من إنشاء الجدول..."
    ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e 'DESCRIBE receipt_order_products;'"
    
    print_status "🎉 تم إصلاح المشكلة بنجاح!"
    exit 0
else
    print_warning "فشلت الطريقة الأولى، جاري المحاولة بالطريقة الثانية..."
fi

# الطريقة الثانية: استخدام SQL مباشرة
print_status "🔧 الطريقة الثانية: استخدام SQL مباشرة..."

# نقل ملف SQL
print_info "نقل ملف SQL المباشر..."
scp create_receipt_order_products_only.sql $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/

# تشغيل SQL مباشرة
print_info "تشغيل SQL مباشرة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME < create_receipt_order_products_only.sql"

if [ $? -eq 0 ]; then
    print_status "✅ نجحت الطريقة الثانية!"
    
    # تحديث جدول migrations يدوياً
    print_info "تحديث جدول migrations..."
    ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e \"INSERT INTO migrations (migration, batch) VALUES ('2024_01_15_000002_create_receipt_order_products_table', (SELECT MAX(batch) FROM (SELECT batch FROM migrations) as temp));\""
    
    print_status "🎉 تم إصلاح المشكلة بنجاح!"
    exit 0
else
    print_error "فشلت الطريقة الثانية أيضاً"
fi

# الطريقة الثالثة: إنشاء يدوي مبسط
print_status "🔧 الطريقة الثالثة: إنشاء يدوي مبسط..."

print_info "إنشاء الجدول بأبسط شكل ممكن..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e '
CREATE TABLE IF NOT EXISTS receipt_order_products (
    id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    receipt_order_id bigint(20) UNSIGNED NOT NULL,
    product_id bigint(20) UNSIGNED NOT NULL,
    quantity decimal(15,2) NOT NULL,
    unit_cost decimal(15,2) DEFAULT 0.00,
    total_cost decimal(15,2) DEFAULT 0.00,
    expiry_date date DEFAULT NULL,
    is_return tinyint(1) DEFAULT 0,
    notes text DEFAULT NULL,
    created_at timestamp NULL DEFAULT NULL,
    updated_at timestamp NULL DEFAULT NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;'"

if [ $? -eq 0 ]; then
    print_status "✅ نجحت الطريقة الثالثة!"
    
    # إضافة الفهارس
    print_info "إضافة الفهارس..."
    ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e '
    ALTER TABLE receipt_order_products ADD INDEX idx_receipt_order_id (receipt_order_id);
    ALTER TABLE receipt_order_products ADD INDEX idx_product_id (product_id);
    ALTER TABLE receipt_order_products ADD INDEX idx_created_at (created_at);'"
    
    # تحديث جدول migrations
    print_info "تحديث جدول migrations..."
    ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e \"INSERT IGNORE INTO migrations (migration, batch) VALUES ('2024_01_15_000002_create_receipt_order_products_table', (SELECT MAX(batch) FROM (SELECT batch FROM migrations) as temp));\""
    
    print_status "🎉 تم إصلاح المشكلة بنجاح!"
else
    print_error "فشلت جميع الطرق - يرجى فحص إعدادات قاعدة البيانات"
    exit 1
fi

# التحقق النهائي
print_status "✅ التحقق النهائي من الجداول..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mysql -u $DB_USER -p $DB_NAME -e 'SHOW TABLES LIKE \"receipt_%\";'"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt"

print_status "🎯 تم إصلاح جدول منتجات أوامر الاستلام بنجاح!"
print_info "يمكنك الآن اختبار الصفحات الجديدة"
