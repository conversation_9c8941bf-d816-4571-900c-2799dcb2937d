# تشخيص وحل خطأ 500 - صفحة التسعير

## 🔍 تحليل المشكلة

خطأ 500 (Internal Server Error) يحدث عادة بسبب:

### 1. مشاكل محتملة في الكود
- **خطأ في Syntax** 
- **استدعاء Class غير موجود**
- **استدعاء Method غير موجود**
- **مشكلة في namespace**

### 2. مشاكل في قاعدة البيانات
- **جداول غير موجودة**
- **أعمدة غير موجودة**
- **علاقات مكسورة**

### 3. مشاكل في الصلاحيات
- **صلاحيات ملفات خاطئة**
- **مجلدات غير موجودة**

## 🛠️ خطوات التشخيص

### الخطوة 1: فحص ملف الـ Routes
```php
// تأكد من وجود هذه الأسطر في routes/web.php

// في أعلى الملف مع باقي الـ imports
use App\Http\Controllers\PricingController;

// في منطقة الـ routes (حوالي السطر 1726)
Route::get('pricing', [PricingController::class, 'index'])->name('pricing.index')->middleware(['auth', 'XSS']);
Route::get('pricing/simple', [PricingController::class, 'simple'])->name('pricing.simple')->middleware(['auth', 'XSS']);
Route::post('pricing/update-inline', [PricingController::class, 'updateInline'])->name('pricing.update.inline')->middleware(['auth', 'XSS']);
Route::get('pricing/field-options', [PricingController::class, 'getFieldOptions'])->name('pricing.field.options')->middleware(['auth', 'XSS']);
```

### الخطوة 2: فحص وجود الملفات
```bash
# تأكد من وجود هذه الملفات:
app/Http/Controllers/PricingController.php
resources/views/pricing/index.blade.php
resources/views/pricing/simple.blade.php
```

### الخطوة 3: فحص صلاحيات الملفات
```bash
# تأكد من صلاحيات الملفات (755 للمجلدات، 644 للملفات)
chmod 755 app/Http/Controllers/
chmod 644 app/Http/Controllers/PricingController.php
chmod 755 resources/views/pricing/
chmod 644 resources/views/pricing/*.blade.php
```

### الخطوة 4: مسح Cache
```bash
# قم بتشغيل هذه الأوامر على السيرفر
php artisan route:clear
php artisan config:clear
php artisan view:clear
php artisan cache:clear
```

## 🔧 حلول محتملة

### الحل 1: إعادة إنشاء Routes (الأكثر احتمالاً)

إذا كان هناك خطأ في ملف routes/web.php، استبدل الأسطر المتعلقة بالتسعير:

```php
// احذف الأسطر القديمة واستبدلها بهذه:

// في أعلى الملف مع باقي الـ use statements (حوالي السطر 110)
use App\Http\Controllers\PricingController;

// في منطقة الـ routes (ضعها بعد routes المشتريات)
// Pricing Management Routes
Route::get('pricing', [PricingController::class, 'index'])->name('pricing.index')->middleware(['auth', 'XSS']);
Route::get('pricing/simple', [PricingController::class, 'simple'])->name('pricing.simple')->middleware(['auth', 'XSS']);
Route::post('pricing/update-inline', [PricingController::class, 'updateInline'])->name('pricing.update.inline')->middleware(['auth', 'XSS']);
Route::get('pricing/field-options', [PricingController::class, 'getFieldOptions'])->name('pricing.field.options')->middleware(['auth', 'XSS']);
```

### الحل 2: فحص ProductService Model

تأكد من أن حقل `quantity` موجود في `$fillable`:

```php
// في app/Models/ProductService.php
protected $fillable = [
    'name',
    'sku',
    'sale_price',
    'purchase_price',
    'quantity',        // ← تأكد من وجود هذا السطر
    'tax_id',
    'category_id',
    'unit_id',
    'type',
    'sale_chartaccount_id',
    'expense_chartaccount_id',
    'created_by',
    'expiry_date',
];
```

### الحل 3: إنشاء Controller مبسط للاختبار

إذا استمر الخطأ، أنشئ controller مبسط للاختبار:

```php
<?php
// ملف: app/Http/Controllers/PricingTestController.php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PricingTestController extends Controller
{
    public function index()
    {
        return "Pricing page is working!";
    }
}
```

ثم أضف route للاختبار:
```php
// في routes/web.php
Route::get('pricing-test', [App\Http\Controllers\PricingTestController::class, 'index']);
```

### الحل 4: فحص قاعدة البيانات

تأكد من وجود هذه الجداول:
- `product_services`
- `product_service_categories`
- `product_service_units`
- `chart_of_accounts`

### الحل 5: فحص الـ Logs

اطلع على ملف الـ logs لمعرفة السبب الدقيق:
```bash
# في مجلد المشروع
tail -f storage/logs/laravel.log
```

## 📋 قائمة فحص سريعة

- [ ] **Routes**: تأكد من وجود routes التسعير في web.php
- [ ] **Controller**: تأكد من وجود PricingController.php
- [ ] **Views**: تأكد من وجود مجلد pricing ومحتوياته
- [ ] **Model**: تأكد من إضافة quantity إلى $fillable
- [ ] **Cache**: مسح جميع أنواع الـ cache
- [ ] **Permissions**: فحص صلاحيات الملفات
- [ ] **Database**: تأكد من وجود الجداول المطلوبة

## 🚨 إجراءات الطوارئ

إذا لم تنجح الحلول السابقة:

### 1. إزالة routes التسعير مؤقتاً
```php
// علق على routes التسعير في web.php
/*
Route::get('pricing', [PricingController::class, 'index'])->name('pricing.index')->middleware(['auth', 'XSS']);
Route::get('pricing/simple', [PricingController::class, 'simple'])->name('pricing.simple')->middleware(['auth', 'XSS']);
Route::post('pricing/update-inline', [PricingController::class, 'updateInline'])->name('pricing.update.inline')->middleware(['auth', 'XSS']);
Route::get('pricing/field-options', [PricingController::class, 'getFieldOptions'])->name('pricing.field.options')->middleware(['auth', 'XSS']);
*/
```

### 2. اختبار الموقع
- إذا عمل الموقع، المشكلة في كود التسعير
- إذا لم يعمل، المشكلة في مكان آخر

### 3. إضافة routes واحد تلو الآخر
```php
// أضف route واحد فقط للاختبار
Route::get('pricing', function() {
    return "Pricing route works!";
});
```

## 📞 طلب المساعدة

إذا استمر الخطأ، أرسل:
1. **محتوى ملف الـ logs** (آخر 50 سطر)
2. **لقطة شاشة من الخطأ**
3. **إصدار PHP** المستخدم
4. **إصدار Laravel** المستخدم

## ✅ التحقق من نجاح الحل

بعد تطبيق الحلول:
1. **امسح الـ cache** مرة أخرى
2. **اختبر الرابط**: `your-domain.com/pricing`
3. **اختبر الرابط المبسط**: `your-domain.com/pricing/simple`
4. **تأكد من ظهور القائمة** في إدارة عمليات الفروع

**المشكلة غالباً في routes أو cache! 🔧**
