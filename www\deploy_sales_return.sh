#!/bin/bash

# =============================================================================
# سكريبت نشر ميزة مرتجع المبيعات في أوامر الاستلام
# =============================================================================

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# إعدادات الخادم (يجب تعديلها حسب بيئتك)
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"

# دالة لطباعة الحالة
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# دالة لطباعة النجاح
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# دالة لطباعة الخطأ
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# دالة لطباعة التحذير
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# دالة للتحقق من نجاح العملية
check_success() {
    if [ $? -eq 0 ]; then
        print_success "$1 تم بنجاح"
    else
        print_error "فشل في $1"
        exit 1
    fi
}

# بداية السكريبت
echo -e "${BLUE}================================${NC}"
echo -e "${BLUE}  نشر ميزة مرتجع المبيعات  ${NC}"
echo -e "${BLUE}================================${NC}"
echo ""

# التحقق من وجود الملفات المطلوبة
print_status "التحقق من وجود الملفات المطلوبة..."

required_files=(
    "resources/views/receipt_order/create.blade.php"
    "app/Http/Controllers/ReceiptOrderController.php"
    "app/Models/ReceiptOrder.php"
    "routes/web.php"
    "database/migrations/2025_01_20_000000_add_pos_invoice_id_to_receipt_orders_table.php"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "الملف غير موجود: $file"
        exit 1
    fi
done

print_success "جميع الملفات المطلوبة موجودة"

# التحقق من التغييرات المطلوبة
print_status "التحقق من التغييرات المطلوبة..."

# البحث عن التغييرات الجديدة
if grep -q "مرتجع مبيعات" resources/views/receipt_order/create.blade.php; then
    print_success "تغييرات الواجهة موجودة"
else
    print_error "تغييرات الواجهة غير موجودة"
    exit 1
fi

if grep -q "getCurrentShiftInvoices" app/Http/Controllers/ReceiptOrderController.php; then
    print_success "تغييرات الكونترولر موجودة"
else
    print_error "تغييرات الكونترولر غير موجودة"
    exit 1
fi

if grep -q "pos_invoice_id" app/Models/ReceiptOrder.php; then
    print_success "تغييرات النموذج موجودة"
else
    print_error "تغييرات النموذج غير موجودة"
    exit 1
fi

# المرحلة 1: إنشاء نسخة احتياطية
print_status "🔄 المرحلة 1: إنشاء نسخة احتياطية..."
ssh $SERVER_USER@$SERVER_HOST "cp $PROJECT_PATH/resources/views/receipt_order/create.blade.php $PROJECT_PATH/resources/views/receipt_order/create.blade.php.backup.$(date +%Y%m%d_%H%M%S)"
ssh $SERVER_USER@$SERVER_HOST "cp $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php.backup.$(date +%Y%m%d_%H%M%S)"
ssh $SERVER_USER@$SERVER_HOST "cp $PROJECT_PATH/app/Models/ReceiptOrder.php $PROJECT_PATH/app/Models/ReceiptOrder.php.backup.$(date +%Y%m%d_%H%M%S)"
ssh $SERVER_USER@$SERVER_HOST "cp $PROJECT_PATH/routes/web.php $PROJECT_PATH/routes/web.php.backup.$(date +%Y%m%d_%H%M%S)"
check_success "إنشاء نسخة احتياطية"

# المرحلة 2: رفع الملفات المحدثة
print_status "🚀 المرحلة 2: رفع الملفات المحدثة..."

# رفع ملف الواجهة
print_status "رفع ملف الواجهة..."
scp resources/views/receipt_order/create.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/receipt_order/
check_success "رفع ملف الواجهة"

# رفع الكونترولر
print_status "رفع الكونترولر..."
scp app/Http/Controllers/ReceiptOrderController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "رفع الكونترولر"

# رفع النموذج
print_status "رفع النموذج..."
scp app/Models/ReceiptOrder.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Models/
check_success "رفع النموذج"

# رفع ملف الروتات
print_status "رفع ملف الروتات..."
scp routes/web.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/
check_success "رفع ملف الروتات"

# رفع Migration
print_status "رفع Migration..."
scp database/migrations/2025_01_20_000000_add_pos_invoice_id_to_receipt_orders_table.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/
check_success "رفع Migration"

# المرحلة 3: ضبط الصلاحيات
print_status "🔐 المرحلة 3: ضبط صلاحيات الملفات..."
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/resources/views/receipt_order/create.blade.php"
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/app/Http/Controllers/ReceiptOrderController.php"
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/app/Models/ReceiptOrder.php"
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/routes/web.php"
ssh $SERVER_USER@$SERVER_HOST "chmod 644 $PROJECT_PATH/database/migrations/2025_01_20_000000_add_pos_invoice_id_to_receipt_orders_table.php"
check_success "ضبط صلاحيات الملفات"

# المرحلة 4: تشغيل Migration
print_status "🗄️ المرحلة 4: تشغيل Migration..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate"
check_success "تشغيل Migration"

# المرحلة 5: مسح الكاش
print_status "🧹 المرحلة 5: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan view:clear"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:clear"
check_success "مسح الكاش"

# المرحلة 6: إعادة تحميل الكاش (اختياري)
print_status "⚡ المرحلة 6: إعادة تحميل الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:cache"
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan config:cache"
check_success "إعادة تحميل الكاش"

# انتهاء النشر
echo ""
echo -e "${GREEN}================================${NC}"
echo -e "${GREEN}       تم النشر بنجاح! 🎉        ${NC}"
echo -e "${GREEN}================================${NC}"
echo ""

print_success "تم نشر ميزة مرتجع المبيعات بنجاح"
print_warning "يرجى اختبار الميزة الجديدة للتأكد من عملها"

echo ""
echo -e "${BLUE}📋 المميزات الجديدة:${NC}"
echo "✅ إضافة خيار مرتجع المبيعات في أوامر الاستلام"
echo "✅ تحميل فواتير الشفت الحالي تلقائياً"
echo "✅ تحميل منتجات الفاتورة المختارة"
echo "✅ معالجة إرجاع المنتجات إلى المخزون"
echo "✅ ربط المرتجعات بالفواتير الأصلية"

echo ""
echo -e "${BLUE}🧪 خطوات الاختبار العاجل:${NC}"
echo "1. تسجيل الدخول بحساب Cashier"
echo "2. إنشاء أمر استلام جديد"
echo "3. اختيار 'مرتجع مبيعات' من قائمة أنواع الأوامر"
echo "4. اختيار مستودع له شفت مفتوح"
echo "5. التحقق من تحميل الفواتير في القائمة المنسدلة"
echo "6. اختيار فاتورة والتحقق من تحميل منتجاتها"
echo "7. حفظ الأمر والتحقق من نجاح العملية"

echo ""
echo -e "${YELLOW}⚠️  ملاحظات مهمة:${NC}"
echo "- يجب وجود شفت مفتوح للمستودع لعرض الفواتير"
echo "- فقط فواتير الشفت الحالي ستظهر في القائمة"
echo "- يتم تحديث مخزون المستودع تلقائياً عند الإرجاع"
echo "- جميع عمليات الإرجاع مسجلة ومرتبطة بالفواتير الأصلية"

echo ""
print_status "انتهى النشر بنجاح! ✨"
print_warning "لا تنس تشغيل الاختبار العاجل!"
