{"__meta": {"id": "X1250796148062ea40bb89a5ddcf95049", "datetime": "2025-06-06 19:18:33", "utime": **********.941715, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237512.342938, "end": **********.94175, "duration": 1.5988121032714844, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749237512.342938, "relative_start": 0, "end": **********.769184, "relative_end": **********.769184, "duration": 1.426246166229248, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.769212, "relative_start": 1.426274061203003, "end": **********.941754, "relative_end": 4.0531158447265625e-06, "duration": 0.17254209518432617, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44755144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0077, "accumulated_duration_str": "7.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.859602, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.247}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.898458, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.247, "width_percent": 13.506}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.917031, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.753, "width_percent": 13.247}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1181191373 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237503349%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlxdVRmT1NDcTZ6Ly9BUnhIR1kwbFE9PSIsInZhbHVlIjoiY09xYkpZeUdFelJ2ZzVUUTlnZDY4ZlRxb2JGTHRCeWFOYTdKcG1sMkIwTURTeEN2a09lcTMvTk9pNHBtanRBbGc3Z3QzVzhPelVHWUZkN1FOdDRkd2xETUc1R3c4Y2FnK3MrYkkvWDg3MEhrajFqbHFXd0VjSUxLSjMvQzNlMlF3UFlPV2xxeDNMUFBhTm1IcCs4MHJXNUgvRFRzV29KazFTK3hmMXVEZmZuUUUzYUd0dGROOUV4UlhVRmJtUUJlQXp5MnJNTU85c1hLQ1ZnRXo2Y2N3d2RpNEZBL05WUVlVbk5GUmd4TTdXOXJOUFVGS0JBcVdaMFVsU0dLOUg1Nnl6S3RjaStCVEZUZlBta0VTZHdkMi9Bb1FRUDZjV2VSUGR5ckYzaGh6a0k1Uzh6RW4xcURwa0dSMlZqTUxsSmZqTitXTXVrM1dqZ3RkRDhobEptc2YyKzZxazNya0NqWE5TV3VhUWVaMzFIZ05mbkoxelg3UVAySDRaTkt6a2VhZWthRWtKVnV1ZXJtS3c0TXNUSG1hSkNwSUNMQnZxUGpZUnlwNkVRNzZuQUQ3ZGRpeDNoZ2xPd0VsLytzQVRXQTd1aHhWUkdrdlB0QzJ3bE9lQ0xIREtWVFQ4WEZjdTgyY0ZOcUg4Tm0rRjZHUnpwUTMySFFUUVEwWXJwcnNjMUwiLCJtYWMiOiJjNTY0ZWI1OWNlOTBkYWNlMjM4ZTE2NjQ1MTRkNjI0NjVlZWNiYjk1YTcwOGM5YzAwYjc1MTkyNDg2ZjQ2MjNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InBFR0xIQzAzK250dEhmaDRPUzA4NkE9PSIsInZhbHVlIjoiSnQ1ZDdrMnd5ZzVlcjNyUWxuNDNqNUpEeXg4U0gvOUV4WTBVeVNrdE5zUmxqdDRBakZ6cUxoYWlPVlNRRUsyNTVGKzFLWnpqSy9CT3dCMlBKQU1PazdkeWJTdHdydjkzTTloc0ppU0hLcUdqb2xybkpnRnN1bXNXK2YyMmhKc1pTbWl3RWdTUit2US9JQzRTQ1dvNk91aFdia1dwNXVPRTluVWdtM3ZLeDBkZVBveWJLTW9wckNpSzVrWVhVZi9VVkx0RU9WK1NFS3lKMXVVRXF3VHlrNThvYjNWdCtib0RvcHZFNXY0UiszS3pTSVhYbzdzenR6TkxnTWRqZDByMVdFOGlVcllKUy9MYmFySWJWa3AvaDJrR2hCN3R0R1dNOGlYLzBHWEhLSmZMdlgyMFFhZnhFbnZ2TWFNZXE0cTF3dmNqcFdYVEQrQS9TVmtyY0ZLaC9WS21TVVA4V2RnMkxRTFEyUU9PWU4wY2srRTM3VFRHQVZSNUJObXdmQVd6Z3JpckFCS29oVzJBSHAzRks3RVJLWURGUW1IVFlYdFg4MlpkTnFLWWM3cXNFbEVHaWNLcjRaU01mVmlWeTF3aEhsWmFRUXd3UVJ5ekVOQVhKVTdOK25yb0dvdmxLVzhvaHZSQjcvWGRRd05uTklrZktnZ3JoVFR6VDhZK3ZqWnEiLCJtYWMiOiI2NGI5NjlmNjNmZTc2YWIxNTQ0MTBmNGI5YjkwMTJjMzhiYmM2MzBjOTFjZWVjZTY5NjI1ZjAwMmE4MDJjNjk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181191373\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1603796545 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603796545\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-49748718 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:18:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ing3RVhIaG9PeTYvME9Yc3BNdXlScWc9PSIsInZhbHVlIjoiTkc5TEpqQ2lCYTVyRG9GUkw0TmVTOVgwNzNJRmNqRDdUL3RQMzRQWng5RGlxR3NvanJTWEU4MGpTbkxjTUpVT2t3Qk4vZWNDbjdkc0NMV1praHlxV0loR1hJeTNxSGVlR0t5bmRicUpqT2RSSVNkazh0VTY2Z2JlWk1HdFJnMUJreWVYMTJINGJiRDg2SWlMTE5razRYYVdGQkdwangrdDVGMzF5eGQ3ZGJObXhsMnFUeWpTN3d1Vnk0Qi9iRTNzWDRrc1FwcUcyYW1xQ0ZzM1ljY2ptcWRXK2YxandBOW9hWEVQVVV3cjZZS0ZydHlnVlZ2bXQ5cWduV2JlM01vMGJIZks1UzlTTFBwZ0VPTGYvRERVbFFHOEtjVUJnZHBIbTVabWZNNTZPUUlEQ3BJUHFCUllXZW14K2w0QkdvckI2dyt6UG5oa3JndXlMMkhTQ2ovTE5QOGlld0o5NDBJeHE3NjliekdSZHU5S1dyaXJ1WEd6c0Mra3VzS1NHTUgzUUVKdGNYbUltYWZGOGl3UWRHUEY5K1c0VkV5bzJUc2hXZkpoOHUzQjJ5dUVXdlB5UGllWmF5bGdhR0paaWlaT0p6bWJMSTJ5enI2SkMxTnZQNEY5K21tUk9JaXBqVlhSS2dBc3JFMWthc3djRzF2dTQ2ZnpvODdDM0RCZ2ZGT2siLCJtYWMiOiJmZDY5NmZiMzg2NWRmYjQzMWQwNWE4NTExM2Y2Y2I3YWE3N2I3NjJlYjhhYjlkNDA0NTY2NDg3NmU1MDhmMzUzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRhRHJOUmdGK3BMTjhXY0IyOFRHaWc9PSIsInZhbHVlIjoicTBRWmcvWUoyaHBPK1ZpRUtwN3RVZW9wVE1EQmEza0dOSGRFcVhiQXNIcEtscFN5QnlrdTUzZGNaaS85QWd6ekh6cUYxbGhuQVRQRnZYMnRXRW5JQkhJMUhpSkF0L1NGOFM4YXlEN1dpcWQ4QkhyVzcvaGp0V3p6dDVkbzJnRWZLN0JKVXV5R1M4TEVBUis4UDhDZWJzKzFFZEZTNWlJTnVmOFZmS0FLY1MvZ1FSc056ZVRja2RWYVNmaW5OaTRrL1RqS0ZmYjZENHYwd0ErTGtIYWxIUlVJbmYxQXZSOXBvYnptTnovR1diaXExWURuL2ZQZ0o4RXcyUVlPN2w2V1FVd1ovZmtFeFYvY2VwN0RZaUtReXhsTGhOd01HYUwwWlArT0VBUTRxVEZEUHdvTGpsU0tFMGlVSkVXODRRdi9jOWQveUJLazJvL0dCL25pRXRlQTJFeWxxS2Q4ZUJyWFJMd0xqYU9RS1UraktQQXJSWnJ0NHQ2dFM2TERpTFVScllEV0N5aXl1TThpeVBVQXg0RElpZkE4eTNxMzU2eEYyVWRQWTY0RGRZZEZaaXd0S1J6dStQRkJxOVFDd2xGR2VrRHlwR1ZyY1doNXpkMjV5dlF2bHpwa056akU2S2hLcGwyZTB2NHhHaHhNc1MyTVB4Rkg0c21uNXhaQVB0cUIiLCJtYWMiOiJhZTc5ODQ0YTQ4ZTY5MjlmZDliZmE5OWQ5N2E0Y2QzNGY0YTc1ZjEyZTcxM2JiMzBjNWNkZmEwYzVlMTg1OGM0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ing3RVhIaG9PeTYvME9Yc3BNdXlScWc9PSIsInZhbHVlIjoiTkc5TEpqQ2lCYTVyRG9GUkw0TmVTOVgwNzNJRmNqRDdUL3RQMzRQWng5RGlxR3NvanJTWEU4MGpTbkxjTUpVT2t3Qk4vZWNDbjdkc0NMV1praHlxV0loR1hJeTNxSGVlR0t5bmRicUpqT2RSSVNkazh0VTY2Z2JlWk1HdFJnMUJreWVYMTJINGJiRDg2SWlMTE5razRYYVdGQkdwangrdDVGMzF5eGQ3ZGJObXhsMnFUeWpTN3d1Vnk0Qi9iRTNzWDRrc1FwcUcyYW1xQ0ZzM1ljY2ptcWRXK2YxandBOW9hWEVQVVV3cjZZS0ZydHlnVlZ2bXQ5cWduV2JlM01vMGJIZks1UzlTTFBwZ0VPTGYvRERVbFFHOEtjVUJnZHBIbTVabWZNNTZPUUlEQ3BJUHFCUllXZW14K2w0QkdvckI2dyt6UG5oa3JndXlMMkhTQ2ovTE5QOGlld0o5NDBJeHE3NjliekdSZHU5S1dyaXJ1WEd6c0Mra3VzS1NHTUgzUUVKdGNYbUltYWZGOGl3UWRHUEY5K1c0VkV5bzJUc2hXZkpoOHUzQjJ5dUVXdlB5UGllWmF5bGdhR0paaWlaT0p6bWJMSTJ5enI2SkMxTnZQNEY5K21tUk9JaXBqVlhSS2dBc3JFMWthc3djRzF2dTQ2ZnpvODdDM0RCZ2ZGT2siLCJtYWMiOiJmZDY5NmZiMzg2NWRmYjQzMWQwNWE4NTExM2Y2Y2I3YWE3N2I3NjJlYjhhYjlkNDA0NTY2NDg3NmU1MDhmMzUzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRhRHJOUmdGK3BMTjhXY0IyOFRHaWc9PSIsInZhbHVlIjoicTBRWmcvWUoyaHBPK1ZpRUtwN3RVZW9wVE1EQmEza0dOSGRFcVhiQXNIcEtscFN5QnlrdTUzZGNaaS85QWd6ekh6cUYxbGhuQVRQRnZYMnRXRW5JQkhJMUhpSkF0L1NGOFM4YXlEN1dpcWQ4QkhyVzcvaGp0V3p6dDVkbzJnRWZLN0JKVXV5R1M4TEVBUis4UDhDZWJzKzFFZEZTNWlJTnVmOFZmS0FLY1MvZ1FSc056ZVRja2RWYVNmaW5OaTRrL1RqS0ZmYjZENHYwd0ErTGtIYWxIUlVJbmYxQXZSOXBvYnptTnovR1diaXExWURuL2ZQZ0o4RXcyUVlPN2w2V1FVd1ovZmtFeFYvY2VwN0RZaUtReXhsTGhOd01HYUwwWlArT0VBUTRxVEZEUHdvTGpsU0tFMGlVSkVXODRRdi9jOWQveUJLazJvL0dCL25pRXRlQTJFeWxxS2Q4ZUJyWFJMd0xqYU9RS1UraktQQXJSWnJ0NHQ2dFM2TERpTFVScllEV0N5aXl1TThpeVBVQXg0RElpZkE4eTNxMzU2eEYyVWRQWTY0RGRZZEZaaXd0S1J6dStQRkJxOVFDd2xGR2VrRHlwR1ZyY1doNXpkMjV5dlF2bHpwa056akU2S2hLcGwyZTB2NHhHaHhNc1MyTVB4Rkg0c21uNXhaQVB0cUIiLCJtYWMiOiJhZTc5ODQ0YTQ4ZTY5MjlmZDliZmE5OWQ5N2E0Y2QzNGY0YTc1ZjEyZTcxM2JiMzBjNWNkZmEwYzVlMTg1OGM0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49748718\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15********\", {\"maxDepth\":0})</script>\n"}}