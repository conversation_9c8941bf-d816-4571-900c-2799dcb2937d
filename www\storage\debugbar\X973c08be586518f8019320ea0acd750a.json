{"__meta": {"id": "X973c08be586518f8019320ea0acd750a", "datetime": "2025-06-06 19:14:25", "utime": **********.089492, "method": "POST", "uri": "/business-setting", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237263.43601, "end": **********.089525, "duration": 1.653515100479126, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1749237263.43601, "relative_start": 0, "end": **********.691521, "relative_end": **********.691521, "duration": 1.2555110454559326, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.691542, "relative_start": 1.2555320262908936, "end": **********.089528, "relative_end": 3.0994415283203125e-06, "duration": 0.39798617362976074, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52870576, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST business-setting", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\SystemController@saveBusinessSettings", "namespace": null, "prefix": "", "where": [], "as": "business.setting", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=366\" onclick=\"\">app/Http/Controllers/SystemController.php:366-498</a>"}, "queries": {"nb_statements": 18, "nb_failed_statements": 0, "accumulated_duration": 0.05000999999999999, "accumulated_duration_str": "50.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.783738, "duration": 0.016210000000000002, "duration_str": "16.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 32.414}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8264382, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 32.414, "width_percent": 2}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 14 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["14", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.872367, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 34.413, "width_percent": 3.859}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.879473, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 38.272, "width_percent": 4.239}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 382}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.893043, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 42.511, "width_percent": 2.879}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('14-logo-dark.png', 'company_logo_dark', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["14-logo-dark.png", "company_logo_dark", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 389}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.948308, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:389", "source": "app/Http/Controllers/SystemController.php:389", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=389", "ajax": false, "filename": "SystemController.php", "line": "389"}, "connection": "ty", "start_percent": 45.391, "width_percent": 5.199}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 408}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.961147, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 50.59, "width_percent": 3.559}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('14-logo-light.png', 'company_logo_light', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["14-logo-light.png", "company_logo_light", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 415}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.974515, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:415", "source": "app/Http/Controllers/SystemController.php:415", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=415", "ajax": false, "filename": "SystemController.php", "line": "415"}, "connection": "ty", "start_percent": 54.149, "width_percent": 4.279}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 436}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.983325, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 58.428, "width_percent": 2.719}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('14-favicon.png', 'company_favicon', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["14-favicon.png", "company_favicon", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 442}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.993157, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:442", "source": "app/Http/Controllers/SystemController.php:442", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=442", "ajax": false, "filename": "SystemController.php", "line": "442"}, "connection": "ty", "start_percent": 61.148, "width_percent": 3.839}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('شركة  تسوق بسرعة التجارية', 'title_text', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["شركة  تسوق بسرعة التجارية", "title_text", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.002002, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 64.987, "width_percent": 3.999}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('شركة تسوق بسرعة التجارية . ذات مسؤلية محدودة', 'footer_text', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["شركة تسوق بسرعة التجارية . ذات مسؤلية محدودة", "footer_text", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0106301, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 68.986, "width_percent": 4.879}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('en', 'default_language', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["en", "default_language", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0192978, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 73.865, "width_percent": 5.039}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('#d42121', 'color', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["#d42121", "color", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.029043, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 78.904, "width_percent": 4.579}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('true', 'color_flag', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["true", "color_flag", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.037724, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 83.483, "width_percent": 4.559}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'SITE_RTL', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "SITE_RTL", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.047457, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 88.042, "width_percent": 3.739}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'cust_theme_bg', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "cust_theme_bg", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.055274, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 91.782, "width_percent": 4.239}, {"sql": "insert into settings (`value`, `name`,`created_by`) values ('off', 'cust_darklayout', 14) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)", "type": "query", "params": [], "bindings": ["off", "cust_darklayout", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\SystemController.php", "line": 482}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0639758, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "SystemController.php:482", "source": "app/Http/Controllers/SystemController.php:482", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=482", "ajax": false, "filename": "SystemController.php", "line": "482"}, "connection": "ty", "start_percent": 96.021, "width_percent": 3.979}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage business settings,\n  result => true,\n  user => 14,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-67411089 data-indent-pad=\"  \"><span class=sf-dump-note>manage business settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage business settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67411089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.891911, "xdebug_link": null}]}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/settings\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "success": "Brand Setting successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/business-setting", "status_code": "<pre class=sf-dump id=sf-dump-619702860 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-619702860\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-767976104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-767976104\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-507559447 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>title_text</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1588;&#1585;&#1603;&#1577;  &#1578;&#1587;&#1608;&#1602; &#1576;&#1587;&#1585;&#1593;&#1577; &#1575;&#1604;&#1578;&#1580;&#1575;&#1585;&#1610;&#1577;</span>\"\n  \"<span class=sf-dump-key>footer_text</span>\" => \"<span class=sf-dump-str title=\"44 characters\">&#1588;&#1585;&#1603;&#1577; &#1578;&#1587;&#1608;&#1602; &#1576;&#1587;&#1585;&#1593;&#1577; &#1575;&#1604;&#1578;&#1580;&#1575;&#1585;&#1610;&#1577; . &#1584;&#1575;&#1578; &#1605;&#1587;&#1572;&#1604;&#1610;&#1577; &#1605;&#1581;&#1583;&#1608;&#1583;&#1577;</span>\"\n  \"<span class=sf-dump-key>default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">theme-3</span>\"\n  \"<span class=sf-dump-key>custom_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#d42121</span>\"\n  \"<span class=sf-dump-key>custom-color</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>color_flag</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>company_logo_dark</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#169</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"10 characters\">php1F0.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"10 characters\">php1F0.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"44 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\php1F0.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>company_logo_light</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#3531</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"17 characters\">&#1588;&#1593;&#1575;&#1585;  &#1575;&#1604;&#1588;&#1585;&#1603;&#1577; .png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"10 characters\">php200.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"10 characters\">php200.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"44 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\php200.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>company_favicon</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#3530</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"10 characters\">&#1588;&#1593;&#1575;&#1585; 2.png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"10 characters\">&#1588;&#1593;&#1575;&#1585; 2.png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"10 characters\">php201.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"10 characters\">php201.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"44 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\php201.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507559447\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-74496169 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">42767</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary0iROxRkDGo2L3Bk3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhyNUV5ZDYyVS9zZUEzQjdnRVd4elE9PSIsInZhbHVlIjoiWHR5QVZqWVcveFF6TTFaeDJHY1dsMDA1WSsrV0RuR3R6UEZiRzQrSEFDQnJQelRmRDZCL0xNbUdHTUFUeFp4VVBST2dyQ3lOczlyYzVsNm1uVHVqMTRMUjIyN2pOTU1HVFhUSE5obnpSNjlnc2s0d3JpOWl0d3ZESHRQeUR1bnhPdGp5SUlURWlwYkxCbXdPRmhMNHhVQzE5Wk5XRUpmTUZ6V1orWEM2cHU2cThuT1ZKYXloOWMvMUF6RzQvdDQ1dHB3RnJ4MUF4UTJodkY5MlgvY0lmM1g2OEEzTjk2YW9QaWdxUjVXZUFtUXQ3cGNlMkFVSTFLaUZBeXNNcDE3U0M4M2tQL3d6T3QvbWkwS3hGRFMra2NOaEIvc0ZSQzFDOGxWZGY3bHM0V2NMcVZOQVphTVFRTTMzWDJGSmZkd0NXeXFQdmNIYm9sbm9HQlZoMFNpTDZSM3I5STFaRDdiUHFPSWQwRXlNYWcraFg3UGFadnRxKzBCTlBhSkhNUUsySUI1SUVzSi8rQ3gyM295MDZJdnF5SVlkakp5eWNHTXhZbnJremI0YzVYTHdiVFVObEJ0cG84cU9YVEdUN2F6cDlxcUpKM2hQNjdxQ1ZFeDJjM0dRM0VhYTEza1NDRUp5TmdSVUk5TjQyKzF3RWxTeGpRdW5PS1BFTDA3a2JjR0IiLCJtYWMiOiIyYzczYjA1M2I1NzRhOWI4MWM1ODBjNzBkZGFmNTBlNDQ3ODFiYmQ0NGVlM2VlMDA2NjA5YWMyMzAyZDAwNTEwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJNU01BRkZ4cjB1bWJqOHV4ZlZybEE9PSIsInZhbHVlIjoid2QzWUJUenVsOStQSDJVaXRIaXUxUlFsK2l3TmJJQzVBVlVIN0tNbkNIaGJlU3p4WjRhUitKZDdqdVBXOTJYaW1mMjJmQUpYYXo3YUJNRFE3Z0dpS3lrMmNHaG9LcXZwZno4aHNDVS80UVNxaUVHNGE2eXh0VThxVDYzemNOaUR3akJhckJQMm1FTnB6Y01QRWdtM3AxZ21icDJsZlJhQ3NHdWRna2w4UEhaUUEvUDRnYk9uSDNpR2NQNlU3QnAwSm9mWi9saDRac056WGNUaWhhNklja050VnFPZGhrenZsK0Jpa1kwYTdGa1pTRGl2WHpvSitTS1B1MGhQSTByNDVYb3dqcjVFZCtqdlhTUCtYWS8zWWJSdDVTS1cvditoSjNuOVlhMHVlZGlKYTRkem1DZFE4WGJ1SkVENFpKSHRyUE9hZkhBb0dmdXc2VTduc1o1L05CbFNCdUFXa0EvMzZnNW9BVHFxT0J2UE9MMmpJdGY5bHpXNkpzSXl5dTdSVFd1TFFGVnA3OUhXdXVXMnBLazJZUEZYeUZidTdQWXA1VlZDQy8yblo2cWxIc2w2NWd0R1dQU1BmS2VHZUhMWmR4akZ2ZGdMcmNTeStsdDI2a0pOaDNkVEdCTTVscDcyQWx3MHo3L0tVbEJCUFZNdUNDMTF4b0FqbFp0S3BZaXUiLCJtYWMiOiI4MmI3ZTg5ZTYxZDEyYjhlNzc0N2Q1YWRjYTA4NGVmOTc5YzcwODc1ZTdkMmZiNGY5MzE2ODc0YjUyZjBjYzFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74496169\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-548047282 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548047282\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1812394901 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:14:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilo1M0NoTE41NG5qZlY3WENDZ0dpaVE9PSIsInZhbHVlIjoidEtSNXp0ZldVVFVKNWRTUG5TN210V2hBR2k2bFZEZGhWMWxSU3p1VFhjS0NyYXRua0hUNE54NG5hSjFwNHoweEIrc1hqU2J5RktjVGQvYm5ReUNmWGJmMjExMzVnV3pXRkJ1QUdVbVBWemloUFc5U1g4MGdpVTV4L0Q1Ti9aQnJVL090M1kvZzF3cFVzN2ozL3F6S2ZyejI2VFY4dHJpbGNTTlBJcHBzWmZ2eDYxcDMxUnc2NmpUenlxc1FvRlY3VEZnbjlqRzhOUEFFMTdIWGxXd1docDEyMHE4WEY5bzQ2UnNwVWpNM1NPSTYySFdneFBqRDV2Tm1walJ2RG9zdUNMYnlhc25Kb3N4SjZuWlZDcmVLQXpxOTErS2FJUGtqMnRsamR5UXVlSzNacWZjUlNjVW1kamdpUDBzZWZ1dzZUdkFuOTZtZVNCZ1BsdFBmNlF0Uzg5Y3ovSmNWWlpDendTZ0szeDdrLzA1UTJDeTZhRDdaeXZmVUl3L3lacFMwRXV1Vk1QQitXUnhBWWluNXdwZUpJZUxrVlgvM3hacUU3VDQrZUFUamVIZXpiUkR1b2toOThwSWE2dkZBQWsxUEQ4aEtGVDdhRVJKRTZaeE1yTUxHMlh1cVJMVXQvWi9OcHQ5YlBuOVdHRENQQ0lGZ2J2SXNMQmVadHBDSjhJK3UiLCJtYWMiOiIwOTBhZTk0ZmY1NGE0ZjEzMGY1MGEyOTIwOWVjM2MxMmQ3ZDQ1NjIwOWVlMzBhNGQ0MGI5NWEwNGZhOGQ3YTczIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktuV3hRd1QzdFhQejR5L0pyTGljM0E9PSIsInZhbHVlIjoid0tzTmRBVjZSNlN0ZDl5T3ROMGJuN0NHdU93WnFGWm9oWnZYcHpqNmVUSDJQcTJ0YXFzS0drTEExL2NReFdhSFNmYS9VTTF4QkVUeXl6VVNnZWZXa3FaYlFtZzcra3VzcFFrOTlhNy9xOWQ5M0M0YngxOGgvd3FkdHplVndYQm9oSjlMZmwxS253R2ZBVEdIMmJuV1I4L0I0cFRBcDhYM0F2ekhLNUpIdlI1VFROMnRBRFpGRms0OHZFYWIwU3lUdnFTV3RnQmd6cmZRR2kwZ1BRV0lRRjR3STU3WHBMQTR4Tm9OZTdUMlh1S0RmTXcycktuRXdSZERvYmR3UVhYM1J6TE45dW5mcmdUYURmWFNWSURxRGRwc1F4S2JwYkZMUHNXazFPVm9GbEtPMlltVmJWeU5tcmpOOWxMcU5yV0dvVys3dTAzK1hPMjFVQXFXLzBQZFR6QXNLN1dCNi80K3hIUUE2Zy9BMUVHMEZXV2JSWlRqUEtLMW84UEhOb1RZeG1nOWRyb0pvNk1kRVJZeHc2dFVaY3ZrUmF0SlZnUDhxQWdRRGV4dzBJcFZZVEVUY0pvY1FEdkxOUUFvVm8rSGk2VnQ2M2VRZ0FYWjV3bEgxb0crN0FZWm9JZmJROHNsOXAxSDdtNEcxQW95UklBZE8yQjAzZlY0OG5ERTFJZ0QiLCJtYWMiOiIzMzE0NzA4NTllNGRhYWE1MjQ2ZDYzYTczNzI0NmRiYWM2YzcxNWZkNzUxOTU2NjhjNTVjODNkNDg2Y2U2YWJmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilo1M0NoTE41NG5qZlY3WENDZ0dpaVE9PSIsInZhbHVlIjoidEtSNXp0ZldVVFVKNWRTUG5TN210V2hBR2k2bFZEZGhWMWxSU3p1VFhjS0NyYXRua0hUNE54NG5hSjFwNHoweEIrc1hqU2J5RktjVGQvYm5ReUNmWGJmMjExMzVnV3pXRkJ1QUdVbVBWemloUFc5U1g4MGdpVTV4L0Q1Ti9aQnJVL090M1kvZzF3cFVzN2ozL3F6S2ZyejI2VFY4dHJpbGNTTlBJcHBzWmZ2eDYxcDMxUnc2NmpUenlxc1FvRlY3VEZnbjlqRzhOUEFFMTdIWGxXd1docDEyMHE4WEY5bzQ2UnNwVWpNM1NPSTYySFdneFBqRDV2Tm1walJ2RG9zdUNMYnlhc25Kb3N4SjZuWlZDcmVLQXpxOTErS2FJUGtqMnRsamR5UXVlSzNacWZjUlNjVW1kamdpUDBzZWZ1dzZUdkFuOTZtZVNCZ1BsdFBmNlF0Uzg5Y3ovSmNWWlpDendTZ0szeDdrLzA1UTJDeTZhRDdaeXZmVUl3L3lacFMwRXV1Vk1QQitXUnhBWWluNXdwZUpJZUxrVlgvM3hacUU3VDQrZUFUamVIZXpiUkR1b2toOThwSWE2dkZBQWsxUEQ4aEtGVDdhRVJKRTZaeE1yTUxHMlh1cVJMVXQvWi9OcHQ5YlBuOVdHRENQQ0lGZ2J2SXNMQmVadHBDSjhJK3UiLCJtYWMiOiIwOTBhZTk0ZmY1NGE0ZjEzMGY1MGEyOTIwOWVjM2MxMmQ3ZDQ1NjIwOWVlMzBhNGQ0MGI5NWEwNGZhOGQ3YTczIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktuV3hRd1QzdFhQejR5L0pyTGljM0E9PSIsInZhbHVlIjoid0tzTmRBVjZSNlN0ZDl5T3ROMGJuN0NHdU93WnFGWm9oWnZYcHpqNmVUSDJQcTJ0YXFzS0drTEExL2NReFdhSFNmYS9VTTF4QkVUeXl6VVNnZWZXa3FaYlFtZzcra3VzcFFrOTlhNy9xOWQ5M0M0YngxOGgvd3FkdHplVndYQm9oSjlMZmwxS253R2ZBVEdIMmJuV1I4L0I0cFRBcDhYM0F2ekhLNUpIdlI1VFROMnRBRFpGRms0OHZFYWIwU3lUdnFTV3RnQmd6cmZRR2kwZ1BRV0lRRjR3STU3WHBMQTR4Tm9OZTdUMlh1S0RmTXcycktuRXdSZERvYmR3UVhYM1J6TE45dW5mcmdUYURmWFNWSURxRGRwc1F4S2JwYkZMUHNXazFPVm9GbEtPMlltVmJWeU5tcmpOOWxMcU5yV0dvVys3dTAzK1hPMjFVQXFXLzBQZFR6QXNLN1dCNi80K3hIUUE2Zy9BMUVHMEZXV2JSWlRqUEtLMW84UEhOb1RZeG1nOWRyb0pvNk1kRVJZeHc2dFVaY3ZrUmF0SlZnUDhxQWdRRGV4dzBJcFZZVEVUY0pvY1FEdkxOUUFvVm8rSGk2VnQ2M2VRZ0FYWjV3bEgxb0crN0FZWm9JZmJROHNsOXAxSDdtNEcxQW95UklBZE8yQjAzZlY0OG5ERTFJZ0QiLCJtYWMiOiIzMzE0NzA4NTllNGRhYWE1MjQ2ZDYzYTczNzI0NmRiYWM2YzcxNWZkNzUxOTU2NjhjNTVjODNkNDg2Y2U2YWJmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812394901\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-867674541 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Brand Setting successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867674541\", {\"maxDepth\":0})</script>\n"}}