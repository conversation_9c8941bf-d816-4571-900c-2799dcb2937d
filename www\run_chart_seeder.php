<?php
// تشغيل بذور شجرة الحسابات
echo "<h1>تشغيل بذور شجرة الحسابات</h1>";

// تغيير المجلد إلى مجلد المشروع
chdir(__DIR__);

echo "<h2>تشغيل الأوامر...</h2>";

// تشغيل أمر البذور
$command = 'php artisan db:seed --class=ChartOfAccountSeeder';
echo "<p>تشغيل الأمر: <code>$command</code></p>";

// تنفيذ الأمر
$output = [];
$returnCode = 0;
exec($command . ' 2>&1', $output, $returnCode);

echo "<h3>نتيجة التنفيذ:</h3>";
echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";

if ($returnCode === 0) {
    echo "<span style='color: green;'>✓ تم تشغيل البذور بنجاح!</span>\n\n";
} else {
    echo "<span style='color: red;'>✗ حدث خطأ أثناء تشغيل البذور</span>\n\n";
}

foreach ($output as $line) {
    echo htmlspecialchars($line) . "\n";
}
echo "</pre>";

// فحص النتائج
echo "<h2>فحص النتائج</h2>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // فحص عدد السجلات
    $tables = [
        'chart_of_account_types' => 'أنواع الحسابات',
        'chart_of_account_sub_types' => 'الأنواع الفرعية',
        'chart_of_accounts' => 'الحسابات'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الجدول</th><th>عدد السجلات</th><th>الحالة</th></tr>";
    
    foreach ($tables as $table => $arabicName) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            $status = $count > 0 ? "<span style='color: green;'>✓ يحتوي على بيانات</span>" : "<span style='color: red;'>✗ فارغ</span>";
            
            echo "<tr>";
            echo "<td>$arabicName ($table)</td>";
            echo "<td>$count</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        } catch (Exception $e) {
            echo "<tr>";
            echo "<td>$arabicName ($table)</td>";
            echo "<td>خطأ</td>";
            echo "<td><span style='color: red;'>✗ " . $e->getMessage() . "</span></td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<ol>";
echo "<li>تأكد من تسجيل الدخول إلى النظام</li>";
echo "<li>اذهب إلى صفحة شجرة الحسابات</li>";
echo "<li>يجب أن تظهر البيانات الآن</li>";
echo "</ol>";
echo "</div>";

echo "<br>";
echo "<a href='http://localhost/chart-of-account' target='_blank' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>اختبار شجرة الحسابات</a>";
echo "<a href='debug_chart_simple.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فحص البيانات</a>";

echo "<br><br>";
echo "<button onclick='location.reload()' style='padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>إعادة تشغيل البذور</button>";
?>
