{"__meta": {"id": "X9ebd8ced52af905de0105aa420894947", "datetime": "2025-06-06 19:13:22", "utime": **********.547853, "method": "GET", "uri": "/users/14/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237201.293138, "end": **********.547886, "duration": 1.2547478675842285, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1749237201.293138, "relative_start": 0, "end": **********.406296, "relative_end": **********.406296, "duration": 1.1131579875946045, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.406313, "relative_start": 1.1131749153137207, "end": **********.547889, "relative_end": 3.0994415283203125e-06, "duration": 0.14157605171203613, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43509944, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01648, "accumulated_duration_str": "16.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.485477, "duration": 0.01531, "duration_str": "15.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.9}, {"sql": "select * from `users` where `users`.`id` = '14' limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.510435, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 92.9, "width_percent": 7.1}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/14/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/14/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-99629064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-99629064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1367790327 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1367790327\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1405229740 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZwTUJ4SmNLM3NQVldhd0I4dVFRelE9PSIsInZhbHVlIjoibWlqSlBuZFEzbVlBd3pOdll1MHZaMmVTaXhhdGdrc0NuUE9CUGRtTzRMcVBwdmx0Q2tqSnI0RU4rMHBFSG8yL1U5TWUrT3gydnY4WGRUVDczUU13ZUFhNzVKdGRmNGJuNGhXa2FEWXNwVEUvYUlZSkhPeGRGTkNSQ2p1M21GT3JCYXBkckpLbjVLMCtBZ3NmTnk4S0lDbjgwOHpOcDNCWDVQU3hLMzZwMEhSUXhHdUtBMWwwR3JCY3NJYmZFbDBjditIL0hMQkRIYlorUXFqM09SSGE1TURQMmhFOXpZd0NBdFMrZmZKeHVlMnU3YVlXUXlncFMxeitUd2ltT2EzTXZrdjZYYVNFSUFTYVpCaENQU3BieGFwUk5BOVA2TlcvS2lESDRqcFNJTkhZZDhTdjBPNlZ0VnhhcE9XcllDVldiZXlHRjlDcXZsSFJDa3BpWU11cjBFdWpZWnJDQWd1dHNIUE1XWGdoV3pWaUxZU2NYSVRHY1VXMEJRTDF5TlRtaE1oSkFlMkVxK0hzMXcrcGZiOW84WldTS3JFY0ZzclRxcDNUaGhoK0ptR1Z1UlZSUUMvbzY2dVVwVXoraHhPV2ZTYlY0MEgvdWlIL1VIL3c5MWR6QkxLaVB5MldPd0ZtZ2JXbS9tTS9RbkZHYWcvS3J5NVErNXhiZ0J6ejJqMFYiLCJtYWMiOiI1ODg3MGVkYmU3ZmJlODFlMDdhMzAxOGY3ZmM3NTE1ZTNhY2ExM2FjODY0Yjc2NGI1NmFhYmEyZmVlZjhiMzNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InoyK1E5R3F0QWZqYXRWei9pY0dpWXc9PSIsInZhbHVlIjoiTm14RUdyOEhaZGxKSjArNWo5UFVpRkxWNXdqWU50ekpwcDZxdHFMVVhML1EwdTFXUHd4RnkzYjVjK1JUT1VyVkdoRmNPWUpYNE5vN0RJYmRlRjc5TStrc09vZ2JmVmdUZkhyK1diN1Mwd2Q3S3RkSXUxTk9LV0FCamRpVEhjbkVsVzkySU55ZEFibUxpVWlLc3M5c1ZDR2k2OTVxc2NxcWNISHdvQytNTnVtNlo1NDVTNVp6Tm9ZOXVmSDB6VStVMzlCT3JiQllrTnlLdW54eDc3bUFmMHpBTWU5dWJTVE5xMVJaWkRQaTFBTG1MWG9qekkvMW5SaUdDM2gyZnFZZHhMV0hTb1JnTGhoTkVVWTRtR1NSbkxQZytRSG1KY2RUYVFLUWNUUGJXOFdYYjF5MFhpUnV3RDFpMG9XSWJ2czNDYmNtd2dueTNGaEs3K1owTHVtV2dLMjFVbW5kVVJkZFdEbVAvTkwyamRxZjYvblg0Nm5yWmhKRFVybkQrM0dRRlF3dFFZcjBkK0tPVmtjeEwzZ0loTk50ZzBBdktLWFdYRHlMOFhFSWNOZzBEd1pDQVM0Wkp0UnlmaUJ0MFBXVnluOW16Rm9IZHRsUVVibDBTNVZsWWMraWsxS3JBeExLNnF0VnFTWFAwZkFwNi9tckJxMlpPU21Qa1J6SWc3b2YiLCJtYWMiOiIyOGM0YjE4OTFmZjZkNjdlOGFjMjk1ZDFhMjBhZDY4ZjhmNjIyNWQwNzU3MWY5OWNkNGRhZDAzOGE4ODZkN2IyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405229740\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1703769812 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PVt0h9icZb5VqFqziyPToWE7TqW2lYjvh7uvnOnw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703769812\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1344640534 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhLeE1RZW04cnVsREloY3RDaGx0anc9PSIsInZhbHVlIjoiWHM1TzJmaHJNT0FPMWxBVWVRNjNPb1lrWktqVUwza0xSeTRiZzhUQ0NCTERSbW4yQUhQMGRIR2RZNVVoWVorRW52S1lpcXBIdXBwTlJnSndZUnlRZE1FVlJpMFRjMmgzN2tzVFAyQ04yOXVLK3pRNm03b3ZSaWxQRDh3ZCtZTXNKbmpmL3Ewb3B0NzZRaUZXdmF0SVlKYkVnUURLdkJHT2kvME9DMW9UbWRqaTQ0dUxLWkUrS2NQRm5TMEV1TVRZVU01UVZPTGtEaktwSklOYjR2N25keVNpeDlibUdhUHcrcVREbG5kaVdDWlhlMjRaR3pubytnWEUxTFFhQ2JRNC8waElpRlRFRys2QVljSWNmTGlXMEQraDdSWFkyZ1BFaDFlaHFVOEgxc2c0Vm44KzNET0RGSTNpbVNWaXRzMzhYSGx1S1E0bUhuYm1Ub2I0aTJHWVNWRkthMERGTitJaGE4UG1mNXNoSFJtWEdOUVcxdStjOUFubmIwTW83WlNaQXF3Vkh6NUhLUEpSYkFqa3lvWGp2MERHNUIwbUFhUTJFNlBlNVN0Q1dFSVlrYk4zVlF6QUxFSGx3RUk4am9HZmk4aVdFdWRjSlQxUzJDUUdZY1JkQTM2OSsycis1YTNHaUxHbEk1TnZJRzJRWEd5MG1RRCtJOXgvTGVCSUplQVQiLCJtYWMiOiIwMmFmMTFiZmE3ZTMwN2NkNzFiZmMxMTM3MGUxZDU2NDc0NTdmZDBiYjUxZDI4ODZhMmYzNGEwZWJhMWI5NDQzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtxZVNERUozaFlTekJINSt6azNoL2c9PSIsInZhbHVlIjoiK05VM0NRTWdxZm90b0t2b29QamJ5LzR0Y0ZFdXkwcnNEVUVSeXNPd0I0dU5sMWJySUIwbnhET2F1Zm1STFB3dmxOcEp4RHlNTmJCd3UxN2swbjAvY0RHbllqVnByWVJ6eW5IZnhhVGVQaU1FMUc3cUxBT2VHaDM2TFlQbHZTa2ROdVpYMC9WTUtKRStIMjcvRWw4Um4xK1VsUzlnR0hEOE54UGFKRUQzVERoNmZDeW9SQ0F0WHVBc3A2ODMxalVoUzNQVlNnSXg1aWNyb0RyY2hGVkNnNmlOVUMrM3JwR1BsOTMxK3Zjd0V6UW1ueTZjSEdDNzZSWFZEMHNPTU41bFJKK3RYQnNxN1dPYXNHK2JBMm42UUxmNmo4TkRnNzJycFJsWUdyVVNPdXQ0eFlnUnFuejB6TnBsZEg3VjkveHdEMmNEMU5pbndTTElZUXJPNWU1WWpHWFVRdy9pcGp3M2pyQlB2VWJ0ckY2MFhXcTdNdC90QVhEWmREK0M5TFpxTll0TEJad3ZQSVZpMlRjdHlJUjNudlhoUmV3RTZQdnZUWSttVFpTMFZJTzBML1p4MFFtcng3SzExN0RONXB3QXBmdnVvRTdHNDVPbHd0c3Y2eHQzYmRBMXFiSm1qSHVaSmRmRlVHZzR4QWhScnQzYXJ3SU1xa0JTNXZNdmtmaTMiLCJtYWMiOiIxNjc3YTY4NGQwZGM0NjZhMTc5ZjYzZmNkZGU2MDJhYjRjYTEzMDlkNzYxYTIwYzhiZTljYjZlZmNmZDcwZmY4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhLeE1RZW04cnVsREloY3RDaGx0anc9PSIsInZhbHVlIjoiWHM1TzJmaHJNT0FPMWxBVWVRNjNPb1lrWktqVUwza0xSeTRiZzhUQ0NCTERSbW4yQUhQMGRIR2RZNVVoWVorRW52S1lpcXBIdXBwTlJnSndZUnlRZE1FVlJpMFRjMmgzN2tzVFAyQ04yOXVLK3pRNm03b3ZSaWxQRDh3ZCtZTXNKbmpmL3Ewb3B0NzZRaUZXdmF0SVlKYkVnUURLdkJHT2kvME9DMW9UbWRqaTQ0dUxLWkUrS2NQRm5TMEV1TVRZVU01UVZPTGtEaktwSklOYjR2N25keVNpeDlibUdhUHcrcVREbG5kaVdDWlhlMjRaR3pubytnWEUxTFFhQ2JRNC8waElpRlRFRys2QVljSWNmTGlXMEQraDdSWFkyZ1BFaDFlaHFVOEgxc2c0Vm44KzNET0RGSTNpbVNWaXRzMzhYSGx1S1E0bUhuYm1Ub2I0aTJHWVNWRkthMERGTitJaGE4UG1mNXNoSFJtWEdOUVcxdStjOUFubmIwTW83WlNaQXF3Vkh6NUhLUEpSYkFqa3lvWGp2MERHNUIwbUFhUTJFNlBlNVN0Q1dFSVlrYk4zVlF6QUxFSGx3RUk4am9HZmk4aVdFdWRjSlQxUzJDUUdZY1JkQTM2OSsycis1YTNHaUxHbEk1TnZJRzJRWEd5MG1RRCtJOXgvTGVCSUplQVQiLCJtYWMiOiIwMmFmMTFiZmE3ZTMwN2NkNzFiZmMxMTM3MGUxZDU2NDc0NTdmZDBiYjUxZDI4ODZhMmYzNGEwZWJhMWI5NDQzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtxZVNERUozaFlTekJINSt6azNoL2c9PSIsInZhbHVlIjoiK05VM0NRTWdxZm90b0t2b29QamJ5LzR0Y0ZFdXkwcnNEVUVSeXNPd0I0dU5sMWJySUIwbnhET2F1Zm1STFB3dmxOcEp4RHlNTmJCd3UxN2swbjAvY0RHbllqVnByWVJ6eW5IZnhhVGVQaU1FMUc3cUxBT2VHaDM2TFlQbHZTa2ROdVpYMC9WTUtKRStIMjcvRWw4Um4xK1VsUzlnR0hEOE54UGFKRUQzVERoNmZDeW9SQ0F0WHVBc3A2ODMxalVoUzNQVlNnSXg1aWNyb0RyY2hGVkNnNmlOVUMrM3JwR1BsOTMxK3Zjd0V6UW1ueTZjSEdDNzZSWFZEMHNPTU41bFJKK3RYQnNxN1dPYXNHK2JBMm42UUxmNmo4TkRnNzJycFJsWUdyVVNPdXQ0eFlnUnFuejB6TnBsZEg3VjkveHdEMmNEMU5pbndTTElZUXJPNWU1WWpHWFVRdy9pcGp3M2pyQlB2VWJ0ckY2MFhXcTdNdC90QVhEWmREK0M5TFpxTll0TEJad3ZQSVZpMlRjdHlJUjNudlhoUmV3RTZQdnZUWSttVFpTMFZJTzBML1p4MFFtcng3SzExN0RONXB3QXBmdnVvRTdHNDVPbHd0c3Y2eHQzYmRBMXFiSm1qSHVaSmRmRlVHZzR4QWhScnQzYXJ3SU1xa0JTNXZNdmtmaTMiLCJtYWMiOiIxNjc3YTY4NGQwZGM0NjZhMTc5ZjYzZmNkZGU2MDJhYjRjYTEzMDlkNzYxYTIwYzhiZTljYjZlZmNmZDcwZmY4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344640534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1232402973 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/14/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232402973\", {\"maxDepth\":0})</script>\n"}}