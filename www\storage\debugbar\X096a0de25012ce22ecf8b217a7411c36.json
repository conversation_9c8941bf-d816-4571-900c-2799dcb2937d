{"__meta": {"id": "X096a0de25012ce22ecf8b217a7411c36", "datetime": "2025-06-06 20:40:17", "utime": **********.461835, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.970997, "end": **********.461874, "duration": 1.****************, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": **********.970997, "relative_start": 0, "end": **********.26245, "relative_end": **********.26245, "duration": 1.****************, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.262472, "relative_start": 1.****************, "end": **********.461877, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "199ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00821, "accumulated_duration_str": "8.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.357491, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.991}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.394243, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.991, "width_percent": 16.565}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.435028, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 83.557, "width_percent": 16.443}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242413154%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZ2eDk2UWdCWjYxbGJUT2N3czNxNEE9PSIsInZhbHVlIjoidGpHVnFWdllwSTFFL3dsM1JIaTlIKzNLOWNSdkNXc3d1UEVCZ052czIvZzlhdE5nTkxRVS9zcXI4N1NPNzBkWkRueGlaVTllNDVTbUNCWndQc1NUSGVwR0FsTzR6bk5sa25xWEd2TE0yZlUrZ3M5UzAyS3o1eDZDWGcyb0ZVdzAwMEFzeDVpT1pHK0RaN3hteE4xOXpsYUp1WDAvV052UUh0c3lCaEkraDhNd3lKWWZoNW9NYlljdndhRmRQMHpwdDNQYUsxVFN5SHV2MUhBR2JyM3BYMEc3SkozcjI4TmhjQldpTnpiSnM1YVRqeExzdDBmeHdmcHJaeXFVTXBPVG1KdCswTDJaN0tRNmZUN011SEdLRXJ2ZE5XOWxLZGpVZmgzSXZHak1uRXBlWXkxYmpJT25qT1FkVG02WCszZXhnbU5WS3RtWUhOWDBnWVI0MW9PWkt3NmFmMFBJNHNab1ZTeUdBUXJYaG0vTkdNbXVMRFpNeVgxR1gzMFBaOUdKUXpuTXE4UktRalc3RTZqSHF3OHVlNXg5Rit3V25hYjlILzQ0QmtwSlNQR3FheG04Y3R3Vy9xOVR4NEUxd3ZXd09kZHhtc0FqRFRwYUhrdVhicVRoMW5WL3U4dW9sSjVTWnBHbFVrT3JITHdvNUt3c2cvN2NidGl6UGR2ZHExd2EiLCJtYWMiOiJmYjhjZmU3M2NmOTc1YjlkNTYwNDMxOGEzZjRkZDA5ZTJjNDZlOTFlZjJlOGIxZWE5OGFlNmJlYTZiOTdmMjk2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNsTFhpZXhGeHFrL1U1RmhjVmpFNmc9PSIsInZhbHVlIjoiMEtNUnFsZG8zMDZNUWlpQ2hmRFFKMER4UDJ5bTc0QjBzbDRKVlphcVZZNE9PaXNiV25reXl0UDVkU2hsZkVrd3E5UEZjVUFUV0FWREJHbjI2cVRKUjVwUVJWRVFBQlVqM3F3dHpyaHNta2JoRFRtMlU1U1VLWlVvenJxMEtGQ1doOGFGS2ZkQ0RKaW93bGhVSkFkU3Z4aFVRaElxbWJyNHBuT1JtMnV4Q0xMVjhBYXM4ZjZWL1pxT3I1Z29uR094dXZVZTVISkg2VFc3SExjYVpMaWg2bGVhcnN0S1l3OXlReFpCMjMvQmQ0Q1A4R1luemNUQ1NwbE1KWjdUaW5iSWxHcCsxU3BjbEEwbzhLSjRJbzJ0QU1XZlo4L09nTjZ3WTlVWTdjWllncVB3QmRPMTNsK05RT3h2cmo4OElVU0YzZFJyTDJRcjlTY1pVT1hzTG1YZWIyNi8xMVRvOWJBbXlZYXUwbjQ5QXVkU0ttbVRUdm5QQ1I4eEovT1AwdTlUVEtYOW5iUURnLy93elNhZGh2RDVTYWpxZklEbTU2YzdXUGIyNGc5bjV6WlR4aHJLUUtHYVdENkRkTldoQzN3aWdEaXR3Y0V3T0RVcGhEQVJ1b2JjWDllTC9PZ3lxYVh6aHZDODVjQzBwZzRMOFNHQWdMSmhZa2Vua3kraExkUTAiLCJtYWMiOiJiNzlhMGE2YzA5NzU4NmYwY2E0NmQ0MjA3NWZlZDhmZGVmNTBkOTAyNzc0ZjViOWE0OGRlNWYxZmY0MTI5ODFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-736836540 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5SyoJjvx9ER6DwaeNylpmlrCKyunmBMNBrM7fYhk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736836540\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1329101438 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:40:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjUrbTk3Z05BQWNWRWp6Sk1SK3VSWkE9PSIsInZhbHVlIjoiY0o5Wk53enFaVnlVT2wxVnFZYVF4ZXBQTFl4NXRQaXlXenpKQnkvcFJjY204MWFFcVg3eHVMOFdOM01TVGUzVnRZTUxUSng3YmYvL3BiaUNzeHpmcGlyN242d2YrT2E5WTNFa040OFhseXJVazUyL1hZOWUzRGRHL04vODQ5VTB5bWhJTmFVaXJSdzZnU1RNcUZjV21RTWZZbzNVNThHOFIyQnI1TW5icnNVa1BoRjZTVHpESEVlRzd0NGszdmxRcWI5YkVVSmhHUnJyUTh3VnZhUDNyWm9SSUVuVVgwZVRaR1N3ajZjYnZJZWw4WXh4UDlZNUhPZTNyZkxhWUdnYUE5REdvQWQ4TGJoZEI2VmpoVllPQ3REajhDRnRNdGtPalVsQmhQMC9NbzlHVHNqdzUrYS9tcjZDdW1qRm1hTVExMjZKazMwZGVKZ1djWnhuVWdxQm5rblNSTEdrSUFZeWc0Zk1vREF4WmlhM2RLejcwN1Zzb1ZMZWZrRW5EZUNQSndSTEFEYitlMVZnWnpNbmxYc1lySXd0VXNMVmEwUFpHaStuK2Q0UmZaWE1tSmNFd0R1UW1jWHpqUG9DS2cxRUpwRXB0TldWRjBlenBBNmFnSHM5VWVsRE5EUGVnRHB4VmtGUXFPcGczUFFiaU8zN0I3Z0JsRzE0R1M5SzF4TW4iLCJtYWMiOiI2MWRiOTk1ZjFmNzM4NzA4MDk2ZTFmODc2MzYwYWU4MDE4NmMwYjAzZTBhMGIzYmFlODA3MzAwNzEzN2E4MjFmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZjR1VQeDZmb2kza0pSbmppSFJManc9PSIsInZhbHVlIjoiN0IrNmduOUZHUmpFQzhPVHUvZlNoTG5ySmorWS9NNm9aZm41OFBVWi9KblhNeTVOekE3NmRKblVIL2RuSjc5SVpoYnZpQ0ZNZld4SzdRYlJLbTE2WFFmY3NMYnQyMWp5VnhnYS9vUjMydndsM29aSTA5TFhlL1Q5VWhZampFRDc4bG9FVzg0WHNiODBvaU0wSDVreWFCTXdZTnpDanNZTXJzUlU5R2JDQWovREppeXdOcmRyWXFOcWgxV05yWGdmTmk3OXUveVZGZmphdlVXMW1DVmwxdWlCR1g3TDNYSHlva1JaUmZ6OU04Sm5LRnFpdllpcFRWMG84cG5tOUplbXU3aHhaK2p6NlJTTU5BUjBUc04yRXBqdHdNVWhFY3IvRHQxL3phNDc5cy9mNUFSaThPMjVoU0JhQjJ1NHhCOElFNzI1ZFN1TWRmSlVPOW42WCsvWU40NE10UHMyc0ttWE82U3B1ajhCSE0rT0hJY1JsZksraG9tYlI0QXZuNHZGK2hkTVB0bEx2RTloaHQyalNRQVhMMFczeGxkNThDUTA1KzkwaXhUZUkyK2FhQnlZZlJUV3J4WWlnYkpBb2lkN05lWUo0bjBZamI2aFh5bk5NOXA2RWQyM3B0b3FmUE55TTBsOHBOZnJsTFFTTHVUbGIwekRKZDhqMTNsbmF4NEgiLCJtYWMiOiJjZGZmMmVkNWM5MjAyYmY0YjJkMjU3Y2ExNDljZWUzMmY3YjNkNmU4MjhkYWQ1YjIzYmMxODcyODk0MjQyYzY0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjUrbTk3Z05BQWNWRWp6Sk1SK3VSWkE9PSIsInZhbHVlIjoiY0o5Wk53enFaVnlVT2wxVnFZYVF4ZXBQTFl4NXRQaXlXenpKQnkvcFJjY204MWFFcVg3eHVMOFdOM01TVGUzVnRZTUxUSng3YmYvL3BiaUNzeHpmcGlyN242d2YrT2E5WTNFa040OFhseXJVazUyL1hZOWUzRGRHL04vODQ5VTB5bWhJTmFVaXJSdzZnU1RNcUZjV21RTWZZbzNVNThHOFIyQnI1TW5icnNVa1BoRjZTVHpESEVlRzd0NGszdmxRcWI5YkVVSmhHUnJyUTh3VnZhUDNyWm9SSUVuVVgwZVRaR1N3ajZjYnZJZWw4WXh4UDlZNUhPZTNyZkxhWUdnYUE5REdvQWQ4TGJoZEI2VmpoVllPQ3REajhDRnRNdGtPalVsQmhQMC9NbzlHVHNqdzUrYS9tcjZDdW1qRm1hTVExMjZKazMwZGVKZ1djWnhuVWdxQm5rblNSTEdrSUFZeWc0Zk1vREF4WmlhM2RLejcwN1Zzb1ZMZWZrRW5EZUNQSndSTEFEYitlMVZnWnpNbmxYc1lySXd0VXNMVmEwUFpHaStuK2Q0UmZaWE1tSmNFd0R1UW1jWHpqUG9DS2cxRUpwRXB0TldWRjBlenBBNmFnSHM5VWVsRE5EUGVnRHB4VmtGUXFPcGczUFFiaU8zN0I3Z0JsRzE0R1M5SzF4TW4iLCJtYWMiOiI2MWRiOTk1ZjFmNzM4NzA4MDk2ZTFmODc2MzYwYWU4MDE4NmMwYjAzZTBhMGIzYmFlODA3MzAwNzEzN2E4MjFmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZjR1VQeDZmb2kza0pSbmppSFJManc9PSIsInZhbHVlIjoiN0IrNmduOUZHUmpFQzhPVHUvZlNoTG5ySmorWS9NNm9aZm41OFBVWi9KblhNeTVOekE3NmRKblVIL2RuSjc5SVpoYnZpQ0ZNZld4SzdRYlJLbTE2WFFmY3NMYnQyMWp5VnhnYS9vUjMydndsM29aSTA5TFhlL1Q5VWhZampFRDc4bG9FVzg0WHNiODBvaU0wSDVreWFCTXdZTnpDanNZTXJzUlU5R2JDQWovREppeXdOcmRyWXFOcWgxV05yWGdmTmk3OXUveVZGZmphdlVXMW1DVmwxdWlCR1g3TDNYSHlva1JaUmZ6OU04Sm5LRnFpdllpcFRWMG84cG5tOUplbXU3aHhaK2p6NlJTTU5BUjBUc04yRXBqdHdNVWhFY3IvRHQxL3phNDc5cy9mNUFSaThPMjVoU0JhQjJ1NHhCOElFNzI1ZFN1TWRmSlVPOW42WCsvWU40NE10UHMyc0ttWE82U3B1ajhCSE0rT0hJY1JsZksraG9tYlI0QXZuNHZGK2hkTVB0bEx2RTloaHQyalNRQVhMMFczeGxkNThDUTA1KzkwaXhUZUkyK2FhQnlZZlJUV3J4WWlnYkpBb2lkN05lWUo0bjBZamI2aFh5bk5NOXA2RWQyM3B0b3FmUE55TTBsOHBOZnJsTFFTTHVUbGIwekRKZDhqMTNsbmF4NEgiLCJtYWMiOiJjZGZmMmVkNWM5MjAyYmY0YjJkMjU3Y2ExNDljZWUzMmY3YjNkNmU4MjhkYWQ1YjIzYmMxODcyODk0MjQyYzY0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329101438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2108254085 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108254085\", {\"maxDepth\":0})</script>\n"}}