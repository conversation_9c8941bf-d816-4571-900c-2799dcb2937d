{"__meta": {"id": "X27848f5e3ad5a3e3213c51475ea0678a", "datetime": "2025-06-06 20:34:36", "utime": **********.522869, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.885003, "end": **********.522899, "duration": 1.****************, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": **********.885003, "relative_start": 0, "end": **********.307606, "relative_end": **********.307606, "duration": 1.***************, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.307628, "relative_start": 1.****************, "end": **********.522902, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028370000000000003, "accumulated_duration_str": "28.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.406266, "duration": 0.02518, "duration_str": "25.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.756}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4636521, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.756, "width_percent": 4.759}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4953568, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.514, "width_percent": 6.486}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242072398%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IngyOTNBckVQUE9SUkJjVlg0WDJIcXc9PSIsInZhbHVlIjoiSDJockg2NjQ4dFFqOHFjRndFeHZTUUtqeEU4Zm9kSHNoRFlyeng5TFYya2xhWTJvVkRiZ0RaT2lJM1dNTUF2REtpUGdaWXE1bkZyYm5aV2tGRVVsTmk0N3FkWVZEc0U1NHRGWXZtdE44WHo2SmtKTjNmcE5vWDNqWThoVkVxRmh3WXBMODYxRGI4WEw2NzlKL24yWlplSUpsUEpoUU9EVXAvN0k3VXozR1FFSGxVQ244SDFMUnVFUEtsam12bzNjOUpBZE4xQUkvelpLaGJpMzNIeGFJZWNRYmlPd0NQUUMxNUhwbnhydmE4NjZKU1B0Q0lsSVVaeGkvREgyVGkvNzNVcmpXZE9OUUJRdjNtSFQ4UGsvZ0dCQ0RZUkR1dW1rTDhWNW1RVFY5ODExYllrb01pZGp6SmgwUzNERkxpcXl6OWdybWpRSTBFZ0c3QWhDbk9DYzRaejFFTG5jNU84c1Nkek5GNEpqS20vcmlIOE4vT01IdTVkSlFzMCt2SkJKL1IvSDVndFRJNHpjZjgrUDFobmtnZnAvVm1IZE1Eazl5NjFrZWsybHNPQXdvbTR1ZVdZS2wyK2Z2cGZIZ3V0TmZ2RDBSY1FjdmhWVXBNVXQ4V2s0Y25wYmFqV1N0aEpvZksreDFBL29JbjJIcXVLWDJGUUVSV3pjbFk1T2dhWEkiLCJtYWMiOiI0ZTE4ZmFlOTkwZDViOTlmMjE5NWI1YzlkYzI4YjBmNDUyZDA4ZjhhYTNmMDJlZTExZTA1OTg4NzY2Njg1MGIyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdFNlRzSmN5U2hFb2I5dnA1L0g2M3c9PSIsInZhbHVlIjoib3FjWndIYk1neHBxK2RpeHFJQVBkZHZMakU4VU9qcEllM1cwWHdzbHpLNTQzUjUxcDFaRTR0Y3pFaEIxK1cxSW13elRuNEdJb3QzNmpPYnE0Z3YyS250Zm9WQ29mRFN2TTBFNVZxSEtBdXRla0VrRXBwUlFGUTBRSTI5Qi9oRmlsL1FIak5HSmtoTkR0SnJqK3JhL0V1S3dZYUVxYzlyakxFNVVQS0JzeUhzWE9rbUxtS0NoMGpPakQ0NUFhUEZFQW5IVFhlamdqVXR4d3Nva01BcDA2enJIa2d4UnArUzEyMUM4Wm1WcGtzK0tlemMrMmdHVlRKaU5LZkxSMTA2YWxlU3kwaGpvZEdFOTBWSVpTb3NYQVJ5RkNnTmJIazNwZjFFZFhERnNZeVF5UEo5dnpiOHJ2UE1scTVnbWFCVUJmbnBDa0ViMGZpejVNTHJ3dFpWNi9vRjJtM0I3N2NraXp1U0pyMlZiTmpxamxlSzAzOE4rNXl3TGppZ2hCVDE0UlljUUZmUUxQZGtkb0h5VTlmVUxPWnpkZ3hpZUNTSFNreUZiZjBiM1NocDFDZ09mTnFOcGR4cFVWYk9xQVNETDJTOEtmUUY1OU91NWR5WTRZdDQxam5hd2J4K3FuQlU1TVlPNVFuUStERVUrNy80bE5samhXWnJGUWlaME5Lc1IiLCJtYWMiOiJkNWI2OGY2ODJmNzRiMmVmMzc5ZWU3NmExMTlhNDAwZWU2ODM3NTVjMDBlODJhYjlkZGQ2YzQ1YjdmMTRlNTU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-305626343 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305626343\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2018778312 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllYRStST3Yralh4WWFiSmRFN1FTRHc9PSIsInZhbHVlIjoiV05nVFd0WE1ZZzh4QjBEUXdjam5jVHhGZ0N2WkhuVlpLemkrVEQ4Q3pWTzU0VjladDJqMy9jTjd5bmFpbDN1VDdIbHB6VDNrVjB0WDIrbDZPQWE0Y1dQZ3pDV1BrZCtYWmV6NzlLRXQ5SjRBL2JjVUZsZzVRdHU4NjdkT0o2YkNkWlV6MndxOEJVclZhYjhNNFVtd0tUUHI4eXEvYWNBeXh5Nk9zT3VqN1hzbUZMRVpJSHJRNmJaazBxditkWk1sUE9wUFlzL0RvZTIyUEtIbFFXa0JGZnlLNGE4K0NLK1NQTk50enFXakgzejdWRTFLbHhIcEd5TjdUZXc5OVk0NkYvbGtmSXAraUdRdTJCY2EyRzJoSFBrUnhVa3VlbnhxRzJFcmRBNGJjUjdDeFZqYzFCRm5UaG5YeDg5NFduRmo0NmF6eE5TN3MyU0p0T2l2VGhydFE0bUphazBnZWozUVNEQVduSmNTTng1b3RZQUxpTzRyT0RtVHZLeFZGbXFnR3NvRDJaRGwyenNXeVo0Z0s4WGcrZ1JSclpialplZHVuUzBCTW1mM1hqdDkyUlFnOXlzaXY1c1lQSzF1VmF0am8wWkhBWkhJRTc1enRQZ0ZYZkE2WENSNmtwaHREK1ZPUkdqaWEwSkRydnNyd2hRMVpoZjhQekJsaFNxSUU3bCsiLCJtYWMiOiI0OTZiYmUxYzJmMTgzZWJlODY1NDI1NDg0MjUyMTkwNzExNzRmNjZhNDBhNWRlODEyMDFmZTE3MDYxOWRiMjRiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFoOExEQjg0V2lwdHlqeWZia0N2Qnc9PSIsInZhbHVlIjoiR1lHVUFEcEFFTndSZ3Vab3NIbllPN0NwRUo3UkQzRU1kMnY3SWljUlVGWEN1MWNWQ05Ldnh0TStZTmxWWDFmTXRBTzJWaGRZc1Y5ZTlNOG1aNk5jVkNKemhRKzRlaWg0NnVWN0ptVTcxUXZsYnRFcFYrWmtlMy96eVBiaEtxSjV2dVN2djRnNjYyQ24rbE0wMGU3VHVTenlhUElDSXhPSTV2M2V0YXpkQ0swZXNONEhadWpiQnNUQkZsZE5ZbFlIbmJhM1dkZm9jZG5xZGs4UUhHSUxoZHZJcFZWMDZRSm8wUWlSMHZoWGRhK0VUN3FIT3NjL1VQbi85K1lkdExGZGRIeEhBZFdXNHZQWHdCRGM2aSt3ZWhMM0xrNS91SXAyQ0ZCY0xtK2RlcXNGTnVYOUhOaDIrak9seG5wQ2tjQ0FoR3FxTWw1TU5nZVRMbGdKN3h6eUJTeGhiajZtc3VBekNXeFZvZnBSeCt0eE1HdFFZenVKekFsNW8xOFQyakIvUlEyK3BJU200SDQxR3VvbG96SEdJY3BvTE9MSnR5cDJ4aHV0TFBmQUFXQ3Q0dnJKUUJySFQ0S2tDNlpsRU5lRXVOL25hcnFDVjBwUVNJNTFqd3N1S1pjSDlMM2xRanhCbUZzYnUzN2pBaEN0dGlpZWFhSm1BM2Y0ZFpOSklxRGsiLCJtYWMiOiJjOWJlMDQ5NWYzZDE1OWQxYmQ2MGY2NzVlNDNlM2RiMmQyMDgzNWUyODIyNDEzNTY3NDE2NjM0MDQ0YzFjZjJlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllYRStST3Yralh4WWFiSmRFN1FTRHc9PSIsInZhbHVlIjoiV05nVFd0WE1ZZzh4QjBEUXdjam5jVHhGZ0N2WkhuVlpLemkrVEQ4Q3pWTzU0VjladDJqMy9jTjd5bmFpbDN1VDdIbHB6VDNrVjB0WDIrbDZPQWE0Y1dQZ3pDV1BrZCtYWmV6NzlLRXQ5SjRBL2JjVUZsZzVRdHU4NjdkT0o2YkNkWlV6MndxOEJVclZhYjhNNFVtd0tUUHI4eXEvYWNBeXh5Nk9zT3VqN1hzbUZMRVpJSHJRNmJaazBxditkWk1sUE9wUFlzL0RvZTIyUEtIbFFXa0JGZnlLNGE4K0NLK1NQTk50enFXakgzejdWRTFLbHhIcEd5TjdUZXc5OVk0NkYvbGtmSXAraUdRdTJCY2EyRzJoSFBrUnhVa3VlbnhxRzJFcmRBNGJjUjdDeFZqYzFCRm5UaG5YeDg5NFduRmo0NmF6eE5TN3MyU0p0T2l2VGhydFE0bUphazBnZWozUVNEQVduSmNTTng1b3RZQUxpTzRyT0RtVHZLeFZGbXFnR3NvRDJaRGwyenNXeVo0Z0s4WGcrZ1JSclpialplZHVuUzBCTW1mM1hqdDkyUlFnOXlzaXY1c1lQSzF1VmF0am8wWkhBWkhJRTc1enRQZ0ZYZkE2WENSNmtwaHREK1ZPUkdqaWEwSkRydnNyd2hRMVpoZjhQekJsaFNxSUU3bCsiLCJtYWMiOiI0OTZiYmUxYzJmMTgzZWJlODY1NDI1NDg0MjUyMTkwNzExNzRmNjZhNDBhNWRlODEyMDFmZTE3MDYxOWRiMjRiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFoOExEQjg0V2lwdHlqeWZia0N2Qnc9PSIsInZhbHVlIjoiR1lHVUFEcEFFTndSZ3Vab3NIbllPN0NwRUo3UkQzRU1kMnY3SWljUlVGWEN1MWNWQ05Ldnh0TStZTmxWWDFmTXRBTzJWaGRZc1Y5ZTlNOG1aNk5jVkNKemhRKzRlaWg0NnVWN0ptVTcxUXZsYnRFcFYrWmtlMy96eVBiaEtxSjV2dVN2djRnNjYyQ24rbE0wMGU3VHVTenlhUElDSXhPSTV2M2V0YXpkQ0swZXNONEhadWpiQnNUQkZsZE5ZbFlIbmJhM1dkZm9jZG5xZGs4UUhHSUxoZHZJcFZWMDZRSm8wUWlSMHZoWGRhK0VUN3FIT3NjL1VQbi85K1lkdExGZGRIeEhBZFdXNHZQWHdCRGM2aSt3ZWhMM0xrNS91SXAyQ0ZCY0xtK2RlcXNGTnVYOUhOaDIrak9seG5wQ2tjQ0FoR3FxTWw1TU5nZVRMbGdKN3h6eUJTeGhiajZtc3VBekNXeFZvZnBSeCt0eE1HdFFZenVKekFsNW8xOFQyakIvUlEyK3BJU200SDQxR3VvbG96SEdJY3BvTE9MSnR5cDJ4aHV0TFBmQUFXQ3Q0dnJKUUJySFQ0S2tDNlpsRU5lRXVOL25hcnFDVjBwUVNJNTFqd3N1S1pjSDlMM2xRanhCbUZzYnUzN2pBaEN0dGlpZWFhSm1BM2Y0ZFpOSklxRGsiLCJtYWMiOiJjOWJlMDQ5NWYzZDE1OWQxYmQ2MGY2NzVlNDNlM2RiMmQyMDgzNWUyODIyNDEzNTY3NDE2NjM0MDQ0YzFjZjJlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018778312\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1874006965 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874006965\", {\"maxDepth\":0})</script>\n"}}