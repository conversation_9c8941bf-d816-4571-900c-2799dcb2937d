{"__meta": {"id": "X0f70aa1652be876c41cfb982bde2fb93", "datetime": "2025-06-06 19:15:35", "utime": **********.535842, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237334.414004, "end": **********.535871, "duration": 1.1218669414520264, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1749237334.414004, "relative_start": 0, "end": **********.440055, "relative_end": **********.440055, "duration": 1.0260508060455322, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.440073, "relative_start": 1.026068925857544, "end": **********.535874, "relative_end": 2.86102294921875e-06, "duration": 0.09580087661743164, "duration_str": "95.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43105728, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00749, "accumulated_duration_str": "7.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5095341, "duration": 0.00749, "duration_str": "7.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1447275445 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1447275445\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-328473270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-328473270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-473132218 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"258 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=f9etjo%7C1749173423509%7C39%7C1%7Cs.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473132218\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-987638769 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987638769\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:15:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRNcnRqREQ5N25iTHVuM2pEVlZJRUE9PSIsInZhbHVlIjoidGZwMGFoejE5aExjaUE1NHdIclF1K21FVmlLZjR2QjBZaWJKcy9Lcjg3d0oydzB1SExlbmRjdzBzYTZXR1QzNVJDSldpaTlEWkloUDUrdzF2clVLblA1N1BYVkllbWpyRm9QaUlNamZrUmdDN3hpeWdLU0VjeFYwVEowOVBlWlIzek5JOFE4RVpaVUxweGV1SkE0KzJNcVRVckFjazEwZGp1dVZPTW5ZajdtbmliVWVDeWFhejlaWHhnQWRrOVdDRmNhdStOQUtPMHg4V3BIYlA0ZXJiVTMwS0FMQ1RsV3pLcllJWGFUQmN2RFh6c082bVgyL2paTVoySUNKczYxSTJyTE5IVHZPbGgzNDdNRkxnMXgzR3BhNnBvOC9OZm5wQk9xejRWSmwvKzhnTFhjRzBLWnRZbkYxS2FxVjhtVlUraEh3RnlWTzNZKy9kQjdPY24zTUNqMm9PWTBsdTZaU24wZ3FwcjZ2OVlPODBWOWZRSittaEdEVmN2S1VZV0hvVDRBMXFrSzZYakI3QVhGeW5BbjFpVTBOMzlTbGNJdDk1YndsUHNqNytxQlM5VGx2eW0zc2pLcGdOOCs2ek01OHBXOXAvZDNRZ3hzUUZYa3JFR0haSTFqT1R3TUh2Q2NCYUZHVkg4R21nTzFvdmhPR2pZeTkwRGJQS1lOc0UyVVkiLCJtYWMiOiJmNzBhMjM3N2Y4ZDkyMjBlZTc1OGVjMDM3OWI5N2I5ZDA5MGYwMDY3NWRkMThiZGJiZWFkYjc1NzQyNmFiOTYyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdGekNtWFNrdTMzbkIrdWpJMkZBNXc9PSIsInZhbHVlIjoiYlM0cEJqVjNwUTN6ZkJRbW5UcEE4aVNxY3Nyby9uNXFXMEY2bEdqV0MyWnJ4YU10V2s4SHpZaTdOSmdoak5ZZWViWFhXRTgzZEJYRzNoaWxWaHJ3aVVpbXQ1cVNmOEhuc2hCRGpDVUVjSzZwMzZDeTFON1RnVUFqdG1USzkyWHMzS3pZT0NkMHAxcmF6Ty9PNXhUMkttcWZ5S0FiT29qUlZXL1ovZGxkWGpTdUJtbHN0WS9FeVloUFhoMmNrOG40OXlKUmp2bFFsK09lTnZCUFZWT0VSM1dBTnpmZTJ1M2REUzNTZndSeU9wSEZ0Zk0raEtqM0dHSElESnRmMmd6cjRLck1CVzlZZzVDdmtUbklVdFIvdjJnT21JSXV3bjlrSi9ZallYb1VxQXpKNWJLWnBMQWV5eUtUVFFsTjFkc0o4RGtUTVFqc3dMSEVjYlRBY05UelUzUWJSK2pSMmZBVGhiQjFZZWx5RHBBUWo3V3AyYXhOL2lDQWZ4MWdxWFNWVy9IUlJyeVBaRW8waFRwSEhWektwMFY4S3JDcHhrdThMdTJWNllBUThObGU0cjg2WWIwNW0ybHhHR0lKZEYxdGQ0TkFMb0wwb2kzWGw2dUZIQk5rdUEwVFZvcEM5Um5rWGxDeGdFOUwrRmM0WHZ0OUZVVEZ2S2g0YjlxZjN2cHIiLCJtYWMiOiJlNDFiYjk2MTlhMzQwYmY3ZGE3ZjM4ZDYzN2E4ZjI5OTBkZjFmNjIwNzBhYzE5NzcyNjY1YmJjNzViMmE2YzIwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRNcnRqREQ5N25iTHVuM2pEVlZJRUE9PSIsInZhbHVlIjoidGZwMGFoejE5aExjaUE1NHdIclF1K21FVmlLZjR2QjBZaWJKcy9Lcjg3d0oydzB1SExlbmRjdzBzYTZXR1QzNVJDSldpaTlEWkloUDUrdzF2clVLblA1N1BYVkllbWpyRm9QaUlNamZrUmdDN3hpeWdLU0VjeFYwVEowOVBlWlIzek5JOFE4RVpaVUxweGV1SkE0KzJNcVRVckFjazEwZGp1dVZPTW5ZajdtbmliVWVDeWFhejlaWHhnQWRrOVdDRmNhdStOQUtPMHg4V3BIYlA0ZXJiVTMwS0FMQ1RsV3pLcllJWGFUQmN2RFh6c082bVgyL2paTVoySUNKczYxSTJyTE5IVHZPbGgzNDdNRkxnMXgzR3BhNnBvOC9OZm5wQk9xejRWSmwvKzhnTFhjRzBLWnRZbkYxS2FxVjhtVlUraEh3RnlWTzNZKy9kQjdPY24zTUNqMm9PWTBsdTZaU24wZ3FwcjZ2OVlPODBWOWZRSittaEdEVmN2S1VZV0hvVDRBMXFrSzZYakI3QVhGeW5BbjFpVTBOMzlTbGNJdDk1YndsUHNqNytxQlM5VGx2eW0zc2pLcGdOOCs2ek01OHBXOXAvZDNRZ3hzUUZYa3JFR0haSTFqT1R3TUh2Q2NCYUZHVkg4R21nTzFvdmhPR2pZeTkwRGJQS1lOc0UyVVkiLCJtYWMiOiJmNzBhMjM3N2Y4ZDkyMjBlZTc1OGVjMDM3OWI5N2I5ZDA5MGYwMDY3NWRkMThiZGJiZWFkYjc1NzQyNmFiOTYyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdGekNtWFNrdTMzbkIrdWpJMkZBNXc9PSIsInZhbHVlIjoiYlM0cEJqVjNwUTN6ZkJRbW5UcEE4aVNxY3Nyby9uNXFXMEY2bEdqV0MyWnJ4YU10V2s4SHpZaTdOSmdoak5ZZWViWFhXRTgzZEJYRzNoaWxWaHJ3aVVpbXQ1cVNmOEhuc2hCRGpDVUVjSzZwMzZDeTFON1RnVUFqdG1USzkyWHMzS3pZT0NkMHAxcmF6Ty9PNXhUMkttcWZ5S0FiT29qUlZXL1ovZGxkWGpTdUJtbHN0WS9FeVloUFhoMmNrOG40OXlKUmp2bFFsK09lTnZCUFZWT0VSM1dBTnpmZTJ1M2REUzNTZndSeU9wSEZ0Zk0raEtqM0dHSElESnRmMmd6cjRLck1CVzlZZzVDdmtUbklVdFIvdjJnT21JSXV3bjlrSi9ZallYb1VxQXpKNWJLWnBMQWV5eUtUVFFsTjFkc0o4RGtUTVFqc3dMSEVjYlRBY05UelUzUWJSK2pSMmZBVGhiQjFZZWx5RHBBUWo3V3AyYXhOL2lDQWZ4MWdxWFNWVy9IUlJyeVBaRW8waFRwSEhWektwMFY4S3JDcHhrdThMdTJWNllBUThObGU0cjg2WWIwNW0ybHhHR0lKZEYxdGQ0TkFMb0wwb2kzWGw2dUZIQk5rdUEwVFZvcEM5Um5rWGxDeGdFOUwrRmM0WHZ0OUZVVEZ2S2g0YjlxZjN2cHIiLCJtYWMiOiJlNDFiYjk2MTlhMzQwYmY3ZGE3ZjM4ZDYzN2E4ZjI5OTBkZjFmNjIwNzBhYzE5NzcyNjY1YmJjNzViMmE2YzIwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}