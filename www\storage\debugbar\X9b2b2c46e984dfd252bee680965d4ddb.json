{"__meta": {"id": "X9b2b2c46e984dfd252bee680965d4ddb", "datetime": "2025-06-06 19:30:12", "utime": **********.782928, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238211.157774, "end": **********.782964, "duration": 1.625190019607544, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1749238211.157774, "relative_start": 0, "end": **********.591629, "relative_end": **********.591629, "duration": 1.4338550567626953, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.591658, "relative_start": 1.4338841438293457, "end": **********.782969, "relative_end": 5.0067901611328125e-06, "duration": 0.19131088256835938, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.016999999999999998, "accumulated_duration_str": "17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.685977, "duration": 0.013689999999999999, "duration_str": "13.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.529}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.727423, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.529, "width_percent": 6.176}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.735609, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 86.706, "width_percent": 7.235}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.757579, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.941, "width_percent": 6.059}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1652480306 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1652480306\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-90087161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-90087161\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-615980696 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615980696\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2132812512 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238180611%7C36%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVJMXBPWWNQNkQydk5TVnhJZkRHQ2c9PSIsInZhbHVlIjoiU2VlckhGWkwzWFB1SnRmc0ZrSmNic2lQbFhQSFRjdzVlUWthRHRpSTI5akF1TFZZcjJuZ0J3YXV1b2UvaVBrWGRUVFR4THBuRHNFeURyVmZhRGtST0NFV1J3NUpYem9PUE9YTTlGRSt3NWlLZ1N5M0ZjK2prRlNGdmtiU3NJaEk0c01EbGx5bGxHZ1NZV3ovMnlQSE9zTkhqV0tJZU0rWWdNVlJVVWp0a3F1ZzhPMkZDTDQ4d2JxeldWZjdlMm4reEdoYndmbXgxZjhHR3VoMWhlWGJVRTZDakx5cDBSNk56OXdFV1V4STRjSit1RjljNHQ2WjlRbGQ4YXJLVE1nYW5UbExacHE1OU1IQktNZlVIVWRKTnNJN1B0S3N3YmhNaXU4SEZ1b0hFcTZoSy9IeEc2Zk9zb2JncWlnN1dBQWh3S3AzMmxTUW5hRVk4emRadjhTa3BIRnEwWHJiRTlkY2hJNXVlZ3ZuQnZsR1FpcUozSUluUTVBSjNIemUwOHZQZDRQelR6UDdRd1c0VEhLQkUrTzQvNTVLandBcUNKM09jWjhjaHJhSEFENmR2Wk45WlNFN05QNkt5MEtDR0Rzdld2ekhUNTFjaTE5azNuNUJIMTBHY1JIYVg5eFNBUHRUdXU1Y09LUnJFeXMrdHlISHM3b2s0UmJsdG13MkI1NWEiLCJtYWMiOiIyMGM0MjgxMWEwNTg2NDYxZmNkODQ5ODk5MTI0YzE3NWZiM2Y2Yjc3YTAxMGFmMmQ1OGE0YzNjYmMyM2I4OWU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNMSFlDNkRYWVJpMWJ0eVQ3Ti80MkE9PSIsInZhbHVlIjoiRUhlOWJuSVR2QndQTWZlMFZ1cHU1WXkrdlduSERVY2dZTnNseHVBdXhqZTNBbTZtQWxWdzRKYVcxcmRCck1mR0s4d2syVjFLazhkSTVDS3pyQUg4MExnanlOclVoYnpCei9jdVZYQXlOcW1qY1Z1QU9PZGl2aVgzVzFHRVhSU2NTMnFoWFpMLzFpU1FqNndLcEV0c0NZKy80NU1JL2EzbVlWQWpXZlp4QWhGR1NSWDFPeXVOZGF0Y2Q0cTBwYnJwa1ovaExET21pcGtsTWlzd2FyeHFXZG8rTkQ3clZiMFhvcHpxcUZYOHBVQ1p1UEYyVHQzaFJaYmpVTUJORjE4dVZYRTg2L0hib0E3d2hMQUVKSFJkWlFwNXYrbk91MTZsY2VtazFjM3duQWZWNzlyMnNJc21JdVhOaWJ3WUh4QmxST1RtUk1OeVFFOE5iTkg0OG56dmdoRVlYblFGbm8vUWdiQjNoUXp0YzB1YmJzbmMxR0dOWlo4VHZ6U0FES0F6ZUpXeUpvZWV6VUdyaDNvcjFwRHh6aCtTSU8xSXVLL0I3WUFZSGh1d3JMOTZLVVpGZWw3WUVlMGpiR1RBbzdpTnZNMGlFK3ZVNFhPSFZLbXVtRFhFVVk2TmNpcFRIcDY0WlpkUDhGUExDWjNYMTlZKzJ3YXB1clNnQjh3MlNacXciLCJtYWMiOiI2MDkxMDM1NzZiYzAwODAwMGVlNzkyN2Q2NzQxNzgyNDAzYTc2ZTdlNDMxZjdkNzVjZTY3ZDVmN2MxMWM1YWM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132812512\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-49824045 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49824045\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1399484237 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:30:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlY2NGpTa0tHbHhPQnlTREVab1JPR0E9PSIsInZhbHVlIjoibnpiWVZqK3JrQ25TQ3R1SHAzWlpyL01wSG9iMnN5bE1meWZlUEpET2p2OVdndWNvL2JZWSt4ejYrNCtvbGN3SFBiSk05RU1SVFVWNnlqNGNDa2Z4K3BzL1NtMDJRSkpmSUw3aHZaU3hKWlBSaXMzajJ0UjNieGJxU0xjUVM3aVdDeWl0bjljRlVnckNuS0E1UmZ0NHFkVVhVQjBkK2x0M1d1UEQramxHR0pKc0M1Tjh5ZXRML292ZWxpQ1lKNm5LcElkTDNIY2hHbVZMUHJJajNnQk9TTGY5S2dhaWZEK0NsTFN6eHU3MENGem9xSHNRVG1NbWQ1bjZtSnZqQktnbDhtYWNFMDVQRitRL2xDUjZWUFdOWk5XRWJnbzZOWlMzYkVDSnVza0hLb3lIY3NBSmZZMzRDUUdOTGlqTWxOYWlSdXlnaDcvRENoMlM1ZHBIbTE1VzlNZUxpWDc4SHhUOWxMclNldFZFeVhtdGtVbkEvdFFEeTBNWUxBUWpNaWI0ZVZjZERCbWdrT205cm9jT3dyOFdpRjhzL1Iwb0llSFVFN1Q5WTYzaENBc00rVHpiUW5aalZWeWM3ZjdMTEd0eGdRdG94ZlBlZVRxN2p1SDhHR1JYSW1kSCtFdmNmOUxNdGpWRFFpZGwvUjhUcTRBN3hDQ2wyMWhFeGM3bURDWkgiLCJtYWMiOiI2MWU0YTdhYmFkNzBmYThmMWRjODg4NzI4MjM1Mjg5ZDU0Y2IyNjgxN2Y5MjMzYjE1ZDNlOGI1OTM1OTQyN2E0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:30:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InQvS1N0aVBtRVlEM256U2pCelVZOVE9PSIsInZhbHVlIjoiRzBhVFhIZFFJWEJ3SUNIY0VCd3JzWDBxZkhIeGQ4elR4MmhjVHZCR3dNbGJGdnFrWmw3Nzk0YVZDUnRSRGlFSGswU3p1eWpBZGlyNklWSmluVXR2d0gvMnVGK3hnektVeUtOR3hMZ0xxVm9OVGk0N0ZQMUdXekhJRmZlK1BibkRkTEdEYzV1Nk5IdHZWWGVUYmRrclE4UzdTQ3d1V3VKRm1TRTRKNFBNZzNhK1d0MWJZSVBBYUtONnpDMkRnUFRHamYwSm1OQkxIS2ZIakxzMTRnUEJLMVhDSzRCOEVmVExseTRZdTJSaGJpcEVObUluQXJjVTRYbHpPRy9KMnpEb0dqbDRWVm5La0FIMGlLbXptaC9ldmRDVmRlSmJ3ak5OamM4NEkzMWJYclJNVno0VDlQMVkySktsNzZvbmJCaDkvUmFkYUlwT29XMUpoMWU3SWx0eVIzZTM5N2hIZlpUdXBrLzJTdGJnZ0lWOXBka052eVk1RkF1clNyUGtYalpydkdhUEpaNjIxN3NsMDJQeEg2QVJTQTAwN3dGZnhBVEhHOC85MEhZMmtMZnpld28zbHRVc1c2SlVzajJhalo2SnJMVHR3U2MxVmtnTW9aNFRWbHEwR0lQaHIyd21TYlBWQVQ1SmpYMTdMdDVlbHFtTlJnK2tqd0hjY21ibmFoZDkiLCJtYWMiOiJkZWEyMjUzY2E4MDExNGM1OGFiMzQ2MDM1MDIwMzc5ZDQ2ZDRhYjQ5Y2ViYTNkMThkMjU4ZjBkMTU1ZTA0MmM3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:30:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlY2NGpTa0tHbHhPQnlTREVab1JPR0E9PSIsInZhbHVlIjoibnpiWVZqK3JrQ25TQ3R1SHAzWlpyL01wSG9iMnN5bE1meWZlUEpET2p2OVdndWNvL2JZWSt4ejYrNCtvbGN3SFBiSk05RU1SVFVWNnlqNGNDa2Z4K3BzL1NtMDJRSkpmSUw3aHZaU3hKWlBSaXMzajJ0UjNieGJxU0xjUVM3aVdDeWl0bjljRlVnckNuS0E1UmZ0NHFkVVhVQjBkK2x0M1d1UEQramxHR0pKc0M1Tjh5ZXRML292ZWxpQ1lKNm5LcElkTDNIY2hHbVZMUHJJajNnQk9TTGY5S2dhaWZEK0NsTFN6eHU3MENGem9xSHNRVG1NbWQ1bjZtSnZqQktnbDhtYWNFMDVQRitRL2xDUjZWUFdOWk5XRWJnbzZOWlMzYkVDSnVza0hLb3lIY3NBSmZZMzRDUUdOTGlqTWxOYWlSdXlnaDcvRENoMlM1ZHBIbTE1VzlNZUxpWDc4SHhUOWxMclNldFZFeVhtdGtVbkEvdFFEeTBNWUxBUWpNaWI0ZVZjZERCbWdrT205cm9jT3dyOFdpRjhzL1Iwb0llSFVFN1Q5WTYzaENBc00rVHpiUW5aalZWeWM3ZjdMTEd0eGdRdG94ZlBlZVRxN2p1SDhHR1JYSW1kSCtFdmNmOUxNdGpWRFFpZGwvUjhUcTRBN3hDQ2wyMWhFeGM3bURDWkgiLCJtYWMiOiI2MWU0YTdhYmFkNzBmYThmMWRjODg4NzI4MjM1Mjg5ZDU0Y2IyNjgxN2Y5MjMzYjE1ZDNlOGI1OTM1OTQyN2E0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:30:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InQvS1N0aVBtRVlEM256U2pCelVZOVE9PSIsInZhbHVlIjoiRzBhVFhIZFFJWEJ3SUNIY0VCd3JzWDBxZkhIeGQ4elR4MmhjVHZCR3dNbGJGdnFrWmw3Nzk0YVZDUnRSRGlFSGswU3p1eWpBZGlyNklWSmluVXR2d0gvMnVGK3hnektVeUtOR3hMZ0xxVm9OVGk0N0ZQMUdXekhJRmZlK1BibkRkTEdEYzV1Nk5IdHZWWGVUYmRrclE4UzdTQ3d1V3VKRm1TRTRKNFBNZzNhK1d0MWJZSVBBYUtONnpDMkRnUFRHamYwSm1OQkxIS2ZIakxzMTRnUEJLMVhDSzRCOEVmVExseTRZdTJSaGJpcEVObUluQXJjVTRYbHpPRy9KMnpEb0dqbDRWVm5La0FIMGlLbXptaC9ldmRDVmRlSmJ3ak5OamM4NEkzMWJYclJNVno0VDlQMVkySktsNzZvbmJCaDkvUmFkYUlwT29XMUpoMWU3SWx0eVIzZTM5N2hIZlpUdXBrLzJTdGJnZ0lWOXBka052eVk1RkF1clNyUGtYalpydkdhUEpaNjIxN3NsMDJQeEg2QVJTQTAwN3dGZnhBVEhHOC85MEhZMmtMZnpld28zbHRVc1c2SlVzajJhalo2SnJMVHR3U2MxVmtnTW9aNFRWbHEwR0lQaHIyd21TYlBWQVQ1SmpYMTdMdDVlbHFtTlJnK2tqd0hjY21ibmFoZDkiLCJtYWMiOiJkZWEyMjUzY2E4MDExNGM1OGFiMzQ2MDM1MDIwMzc5ZDQ2ZDRhYjQ5Y2ViYTNkMThkMjU4ZjBkMTU1ZTA0MmM3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:30:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399484237\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1423358128 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423358128\", {\"maxDepth\":0})</script>\n"}}