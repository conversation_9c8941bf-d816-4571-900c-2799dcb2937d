{"__meta": {"id": "X9e3ba83a21860dbe476e74fa1f3bdaf4", "datetime": "2025-06-06 19:15:20", "utime": **********.889532, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237319.48519, "end": **********.889563, "duration": 1.4043731689453125, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749237319.48519, "relative_start": 0, "end": **********.674168, "relative_end": **********.674168, "duration": 1.1889781951904297, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.674191, "relative_start": 1.1890010833740234, "end": **********.889566, "relative_end": 2.86102294921875e-06, "duration": 0.21537494659423828, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44800120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.025200000000000004, "accumulated_duration_str": "25.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.754993, "duration": 0.01933, "duration_str": "19.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.706}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8078551, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.706, "width_percent": 5.317}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.848037, "duration": 0.0028599999999999997, "duration_str": "2.86ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 82.024, "width_percent": 11.349}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.868565, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.373, "width_percent": 6.627}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1750840739 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1750840739\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1637965643 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1637965643\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1000307512 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000307512\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-935721319 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9QQm14VlhKeVBpbXdkcGUvb1JuNEE9PSIsInZhbHVlIjoiS1RMN0JnbDR5bUx1c01rVFhsMnljRXowWHV5OFBudFlGVjZNdG5MTThjN3hvdkxFWWRldXRFZHJOTmVPbUhaSERyUVV0M0o0bU9wWTAwc203V3cyT3E4ajlGWFZidmRTN0g5T0RjNHhMN0xwdjk2MktCUmp2aHk1MUZ3RlVZODFSWWJQNkd0WU9oSWhuK2o0b1c2emdlNTE0RVNkOENKTW9BcTVzZkhvVG95OWVRZyt3U2xhVDFyZ1VNWUgzalpPOVp0dS9La3h6UW1ZR3NRbW83OWVic0V1Rjk1ek9PZ1dwazBmOVMxWnBjVE4vR2xVbzVWbXRseHd3b1VqM1pKMWlld2pnZERwTDJYV0d6SFNhcisrRE1hT0ZWdi9JWURhU1pmaFdyNkN0NEh2Q2hGT3ZUTHRIcndUc2lBQ3ZhMFBhenZHZjZLMDR2S0NMUjloZzNaS0RvZ3BVV2gvL2MwVXpwd25xS2VKeldjYzdaRmZHcjllK2hNTngxVlR3RVV6RVNTbkZtcFlnUG9CY1RwOHozSkwzcUQxaWYwOUJmK01EUVZCTXRUaGR3cUNKUzRja1FoVmsxQVVyeEdpMzFlUUdsQVRNSkxXUW0wZC95UVRUbVJ4dndiSFF0VGxOSzZhcUJrYnZLeHFRZEVsMzJVUWlNMmlrc2NUWGNMeVhYWFIiLCJtYWMiOiJhNjdjZTNlZjQ5NWY3ODIzY2NjMDI3OGI2Yjk2NzliMGI0ZjJiOTcxOTViYjljNTUwOTQ5NmI1MDhjNDg3N2U5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhEdW1vcElyYTRLejBLME5RK1JJS1E9PSIsInZhbHVlIjoiS0lvOTRQMXAySGRFVnUxM0hGN01HcklQM0NTZjVjWG9YUjRPdDNqbldmS0pqWVloUTJKUWVQd2Fmb2I5MjhYZFcya1kyOEdLYTVJNkkyUkgxd0F3Wk8wVGE2bXRoTm5CVVBBMWJSK2JMQit2N1lubmFMb3BzUzBYTm5QY2VmbmVMVGtscERjdzNNYzAzcHBtS3Z1K2Y2T1I2OFZmT1ZlZGZVMzVQYWxlRVUyL2c5bnlHTWRnMFkwVzcwUVUydFNmOE8wUDlxOU9uaTN3M3k3K0wyOUJpejRtMlREL2swWW0vOVRWaGJhdEpSNktkelRacjhybVRyQkRFSmpPTmNGWXhWWlBpQ3Y2dm1TeE0ySlRva2xUU2g3MVo3OXZKdStBRVo1cWNCUUFWVjJnSEM5RzBJalhkWWlVSVNGNG80RFdSLyt5TTdySDllK25UeEplVHNwd2lRWHhMVXRoZnBPUC9sd2ZzajVkRSt4Z3dzZkFVbUNyc0FhTGRQY2YvMjA2UFhZYXdoVmpWMC9pT0VnVzNycDBSV0lBME1KTG5oNDNKYnU3ZTlpL09XYkR3cTNGeFR4c3EwSTdhOS9jNk8rZ2VFYmd3RDFQWmNLR0pwYkZHV1dkZDlONXpWQUZVZXFkc0s2T1huS0tVQjNjbXYzVlRma0g0czVJaUs4WUNSa1AiLCJtYWMiOiI0ZTU1NmFiZDQxNjVjMWI1MTQ1Nzc0OGY5M2U2MWE5OTcwNGZkYjY0MmJkYzJlZmMwYWIyODYzMWVlYmI0Y2UzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935721319\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-432535411 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">07zLQ1IzhcCwivgYmqVlKD2YgfwQ5DGdasvfNucP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432535411\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1449473891 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:15:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZRM1ZEb25NMmU5S2pEQU5xMDJwMWc9PSIsInZhbHVlIjoiZkcrMjVKV1NGeFJ2S3F0bU1YeStncit4N0N6MkV2S0NRcUlkVkF0ZmpOWlkxZFlxVW5zQ1RGSWlpSGZuQWhzb2RsdXhJcVVxaHk0bGFvUlhPUXVhckw1Z3hSb3dJMFh6NHNmT1RjbDEvaVdSZDZ3QmUrdVVyZjhrczB0djg0Tmhpa01KcnJFTEZreWpIZXEyNkNDSWhaWXloTzdlSFlOa0dYK3FVdktuQnhJV3BqY1Q0eDJ2Qm9ZV3BDZ1J2UFhjOWFRSGZYQkRsWFlKZ2JuM0VLclFFSnVoTWhNdkZTbjEyWVV1d3YyelBkNG9yaUNKV2FoTmhRSkNrb1prd1ZHM2ZLNEtLWmRTQXhMZ3pxNWx4RXllWWlqTndYamJVMkUwYWdsSXo4dW12VWpEUS9JN0N2QURKUTdJbk16cm5uVEowTXZPeU9zeEpXMWkrNXo3N0xOWVZ6Q1JvaHg3d1pvd0FHZ3pFMEZsVUlaZE10dloyOWdRVTFkbWk3ZC9rV1R0aEh6VHFCcHVoVGZoV2tzS3B3Z0hOS1g2NmRUU3U0dVZ3QjZxWDIyTjVpNllOME9hSWdLZWVNT3lhTG85Rlh1VGNyOFFaVVpwcDRoKzJXbDVTRzRmQmpCRjBQU2ZydDBkREhBeWVxbW44MVZWMzhKczBvaDVZQ1JqbDJ3bjhOMDgiLCJtYWMiOiJhZjk4NDM0NzlhZDE1NTNlYTIyOGJjZmYwYjMyNTU4MzU0MDNmMzFiMzYzN2YwYzJlYzk5ZTdmZDU2ODYyZjI5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhCNThKeHU5cUlYcEt1Nm9TWU1ScGc9PSIsInZhbHVlIjoiNC9PTmlLVFk1TXI0OGh2bnRQMW02ODBkZ1ZPdWQ3N0tXeEJ2RGxsSXFzMHdEQW9GN0dHZFdJano0OFZFbG5ScWRPSFVPTmxscXJmbnlWcjlEV04wNGgvS1hEdXMwZ3R2SEFJL2VEMENIR0NtNzNtR24ySVFDQ0dHRHlkNDFnbFRFK01kbVBha05YbHJIN3VaNkdxTnZ4K29icURHZ01mSlh5bUNoaHpNWHFYVUEyRmJXOWwzR3Azb3ZkUzluZGc1QS9qY1hLZzE3OWM1R2dvQ0NiUG1NYW5DekQzR3pQVm5DZGltY2p1QkZtaTJ3eS9oM1NWbWxrV29mb1cxWHJVc1BQbEJES2RRUHo3eGlTMmxuRkxtdzR4eXZTK3IvUWRBa2hIeVExK1EyaG9seDRZQUYvTXhLM0hXYzhob1E3WmtudEYvWURGMzF6TzJCN3dNQzdzVnc1bk9PQk1wNjZjSjM2Wnd6aU4zMWRlYk0zQ0RLblVxVkw3ZWZ2ckVBaUpDSHZHWUxiQnE3V3E2dTg2Q1gySERSUm5aK1R1NHI0S3FDTzRadDR6ZjNMSnBFeFJSRGNlQ1RVZjdSZ1hKTDJOMjk4ZHI0OHhDL0cyV3FPTThhT2oxcEdBWS93bDZ0bnhLNWhiUVNONHBzQ2x4aWFJSXRVejNkN05UT0ZuUVJwNHYiLCJtYWMiOiJmZDZkZjM4ZmRmYWY2ZGEyMDBkOGI0MGM1YWU5YzlhY2M0YmQzOTE5MDFjN2M1NDdiNDNjNmJlODNlNTc2OTk2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:15:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZRM1ZEb25NMmU5S2pEQU5xMDJwMWc9PSIsInZhbHVlIjoiZkcrMjVKV1NGeFJ2S3F0bU1YeStncit4N0N6MkV2S0NRcUlkVkF0ZmpOWlkxZFlxVW5zQ1RGSWlpSGZuQWhzb2RsdXhJcVVxaHk0bGFvUlhPUXVhckw1Z3hSb3dJMFh6NHNmT1RjbDEvaVdSZDZ3QmUrdVVyZjhrczB0djg0Tmhpa01KcnJFTEZreWpIZXEyNkNDSWhaWXloTzdlSFlOa0dYK3FVdktuQnhJV3BqY1Q0eDJ2Qm9ZV3BDZ1J2UFhjOWFRSGZYQkRsWFlKZ2JuM0VLclFFSnVoTWhNdkZTbjEyWVV1d3YyelBkNG9yaUNKV2FoTmhRSkNrb1prd1ZHM2ZLNEtLWmRTQXhMZ3pxNWx4RXllWWlqTndYamJVMkUwYWdsSXo4dW12VWpEUS9JN0N2QURKUTdJbk16cm5uVEowTXZPeU9zeEpXMWkrNXo3N0xOWVZ6Q1JvaHg3d1pvd0FHZ3pFMEZsVUlaZE10dloyOWdRVTFkbWk3ZC9rV1R0aEh6VHFCcHVoVGZoV2tzS3B3Z0hOS1g2NmRUU3U0dVZ3QjZxWDIyTjVpNllOME9hSWdLZWVNT3lhTG85Rlh1VGNyOFFaVVpwcDRoKzJXbDVTRzRmQmpCRjBQU2ZydDBkREhBeWVxbW44MVZWMzhKczBvaDVZQ1JqbDJ3bjhOMDgiLCJtYWMiOiJhZjk4NDM0NzlhZDE1NTNlYTIyOGJjZmYwYjMyNTU4MzU0MDNmMzFiMzYzN2YwYzJlYzk5ZTdmZDU2ODYyZjI5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhCNThKeHU5cUlYcEt1Nm9TWU1ScGc9PSIsInZhbHVlIjoiNC9PTmlLVFk1TXI0OGh2bnRQMW02ODBkZ1ZPdWQ3N0tXeEJ2RGxsSXFzMHdEQW9GN0dHZFdJano0OFZFbG5ScWRPSFVPTmxscXJmbnlWcjlEV04wNGgvS1hEdXMwZ3R2SEFJL2VEMENIR0NtNzNtR24ySVFDQ0dHRHlkNDFnbFRFK01kbVBha05YbHJIN3VaNkdxTnZ4K29icURHZ01mSlh5bUNoaHpNWHFYVUEyRmJXOWwzR3Azb3ZkUzluZGc1QS9qY1hLZzE3OWM1R2dvQ0NiUG1NYW5DekQzR3pQVm5DZGltY2p1QkZtaTJ3eS9oM1NWbWxrV29mb1cxWHJVc1BQbEJES2RRUHo3eGlTMmxuRkxtdzR4eXZTK3IvUWRBa2hIeVExK1EyaG9seDRZQUYvTXhLM0hXYzhob1E3WmtudEYvWURGMzF6TzJCN3dNQzdzVnc1bk9PQk1wNjZjSjM2Wnd6aU4zMWRlYk0zQ0RLblVxVkw3ZWZ2ckVBaUpDSHZHWUxiQnE3V3E2dTg2Q1gySERSUm5aK1R1NHI0S3FDTzRadDR6ZjNMSnBFeFJSRGNlQ1RVZjdSZ1hKTDJOMjk4ZHI0OHhDL0cyV3FPTThhT2oxcEdBWS93bDZ0bnhLNWhiUVNONHBzQ2x4aWFJSXRVejNkN05UT0ZuUVJwNHYiLCJtYWMiOiJmZDZkZjM4ZmRmYWY2ZGEyMDBkOGI0MGM1YWU5YzlhY2M0YmQzOTE5MDFjN2M1NDdiNDNjNmJlODNlNTc2OTk2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:15:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449473891\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1571113884 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571113884\", {\"maxDepth\":0})</script>\n"}}