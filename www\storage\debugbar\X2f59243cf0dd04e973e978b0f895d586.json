{"__meta": {"id": "X2f59243cf0dd04e973e978b0f895d586", "datetime": "2025-06-06 19:40:31", "utime": **********.530608, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238829.903136, "end": **********.530647, "duration": 1.6275110244750977, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1749238829.903136, "relative_start": 0, "end": **********.318259, "relative_end": **********.318259, "duration": 1.4151229858398438, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.318281, "relative_start": 1.415144920349121, "end": **********.530651, "relative_end": 4.0531158447265625e-06, "duration": 0.2123701572418213, "duration_str": "212ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.022139999999999996, "accumulated_duration_str": "22.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4200609, "duration": 0.01832, "duration_str": "18.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.746}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4739811, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.746, "width_percent": 4.426}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.481824, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 87.173, "width_percent": 6.549}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.504571, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.722, "width_percent": 6.278}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-564107545 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-564107545\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1232252710 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1232252710\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1630733051 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630733051\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1693821628 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238801714%7C57%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFON3ZZZHk2OWEvWVVHOVVITVl5M2c9PSIsInZhbHVlIjoiWVFEL1A5ZXJ6RmVPL1IvNnpzV1VPT0ZKRTdhcVYzb0ExRUJ5N0Ric0w4ZXdoY2o3L1pmb2xmdmJkMTVydndRRVhyUXdwNWdlalp4WG1lU2RwL0NaTyszYkJlMWZsVWgrTWpFZzdMREhWUmZPcUNrZDNSclJHYmJReU16L1dNZVRuM0VJVXlrcG9QaW9RYU1KQVQyM2tjMlBFK1VQM01iVFNoTXMvUk1wS3VrZjhQR0lGTEM2Yi92M3FCTUVVLzBtVVRnamltMGRwVXBZRWZ4UUcweDk5NTdHRTE3YTRNa1Jycy80blA1dGxzZXRZZTFoR3o3Z1RrdFVqdjREc1RTVlRPQmZXKzJNc2tCb3liMnl3NTBPWHAxczRja2xVeEhlZUtCZFVmamhHOWFVdmc0cEorWGNIMEdqb21zTFQrTlg3c3k5aVBpQStncGh6MWpWNmtrd1M1TUU5Z3V2TTREMGpKZEZsVzhPS3ByYVlnYk5ubXJoRWt3RXJlYmFkL2NMNXBQTm9qaXpDc29OTEpkK1NRYkdKbEI5S2txeDlHemNpbTRZcTFVYk9jL0VCTjlyYzJhYXBlcTNXZndwVWJvWUFlWlJ1MDI2T0d6RW1Dcnh4a0ZMS3JibDFpOWJiOFlqVFptczlSd3V3d2V4TE5QUjU5eFVVVkpVWjJCQnpmQU4iLCJtYWMiOiJiOWMxNDM4OTE3M2EyZTFlNjhmYzhlZTkyYThmYWY1YWVlZDEzMjMxYmE5ZTc5MjBiZGM5MzhkOGE3ZjAzY2Y1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitoSllyUGtPd2ZIZFgrQ2FwSlNrSkE9PSIsInZhbHVlIjoidjgrM3pXWjQzSWk3K05YU2plSU43V1JBT25SYjdCMDdFdGt4ZmlGZ3NScGR5NzQ0bnorRXYzZ2FsMkNyZEY4WHZmdGl5T0lUMG51WHdNejMrTVA1QlpYUHYvWk5BSlNRKzUycGI3RWg3S3dYYjMrOE5vWFhXL1YyVmtZQzNQTjdDYkd1YUpxV2szSDBUb1FPTm9BZ2JUOXNVRGp0Q0lVVnJERjA2NEFCaUpPQzN6SFhuRGRtWENyNGE4d1RBUys2WEUrcW5sV0M1SWxXc05PVWVZTXlzWTYwVFI1S0huUlkrbTdOM010MnhYNGwydFYvcXFiNitEZmo1TFVZWE5VSHlvVDgzSm9yUXFIQWU5S1ZOQktnOXBVYmc2OExZc253dzhTZjV3SjRoRnh4clZnMWoyZXZlUHh1b0t1UlJSbzhwd2cwc28zRVpuZS9UUkx0eTliYTFHbWJNTnJtK3U5MkMwMXM0bk9wQUc3cnJqTU9QNkc4SEM3UGg3Qmk1NmFabUNoRXRQN1pLY2phcWZJWWxna3pzd3RHR2w2VFpaekg4N1BSS2ZxTk9ENzhqNG9VYkVmdXhKcFZBdmtBVml2clh5Sk15TTZxaURIZVBxNGFpN21VZlMwL2ZJbWhYUzFTMDdCa2d4R1FPeVJMeU1WQ0p4Q3JUTFZWd1R5YmhnK3MiLCJtYWMiOiI2YzI0NWMxOTBmZTYxODM3Yjg1MGYyYjQxNTdmZmRiNWZjZGQ1NzA0YmY0ODlhYWM1YWM5MzUwNTMxZWFlMjA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693821628\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-275942027 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275942027\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-530542198 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:40:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink0dDk3VTRuNlJNSDJJOU4xUmtIamc9PSIsInZhbHVlIjoiaXhCL0JESkVmZnFwQTJ4ZnZLaEZrNDEwOC9wVXk3d05jdlQ5ZHlsS25NVWxycnhrNnEveGJGREJWa3pteFl5cUw0c1B2Z2l3eCswdmdHb1hkN1c4enVoTFgvNnAxTGNuVkF6S2ZWOHUrdzMzWkhZNjkwcC9NOVdydHBpNXdrMmpGaCtRREJUZjlyVkFxVmt0SWo5ejFUZ1RidmYvQ0c2bFFMRlozZzBOeHlqQUxxUHBrZWlFeitmUkV4OTJRVVZnTVBlQWdMM0UvUHlQb2lnVjU3TVBrc3RPdmZFRHZhcVVpNUxmR2trSFRNV1RBL0ZiMVBlTDlQQnNlelo5ZDlON1BGTDloZEh2QUkwdWNST2RZblp5QXBDMmFwYzJMaU9heHl6SDI4RGZ5QmdWVHhWR1dpRUl3YWxNeVZPRldIMDZhRDJONTFEMjRxWXBDTHRsWnhiR0VGSHhuMGRZRWQ0WXIxWndkZExXdGpRM1ZUdXY5ZTh5bVpsMkNqYmczeEx4N3JGZm1CNWNuWmRoMFBPZVJTY1hjWTJKTDl3T043TDUyU25YeHJqVWllUTJ2OFdoUjlPTU9ackJXMVUzSGRBQTFPTnZkN1BYS0VkeVhHTi9jV0RYblRiVnFWZHAvQnRiYnlrU24rOFpibFp6ZVBhMFBHNzcwN3l5WGpOZkJJQmYiLCJtYWMiOiI4YTRlNzZjNjJmMjhmN2Y1MjA0ODQyYjEzZDM1MmY0ZTA1YjJhZThmYWIxZTIyMzkwZGEzODNhYjUzMjljYmVkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:40:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iml3M0d6TzQ5TWhFa2QyN3o3c2piMmc9PSIsInZhbHVlIjoicTE3ZGlLdUN3UjRQTUFERFUwWXNpcDZtK0ExWjBMaHBvOHI4d3pSVUpqYlNkNXpTU1VnQm4rbVU2WXNkWC9VYk1KSmZMenpXK0t6eWNyajVpU2xYNWdyMEtLT3Q4ZEU2dThvT3RraUVXblFFNmVmSEszTTFMVC92YUE1ODZMcHpackw3RnBGNmt2S09NOVRJdEs2V2srZ1lISFFoMlRhTnJVVXhjWThNZUt3TGxKVHJrZllJQ243UUM1ZUVjSVJNTEd2eWEvTTJqNHNTbXVIaVJ5bGIyR3RqL3N6YXpkdEtHQ2s2bFNuVFdDcG5RbEViaTkzMWFhTkFmdUl0YkU3OXFmbGM5bzdqS0cvZ05RZ2hhakdkK2NlTTZCUldTTytmY1ZHU24rbkx2NmQ1b3NsTzBmMXByMCtZNEVUWnU0OCtuMkE2K0xhV3ppN2UySTFlcFRTb3hJYnBGaEhZQVY0RUFxd0pCZ05UYTZrNXdmRkhLR2RYazlhV05uUW04YlNuTW1PcVdNSWIzTFQ3cGlNL0k3UEZnUEZab2ROZy9wQVFRR1EwRlZEczJrUWpxVERVOGRxdDQ3c2h0OGIybmVKKzQ3eXZMRGx0YjZ6MlB1dkZ6aVg0NDJYaFVOOEp1eS9sRlpHdXFoV3BQMlE1NUdYK3lkOU9KeVVvMDdsdHJodWwiLCJtYWMiOiIxMWIxMDU5Nzk3YWFlNzE2NDMzODE2OGVkOGNkOWQ5YzFmZTZhZjgxNWMxM2YwYmEyY2Y5NjkzZjZmNmUwMjkwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:40:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink0dDk3VTRuNlJNSDJJOU4xUmtIamc9PSIsInZhbHVlIjoiaXhCL0JESkVmZnFwQTJ4ZnZLaEZrNDEwOC9wVXk3d05jdlQ5ZHlsS25NVWxycnhrNnEveGJGREJWa3pteFl5cUw0c1B2Z2l3eCswdmdHb1hkN1c4enVoTFgvNnAxTGNuVkF6S2ZWOHUrdzMzWkhZNjkwcC9NOVdydHBpNXdrMmpGaCtRREJUZjlyVkFxVmt0SWo5ejFUZ1RidmYvQ0c2bFFMRlozZzBOeHlqQUxxUHBrZWlFeitmUkV4OTJRVVZnTVBlQWdMM0UvUHlQb2lnVjU3TVBrc3RPdmZFRHZhcVVpNUxmR2trSFRNV1RBL0ZiMVBlTDlQQnNlelo5ZDlON1BGTDloZEh2QUkwdWNST2RZblp5QXBDMmFwYzJMaU9heHl6SDI4RGZ5QmdWVHhWR1dpRUl3YWxNeVZPRldIMDZhRDJONTFEMjRxWXBDTHRsWnhiR0VGSHhuMGRZRWQ0WXIxWndkZExXdGpRM1ZUdXY5ZTh5bVpsMkNqYmczeEx4N3JGZm1CNWNuWmRoMFBPZVJTY1hjWTJKTDl3T043TDUyU25YeHJqVWllUTJ2OFdoUjlPTU9ackJXMVUzSGRBQTFPTnZkN1BYS0VkeVhHTi9jV0RYblRiVnFWZHAvQnRiYnlrU24rOFpibFp6ZVBhMFBHNzcwN3l5WGpOZkJJQmYiLCJtYWMiOiI4YTRlNzZjNjJmMjhmN2Y1MjA0ODQyYjEzZDM1MmY0ZTA1YjJhZThmYWIxZTIyMzkwZGEzODNhYjUzMjljYmVkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:40:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iml3M0d6TzQ5TWhFa2QyN3o3c2piMmc9PSIsInZhbHVlIjoicTE3ZGlLdUN3UjRQTUFERFUwWXNpcDZtK0ExWjBMaHBvOHI4d3pSVUpqYlNkNXpTU1VnQm4rbVU2WXNkWC9VYk1KSmZMenpXK0t6eWNyajVpU2xYNWdyMEtLT3Q4ZEU2dThvT3RraUVXblFFNmVmSEszTTFMVC92YUE1ODZMcHpackw3RnBGNmt2S09NOVRJdEs2V2srZ1lISFFoMlRhTnJVVXhjWThNZUt3TGxKVHJrZllJQ243UUM1ZUVjSVJNTEd2eWEvTTJqNHNTbXVIaVJ5bGIyR3RqL3N6YXpkdEtHQ2s2bFNuVFdDcG5RbEViaTkzMWFhTkFmdUl0YkU3OXFmbGM5bzdqS0cvZ05RZ2hhakdkK2NlTTZCUldTTytmY1ZHU24rbkx2NmQ1b3NsTzBmMXByMCtZNEVUWnU0OCtuMkE2K0xhV3ppN2UySTFlcFRTb3hJYnBGaEhZQVY0RUFxd0pCZ05UYTZrNXdmRkhLR2RYazlhV05uUW04YlNuTW1PcVdNSWIzTFQ3cGlNL0k3UEZnUEZab2ROZy9wQVFRR1EwRlZEczJrUWpxVERVOGRxdDQ3c2h0OGIybmVKKzQ3eXZMRGx0YjZ6MlB1dkZ6aVg0NDJYaFVOOEp1eS9sRlpHdXFoV3BQMlE1NUdYK3lkOU9KeVVvMDdsdHJodWwiLCJtYWMiOiIxMWIxMDU5Nzk3YWFlNzE2NDMzODE2OGVkOGNkOWQ5YzFmZTZhZjgxNWMxM2YwYmEyY2Y5NjkzZjZmNmUwMjkwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:40:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530542198\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-406043756 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406043756\", {\"maxDepth\":0})</script>\n"}}