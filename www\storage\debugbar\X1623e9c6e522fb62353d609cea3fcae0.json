{"__meta": {"id": "X1623e9c6e522fb62353d609cea3fcae0", "datetime": "2025-06-07 04:15:51", "utime": **********.55434, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269750.114344, "end": **********.554369, "duration": 1.4400250911712646, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749269750.114344, "relative_start": 0, "end": **********.347401, "relative_end": **********.347401, "duration": 1.2330570220947266, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.34743, "relative_start": 1.233086109161377, "end": **********.554372, "relative_end": 3.0994415283203125e-06, "duration": 0.20694208145141602, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44812728, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.024120000000000003, "accumulated_duration_str": "24.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4335082, "duration": 0.01853, "duration_str": "18.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.824}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4823089, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.824, "width_percent": 5.182}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.513557, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 82.007, "width_percent": 9.245}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.532015, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.252, "width_percent": 8.748}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-993860688 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-993860688\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1253910225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1253910225\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1887871603 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887871603\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-752265503 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269710707%7C3%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVHeldId25lR2VHSU5Sd2ZURytTS0E9PSIsInZhbHVlIjoiejloYSt5R0lkU2NzSVpsTzVUdjViS1ZNNTBXUlZRM3ZJcmFCZHNQU25uVytTU09vREowSjNLa1VNZmlVTmN6M0dzSjRlME8vZkd4UFFYQjdkRGxyM0FOSVp1MGU3VkVINnlhMVYrTS91MEFtTDVkOGlBa0FPbGFyVG1sUUFPZXlrNUpoNmVRdS91T3pBejMrMWhFTDlGVkNPV1JhWmVQZVhiTFYwQldKeEpkeDhzOTVqWmthZ2Y4c1JMcXZWTitEYytrb2xEQjBidDlVb1pOVmlHdjFXeVc2UHczL2crSXFlSjRKekl5UGQyVHYwZkY0YUVhbWVLYVNHRGRtSVN0Nk1aMnZVMkRTYkJWUE9CMzY3SVVNNHZJUmlsbVJtYWNqdS9BV3o3UXBrdlBZd1NqUVozdW12d3o2WDUyZGFWWGJiZjdoV2czUHJFekF6bThnZGVFTkcvQzE4dVBiRUFsNTgyQ0lKclJhY1EycGRNOTdMeHRXYkphalBIRWxGMFZvbkhIWWtXYWFmUFZVcStIVnNPaFNhV3ZGWjhWbllOMThhcFpuVWlMdmFpMENoK1pLVG0xbjdVRGszSGYyd2VIcjhZMW5wbDkxZmY1ZUtYT3I2VUsvTlFIcU0wQUYydytFemRHR1U5MkpCeisxbkIybGtYakNNM1F3U3ZNN1NlTHIiLCJtYWMiOiIwYWI0OGI2MjQ1NWM0YWRkNWFlMDY0NzFkZTE3ZmI4MGUzYjUwNzlkMzQzMTI4YzFiYTEzNzdhYTRlMmQ0ZTg5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikh5TEtQQ3FkOW1nQjdJR3JMYWVEdXc9PSIsInZhbHVlIjoibFR1THdmdm9adk5PbTdER2hOekY1Q1A4bWlycXVQdTZxNmNkOWJpaGdxUy9CYWFER1pqM3R6NUlURkJsV0lkYzlGRHZCb0RnWElESkJHZVpBc2NmeG55ZzY0aS9Vc0JrL2JmSnBqMHJyeWRUV3JIUGRFenUrZkMxZURoYXJIbzBZTFp0Vnlma1NhUUkwdVlOdEFyYjh1OWdSSlpVdjl3Z1pzUENzRjBucVJwRmQ4UjVZVW5KYmJmWnkwRldmYXVyZTNobnIwci9Kcy9rTGIzVFg4YzVNNXdTZ1BIVkFKOU1TYnRESXR2Y2NJR25LZ0dVcm16KzdvWUxLcHlvYW5EYW40aTF1bjI1TmlBYmZBdk5RKzdWdmQ4ZGM4Y29yR0FCOFN1WFRiMVljRFVvanBqNnVzYmcwMjB5MmRUeTJKaDJFT3NkQjFkR0c3NGErQkxZUHJSOTl3aFZqVE5lczV0aHU3OHRKOGJ4UDR2cEFmT25GR3U5ME4wcGxUaWFkRUF3UGcxK0p2MzJiWmRXblFCTmQyRFJpOHZkOWcydGpTWFROUEtIblVocmFjR2pZT25OZnQxejJSUUpGY2loN2lWampHU21aUTVyUTBsWloyZTR5ais3YkZlWUQ5aEtheWltN0plN1k3S0RVVGhJaWhBWmxKZGRqQ2FBbkR3a1ZnSkwiLCJtYWMiOiI3YjRmNWNmOGZiM2JlMjRiNzcxNjg2YWQyZTA1Y2ZkMTIxYjRhZWY1Y2ZjZTRiYzI1MjA1YTQ1ZTM3YTcwOTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752265503\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-766053789 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766053789\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1636733974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:15:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikt5WUVOOGdTVmMwd2tsSEVVcFNpZ1E9PSIsInZhbHVlIjoiQVo5aGc4MldRZ1VtcWxsaDlSYUIxS0J5RktrdjBVYWk3ZSsvMlBqU1FrSkZJOEJqb04zUjRVMDdZNGZ5VUM5a0l2OWkzeXpaVWJZenpERFc4NkxZQyszRzNRa29zS1lhV05Cbmh2UWZ3VC9HWEJZYzhpTDFXRUNzWXdMSGNhb0pjNVh6bzkzbllpaTZyaVJFOGVtaXRkYVRGM2prR0hBWXNkaThuTFY1QTNFR1JUcGMvRVcwU0VrZW51TTlHS0gyQlF2aWJRalVjVVVuOG4zcjFpU3FhQXovSEdpUTRPanVaUVlVQXF2Nlh5T1RoL21EZW4zRU1OaUJ5WEY1WUpSU3JqdG5QTHdYbTY4eFZpdjBHbnZObi9iSnZwQ2RDcDZBOVBNQ0IwNXdMMFdNWWsvRkVwbmNCcUd5VUJmSDVXWGlrZHRWcEFEWkRoN3M3MnY1TUNCUkRqODdkSmErVytXeTJlZk0zVVk1Vnh5aXNHU3EyUFl0ZFU3ZS9uelhuUTFVWFhqL1BzWU40VS9jMGZ6dzBzU043SkhhRWNmT1hEQkFWN2NrTUpqQ090bzJ5MlA0bStUUGJwNkkvczhNK0dXalFjdklaL2lYSDVxK3ZyWDRLeTkvRlNFL3lJYXFZWVA4Ti9FdDJrdUp3Sm1jR1FndUVuRnhNaFIrOWNyRlc1ZlAiLCJtYWMiOiIxNDY4YzA5MDIzYmE3NjYzNGM4MWIxMjdjNWRiZjcxODVlN2ZkNTE2NGFlM2VhNGYwM2Y0ODJkNjhjYzU0NGZkIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii95VWdULzEwK3BBdmhlSW0zaW5MMkE9PSIsInZhbHVlIjoiRlVNVW5teGtwdE1XVCtSMnZFYUNvZmZWcHVUTjlyNmtwWmZDeGNwZDlqUXlwTzVBVzJVanBCbVhWOGNJY1hNcGxqSy96NldZYjREQXBxQ0RpZU5ib1F0VG41ZWFnZVR4UHpTZUNFN3gwSTdaUk9BbEtMbzNWUEJXMDIvcjZGVkIrZHk0N2paM1h1NzVCRFpURHhyOE8yNkp5N2JCS2lHbDZiQTJTSEFJbWJ3TGh2YlFRYVFRbWppcEFBYktEZERTNkFPQnNTeDhqL0NpUkZpMGo2U0xieUdPaC9jSDFaSGNZUnAwQ2ZFRVBhN3FhR0s4NmtkR0FjcjB2bng3ZGI5RjVwWHVqUHU5OFV0UzArUW4zSmdnYm5OUlQ1Q0cwR2phRWdBODVnUjdMMTlGdWZMemRMeWNhdkJHK1ZDNGpaMDFNa3ZHRnN2ZFVzSG93eUkrUW9Da2h5U3Q4RXA4ZzMxUExCV3grblMzK3ZZc0EzNSttYWlLWW42NzdJSTFCYmxTaVd2WWJIeSt0aXQ3aVMvNTBkN0diaWZrU01LeWthb3VSSlJYQlVVbnFqT0dNS2lkckwrb2FyUnJ5TjFjUUdVcGk5aGw3Zk84VThuNGNraElPOEZxK0VrU2krZTZ6NXhrdEF6WStKd0F5T2RVd2RJZVY1T1BtT2pLc3lwWXpZNG4iLCJtYWMiOiIwNzczNjkzMDBlOTkwYjM5YzIwOTUwMzAzZjg4NTZlZDVjYmNkYWIyN2I0ODk3NzA3YzU3MGIyY2JiYTVhZTdjIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikt5WUVOOGdTVmMwd2tsSEVVcFNpZ1E9PSIsInZhbHVlIjoiQVo5aGc4MldRZ1VtcWxsaDlSYUIxS0J5RktrdjBVYWk3ZSsvMlBqU1FrSkZJOEJqb04zUjRVMDdZNGZ5VUM5a0l2OWkzeXpaVWJZenpERFc4NkxZQyszRzNRa29zS1lhV05Cbmh2UWZ3VC9HWEJZYzhpTDFXRUNzWXdMSGNhb0pjNVh6bzkzbllpaTZyaVJFOGVtaXRkYVRGM2prR0hBWXNkaThuTFY1QTNFR1JUcGMvRVcwU0VrZW51TTlHS0gyQlF2aWJRalVjVVVuOG4zcjFpU3FhQXovSEdpUTRPanVaUVlVQXF2Nlh5T1RoL21EZW4zRU1OaUJ5WEY1WUpSU3JqdG5QTHdYbTY4eFZpdjBHbnZObi9iSnZwQ2RDcDZBOVBNQ0IwNXdMMFdNWWsvRkVwbmNCcUd5VUJmSDVXWGlrZHRWcEFEWkRoN3M3MnY1TUNCUkRqODdkSmErVytXeTJlZk0zVVk1Vnh5aXNHU3EyUFl0ZFU3ZS9uelhuUTFVWFhqL1BzWU40VS9jMGZ6dzBzU043SkhhRWNmT1hEQkFWN2NrTUpqQ090bzJ5MlA0bStUUGJwNkkvczhNK0dXalFjdklaL2lYSDVxK3ZyWDRLeTkvRlNFL3lJYXFZWVA4Ti9FdDJrdUp3Sm1jR1FndUVuRnhNaFIrOWNyRlc1ZlAiLCJtYWMiOiIxNDY4YzA5MDIzYmE3NjYzNGM4MWIxMjdjNWRiZjcxODVlN2ZkNTE2NGFlM2VhNGYwM2Y0ODJkNjhjYzU0NGZkIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii95VWdULzEwK3BBdmhlSW0zaW5MMkE9PSIsInZhbHVlIjoiRlVNVW5teGtwdE1XVCtSMnZFYUNvZmZWcHVUTjlyNmtwWmZDeGNwZDlqUXlwTzVBVzJVanBCbVhWOGNJY1hNcGxqSy96NldZYjREQXBxQ0RpZU5ib1F0VG41ZWFnZVR4UHpTZUNFN3gwSTdaUk9BbEtMbzNWUEJXMDIvcjZGVkIrZHk0N2paM1h1NzVCRFpURHhyOE8yNkp5N2JCS2lHbDZiQTJTSEFJbWJ3TGh2YlFRYVFRbWppcEFBYktEZERTNkFPQnNTeDhqL0NpUkZpMGo2U0xieUdPaC9jSDFaSGNZUnAwQ2ZFRVBhN3FhR0s4NmtkR0FjcjB2bng3ZGI5RjVwWHVqUHU5OFV0UzArUW4zSmdnYm5OUlQ1Q0cwR2phRWdBODVnUjdMMTlGdWZMemRMeWNhdkJHK1ZDNGpaMDFNa3ZHRnN2ZFVzSG93eUkrUW9Da2h5U3Q4RXA4ZzMxUExCV3grblMzK3ZZc0EzNSttYWlLWW42NzdJSTFCYmxTaVd2WWJIeSt0aXQ3aVMvNTBkN0diaWZrU01LeWthb3VSSlJYQlVVbnFqT0dNS2lkckwrb2FyUnJ5TjFjUUdVcGk5aGw3Zk84VThuNGNraElPOEZxK0VrU2krZTZ6NXhrdEF6WStKd0F5T2RVd2RJZVY1T1BtT2pLc3lwWXpZNG4iLCJtYWMiOiIwNzczNjkzMDBlOTkwYjM5YzIwOTUwMzAzZjg4NTZlZDVjYmNkYWIyN2I0ODk3NzA3YzU3MGIyY2JiYTVhZTdjIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636733974\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-429279214 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429279214\", {\"maxDepth\":0})</script>\n"}}