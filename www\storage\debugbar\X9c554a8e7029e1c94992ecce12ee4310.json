{"__meta": {"id": "X9c554a8e7029e1c94992ecce12ee4310", "datetime": "2025-06-06 19:14:47", "utime": **********.910469, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237286.566403, "end": **********.910505, "duration": 1.344102144241333, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749237286.566403, "relative_start": 0, "end": **********.747261, "relative_end": **********.747261, "duration": 1.1808581352233887, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.747285, "relative_start": 1.1808819770812988, "end": **********.910509, "relative_end": 4.0531158447265625e-06, "duration": 0.1632242202758789, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44754848, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00874, "accumulated_duration_str": "8.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8348591, "duration": 0.00609, "duration_str": "6.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.68}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.866535, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.68, "width_percent": 13.616}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.886353, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.295, "width_percent": 16.705}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-279091176 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-279091176\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1452177285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1452177285\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-14091826 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14091826\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1813984537 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRiajRIYjFvY2xLK3EvMzdMNU90bXc9PSIsInZhbHVlIjoiazl1aStDczgzMDVkS2RzYndPaldDNlNEdHg0VnNKellQeFZGM3I1dldJaDNRSDJsdWJEUmhsZUkrUGVwb3duY2NkbGw5N1Y2WE5HOWUxQW9vSHBiUE5JWWkwZjI0R3Q4N1RyeUwzU0xmOVV6dmxuRERmVkgvNUFRWDhpMTVUbE0vQklQdkdVRXdiSUk0em5XNkp6NDZ5MVQyM1I2aEFHRjNhY3o4WlZ5VGx4Rk52NE1RRHhHdldxditpNzRhNjFmQUduSm1ISCsxZU90a1VSUXJ5M1M4djVjNlVTNTZjTGw0aHYwT2FjS0lHaksvaHg5bkw1VVZEcHFvd2xqZmRIWmJ3VUJOTUhOaDArS2hoZXFGTHFITzczbWRPQnlpUnpPUlpqUXJHMlN4TEZHL2duWC9nYjVHTDF6d1FaY1h5a1I4cDlyZVFMS2RIMG0zdSs5RDlsYUg4dnBXQVFGWWxvOXlCbDhJQ1JHdmJmNWFWYVBrKzJWTnZUZXNIdVFOZjNId1YwLzBLNUFWVGlJa3k3U3FFdnpDei9NeXpyOFNGcU5ESVNOdlJDbm1LUGVkSjBvcS9FR2t4blZOT09wZEc5bU1kdnBFdi9CY09hOHZwZzVsbFdIbjR6dlkrUDZORzZ5SjN6aHA5SWZ2TmNTcnlxRnlKY2lRRUg3YTRBTXJiUFciLCJtYWMiOiJhNTMzYWZlYzdkYmZlNzllMzlmMTc5YTE0NzAwZTBiMmNlYWNhMWVkOWExODE4MjM1M2FkYjliZDVkZmZjYWUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9PNyttclV6U1ZTWE1GZmgwT3pOMWc9PSIsInZhbHVlIjoiSFZRRmpHWmY2elJsbnBHYzdKSmJVbHlvYzhNeFBwZ1oxbmNqaWNOVHRsOU13cjFmc2R3VDZkTUdSZFE4WkQ2WGF5RDg5TXlKNWp3MHNSdkNmU2d3MGZrTWpXdzI3V2hYWDNYY0RrQzN1QUQ1MzFtYmxsMFg4dy9XQVRKdlVBRS80RjVDSjJOdmpGOGRjL05aMk5wUVRUVHYzc0JtSmNxSGs1WUlVdVQ1U3pLekk2UzZ1YjNUbGtieXMyVE54TDQxUmI1WC9PYTFqT09mTkJXVFF4US9nQVpkVzJGMVlrS1I1U1hnVlc3TGcxUy9kL09JMVpCbVFXRCsybTNVRkhwREpieUJIUGk4cGo2cHlOMUFmcUk3MzZwNllJbWlVOHlLcVlBUDJSR0d5RXBUOUxueDc3cE0ydmZhYkF5K21UUlNaTGpsYWlQdmlvSWZwVjBBU1RLQzNLNERWZE1TL0lDVXo3K0VaSzlWWnpXVi9EUW5IYVRxaHo0Z3o4K1Iwa2ErWkRHSm5uOTBETjhMY2ZXZXZvaWVydmRLNGlYM0VjOU1qQTZKaHVSSWhQbUVhUUZQeTBpVnhwNnN4QUlORUpnMDZXOENidmFES3hncUhVYndjbTNxYVVFVkRUUXE4akFXWWYySldtcHc3VVo3QWd2SWdtaTZaRjkrc1lLK2tjUjkiLCJtYWMiOiI2ZmVmOGVhOWYwOTMzMDY2YjE1NjAxNDkyN2RmYzE3NTlkMWNiMGVlODc4MWRhMjdjNTJkNjlhYmU1MzZjMmY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813984537\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1601622203 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601622203\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2025520572 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:14:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFNeVlvNExmZ3BTL29zZjFJdFFTbGc9PSIsInZhbHVlIjoiT0dKNjh1UlpuUFJIQWYyaWNEeEg2aUJsdVpsWHBKK1Y1cFRNZXZmNk9GWlZLWVRHVVNySVBKWWtLc0pLbmdYZHdMeFBTQjBNQ3N2WkQwVjJJL21zZXdRWE9UUU9MbFBPb3hod0ZJUzJ3UU55YS9qbmZQQVRBL1hKQjRDOUZvVVY1Y2laUXJiZmZNeVJxc2lmSEpiVmduNXVPdnlMZVg5aXpSOTB0RW9KNGl3ZjFkc2IxOVA0WTlBMmt5Nk92dk9vdzMyZFBmdDhwa3I3SVcrL3o0VERBT2tnOGt0OEJ5aTJMZmM5TXorRElUOFdrcWFFakZkMkVUZlRFN0tJQzZ0dXU5cjlPRU9XK2Z1T3lPRmNmcEhabHFJTStBc2t3RXl0YVpXY0U5dW45L1QrVXAwaUZYWlVMVUpvSW51Y2VEVnlEbkNmczNkRHUxSlVmaTZKWTRRQ2tsUEVhUXE3emw4dURsenVGcXZFUU1SejhQVklaaTNyN091MUFGblBEbHExRkUyeG1ueDdWMXNnZTR0Sm9qd0pHTDhBSjl4dS85UHBZNUwrWnlZQVdBUWJJaExuRDkxY2F2dXRLZ042Y29PMWtVb1VsdWdqTjRVOXdYaHBScldjOU5TSitDbytWbW5qUUtDLzRQNzdibmtrL2tadVJBMUNtS01KcnFITFlCRTYiLCJtYWMiOiI4MmU4NzcwN2QyNmVkYWY0NTk2OWM1M2M1ZjEzMzk5MGYwNmM4NjQzNGE1ZWNhN2IxMWY3NWJhMGViMDE1ZGQyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNKb1N5SGIvWGxiV2sraTdab1JhUUE9PSIsInZhbHVlIjoiTFNZbTRCQnNNU2l2eG1FcCs0VkU3VmdRS3pwZFBvVHBKNG9QQ3g5dklQK2QrWG1BVFN3TWdWWEtvL1o3QXBDVkV3Mkp2UEpkZ2F4RmU3VGxhNklseGxUL1pqMjJBT0RjanpsZmJHRVBYbG5TaWw3czFESXdDV0Rtck9wNHpXLzl0em02UmpVVGNCcnVXSE9rRkc5NWlYRCtlNzdOU0MzUFBqcklyYkU2Q3ZNaFZyRUQ0RTJBVTZ0bHQ3bEpwdmxDVW91TitGakdWR3lGU3N5K09LdjNmVUNjKzlXNmpXZ0E2bXZaU2MyaUtRZEpKWmQzdTFubkxKWGdnakdKWWliRDEreGYrZDV3Q0g4amlrWG95N2pGaHphZ0NGcW9STEI0WU9heDJtaGttUGM0NnRxZGJjb01nZHI3SW40c01XWnhqSGFRdmlEeGNYK3dzejFheGp5WFpUQXYrUzZWcmEwRkhjOFo0aUxTTWdsZ3drOU5YSVJDSW9kR0dYcDg4Wkt5anBucm0rZzVCa2lrUjI1eXNDdVhmRXg2UFYrdDhYVlRlRU53MjJIOXNpMnI0cVhUdXkxMjJoY2dnZ1dJZmRTRTVCazJtUFByb1F5NXl6ajZnWDMwdUVtNVYzMlkvdDNPajEwQkhSSmE2OW9EYk9BQ1N3ZnFjRlVFQ0dZcElwRDciLCJtYWMiOiI1YzNlNmFiZDdjOTk2OTc0ZmMwZmE3ZDIyYWMwNmUwZmE1Mzk3NzFjNDYwMTViMDc3NDcwOWVjOTVhOWViNWMyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFNeVlvNExmZ3BTL29zZjFJdFFTbGc9PSIsInZhbHVlIjoiT0dKNjh1UlpuUFJIQWYyaWNEeEg2aUJsdVpsWHBKK1Y1cFRNZXZmNk9GWlZLWVRHVVNySVBKWWtLc0pLbmdYZHdMeFBTQjBNQ3N2WkQwVjJJL21zZXdRWE9UUU9MbFBPb3hod0ZJUzJ3UU55YS9qbmZQQVRBL1hKQjRDOUZvVVY1Y2laUXJiZmZNeVJxc2lmSEpiVmduNXVPdnlMZVg5aXpSOTB0RW9KNGl3ZjFkc2IxOVA0WTlBMmt5Nk92dk9vdzMyZFBmdDhwa3I3SVcrL3o0VERBT2tnOGt0OEJ5aTJMZmM5TXorRElUOFdrcWFFakZkMkVUZlRFN0tJQzZ0dXU5cjlPRU9XK2Z1T3lPRmNmcEhabHFJTStBc2t3RXl0YVpXY0U5dW45L1QrVXAwaUZYWlVMVUpvSW51Y2VEVnlEbkNmczNkRHUxSlVmaTZKWTRRQ2tsUEVhUXE3emw4dURsenVGcXZFUU1SejhQVklaaTNyN091MUFGblBEbHExRkUyeG1ueDdWMXNnZTR0Sm9qd0pHTDhBSjl4dS85UHBZNUwrWnlZQVdBUWJJaExuRDkxY2F2dXRLZ042Y29PMWtVb1VsdWdqTjRVOXdYaHBScldjOU5TSitDbytWbW5qUUtDLzRQNzdibmtrL2tadVJBMUNtS01KcnFITFlCRTYiLCJtYWMiOiI4MmU4NzcwN2QyNmVkYWY0NTk2OWM1M2M1ZjEzMzk5MGYwNmM4NjQzNGE1ZWNhN2IxMWY3NWJhMGViMDE1ZGQyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNKb1N5SGIvWGxiV2sraTdab1JhUUE9PSIsInZhbHVlIjoiTFNZbTRCQnNNU2l2eG1FcCs0VkU3VmdRS3pwZFBvVHBKNG9QQ3g5dklQK2QrWG1BVFN3TWdWWEtvL1o3QXBDVkV3Mkp2UEpkZ2F4RmU3VGxhNklseGxUL1pqMjJBT0RjanpsZmJHRVBYbG5TaWw3czFESXdDV0Rtck9wNHpXLzl0em02UmpVVGNCcnVXSE9rRkc5NWlYRCtlNzdOU0MzUFBqcklyYkU2Q3ZNaFZyRUQ0RTJBVTZ0bHQ3bEpwdmxDVW91TitGakdWR3lGU3N5K09LdjNmVUNjKzlXNmpXZ0E2bXZaU2MyaUtRZEpKWmQzdTFubkxKWGdnakdKWWliRDEreGYrZDV3Q0g4amlrWG95N2pGaHphZ0NGcW9STEI0WU9heDJtaGttUGM0NnRxZGJjb01nZHI3SW40c01XWnhqSGFRdmlEeGNYK3dzejFheGp5WFpUQXYrUzZWcmEwRkhjOFo0aUxTTWdsZ3drOU5YSVJDSW9kR0dYcDg4Wkt5anBucm0rZzVCa2lrUjI1eXNDdVhmRXg2UFYrdDhYVlRlRU53MjJIOXNpMnI0cVhUdXkxMjJoY2dnZ1dJZmRTRTVCazJtUFByb1F5NXl6ajZnWDMwdUVtNVYzMlkvdDNPajEwQkhSSmE2OW9EYk9BQ1N3ZnFjRlVFQ0dZcElwRDciLCJtYWMiOiI1YzNlNmFiZDdjOTk2OTc0ZmMwZmE3ZDIyYWMwNmUwZmE1Mzk3NzFjNDYwMTViMDc3NDcwOWVjOTVhOWViNWMyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025520572\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-879028909 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879028909\", {\"maxDepth\":0})</script>\n"}}