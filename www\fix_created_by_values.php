<?php
// إصلاح قيم created_by لجعل البيانات تظهر للمستخدمين
echo "<h1>إصلاح قيم created_by</h1>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>1. فحص المستخدمين الحاليين</h2>";

try {
    $stmt = $pdo->query("SELECT id, name, email, created_by FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>Created By</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['name']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['created_by']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // تحديد المستخدم الأول كمرجع
    $firstUser = $users[0];
    $targetCreatedBy = $firstUser['created_by'] ?? $firstUser['id'];
    
    echo "<p><strong>سيتم تحديث البيانات للمستخدم:</strong> {$firstUser['name']} (ID: $targetCreatedBy)</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص المستخدمين: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>2. تحديث قيم created_by</h2>";

$tables = [
    'chart_of_account_types' => 'أنواع الحسابات',
    'chart_of_account_sub_types' => 'الأنواع الفرعية',
    'chart_of_accounts' => 'الحسابات'
];

foreach ($tables as $table => $name) {
    try {
        // فحص البيانات الحالية
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $currentCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($currentCount > 0) {
            // تحديث created_by
            $stmt = $pdo->prepare("UPDATE $table SET created_by = ? WHERE created_by != ?");
            $stmt->execute([$targetCreatedBy, $targetCreatedBy]);
            $updatedRows = $stmt->rowCount();
            
            echo "<p style='color: green;'>✓ $name: تم تحديث $updatedRows سجل (إجمالي: $currentCount)</p>";
        } else {
            echo "<p style='color: orange;'>⚠ $name: لا توجد بيانات للتحديث</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في تحديث $name: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>3. فحص النتائج بعد التحديث</h2>";

try {
    foreach ($tables as $table => $name) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM $table WHERE created_by = ?");
        $stmt->execute([$targetCreatedBy]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            echo "<p style='color: green;'>✓ $name للمستخدم $targetCreatedBy: $count سجل</p>";
        } else {
            echo "<p style='color: red;'>✗ $name للمستخدم $targetCreatedBy: لا توجد بيانات</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص النتائج: " . $e->getMessage() . "</p>";
}

echo "<h2>4. محاكاة منطق الكنترولر</h2>";

try {
    // محاكاة ما يحدث في ChartOfAccountController@index
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff;'>";
    echo "<h3>محاكاة الكنترولر للمستخدم $targetCreatedBy:</h3>";
    
    // الخطوة 1: جلب أنواع الحسابات
    $stmt = $pdo->prepare("SELECT * FROM chart_of_account_types WHERE created_by = ?");
    $stmt->execute([$targetCreatedBy]);
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>1. أنواع الحسابات الموجودة: " . count($types) . "</p>";
    
    if (count($types) > 0) {
        // الخطوة 2: جلب الحسابات
        $typeIds = array_column($types, 'id');
        $placeholders = str_repeat('?,', count($typeIds) - 1) . '?';
        $stmt = $pdo->prepare("SELECT * FROM chart_of_accounts WHERE type IN ($placeholders) AND created_by = ?");
        $stmt->execute(array_merge($typeIds, [$targetCreatedBy]));
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>2. الحسابات الموجودة: " . count($accounts) . "</p>";
        
        // الخطوة 3: تجميع الحسابات حسب النوع
        $chartAccounts = [];
        foreach ($types as $type) {
            $typeName = $type['name'];
            $typeAccounts = array_filter($accounts, function($account) use ($type) {
                return $account['type'] == $type['id'];
            });
            $chartAccounts[$typeName] = $typeAccounts;
            echo "<p>3. النوع '$typeName' يحتوي على " . count($typeAccounts) . " حساب</p>";
        }
        
        $totalAccounts = array_sum(array_map('count', $chartAccounts));
        
        if ($totalAccounts > 0) {
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✓ النتيجة: ستظهر البيانات في الصفحة!</p>";
            echo "<p style='color: green;'>إجمالي الحسابات التي ستظهر: $totalAccounts</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>✗ النتيجة: لن تظهر أي بيانات في الصفحة</p>";
        }
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ النتيجة: لا توجد أنواع حسابات، لن تظهر أي بيانات</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في المحاكاة: " . $e->getMessage() . "</p>";
}

echo "<h2>5. الخطوات التالية</h2>";

echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<h3>الآن يجب أن تعمل الصفحة!</h3>";
echo "<ol>";
echo "<li>تأكد من تسجيل الدخول إلى النظام</li>";
echo "<li>تأكد من أن لديك صلاحية 'manage chart of account'</li>";
echo "<li>اذهب إلى صفحة شجرة الحسابات</li>";
echo "</ol>";

echo "<p style='margin-top: 20px;'>";
echo "<a href='http://localhost/chart-of-account' target='_blank' style='background-color: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-size: 16px; font-weight: bold;'>🔗 اختبار صفحة شجرة الحسابات الآن</a>";
echo "</p>";
echo "</div>";

echo "<h2>6. إذا لم تعمل بعد</h2>";
echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<p><strong>إذا لم تظهر البيانات بعد، جرب:</strong></p>";
echo "<ol>";
echo "<li><a href='seed_chart_accounts_direct.php' style='color: #007bff;'>إعادة إنشاء البيانات الأساسية</a></li>";
echo "<li><a href='quick_chart_check.php' style='color: #007bff;'>فحص سريع للمشكلة</a></li>";
echo "<li>تحقق من تسجيل الدخول والصلاحيات</li>";
echo "</ol>";
echo "</div>";
?>
