{"__meta": {"id": "X1a66ebf237740a85d30ceb024424a5e9", "datetime": "2025-06-06 19:20:35", "utime": 1749237635.036905, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237633.415565, "end": 1749237635.036935, "duration": 1.6213700771331787, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1749237633.415565, "relative_start": 0, "end": **********.827178, "relative_end": **********.827178, "duration": 1.4116129875183105, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.827199, "relative_start": 1.4116339683532715, "end": 1749237635.036938, "relative_end": 2.86102294921875e-06, "duration": 0.20973896980285645, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44752936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02149, "accumulated_duration_str": "21.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.937808, "duration": 0.01864, "duration_str": "18.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.738}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.991665, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.738, "width_percent": 6.096}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749237635.011189, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.834, "width_percent": 7.166}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1558023590 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1558023590\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-393468023 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-393468023\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-595411175 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595411175\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-950663294 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237592661%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlWUUxsOE5scTFxbkVpTWhvQUhNdnc9PSIsInZhbHVlIjoiTHNMamhVaG4wVDdNY0N2cTYwelh4Rzd3WS80WDhuTndLUitxTEYyOUxsZjN1bTZLb056RmtxOUl4K1RweHV6aGxRekg0ODRLb3FGeVJzRktvQWxhaEtDTmR5M2FjWko3dXRZSnA1ZnlCZURoblUyZkNZMFE1cmhoSkpXRnFCQlNNcDd2R3Z0VEJVOGpCUVRvQVpZYjRKdUFMb05iS21mM3dEV05qRzJvbXVBZ0hhRzRyVFhLamN3L0o3c2lvR1k3QkJhNi8wN0FsRXRDalF2OWV3eXdwaUxjMTBuaFFGeEhFNWQ3SVRBN3BURUVqRjhYNFkyTEV3VVE4d0VlT1g3aHhJTXRDRTh1OGthWWs2Qm9Ja3g0cEFXN1JlT3FtbEZIZ2trcHgxa0h4cEd6YmFjYVMrTWNveHFpaFE2aUdZZEdhR0xzV09teWNuVjBkQnl2cGRGeHNkRzdYRDM3UWVlbkhiSWRsckxQejl5amF0c0VrallHelFCRFEyc2g1V1NvTWxLaTF5cDlqS25Ma1Y4VFlBUk1XREFvM1ZsVTdPeldaRkpoUWNHR3p5WmZHYjVjSmgrOG9ZOEZsbnhpenhiSGZ3TlE5QmkwVHhEcGxmTERyczFFN2JJbS9QYlRjUk83cGQ4SHlpOHV6amd0a0tDU01aZHdQanJmRWNlMVF3cnYiLCJtYWMiOiI2MGExMDlmNzI3Yzk4NmMwOGQzZmViMmEzNThiYTI4ZWMwYTFhNzI1N2Q3OGRhZGM2Mjc5MWUxOTFhNjUxYTVmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikk3VTJXQkpxMnZ2aDZReWdLM1F4Z0E9PSIsInZhbHVlIjoiMzJlRDI2aUxodUhERzF5a0sycFRDSFIyYlU3Z3lNdWRhVjg5czlPWDNIZVEveS9IYmwvSFhvbnFKemFqRndMVi80dHpJVjRnejRpQUdCSVhEZEdvSHFqVS9QQmxjd1hHWnpYa2ZieDhSSU1aVUV6R3U1NTVNSEtjVmlWWENKOE5MTzN2cWU5QlNjeldZK2VzUTY4Ynk3VnQ3OERHR3BibkRMZE81MEJkellhcUZ0TTFhb2RyMnN2NGRJZUNFbFZ2cGNDaGR6QXJlenJvV0lQK3VndHIvNUZ0YXFYQ3QxMWlMWFVVMFhxajZUWXdSVS9RM2JuYTJneHI5cE1JemlZOVFDL2lEVnVrTTdEekVHTThKMjNncjd4Njd0NnVCN1FnV2VSeGpkOGJYN0J5L0RseFZsQzFlVExRMlo1dDBHUVQ3SEZ6UGhYRm9jQlAxZXc2MlRYZjR4akhiSnJoTWZZK2ZITmdkaFRmVWtNTDFPcmVKcjFyZzZnM3hnY1BwZGl4L25ZeWpSMzVHMWhWdkZmcUgrR0owdG8ya0ZjeGxYZUt0OHFRQUZCNzdwTkdLKzVJdWd0QTBqT29yaHpTbnhRV1NON0VXRDk2eGdmd0JwREcrRzJHZndKelVidEpMZW9qTldra1dCWWhXQnhyblk5VmJwbXpQRDFFR0o5L0puWDQiLCJtYWMiOiIyMTBmMDdiZmFkMjM4YTRjOTg2NjI2Y2I4MzM4MjAyNGZjYmZlMDdjY2RkNmJiMDcwMGU2Y2EyZDIyNGVkMWNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-950663294\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1121713677 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1121713677\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-132468390 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:20:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpxYU1tTUlJMUQ5K0oxS0VWcXNXQ3c9PSIsInZhbHVlIjoiMGdqcGQ3QUM5L01jMjVNaUpzVHI0aUtOMzdpSCtvV0FKMjM1c1k5My9SR3hqaVBBL3g0bWNBcVFTZGdFSnlDa1ozUHA3czRjYXdNZzA3TnpEbW03UEpaS3F3K0xtUkJaTm9SZ2puZTlJUVVPai84bU1CcGd1cFk4YkdCTzhLbHRWZmJyQldKendnbTRmVW1qSzl6RXc1cS9vZkJMd2ZNOGtwdVJnTXhsMU5jNUk3ODNNNURHMmpaSlEya3p1TXdXTGlEejE5SWc2SXRjdFF6bkEwUmxDR0VncjZhYUZRcGx0YU04bGJCMzYzbkZWVjZnNG45SW91M28rYWE5U3pva1UyUWlkVFBza2t6bWd1YVZnd3pUZWw4WFJVWmRIcUxTdmF4U0FDNEk4WWEvNEpKZ0N1Wm82NWhVSjNtTFRqRVRISFBjOElJS3dDOUcvNHdUMWhtNzBTSnpQK0M4T0RJM2sraUQwUDZ5ejdIc3ZNNGp6Vyt0cjNsZzkySWlmd0piV2Nqazk2MldBOExWZWtVcDZ2ZXZ4SEszVXRZSC8wTTRzZ29NQ3hjbkVqeUdHZXpURWRFSStRV3hIZ2RtYXRZVk5kdFVtL2ZxQUlvRHlVS0VoUHNIWnYyR05OclJFYmwraUJwUTlGNGR1WGo5b3ZJNk5KSVBFeTNURnpxWG5yd2siLCJtYWMiOiI5YzgxNDM5OWI0MzBmZTk2ZGY1OWMyMTgyZDQwNzljMGRmNjM4MmRhY2Q5OTE0MDNiMDkxOTBjZDcxMWI5ZjUyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:20:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkgwVzYzSXYvQjRPa2xqS0NQVG9CK2c9PSIsInZhbHVlIjoiSkZVY1E1VlUxN3VWWGszdVhwZGMwRlJ5SFpFaTc4NVpBbVBOOTZKMlNUVWZhSjFxUytHY094RkRMcEtFTjNmejNQQlJOV2w3KzE0T2J1VFo2Vm95Z0lBaVpBM3lNVmhZL3ROT2JTUGg5bGcxTFpsdDVXcURZWjZWTEY2QkZQNkNFemtWVEpFN2ZsS1VQZzRzdTlDTC9DQkVQRWphVVJISGd0Y0dURkR5RzJGeHBsYjhvWjNLV1h5WVZ5YlgvMEM1cDIrTzBrWm1GaUNqMGwyS1VXVHZSelgwVTdsTTZjRGwwS3Q0VkN5Q0hDamtNZi9aRXNwT3lIcDF6RElCNk1VYmNDYldoSVFFL0pJOGF2d1A4ZndLMGtieWkxSzVEYk1wbDZKNDF0anlVNmdUUDl1SmhUY2ZyY3JhRU5jdENnTHgwY01XTDFDaHNSL1FLcjdsc0ZsbzRxZ0RYaDMxT1d0dmVjaFNndk5wZDl4ck4wNzRHVEZ6eElRamUvRW9MdVpoTCs3dnVDOGZIeS9RZjhVdjJ6dXhPdlRscGpQblR5cGhCWjdjYzNSOWY2SHUzQk5naWRaSlJRZnZ0ZnRVSmdkMi9ZNS94SkoxTmRuc2k4NHFqTTRGeUhFR2ZmNmNVL3FNODRQVUtjSnp1dGoveE80WmJKQVFKQWF4L0tuaFFRNzkiLCJtYWMiOiJjNGY3YzQ1YjdhMTVhNWQ5ZjhmMzRkZTgzMWIxMWIyZGZiNDgwNjNjMTc4MDg3YzNhNjk0MmRkYTNiYTM1MDEzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:20:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpxYU1tTUlJMUQ5K0oxS0VWcXNXQ3c9PSIsInZhbHVlIjoiMGdqcGQ3QUM5L01jMjVNaUpzVHI0aUtOMzdpSCtvV0FKMjM1c1k5My9SR3hqaVBBL3g0bWNBcVFTZGdFSnlDa1ozUHA3czRjYXdNZzA3TnpEbW03UEpaS3F3K0xtUkJaTm9SZ2puZTlJUVVPai84bU1CcGd1cFk4YkdCTzhLbHRWZmJyQldKendnbTRmVW1qSzl6RXc1cS9vZkJMd2ZNOGtwdVJnTXhsMU5jNUk3ODNNNURHMmpaSlEya3p1TXdXTGlEejE5SWc2SXRjdFF6bkEwUmxDR0VncjZhYUZRcGx0YU04bGJCMzYzbkZWVjZnNG45SW91M28rYWE5U3pva1UyUWlkVFBza2t6bWd1YVZnd3pUZWw4WFJVWmRIcUxTdmF4U0FDNEk4WWEvNEpKZ0N1Wm82NWhVSjNtTFRqRVRISFBjOElJS3dDOUcvNHdUMWhtNzBTSnpQK0M4T0RJM2sraUQwUDZ5ejdIc3ZNNGp6Vyt0cjNsZzkySWlmd0piV2Nqazk2MldBOExWZWtVcDZ2ZXZ4SEszVXRZSC8wTTRzZ29NQ3hjbkVqeUdHZXpURWRFSStRV3hIZ2RtYXRZVk5kdFVtL2ZxQUlvRHlVS0VoUHNIWnYyR05OclJFYmwraUJwUTlGNGR1WGo5b3ZJNk5KSVBFeTNURnpxWG5yd2siLCJtYWMiOiI5YzgxNDM5OWI0MzBmZTk2ZGY1OWMyMTgyZDQwNzljMGRmNjM4MmRhY2Q5OTE0MDNiMDkxOTBjZDcxMWI5ZjUyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:20:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkgwVzYzSXYvQjRPa2xqS0NQVG9CK2c9PSIsInZhbHVlIjoiSkZVY1E1VlUxN3VWWGszdVhwZGMwRlJ5SFpFaTc4NVpBbVBOOTZKMlNUVWZhSjFxUytHY094RkRMcEtFTjNmejNQQlJOV2w3KzE0T2J1VFo2Vm95Z0lBaVpBM3lNVmhZL3ROT2JTUGg5bGcxTFpsdDVXcURZWjZWTEY2QkZQNkNFemtWVEpFN2ZsS1VQZzRzdTlDTC9DQkVQRWphVVJISGd0Y0dURkR5RzJGeHBsYjhvWjNLV1h5WVZ5YlgvMEM1cDIrTzBrWm1GaUNqMGwyS1VXVHZSelgwVTdsTTZjRGwwS3Q0VkN5Q0hDamtNZi9aRXNwT3lIcDF6RElCNk1VYmNDYldoSVFFL0pJOGF2d1A4ZndLMGtieWkxSzVEYk1wbDZKNDF0anlVNmdUUDl1SmhUY2ZyY3JhRU5jdENnTHgwY01XTDFDaHNSL1FLcjdsc0ZsbzRxZ0RYaDMxT1d0dmVjaFNndk5wZDl4ck4wNzRHVEZ6eElRamUvRW9MdVpoTCs3dnVDOGZIeS9RZjhVdjJ6dXhPdlRscGpQblR5cGhCWjdjYzNSOWY2SHUzQk5naWRaSlJRZnZ0ZnRVSmdkMi9ZNS94SkoxTmRuc2k4NHFqTTRGeUhFR2ZmNmNVL3FNODRQVUtjSnp1dGoveE80WmJKQVFKQWF4L0tuaFFRNzkiLCJtYWMiOiJjNGY3YzQ1YjdhMTVhNWQ5ZjhmMzRkZTgzMWIxMWIyZGZiNDgwNjNjMTc4MDg3YzNhNjk0MmRkYTNiYTM1MDEzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:20:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132468390\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-814014519 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814014519\", {\"maxDepth\":0})</script>\n"}}