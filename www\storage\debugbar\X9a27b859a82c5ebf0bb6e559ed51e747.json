{"__meta": {"id": "X9a27b859a82c5ebf0bb6e559ed51e747", "datetime": "2025-06-07 04:34:02", "utime": 1749270842.005749, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270840.636298, "end": 1749270842.005791, "duration": 1.369493007659912, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1749270840.636298, "relative_start": 0, "end": **********.848375, "relative_end": **********.848375, "duration": 1.2120771408081055, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.848395, "relative_start": 1.21209716796875, "end": 1749270842.005794, "relative_end": 3.0994415283203125e-06, "duration": 0.15739893913269043, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44780160, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01712, "accumulated_duration_str": "17.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9252782, "duration": 0.01467, "duration_str": "14.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.689}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9642432, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.689, "width_percent": 6.893}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.981655, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.582, "width_percent": 7.418}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1185251302 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1185251302\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2012367870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2012367870\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1708287285 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708287285\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1228410673 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270822571%7C16%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhycnZOd0NCL1lBaTlRK1BiQXZuVlE9PSIsInZhbHVlIjoiRmVvdTVyUkJvQnQwZVM0bXJOdUh3ckU5MnIyTEh3ZG5uT0pwcGFCMytRd1Z3RnhlWnhua3U1dTFyWjFzZVhkVFUzUGdRZlc4V0RJK0h2SThzdFY2eW9FckpWRlBpTC83K1BCbGkxa2paNTBQdFMyWGlRMEJ5SlNnMksvdmt0Vy9scGROTTVOaExiME9jRlRyTU9vc2xndE9RRjlqcTF1Wm95eHdmOTFKN1NCVko1WVpZWDVvL09hQnRTMzVYanJMLy9lOVR3WU1aaEJwMnFycEczVHRXRVBxelZDMFh1R0R1OXNoRXMvUnZxTmcvdFkza2pMaGQ2R1FnazcrN2ZoVkZTL0hJUFhJanBUSjdhSyt4UCtheVY1SmNkcUFjaE9xL0NSS0wyRU1SQ3lxeDFzZFo4YU5DTDBrNTlrSEM2cXFxTmovMnBZN001d0IvUE1yenZlQkNVbVJQSnFzVzFjZFNWZWl0UklwY0U0UDA5aFhkRjkxL0h1V3FqR21ucndSa00yUStIYkdXcm5iL014U3pGSXBURlF2d2N2QyttTklmWG8vMlVjczF0RUZXRC81cWdicGRWcGFhS0lQSmZ3MThtalpmWFlackp6TXhyUGYrWVlRMnR1czdpMERZUUlxMHZuWFNLMjlVeHRDV2hmTDA4a0ZQdGFGdXB3K0x0a0giLCJtYWMiOiIxOGE1MTg5NGNmNjRjYjgwZDEzMjE2ODI2NGJiNzM1NjgzYTI0NDRhMzk2MzE5YTAyYTE5ZWRhMjVlZjM0NTE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InA5SlhOc1lXTzg3dlk3a3hQY2R1ZHc9PSIsInZhbHVlIjoiZDNDSkFtbUZkN3l5cW41RVVMSU1PV05CUzllVVFQZGI2eCtHR1dlZ1BKNGVFVmlKZFRWK1NrZ2wySjFleFdHM0ovZlprbUdSVHZiQlpJdlJQbHVuTnFWU0F6NGYyY0doSGFQcXYyZHVTRWNQcTQrOXVEalRKWmVDZHVwZjhra2Y2SHhRR0Q1a0Mwc1VXelMvbVJEN252TGp5TDFvYUpqaGNlS2Q3TjVTWWQwQTRXcDVGTjZTU1Z2WFg4NTFiU2VQajZjbFJIMDVONzFqQS9udThRcmM2MFRYcjVuZHN4bjRYSmNJSVVXTFFqbFNRTnl5UlBTclpPTytpMnFrUG5keVh2RlhLY29NdnZYMGpSV3hFWWxDSFdwYjNPNHdlRnRoZHd6Mys4cDBWOHhqYWVzM3NYSGxyTmcrU2dpZzJOTHFDWXZBMm5yWm4wUVZzMzk5b3VmaWxwb1JseDhBME5yTnN6VE5XT0hPNy9KdVdEMDZudjBiak1lTWx0aXdHU2MrekptbDNqcE5hcEExcFNCUDcvc2RJdUNncDcvblFzYnlQYUtNRzVKSzJFQVJJNGs0bUFSUWptZm40a3dDdjRQeENDUXpwNUUxUFl4VXpGMFovQmZWQ05ERDF1V1hoZzN2YWNlOFh2V3JEblR5Rm8xRlJXcFprZEpLNlJwWEZ3d1ciLCJtYWMiOiIxNTdhYzFjNDEyZDUyMTc5MThhODJjZTIwZjI3NzY1ZWEzMzIxZjYzMDUxMDgwNzViODU2YTQyOGNlODE5ODIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1228410673\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1724911259 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zyhWlMrUupyybJnMtPnBjJ716zpTnMkeCiqRAqWb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724911259\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-679482221 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:34:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllRSlZvc3A3UnBvVDM0Q1A5UUJWL2c9PSIsInZhbHVlIjoiS3Q2L0xoa2ZnV3BrK3lMZlNyN2FnMGZhanhYNVJZNXEyVEI0VzBIOUJsM2daajE0V2xMeVA1TDJQdnZkcnd6Nk5nZ2JzYlR1QzBhTHNLRzFWVFA5VWtpRzZIQndNUElHL0JpZUxuM3FibjltZXZDNkFSeXNOTXJ5T3hkLzVFa21yaDBISFJTRFZxdDZwUW1tbDM0NVp6dXcvcW9yUFZ0SDZ0bS9UdDROYWp0SklIVG1qS3dSSUdQZ2J2QnVMVUw0eDJIR3VNay9VaE5GYzU5MnQvWVBDTWxWRG9FZGNwMlB6NGl1bERoYVd2ZzFQMDgzSnVhYWJPNG5zbjVlaS9FRUxVNXAxMEhBdlppN3gvdzRVV2pPdlJQa05aUzRMa2ZmRG9MR3BVUHpIejVjTHI0b0pTM1dSQmgydmhoUjVXUHA5SDdTZDVzb1FiMTFRVmZ6bG0rc2pURFJVa3kxZG5JdENhMGpVUmV0Q1F6WW5jWHpVK05wTWFMK3NrNHVDU2dpYWlFVG92c1hnR3RpSiszc3pWWU9Wd3pnSUlJMWVyRVFGR2dhOCtJZUdwMVUzUlVyV1dPMmtKMjc0OGJzaklkcUFSa2dudlpLSHdUSmtqY0VCd1cvMG1hQmczVjZKWlhld2Q5T3NvSzJmZ2JnL3ptK3o1VGF4UHhpekVCTEhETWwiLCJtYWMiOiI3M2ZhNTQyMjA1NTMzMmMzOGVkNTFlZWVkNTU2MWMzZjMyNTlkZjk3MjlkNTFlYzU4YjYyMDRkZTBkMmJiYzJkIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:34:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVzOE9hRmJxQW0yN1JBWnhXbDczclE9PSIsInZhbHVlIjoieVlJYXNJTFRIRmlCdVZWUnJ0Wk5PbVFWV25Hc0FwMkVyZ211WThqSjF2aVJtYS9QL09QYklyc3VxbXI2ZEh3MEhvVjk3SmlEb1drZjhPYm5oS3E2cjQwOGhwemtQY2VMU202N3E2TGxhV2dncUNpZ2xCcVQ5c1FKTDdDNHFIajliaGtEM1AzNjNWUUx1YkFxK0puYXFld0ExZXBsMjNCK01PU2swWDhXTWRrS1k4MnFhY3hlSUdsSWJaUUlwZWgxSEdKdjd3VXVWczlQc3VzZ1FZRW9jekUxSjJFSWJDdmFQTERYdmhmRDhuQjVJb0Y0aldvZEYrcUdpQTdvK01wZ1pYdXF4TW9oSmJIOCtMbjhQQkhlNm5iYmxCYnprT3BYdlFsT2c0azdtMUdQOUpuT3JkUW9rOWlLVTBCdTFGWE0xWm1MVU1BRFYwYTYvNktxNnRTdmRucGxzY202aVhYYlVyU2JUTHFPVmRoWGk3OGRYaFN6R2RURk94bGtPc0NYdXUrOUQrMWxNMWJkdWNFS2VQbFp4RVJneEJEY0d3ZWV4SVNZWStTVWlqbEs3dkw5ajdMWExQZXoyaDF0L3F6WGFCV0JXVDgyWlNNd2FNelFDM0RmY1BROHFwZmx2dTVzWHBjOGttaUlwZlNMMlE0eStuUnhBenFoNFVIc0w4U1QiLCJtYWMiOiI5OTFkZWY3ODUxZDg0N2ZmOTZmZGM2MmQ3NzI5MzJmNDg5NGI5OGE5ZGEwYWY0M2ZiMDQ2YzVmNzY5N2NhMjUzIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:34:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllRSlZvc3A3UnBvVDM0Q1A5UUJWL2c9PSIsInZhbHVlIjoiS3Q2L0xoa2ZnV3BrK3lMZlNyN2FnMGZhanhYNVJZNXEyVEI0VzBIOUJsM2daajE0V2xMeVA1TDJQdnZkcnd6Nk5nZ2JzYlR1QzBhTHNLRzFWVFA5VWtpRzZIQndNUElHL0JpZUxuM3FibjltZXZDNkFSeXNOTXJ5T3hkLzVFa21yaDBISFJTRFZxdDZwUW1tbDM0NVp6dXcvcW9yUFZ0SDZ0bS9UdDROYWp0SklIVG1qS3dSSUdQZ2J2QnVMVUw0eDJIR3VNay9VaE5GYzU5MnQvWVBDTWxWRG9FZGNwMlB6NGl1bERoYVd2ZzFQMDgzSnVhYWJPNG5zbjVlaS9FRUxVNXAxMEhBdlppN3gvdzRVV2pPdlJQa05aUzRMa2ZmRG9MR3BVUHpIejVjTHI0b0pTM1dSQmgydmhoUjVXUHA5SDdTZDVzb1FiMTFRVmZ6bG0rc2pURFJVa3kxZG5JdENhMGpVUmV0Q1F6WW5jWHpVK05wTWFMK3NrNHVDU2dpYWlFVG92c1hnR3RpSiszc3pWWU9Wd3pnSUlJMWVyRVFGR2dhOCtJZUdwMVUzUlVyV1dPMmtKMjc0OGJzaklkcUFSa2dudlpLSHdUSmtqY0VCd1cvMG1hQmczVjZKWlhld2Q5T3NvSzJmZ2JnL3ptK3o1VGF4UHhpekVCTEhETWwiLCJtYWMiOiI3M2ZhNTQyMjA1NTMzMmMzOGVkNTFlZWVkNTU2MWMzZjMyNTlkZjk3MjlkNTFlYzU4YjYyMDRkZTBkMmJiYzJkIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:34:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVzOE9hRmJxQW0yN1JBWnhXbDczclE9PSIsInZhbHVlIjoieVlJYXNJTFRIRmlCdVZWUnJ0Wk5PbVFWV25Hc0FwMkVyZ211WThqSjF2aVJtYS9QL09QYklyc3VxbXI2ZEh3MEhvVjk3SmlEb1drZjhPYm5oS3E2cjQwOGhwemtQY2VMU202N3E2TGxhV2dncUNpZ2xCcVQ5c1FKTDdDNHFIajliaGtEM1AzNjNWUUx1YkFxK0puYXFld0ExZXBsMjNCK01PU2swWDhXTWRrS1k4MnFhY3hlSUdsSWJaUUlwZWgxSEdKdjd3VXVWczlQc3VzZ1FZRW9jekUxSjJFSWJDdmFQTERYdmhmRDhuQjVJb0Y0aldvZEYrcUdpQTdvK01wZ1pYdXF4TW9oSmJIOCtMbjhQQkhlNm5iYmxCYnprT3BYdlFsT2c0azdtMUdQOUpuT3JkUW9rOWlLVTBCdTFGWE0xWm1MVU1BRFYwYTYvNktxNnRTdmRucGxzY202aVhYYlVyU2JUTHFPVmRoWGk3OGRYaFN6R2RURk94bGtPc0NYdXUrOUQrMWxNMWJkdWNFS2VQbFp4RVJneEJEY0d3ZWV4SVNZWStTVWlqbEs3dkw5ajdMWExQZXoyaDF0L3F6WGFCV0JXVDgyWlNNd2FNelFDM0RmY1BROHFwZmx2dTVzWHBjOGttaUlwZlNMMlE0eStuUnhBenFoNFVIc0w4U1QiLCJtYWMiOiI5OTFkZWY3ODUxZDg0N2ZmOTZmZGM2MmQ3NzI5MzJmNDg5NGI5OGE5ZGEwYWY0M2ZiMDQ2YzVmNzY5N2NhMjUzIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:34:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679482221\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1316407405 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316407405\", {\"maxDepth\":0})</script>\n"}}