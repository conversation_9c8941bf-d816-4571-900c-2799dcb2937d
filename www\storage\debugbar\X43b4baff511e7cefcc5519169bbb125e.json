{"__meta": {"id": "X43b4baff511e7cefcc5519169bbb125e", "datetime": "2025-06-06 19:29:36", "utime": **********.550224, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238175.085771, "end": **********.550255, "duration": 1.4644839763641357, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749238175.085771, "relative_start": 0, "end": **********.348803, "relative_end": **********.348803, "duration": 1.2630319595336914, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.348824, "relative_start": 1.2630529403686523, "end": **********.550259, "relative_end": 4.0531158447265625e-06, "duration": 0.20143508911132812, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.020540000000000003, "accumulated_duration_str": "20.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.452354, "duration": 0.01705, "duration_str": "17.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.009}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4942389, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.009, "width_percent": 5.258}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5039759, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 88.267, "width_percent": 6.086}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.525012, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.352, "width_percent": 5.648}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1851503071 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1851503071\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1858798970 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1858798970\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1346867822 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346867822\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-508544673 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238140647%7C34%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxLQTd1ZXBLeUdGTnNldUg1ZjU1RUE9PSIsInZhbHVlIjoiOUROd0lhL3dZL3pRSTQ2MHo1dWRNKzRWV0t6YXZEZTZrNWxtMkg0cDJxenZTbHNrcHgxRFUyZnNTSFJOQW5UTGJjSjJuc2NiSmJuczcxVnl0b2N1MWIxVGI3TitNR1huNWJmalZXVUdLMHZ6eXdNdHFpV3FuZm53TDZaKzBSNUhYMEtnbVpIZjJ1Q1JVK2p6RE1ZR1BQZHBRRnhYM2t2VWxQM2gwaWVOcTh0SHp4TkI2WnVzZ1hKcGlYSXVnUmhRNjkxYTYvYkFDVEdwMWcxVU5WVW5CZFU1MGxubzhqYk5YRmRqdThKdW5KOWZtcUtUaDdxcVordmcrNGoyc3pLWGRPbEdkNHFZbXRrRXNjeStiUTZpL3hMTjVud2NHUm1BV2JVMGp0Umh3YmNqK001cVhzQWxnZ0R3UUNWdDc2bUg5VXFuWDFYVUN4L3lORUpqL3BmMEw4dVpFVWxJeVYvcHh3TXQydE5GdXd5MXBVQ1ZtcFR1SVZHRFI5OElKS3preGdRaDIyUUNPRGVldXlUUks3RUtsbmpxbGNaRTcyZzloQmVpa2dkU1lIcEc4OEZMY2FkOGRmYjlGM2NSbUJZMjQ1dGlYOHoyR1dNUGI3ME40Sk5GT29KY3U5TDFLVW9zaEJ3QzFBWmhueDRxTVluSEJ4SndyQWIyUFc0aTFMVWYiLCJtYWMiOiIwYjIyYWMyZmE2ZDdmZGIyOWUzODUxNzZkYjQzMjdhNWNkOTk1ODVlNjZlNWRlMWE1ZjA4MjQ3MzQ3ZDUzZmE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJJU1pQdzVLVWErNjNZTkNCY0hKWEE9PSIsInZhbHVlIjoiRHM3OEIzT1VDZDVObTQ1WHl3MHlZR2pRSEUrejl1WEhickhrSENoSUNPQXhTSWM3Sms5L1lBRDMvVE1IM1hhaCtoNkJSVG14WUtDQTNXU2JmbzFiUVA2RHZadEhPaTk1VEx0WUhDK1kvNHg4dXZYZUt3am9sTTVNVDJ0Zlo0Zm5sVTFOQXRWR0ZsZmhaekFJZEViL2gwTGJGc0xLK3RndVQ4aWFIYW54WnA2ajh4bFlOTG1LMHgvRmpucWRrbndtUURsVnhHUUl6VUJ1ZitRcFhZVHpkaklqa2tFMUl4RWx3c3RFOVFwempldTlMbmFzNHJ2b0ExK2JIRS9tYUZvYWFaUlZmRlBFSGYzTHZ6d25RbURTVEl6b3o4ZmJFbjJMeCtQbW1XbWhsaFZMaXhZV0NjSTZVNkFEdng4dVdzUGh4L2V4LzVDNjFjZHVXS25yWm5ORDlFU1hzWVNoNnFBS1RsSGVVNzhoQjRHZTVSaHVoNHkvL1prTW93YmFNVFd6SEl2aEZkVUJHa1pjYmdBMllpV21LTno5SmRUbC9ldUxyMTBmQWFHZnJPdlQ1dXlSbUc3Q3N3eHZUVndNRXV0Nm43bExlTnY0aUdCekt5NDJnVFFwRldvWmQwU1MvMzQ1Wm43RG5Ebmp4bUxLTEk2blF6ai9UUldjNVp1ZmpsV0oiLCJtYWMiOiJlNWYzMGE0NjI4ODRkNDY0OTg2ZTg3NzBkOGRiM2JiZDM1ZTQyMDFjMDdkMWExYjNhNmUyNDAyZTE1NmRmMTA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508544673\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-332005081 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332005081\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:29:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJxN1dhQm93WFN3KzdLWjg2dUtLV0E9PSIsInZhbHVlIjoiMElqcENiNStRMWZvckZtN2VGMktYMnZ6N1hNYnI2SjdXbHFEM01xZEVmenFYYm0yR1BCbmZseGorMWlDbHpJOFc3Z2N6SkxScFlLbDR3R1hpSkdxNTVjdDlNMEJHU1lRYVE5SHlnNWpOZFprRm5sZUlRa0R3YXNKQlZsNVUrSDhNUkNOd09aZGV6bW1jMjdHUkFabzIyeXIzUmZNb3VGREh0WDY1UGNLT2FhVWNVc3JYYURwOGltYWZid29EUU1CeGFDckZEai9rQkQ5QjVqUC9VTTN3Q29yQ0YvNE1RV1VyTXFtUmFNT0ZyUXYwc2l1dWVSTDVsejV0SloybGNJR0pWcmROZ2xYazJ5V0tpaWNCcnBnRnlGSjZtMWhBaWpFT29hRkVSN2Nib0xZUWZjMmdIR3ozVjNkRmpzbGJjek03TDF0bE5JVUp1Tm1ZYWhHdXRucnJkenZYL3hCSXZHTFVOQmQvdnNhRVlYcGVLbHRqWWMyY3htZ1RxMUJDVkU1M3UyYlNLQ01lb1E0a05WVDhhbUx6cVV3NWlnM1BGYWRxREFDdnhHZnk3QTAzTHBuSHYzcUwvOVRvWStzdmxFZkR3ZFU1akRPZTBlYjQ2UnZPRlN4U2N5c3p3anhkNUN1aEpYK1NhMjdoYUcwYnR4YVBWUnByaURuR0NBYlY5Y2giLCJtYWMiOiI1YTdlYjUwN2UyMzg5YjRmYjU3ZjM2MjIzMDAxMWRiOTQ4M2Q1NjY0OGE5MDI0MDdiNjljNGFhM2U2YTE0YmNmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:29:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5aSEgwa1REWUlZSWoycnlYelNwUlE9PSIsInZhbHVlIjoiOXlWaDJPZnV2Ui8zeHdsd0ZLTnR5Z09ZT0pINHVzVG9EYmtIdno3ZUdVaFlYZzdXak9IZ3hpM3NLYkxZTjc3VG9QTDVlWFlEa0g5QUYrYkp5NWdQUmo1VFlldFRyRHBNb2pMbXhvak9lU1NUYi9JdHZqQ0M1R1AvN3VmRE5kS1VKTklzcEV2TE1VWlJpdktYb2FVc3FvZTJtd2c4V1dsTHcyV1BaWldmMXlpNU1wS21WdlI1ZlhkVlB0Sno1cGhtOVZIRkFHNTAxMlMxM2VhOHRMajRLaHdRUEJ5R1d1TjU0aGxLeS9Oc0NYYkVvVEhwNjIzU1JyeE9NM2dPcHI4K01jWFZHUHhaZEJhL2paVXdacFB1bkZ5WHJQU2dNSFBGWlpURHJ5cXVOVjc4c2RoNHliVk5YZEJyMTJqdUp6WjJjZ3JhSFpudDg3RzQ3K0lDSlB4VHVKZFZhbndGQXRZRG0xQ201K3JpUXNHaXMvYnl1Q3pRU2cvTm1sRzFyb0VqMGJiL2lmUFJneCtqSHYwR1FVOFZ5Mk5DM0h3by8zMXlINlo2M3VEdlZOSHpER0o1bi9lTU5IVkpXelptV082cXRmK2ZYVEREbzJhN0ZTMlU0bitEdS9jeFBHYWZXd201eFgvMXhUWUR4aGxkQVptS1lMMDYyVUg4SWJ3RW1MVUwiLCJtYWMiOiJjODczYjNhOTg3NzcxOTBmODhjMTg1Y2EzNWI0ZThiNTY0M2YxN2EzZDc0MTg1MGRiMGVkNTllNTFiNzI4NDcwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:29:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJxN1dhQm93WFN3KzdLWjg2dUtLV0E9PSIsInZhbHVlIjoiMElqcENiNStRMWZvckZtN2VGMktYMnZ6N1hNYnI2SjdXbHFEM01xZEVmenFYYm0yR1BCbmZseGorMWlDbHpJOFc3Z2N6SkxScFlLbDR3R1hpSkdxNTVjdDlNMEJHU1lRYVE5SHlnNWpOZFprRm5sZUlRa0R3YXNKQlZsNVUrSDhNUkNOd09aZGV6bW1jMjdHUkFabzIyeXIzUmZNb3VGREh0WDY1UGNLT2FhVWNVc3JYYURwOGltYWZid29EUU1CeGFDckZEai9rQkQ5QjVqUC9VTTN3Q29yQ0YvNE1RV1VyTXFtUmFNT0ZyUXYwc2l1dWVSTDVsejV0SloybGNJR0pWcmROZ2xYazJ5V0tpaWNCcnBnRnlGSjZtMWhBaWpFT29hRkVSN2Nib0xZUWZjMmdIR3ozVjNkRmpzbGJjek03TDF0bE5JVUp1Tm1ZYWhHdXRucnJkenZYL3hCSXZHTFVOQmQvdnNhRVlYcGVLbHRqWWMyY3htZ1RxMUJDVkU1M3UyYlNLQ01lb1E0a05WVDhhbUx6cVV3NWlnM1BGYWRxREFDdnhHZnk3QTAzTHBuSHYzcUwvOVRvWStzdmxFZkR3ZFU1akRPZTBlYjQ2UnZPRlN4U2N5c3p3anhkNUN1aEpYK1NhMjdoYUcwYnR4YVBWUnByaURuR0NBYlY5Y2giLCJtYWMiOiI1YTdlYjUwN2UyMzg5YjRmYjU3ZjM2MjIzMDAxMWRiOTQ4M2Q1NjY0OGE5MDI0MDdiNjljNGFhM2U2YTE0YmNmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:29:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5aSEgwa1REWUlZSWoycnlYelNwUlE9PSIsInZhbHVlIjoiOXlWaDJPZnV2Ui8zeHdsd0ZLTnR5Z09ZT0pINHVzVG9EYmtIdno3ZUdVaFlYZzdXak9IZ3hpM3NLYkxZTjc3VG9QTDVlWFlEa0g5QUYrYkp5NWdQUmo1VFlldFRyRHBNb2pMbXhvak9lU1NUYi9JdHZqQ0M1R1AvN3VmRE5kS1VKTklzcEV2TE1VWlJpdktYb2FVc3FvZTJtd2c4V1dsTHcyV1BaWldmMXlpNU1wS21WdlI1ZlhkVlB0Sno1cGhtOVZIRkFHNTAxMlMxM2VhOHRMajRLaHdRUEJ5R1d1TjU0aGxLeS9Oc0NYYkVvVEhwNjIzU1JyeE9NM2dPcHI4K01jWFZHUHhaZEJhL2paVXdacFB1bkZ5WHJQU2dNSFBGWlpURHJ5cXVOVjc4c2RoNHliVk5YZEJyMTJqdUp6WjJjZ3JhSFpudDg3RzQ3K0lDSlB4VHVKZFZhbndGQXRZRG0xQ201K3JpUXNHaXMvYnl1Q3pRU2cvTm1sRzFyb0VqMGJiL2lmUFJneCtqSHYwR1FVOFZ5Mk5DM0h3by8zMXlINlo2M3VEdlZOSHpER0o1bi9lTU5IVkpXelptV082cXRmK2ZYVEREbzJhN0ZTMlU0bitEdS9jeFBHYWZXd201eFgvMXhUWUR4aGxkQVptS1lMMDYyVUg4SWJ3RW1MVUwiLCJtYWMiOiJjODczYjNhOTg3NzcxOTBmODhjMTg1Y2EzNWI0ZThiNTY0M2YxN2EzZDc0MTg1MGRiMGVkNTllNTFiNzI4NDcwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:29:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}