{"__meta": {"id": "X12ed1f36d4d05c0640d19e7dd5ad8fac", "datetime": "2025-06-06 20:34:37", "utime": **********.27207, "method": "GET", "uri": "/users/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242075.55107, "end": **********.272104, "duration": 1.721034049987793, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1749242075.55107, "relative_start": 0, "end": 1749242076.944728, "relative_end": 1749242076.944728, "duration": 1.393657922744751, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749242076.944754, "relative_start": 1.393683910369873, "end": **********.272107, "relative_end": 2.86102294921875e-06, "duration": 0.32735300064086914, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51707920, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x user.create", "param_count": null, "params": [], "start": **********.243817, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/user/create.blade.phpuser.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fuser%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.create"}, {"name": "3x components.required", "param_count": null, "params": [], "start": **********.260553, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.required"}]}, "route": {"uri": "GET users/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.create", "controller": "App\\Http\\Controllers\\UserController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=51\" onclick=\"\">app/Http/Controllers/UserController.php:51-62</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.012250000000000002, "accumulated_duration_str": "12.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.056923, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 43.102}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.095783, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 43.102, "width_percent": 9.306}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.128839, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 52.408, "width_percent": 15.51}, {"sql": "select * from `custom_fields` where `created_by` = 1 and `module` = 'user'", "type": "query", "params": [], "bindings": ["1", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.140429, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "UserController.php:54", "source": "app/Http/Controllers/UserController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=54", "ajax": false, "filename": "UserController.php", "line": "54"}, "connection": "ty", "start_percent": 67.918, "width_percent": 6.531}, {"sql": "select * from `roles` where `created_by` = 1 and `name` != 'client'", "type": "query", "params": [], "bindings": ["1", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.150372, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "UserController.php:56", "source": "app/Http/Controllers/UserController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=56", "ajax": false, "filename": "UserController.php", "line": "56"}, "connection": "ty", "start_percent": 74.449, "width_percent": 7.673}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.201165, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 82.122, "width_percent": 8.898}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.2082999, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.02, "width_percent": 8.98}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create user, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2096392592 data-indent-pad=\"  \"><span class=sf-dump-note>create user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096392592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.219668, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1"}, "request": {"path_info": "/users/create", "status_code": "<pre class=sf-dump id=sf-dump-253397056 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-253397056\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-939196076 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-939196076\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1835823529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1835823529\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1552253400 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242072398%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik01OW1SSUJmTGVYWlU3cmcrNVUrRmc9PSIsInZhbHVlIjoiSFpMN1dLbjQ5eUxKTmcxajh3cEtWRWIxUE5laTlvYWFvRkg5RXRlbk54eDYwcGdqa1Y5OFlIOVcwTFFFMEZHNGNPRnpHZCtTTVU3YjN2ZDZ1K2Y3a3JJWHIydVhhMWlhLzhIdHR3TGVtcDRlMEJUVnRJUVJPU00rU2JWSXNDenRzSVduTlVyQ1FuenBJTFE1d3JWYlhmNGJ6RHlFUFFZb3JVakFtSGlqRlZIVXRRV2VnZ0c0K29rdHkyM3FtNlg1MFY3MVFDRzlIME0xOE5IbWJHUlc1MXVTVU4rWkFHU2puaU5PN1Q3NWhMRDVMaS9EeGJxZHpCeFREcVlVTHM3Nkd5aTEwQUlKNzhwTU5jOUcxZ0VsWHVVTkRkZ3lzUDlnRjBnOGJUWnVnVXNpSGNTK2o2U0RUak5JcUJpaEpwZVVIblFvbldjbnVNY01yY2xGM1FseC9MWVA0TG1CdGFma0FKMHRxQzdHZlI0UXRRakZvMFpzN0xqRUhoZXE4Y3gzNVBZeGlMeWtQQy94QytsY25PSm5adjcwVlR6SVNxczZoUG02U051eHlMZEZJR1d0bHZhVlJsaDBzWjgvZmVkb0lKMnpRM1ZWSGhXNmhiMFdWUnBiazVnZlVUSTVsNlArRzZNSWpHai83czZtWU41dEhLNUNtejdtOFUxa3RsUnciLCJtYWMiOiI5YzY1NmI0NTEyMzczMzIzYjI1YzdiMWJiMDBjNGUyYzliNzg1NTlhYWQ4NjAzNzQyMGI2ZTM1OGI5MWNiMmVkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRnWnppZVNuNEp2ODJFb1R1RGNOaFE9PSIsInZhbHVlIjoiZXhhdWxjNnFwck82MGQ1ZzVSby93VnVIYTdlT1pKTS9OSkhYcDEydEhTZTV4RUF0V1FVd2djVW52bWFGVGJURWdsbCs3bTUyZ3d5Q2tZZ0w0RWEzdVU4ZmRVekViRk14Vy9RZWhGbC9iNVcxVkpRK2pJZVozVEV4bGhNUHJtS2dtNHE2R3QxMHlGNUJqNS9Ma2JhQzVxbGJ0YnpvVzlrN2t0N2VIZjBjL1oyaTFpK0V4SFpVRjFTdXByc0h3NmxVYTU1cXdEeXBrZVM4b1ZwR1IzWStCUXJiMDBOQTE5dXAzcjY5SC8zSDNrcnh2VzlzcXFEVzFGMURBcGpwcTl1dFpPZnFKV0J4ODVTVXV5Ylc1RVRkajBCMG9xd3NNZ09KMk05RDhHQ0NrTWthYmlLVFFPOU1UU0FETjY1KzViM1Q0WGp3aE5RMHdqenJCODV0MVhoM3FHd1VIWXNxWkFMS1NUVi9BTStTb250VHZvRzhPSUUyUjhJSm4zLzZJVEVWUGZtRFNnbXR6bUZybEVaRmdjNU9CTUxwVGZTeTJucHYwVThJMkN0MnlBQ24wZWR5Q3hlMzlOeHBnb25BTFU3dnV4RkpKRm0vNDdBZ2Jhc1JkMGgzT0Rhcm1UUGtxSzdYVmdwYWErR0lBRFI5eGJVSzN4eTF3eStDZitCaklKT2IiLCJtYWMiOiIzMTAwZTAzZTJkNTUwMmZiZWNmODAwZGZmNTg4ODM4Mjc4NTEyNWNhODljN2EyNGVlYmVjMDhmNWQxZDY1M2E4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552253400\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2096648659 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096648659\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1108152124 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRoRkhrN2RuWDVhZTAwc293eGlCcWc9PSIsInZhbHVlIjoiRmhQUnBPK2dyK2NzUVZoSlRsZDlHdHpTS3VQd3h5d0JlYmExaXNvZmFXMWY2TlBWOFE4K084OWkvKzRTOGVGeE1rSW5LcE1vNkhIKzl3MVhMdEFtUnRnK2l4TVBybGFPbUZpQWFDM0NJL2htbERXR1ltdHoxOWdtNjU4MFJZakFiODZOSzhpdklTL3Yrb3pJQlI4Mkhnb0t6TFptRW9NcVNTakFpZG1ER2NRcjkvVE1kc2dlWS96dGNGOU5tUktZNEdJNnZwNFQ2TkdxTDA0RE5wU2k5UWVGbWw0eHdTTVN5QkZjcDJRc1orZGJvYWU5UGR4aXRPdFlTdTdtaHkwRFBvODBNMC9IaHlWV1M2ZGpGaUtmM3BuczM0dE9kalBGb1lta1hGdHFhV0k2QzY4bE9LbDVKVXlZd3JOdWV4ZXhYTWFENFJSdU81TFN6UmJVQ2F6NUZvSi95ZVhNekQwTUN2a3hiVllGUXNlbVd4OGV6ZklUa3pSMXhiSi9iSVBnYzl4Z1l6N2FtRnVlTERmOTJmZm43aEVJTEFhY3NrVmFvaGhiV0dvOUsvRkpNYi83a3RTNkc4cEZBS3pCOHlOMXNPMWw1TmpSdTBMZ1NlQW90U2FLc24vTWVKOHpmR2MzeFZlaEZ1cnl1MGtxRHhMaGlhU3puWHkyZzNxdEkwbTMiLCJtYWMiOiIzN2RlNmRhODY5MTY5NTg0ZmQ0NTNhM2JlOWQ1NjQyMGRlOGIyNTllOWI5NjNkM2ViNDRiYmI5NWJjNWE1Y2VhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJwdVlWa1dRYkozaVFhTVZFTjRvYXc9PSIsInZhbHVlIjoiT0VVSVNla0VGbnBkQUtIZ0VKNlVJVWtZL1pXc1lDWVdxTlZNSTAraVlyei9ZeTNUeU1BYTZoVFJiTHZlNjNoMzFEZERMSVlPUU9VaHJOMUsxVTV1eVhybVZwc2QwbUVnUWRRUVh0UDRQREhuMG1XOWRCMWh3cUdZTlFWcTJRa0J4MkpMcWJKSi9yRmhuc3pSZ0F3RDVwck5zVVBDK0h3U2wrOGtlVWM3ZmpUNkZTTWJVa1F2Z29NOEhZNUVoNHJjL3hydzdKZ29tTHVQbEExWFlYTkNnNFFyZFZtTkRrWFdGR0J6VnlLZHpDWngzSS9uWEF1eHMrLytIb1pxdGRTRnk2ZUFZa2s3ZnJIMHpRVnhVdnNEekE3cTRwOUwzNkxVWVVvTFpmSnBXT0N2RWpBK0ptSGN5N2xsYkRDb0REQWIybWxJM0szVWZ4cWZjMzM2dXdiSHYzMTdjWkVYUE85RC9JcnY2RW1nUWZFdXlGT241V21qK1F0emRyVXJ0RUg0ODdSNmhNSFoxQ0llaEJNdGRyNDMralZQZXNmU2F3RGNiUTY4YkpZaEJ4YkVkVndENmI5VmZaa2FsTE9MRkZMTkJuRUFBZFBrNm14RjlXTEZHL3BQSzlNMThUYndOWXZPeWhhRDhLQlBOTzBTZXIzRVBNQXlXRmQrOEZpeVpKc3oiLCJtYWMiOiIzZWJkYWMzODkzMzA3NGQzZTdhNjBkMWRhY2VkZjAxNWMxYWQ0NTNjYmExMDQyOTNhYWQxODgzNjg0OTY4OGUzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRoRkhrN2RuWDVhZTAwc293eGlCcWc9PSIsInZhbHVlIjoiRmhQUnBPK2dyK2NzUVZoSlRsZDlHdHpTS3VQd3h5d0JlYmExaXNvZmFXMWY2TlBWOFE4K084OWkvKzRTOGVGeE1rSW5LcE1vNkhIKzl3MVhMdEFtUnRnK2l4TVBybGFPbUZpQWFDM0NJL2htbERXR1ltdHoxOWdtNjU4MFJZakFiODZOSzhpdklTL3Yrb3pJQlI4Mkhnb0t6TFptRW9NcVNTakFpZG1ER2NRcjkvVE1kc2dlWS96dGNGOU5tUktZNEdJNnZwNFQ2TkdxTDA0RE5wU2k5UWVGbWw0eHdTTVN5QkZjcDJRc1orZGJvYWU5UGR4aXRPdFlTdTdtaHkwRFBvODBNMC9IaHlWV1M2ZGpGaUtmM3BuczM0dE9kalBGb1lta1hGdHFhV0k2QzY4bE9LbDVKVXlZd3JOdWV4ZXhYTWFENFJSdU81TFN6UmJVQ2F6NUZvSi95ZVhNekQwTUN2a3hiVllGUXNlbVd4OGV6ZklUa3pSMXhiSi9iSVBnYzl4Z1l6N2FtRnVlTERmOTJmZm43aEVJTEFhY3NrVmFvaGhiV0dvOUsvRkpNYi83a3RTNkc4cEZBS3pCOHlOMXNPMWw1TmpSdTBMZ1NlQW90U2FLc24vTWVKOHpmR2MzeFZlaEZ1cnl1MGtxRHhMaGlhU3puWHkyZzNxdEkwbTMiLCJtYWMiOiIzN2RlNmRhODY5MTY5NTg0ZmQ0NTNhM2JlOWQ1NjQyMGRlOGIyNTllOWI5NjNkM2ViNDRiYmI5NWJjNWE1Y2VhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJwdVlWa1dRYkozaVFhTVZFTjRvYXc9PSIsInZhbHVlIjoiT0VVSVNla0VGbnBkQUtIZ0VKNlVJVWtZL1pXc1lDWVdxTlZNSTAraVlyei9ZeTNUeU1BYTZoVFJiTHZlNjNoMzFEZERMSVlPUU9VaHJOMUsxVTV1eVhybVZwc2QwbUVnUWRRUVh0UDRQREhuMG1XOWRCMWh3cUdZTlFWcTJRa0J4MkpMcWJKSi9yRmhuc3pSZ0F3RDVwck5zVVBDK0h3U2wrOGtlVWM3ZmpUNkZTTWJVa1F2Z29NOEhZNUVoNHJjL3hydzdKZ29tTHVQbEExWFlYTkNnNFFyZFZtTkRrWFdGR0J6VnlLZHpDWngzSS9uWEF1eHMrLytIb1pxdGRTRnk2ZUFZa2s3ZnJIMHpRVnhVdnNEekE3cTRwOUwzNkxVWVVvTFpmSnBXT0N2RWpBK0ptSGN5N2xsYkRDb0REQWIybWxJM0szVWZ4cWZjMzM2dXdiSHYzMTdjWkVYUE85RC9JcnY2RW1nUWZFdXlGT241V21qK1F0emRyVXJ0RUg0ODdSNmhNSFoxQ0llaEJNdGRyNDMralZQZXNmU2F3RGNiUTY4YkpZaEJ4YkVkVndENmI5VmZaa2FsTE9MRkZMTkJuRUFBZFBrNm14RjlXTEZHL3BQSzlNMThUYndOWXZPeWhhRDhLQlBOTzBTZXIzRVBNQXlXRmQrOEZpeVpKc3oiLCJtYWMiOiIzZWJkYWMzODkzMzA3NGQzZTdhNjBkMWRhY2VkZjAxNWMxYWQ0NTNjYmExMDQyOTNhYWQxODgzNjg0OTY4OGUzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108152124\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-399742995 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399742995\", {\"maxDepth\":0})</script>\n"}}