<?php
/**
 * ملف اختبار سريع لـ AJAX
 * 
 * ضع هذا الملف في public/ واستخدمه لاختبار AJAX
 * http://127.0.0.1:8000/test_ajax.php
 */

// إعداد CORS للاختبار
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-CSRF-TOKEN');
header('Content-Type: application/json');

// التعامل مع OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// اختبار بسيط
$response = [
    'success' => true,
    'message' => 'AJAX يعمل بشكل صحيح!',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'data' => $_REQUEST
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
