{"__meta": {"id": "Xa2776b5d7cc80cabc70bbfdd21049493", "datetime": "2025-06-07 07:30:15", "utime": **********.333745, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.401349, "end": **********.333776, "duration": 0.***************, "duration_str": "932ms", "measures": [{"label": "Booting", "start": **********.401349, "relative_start": 0, "end": **********.194807, "relative_end": **********.194807, "duration": 0.****************, "duration_str": "793ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.194823, "relative_start": 0.****************, "end": **********.33378, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018430000000000002, "accumulated_duration_str": "18.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.259279, "duration": 0.01567, "duration_str": "15.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.024}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.293958, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.024, "width_percent": 6.837}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3168082, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.861, "width_percent": 8.139}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281412786%7C4%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjY3cEI4UURCQXhOZ2VqRWtXTlRjSWc9PSIsInZhbHVlIjoiR0xiUTF5WTl5bVRlSk1pSUNYZTFTcUlSQll1NUtXM0JxckxXMkhmM3FOdzlOZGFiclE4L1EzcDNWdStWZ09YNmdJNzhjQ2FlMFFFOHA4V2VBYlE0elJ3L252NEZKUEoyKzVHMlNua3FOQmVPQldoako1NUhtNDZteit0U2ZQSXkvKytNT1RybnFKSTFTRnkzUVpQY0Fja3NIZ3BzZkF5aXczNXE5cVVrYnJnWStOdFhQYU9DSC9xWGJzd09xNEdkYW5UTTFwNmZ3MjBybUtMNE5mcUs3cmtPRHdManpOdmRZRHh0cWMvRlFLRVFaRkhHZFh3bDlMSkFmRnVyYVp1dVRFd0NrZDBYT0VGUzRERGluaGFKVHpRSXJjREdUK2FkOWNzRU5nZzlwQTg5c21YenptcVJZdjh1Ym1UTGcyN2dDMFZxMEh4NjE0U3I1U3lyaW9CUW5pRG9RaWZ6cjFncFBoQ1VEMkppazVHSVBRSExkOWl4NUVXVHkzRnBFcXZ4QXlYTDR5NFMvNHlXaUR3Wjg2SG9SR2l0OHgyZVhOOE1Vb2N3MU5QeGJOSjFTWERTWVUxQnh6ZEhVY20zNjFPK2kxVW5PSzdpNjZoOW9WNDlXOEg1RVdWMEh6YVhBVjBPaWluUndSQzAwcHBzSmFseXRpRGRkamd3UVFkWkdXQTIiLCJtYWMiOiJhNzUwN2VkYjUxOTZhOTFmY2ZjNjcwMTkyY2NiOGUyZTk5MjZkZThmMmM4NWZmNmJjYzUwMzRkMDQyMzgxZjZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imd4dVl0L0UrVG5TdUwrUHpLa05BMFE9PSIsInZhbHVlIjoib2FhRDlySXdGOXBKc01Pbnh1UlFPV1NLbFJKRXFJZ1RvYkxlQjlEMlBIdGIvMTUxcG05cnlGN2F1U0dTQ09lMVA1TUlBenpuNmorY3pPVUJoNE9NbU9LTEVHTlIvSHFDdzZYYTFoY3B6R3cyVWF4OERxeDJ3a25Kb013OUxPWEhWbW82cDV6RUthU1pKemFPelZIblcwYTNjMTdZOWRyNGJ5c2Q4bEtSUnkyN0todnY2YytITUFkeFRJdjhGVGx0OCtLMHFFYXBOcnhLY2ttZkFoa2V6V3FySTlvak9PTkVoUzRjVFhLL0ppT0xWbDR1Z1BRVWQ2MzNySS92SGFOVHg3MFdGcThzdGhvdWpBYjlFR2JKTFh0QUdMcnpzbGgrMmVsUC9qdmRSLzVSS2srWmJhNTczNlAxOWtpbDFkOGxNMVJBU25LbUxwUTFtRUhDOTlsU3ZRUi9CcWp3Y1FSWUlWVEVsd251Qnl2clZGcmdmT3NjUVJ0R29DNWxHemdQY3JKSTVnSlRLRktFUWduZGdhNGRFY0JxM1YvUzcyQ2ZXUk4wN3pyTmJRL2JFdjU3QlBRRVZsaHNwcWt1a3VZVjlIY0pMd1ZrMTBTK2x6cmNVYUt0eExEWFlGNlVDU1kvVm1KaThSSVkrWHFqM2t4d29qMmlYVnBCcjNLbDcwUWoiLCJtYWMiOiJhM2UwOGFiZGZjOWY0NTdlYzc5ZGUzYmQ1Y2E0Mjk1OWMzN2VkZGRhMDNmZTRmMDYyMzA3MmM3NmIwNjQyNTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-354484191 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YNJBgXOqtFU4b8z8Vx4XcGRnEfxLj9OGGtpNz79H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354484191\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-367635170 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllnR0dCc05iVnFDb1Z6MkgxY0VkTnc9PSIsInZhbHVlIjoiNnl3NXpEY2ZkSStsWU9IUTErM29lYVNGaWczSjdKb09XeU5lYk82VVlPWHpFQlRqZVJFUlAzY2lsWHp3aGVsaHEyNTNyMXhEV2xzSCsvc2hQTUUrTFpCZXJDa0EvVi9TUVprdFJIdmF5aUtrM2JWM2FjVVFhU3MwVlQraHN2ZjZUMXMzc1dkUnYwSkFtSmpsWG91NmtqNWFMbWpYd3lrc3RIT002alV0M2hsRStoWU5XQ0RhRG9PbUhxTGNFRnVNQStmUUt6bHJzc3FkYi83ekkrU2dodVE5UkMxc0tOMERXbmhYZmplTUN5bVFsdGMzcmxlRWRSVU4ydTJtaTliM2dJK3I5OEwwRitSTTdSQkcyVldJaVRhb3RjT0VSM2oycjhlRkJtdzExTVJHZW4xYjZUc0doZXQvQmtHbklRMDR1VWVrK3FwQXFkdWxESlNNeUlPOElqVU5XQjhIdkhRNzRqa2lCWldvQVdaRXVCWmlVeG1zVWN6MjdtTVJMODd6NGtaZTNQOVhRK2dqekhIL2h0U2dnOVFwM2tWTW9hRzRqakZCYUpYdlZRZWQwMjNaY3VSaW5xUmsvU09UQWp4RDIwTTR0VWtEZy8xSmVrVTQreDhXQ3RyOGtXeUREWk9zSms2S1Q1TEV0MGNYRnpmbjArdWdPa0puYUxnUGkrZ1ciLCJtYWMiOiIxNzgzZmE0NzYyY2JjZGUzNzk4Njc1MjUxZWI4Yjc0MmJjMTE5M2I0MDdkMmQzYWQzODg0N2NiMTczZjRjOTI5IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxkNUx0d2JhdzlkejVBYmRrVWNoNkE9PSIsInZhbHVlIjoiUW04RWJxWWlvTFp2MERIck9CSGpNT1BQVzJadU9oZkczaloya2VxOS9UMzdWcWxoS1RFME5mS0gxQUZHS2hQdG9CY3ZoSG9pczlUVmJJR003TzdHMnI3LzdNeUhjbWVMdjhqQi9FVlhVTmxySUVWWjQ1dkE2MjNTU3RZYWdqcjIrRE55NldxbjV5Rng4MFltZUE0WFdDU2dsRlFuRXBnRGFLUjdYUmVFcHJ4aTQ4YytjblN4ZUhxRHV3TXRYS1p2cy9FbHRSTGZNdVZ1cElEZmxUYVc2UWZLWmhLcllaR0dDSTRQNlFkOVJWYnVkQkVybXJudlRCUC8xajVvWUovTkxPWVBLWUJEZHJQZG5rZ25tZGRkazI1dzZaeGx0NXFGbnlZWmoxNHRUeitzd3MzZ3cvVXJjVUM5K0o4RnV4dVNOdXpUc3JqVjhQUnQza2JpTU93OWNHakR2eGxDZHJhU09UNDhLcnhaYmN4VlU4cDI0K1pYVGIyWHhlKzBEUmxLN1hIZFpGTFhzQjBubGhmR2lsR2ozTWswL0grYjNOL2dHWDVYV2w0ZzQ0b2E0UGNYY1dndDdpdWZOTE5VeUlvajVJZzJzMDE4S0d5YU5oMmRzQXM0cjg2cUxhQ2V3VnpXb2YxcU1nb3h5SWdya2s4RThnSW84b05sSGVubkEvUHgiLCJtYWMiOiI2OGZhOTliODczYzkzN2U3ZjgyMjMwMTc2NTkwMWJjMGUwZTU4MGE0NjQ0ODQwNjg5N2ZlYzVlOTVjMGE3NDQ3IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllnR0dCc05iVnFDb1Z6MkgxY0VkTnc9PSIsInZhbHVlIjoiNnl3NXpEY2ZkSStsWU9IUTErM29lYVNGaWczSjdKb09XeU5lYk82VVlPWHpFQlRqZVJFUlAzY2lsWHp3aGVsaHEyNTNyMXhEV2xzSCsvc2hQTUUrTFpCZXJDa0EvVi9TUVprdFJIdmF5aUtrM2JWM2FjVVFhU3MwVlQraHN2ZjZUMXMzc1dkUnYwSkFtSmpsWG91NmtqNWFMbWpYd3lrc3RIT002alV0M2hsRStoWU5XQ0RhRG9PbUhxTGNFRnVNQStmUUt6bHJzc3FkYi83ekkrU2dodVE5UkMxc0tOMERXbmhYZmplTUN5bVFsdGMzcmxlRWRSVU4ydTJtaTliM2dJK3I5OEwwRitSTTdSQkcyVldJaVRhb3RjT0VSM2oycjhlRkJtdzExTVJHZW4xYjZUc0doZXQvQmtHbklRMDR1VWVrK3FwQXFkdWxESlNNeUlPOElqVU5XQjhIdkhRNzRqa2lCWldvQVdaRXVCWmlVeG1zVWN6MjdtTVJMODd6NGtaZTNQOVhRK2dqekhIL2h0U2dnOVFwM2tWTW9hRzRqakZCYUpYdlZRZWQwMjNaY3VSaW5xUmsvU09UQWp4RDIwTTR0VWtEZy8xSmVrVTQreDhXQ3RyOGtXeUREWk9zSms2S1Q1TEV0MGNYRnpmbjArdWdPa0puYUxnUGkrZ1ciLCJtYWMiOiIxNzgzZmE0NzYyY2JjZGUzNzk4Njc1MjUxZWI4Yjc0MmJjMTE5M2I0MDdkMmQzYWQzODg0N2NiMTczZjRjOTI5IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxkNUx0d2JhdzlkejVBYmRrVWNoNkE9PSIsInZhbHVlIjoiUW04RWJxWWlvTFp2MERIck9CSGpNT1BQVzJadU9oZkczaloya2VxOS9UMzdWcWxoS1RFME5mS0gxQUZHS2hQdG9CY3ZoSG9pczlUVmJJR003TzdHMnI3LzdNeUhjbWVMdjhqQi9FVlhVTmxySUVWWjQ1dkE2MjNTU3RZYWdqcjIrRE55NldxbjV5Rng4MFltZUE0WFdDU2dsRlFuRXBnRGFLUjdYUmVFcHJ4aTQ4YytjblN4ZUhxRHV3TXRYS1p2cy9FbHRSTGZNdVZ1cElEZmxUYVc2UWZLWmhLcllaR0dDSTRQNlFkOVJWYnVkQkVybXJudlRCUC8xajVvWUovTkxPWVBLWUJEZHJQZG5rZ25tZGRkazI1dzZaeGx0NXFGbnlZWmoxNHRUeitzd3MzZ3cvVXJjVUM5K0o4RnV4dVNOdXpUc3JqVjhQUnQza2JpTU93OWNHakR2eGxDZHJhU09UNDhLcnhaYmN4VlU4cDI0K1pYVGIyWHhlKzBEUmxLN1hIZFpGTFhzQjBubGhmR2lsR2ozTWswL0grYjNOL2dHWDVYV2w0ZzQ0b2E0UGNYY1dndDdpdWZOTE5VeUlvajVJZzJzMDE4S0d5YU5oMmRzQXM0cjg2cUxhQ2V3VnpXb2YxcU1nb3h5SWdya2s4RThnSW84b05sSGVubkEvUHgiLCJtYWMiOiI2OGZhOTliODczYzkzN2U3ZjgyMjMwMTc2NTkwMWJjMGUwZTU4MGE0NjQ0ODQwNjg5N2ZlYzVlOTVjMGE3NDQ3IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367635170\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1025649184 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025649184\", {\"maxDepth\":0})</script>\n"}}