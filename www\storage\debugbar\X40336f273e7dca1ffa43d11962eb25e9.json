{"__meta": {"id": "X40336f273e7dca1ffa43d11962eb25e9", "datetime": "2025-06-06 19:23:09", "utime": **********.96665, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237788.425835, "end": **********.966678, "duration": 1.5408430099487305, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749237788.425835, "relative_start": 0, "end": **********.783303, "relative_end": **********.783303, "duration": 1.3574681282043457, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.783325, "relative_start": 1.357490062713623, "end": **********.966681, "relative_end": 3.0994415283203125e-06, "duration": 0.18335604667663574, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01634, "accumulated_duration_str": "16.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.880217, "duration": 0.014029999999999999, "duration_str": "14.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.863}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.925097, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.863, "width_percent": 5.875}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.945353, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.738, "width_percent": 8.262}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-726245514 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-726245514\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2009475281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2009475281\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-28756088 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28756088\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-749639528 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237764242%7C14%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIxRDdMR09iMjB3cTRSTndtektTaEE9PSIsInZhbHVlIjoiS2RuaFhWQjVpTit5VVZCdzFTeG16Wm0rTk1LMGR4MS9JVDYyVW11ck1NUGtjV3BQRm0rT1hpT2t5Qms4SnUxSnA3S1FrQkpMeDEvVGt3NlJKdW8zN0xkL1VFYk9GZG9qSERtQ1Bwb3BmR3kvdGdEQlFHbGVNWnZSYnZ2NUlvQUlBOEhWOENmQnZIOU1TeS95cjVrcmY4U2szUldiZ0lQbTAyNGFWaCtua2ZYREQ5bkNqVTR1eWtxR2hmc2k4aVJqbEF1cmxFOWRCczVGWitobEh5SDJ3ZEhoSWdmTExncHp6S2VzWnpBWHBid3JkMEdEalhaaHZFUkUvdlpHUEZ6Tm5ZUWJxK2ZsVU1hZU5GRUg0bGpWdC9xTWVtdTk1Z3UyRTljREdhWThnTERJRTkvUjVhOXhhREtJTXZQTDFsaFRtc0JiOFh1WlQ1Snh4TE83ZUhvaEYwUnhqSVQxcUxOQnVLZFYyaVhiaWtqY3Z1WElQNkEwekxKWlJCdGoycFN1ZFBVZjkzTWVqeGZ0cnU5NXVmMVloVzA2c2RaTmNDeUR1YkMwM2hUWGx0RFhYMW56dGNGYnpaRXVUVTZOZmd5TjRwenp0NzN5UmFsZUFNVGU4aXJJVEVjR1hMdVlyK09NNk9oT0oyVkc2Z29yaCticHJyRSsreGNaNUhqTHk2cTYiLCJtYWMiOiI4MmU0MmIxYTVlNzI2NGU5Njc5OTU4NjQ2YThkYmM2ZjZiZDllOWY4NjA0ZWI1MDU2ZTQ1M2JhMGNmMWQwOTU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNuamVSSkpIc1NZbzBPSEVxekhvL3c9PSIsInZhbHVlIjoiT3FSRDljeWQ5QVJibnBITU9McUQyc2k5ZVhyV2t2UVhINkk3dUZGME1JVDVFdXRhVExVWVdnWnlMWUhuVUxhczVDWWsxaGZyd0dZZG0vc0grVnoxY1YzN1E0RFdoRUNsYkxYNUJwVnhabS9pa05GcHlMbVFYNUpST0pnSWRiSVovRjZuZTU1YnlFU1crMTdHaW1INmFPV21QMlNNT2FkRXk5b0t4KzYxUHVzQ2JXVXRhVCtQd1ZYNFN6MTFEaE4vVWZwdCtYa0hPd2JsT0MxS21GYkZvbXJNemlQQXNwOHR0MjRCYlV1K2hsaVhHK0V1Rjl0RHlyeUJTeTljMkVqbXp4clR3RzNvaDJXVXRyMndCVGdYMFNvd1lhSDhaY2pnUG5ESVd6Wm52S3hrK05DdWcxMVM0TVlTZ2dSNEY4N29mUmRTT3BWQzJ1SVhvTUlzRjRDeU9PZ2R4NkdKWUs2alMrbE10ZmJpV2dUdjZRTXZWYUlFazd2UExDRDhRaktVeVp1QWFOeWV1VTlOMU1tek55emZlclV2czRCaVpoODMzYVlTSXZjUzhEWFVOc0lBbU9MSnRRWE9PYVZNSHRURmRrYjFFY1p3SEJydlRBTGYzTWRBRHRUTEkvUlRIZnFzZ1JZa3d5Vkk1Qk8wczVGa3VRWDhuQWo5Nk8zNUFHM3ciLCJtYWMiOiIyYjM4MmRhOGU3NGRhZmU1NzU0YzNhNzRiNDA5N2Y2YTQxNjEwMmMwZjMxZTYzMWVmYzM1ZWY4OTIzYjVkYWZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749639528\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-989971684 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989971684\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-940269310 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:23:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImIwRUd4QjROR2tWQkIzOVNoVmp5WXc9PSIsInZhbHVlIjoiTE1pbUJpeGcyY29CRFprOW1FZHBGejlLa2JvQW5rUzZ5U3BoVnJVUmlOdFdXSVdHWHJ5cC9uWjRjM0dBVXVUN3NVUWp6d1JpM0hBL1VPQW9HZXBHTEpab2FscTIzVmo4cXE2NEhqSTlLNG5SclZJU1RTczNLZW9LMDA2YktXanJ1Mnh5bjlCblFFdGFYM3pNckhSeGpYNjkxR1R6ZGN4Sk44SUpSR3VyVGQrRitCcHVJck5qOUpLbmRGbUgrdzlDejVKV3E5bXAxYTN1VkJqc3E4WU5Ma0pSaGdoNjVzbU1Uemh0THdUY0lyaGFoSzZnQmRIS0ZtcVN1SyswVTVKTEJWNVgvWm5JSFhiVmhSZUF0UHl3bW9Cb3F2YXYvYVhCb3BDVmFCY1MvdEtSVmpNMGlvcE9KeEY4NkE3eDIvM09vMEZtcFY1TWRuYklEVXltNkxNcU9IK2xkVW9DNGI5ZDVUR2sxR1YyV2toQ0VLdGlCUVloRjVzbExqQ3FzQitXN2l6UmVIa0JOODJNWU9iOU1EZWJrVUhSc1c3aTZyZ2JUSCtOZnh3YVVFT2dnOVBLbDNzSmhPWGxKcTM4TDJrNCtwM1lvZXZnZ28zQmtzUTlNT1FkazdZUmJKcmxHaG50NHpWQmZSWWFlazlLMVZSM1dWL2xIUCs1dDNLZk9hcCsiLCJtYWMiOiJmODA4YjYzNGEwY2QzMjBjNzQyNTU0ZDdlMmU5N2M4MDA0MDU1ZGE2ZDU1ODBlZWIxZTAyNmE1MzRlMTQ1NDEyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:23:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdyZi80TWpxNFF4alZNSE1SQ3UzMWc9PSIsInZhbHVlIjoiUzJEMXZ5VG9PQ0szaGVoM2ZpU2xhN3AreFNpL0owSmZiTXd1Zk5aekZMMG4vZVlxelpOMWhTcjlBZkNFVjRJVVk2eEVKYmZjeVpBVExyVWZRZnZQOStFSUZCVVVmdWtvTDAzMzM5S3FuREZpamhCTWtFamZaY013WjU5Vi9qQytndFFsbTlzL3piNnNPZVIrZ2dkMGF6V0ZpdXlHYlBEWW5CL28yYWl5UzI0azU0N21IY1MxT2tnNmFLdVltU0tCWXFaVUVWM3VkNFRhNDRjT1RtRlA1eGdabDVGeFlnT0g4eHR0dUtjOEVaUVlIVzBPYnFpd2puT2RnQ1lZSXlsNXJSeFgwbyt2UlMwMTgxbFQrRDI2NURIWG1ZTEp6OS9JTkZndE5RN3VXUGRFOS9nZThRRUVlekswc2U5bmd5S3hPdTdYUHNNdG9ETVAxclFwQUxGYkhCZVkrKzNDTU9xelZRYUhLNlpBUjZjTWVraXNIdTZPMUc2N0c0RjBJUEtTYUpOZ1hzVVltRnRVRmN4YWFaRldwL3QvNitQUW1UQTdjenZyenJiVWZQeDR4TjkycXYrR0ZhdzZPcGJRTXdEY3h6YllHdk02VmZEKy9HV2dMVDcvRzlnQ2QxTzQyVk5ldXRPN0hIUHFqLzY2V2lTM1NFQ2NwOXFQbW14RXFnWFEiLCJtYWMiOiI0MzcxOTBjYWRkY2MxYjFkN2YxOTRiN2JhMTc4M2IwOWY5YmI5ZjM5MjYwZWNiNzM1YzgyNjcwZmMxZjk1ZmJmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:23:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImIwRUd4QjROR2tWQkIzOVNoVmp5WXc9PSIsInZhbHVlIjoiTE1pbUJpeGcyY29CRFprOW1FZHBGejlLa2JvQW5rUzZ5U3BoVnJVUmlOdFdXSVdHWHJ5cC9uWjRjM0dBVXVUN3NVUWp6d1JpM0hBL1VPQW9HZXBHTEpab2FscTIzVmo4cXE2NEhqSTlLNG5SclZJU1RTczNLZW9LMDA2YktXanJ1Mnh5bjlCblFFdGFYM3pNckhSeGpYNjkxR1R6ZGN4Sk44SUpSR3VyVGQrRitCcHVJck5qOUpLbmRGbUgrdzlDejVKV3E5bXAxYTN1VkJqc3E4WU5Ma0pSaGdoNjVzbU1Uemh0THdUY0lyaGFoSzZnQmRIS0ZtcVN1SyswVTVKTEJWNVgvWm5JSFhiVmhSZUF0UHl3bW9Cb3F2YXYvYVhCb3BDVmFCY1MvdEtSVmpNMGlvcE9KeEY4NkE3eDIvM09vMEZtcFY1TWRuYklEVXltNkxNcU9IK2xkVW9DNGI5ZDVUR2sxR1YyV2toQ0VLdGlCUVloRjVzbExqQ3FzQitXN2l6UmVIa0JOODJNWU9iOU1EZWJrVUhSc1c3aTZyZ2JUSCtOZnh3YVVFT2dnOVBLbDNzSmhPWGxKcTM4TDJrNCtwM1lvZXZnZ28zQmtzUTlNT1FkazdZUmJKcmxHaG50NHpWQmZSWWFlazlLMVZSM1dWL2xIUCs1dDNLZk9hcCsiLCJtYWMiOiJmODA4YjYzNGEwY2QzMjBjNzQyNTU0ZDdlMmU5N2M4MDA0MDU1ZGE2ZDU1ODBlZWIxZTAyNmE1MzRlMTQ1NDEyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:23:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdyZi80TWpxNFF4alZNSE1SQ3UzMWc9PSIsInZhbHVlIjoiUzJEMXZ5VG9PQ0szaGVoM2ZpU2xhN3AreFNpL0owSmZiTXd1Zk5aekZMMG4vZVlxelpOMWhTcjlBZkNFVjRJVVk2eEVKYmZjeVpBVExyVWZRZnZQOStFSUZCVVVmdWtvTDAzMzM5S3FuREZpamhCTWtFamZaY013WjU5Vi9qQytndFFsbTlzL3piNnNPZVIrZ2dkMGF6V0ZpdXlHYlBEWW5CL28yYWl5UzI0azU0N21IY1MxT2tnNmFLdVltU0tCWXFaVUVWM3VkNFRhNDRjT1RtRlA1eGdabDVGeFlnT0g4eHR0dUtjOEVaUVlIVzBPYnFpd2puT2RnQ1lZSXlsNXJSeFgwbyt2UlMwMTgxbFQrRDI2NURIWG1ZTEp6OS9JTkZndE5RN3VXUGRFOS9nZThRRUVlekswc2U5bmd5S3hPdTdYUHNNdG9ETVAxclFwQUxGYkhCZVkrKzNDTU9xelZRYUhLNlpBUjZjTWVraXNIdTZPMUc2N0c0RjBJUEtTYUpOZ1hzVVltRnRVRmN4YWFaRldwL3QvNitQUW1UQTdjenZyenJiVWZQeDR4TjkycXYrR0ZhdzZPcGJRTXdEY3h6YllHdk02VmZEKy9HV2dMVDcvRzlnQ2QxTzQyVk5ldXRPN0hIUHFqLzY2V2lTM1NFQ2NwOXFQbW14RXFnWFEiLCJtYWMiOiI0MzcxOTBjYWRkY2MxYjFkN2YxOTRiN2JhMTc4M2IwOWY5YmI5ZjM5MjYwZWNiNzM1YzgyNjcwZmMxZjk1ZmJmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:23:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940269310\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-523351760 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523351760\", {\"maxDepth\":0})</script>\n"}}