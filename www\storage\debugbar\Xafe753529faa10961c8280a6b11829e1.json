{"__meta": {"id": "Xafe753529faa10961c8280a6b11829e1", "datetime": "2025-06-07 04:14:42", "utime": **********.68093, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269681.156254, "end": **********.680958, "duration": 1.5247039794921875, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749269681.156254, "relative_start": 0, "end": **********.234998, "relative_end": **********.234998, "duration": 1.0787439346313477, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.23502, "relative_start": 1.078765869140625, "end": **********.680961, "relative_end": 2.86102294921875e-06, "duration": 0.4459409713745117, "duration_str": "446ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45693224, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.421566, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.44688, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.647341, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.661064, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.17248999999999998, "accumulated_duration_str": "172ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.32007, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 3.113}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.332353, "duration": 0.02258, "duration_str": "22.58ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 3.113, "width_percent": 13.091}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.366634, "duration": 0.007019999999999999, "duration_str": "7.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 16.204, "width_percent": 4.07}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4254189, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 20.274, "width_percent": 0.736}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.449534, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 21.01, "width_percent": 0.626}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.488466, "duration": 0.12783, "duration_str": "128ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 21.636, "width_percent": 74.109}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.627928, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 95.745, "width_percent": 0.736}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.634438, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 96.481, "width_percent": 0.62}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.650507, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.101, "width_percent": 2.899}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HjXxoHcTyQnXHMNgWIK18Kup7wtTlguM2jElQVHa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-703690607 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-703690607\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1121171543 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1121171543\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-189107871 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-189107871\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1652653488 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=v1wjex%7C1749242089260%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikh1NVVuc1c3NEpITXduWWxSU3Z4WVE9PSIsInZhbHVlIjoiQ0FMMnFXRVBxZm9USmdyU2IyeEhoaGI5d0ZBU0NVUGVkQ29sVnhlZS8ycjNNVTgzRWptK0RBb1dsMkhMOG1XeFR1bmFBcUFxQmhIYzVSOVVMVDJFa1Fxa3JsL1QyaTRJc1RDc3ZLMzhUOXovOWZEZXV1NzJVYldFY2NYYlFYSUk2L055Q0M5d1F1cHNtanBzWFZhWHVrd3dDd3d0NTZ4QUpnZnF2aTFFTWpxdDBobGZNeDRlYUF6d0RoVmlDYndNRHJHUTdXUFV0TUVpcXorb1NGNlpYTWRLSmhWSmQvelJpaENnOWNKUUVsTXFsYlRxaEhSdUhTOWEwMHBWa3VJVjY2SzRFRFBBZjRtR0NDR05jQ0RObUNldkZvckFxMjVwdEFXS1JWVnl4UHdtVG1lOXVTcWJQdmMzT2RWRHNFcWlSZGNDVi81MXpEQUpSMGxJcytrcUNBWk1WOGVSSlkrL05STDJDZ2dlNnNGUk9hSzAySENDUUZXeXgzK2FTNDh6M3VxVGNuaGwxS1hybWNiSEFBbFVQOXdjL1FRZGZ3eCtWekNVekUweDgrL3BuWk9HUzZkVlhHcCtCdDdjVlk5QnVPbEZaSmZTbVdmRmJaeFV3YUg2UDZzVFc5WEJMOVNuMG9FWS9SNUZtSkpNRHZpcTVBSlNDRjhWVDlJMjBJSXEiLCJtYWMiOiI0YjYxZTgyYWU1Mjg0NGY0ZmVhMWQ0YzAyMmJkYjdiMzFmNjViYjQ5NmMxZmZiYmVlNzhiYmRhZmZjNzhiMWFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5oNTdZN2ErL2JGNWcrUkVkSjdPelE9PSIsInZhbHVlIjoidVU4MG54TDROc2RPdVZUQ09kZ3ZIelMzVDlWTCswS1NNdGh6ckl2NlZ0SzZCMFVZNE4zdi80VnJLWFA1Q0FOeFhGajJ3ZFJkZFU0bWlabGNYbnVPL0g2NUxDV21BRzY4Tzl6dkRnRlRkak9WU2tsaXgwUTFjSnloazJRdEdkZ0hYSFBHaGlpSmUyampBZHRtNHdwQkM5NlA5ZE12VjJxNUNHek10eW9KT2x3aGc5akkycWs4c3NjUEtEeURSYWNVb1JGZkFWSlhQS1RSOFQ2d3NLZmtBVGNKS2lFbnFVLzBmZmpiSWp5R0JZY3Q1Z3B2NXJTNVVmOFVxVHZlS3ZTUlUwckJ3ajE5RzEvSlNEalZFM05yVWJVTUsvMk16TndtWWRSNDJIY1h2TjRBTmN4VzF4YnEwRHpOV2p1WnpSdjJod0FPN2pMYU9yMjROaTloeWxlekR2RHNjRFVJdHNNMUxXV2NBLy9DL2RSL1huSW9wRyt0MVNheUROWStVYVBueGw4RzFrSmRFdHg2WjlORjRJa1hHdFhyaGozUk0ranFEendCNlpqcUt2Sm4zdFhvZXB0UnQwVFVUR0hsZ2NuUk5ScFJYbGgxSW40TTZzcXFGdzM0T3RZUVFTOXJETHFaZXlsZlBkRkYrM3E5ZnR2S3ExTnhPT2Vwajlnczd4VmUiLCJtYWMiOiJjYmVkMzhkNDYzNmJhMjZiYjA3Yzg4MTcyZjIyZGIyYzZkNWE1NmIzMjNkMTc1NWU3YmU5Njk5OWQ5NGY4ZWU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652653488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-470077500 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HjXxoHcTyQnXHMNgWIK18Kup7wtTlguM2jElQVHa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4gyzHSRcZmygV63EDHIuIrwnK5l2jBXuJ4M26Vli</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-470077500\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1790694290 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:14:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkF1a0pBYUFBbnFtNHRnYWk2eVNXd2c9PSIsInZhbHVlIjoiV2dlRjRaODMvaG84dnNjb3AvWnUrNmF4MlBhMENWNG9xcVltM09tQ1NMelJXVjY1dmpMbUlEZWNTL0t3cnk3L0tDOW9WcUFlcVlLNjd3QTNtakVvMG1JUWhnYm54MDk4NU5xcUhrTTJXeWpiN1B3b2FpTDNPekhMNmh6S1RjZnU3enFRaXhMQzRRNGFBOG42bVpFbnoyeGhVQldXcVI0U2ZKYmZXUWZIU2c0aE5aeG1qblF1YXRucTR4NXFTbGUwMFcxQXIvWHJ0TDF1NjNZdGNlNmFmZHJTS21pbThHSzZEN2hIbnF6aVhyU2VjRTRaSzlxcU1ObjRJdG96MTEyZS9PZFhJOHlrdDlYMEgvK01GZTVrT2F5ZCtRblJURUdtblNXVTdkWHRud2tsWGprcjAvMHcrNjg4REZLTEFYVTdKTzliTHlIY0oyeWw0ejcyT25LVDNHSVU4L25naVVuM21tYk95RnllN1FIMGdPOUw0QnZtNWtFV21YYll6M1Z3c1VkcnU2dzJ3VHhiRjNpSExYZ2wrazhVbWpkQTNzMi9IMytCK2NVY3hpblVMVFk2SVU0Tk5VWEhaRElpcUpqTnMxb1ZtQ3MrMFFOQU81VHkvWjg5cms1RVNxRGlpTGEzTWU1YVZhOC9iaEdFSzJMenEvczV4VC85d29XM1hMZFgiLCJtYWMiOiIzN2UyMWRiMmU5NjZlNTY3NDliMGIxMDg2ODVhODJhYTI0ZDA3N2M3NzQ4ZjE5MDlkYTk3MjBiN2VkMjM0ZDhmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:14:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkkwT1ozbmVrUVJuVnJKb1hoYW82SXc9PSIsInZhbHVlIjoiSzZHSjVVRGIvSDdGc3EzNmtxQnBSdkhKSlhXVjBuT1o1RmlUQXFYQTNNenJnOTVvOVJ6V1pYUnJoNHlHaDdYL3JBZHd6ZysvamgzeDdycGtFbFR6NlI1bmIvb1ZrV1IrVFhZOSsvT3Z6b25IdjdvY2NXY21HaHJXQkl0WDVWQjdxdHM4Tm9MRlVoWE1Zd2JNSzJpaFZpemVjZjZJMjc2R2lhcjVhMXJWbk5wRUdsLzI2TWxhOXBGcHMzT1ZhQ1Q2VkVoYXZNbW9XNlNsYWZkVEp1bE1ERnRkS3phbmF2UWJxQWM1d3Z3MW40L1RvaG9vcCtNcm5yMTdOVHRSVUJ3aVRadWt6WHc3Q28vL1RSbk8wZzZ3anh4cUUyR1BKYWx1VGYwZTlFVm13K0pBbzd4bktYeDNCa0RhbWhMNXVLNkE4SFNvekVEU1dEMDdrOThiYm92NzRUOXBJcjRpTCtQd0NYRTdLZ1FTZ1o3cG1URFhsUHFYemg1T2RTZ0xJTkwxMXE0Mm5uTjRrWXhNMjFtL0NvNW5TWFRUMktzME5vSjhVVmpqaXMvU2pzTkJrajVnN2lCTnVJUXpXbmpkLzhNaXUzVWVYYVRMT2ExNkMrU3JyVHRIOEkrVkpjWkE5UHBnT2JCWjVQWGU0dEJnamhwNG0zRndQbGJKVjB4cjIwdlAiLCJtYWMiOiIwNzI4MWQ3MTIyZDRjODc4ZDAzZDk5MTdjOGFkM2ZkNTVhODMyNDdjYjFmZjgzMjQ1ZDNjNzhhZjEzMDIxNzc5IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:14:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkF1a0pBYUFBbnFtNHRnYWk2eVNXd2c9PSIsInZhbHVlIjoiV2dlRjRaODMvaG84dnNjb3AvWnUrNmF4MlBhMENWNG9xcVltM09tQ1NMelJXVjY1dmpMbUlEZWNTL0t3cnk3L0tDOW9WcUFlcVlLNjd3QTNtakVvMG1JUWhnYm54MDk4NU5xcUhrTTJXeWpiN1B3b2FpTDNPekhMNmh6S1RjZnU3enFRaXhMQzRRNGFBOG42bVpFbnoyeGhVQldXcVI0U2ZKYmZXUWZIU2c0aE5aeG1qblF1YXRucTR4NXFTbGUwMFcxQXIvWHJ0TDF1NjNZdGNlNmFmZHJTS21pbThHSzZEN2hIbnF6aVhyU2VjRTRaSzlxcU1ObjRJdG96MTEyZS9PZFhJOHlrdDlYMEgvK01GZTVrT2F5ZCtRblJURUdtblNXVTdkWHRud2tsWGprcjAvMHcrNjg4REZLTEFYVTdKTzliTHlIY0oyeWw0ejcyT25LVDNHSVU4L25naVVuM21tYk95RnllN1FIMGdPOUw0QnZtNWtFV21YYll6M1Z3c1VkcnU2dzJ3VHhiRjNpSExYZ2wrazhVbWpkQTNzMi9IMytCK2NVY3hpblVMVFk2SVU0Tk5VWEhaRElpcUpqTnMxb1ZtQ3MrMFFOQU81VHkvWjg5cms1RVNxRGlpTGEzTWU1YVZhOC9iaEdFSzJMenEvczV4VC85d29XM1hMZFgiLCJtYWMiOiIzN2UyMWRiMmU5NjZlNTY3NDliMGIxMDg2ODVhODJhYTI0ZDA3N2M3NzQ4ZjE5MDlkYTk3MjBiN2VkMjM0ZDhmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:14:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkkwT1ozbmVrUVJuVnJKb1hoYW82SXc9PSIsInZhbHVlIjoiSzZHSjVVRGIvSDdGc3EzNmtxQnBSdkhKSlhXVjBuT1o1RmlUQXFYQTNNenJnOTVvOVJ6V1pYUnJoNHlHaDdYL3JBZHd6ZysvamgzeDdycGtFbFR6NlI1bmIvb1ZrV1IrVFhZOSsvT3Z6b25IdjdvY2NXY21HaHJXQkl0WDVWQjdxdHM4Tm9MRlVoWE1Zd2JNSzJpaFZpemVjZjZJMjc2R2lhcjVhMXJWbk5wRUdsLzI2TWxhOXBGcHMzT1ZhQ1Q2VkVoYXZNbW9XNlNsYWZkVEp1bE1ERnRkS3phbmF2UWJxQWM1d3Z3MW40L1RvaG9vcCtNcm5yMTdOVHRSVUJ3aVRadWt6WHc3Q28vL1RSbk8wZzZ3anh4cUUyR1BKYWx1VGYwZTlFVm13K0pBbzd4bktYeDNCa0RhbWhMNXVLNkE4SFNvekVEU1dEMDdrOThiYm92NzRUOXBJcjRpTCtQd0NYRTdLZ1FTZ1o3cG1URFhsUHFYemg1T2RTZ0xJTkwxMXE0Mm5uTjRrWXhNMjFtL0NvNW5TWFRUMktzME5vSjhVVmpqaXMvU2pzTkJrajVnN2lCTnVJUXpXbmpkLzhNaXUzVWVYYVRMT2ExNkMrU3JyVHRIOEkrVkpjWkE5UHBnT2JCWjVQWGU0dEJnamhwNG0zRndQbGJKVjB4cjIwdlAiLCJtYWMiOiIwNzI4MWQ3MTIyZDRjODc4ZDAzZDk5MTdjOGFkM2ZkNTVhODMyNDdjYjFmZjgzMjQ1ZDNjNzhhZjEzMDIxNzc5IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:14:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790694290\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1523646080 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HjXxoHcTyQnXHMNgWIK18Kup7wtTlguM2jElQVHa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523646080\", {\"maxDepth\":0})</script>\n"}}