{"__meta": {"id": "X062f759c7eda19ae61a83420c2b29541", "datetime": "2025-06-06 19:38:25", "utime": **********.595838, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238703.952909, "end": **********.595877, "duration": 1.642967939376831, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749238703.952909, "relative_start": 0, "end": **********.294476, "relative_end": **********.294476, "duration": 1.341567039489746, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.294498, "relative_start": 1.3415889739990234, "end": **********.595881, "relative_end": 4.0531158447265625e-06, "duration": 0.30138301849365234, "duration_str": "301ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45680352, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.47825, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.497273, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.569781, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.580341, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.051120000000000006, "accumulated_duration_str": "51.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.386631, "duration": 0.030010000000000002, "duration_str": "30.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 58.705}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.425479, "duration": 0.01305, "duration_str": "13.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 58.705, "width_percent": 25.528}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.449693, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 84.233, "width_percent": 1.878}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.480181, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 86.111, "width_percent": 2.484}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.500143, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 88.595, "width_percent": 1.995}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.53623, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 90.591, "width_percent": 2.719}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.547623, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 93.31, "width_percent": 2.015}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.556021, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 95.325, "width_percent": 2.426}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.5733392, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.75, "width_percent": 2.25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-45392246 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-45392246\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-364783057 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-364783057\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-591479367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-591479367\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-665469676 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=1jor13%7C2%7Cfwj%7C0%7C1983; _clsk=151odr7%7C1749238683155%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVJaVpLZVVnZVRqNkh4WSs3eTBsbnc9PSIsInZhbHVlIjoiYnZ0Snd0aXhFek52TVovZlpSckRJbWZwOWplWFY2OTM0aHRtSWhwdldrOTdEd3c3TFNsS1l5ZE5YWEg5NnBBck5PWStzT1hTTlhMTDd3YXVOaWpuMUxRTnVTM2xmS0YvTEdCZnRzT3hFanY1U25Wc3NlWXo4YXFjRTRNRitRVzVzaVNTK2ErcjZqbDltMWtreVVaSEdVMEJTSWIvU0ExbmpwbjJYejNLMWJkaVJTcnhTU3hrVlpiS1FaekRGclFYa08ra0J0aGxFWXFFbUszdDJCTDY1YWt0R3VNb05tUDN6UkxwMUVWc2xLTmFzbFM4OWZGU3ZYK3gzbGJHWmgxZ1NjcnpOT2l3OTdYOEd2K1JMVE5HR2pySnFVelJGNlRUdW1aVHlxRkJyQ2U4RGFnT3JqalJlZ3ZtdzlWVVNEZzdIeEprR3kxWTNQRUlYWjA3NUMyRWRlR3R4MmQ1YUFWY1Rrb05Nb1IxNHRHaWgzU2ZzT0VkOU5sUU9nWUNzQTlsQnFoWHpaamhiQi82OGN0UzErcVcvRnJFRUIxT2syZHB3Vmp1aXR2RUUrTGorWVoya1ltR2Q3SnVLRE1BVXJOemNUak9CZlVOc1plOTJ5TWFSNkIzUHpMdEhzNThMRzRNWUxWc2JwZ2FPWDd5TjI2c2ZxWDBURzZqOW45bmZPRzMiLCJtYWMiOiI2MzI0MTViZDQzZWVkNGYzZDcxZWNhYjE3NDFlM2E4ZjdmYTQ0MTMzNDg4ZmRhZjA4Yzc3MGE3MTVmNTk4NmU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxoVEx5d0lxRlFIVmFQekNxck5Na1E9PSIsInZhbHVlIjoiUGlWRVRsR3VKcy9HSXIzMkRNb3Y0QlQ0YnEwUjdqZzV4Y00zZnJFUnJ6MFZGSXh3aXRtOXZGQTBKNXhBK1VwM2RHdUFHeGtTTVlIaUhkM0REQUVzWGtuOVA4SERqU0dqWlAwd2RuSjVYZEozWW44czRmSHdiYjQ5ZFdONHNudHVVeGdCb1lUNi9lMUZRbjdhMXdTeFdGNDl0Kzl6ODNUakszWkZCNkhGajFuU1A3NXVPa05SRVNzeUM4UkNBMTBEY0pWWWRacU9QS2V6eGZBbUpNWXRNZDhVSHdtdDF1RWZsTXlYbnhRZlV1K2ZDNys4K2UyTG93U2UvdEVBeUpITEJWY1A4S3NRK0MzTkZSVm95azRWSWJIZlZlUEtzMkVMclh0RTB2eVVqN2pFUDl1VEZocXozQ1FTeGY0V2s0ZGZKTlhMU1RveGYybUx5WkM4RFozMTdqKy9hdG0yZG10Y2U3NWRXaHNNNDYwZmpjR0xocnl5a2w2cW1UWVhFdGlaMHZvalBjMUlEUVdGN3R0SHB2Wlczd25BYi82eW9nd2xtSGJCTEUwYkVuRU9nVXdqcHpKaXBiZzlhS3NzM0NEU0l4aGFUWkkrUGNseTZXdG9Pb3prNkFZUDJqWUcrVTdTUGRpdHN3ckdIMDlHVzJqTlhsTEc0MDVLbWJxcjdXS1giLCJtYWMiOiJhNThhMjZhZDViNTFhODEzOWM3OWUwN2QxNzEzNzkzMDc5OGUwMWM1NTU4MzYyOGJmYjYzMDI0OGRjZGRhODUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665469676\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2044894275 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044894275\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1645178106 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:38:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkR4bEkzeXppUTVmTmsvV1k1eUlsWFE9PSIsInZhbHVlIjoiMjVuTjdaeGQyUUNRVSt0RUpJTVI5NmhubzBKVWlKSGRoV05CcjhJcVY3bEhjZnQ5R1dZaFcydlBWWWZSenRqNVE3aTRFZnV2bWNNQTg1VDZuRGNUWkNyWU43UnQ0dk1uRkl6VUxtcUpYQmdXSGdmT2xuQ2lRUlUyd3B0eUMzSUtaQmYwcldJYjVEQ3o1a2hnQUtMT2UvTEY2QzJMbkJHTzJRaEhaWmVVaUNrL2U5WnlHWDFOb0I3a1Fjd0h1ZEFMNmZ6V2xSVElJMDhGUFlTZDcraVppMGN2Tk53emRYTkljSFUxOHhzQzA1MlM2OHYyTUJ4L2NFcERHS2R6Z1JvRHhvc2J4T1lTaHFDa2hpVEx6SzM0MnluSVE4b3NWdGFBSEdiaUxTRm5uNEIyZmE5M0RCV3dRaE1QSTlWWVhlWnhGMHdtakhBaTBCV3JvRm1laFpqcTh4N3RaeWVxWlpTVm5tc01YTWtmc2FKYTZ1SE5ENzh4RUtqeGpxWTV0aWFId1RoSlkrSDZZNTR6d3p6ZkNWS0U3NTJVazh6ZE1MMHFtRDhFMitrZ2R1cVF4clVIdkFpaGxDODcvQzArRGR6dHZ2NUdKQlFIOElieVFkN3RHQW9LblZET2dWLzZaNUFSUDRGSlNHUXJlT1BIOWxiZE5zeHNhUnpRWjJmdER0UTUiLCJtYWMiOiI1NDQ0MGEzMzk3OWQxYTlkNGRmOGQ2MmIyNDY0M2M4MjQ4MDZmOGJiMjhmODk4ZjlmZGY3ZWM4YzYyZWM4ZmM3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZpaDJMR05zd0NzeWdmOWwxMnVKRVE9PSIsInZhbHVlIjoiS3FnL1hrVWF2RGVKQnNyeEliM0tYb1dlKytiS3ZFNUs0WnBtRGV2TkdIY2pDaXBta3lSVHdUWW1adWx3S0xKS2RQMVplU2J4ZEVIV1FvN1NHdzNIR2lBUHFueGx2T0J1dTlCY3VOYW5BallyVHdaVTlOMk4vcngrVEcvN3RITEM2bUF1ZUZhNUpqL3c1TEFNVWhlRUxxQWtxWU9BUDc5OW5JMTFOWmtrVGxNaDM5VVVTRDFwUEorNEtRWTRWOU1OUDdWczVHRUs4RE1ETGdFY3RUdWE2aUpTakJ4V2w0ZWNFOG1NekJEUHpnRWF4UG1DbjUwMGphd3pLOFY5d0lvWC96Rks4K0JjQ2FTVDh0bkpodFdBbnlBTVdzYkxYb2RaYi9mekN0STB2RnBQZ3BBc3ZPYWRoSGFnVi9TUlZ0UWVUeXZaNEdtWmN0K0JHUzhoZ050bFp3YUdGakJ0RE9RQVM4VS9IZTZDa0tnTVNubkFLU2txR2NMMW5XZ2grQ01YM3VveGl0NkpCUGRJY0pXZnhhMG4wTVVRM3hlMWxGeE1HY3NLbGhvWjROUDNtZnhJYlo2aXZjQ2dxNEl6OVByY3JkbUp1ZzVwWE1YbnN2SCtnU0cxMWlTWEl3bE9oQjk4YTNsaHhoTlpYNVlybXg5ZHozQXg3RUdBSFpvYWhxeFUiLCJtYWMiOiJlNmUwYWZmMDUwMGYyNDZlZWY5ZGM5ZmY1MjRjYWUzMTgxMTNhNzE2M2MzMjdjM2MxZGM5NmIwZGU3NjVlODdmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkR4bEkzeXppUTVmTmsvV1k1eUlsWFE9PSIsInZhbHVlIjoiMjVuTjdaeGQyUUNRVSt0RUpJTVI5NmhubzBKVWlKSGRoV05CcjhJcVY3bEhjZnQ5R1dZaFcydlBWWWZSenRqNVE3aTRFZnV2bWNNQTg1VDZuRGNUWkNyWU43UnQ0dk1uRkl6VUxtcUpYQmdXSGdmT2xuQ2lRUlUyd3B0eUMzSUtaQmYwcldJYjVEQ3o1a2hnQUtMT2UvTEY2QzJMbkJHTzJRaEhaWmVVaUNrL2U5WnlHWDFOb0I3a1Fjd0h1ZEFMNmZ6V2xSVElJMDhGUFlTZDcraVppMGN2Tk53emRYTkljSFUxOHhzQzA1MlM2OHYyTUJ4L2NFcERHS2R6Z1JvRHhvc2J4T1lTaHFDa2hpVEx6SzM0MnluSVE4b3NWdGFBSEdiaUxTRm5uNEIyZmE5M0RCV3dRaE1QSTlWWVhlWnhGMHdtakhBaTBCV3JvRm1laFpqcTh4N3RaeWVxWlpTVm5tc01YTWtmc2FKYTZ1SE5ENzh4RUtqeGpxWTV0aWFId1RoSlkrSDZZNTR6d3p6ZkNWS0U3NTJVazh6ZE1MMHFtRDhFMitrZ2R1cVF4clVIdkFpaGxDODcvQzArRGR6dHZ2NUdKQlFIOElieVFkN3RHQW9LblZET2dWLzZaNUFSUDRGSlNHUXJlT1BIOWxiZE5zeHNhUnpRWjJmdER0UTUiLCJtYWMiOiI1NDQ0MGEzMzk3OWQxYTlkNGRmOGQ2MmIyNDY0M2M4MjQ4MDZmOGJiMjhmODk4ZjlmZGY3ZWM4YzYyZWM4ZmM3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZpaDJMR05zd0NzeWdmOWwxMnVKRVE9PSIsInZhbHVlIjoiS3FnL1hrVWF2RGVKQnNyeEliM0tYb1dlKytiS3ZFNUs0WnBtRGV2TkdIY2pDaXBta3lSVHdUWW1adWx3S0xKS2RQMVplU2J4ZEVIV1FvN1NHdzNIR2lBUHFueGx2T0J1dTlCY3VOYW5BallyVHdaVTlOMk4vcngrVEcvN3RITEM2bUF1ZUZhNUpqL3c1TEFNVWhlRUxxQWtxWU9BUDc5OW5JMTFOWmtrVGxNaDM5VVVTRDFwUEorNEtRWTRWOU1OUDdWczVHRUs4RE1ETGdFY3RUdWE2aUpTakJ4V2w0ZWNFOG1NekJEUHpnRWF4UG1DbjUwMGphd3pLOFY5d0lvWC96Rks4K0JjQ2FTVDh0bkpodFdBbnlBTVdzYkxYb2RaYi9mekN0STB2RnBQZ3BBc3ZPYWRoSGFnVi9TUlZ0UWVUeXZaNEdtWmN0K0JHUzhoZ050bFp3YUdGakJ0RE9RQVM4VS9IZTZDa0tnTVNubkFLU2txR2NMMW5XZ2grQ01YM3VveGl0NkpCUGRJY0pXZnhhMG4wTVVRM3hlMWxGeE1HY3NLbGhvWjROUDNtZnhJYlo2aXZjQ2dxNEl6OVByY3JkbUp1ZzVwWE1YbnN2SCtnU0cxMWlTWEl3bE9oQjk4YTNsaHhoTlpYNVlybXg5ZHozQXg3RUdBSFpvYWhxeFUiLCJtYWMiOiJlNmUwYWZmMDUwMGYyNDZlZWY5ZGM5ZmY1MjRjYWUzMTgxMTNhNzE2M2MzMjdjM2MxZGM5NmIwZGU3NjVlODdmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645178106\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-682032207 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682032207\", {\"maxDepth\":0})</script>\n"}}