# دليل نشر صفحة التسعير - Pricing Management

## 📋 نظرة عامة
تم إنشاء صفحة "التسعير" في قسم إدارة العمليات المالية لإدارة أسعار المنتجات والخدمات مع إمكانية التعديل المباشر.

## 🎯 الميزات المضافة

### ✅ الوظائف الأساسية
- **عرض جميع المنتجات**: قائمة شاملة بجميع المنتجات والخدمات
- **التعديل المباشر**: إمكانية تعديل البيانات مباشرة من الجدول
- **البحث والفلترة**: بحث بالاسم أو SKU وفلترة حسب الفئة والنوع
- **عرض الصور**: عرض صور المنتجات في الجدول

### ✅ الحقول القابلة للتعديل
- **Name** (الاسم) - تعديل نصي
- **SKU** (رمز المنتج) - تعديل نصي
- **Sale Price** (سعر البيع) - تعديل رقمي
- **Purchase Price** (سعر الشراء) - تعديل رقمي
- **Expense Account** (حساب المصروفات) - قائمة منسدلة
- **Tax** (الضريبة) - قائمة منسدلة
- **Category** (الفئة) - قائمة منسدلة
- **Unit** (الوحدة) - قائمة منسدلة
- **Type** (النوع) - قائمة منسدلة (منتج/خدمة)

### ✅ الميزات التقنية
- **AJAX**: تحديث البيانات بدون إعادة تحميل الصفحة
- **التحقق من الصحة**: فحص البيانات قبل الحفظ
- **الصلاحيات**: التحقق من صلاحيات المستخدم
- **التنبيهات**: رسائل نجاح وخطأ واضحة
- **واجهة سهلة**: تصميم بديهي مع مؤشرات بصرية

## 📁 الملفات المضافة/المعدلة

### 1. Controller جديد
```
app/Http/Controllers/PricingController.php
```
**الوظائف:**
- `index()` - عرض الصفحة الرئيسية
- `updateInline()` - تحديث البيانات مباشرة
- `getFieldOptions()` - جلب خيارات القوائم المنسدلة
- `formatDisplayValue()` - تنسيق القيم للعرض

### 2. View جديد
```
resources/views/pricing/index.blade.php
```
**المحتويات:**
- فلاتر البحث والفلترة
- جدول المنتجات مع التعديل المباشر
- JavaScript للتعديل المباشر
- CSS للتصميم والتفاعل

### 3. Routes جديدة
```
routes/web.php (تحديث)
```
**المسارات المضافة:**
- `GET /pricing` - عرض الصفحة
- `POST /pricing/update-inline` - تحديث البيانات
- `GET /pricing/field-options` - جلب الخيارات

### 4. القائمة الجانبية
```
resources/views/partials/admin/menu.blade.php (تحديث)
```
**التحديث:**
- إضافة رابط "التسعير" في قسم إدارة العمليات المالية

## 🚀 خطوات النشر

### المرحلة 1: رفع الملفات الأساسية
```bash
# 1. Controller
app/Http/Controllers/PricingController.php

# 2. View
resources/views/pricing/index.blade.php

# 3. Routes (تحديث الملف الموجود)
routes/web.php

# 4. Menu (تحديث الملف الموجود)
resources/views/partials/admin/menu.blade.php
```

### المرحلة 2: التحقق من المتطلبات
- ✅ جدول `product_services` موجود
- ✅ جدول `product_service_categories` موجود
- ✅ جدول `product_service_units` موجود
- ✅ جدول `chart_of_accounts` موجود
- ✅ جدول `taxes` موجود

### المرحلة 3: الصلاحيات المطلوبة
- `manage product & service` - لعرض الصفحة
- `edit product & service` - للتعديل المباشر

## 🔧 كيفية الاستخدام

### 1. الوصول للصفحة
- انتقل إلى **إدارة العمليات المالية** > **التسعير**

### 2. البحث والفلترة
- استخدم حقل البحث للبحث بالاسم أو SKU
- اختر فئة معينة من القائمة المنسدلة
- اختر نوع المنتج (منتج/خدمة)

### 3. التعديل المباشر
- انقر على أي خلية قابلة للتعديل
- ستظهر أيقونة قلم عند التمرير
- للحقول النصية: اكتب القيمة الجديدة واضغط Enter
- للقوائم المنسدلة: اختر القيمة الجديدة من القائمة

### 4. حفظ التغييرات
- التغييرات تُحفظ تلقائياً عند الانتهاء من التعديل
- ستظهر رسالة تأكيد عند النجاح
- ستظهر رسالة خطأ في حالة وجود مشكلة

## 🛡️ الأمان والصلاحيات

### التحقق من الصلاحيات
- فحص صلاحية `manage product & service` لعرض الصفحة
- فحص صلاحية `edit product & service` للتعديل
- التحقق من ملكية المنتج للمستخدم الحالي

### التحقق من صحة البيانات
- فحص نوع البيانات (نص، رقم، إلخ)
- فحص القيم المطلوبة
- فحص تفرد SKU
- فحص وجود العلاقات (فئة، وحدة، إلخ)

## 🎨 التصميم والواجهة

### الميزات البصرية
- **مؤشرات التعديل**: أيقونة قلم تظهر عند التمرير
- **ألوان التفاعل**: تغيير لون الخلفية عند التعديل
- **مؤشر التحميل**: أيقونة دوارة أثناء الحفظ
- **التنبيهات**: رسائل ملونة للنجاح والخطأ

### التوافق
- **DataTables**: جدول قابل للترتيب والبحث
- **Bootstrap**: تصميم متجاوب
- **Font Awesome**: أيقونات واضحة
- **jQuery**: تفاعل سلس

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. صفحة 404 عند الوصول للرابط
**السبب:** Routes غير محدثة
**الحل:** تأكد من إضافة المسارات في `routes/web.php`

#### 2. خطأ "Permission denied"
**السبب:** المستخدم لا يملك الصلاحيات
**الحل:** تأكد من وجود صلاحية `manage product & service`

#### 3. التعديل المباشر لا يعمل
**السبب:** JavaScript غير محمل أو خطأ في AJAX
**الحل:** تحقق من console المتصفح وتأكد من صحة المسارات

#### 4. القوائم المنسدلة فارغة
**السبب:** عدم وجود بيانات أو خطأ في العلاقات
**الحل:** تأكد من وجود فئات ووحدات وحسابات في قاعدة البيانات

## 📊 الإحصائيات

### الملفات المضافة: 2
- Controller: 1
- View: 1

### الملفات المعدلة: 2
- Routes: 1
- Menu: 1

### إجمالي الأكواد: ~550 سطر
- PHP: ~280 سطر
- Blade/HTML: ~200 سطر
- JavaScript: ~70 سطر

## ✅ قائمة فحص النشر

- [ ] رفع `PricingController.php`
- [ ] رفع `pricing/index.blade.php`
- [ ] تحديث `routes/web.php`
- [ ] تحديث `menu.blade.php`
- [ ] اختبار الوصول للصفحة
- [ ] اختبار البحث والفلترة
- [ ] اختبار التعديل المباشر
- [ ] اختبار الصلاحيات
- [ ] اختبار التنبيهات

## 🎯 النتيجة النهائية

صفحة تسعير متكاملة تسمح بإدارة أسعار المنتجات بكفاءة عالية مع واجهة سهلة الاستخدام وإمكانيات تعديل مباشر متقدمة.
