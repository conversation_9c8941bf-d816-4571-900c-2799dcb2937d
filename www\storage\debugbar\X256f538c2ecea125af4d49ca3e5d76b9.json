{"__meta": {"id": "X256f538c2ecea125af4d49ca3e5d76b9", "datetime": "2025-06-06 19:18:41", "utime": **********.880377, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237520.082334, "end": **********.88042, "duration": 1.7980859279632568, "duration_str": "1.8s", "measures": [{"label": "Booting", "start": 1749237520.082334, "relative_start": 0, "end": **********.653932, "relative_end": **********.653932, "duration": 1.5715980529785156, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.653959, "relative_start": 1.571624994277954, "end": **********.880426, "relative_end": 5.9604644775390625e-06, "duration": 0.22646689414978027, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44754840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021079999999999998, "accumulated_duration_str": "21.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.777901, "duration": 0.01883, "duration_str": "18.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.326}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.828924, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.326, "width_percent": 5.313}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.852504, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.639, "width_percent": 5.361}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1019840663 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237512792%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZqcCtyWlJ4cG0vQ2wwRmsxcVVEd0E9PSIsInZhbHVlIjoidmY4Ti9pZ0ZTckxTUk1keXdNMjBRcThCVGlkbDRLVFMxd2FUOTFzeFFZWG9RWWVLaFJ2amV3YXZIRjVhYVIyMUhkUEJyZlVjTU1GbnE5R01LbDc4U2NoYklRc1Y4djhCVmtVQW5DZnBUM2RhNG05NENvcDFNSjNiMVF5S2o0L3RMemVkUUMwYzNNQXFicUhWcnVQUVpqMDBvdW9yZjdESUlwNWw0MXRkUjJ4VSs0ajJKMFUyU042TmNLajBBd0t5WFhiWjFqN2RHcm0vRjFadFpkdElYWnhyNStaa29HeUZ6aEVPY052UUE4RmZ4RmxuM1I1dWhLK1pJMEtpWWRBSGxLZVE2eEM0YUJoNk9iKzRGRTBBclFGdGloTWVBYVJ3WVU3VkVwd2lrUDRZRkUxdjhIcURVa3NVWXQ1NWhJQVNXbW13N0R5VnVXOHIxSGpWZUFsVkxNRWdPVldHdnA4OXM4YlNmbjNDUGk1OUtOU0VLSXlCWkpSRlRlWGFBWDQwME1ic1dYTTdsc2EwQXFVU3pibG1UK0c1VUViV0dCcmlQaVRDS3FFRUZVWWdSKzdHVm12NFp3U2gwVnUvaTY3eWJlSXQ3WUZTT3ZaZUVJcytCQklocExwZVpqK1YyRTB0Ty9NNzFsNDRNd2RBRlliM3BmRkxmYWNQd3J5TlZWMXciLCJtYWMiOiI1ZjlmZjBhMGU5OTFmYmYwMmQ1NjI1ZDFiMWVmOWEzNjhhYjM3YTBmNGYxZWUxZGViMTJkMTkzMWRmZGYxMWFmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhBVDlvc1RJR09sTUV0bHdHSG55a0E9PSIsInZhbHVlIjoiNEhzYUxMZEtpdjRrM1N2Rmhvb3BSVzJ0NzFCMHdVc0JGYjFNY3RkcmxpUWk3SERzeEZOUGpRV0RpQm9VZ1JMTlA2ZVBwcDRhancyQVdGZ3djeXNLckRpazVPRFZDeVA1N1BDSkpQN1o3ampUMWdtNnBBYmFXYzIrOXR5YUZoVEJ4a1oyVTA0ZzV4Z3h2OHBDR05GcTJIaFQvMk9veDFWb29wVW14UTJxYXlpWnVBZjBoZzVwWm5ObUVrK1UrY1ZhN1p4VVY0SGNRUEE2MWl0UVNkZGo1UWNZdnlkZmlsY3ZZb3oyWEJKUFFWZktXOWp3UlNRSkFoQjZWTE1leFlUVU9DSnpQSm15WmI4QlFLZm1PbExEM2lXV1YySUQvd2wzWGJ4dlZ1aDQ0SzF2eUxoWE15cnlGcXpGYXBrYUpBSHNNUSsxOVJKQ1JDcDVJRFBhN2NrUlA3dzlsYy9ETUFaRzdDdkFHOFFtK2VpbXJYdVM0amdMaVRtemRFVUR4RmN5dGhpS0NuOU1ISFE2a0V3UDl3UXc3THQzK0F3NktCVml0dDZ2OXFaUWJZNVV1bmNsbGZnL1VkOVlQVjAxRkF4cGNza29mQWR3WllSRElSV2F1Vk5ZOHNQdzBoU0E4M1NNcHNZZ2pYYjIwMWNFRFZ4KzdFbmZQQkxpQ0pKakQrSzUiLCJtYWMiOiI4MzU2MTY4NDI5OTY1ZWU4Mjg3YmI5YmViNzA1ZWU5NTYyOTg5YWM2ZTMwOThiYmRhNTQ4MzI2NmMyNmVjNGQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019840663\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-379513860 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379513860\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-715871342 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:18:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii93am80MDNWMWRvMWFrallkWTZmdmc9PSIsInZhbHVlIjoiNE9uMExIdTRONWtRK2h3SDdxcHdrb3hBdVFxWlpnQ1kvUHVnZU56UUo0STRmc0ZGbmlpMWxjYi9LaCtOU3lUT1BIRGlIdTN0MXo0UXNCZGVzUzBTR3JJTDA0c2NkT2YyUkRPd3N6ZURud3ZWbklTMDBkdERyWW9PN3RESjhMYmx0K2E1YWJWNEZINWgvaUVNQmR4NStLZkcySTJ6ckhmUmlIYWdXRUYwc1Y3M2Rja3BINllSODdPWkpyMDRGMHNtdW9FelUzK1F0d2RnSlFjZmZmMy9sR3NUMW10amgvQWJ1Q2hreXhDY2R0emdlOVZvUndkN3N1WEJQQ1VjZkx5dDJGUTAraTNkakI0UWFFMXlXb3pTWDlWc1VOa2I5b2luOE12cVNKWXl1ZWRaYmdaWWpIeng3aTROY1o1WUd4V2RjUk1uZ2lhUzFhajVzdVRWK0hKU1dBbWZvQWNOV2lHbVZwNTNENGgvR1RucmpQbGREREQzU2RvQjQxQ3B2VVVXd0lKa2xJcGJ3ckxnTWNtODhheWIweHg4ejY5ci8vREFPbGZiLytRYVUwZ0txVVIxcHJTZnRoRGNBVTFldlhoeW5ubjRFcWdObmc5NFJyZGJ2ZmJzTmJLUWNkRTdiaXZYc2IvQ2x2LzloS0lzV21yaEpmK2YyT1lpVEY1Qy9wbjgiLCJtYWMiOiIxZTg1NDU4YWMwMzMzMGFiODVmZGY4MWJlYmUwZTdkODQyZGFiMDNmOWVkMDI2MjRjZjQ2ODdlNWFhYWFmYjJmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1SNkx1dEhXZFZPNHpybVQwOGpINFE9PSIsInZhbHVlIjoiNjgzODBqZENLbEJNUDFCbnNjdy9jSzdCbWlIUzRST3RMYTh3VTM5VHBhZ1RDZWRkbEYzN2xiMmlJQjlXKzVnNThXaUZsa1l5VTJyampUZFR1eW54SnhhS3J0OUl0c0VYMlcxbUdPOXhXb1NJWEptRFRsS2VqR0RTeVcxay81YTFDQWl0MWpKT2FZYmRZWStrOEJMSmJCSmtnS2ZEWVVuNXFQNktVOWZCakcvQ2ZGdS8yZVlsVmZBTG0vWUF6RUpqcjE1ZzY2WkRWTTFpd2pZNlVMM21WTjlJN0hKSExiMCtRZnd1RjlRcGcya0tMcTIzLzhvTEQ4UVoxMWcyaWpSdlE0QklvZURTZW5zMGlQcEwwWXhHL0hnYWRyTjdsM1BVeURzbmlhMzAwTlRRQjg2c3djSXluOGRweFQ4NHEzb3ZYRUhyWEVqTzVjSDRPbU9LeDFCckpKYnVjN3FNd1RPMEtZS1FiTmh1MEQ2b081em96cFFjanF4SWRTMVJBdU1WRHpJd2w2M1N3ZzNpZUMxZHJFKzhoWTRYWWtscFg3d3ZVdWxxU0NWV3RaREhvd2NHRDNTY081cnVBaFc3RlJES2JiZTRFR0ZvMG11a3p1c1NxY2Q3QXUrclFUTGlpNDdvOXJjRG1HbDhFY1phTXg3QnlyUkw5SzY3OUhIYVVKSGMiLCJtYWMiOiI5MzI1MzY3ZTFlNGU4MjcxYTY0MmNhZDg5YWU2ZmFkMjBmODI1YjM5OTU5MWJjNzgxMTNhNzBkZjI4N2ZiYjhiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii93am80MDNWMWRvMWFrallkWTZmdmc9PSIsInZhbHVlIjoiNE9uMExIdTRONWtRK2h3SDdxcHdrb3hBdVFxWlpnQ1kvUHVnZU56UUo0STRmc0ZGbmlpMWxjYi9LaCtOU3lUT1BIRGlIdTN0MXo0UXNCZGVzUzBTR3JJTDA0c2NkT2YyUkRPd3N6ZURud3ZWbklTMDBkdERyWW9PN3RESjhMYmx0K2E1YWJWNEZINWgvaUVNQmR4NStLZkcySTJ6ckhmUmlIYWdXRUYwc1Y3M2Rja3BINllSODdPWkpyMDRGMHNtdW9FelUzK1F0d2RnSlFjZmZmMy9sR3NUMW10amgvQWJ1Q2hreXhDY2R0emdlOVZvUndkN3N1WEJQQ1VjZkx5dDJGUTAraTNkakI0UWFFMXlXb3pTWDlWc1VOa2I5b2luOE12cVNKWXl1ZWRaYmdaWWpIeng3aTROY1o1WUd4V2RjUk1uZ2lhUzFhajVzdVRWK0hKU1dBbWZvQWNOV2lHbVZwNTNENGgvR1RucmpQbGREREQzU2RvQjQxQ3B2VVVXd0lKa2xJcGJ3ckxnTWNtODhheWIweHg4ejY5ci8vREFPbGZiLytRYVUwZ0txVVIxcHJTZnRoRGNBVTFldlhoeW5ubjRFcWdObmc5NFJyZGJ2ZmJzTmJLUWNkRTdiaXZYc2IvQ2x2LzloS0lzV21yaEpmK2YyT1lpVEY1Qy9wbjgiLCJtYWMiOiIxZTg1NDU4YWMwMzMzMGFiODVmZGY4MWJlYmUwZTdkODQyZGFiMDNmOWVkMDI2MjRjZjQ2ODdlNWFhYWFmYjJmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1SNkx1dEhXZFZPNHpybVQwOGpINFE9PSIsInZhbHVlIjoiNjgzODBqZENLbEJNUDFCbnNjdy9jSzdCbWlIUzRST3RMYTh3VTM5VHBhZ1RDZWRkbEYzN2xiMmlJQjlXKzVnNThXaUZsa1l5VTJyampUZFR1eW54SnhhS3J0OUl0c0VYMlcxbUdPOXhXb1NJWEptRFRsS2VqR0RTeVcxay81YTFDQWl0MWpKT2FZYmRZWStrOEJMSmJCSmtnS2ZEWVVuNXFQNktVOWZCakcvQ2ZGdS8yZVlsVmZBTG0vWUF6RUpqcjE1ZzY2WkRWTTFpd2pZNlVMM21WTjlJN0hKSExiMCtRZnd1RjlRcGcya0tMcTIzLzhvTEQ4UVoxMWcyaWpSdlE0QklvZURTZW5zMGlQcEwwWXhHL0hnYWRyTjdsM1BVeURzbmlhMzAwTlRRQjg2c3djSXluOGRweFQ4NHEzb3ZYRUhyWEVqTzVjSDRPbU9LeDFCckpKYnVjN3FNd1RPMEtZS1FiTmh1MEQ2b081em96cFFjanF4SWRTMVJBdU1WRHpJd2w2M1N3ZzNpZUMxZHJFKzhoWTRYWWtscFg3d3ZVdWxxU0NWV3RaREhvd2NHRDNTY081cnVBaFc3RlJES2JiZTRFR0ZvMG11a3p1c1NxY2Q3QXUrclFUTGlpNDdvOXJjRG1HbDhFY1phTXg3QnlyUkw5SzY3OUhIYVVKSGMiLCJtYWMiOiI5MzI1MzY3ZTFlNGU4MjcxYTY0MmNhZDg5YWU2ZmFkMjBmODI1YjM5OTU5MWJjNzgxMTNhNzBkZjI4N2ZiYjhiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715871342\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-21******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21********\", {\"maxDepth\":0})</script>\n"}}