{"__meta": {"id": "X493c1928388725d7cc95481c95bf3e22", "datetime": "2025-06-06 20:40:15", "utime": **********.958286, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.425196, "end": **********.958321, "duration": 1.****************, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": **********.425196, "relative_start": 0, "end": **********.738929, "relative_end": **********.738929, "duration": 1.****************, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.738952, "relative_start": 1.***************, "end": **********.958325, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020010000000000003, "accumulated_duration_str": "20.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.845433, "duration": 0.016640000000000002, "duration_str": "16.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.158}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8907511, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.158, "width_percent": 6.647}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9309402, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 89.805, "width_percent": 10.195}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242413154%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZRN25zc3VkMFVYaFNONWR0V0JlRlE9PSIsInZhbHVlIjoiNUZEWUFKeHFOQ0NTeEdmSkhLNWUwdS9ZUE1IY213dXhOakpRWnF6OGxZNnBiWVN4ejh4Q2xOcldGSm9iWnZEZ0s1Z3dQaVo5TGlvd29tQUdvZnEyRHJmN1hnSHdBNGZQczh3bGZXbVMxQlVPUzd5cVlJcHFPTzBKTlN3MEVVWGNIL2pMWU4rakdSMzF0aGZxQnM1N0tFdkphVXpNQkdrWUZzR0xCdDl3eWRqcHE1bWJrZDNxK21IK2pFaWhQbG5oYkF0RkdQRDlCRFFiSmh0RmRhaENLNFROOWhiWW1WL0dKWW9KYTJtdWhMQkF4ekZ5RmE2Y2hkS3d3Wk11ZW5Cc3JsNnFFaTZXYVhpNWRvc1lleEZqZ3JzTVQvaXNhVFlVYTd5dXVTTFpQUElWaGFPN0kyaGpacHFKVElNczQzM05PQXJMN1ZKYzloNVdqbDg1aFg2ZG43OUkxbkFobUtSdTVyUnBTalBWOURNSmllOW9tRlJDd1lLbEozOGlQeDIyNUgzbHRzNC93MzJ5bjl2TVRQMGFHYTdHQUZ6c2NwQjRrWjl3aHZ6TGRzQXdULzg3Zk43dVowd0sybTlYR3g5OVZqL0xGeCtwZWZjb2o3YVRPY2Z1WDBscjNPRGdJcTN5MThEaGRZSkFXdXBhWkwwS1FoOFVRWVZ3Sitlcy95WHQiLCJtYWMiOiJlZjYyZGU2ODIwNzYzMTJiMWYwOWYxNThiOWY2YmMzYzc1Mzk5ZmQ1ZDU4YmMzMzViZGQ3YTQwMWQ5OTQxNTA0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im04bmV5c3R0V2ZuN2VXTTBwcXUyRGc9PSIsInZhbHVlIjoiK25zK0UwbzEzRWV2dHhTWDFISmhaWmFQNVNBb05manBLeWpjZEplcTNHeVY4VjhQU1ErSHV4UjNQejZ4L0MxQXlZNk81MTJHMHNzaW8wMTA2dW5JdGxoTVNUdTZTOUNFVlo5Q2FtZ2dnd3dQU2Y1MlREclQ1SHZod2hhOGtBcGl5MVJsLzY2U1hpTnhocmtsSWxlclh3TkxnY0lJaEFFL0xqT1ExbEtIU1NGVVRlTDN4anExR0gzQ3FGNko5VW51NndPSkY3VHE3WHArS1A1SWJxK2I5c3gvOTNyRHhsS3F6NzJrNHZQRkFXSElrZXdsTTlZNVpGSnFpK0hKTkJvZmVmUmR1TkdnUXd1VXVHVERUYVdwWnFEb09DaVVLRnhYSzFwZVpJcXMrS0JpM2RkNk5QY0c0cTNxTnVnZ25hMVErbjZDd09LYW5zUnMzSEhkMFN4NVcyV0VTQzBFSlFhQUVDb3R0MVhkaXJQYmlBYWJMZlFjcTJhUENHU2UzV2JCcmZLaERWSDU5eHFVajBQSnRsazQzWjE2RU54N2VlOFdUVHpiR2FHQktaTkdsU3RiM0hiNWhFeWsvMDVQSWFnSWkrWjhrY3Y2dzdmaVhJOVBDWDNjVHFEUXhOeDh5OUp1S25sRCtDaVNUZzg0d09IS2RlQmtwNzI0MFBnb2Q5UHEiLCJtYWMiOiI2ZDUyZTZmYjcyODA3ZmJiODlmZjg4ZTBiNDE4OTVmMzBmYzFlMWYxM2E2YjZiOTc2M2E4MWMxMzc3OTFlODBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1278350744 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5SyoJjvx9ER6DwaeNylpmlrCKyunmBMNBrM7fYhk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278350744\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-364828947 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:40:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNaSE42NUZIcmhEaDBYV0FKRTJzNkE9PSIsInZhbHVlIjoiS0xUdXBlblBrdEJQS3U5Y2p6ZFB6OWhPb0gwUWpWYVlLOXJCbk1OM1FzNGduLzV6cjNFejh2M1JWQUxrZ2drNU1ydTN4Z0xoQi9VNndaNTNRVisvU3A5QXM4NjNFVE9HVUpDaVdvRW1aWmRCRFdiS1BnelRUNTlPWmN0em9xSzdYMEFXbUJGamlQZ0NOZE1GQmJnWi8wSldYMHptZk5oNjV2MVpGU21VV0R5QXBlbDd0U2hlN2FrTFAvemt0ZjJxbkdhZXg1U1ZRUUhFWGVjbjYxS3cyeVdGVTBzZUJxMWVSR1c4RExWVCtSVWNEZlhBNFplb2g2S0R1M2RJQWFiNUJXbitpcnViTC9yY3dDMFM0aGpMUjhpc3g2T3I3Q3g5WVpHb292Ti9CYk1Ic0JZVG1QL3BYS05QcXR4bmErZTBHdXNmdVI5WEJ4WXlsekZMbzJRckdLUEtEaG9wMGxVUFR6ODRpWUwrS2lvWDB0d3dKVGNSc1N3cVlMZ3hQWVZYUVYrVlRvSEh4OGxEK3JucTA3aE8zZ3c2LzN4Zm9IWE41d3J1WUtoUmRoYndSN292RllGaFhVQ2VWNVc3M0dtcWVhSG5HMjU5MW5aUkVnNG94VlFaaDllOUhhQlJUVWVtK25pZ0hNQU9obTZOOVJXdHZ6bENDOG1iTGppbldzSXciLCJtYWMiOiIyYjMxZDk3NmUwYzcwYTI4NWJmNDc2N2I2MTg4MjdlZTgwYjY5Zjc5ODY0NjJkZjZiNzIxNDE4M2ZmN2I2OWVjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImcvQVZzZWVYNS93ZTdiNzFMQk1YTkE9PSIsInZhbHVlIjoiQmJqMXJ5aUxPSHNpYXR0NFY3TFdxOVg0a2dGUjBTRXNPb1I0L20ybVloRjA2bkUxOGZDMldBYTl6Q0N1dGg2YUh2OHk4N0h4QlhUOUtYVllJTHZ0WUVzTFFvakJYRGJQeDBYTW9yWHZLM2Y4ZTU0T3JqVUpHMFJqeEdaallyNE9NaUx2T1JrNzRwcHhBSnZVVW5xS0t4SnZ5KzNqRlZTOE9WMVlSR2FqQUFpaExBT2xFb2xTR2NiRWJiWmVzRkNpTnNaU1NHQi9ZSm5DTDEvQ0t3RzJlZmY5NG02a25yZmM2aUp0bG42b3J0NENZQ2NkYXBmREVwZklIVm9wVy90WGtCNUVodlRsRFlOZVpYZUdsRHFZUVFoYWowWkpYc2lmYzIvK29hTHZrSHdsOEZ3VkZKVlRXVTJuQnFDUzFBT2NZVElHT1Q1SUh4UWoycjYvc3dMRENObjRIZjNXWmlheXZyNkdFOTBRQXA3N1d3eHhDeEJwZ2Z1VFE1VGtGdktKcmRsd2UvejZGRVpVRGxydy9RY3AzYTBnY0xXSWZjWmxXbW9ia1JwUXlFeElXV1ZIdE95elN5UVNJTTh4ZjFubmNkcFN0d1BDQ2YzMVNxZzJtUDNDdnZKUUhCMTA3OGlBaGdNTmRobUR6bFFIRU1TWHJTMlNYOGRYVVBuSWJQTjEiLCJtYWMiOiI4M2JjNTNlYzdjM2I5NDcwYjAyNWMxNGFkOTU5YTYxZjJlZmRjNDM5NmZkMTkxZDQyYjA0ZDM3Y2Q4ODBiNGQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNaSE42NUZIcmhEaDBYV0FKRTJzNkE9PSIsInZhbHVlIjoiS0xUdXBlblBrdEJQS3U5Y2p6ZFB6OWhPb0gwUWpWYVlLOXJCbk1OM1FzNGduLzV6cjNFejh2M1JWQUxrZ2drNU1ydTN4Z0xoQi9VNndaNTNRVisvU3A5QXM4NjNFVE9HVUpDaVdvRW1aWmRCRFdiS1BnelRUNTlPWmN0em9xSzdYMEFXbUJGamlQZ0NOZE1GQmJnWi8wSldYMHptZk5oNjV2MVpGU21VV0R5QXBlbDd0U2hlN2FrTFAvemt0ZjJxbkdhZXg1U1ZRUUhFWGVjbjYxS3cyeVdGVTBzZUJxMWVSR1c4RExWVCtSVWNEZlhBNFplb2g2S0R1M2RJQWFiNUJXbitpcnViTC9yY3dDMFM0aGpMUjhpc3g2T3I3Q3g5WVpHb292Ti9CYk1Ic0JZVG1QL3BYS05QcXR4bmErZTBHdXNmdVI5WEJ4WXlsekZMbzJRckdLUEtEaG9wMGxVUFR6ODRpWUwrS2lvWDB0d3dKVGNSc1N3cVlMZ3hQWVZYUVYrVlRvSEh4OGxEK3JucTA3aE8zZ3c2LzN4Zm9IWE41d3J1WUtoUmRoYndSN292RllGaFhVQ2VWNVc3M0dtcWVhSG5HMjU5MW5aUkVnNG94VlFaaDllOUhhQlJUVWVtK25pZ0hNQU9obTZOOVJXdHZ6bENDOG1iTGppbldzSXciLCJtYWMiOiIyYjMxZDk3NmUwYzcwYTI4NWJmNDc2N2I2MTg4MjdlZTgwYjY5Zjc5ODY0NjJkZjZiNzIxNDE4M2ZmN2I2OWVjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImcvQVZzZWVYNS93ZTdiNzFMQk1YTkE9PSIsInZhbHVlIjoiQmJqMXJ5aUxPSHNpYXR0NFY3TFdxOVg0a2dGUjBTRXNPb1I0L20ybVloRjA2bkUxOGZDMldBYTl6Q0N1dGg2YUh2OHk4N0h4QlhUOUtYVllJTHZ0WUVzTFFvakJYRGJQeDBYTW9yWHZLM2Y4ZTU0T3JqVUpHMFJqeEdaallyNE9NaUx2T1JrNzRwcHhBSnZVVW5xS0t4SnZ5KzNqRlZTOE9WMVlSR2FqQUFpaExBT2xFb2xTR2NiRWJiWmVzRkNpTnNaU1NHQi9ZSm5DTDEvQ0t3RzJlZmY5NG02a25yZmM2aUp0bG42b3J0NENZQ2NkYXBmREVwZklIVm9wVy90WGtCNUVodlRsRFlOZVpYZUdsRHFZUVFoYWowWkpYc2lmYzIvK29hTHZrSHdsOEZ3VkZKVlRXVTJuQnFDUzFBT2NZVElHT1Q1SUh4UWoycjYvc3dMRENObjRIZjNXWmlheXZyNkdFOTBRQXA3N1d3eHhDeEJwZ2Z1VFE1VGtGdktKcmRsd2UvejZGRVpVRGxydy9RY3AzYTBnY0xXSWZjWmxXbW9ia1JwUXlFeElXV1ZIdE95elN5UVNJTTh4ZjFubmNkcFN0d1BDQ2YzMVNxZzJtUDNDdnZKUUhCMTA3OGlBaGdNTmRobUR6bFFIRU1TWHJTMlNYOGRYVVBuSWJQTjEiLCJtYWMiOiI4M2JjNTNlYzdjM2I5NDcwYjAyNWMxNGFkOTU5YTYxZjJlZmRjNDM5NmZkMTkxZDQyYjA0ZDM3Y2Q4ODBiNGQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364828947\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-969310809 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969310809\", {\"maxDepth\":0})</script>\n"}}