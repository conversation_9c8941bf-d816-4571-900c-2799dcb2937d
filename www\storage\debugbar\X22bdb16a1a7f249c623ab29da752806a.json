{"__meta": {"id": "X22bdb16a1a7f249c623ab29da752806a", "datetime": "2025-06-06 19:39:54", "utime": **********.497559, "method": "GET", "uri": "/user-login/eyJpdiI6Ii8xc1ZuZXhxdTBTM2Y5eUx2WXRodHc9PSIsInZhbHVlIjoiZ24vTGpaZkl0R2hiKytUUUpMYTZmZz09IiwibWFjIjoiNGUxN2I5YWE2NzUxNmRkOTlmNTE4NDEwOTEyMTcwZWEzMTNmZGRmN2EyNWZkNGIyNDE1YTc1MDdiNzBmZDk4YSIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238793.225808, "end": **********.497593, "duration": 1.271785020828247, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1749238793.225808, "relative_start": 0, "end": **********.327547, "relative_end": **********.327547, "duration": 1.1017391681671143, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.327567, "relative_start": 1.1017591953277588, "end": **********.497597, "relative_end": 4.0531158447265625e-06, "duration": 0.170029878616333, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44001952, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET user-login/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UserController@LoginManage", "namespace": null, "prefix": "", "where": [], "as": "users.login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=698\" onclick=\"\">app/Http/Controllers/UserController.php:698-728</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03753, "accumulated_duration_str": "37.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `users`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 701}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.413084, "duration": 0.026, "duration_str": "26ms", "memory": 0, "memory_str": null, "filename": "UserController.php:701", "source": "app/Http/Controllers/UserController.php:701", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=701", "ajax": false, "filename": "UserController.php", "line": "701"}, "connection": "ty", "start_percent": 0, "width_percent": 69.278}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 702}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4538062, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 69.278, "width_percent": 3.624}, {"sql": "update `users` set `is_enable_login` = 1, `users`.`updated_at` = '2025-06-06 19:39:54' where `id` = 17", "type": "query", "params": [], "bindings": ["1", "2025-06-06 19:39:54", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 718}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.463175, "duration": 0.01017, "duration_str": "10.17ms", "memory": 0, "memory_str": null, "filename": "UserController.php:718", "source": "app/Http/Controllers/UserController.php:718", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=718", "ajax": false, "filename": "UserController.php", "line": "718"}, "connection": "ty", "start_percent": 72.902, "width_percent": 27.098}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/user-login/eyJpdiI6Ii8xc1ZuZXhxdTBTM2Y5eUx2WXRodHc9PSIsInZhbHVlIjoiZ24vTGpaZkl0R2hiKytUUUpMYTZmZz09IiwibWFjIjoiNGUxN2I5YWE2NzUxNmRkOTlmNTE4NDEwOTEyMTcwZWEzMTNmZGRmN2EyNWZkNGIyNDE1YTc1MDdiNzBmZDk4YSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User login enable successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user-login/eyJpdiI6Ii8xc1ZuZXhxdTBTM2Y5eUx2WXRodHc9PSIsInZhbHVlIjoiZ24vTGpaZkl0R2hiKytUUUpMYTZmZz09IiwibWFjIjoiNGUxN2I5YWE2NzUxNmRkOTlmNTE4NDEwOTEyMTcwZWEzMTNmZGRmN2EyNWZkNGIyNDE1YTc1MDdiNzBmZDk4YSIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-618575833 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-618575833\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1846287331 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1846287331\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1115003689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1115003689\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238772341%7C56%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdyaXlTNVkrRkpnWngxS1NnRFQ5OVE9PSIsInZhbHVlIjoiOHZ2Mk1QOTBqSDdObEYyT2ppVXBWczVVOThUNnhjU3JsY3VmaW84enY5UEZZOHZlaFFqMmttbGRMUjhxaHFKbmZ1MGxoQ1E4VFhzbFlqaERkWTNZdVUxVFNYVW13Zi9jbXM0NWhqUW05Z3h1Z1QzTTMvVU0yczlHNlFvYVZQR1BOOWJLMFRKZXAxWTJpeWZNTVFzWUlWODh3QXVOeGpDdnJ1dkxYRkQwaGNreWkybEd5OXVxMUt0ODQxY3c2TGc5dUJiT2J3b0t3V3Z6V05nNWgySFVXanE1UHNWU2M5S1ZBUlFEczVIM1VJV1Qwdndkd0UvRmhFRUwxQnFacGZLQkpmcDlDRlNINTFCUGwvTTdIUk55akVuRmJ3eDhySE1hblhxUUh5V1YzRUx1eEZ1Y1RHOFJpY3hiYU0yZzMrNDZTZ1U5aC9lc2pnZmxMeWVvSUxjUXpjUTR3YWRxOVA2V2RRTHYxWGJYRTNwNzhEeTRybUhqZmtZN256WmFnYjgvQ2dUTU9yYlVBR25Qb2w2a0FQYTVKMlJZMmhhd1BVTVhyQXI1Ly9LVUN0MUJ2UFhSQ2Q3ZXhGOUlZNGkxbTBaTkF2aFVuS2JrUGY4a1hUN1hDNzJZdCtrTktSY1d4RnpMUXlLSjMzbnlQSDloZHFYb1Zob1JScDRWQStqS082RmQiLCJtYWMiOiI1ZTAzOTFiZmY3ZTNlMjEyMmQ3ZWViNDE0ZTAyYWFmNmQyZTA2YTA5YjEwMjIxZGY0YTM2MjMxZTI1MjQwYjYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijd3OWdiQkE1dXIwRXdOL05IeVpXWUE9PSIsInZhbHVlIjoiR21qcS8vcks0WXlVQU1ubXR4SmRQWWM3RmwyK0pyNnBLVFQvbjNuVWtBakY5azFtN2hSSmZFanBZdW1WdDhXRWh3OTJhZ29KV0s4Y0ZaUDRCa2paTnlnSTZiZGpiaHJsbnJ2a0s3WEI4M1VqZWVDNG92SEJBTnhCM1BFYWpGUmtzbzhKWW5kQkk3dlV3bTlQb1E5YkQybE03WUhhYmRKZkd0R3ZwMUw0OWFLZERkYjF2Sk4yclZQRFNLR25PNC9USUUzMDh3SDlrOUJ5ZjE2Vkg4dVpKNGFYdGVJbjYzZ0NmaVU1VEFYUEowL2NuWW51WkVOWWQzRU9EeWdVMVJIQi9SWFR1eVZwL3krQ0R5U1ZNWGpUbDZPSmQ2NVFUSkpLQUEwZmVqZXNEQVhzS0gzK3MvUGxid0RCVmx6TmFQQmwvMkErS2Z2VmF0STBxRkVzY2lCZVRtNm96ZkJ0ZnNlQnJJaWJrK3B0MitoRWVGb2F3QTBkQWxqRlE4ZHM2VEw0RDh5MjZGNll4QjgrQ3IvbWs3SktMbTBIMmpiU3R4clBLbGI5V2tlS0d4R0NaY2VPNkRySjkwOWhTKzJIWVY2eFowVWZNSWF6aWtvb0VRK3YyS3BtWkNROXFIS3M2dytwZmpLaFBxV0dBZldGaEZ6M3R1YVFkVzJNaEh1M0hXdHYiLCJtYWMiOiJhMTUyMWFiOTk3MzFmYThhYzQxYzM0MTU1NTBhZjIzNGQwNTlkMWJhYTg3ODY5MmViNTFiZGEwZTU2MjM2MGRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1276508293 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276508293\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-983174885 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:39:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNVd3VHN3F0UGoxQmpIa0VOZ2dxMkE9PSIsInZhbHVlIjoiYkJvUUNLM3A0R0tNM0VobllkZmZYeHY3K0hwWGd3MW56RXJ0c3k2aEduL2hyWW9nZG9KVGUzbXhHbG9hRkc0dWZJWG14alZCemtyV3RmWC9JYktodk1lUm1PQTBPOHlEWmlvTHJpTUxnR3ZqeE9WYzNPTG5GOTdUenQ1MXlaKzVsKzhkUkplUVB3NC9SeE05WTZkTHJXMDdHZERlUERTenQzbUVmdDVLZUtQOVpRdERmQ2NOODVjSWEvWWFMeXlYaWZCKzFtQTE3RkVTVmVVN3Y2YlBxSkVMcnY0MEs3MFNNN3M5Z3N0RTc5Z3VZWFFReTc1WEowOVhtOWlBb2U4ampHSWJPWE91SnVNVmNic2VEemNGcTZWeUk5MnFDOHFHaDA4cjNzZ3k5NkM5NG1pd0ZFMmhQT3NDd0FPUTdEekNjODZsdXZlbnM5L2NkaGpmZjRMd0tnVkVsQ0JrVmdyZDFEcXNaNURiZ2NiRzl0anBzWENkc2hKMjdJUjVxSWg4MitLZDhWTENCTEE1YWhIY3BOMnZOQlZja0t3WjFoSk9hK3BXc1lnakpwdjJyZ09Ydit2NWJyZ0hJdklTYUZsbXE5RjZSOW9kVTFwc1FRQzZSYUVOd210VnYwOTMzN255cTZraVVRdVZTQVN4YVl6QktwYUoyYVhpb3BNc3hZcWwiLCJtYWMiOiIxZDMwMTMwMTA0YWE0ZDNjZDYxMjEzYzY2Yjk1OWYzOTY5YmU0ZDk5N2IzMDdhYzkyMWZlMDYyNjliYjZiYTk3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFjd01UcGlXQjRNckZZRXMwdy96RVE9PSIsInZhbHVlIjoiSGptNGpDMWpxNVBKOVpIUzVsQ1V0UnlwU3N0VXB0Tnc3UHhaMEtHcTR5WkNLcExmdXZ1UDJCOTRXcm5PTGYzOVhrZWhyeWxCVm85YTBpbXdIM3JiS25WVzdhZGM5dFFFc0tkcTdpNzQydW9hSGZqVExabVRUVncydks0ZURuVWVMVWRDNHVCWFdrdVlzamg3aThWVEh6blFkcnN3Y0JBVTNyNTNtaHEvdG1SU2cxV1B2RWdRVExhME1CVGFzVzc0TnluT09XV0E3cG13d0RhQlJxOTZmUnhyV2ZaT1N6NUtKWm9CMEtKeG5JK09ZdDlFWG1xUzZqVjVMWm9lekFDWEg0cjVJUmtpdVk4Mzk4TmxYVm4rRHpNRldXWGtEeXRqYWRYZnF0cDRyTDRYV3J6RlZ3cCs4YTB4OTRXN1dGbURvcG5CWW9xd3ZmMU5vd0QxQWU2WkFoS05LQ3VMOXdqL0ZMZTJ5cGZqNmlTNHAzZkhyOFV4eWZyeXJtSnp3R3Zub0tlODdSWDFSWVQ0RmR5czlSYmVha1AwVS9kak9xZThBTHd2bUoyNEIrdnNFLy9xS09jZFZXeU45b3RIczVCV0FLa0I3MFk0dk9QT3VIdjlmdEdKTHZkWndVeXluTi9id20wdXk0RG5tcG10dTJmQkk4d0JwYURnRFFmQmZnWm0iLCJtYWMiOiI0NTVlNzFlYzkwYmQ2ZmIzNGU1OTdmMDU2NDdmNDUxZjgyNzNlNTk0YzUyOWFjYWMzMTE4OWQ3N2RmYjgyYjgwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNVd3VHN3F0UGoxQmpIa0VOZ2dxMkE9PSIsInZhbHVlIjoiYkJvUUNLM3A0R0tNM0VobllkZmZYeHY3K0hwWGd3MW56RXJ0c3k2aEduL2hyWW9nZG9KVGUzbXhHbG9hRkc0dWZJWG14alZCemtyV3RmWC9JYktodk1lUm1PQTBPOHlEWmlvTHJpTUxnR3ZqeE9WYzNPTG5GOTdUenQ1MXlaKzVsKzhkUkplUVB3NC9SeE05WTZkTHJXMDdHZERlUERTenQzbUVmdDVLZUtQOVpRdERmQ2NOODVjSWEvWWFMeXlYaWZCKzFtQTE3RkVTVmVVN3Y2YlBxSkVMcnY0MEs3MFNNN3M5Z3N0RTc5Z3VZWFFReTc1WEowOVhtOWlBb2U4ampHSWJPWE91SnVNVmNic2VEemNGcTZWeUk5MnFDOHFHaDA4cjNzZ3k5NkM5NG1pd0ZFMmhQT3NDd0FPUTdEekNjODZsdXZlbnM5L2NkaGpmZjRMd0tnVkVsQ0JrVmdyZDFEcXNaNURiZ2NiRzl0anBzWENkc2hKMjdJUjVxSWg4MitLZDhWTENCTEE1YWhIY3BOMnZOQlZja0t3WjFoSk9hK3BXc1lnakpwdjJyZ09Ydit2NWJyZ0hJdklTYUZsbXE5RjZSOW9kVTFwc1FRQzZSYUVOd210VnYwOTMzN255cTZraVVRdVZTQVN4YVl6QktwYUoyYVhpb3BNc3hZcWwiLCJtYWMiOiIxZDMwMTMwMTA0YWE0ZDNjZDYxMjEzYzY2Yjk1OWYzOTY5YmU0ZDk5N2IzMDdhYzkyMWZlMDYyNjliYjZiYTk3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFjd01UcGlXQjRNckZZRXMwdy96RVE9PSIsInZhbHVlIjoiSGptNGpDMWpxNVBKOVpIUzVsQ1V0UnlwU3N0VXB0Tnc3UHhaMEtHcTR5WkNLcExmdXZ1UDJCOTRXcm5PTGYzOVhrZWhyeWxCVm85YTBpbXdIM3JiS25WVzdhZGM5dFFFc0tkcTdpNzQydW9hSGZqVExabVRUVncydks0ZURuVWVMVWRDNHVCWFdrdVlzamg3aThWVEh6blFkcnN3Y0JBVTNyNTNtaHEvdG1SU2cxV1B2RWdRVExhME1CVGFzVzc0TnluT09XV0E3cG13d0RhQlJxOTZmUnhyV2ZaT1N6NUtKWm9CMEtKeG5JK09ZdDlFWG1xUzZqVjVMWm9lekFDWEg0cjVJUmtpdVk4Mzk4TmxYVm4rRHpNRldXWGtEeXRqYWRYZnF0cDRyTDRYV3J6RlZ3cCs4YTB4OTRXN1dGbURvcG5CWW9xd3ZmMU5vd0QxQWU2WkFoS05LQ3VMOXdqL0ZMZTJ5cGZqNmlTNHAzZkhyOFV4eWZyeXJtSnp3R3Zub0tlODdSWDFSWVQ0RmR5czlSYmVha1AwVS9kak9xZThBTHd2bUoyNEIrdnNFLy9xS09jZFZXeU45b3RIczVCV0FLa0I3MFk0dk9QT3VIdjlmdEdKTHZkWndVeXluTi9id20wdXk0RG5tcG10dTJmQkk4d0JwYURnRFFmQmZnWm0iLCJtYWMiOiI0NTVlNzFlYzkwYmQ2ZmIzNGU1OTdmMDU2NDdmNDUxZjgyNzNlNTk0YzUyOWFjYWMzMTE4OWQ3N2RmYjgyYjgwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983174885\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-260037147 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"228 characters\">http://localhost/user-login/eyJpdiI6Ii8xc1ZuZXhxdTBTM2Y5eUx2WXRodHc9PSIsInZhbHVlIjoiZ24vTGpaZkl0R2hiKytUUUpMYTZmZz09IiwibWFjIjoiNGUxN2I5YWE2NzUxNmRkOTlmNTE4NDEwOTEyMTcwZWEzMTNmZGRmN2EyNWZkNGIyNDE1YTc1MDdiNzBmZDk4YSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"31 characters\">User login enable successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260037147\", {\"maxDepth\":0})</script>\n"}}