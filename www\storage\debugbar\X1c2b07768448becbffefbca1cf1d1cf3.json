{"__meta": {"id": "X1c2b07768448becbffefbca1cf1d1cf3", "datetime": "2025-06-06 20:34:13", "utime": **********.622692, "method": "POST", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242052.168465, "end": **********.622738, "duration": 1.454272985458374, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749242052.168465, "relative_start": 0, "end": **********.434907, "relative_end": **********.434907, "duration": 1.266442060470581, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.434936, "relative_start": 1.2664711475372314, "end": **********.622742, "relative_end": 4.0531158447265625e-06, "duration": 0.1878058910369873, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44989552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=75\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:75-235</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.012549999999999999, "accumulated_duration_str": "12.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.573206, "duration": 0.01091, "duration_str": "10.91ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:78", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=78", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "78"}, "connection": "ty", "start_percent": 0, "width_percent": 86.932}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.596215, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:81", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=81", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "81"}, "connection": "ty", "start_percent": 86.932, "width_percent": 13.068}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"status\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "status": "Your Account is disable from company.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1319181716 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">142</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IkxiQlVxV3ZSMURxZlFEdXg2WC9nU3c9PSIsInZhbHVlIjoiZEZIR3U2bVZPejN6cTdnenZ4QXkrVmJyMlNzVVk3ZDdTc25DdEJBSkFteHdnQ3krRDRoamU5SlJKNVF5dDNEcHdEbER5RDVIZk5XaU9RVjlBeGRwdThXVktzSlIxNjNKSVNkOHNkRTVLeFQ5bVRYRk5mYjVtUDU3cFdxZmZHcFppYmRFZkN6ZGFTdm9VNE1SQmluU0ZwenAvQVRqbFp4RXN4Z2JGeEp0N3BITzNIcTdtK0hLZEdlVnFTWC9lNlM1Q0s0QjNmc3lUWE5QMG5jUVdPMURoczdoSEZITGZzL1hKL3MyTVZUeTdGVGQ4TG5HdXI4RmlKV1FyVzBwWGVzWVBMWWZlVVFlREZqck84YlUyRE1JS2h3Tko5NUMzb2xlN3o4M0RTQm9XWW9kb09FMlRJbHNYOHJJRllFb3d5UnNPMS9jakd4dWhWVUVpWE1hUnBSTVJsZlA5eFRQRElNTllHKzVJV2NpZkdZSVU2UTFYcWRoSGVwbGhyZy8zN3dMWDRnZHBPVnV2dGhaWjRHZnlIZnBWQUtxUGhOUG84V1dITGY3UzA1YmlVU3FEUGV4cE90TjFwcmxpeG5yLzdlK2RCWFlnd3l4d3BiVUlmT2NZMXBMWWVnNHQzdVZOcll4RVpQL2J3WHQyWDR0MGRoUWlxVE10SjBMM3pIaGh1WjQiLCJtYWMiOiJjZjY2MTJkOWJmYmE5OWRlMjgzM2M4MDcyYmJmYjg4ZDA3YTdhYmE1YTBjZmM3NzJjMjgwN2FmMWY4N2YwYjljIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJZSCtIQ3NGWmVuUE50UDc3MG9weEE9PSIsInZhbHVlIjoiUEFDQlV2bDJiSGltY3p6RndBMytJeFdOQnNPdk0wVFhxV3BvME81ZHBPOERGNlVMYWhMdE13Z2MvbFBid3VHcmRxUmlCR2hlTUtoMVNxV2h6TE9vMFhrVWtFbU9YVWxBbCtaYlI3alBZRE4vNFRWYlpFSnlIMDVJSlB5MWdDZW56eldrU0x2UjJXMmVJMlBYTUcrQ0dTcW5nRzYwWWpUa0VqR2JlZWRMVVQ2bjlvTThrdkFRNy9KSFdqdmc0M1NnN05nczFCTUNlemwrOEFxaHdjaHN2dVdXTzU1RW43M1cwblBlSzlQczI0dmtHUmx5SkxtK3N4Y3djdzkvV3AzQmFTYzJyWDFsV0h1Nmg0Z3NUeUFVTHpHaXNzbk5QcDZPdWNoc2hHU2crZ1JON2ozN0s5NytKaTdWNjRidEJWWDJHekNETnlkc1NyTkR0aCtsL3J1T0ZJVDJQcEx2aUgwczJIVHlBVVozbXRQQ0pMdDBSbVFLL3dNR3licndRSGppalNqdUNMQkNaRGxMVkR3bE9Fc2laQnM1Q1lYM1lTMmdFaE43UXBGOGNtTy8yZVJBSGk2dWUzK0JBNlFRU09yZE1sYmZ0TXpFTU5lcDk1czZRVmNyaTVTM3dESjd3WC9SWnJ0R3BJNHROeGUrUE9wSit2SlJUN3pvcG5Xb1daMzkiLCJtYWMiOiJiOTNiOTdmMWVlZThiMzE1MzYzY2I4OGM2Y2E1YWRiNWMxZGI5ZWNjZGNjYzg1YTY1MmM3OTZkNDU4YjZlMDIzIiwidGFnIjoiIn0%3D; _clsk=v1wjex%7C1749242043878%7C2%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319181716\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1317852948 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ib82UhgW7QbmPWC6dET38U6gjVREpdbmeIYDPeql</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317852948\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1692589607 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxOOGRUS1BBZ0FxemtUTExJVnhRZ1E9PSIsInZhbHVlIjoiUWRRNlhpczZ2RjFSKzA3dm9CeXFGVVh5OWlDZE1yQmUrTFkrNW1wQVhGTFRsOVk0Wmp4Zjl1ZkZiVmRobHM1SHVzTWRmbU5WOXBreG1UM0ZIQStYVGVnNm1jelhGSHZ6T0ZudDB0My9BN0FKYkp4WDZCdjYwWEZpVFdOSStPNDJ3dnVnQ0ZwaGdMTDlUSkZXa1BodWRhUGl0dDhKUE1ncDhnVmN1SEtkSjlUU1FDcGZpeHI0dEZabnJJN0ZXRHJkbUlzWEJZRHlpV3drYWE5bzRTRHRsQkFqdEJLdERLOWdrVUlUUHlmQ2hOQ0VWdk5IaG92TlVHcHI1b2xBanFYTmYrNFk0MTNyMzNMWGJYM3JCQ2l3cDhtQWI2ZjZiQURZZnBVcnFzcDh3YmxxQVNHR200NW1ZVlVJRENSUlNZVzQ4SFZuZ0p1NXB4Ymh0SHQrOTRjeTA3aHV5ZUovcUppT3Q2YisyQnhhNDJnUzBFMHQvVmJ0Sm11Y3lxQlNhQXFGeDh0ZEREejlrR2Jna3JBSXQzNEJwbFZTSEd1QzBKWERtTy9tdHBUMDBwOThVbjRnN21mdjBDQ2d0bGx1VTNjKzB1QzVyZTZIdjJkaThtQ0pTOTlSSm94NjJOT3hoNHVrZGE1bFZacTlnRmJmaWdUd0xSNmtqcUVKM1pOMHJsTHoiLCJtYWMiOiIzODUyMmU0MDllMWZlNTQwYWRiNTZmZDBmMTljZTllY2QyMjA0NDFhM2JmNmI1MjlmY2E5Njg1Nzc4ZWE1MTExIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxZNEYyOFdSb0lFZkpyZXpXSTFhdGc9PSIsInZhbHVlIjoiVFhabTBIM3IxVXpSMk00b3hOSEJ1MG5xZHRuU0JudmpHbUNPZlRCVVp3WmV1aVUzMTg0c0pWU3phK3pNOVhVNS9pandSTmoyaEhZK0tpM2ZoZGgwKzM2K1RnZ0oralVlWlZ4aW9ZQkpNN0VsUnVkUitCaFl0b2JyaHNIM0RLRjVsNlZjMEFOM2hhcWJNSzJYU2ptNTIxZnF1UE04WTI1VTZUWjdUajJNMzVjaFE2NVQ1LzhkWVdnTmg3UjJjNUt2UkJnRmVnRGNsUWI3SWJabTZsNUROaTEwK2EwNEorZlpmSEdQMVRRd1RLYUhmSmxNRkFHNWNtWEV1Y3g5NklRbnRKa0d6dTE5T3NjcEdHcE1IREw3aGl4cTVoTmJjazhjRFpvcS96TTdXZFMxcS9qaUpxbTNYd0xqVk8vcTRjak5VUkYvczcxSVlFUzM3OHJxR1pOOUhHVktLMmFKMnBiVzRzYTgvUGFxdFpwYWNTcnlNeXhQVmUvQk5CK3VCOWVBelc4Q0VWeWZmSTAxN2lxc29McHIxcnVlRmkvU0U1RTdrSFVSRVR1QWExWTVaNGtZM3ViRXdNbHNyeGVkc1FFTHBGVjI1WU9Wa0FrbEs2SEs3YXJNZWpoM0MwZGNKWjJaVUpjWVgvTUQ0TjU0NVRUeU5ncVFZNk5WYzVhcFZoNzIiLCJtYWMiOiJjOGQ5MzdjYjVhYjEwYjNhMjNiYTBhNWUzMTE2NjNlNWI1OTMyY2M0MzMxMWUyZDVkZTZkYzFiNDVmNWVhMWEwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxOOGRUS1BBZ0FxemtUTExJVnhRZ1E9PSIsInZhbHVlIjoiUWRRNlhpczZ2RjFSKzA3dm9CeXFGVVh5OWlDZE1yQmUrTFkrNW1wQVhGTFRsOVk0Wmp4Zjl1ZkZiVmRobHM1SHVzTWRmbU5WOXBreG1UM0ZIQStYVGVnNm1jelhGSHZ6T0ZudDB0My9BN0FKYkp4WDZCdjYwWEZpVFdOSStPNDJ3dnVnQ0ZwaGdMTDlUSkZXa1BodWRhUGl0dDhKUE1ncDhnVmN1SEtkSjlUU1FDcGZpeHI0dEZabnJJN0ZXRHJkbUlzWEJZRHlpV3drYWE5bzRTRHRsQkFqdEJLdERLOWdrVUlUUHlmQ2hOQ0VWdk5IaG92TlVHcHI1b2xBanFYTmYrNFk0MTNyMzNMWGJYM3JCQ2l3cDhtQWI2ZjZiQURZZnBVcnFzcDh3YmxxQVNHR200NW1ZVlVJRENSUlNZVzQ4SFZuZ0p1NXB4Ymh0SHQrOTRjeTA3aHV5ZUovcUppT3Q2YisyQnhhNDJnUzBFMHQvVmJ0Sm11Y3lxQlNhQXFGeDh0ZEREejlrR2Jna3JBSXQzNEJwbFZTSEd1QzBKWERtTy9tdHBUMDBwOThVbjRnN21mdjBDQ2d0bGx1VTNjKzB1QzVyZTZIdjJkaThtQ0pTOTlSSm94NjJOT3hoNHVrZGE1bFZacTlnRmJmaWdUd0xSNmtqcUVKM1pOMHJsTHoiLCJtYWMiOiIzODUyMmU0MDllMWZlNTQwYWRiNTZmZDBmMTljZTllY2QyMjA0NDFhM2JmNmI1MjlmY2E5Njg1Nzc4ZWE1MTExIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxZNEYyOFdSb0lFZkpyZXpXSTFhdGc9PSIsInZhbHVlIjoiVFhabTBIM3IxVXpSMk00b3hOSEJ1MG5xZHRuU0JudmpHbUNPZlRCVVp3WmV1aVUzMTg0c0pWU3phK3pNOVhVNS9pandSTmoyaEhZK0tpM2ZoZGgwKzM2K1RnZ0oralVlWlZ4aW9ZQkpNN0VsUnVkUitCaFl0b2JyaHNIM0RLRjVsNlZjMEFOM2hhcWJNSzJYU2ptNTIxZnF1UE04WTI1VTZUWjdUajJNMzVjaFE2NVQ1LzhkWVdnTmg3UjJjNUt2UkJnRmVnRGNsUWI3SWJabTZsNUROaTEwK2EwNEorZlpmSEdQMVRRd1RLYUhmSmxNRkFHNWNtWEV1Y3g5NklRbnRKa0d6dTE5T3NjcEdHcE1IREw3aGl4cTVoTmJjazhjRFpvcS96TTdXZFMxcS9qaUpxbTNYd0xqVk8vcTRjak5VUkYvczcxSVlFUzM3OHJxR1pOOUhHVktLMmFKMnBiVzRzYTgvUGFxdFpwYWNTcnlNeXhQVmUvQk5CK3VCOWVBelc4Q0VWeWZmSTAxN2lxc29McHIxcnVlRmkvU0U1RTdrSFVSRVR1QWExWTVaNGtZM3ViRXdNbHNyeGVkc1FFTHBGVjI1WU9Wa0FrbEs2SEs3YXJNZWpoM0MwZGNKWjJaVUpjWVgvTUQ0TjU0NVRUeU5ncVFZNk5WYzVhcFZoNzIiLCJtYWMiOiJjOGQ5MzdjYjVhYjEwYjNhMjNiYTBhNWUzMTE2NjNlNWI1OTMyY2M0MzMxMWUyZDVkZTZkYzFiNDVmNWVhMWEwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692589607\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hPmhCtd82LHqASZ3oajMGrcCQxTVILTJacBU32UM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Your Account is disable from company.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}