#!/bin/bash

# سكريبت شامل لنشر نظام أوامر الاستلام
# يشمل نقل الملفات وتشغيل الهجرة

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# متغيرات - عدل هذه القيم حسب خادمك
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
PROJECT_PATH="/path/to/your/project"
DB_USER="database_username"
DB_NAME="database_name"

# دالة لطباعة الرسائل الملونة
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

# دالة للتحقق من نجاح الأوامر
check_success() {
    if [ $? -eq 0 ]; then
        print_status "$1 - تم بنجاح"
    else
        print_error "$1 - فشل"
        exit 1
    fi
}

# بداية السكريبت
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    نشر نظام أوامر الاستلام                    ║"
echo "║                  Receipt Orders Deployment                   ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_info "بدء عملية النشر الشاملة..."

# المرحلة 1: إنشاء المجلدات
print_status "📁 المرحلة 1: إنشاء المجلدات المطلوبة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && mkdir -p resources/views/financial_operations/receipt_orders && mkdir -p resources/views/branch_operations/receipt_orders"
check_success "إنشاء المجلدات"

# المرحلة 2: نقل الكونترولرات
print_status "🎮 المرحلة 2: نقل الكونترولرات..."
scp app/Http/Controllers/FinancialReceiptOrderController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "نقل FinancialReceiptOrderController"

scp app/Http/Controllers/BranchReceiptOrderController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "نقل BranchReceiptOrderController"

scp app/Http/Controllers/ReceiptOrderPdfSimpleController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "نقل ReceiptOrderPdfSimpleController"

scp app/Http/Controllers/ReceiptOrderController.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Http/Controllers/
check_success "نقل ReceiptOrderController المحدث"

# المرحلة 3: نقل النماذج
print_status "📊 المرحلة 3: نقل النماذج..."
scp app/Models/ReceiptOrder.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/app/Models/
check_success "نقل نموذج ReceiptOrder"

# المرحلة 4: نقل العروض
print_status "🎨 المرحلة 4: نقل العروض..."

# عروض الأقسام الجديدة
scp resources/views/financial_operations/receipt_orders/index.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/financial_operations/receipt_orders/
check_success "نقل عرض إدارة العمليات المالية"

scp resources/views/branch_operations/receipt_orders/index.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/branch_operations/receipt_orders/
check_success "نقل عرض إدارة عمليات الفروع"

# عروض أوامر الاستلام
scp resources/views/receipt_order/index.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/receipt_order/
check_success "نقل عرض أوامر الاستلام الرئيسي"

scp resources/views/receipt_order/show.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/receipt_order/
check_success "نقل عرض تفاصيل الأمر"

scp resources/views/receipt_order/print.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/receipt_order/
check_success "نقل عرض الطباعة"

scp resources/views/receipt_order/pdf.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/receipt_order/
check_success "نقل قالب PDF المتقدم"

scp resources/views/receipt_order/pdf_simple.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/receipt_order/
check_success "نقل قالب PDF المبسط"

# العروض الأخرى المحدثة
scp resources/views/partials/admin/menu.blade.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/resources/views/partials/admin/
check_success "نقل القائمة الجانبية المحدثة"

# المرحلة 5: نقل المسارات
print_status "🔗 المرحلة 5: نقل المسارات..."
scp routes/web.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/routes/
check_success "نقل ملف المسارات"

# المرحلة 6: نقل ملفات الهجرة
print_status "🗃️ المرحلة 6: نقل ملفات الهجرة..."
scp database/migrations/2024_01_15_000001_create_receipt_orders_table.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/
check_success "نقل ملف هجرة جدول أوامر الاستلام"

scp database/migrations/2024_01_15_000002_create_receipt_order_products_table.php $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/database/migrations/
check_success "نقل ملف هجرة جدول منتجات الأوامر"

scp receipt_orders_migration.sql $SERVER_USER@$SERVER_HOST:$PROJECT_PATH/
check_success "نقل ملف SQL المباشر"

# المرحلة 7: تشغيل الهجرة
print_status "⚡ المرحلة 7: تشغيل هجرة قاعدة البيانات..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate"
check_success "تشغيل الهجرة"

# المرحلة 8: مسح الكاش
print_status "🧹 المرحلة 8: مسح الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan cache:clear && php artisan view:clear && php artisan route:clear && php artisan config:clear"
check_success "مسح الكاش"

# المرحلة 9: إعادة تحميل الكاش
print_status "⚡ المرحلة 9: إعادة تحميل الكاش..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:cache && php artisan config:cache"
check_success "إعادة تحميل الكاش"

# المرحلة 10: ضبط الصلاحيات
print_status "🔐 المرحلة 10: ضبط الصلاحيات..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && chmod -R 755 app/Http/Controllers/ && chmod -R 755 resources/views/ && chmod -R 644 routes/web.php"
check_success "ضبط الصلاحيات"

# المرحلة 11: التحقق من نجاح الهجرة
print_status "✅ المرحلة 11: التحقق من نجاح الهجرة..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan migrate:status | grep receipt"
check_success "التحقق من حالة الهجرة"

# المرحلة 12: اختبار المسارات
print_status "🧪 المرحلة 12: اختبار المسارات..."
ssh $SERVER_USER@$SERVER_HOST "cd $PROJECT_PATH && php artisan route:list | grep receipt"
check_success "التحقق من المسارات"

# النتيجة النهائية
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                        تم النشر بنجاح! 🎉                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

print_status "🎯 النشر مكتمل بنجاح!"
print_info "يمكنك الآن اختبار الروابط التالية:"
echo -e "${BLUE}   - /financial-operations/receipt-orders${NC}"
echo -e "${BLUE}   - /branch-operations/receipt-orders${NC}"
echo -e "${BLUE}   - /receipt-order${NC}"

print_warning "تأكد من اختبار جميع الوظائف قبل الاستخدام الفعلي"

# إحصائيات النشر
echo -e "${GREEN}"
echo "📊 إحصائيات النشر:"
echo "   - الكونترولرات: 4 ملفات"
echo "   - النماذج: 1 ملف"
echo "   - العروض: 8+ ملفات"
echo "   - المسارات: 1 ملف"
echo "   - ملفات الهجرة: 3 ملفات"
echo "   - الجداول المنشأة: 2 جداول"
echo -e "${NC}"

print_status "انتهى النشر في $(date +'%Y-%m-%d %H:%M:%S')"
