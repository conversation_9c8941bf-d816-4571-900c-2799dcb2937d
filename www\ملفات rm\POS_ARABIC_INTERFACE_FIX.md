# إصلاح واجهة POS العربية

## المشكلة الأصلية:
- كانت واجهة POS تعرض النصوص باللغة الإنجليزية
- النص التوجيهي "1. Select warehouse → 2. Scan products → 3. Select customer → 4. Pay" يظهر بالإنجليزية
- نص placeholder في حقل البحث يظهر بالإنجليزية

## الحلول المطبقة:

### 1. تحديث النص التوجيهي في POS
**الملف**: `resources/views/pos/index.blade.php`
**السطر**: 369

**قبل التعديل**:
```html
<small class="text-muted">{{ __('1. Select warehouse → 2. Scan products → 3. Select customer → 4. Pay') }}</small>
```

**بعد التعديل**:
```html
<small class="text-muted">{{ __('1. <PERSON>ختر المستودع → 2. <PERSON><PERSON><PERSON><PERSON> المنتجات → 3. اختر العميل → 4. ادفع') }}</small>
```

### 2. تحديث نص placeholder في حقل البحث
**الملف**: `resources/views/pos/index.blade.php`
**السطر**: 359

**قبل التعديل**:
```html
placeholder="{{ __('Scan Barcode or Enter SKU - Product will be added to cart automatically') }}"
```

**بعد التعديل**:
```html
placeholder="{{ __('امسح الباركود أو أدخل الرمز التعريفي - سيتم إضافة المنتج للسلة تلقائياً') }}"
```

### 3. إضافة الترجمات العربية
**الملف**: `resources/lang/ar.json`

**الترجمات المضافة**:
```json
{
    "1. اختر المستودع → 2. امسح المنتجات → 3. اختر العميل → 4. ادفع": "1. اختر المستودع → 2. امسح المنتجات → 3. اختر العميل → 4. ادفع",
    "1. Select warehouse → 2. Scan products → 3. Select customer → 4. Pay": "1. اختر المستودع → 2. امسح المنتجات → 3. اختر العميل → 4. ادفع",
    "امسح الباركود أو أدخل الرمز التعريفي - سيتم إضافة المنتج للسلة تلقائياً": "امسح الباركود أو أدخل الرمز التعريفي - سيتم إضافة المنتج للسلة تلقائياً",
    "Scan Barcode or Enter SKU - Product will be added to cart automatically": "امسح الباركود أو أدخل الرمز التعريفي - سيتم إضافة المنتج للسلة تلقائياً"
}
```

## النتيجة المتوقعة:
- ✅ النص التوجيهي سيظهر باللغة العربية: "1. اختر المستودع → 2. امسح المنتجات → 3. اختر العميل → 4. ادفع"
- ✅ نص placeholder في حقل البحث سيظهر باللغة العربية
- ✅ واجهة POS ستكون أكثر وضوحاً للمستخدمين العرب

## ملاحظات:
- تم الحفاظ على وظائف POS الأساسية دون تغيير
- التحديثات تؤثر فقط على النصوص المعروضة
- يمكن إضافة المزيد من الترجمات حسب الحاجة

## الملفات المحدثة:
1. `resources/views/pos/index.blade.php` - تحديث النصوص في الواجهة
2. `resources/lang/ar.json` - إضافة الترجمات العربية

## اختبار التحديثات:
1. افتح صفحة POS
2. تأكد من ظهور النص التوجيهي باللغة العربية
3. تأكد من ظهور placeholder حقل البحث باللغة العربية
4. تأكد من عمل جميع وظائف POS بشكل طبيعي
