<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('تحليل المبيعات المتقدم')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="#"><?php echo e(__('إدارة العمليات المالية')); ?></a></li>
    <li class="breadcrumb-item active"><?php echo e(__('تحليل المبيعات المتقدم')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <div class="d-flex align-items-center">
            <!-- أيقونة تحليل أداء المنتجات -->
            <div class="me-3">
                <a href="<?php echo e(route('financial.product.analytics.index')); ?>" class="btn btn-success" title="تحليل أداء المنتجات">
                    <i class="fas fa-chart-bar me-2"></i><?php echo e(__('تحليل أداء المنتجات')); ?>

                </a>
            </div>

            <!-- فلتر المستودع -->
            <div class="me-3">
                <select class="form-select" id="warehouse-filter" style="min-width: 200px;">
                    <option value=""><?php echo e(__('جميع المستودعات')); ?></option>
                    <?php $__currentLoopData = $warehouses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($warehouse->id); ?>"><?php echo e($warehouse->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <!-- فلتر التاريخ -->
            <div class="me-3">
                <input type="date" class="form-control" id="date-from" value="<?php echo e(date('Y-m-01')); ?>">
            </div>
            <div class="me-3">
                <input type="date" class="form-control" id="date-to" value="<?php echo e(date('Y-m-t')); ?>">
            </div>
            
            <!-- زر التحديث -->
            <button class="btn btn-primary" id="refresh-data">
                <i class="fas fa-sync-alt"></i> <?php echo e(__('تحديث البيانات')); ?>

            </button>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- الرؤى والتنبيهات -->
    <?php if($unreadInsights->count() > 0): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <h5 class="alert-heading">
                    <i class="fas fa-lightbulb me-2"></i>
                    <?php echo e(__('رؤى ذكية جديدة')); ?>

                </h5>
                <div class="row">
                    <?php $__currentLoopData = $unreadInsights; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $insight): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-<?php echo e($insight->insight_color); ?> me-2">
                                <?php echo e($insight->insight_type_name); ?>

                            </span>
                            <small class="text-muted"><?php echo e($insight->title); ?></small>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- التبويبات الرئيسية -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-pills card-header-pills" id="sales-analytics-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="realtime-tab" data-bs-toggle="pill" data-bs-target="#realtime" type="button" role="tab">
                                <i class="fas fa-chart-line me-2"></i><?php echo e(__('المبيعات المباشرة')); ?>

                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="customers-tab" data-bs-toggle="pill" data-bs-target="#customers" type="button" role="tab">
                                <i class="fas fa-users me-2"></i><?php echo e(__('تحليل العملاء')); ?>

                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="products-tab" data-bs-toggle="pill" data-bs-target="#products" type="button" role="tab">
                                <i class="fas fa-boxes me-2"></i><?php echo e(__('أداء المنتجات')); ?>

                            </button>
                        </li>


                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="sales-analytics-content">
                        <!-- تبويب المبيعات المباشرة -->
                        <div class="tab-pane fade show active" id="realtime" role="tabpanel">
                            <div id="realtime-loading" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                                </div>
                            </div>
                            <div id="realtime-content" style="display: none;">
                                <!-- إحصائيات سريعة -->
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-primary text-white h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="mb-1" id="today-sales">0</h4>
                                                        <p class="mb-0"><?php echo e(__('مبيعات اليوم')); ?></p>
                                                        <small id="today-amount">0 ر.س</small>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-chart-line fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <small>
                                                        <span id="daily-growth" class="me-1">0%</span>
                                                        <span><?php echo e(__('مقارنة بالأمس')); ?></span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-success text-white h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="mb-1" id="hour-sales">0</h4>
                                                        <p class="mb-0"><?php echo e(__('مبيعات الساعة')); ?></p>
                                                        <small id="hour-amount">0 ر.س</small>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-clock fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <small id="last-updated"><?php echo e(__('آخر تحديث:')); ?> --:--:--</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-info text-white h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="mb-1" id="week-sales">0</h4>
                                                        <p class="mb-0"><?php echo e(__('مبيعات الأسبوع')); ?></p>
                                                        <small id="week-amount">0 ر.س</small>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-calendar-week fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-warning text-white h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="mb-1" id="month-sales">0</h4>
                                                        <p class="mb-0"><?php echo e(__('مبيعات الشهر')); ?></p>
                                                        <small id="month-amount">0 ر.س</small>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="fas fa-calendar-alt fa-2x"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الهدف اليومي -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-bullseye me-2"></i>
                                                    <?php echo e(__('الهدف اليومي')); ?>

                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row align-items-center">
                                                    <div class="col-md-8">
                                                        <div class="progress" style="height: 25px;">
                                                            <div class="progress-bar bg-success" role="progressbar" 
                                                                 id="target-progress" style="width: 0%" 
                                                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                                <span id="target-percentage">0%</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 text-end">
                                                        <h5 class="mb-0">
                                                            <span id="target-current">0</span> / <span id="target-goal">0</span> ر.س
                                                        </h5>
                                                        <small class="text-muted"><?php echo e(__('المحقق / الهدف')); ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الرسم البياني للمبيعات بالساعة -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-chart-area me-2"></i>
                                                    <?php echo e(__('المبيعات خلال آخر 24 ساعة')); ?>

                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="hourly-sales-chart" height="100"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب تحليل العملاء -->
                        <div class="tab-pane fade" id="customers" role="tabpanel">
                            <div id="customers-loading" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                                </div>
                            </div>
                            <div id="customers-content" style="display: none;">
                                <!-- إحصائيات العملاء -->
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h3 id="total-customers">0</h3>
                                                <p class="mb-0">إجمالي العملاء</p>
                                                <small class="text-light">في النظام</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h3 id="active-customers">0</h3>
                                                <p class="mb-0">عملاء نشطون</p>
                                                <small class="text-light">في الفترة المحددة</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h3 id="new-customers">0</h3>
                                                <p class="mb-0">عملاء جدد</p>
                                                <small class="text-light">أول مرة يشترون</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <h3 id="repeat-customers">0</h3>
                                                <p class="mb-0">عملاء متكررون</p>
                                                <small class="text-light">أكثر من طلب واحد</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- إحصائيات مالية للعملاء -->
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card border-primary">
                                            <div class="card-body text-center">
                                                <h4 id="total-revenue" class="text-primary">0 ر.س</h4>
                                                <p class="mb-0">إجمالي الإيرادات</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card border-success">
                                            <div class="card-body text-center">
                                                <h4 id="avg-order-value" class="text-success">0 ر.س</h4>
                                                <p class="mb-0">متوسط قيمة الطلب</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card border-warning">
                                            <div class="card-body text-center">
                                                <h4 id="avg-customer-value" class="text-warning">0 ر.س</h4>
                                                <p class="mb-0">متوسط قيمة العميل</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card border-info">
                                            <div class="card-body text-center">
                                                <h4 id="total-orders" class="text-info">0</h4>
                                                <p class="mb-0">إجمالي الطلبات</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- جدول أفضل العملاء -->
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="fas fa-crown me-2"></i>أفضل العملاء (حسب إجمالي الإنفاق)</h5>
                                        <small class="text-muted" id="customers-period-info">الفترة: --</small>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover" id="top-customers-table">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>#</th>
                                                        <th>اسم العميل</th>
                                                        <th>رقم الهاتف</th>
                                                        <th>عدد الطلبات</th>
                                                        <th>إجمالي الإنفاق</th>
                                                        <th>متوسط الطلب</th>
                                                        <th>أول شراء</th>
                                                        <th>آخر شراء</th>
                                                        <th>نوع العميل</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td colspan="9" class="text-center py-4">
                                                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                            جاري تحميل بيانات العملاء...
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب أداء المنتجات -->
                        <div class="tab-pane fade" id="products" role="tabpanel">
                            <div id="products-loading" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden"><?php echo e(__('جاري التحميل...')); ?></span>
                                </div>
                            </div>
                            <div id="products-content" style="display: none;">
                                <!-- إحصائيات المنتجات -->
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h3 id="total-products">0</h3>
                                                <p class="mb-0">إجمالي المنتجات</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h3 id="products-sold">0</h3>
                                                <p class="mb-0">منتجات مباعة</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h3 id="slow-products">0</h3>
                                                <p class="mb-0">منتجات بطيئة</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <h3 id="coverage-percent">0%</h3>
                                                <p class="mb-0">نسبة التغطية</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- إحصائيات تواريخ الصلاحية -->
                                <div class="row mb-4">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-danger text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                                <h3 id="expired-products">0</h3>
                                                <p class="mb-0">منتهية الصلاحية</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-clock fa-2x mb-2"></i>
                                                <h3 id="high-risk-products">0</h3>
                                                <p class="mb-0">خطر عالي (7 أيام)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-orange text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-hourglass-half fa-2x mb-2"></i>
                                                <h3 id="medium-risk-products">0</h3>
                                                <p class="mb-0">خطر متوسط (15 يوم)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <div class="card bg-secondary text-white">
                                            <div class="card-body text-center">
                                                <i class="fas fa-bell fa-2x mb-2"></i>
                                                <h3 id="warning-products">0</h3>
                                                <p class="mb-0">تحذير (30 يوم)</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- جدول أفضل المنتجات -->
                                <div class="row">
                                    <div class="col-lg-8">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-star me-2"></i>أفضل المنتجات مبيعاً
                                                    <button type="button" class="btn btn-sm btn-outline-info ms-2" data-bs-toggle="modal" data-bs-target="#ordersExplanationModal">
                                                        <i class="fas fa-question-circle me-1"></i>ما معنى عدد الطلبات؟
                                                    </button>
                                                </h5>
                                                <small class="text-muted" id="products-period-info">الفترة: --</small>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-hover" id="top-products-table">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>#</th>
                                                                <th>اسم المنتج</th>
                                                                <th>الباركود</th>
                                                                <th>الكمية المباعة</th>
                                                                <th>إجمالي الإيرادات</th>
                                                                <th>
                                                                    عدد الطلبات
                                                                    <button type="button" class="btn btn-sm btn-link p-0 ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="عدد الفواتير المختلفة التي تحتوي على هذا المنتج. مؤشر على شعبية المنتج بين العملاء">
                                                                        <i class="fas fa-info-circle text-muted"></i>
                                                                    </button>
                                                                </th>
                                                                <th>تاريخ الصلاحية</th>
                                                                <th>حالة الصلاحية</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="8" class="text-center py-4">
                                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                                    جاري تحميل بيانات المنتجات...
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-clock me-2 text-warning"></i>المنتجات قريبة الانتهاء
                                                    <small class="text-muted">(خلال 30 يوم)</small>
                                                </h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-sm" id="expiring-products-table">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>المنتج</th>
                                                                <th>المخزون</th>
                                                                <th>الأيام المتبقية</th>
                                                                <th>المستوى</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="4" class="text-center py-3">
                                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                                    جاري التحميل...
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>


    </div>
<!-- نافذة شرح عدد الطلبات -->
<div class="modal fade" id="ordersExplanationModal" tabindex="-1" aria-labelledby="ordersExplanationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="ordersExplanationModalLabel">
                    <i class="fas fa-shopping-cart me-2"></i>شرح مفهوم "عدد الطلبات"
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-lightbulb me-2"></i>ما المقصود بـ "عدد الطلبات"؟
                        </h6>
                        <div class="alert alert-light border">
                            <div class="text-center mb-3">
                                <h5 class="text-dark">عدد الطلبات = <span class="text-success">عدد الفواتير المختلفة</span> التي تحتوي على هذا المنتج</h5>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">
                            <i class="fas fa-calculator me-2"></i>مثال توضيحي:
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">منتج: شاي أحمد</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="mb-0">
                                            <li><strong>الفاتورة #001:</strong> 5 علب شاي</li>
                                            <li><strong>الفاتورة #003:</strong> 2 علب شاي</li>
                                            <li><strong>الفاتورة #007:</strong> 10 علب شاي</li>
                                            <li><strong>الفاتورة #012:</strong> 3 علب شاي</li>
                                        </ul>
                                        <hr>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <h5 class="text-success">20</h5>
                                                <small>الكمية الإجمالية</small>
                                            </div>
                                            <div class="col-6">
                                                <h5 class="text-primary">4</h5>
                                                <small>عدد الطلبات</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">منتج: قهوة خاصة</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="mb-0">
                                            <li><strong>الفاتورة #005:</strong> 50 كيس قهوة</li>
                                        </ul>
                                        <hr>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <h5 class="text-success">50</h5>
                                                <small>الكمية الإجمالية</small>
                                            </div>
                                            <div class="col-6">
                                                <h5 class="text-primary">1</h5>
                                                <small>عدد الطلبات</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3 mt-4">
                            <i class="fas fa-chart-line me-2"></i>لماذا هذا المؤشر مهم؟
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-fire text-danger fa-2x mb-2"></i>
                                    <h6>شعبية المنتج</h6>
                                    <p class="small mb-0">عدد طلبات عالي = منتج مطلوب من عملاء كثيرين</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-users text-info fa-2x mb-2"></i>
                                    <h6>نوع الشراء</h6>
                                    <p class="small mb-0">طلبات كثيرة = تجزئة<br>طلبات قليلة = جملة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-chart-bar text-success fa-2x mb-2"></i>
                                    <h6>استقرار المبيعات</h6>
                                    <p class="small mb-0">طلبات منتظمة = مبيعات مستقرة</p>
                                </div>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3 mt-4">
                            <i class="fas fa-lightbulb me-2"></i>أمثلة من الواقع:
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>الطلبات</th>
                                        <th>التفسير</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="table-success">
                                        <td><strong>أرز بسمتي</strong></td>
                                        <td>200 كيس</td>
                                        <td>85 طلب</td>
                                        <td><span class="badge bg-success">منتج شعبي ومستقر</span></td>
                                    </tr>
                                    <tr class="table-info">
                                        <td><strong>كرتون عصائر</strong></td>
                                        <td>150 كرتون</td>
                                        <td>8 طلبات</td>
                                        <td><span class="badge bg-info">مبيعات جملة</span></td>
                                    </tr>
                                    <tr class="table-warning">
                                        <td><strong>حلويات خاصة</strong></td>
                                        <td>50 علبة</td>
                                        <td>3 طلبات</td>
                                        <td><span class="badge bg-warning">منتج موسمي</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h6 class="text-primary mb-3">
                            <i class="fas fa-target me-2"></i>كيف تستفيد من هذا المؤشر؟
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">طلبات كثيرة (شعبي)</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="mb-0 small">
                                            <li>ركز عليه في الإعلانات</li>
                                            <li>احتفظ بمخزون ثابت</li>
                                            <li>منتج أساسي لا يجب نفاده</li>
                                            <li>يمكن زيادة السعر قليلاً</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">طلبات قليلة (محدود)</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="mb-0 small">
                                            <li>يحتاج ترويج أكثر</li>
                                            <li>لا تكدس المخزون</li>
                                            <li>منتج اختياري</li>
                                            <li>قد تحتاج تخفيض السعر</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-info" onclick="window.open('https://ar.wikipedia.org/wiki/إدارة_المبيعات', '_blank')">
                    <i class="fas fa-external-link-alt me-2"></i>اقرأ المزيد
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<style>
    .bg-orange {
        background-color: #fd7e14 !important;
    }
    .text-orange {
        color: #fd7e14 !important;
    }
    .card.bg-orange {
        background-color: #fd7e14 !important;
        color: white !important;
    }
    .badge.bg-orange {
        background-color: #fd7e14 !important;
        color: white !important;
    }

    /* تحسين مظهر البطاقات */
    .card.bg-danger, .card.bg-warning, .card.bg-orange, .card.bg-secondary {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    /* تحسين مظهر الجداول */
    .table-responsive {
        border-radius: 0.375rem;
        overflow: hidden;
    }

    .badge.small {
        font-size: 0.7em;
    }
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    let hourlySalesChart = null;
    let autoRefreshInterval = null;

    // تحديد القيم المختارة في الفلاتر من URL
    const urlParams = new URLSearchParams(window.location.search);
    const warehouseId = urlParams.get('warehouse_id');
    const date = urlParams.get('date');

    if (warehouseId) {
        $('#warehouse-filter').val(warehouseId);
    }
    if (date) {
        $('#date-from').val(date);
    }

    // عرض البيانات المحملة مسبقاً من الكونترولر (مثل invoice-processor)
    <?php if(isset($realtimeData)): ?>
        updateRealtimeDashboard(<?php echo json_encode($realtimeData, 15, 512) ?>);
        $('#realtime-loading').hide();
        $('#realtime-content').show();
    <?php endif; ?>

    // تحميل البيانات عند تحميل الصفحة
    loadRealtimeDashboard();

    // أحداث الفلاتر
    $('#refresh-data').on('click', function() {
        loadActiveTab();
    });

    $('#warehouse-filter, #date-from, #date-to').on('change', function() {
        // إعادة تحميل الصفحة مع المعاملات الجديدة للتبويب الرئيسي
        const warehouseId = $('#warehouse-filter').val();
        const dateFrom = $('#date-from').val();
        const dateTo = $('#date-to').val();

        // بناء URL جديد مع المعاملات
        const currentUrl = new URL(window.location.href);
        if (warehouseId) {
            currentUrl.searchParams.set('warehouse_id', warehouseId);
        } else {
            currentUrl.searchParams.delete('warehouse_id');
        }
        if (dateFrom) {
            currentUrl.searchParams.set('date', dateFrom);
        }

        // إعادة تحميل الصفحة مع المعاملات الجديدة
        window.location.href = currentUrl.toString();
    });

    // أحداث التبويبات
    $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function (e) {
        const target = $(e.target).data('bs-target');
        
        if (target === '#realtime') {
            loadRealtimeDashboard();
            startAutoRefresh();
        } else {
            stopAutoRefresh();
            
            if (target === '#customers') {
                loadCustomerAnalytics();
            } else if (target === '#products') {
                loadProductPerformance();
            }
        }
    });

    // بدء التحديث التلقائي للمبيعات المباشرة (محسن)
    function startAutoRefresh() {
        stopAutoRefresh(); // إيقاف أي تحديث سابق
        autoRefreshInterval = setInterval(function() {
            if ($('#realtime').hasClass('active')) {
                loadRealtimeDashboard(false); // تحديث صامت بدون مؤشر التحميل
            }
            // تجنب التحديث التلقائي لتبويب اتجاهات المبيعات لتحسين الأداء
        }, 60000); // كل دقيقة بدلاً من 30 ثانية
    }

    // إيقاف التحديث التلقائي
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    // تحميل التبويب النشط
    function loadActiveTab() {
        const activeTab = $('.nav-link.active').data('bs-target');
        
        if (activeTab === '#realtime') {
            loadRealtimeDashboard();
        } else if (activeTab === '#customers') {
            loadCustomerAnalytics();
        } else if (activeTab === '#products') {
            loadProductPerformance();
        }
    }

    // تحميل بيانات المبيعات المباشرة
    function loadRealtimeDashboard(showLoading = true) {
        if (showLoading) {
            $('#realtime-loading').show();
            $('#realtime-content').hide();
        }

        $.ajax({
            url: '<?php echo e(route("financial.sales.analytics.realtime")); ?>',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                date: $('#date-from').val()
            },
            success: function(response) {
                if (response.success) {
                    updateRealtimeDashboard(response.data);
                    if (showLoading) {
                        $('#realtime-loading').hide();
                        $('#realtime-content').show();
                    }
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات المبيعات المباشرة:', xhr.responseText);
                if (showLoading) {
                    $('#realtime-loading').hide();
                    $('#realtime-content').show();
                }
            }
        });
    }

    // تحديث واجهة المبيعات المباشرة
    function updateRealtimeDashboard(data) {
        // تحديث الإحصائيات
        $('#today-sales').text(data.today.sales);
        $('#today-amount').text(data.today.amount + ' ر.س');
        $('#daily-growth').text(data.today.growth + '%');
        
        $('#hour-sales').text(data.current_hour.sales);
        $('#hour-amount').text(data.current_hour.amount + ' ر.س');
        
        $('#week-sales').text(data.week.sales);
        $('#week-amount').text(data.week.amount + ' ر.س');
        
        $('#month-sales').text(data.month.sales);
        $('#month-amount').text(data.month.amount + ' ر.س');
        
        $('#last-updated').text('آخر تحديث: ' + data.last_updated);

        // عرض معلومات التشخيص إذا كانت متوفرة
        if (data.debug_info) {
            console.log('🔍 معلومات التشخيص:', data.debug_info);

            // إضافة معلومات التشخيص في أسفل الصفحة
            let debugHtml = '<div class="alert alert-info mt-3" style="font-size: 12px;">';
            debugHtml += '<strong>🔍 معلومات التشخيص:</strong><br>';
            debugHtml += 'إجمالي السجلات: ' + (data.debug_info.total_pos_records || 0) + '<br>';
            debugHtml += 'سجلات اليوم: ' + (data.debug_info.today_records || 0) + '<br>';
            debugHtml += 'طريقة الجلب: ' + (data.debug_info.method || 'غير محدد') + '<br>';
            debugHtml += 'فلتر المستودع: ' + (data.debug_info.warehouse_filter || 'غير مطبق') + '<br>';
            if (data.debug_info.error) {
                debugHtml += '<span class="text-danger">خطأ: ' + data.debug_info.error + '</span><br>';
            }
            debugHtml += '</div>';

            // إضافة أو تحديث معلومات التشخيص
            if ($('#debug-info').length) {
                $('#debug-info').html(debugHtml);
            } else {
                $('#realtime-content').append('<div id="debug-info">' + debugHtml + '</div>');
            }
        }

        // تحديث الهدف اليومي
        $('#target-current').text(data.today.amount);
        $('#target-goal').text(data.today.target);
        $('#target-percentage').text(data.today.achievement + '%');
        $('#target-progress').css('width', Math.min(data.today.achievement, 100) + '%');

        // تحديث الرسم البياني
        updateHourlySalesChart(data.hourly_chart);
    }

    // تحديث الرسم البياني للمبيعات بالساعة
    function updateHourlySalesChart(data) {
        const ctx = document.getElementById('hourly-sales-chart').getContext('2d');
        
        if (hourlySalesChart) {
            hourlySalesChart.destroy();
        }

        hourlySalesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.hour),
                datasets: [{
                    label: 'المبيعات (ر.س)',
                    data: data.map(item => item.amount),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                }
            }
        });
    }

    // تحميل بيانات تحليل العملاء
    function loadCustomerAnalytics() {
        $('#customers-loading').show();
        $('#customers-content').hide();

        $.ajax({
            url: '<?php echo e(route("financial.sales.analytics.customers")); ?>',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                date_from: $('#date-from').val(),
                date_to: $('#date-to').val()
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث إحصائيات العملاء الأساسية
                    $('#total-customers').text(data.total_customers || 0);
                    $('#active-customers').text(data.active_customers || 0);
                    $('#new-customers').text(data.new_customers || 0);
                    $('#repeat-customers').text(data.customer_stats?.repeat_customers || 0);

                    // تحديث الإحصائيات المالية
                    $('#total-revenue').text(parseFloat(data.customer_stats?.total_revenue || 0).toLocaleString('ar-SA') + ' ر.س');
                    $('#avg-order-value').text(parseFloat(data.customer_stats?.avg_order_value || 0).toFixed(2) + ' ر.س');
                    $('#avg-customer-value').text(parseFloat(data.customer_stats?.avg_customer_value || 0).toFixed(2) + ' ر.س');

                    // حساب إجمالي الطلبات
                    const totalOrders = data.top_customers ? data.top_customers.reduce((sum, customer) => sum + parseInt(customer.total_orders || 0), 0) : 0;
                    $('#total-orders').text(totalOrders.toLocaleString('ar-SA'));

                    // تحديث معلومات الفترة
                    if (data.period) {
                        const periodText = `من ${data.period.from} إلى ${data.period.to}`;
                        $('#customers-period-info').text('الفترة: ' + periodText);
                    }

                    // تحديث جدول أفضل العملاء
                    let customersTableHtml = '';
                    if (data.top_customers && data.top_customers.length > 0) {
                        data.top_customers.forEach((customer, index) => {
                            // تحديد نوع العميل
                            const customerType = parseInt(customer.total_orders) > 1 ?
                                '<span class="badge bg-success">متكرر</span>' :
                                '<span class="badge bg-warning">جديد</span>';

                            // تنسيق التواريخ
                            const firstPurchase = customer.first_purchase_date ?
                                new Date(customer.first_purchase_date).toLocaleDateString('ar-SA') : '--';
                            const lastPurchase = customer.last_purchase_date ?
                                new Date(customer.last_purchase_date).toLocaleDateString('ar-SA') : '--';

                            customersTableHtml += `
                                <tr>
                                    <td><strong>${index + 1}</strong></td>
                                    <td>
                                        <div class="fw-bold">${customer.name || 'غير محدد'}</div>
                                        ${customer.email ? `<small class="text-muted">${customer.email}</small>` : ''}
                                    </td>
                                    <td>${customer.contact || '--'}</td>
                                    <td><span class="badge bg-primary">${customer.total_orders || 0}</span></td>
                                    <td class="fw-bold text-success">${parseFloat(customer.total_spent || 0).toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
                                    <td>${parseFloat(customer.avg_order_value || 0).toFixed(2)} ر.س</td>
                                    <td><small>${firstPurchase}</small></td>
                                    <td><small>${lastPurchase}</small></td>
                                    <td>${customerType}</td>
                                </tr>
                            `;
                        });
                    } else {
                        customersTableHtml = '<tr><td colspan="9" class="text-center py-4">لا توجد بيانات للعملاء في الفترة المحددة</td></tr>';
                    }
                    $('#top-customers-table tbody').html(customersTableHtml);

                    $('#customers-loading').hide();
                    $('#customers-content').show();
                } else {
                    $('#customers-loading').hide();
                    $('#customers-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في تحميل بيانات العملاء: ${response.message}
                        </div>
                    `).show();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات تحليل العملاء:', xhr.responseText);
                $('#customers-loading').hide();
                $('#customers-content').show();
            }
        });
    }

    // تحميل بيانات أداء المنتجات
    function loadProductPerformance() {
        $('#products-loading').show();
        $('#products-content').hide();

        $.ajax({
            url: '<?php echo e(route("financial.sales.analytics.products")); ?>',
            method: 'GET',
            data: {
                warehouse_id: $('#warehouse-filter').val(),
                date_from: $('#date-from').val(),
                date_to: $('#date-to').val()
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // تحديث إحصائيات المنتجات
                    $('#total-products').text(data.total_products || 0);
                    $('#products-sold').text(data.products_with_sales || 0);
                    $('#slow-products').text(data.products_without_sales || 0);
                    $('#coverage-percent').text((data.sales_coverage || 0) + '%');

                    // تحديث إحصائيات تواريخ الصلاحية
                    if (data.expiry_statistics) {
                        $('#expired-products').text(data.expiry_statistics.expired || 0);
                        $('#high-risk-products').text(data.expiry_statistics.high_risk || 0);
                        $('#medium-risk-products').text(data.expiry_statistics.medium_risk || 0);
                        $('#warning-products').text(data.expiry_statistics.warning || 0);
                    }

                    // تحديث معلومات الفترة
                    const dateFrom = $('#date-from').val();
                    const dateTo = $('#date-to').val();
                    if (dateFrom && dateTo) {
                        const periodText = `من ${dateFrom} إلى ${dateTo}`;
                        $('#products-period-info').text('الفترة: ' + periodText);
                    }

                    // تحديث جدول أفضل المنتجات
                    let productsTableHtml = '';
                    if (data.top_selling_products && data.top_selling_products.length > 0) {
                        data.top_selling_products.forEach((product, index) => {
                            // تحديد حالة الصلاحية
                            let expiryDisplay = 'لا يوجد تاريخ';
                            let expiryBadge = 'bg-secondary';
                            let expiryStatus = product.expiry_status || 'لا يوجد تاريخ انتهاء';

                            if (product.expiry_date) {
                                expiryDisplay = new Date(product.expiry_date).toLocaleDateString('ar-SA');

                                switch(expiryStatus) {
                                    case 'منتهي الصلاحية':
                                        expiryBadge = 'bg-danger';
                                        break;
                                    case 'خطر عالي':
                                        expiryBadge = 'bg-warning';
                                        break;
                                    case 'تحذير':
                                        expiryBadge = 'bg-info';
                                        break;
                                    case 'صالح':
                                        expiryBadge = 'bg-success';
                                        break;
                                    default:
                                        expiryBadge = 'bg-secondary';
                                }
                            }

                            productsTableHtml += `
                                <tr>
                                    <td><strong>${index + 1}</strong></td>
                                    <td>
                                        <div class="fw-bold">${product.name || 'غير محدد'}</div>
                                        ${product.category_name ? `<small class="text-muted">${product.category_name}</small>` : ''}
                                    </td>
                                    <td><code>${product.sku || '--'}</code></td>
                                    <td><span class="badge bg-primary">${parseInt(product.total_quantity || 0).toLocaleString('ar-SA')}</span></td>
                                    <td class="fw-bold text-success">${parseFloat(product.total_revenue || 0).toLocaleString('ar-SA', {minimumFractionDigits: 2})} ر.س</td>
                                    <td><span class="badge bg-info">${product.order_count || 0}</span></td>
                                    <td>
                                        <div class="small">${expiryDisplay}</div>
                                        ${product.days_to_expiry !== null && product.days_to_expiry !== undefined ?
                                            `<small class="text-muted">(${product.days_to_expiry} يوم)</small>` : ''}
                                    </td>
                                    <td><span class="badge ${expiryBadge}">${expiryStatus}</span></td>
                                </tr>
                            `;
                        });
                    } else {
                        productsTableHtml = '<tr><td colspan="8" class="text-center py-4">لا توجد بيانات للمنتجات في الفترة المحددة</td></tr>';
                    }
                    $('#top-products-table tbody').html(productsTableHtml);

                    // تحديث جدول المنتجات قريبة الانتهاء
                    let expiringProductsHtml = '';
                    if (data.expiring_products && data.expiring_products.length > 0) {
                        data.expiring_products.forEach(product => {
                            const daysToExpiry = parseInt(product.days_to_expiry || 0);
                            const currentStock = parseInt(product.current_stock || 0);
                            let riskBadge = 'bg-secondary';
                            let riskText = product.risk_level || 'غير محدد';

                            // تحديد لون المستوى حسب الخطر
                            switch(product.risk_level) {
                                case 'منتهي الصلاحية':
                                    riskBadge = 'bg-danger text-white';
                                    break;
                                case 'خطر عالي':
                                    riskBadge = 'bg-warning text-dark';
                                    break;
                                case 'خطر متوسط':
                                    riskBadge = 'bg-orange text-white';
                                    break;
                                case 'تحذير':
                                    riskBadge = 'bg-info text-white';
                                    break;
                                default:
                                    riskBadge = 'bg-secondary text-white';
                            }

                            // تحديد لون المخزون
                            let stockBadge = 'bg-primary';
                            if (currentStock === 0) {
                                stockBadge = 'bg-danger';
                            } else if (currentStock <= 5) {
                                stockBadge = 'bg-warning text-dark';
                            } else {
                                stockBadge = 'bg-success';
                            }

                            expiringProductsHtml += `
                                <tr>
                                    <td>
                                        <div class="fw-bold small">${product.name || 'غير محدد'}</div>
                                        <small class="text-muted">${product.sku || '--'}</small>
                                    </td>
                                    <td><span class="badge ${stockBadge}">${currentStock}</span></td>
                                    <td>
                                        <span class="fw-bold ${daysToExpiry <= 0 ? 'text-danger' : daysToExpiry <= 7 ? 'text-warning' : 'text-info'}">${daysToExpiry}</span>
                                        <small class="text-muted d-block">${daysToExpiry <= 0 ? 'منتهي' : 'يوم'}</small>
                                    </td>
                                    <td><span class="badge ${riskBadge} small">${riskText}</span></td>
                                </tr>
                            `;
                        });
                    } else {
                        expiringProductsHtml = '<tr><td colspan="4" class="text-center py-3 text-success">🎉 لا توجد منتجات قريبة الانتهاء</td></tr>';
                    }
                    $('#expiring-products-table tbody').html(expiringProductsHtml);

                    $('#products-loading').hide();
                    $('#products-content').show();
                } else {
                    $('#products-loading').hide();
                    $('#products-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في تحميل بيانات أداء المنتجات: ${response.message}
                        </div>
                    `).show();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل بيانات أداء المنتجات:', xhr.responseText);
                $('#products-loading').hide();
                $('#products-content').show();
            }
        });
    }









    // بدء التحديث التلقائي عند تحميل الصفحة
    startAutoRefresh();

    // إيقاف التحديث التلقائي عند مغادرة الصفحة
    $(window).on('beforeunload', function() {
        stopAutoRefresh();
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\up20251\resources\views/financial_operations/sales_analytics/index.blade.php ENDPATH**/ ?>