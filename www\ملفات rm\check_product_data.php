<?php
/**
 * فحص سريع للبيانات الأساسية المطلوبة لإنشاء المنتجات
 * تشغيل هذا الملف: php artisan tinker
 * ثم: include 'check_product_data.php';
 */

echo "=== فحص البيانات الأساسية لإنشاء المنتجات ===\n\n";

// فحص الفئات
$categories = \App\Models\ProductServiceCategory::where('type', 'product & service')->get();
echo "1. فئات المنتجات:\n";
echo "   العدد: " . $categories->count() . "\n";
if ($categories->count() > 0) {
    foreach ($categories as $category) {
        echo "   - {$category->name} (ID: {$category->id})\n";
    }
} else {
    echo "   ⚠️ لا توجد فئات! يجب إنشاء فئة واحدة على الأقل\n";
}
echo "\n";

// فحص الوحدات
$units = \App\Models\ProductServiceUnit::all();
echo "2. وحدات القياس:\n";
echo "   العدد: " . $units->count() . "\n";
if ($units->count() > 0) {
    foreach ($units as $unit) {
        echo "   - {$unit->name} (ID: {$unit->id})\n";
    }
} else {
    echo "   ⚠️ لا توجد وحدات! يجب إنشاء وحدة واحدة على الأقل\n";
}
echo "\n";

// فحص حسابات الإيرادات
$incomeAccounts = \App\Models\ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
    ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
    ->where('chart_of_account_types.name', 'income')
    ->get();

echo "3. حسابات الإيرادات:\n";
echo "   العدد: " . $incomeAccounts->count() . "\n";
if ($incomeAccounts->count() > 0) {
    foreach ($incomeAccounts as $account) {
        echo "   - {$account->code_name} (ID: {$account->id})\n";
    }
} else {
    echo "   ⚠️ لا توجد حسابات إيرادات! يجب إنشاء حساب إيرادات واحد على الأقل\n";
}
echo "\n";

// فحص حسابات المصروفات
$expenseAccounts = \App\Models\ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
    ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
    ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
    ->get();

echo "4. حسابات المصروفات:\n";
echo "   العدد: " . $expenseAccounts->count() . "\n";
if ($expenseAccounts->count() > 0) {
    foreach ($expenseAccounts as $account) {
        echo "   - {$account->code_name} (ID: {$account->id})\n";
    }
} else {
    echo "   ⚠️ لا توجد حسابات مصروفات! يجب إنشاء حساب مصروفات واحد على الأقل\n";
}
echo "\n";

// فحص الصلاحيات
$permissions = \Spatie\Permission\Models\Permission::where('name', 'like', '%product%service%')->get();
echo "5. صلاحيات المنتجات:\n";
echo "   العدد: " . $permissions->count() . "\n";
if ($permissions->count() > 0) {
    foreach ($permissions as $permission) {
        echo "   - {$permission->name}\n";
    }
} else {
    echo "   ⚠️ لا توجد صلاحيات! يجب إنشاء الصلاحيات المطلوبة\n";
}
echo "\n";

// فحص المستخدم الحالي
if (auth()->check()) {
    $user = auth()->user();
    echo "6. المستخدم الحالي:\n";
    echo "   الاسم: {$user->name}\n";
    echo "   البريد: {$user->email}\n";
    echo "   النوع: {$user->type}\n";
    
    $hasPermission = $user->can('create product & service');
    echo "   صلاحية إنشاء المنتجات: " . ($hasPermission ? "✅ موجودة" : "❌ غير موجودة") . "\n";
} else {
    echo "6. المستخدم الحالي: غير مسجل دخول\n";
}
echo "\n";

// خلاصة التشخيص
echo "=== خلاصة التشخيص ===\n";
$issues = [];

if ($categories->count() == 0) {
    $issues[] = "لا توجد فئات منتجات";
}

if ($units->count() == 0) {
    $issues[] = "لا توجد وحدات قياس";
}

if ($incomeAccounts->count() == 0) {
    $issues[] = "لا توجد حسابات إيرادات";
}

if ($expenseAccounts->count() == 0) {
    $issues[] = "لا توجد حسابات مصروفات";
}

if ($permissions->count() == 0) {
    $issues[] = "لا توجد صلاحيات منتجات";
}

if (auth()->check() && !auth()->user()->can('create product & service')) {
    $issues[] = "المستخدم لا يملك صلاحية إنشاء المنتجات";
}

if (empty($issues)) {
    echo "✅ جميع البيانات الأساسية موجودة! يمكن إنشاء المنتجات بنجاح.\n";
} else {
    echo "❌ توجد مشاكل يجب حلها:\n";
    foreach ($issues as $issue) {
        echo "   • {$issue}\n";
    }
    echo "\nيرجى تشغيل ملف fix_product_creation_data.sql لحل هذه المشاكل.\n";
}

echo "\n=== انتهى الفحص ===\n";
?>
