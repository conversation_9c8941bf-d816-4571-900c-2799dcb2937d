# تشخيص مشكلة عدم ظهور المنتجات

## 🔍 المشكلة
المنتجات لا تظهر في القائمة المنسدلة عند إنشاء أمر استلام جديد.

## 🛠️ خطوات التشخيص

### الخطوة 1: فحص قاعدة البيانات

#### أ) رفع ملف الاختبار:
```bash
# رفع ملف اختبار قاعدة البيانات
scp test_database.php user@server:/path/to/public/
```

#### ب) تعديل إعدادات قاعدة البيانات في الملف:
```php
$host = 'localhost';
$dbname = 'your_database_name';  // اسم قاعدة البيانات
$username = 'your_username';     // اسم المستخدم
$password = 'your_password';     // كلمة المرور
```

#### ج) تشغيل الاختبار:
```
http://yoursite.com/test_database.php
```

#### د) التحقق من النتائج:
- [ ] وجود جدول `product_services`
- [ ] وجود جدول `warehouses`
- [ ] وجود جدول `warehouse_products`
- [ ] وجود منتجات في قاعدة البيانات
- [ ] وجود مستودعات في قاعدة البيانات

### الخطوة 2: فحص المسارات

#### أ) تشغيل اختبار المسارات:
```bash
ssh user@server "cd /path/to/project && php artisan route:list | grep receipt"
```

#### ب) التحقق من وجود المسارات:
```
receipt-order.index
receipt-order.create
receipt.order.warehouse.products
receipt.order.search.products
```

#### ج) اختبار المسارات مباشرة:
```bash
# اختبار مسار تحميل المنتجات
curl -X GET "http://yoursite.com/receipt-order-warehouse-products?warehouse_id=1" \
     -H "Accept: application/json"

# اختبار مسار البحث
curl -X GET "http://yoursite.com/receipt-order-search-products?search=test&warehouse_id=1" \
     -H "Accept: application/json"
```

### الخطوة 3: فحص JavaScript في المتصفح

#### أ) افتح Developer Tools (F12)
#### ب) اذهب إلى Console
#### ج) اختبر المسارات:
```javascript
// اختبار المسارات
console.log('Warehouse Products URL:', '/receipt-order-warehouse-products');
console.log('Search Products URL:', '/receipt-order-search-products');

// اختبار AJAX مباشر
$.ajax({
    url: '/receipt-order-warehouse-products',
    method: 'GET',
    data: { warehouse_id: 1 },
    success: function(response) {
        console.log('Success:', response);
    },
    error: function(xhr, status, error) {
        console.error('Error:', error);
        console.error('Response:', xhr.responseText);
    }
});
```

### الخطوة 4: فحص الصلاحيات

#### أ) التحقق من صلاحيات المستخدم:
```sql
-- فحص صلاحيات المستخدم الحالي
SELECT p.name as permission_name 
FROM permissions p
JOIN model_has_permissions mhp ON p.id = mhp.permission_id
WHERE mhp.model_id = YOUR_USER_ID AND mhp.model_type = 'App\\Models\\User';

-- أو فحص الأدوار
SELECT r.name as role_name
FROM roles r
JOIN model_has_roles mhr ON r.id = mhr.role_id
WHERE mhr.model_id = YOUR_USER_ID AND mhr.model_type = 'App\\Models\\User';
```

#### ب) التحقق من الصلاحيات المطلوبة:
- `manage warehouse`
- `show warehouse`

### الخطوة 5: فحص ملفات اللوج

#### أ) فحص لوج Laravel:
```bash
tail -f storage/logs/laravel.log
```

#### ب) فحص لوج الخادم:
```bash
# Apache
tail -f /var/log/apache2/error.log

# Nginx
tail -f /var/log/nginx/error.log
```

### الخطوة 6: فحص CSRF Token

#### أ) التحقق من وجود CSRF Token:
```javascript
// في Console
console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));
```

#### ب) إضافة CSRF Token للطلبات:
```javascript
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});
```

## 🔧 الحلول المحتملة

### الحل 1: مشكلة في قاعدة البيانات

#### إذا لم توجد منتجات:
```sql
-- إضافة منتجات تجريبية
INSERT INTO product_services (name, sku, sale_price, purchase_price, created_by, created_at, updated_at) 
VALUES 
('منتج تجريبي 1', 'TEST001', 10.00, 8.00, 1, NOW(), NOW()),
('منتج تجريبي 2', 'TEST002', 15.00, 12.00, 1, NOW(), NOW());
```

#### إذا لم توجد مستودعات:
```sql
-- إضافة مستودع تجريبي
INSERT INTO warehouses (name, created_by, created_at, updated_at) 
VALUES ('المستودع الرئيسي', 1, NOW(), NOW());
```

### الحل 2: مشكلة في المسارات

#### إعادة تحميل المسارات:
```bash
php artisan route:clear
php artisan route:cache
```

### الحل 3: مشكلة في الصلاحيات

#### إعطاء صلاحيات للمستخدم:
```sql
-- إعطاء صلاحية manage warehouse
INSERT INTO model_has_permissions (permission_id, model_type, model_id)
SELECT p.id, 'App\\Models\\User', YOUR_USER_ID
FROM permissions p 
WHERE p.name = 'manage warehouse';
```

### الحل 4: مشكلة في JavaScript

#### إضافة تشخيص في الكود:
```javascript
// في بداية دالة loadProductsForSelect
console.log('Loading products for warehouse:', warehouseId);
console.log('AJAX URL:', '{{ route("receipt.order.warehouse.products") }}');
```

### الحل 5: مشكلة في الكونترولر

#### إضافة تشخيص في الكونترولر:
```php
public function getWarehouseProducts(Request $request)
{
    \Log::info('getWarehouseProducts called', [
        'warehouse_id' => $request->warehouse_id,
        'user_id' => Auth::id()
    ]);
    
    // باقي الكود...
}
```

## 📋 قائمة التحقق السريعة

- [ ] **قاعدة البيانات**: هل توجد منتجات ومستودعات؟
- [ ] **المسارات**: هل المسارات معرفة ومتاحة؟
- [ ] **الصلاحيات**: هل المستخدم لديه صلاحيات؟
- [ ] **JavaScript**: هل هناك أخطاء في Console؟
- [ ] **AJAX**: هل الطلبات تصل للخادم؟
- [ ] **الاستجابة**: هل الخادم يرد بالبيانات الصحيحة؟

## 🚨 الأخطاء الشائعة

### 1. **خطأ 404 - المسار غير موجود**
```
الحل: تحقق من routes/web.php وتأكد من وجود المسارات
```

### 2. **خطأ 403 - ممنوع الوصول**
```
الحل: تحقق من الصلاحيات والأدوار
```

### 3. **خطأ 500 - خطأ في الخادم**
```
الحل: فحص ملفات اللوج لمعرفة السبب
```

### 4. **استجابة فارغة**
```
الحل: تحقق من وجود البيانات في قاعدة البيانات
```

### 5. **CSRF Token مفقود**
```
الحل: إضافة meta tag في head وإعداد AJAX
```

## 🎯 الخطوات التالية

1. **ابدأ بفحص قاعدة البيانات** باستخدام `test_database.php`
2. **اختبر المسارات** مباشرة من المتصفح
3. **فحص Console** في Developer Tools
4. **راجع ملفات اللوج** للأخطاء
5. **اختبر الصلاحيات** للمستخدم الحالي

## 📞 طلب المساعدة

إذا استمرت المشكلة، شارك النتائج التالية:

1. **نتائج `test_database.php`**
2. **رسائل الخطأ من Console**
3. **محتوى ملف اللوج**
4. **نتائج اختبار المسارات**
5. **معلومات المستخدم والصلاحيات**

هذا سيساعد في تحديد السبب الدقيق للمشكلة وإيجاد الحل المناسب.
