<?php
/**
 * أداة تشخيص شاملة لمشاكل نظام POS
 * تحليل مفصل للفواتير غير المدفوعة ومشاكل إدخال المنتجات
 */

// إعدادات قاعدة البيانات - يجب تحديثها حسب إعدادات المشروع
$host = 'localhost';
$dbname = 'up20251';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// دوال التحليل
function getUnpaidInvoices($pdo) {
    $sql = "SELECT p.id, p.pos_id, p.pos_date, p.customer_id, p.warehouse_id, p.created_by,
                   pp.payment_type, pp.amount as payment_amount,
                   c.name as customer_name, w.name as warehouse_name,
                   u.name as created_by_name
            FROM pos p 
            LEFT JOIN pos_payments pp ON p.id = pp.pos_id 
            LEFT JOIN customers c ON p.customer_id = c.id
            LEFT JOIN warehouses w ON p.warehouse_id = w.id
            LEFT JOIN users u ON p.created_by = u.id
            WHERE pp.id IS NULL OR pp.payment_type IS NULL OR pp.payment_type = ''
            ORDER BY p.id DESC 
            LIMIT 50";
    
    return $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
}

function getInvoicesWithoutProducts($pdo) {
    $sql = "SELECT p.id, p.pos_id, p.pos_date, p.customer_id, p.warehouse_id,
                   COUNT(pp.id) as product_count,
                   c.name as customer_name, w.name as warehouse_name
            FROM pos p 
            LEFT JOIN pos_products pp ON p.id = pp.pos_id
            LEFT JOIN customers c ON p.customer_id = c.id
            LEFT JOIN warehouses w ON p.warehouse_id = w.id
            GROUP BY p.id
            HAVING product_count = 0
            ORDER BY p.id DESC 
            LIMIT 20";
    
    return $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
}

function getSystemStatistics($pdo) {
    $stats = [];
    
    // إجمالي الفواتير
    $stats['total_invoices'] = $pdo->query("SELECT COUNT(*) FROM pos")->fetchColumn();
    
    // إجمالي المدفوعات
    $stats['total_payments'] = $pdo->query("SELECT COUNT(*) FROM pos_payments")->fetchColumn();
    
    // الفواتير بدون مدفوعات
    $stats['missing_payments'] = $pdo->query("SELECT COUNT(*) FROM pos p LEFT JOIN pos_payments pp ON p.id = pp.pos_id WHERE pp.id IS NULL")->fetchColumn();
    
    // الفواتير بدون منتجات
    $stats['invoices_without_products'] = $pdo->query("SELECT COUNT(*) FROM pos p LEFT JOIN pos_products pp ON p.id = pp.pos_id WHERE pp.id IS NULL")->fetchColumn();
    
    // المدفوعات بدون payment_type
    $stats['payments_without_type'] = $pdo->query("SELECT COUNT(*) FROM pos_payments WHERE payment_type IS NULL OR payment_type = ''")->fetchColumn();
    
    // الفواتير الحديثة (آخر 7 أيام)
    $stats['recent_invoices'] = $pdo->query("SELECT COUNT(*) FROM pos WHERE pos_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn();
    
    return $stats;
}

function getProductIssues($pdo) {
    $sql = "SELECT p.id, p.pos_id, p.pos_date,
                   COUNT(pp.id) as product_count,
                   SUM(pp.quantity) as total_quantity,
                   SUM(pp.price * pp.quantity) as calculated_total,
                   pay.amount as payment_amount,
                   CASE 
                       WHEN COUNT(pp.id) = 0 THEN 'بدون منتجات'
                       WHEN pay.amount IS NULL THEN 'بدون دفع'
                       WHEN ABS(SUM(pp.price * pp.quantity) - pay.amount) > 0.01 THEN 'مبلغ خاطئ'
                       ELSE 'طبيعي'
                   END as issue_type
            FROM pos p 
            LEFT JOIN pos_products pp ON p.id = pp.pos_id
            LEFT JOIN pos_payments pay ON p.id = pay.pos_id
            GROUP BY p.id
            HAVING issue_type != 'طبيعي'
            ORDER BY p.id DESC 
            LIMIT 30";
    
    return $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
}

// جلب البيانات
$unpaidInvoices = getUnpaidInvoices($pdo);
$invoicesWithoutProducts = getInvoicesWithoutProducts($pdo);
$systemStats = getSystemStatistics($pdo);
$productIssues = getProductIssues($pdo);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص شامل لمشاكل نظام POS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .info { background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; }
        .warning { background: linear-gradient(135deg, #fdcb6e, #e17055); color: white; }
        .error { background: linear-gradient(135deg, #fd79a8, #e84393); color: white; }
        .success { background: linear-gradient(135deg, #00b894, #00a085); color: white; }
        .critical { background: linear-gradient(135deg, #d63031, #74b9ff); color: white; border: 3px solid #ff7675; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            overflow: hidden;
            font-size: 12px;
        }
        
        .invoice-table th,
        .invoice-table td {
            padding: 8px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .invoice-table th {
            background: rgba(0,0,0,0.2);
            font-weight: bold;
        }
        
        .status-paid { background: #00b894; color: white; padding: 3px 8px; border-radius: 15px; font-size: 0.8em; }
        .status-unpaid { background: #e17055; color: white; padding: 3px 8px; border-radius: 15px; font-size: 0.8em; }
        .status-missing { background: #d63031; color: white; padding: 3px 8px; border-radius: 15px; font-size: 0.8em; }
        .status-error { background: #fd79a8; color: white; padding: 3px 8px; border-radius: 15px; font-size: 0.8em; }
        
        .problem-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .problem-critical { background: #d63031; }
        .problem-warning { background: #fdcb6e; }
        .problem-info { background: #74b9ff; }
        .problem-success { background: #00b894; }
        
        .sql-code {
            background: rgba(0,0,0,0.1);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            white-space: pre-wrap;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص شامل لمشاكل نظام POS</h1>
            <p>تحليل مفصل للفواتير غير المدفوعة ومشاكل إدخال المنتجات</p>
        </div>
        
        <div class="content">
            <!-- إحصائيات النظام -->
            <div class="section info">
                <h2>📊 إحصائيات النظام العامة</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?= $systemStats['total_invoices'] ?></div>
                        <div>إجمالي الفواتير</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?= $systemStats['total_payments'] ?></div>
                        <div>إجمالي المدفوعات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?= $systemStats['missing_payments'] ?></div>
                        <div>فواتير بدون مدفوعات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?= $systemStats['invoices_without_products'] ?></div>
                        <div>فواتير بدون منتجات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?= $systemStats['payments_without_type'] ?></div>
                        <div>مدفوعات بدون نوع</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?= $systemStats['recent_invoices'] ?></div>
                        <div>فواتير آخر 7 أيام</div>
                    </div>
                </div>
            </div>

            <!-- تحليل المشاكل الحرجة -->
            <?php if ($systemStats['missing_payments'] > 0 || $systemStats['invoices_without_products'] > 0): ?>
            <div class="section critical">
                <h2>🚨 مشاكل حرجة تتطلب إصلاح فوري</h2>

                <?php if ($systemStats['missing_payments'] > 0): ?>
                <div style="margin: 15px 0;">
                    <span class="problem-indicator problem-critical"></span>
                    <strong>فواتير بدون سجلات دفع:</strong> <?= $systemStats['missing_payments'] ?> فاتورة
                    <p>هذه الفواتير تظهر كـ "غير مدفوع" في النظام رغم أنها قد تكون مدفوعة فعلياً.</p>
                </div>
                <?php endif; ?>

                <?php if ($systemStats['invoices_without_products'] > 0): ?>
                <div style="margin: 15px 0;">
                    <span class="problem-indicator problem-critical"></span>
                    <strong>فواتير بدون منتجات:</strong> <?= $systemStats['invoices_without_products'] ?> فاتورة
                    <p>هذه الفواتير تم إنشاؤها ولكن لم يتم إضافة أي منتجات إليها، مما يشير إلى مشكلة في عملية إضافة المنتجات للسلة.</p>
                </div>
                <?php endif; ?>

                <?php if ($systemStats['payments_without_type'] > 0): ?>
                <div style="margin: 15px 0;">
                    <span class="problem-indicator problem-warning"></span>
                    <strong>مدفوعات بدون نوع دفع:</strong> <?= $systemStats['payments_without_type'] ?> مدفوعة
                    <p>هذه المدفوعات لا تحتوي على نوع الدفع (نقدي/شبكة) مما يؤثر على التقارير.</p>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- الفواتير غير المدفوعة -->
            <?php if (!empty($unpaidInvoices)): ?>
            <div class="section error">
                <h2>💳 الفواتير غير المدفوعة (آخر 50 فاتورة)</h2>
                <p>هذه الفواتير تظهر كـ "غير مدفوع" في النظام:</p>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>العميل</th>
                            <th>المستودع</th>
                            <th>المنشئ</th>
                            <th>نوع الدفع</th>
                            <th>مبلغ الدفع</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($unpaidInvoices as $invoice): ?>
                        <tr>
                            <td><?= htmlspecialchars($invoice['pos_id']) ?></td>
                            <td><?= htmlspecialchars($invoice['pos_date']) ?></td>
                            <td><?= htmlspecialchars($invoice['customer_name'] ?? 'عميل عادي') ?></td>
                            <td><?= htmlspecialchars($invoice['warehouse_name'] ?? 'غير محدد') ?></td>
                            <td><?= htmlspecialchars($invoice['created_by_name'] ?? 'غير محدد') ?></td>
                            <td><?= htmlspecialchars($invoice['payment_type'] ?? 'غير محدد') ?></td>
                            <td><?= htmlspecialchars($invoice['payment_amount'] ?? '0.00') ?></td>
                            <td>
                                <?php if (empty($invoice['payment_type'])): ?>
                                    <span class="status-missing">بدون دفع</span>
                                <?php else: ?>
                                    <span class="status-unpaid">غير مدفوع</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <!-- الفواتير بدون منتجات -->
            <?php if (!empty($invoicesWithoutProducts)): ?>
            <div class="section warning">
                <h2>📦 الفواتير بدون منتجات (آخر 20 فاتورة)</h2>
                <p>هذه الفواتير تم إنشاؤها ولكن لا تحتوي على أي منتجات:</p>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>العميل</th>
                            <th>المستودع</th>
                            <th>عدد المنتجات</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoicesWithoutProducts as $invoice): ?>
                        <tr>
                            <td><?= htmlspecialchars($invoice['pos_id']) ?></td>
                            <td><?= htmlspecialchars($invoice['pos_date']) ?></td>
                            <td><?= htmlspecialchars($invoice['customer_name'] ?? 'عميل عادي') ?></td>
                            <td><?= htmlspecialchars($invoice['warehouse_name'] ?? 'غير محدد') ?></td>
                            <td><?= $invoice['product_count'] ?></td>
                            <td><span class="status-error">بدون منتجات</span></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <!-- تحليل مشاكل المنتجات -->
            <?php if (!empty($productIssues)): ?>
            <div class="section warning">
                <h2>🔧 تحليل مشاكل المنتجات والمبالغ</h2>
                <p>فواتير تحتوي على مشاكل في المنتجات أو المبالغ:</p>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>عدد المنتجات</th>
                            <th>إجمالي الكمية</th>
                            <th>المبلغ المحسوب</th>
                            <th>مبلغ الدفع</th>
                            <th>نوع المشكلة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($productIssues as $issue): ?>
                        <tr>
                            <td><?= htmlspecialchars($issue['pos_id']) ?></td>
                            <td><?= htmlspecialchars($issue['pos_date']) ?></td>
                            <td><?= $issue['product_count'] ?></td>
                            <td><?= $issue['total_quantity'] ?? 0 ?></td>
                            <td><?= number_format($issue['calculated_total'] ?? 0, 2) ?></td>
                            <td><?= number_format($issue['payment_amount'] ?? 0, 2) ?></td>
                            <td>
                                <?php
                                $issueClass = 'status-error';
                                if ($issue['issue_type'] == 'بدون منتجات') $issueClass = 'status-missing';
                                elseif ($issue['issue_type'] == 'بدون دفع') $issueClass = 'status-unpaid';
                                ?>
                                <span class="<?= $issueClass ?>"><?= htmlspecialchars($issue['issue_type']) ?></span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <!-- حلول الإصلاح -->
            <div class="section success">
                <h2>🛠️ حلول الإصلاح المقترحة</h2>

                <div style="margin: 20px 0;">
                    <h3>1. إصلاح الفواتير بدون سجلات دفع</h3>
                    <div class="sql-code">
-- إنشاء سجلات دفع للفواتير المفقودة
INSERT INTO pos_payments (pos_id, date, amount, discount, payment_type, created_by, created_at, updated_at)
SELECT
    p.id as pos_id,
    p.pos_date as date,
    COALESCE((
        SELECT SUM(pp.price * pp.quantity)
        FROM pos_products pp
        WHERE pp.pos_id = p.id
    ), 0) as amount,
    0 as discount,
    'cash' as payment_type,
    p.created_by,
    p.created_at,
    p.updated_at
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
WHERE pp.id IS NULL;
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3>2. إصلاح أنواع الدفع الفارغة</h3>
                    <div class="sql-code">
-- تحديث payment_type الفارغة إلى 'cash'
UPDATE pos_payments
SET payment_type = 'cash',
    cash_amount = amount,
    network_amount = 0
WHERE payment_type IS NULL OR payment_type = '';
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3>3. حذف الفواتير بدون منتجات (اختياري)</h3>
                    <div class="sql-code">
-- ⚠️ تحذير: هذا سيحذف الفواتير نهائياً
-- حذف المدفوعات أولاً
DELETE pp FROM pos_payments pp
INNER JOIN pos p ON pp.pos_id = p.id
LEFT JOIN pos_products ppr ON p.id = ppr.pos_id
WHERE ppr.id IS NULL;

-- ثم حذف الفواتير
DELETE p FROM pos p
LEFT JOIN pos_products pp ON p.id = pp.pos_id
WHERE pp.id IS NULL;
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <h3>4. تشغيل أمر الإصلاح التلقائي</h3>
                    <div class="sql-code">
-- تشغيل أمر Laravel للإصلاح
php artisan pos:fix-unpaid-invoices
                    </div>
                </div>
            </div>

            <!-- توصيات لمنع المشاكل المستقبلية -->
            <div class="section info">
                <h2>💡 توصيات لمنع المشاكل المستقبلية</h2>

                <div style="margin: 15px 0;">
                    <h4>1. تحسين عملية إضافة المنتجات:</h4>
                    <ul>
                        <li>التأكد من وجود المنتج في المخزون قبل الإضافة</li>
                        <li>إضافة تحقق من صحة البيانات في JavaScript</li>
                        <li>تحسين معالجة الأخطاء في AJAX</li>
                    </ul>
                </div>

                <div style="margin: 15px 0;">
                    <h4>2. تحسين عملية الدفع:</h4>
                    <ul>
                        <li>التأكد من وجود منتجات في السلة قبل السماح بالدفع</li>
                        <li>إضافة قيمة افتراضية لـ payment_type</li>
                        <li>تحسين معالجة الأخطاء في عملية حفظ الفاتورة</li>
                    </ul>
                </div>

                <div style="margin: 15px 0;">
                    <h4>3. مراقبة النظام:</h4>
                    <ul>
                        <li>إضافة تقارير دورية للفواتير غير المكتملة</li>
                        <li>إنشاء تنبيهات للمشاكل الحرجة</li>
                        <li>تشغيل هذه الأداة بشكل دوري</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
