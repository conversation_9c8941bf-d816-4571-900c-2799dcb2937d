{"__meta": {"id": "X18bad301f65c684a58bb629ac237933f", "datetime": "2025-06-06 19:13:20", "utime": **********.982531, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.705345, "end": **********.98256, "duration": 1.****************, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": **********.705345, "relative_start": 0, "end": **********.813344, "relative_end": **********.813344, "duration": 1.****************, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.813363, "relative_start": 1.***************, "end": **********.982564, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007980000000000001, "accumulated_duration_str": "7.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.903575, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.644}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.932393, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.644, "width_percent": 14.662}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.961193, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 72.306, "width_percent": 27.694}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRvTGZUWVowWjNPNFl1MHNEeUEyeFE9PSIsInZhbHVlIjoiMGp1WnNVMHl4M1lsRnJCdnhuR1F5MndKdCtsMzI5aDdyMUMzT1JFZTVmY29oTFpoYkJuZGp4NEE3S2U5OUxMMW5KSG15ckJwbUFocTJsdWx5MzczUXEyK3NFcVMvaDMwV1l2cGVHVmM2RDlKYW1oOXQ0aE5wakplbkltR21VMTBsSCt3bnA0QlZweWpjMEZzaEpZYjM5WjBNOVlWOFo4VU1Bd3pHOWNheTZzWHZwUHJhU3ZNT29VbmVYZFpHMk1IQXdwLzVmSHFCSGh5bW5MZWwvejBzcHlwWXBncFBQcnovZXJCWVFGblBNZ1pjb1BaWkhpSGJjcHZaajVJUlVoakNDNXJ6MUYrOWxNVUpxRzM0ZmYzT2trd2xuY0IyTEw5SnZCOHpSRUdSdkR1SjFjcUhwNTl1RnQxcCtxaFd0TStHSkVRVnRkZk5HbWk0UGpnYUFmWVc1b3AxYUNJVEZIY2ZrYmlRVEpIdnRraE5HVnZvdUxFMXlwcmxVVVRCMC9ESER0M0h2QWhaZnpvT1k3V1hXWE1NQ3Z0cDZlMUpRZURDenVBcFV4RUxFU1owTHpjTWtwekUyVmRTT3NhRTNSR0FobWVVMVNZVnZaUWpQczlGRi9MbTg1M3FpWWhmc3Y2R01WczYxMXM3MmRsQm1vUXhoenJXSEltOGNPNzFZRTQiLCJtYWMiOiIzNTU2Njg2MjU3ZTZjYzAyMTU5MDE1ZmRjZDEwMWIyZTNlY2Q3NjQ3ZGVmNzNmZGY5MDYwZjNkNmMzMjQ2MjJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkR1UmxBSGdIOVl4SVhRODQvd1dZUXc9PSIsInZhbHVlIjoiZFRVb2t6VmFzb2hzRVREYnVWU3RuZjRoVlJ2UXRmR1FEcnM5dlFER3hWcTAwS0VhUlVHL1JPK0lCTzg4VkZNdzFjUmJFSHJrK1N3RFcvdnJoMW5Bc01DN0ExSEJkOEhURG1LSE01YzA3SlJ2ZkFETGNuTEZBbmJaclhDNU5JNTZHWFc5bG4wZ2RINzQzV2tUYmNlbkt6dlp1Y3EybTdXdEhDa2ZVSUpuNDAvVU4rdHJRVXdmYXRQajR5Q1Q0WFIwYVJIUmQ1MkowTXE1QzZxbWVTZFNYbUJ3Y1NWMGo4eUlQSjJ4akxFazRxS1Z1OVBsN2JyRXNzb2RYSW54QW5ISDlTS2NsVkIyMmR6VGZ4cGVEYjF2cFM2d3VXVUpmS25QZEVGZitodmo3Z0U2eXk1dzFoaHVxRmcwR1p0b3NwNzdiOXZmQVBqc1Z4Wm95TFJhTU95VHhmcUl4M2UxOVYwRUdRTGhUSlRBTnltdHRRTktxZkh4bXk0VGpqZ2g1a2l2dWZRWXVJZVRRVE13UVZIWEhrbGwxcWtpWWpGbW9CNXdGMkNvRGZuNnRoYjdXRC9UNHEycXRleWEwd0J6dlJXZ2hEV3dQVmFOcDEwMm1TT1JuWjU0VXR5ajg2TW85cEFjWDVONDcvRFZRUDV3VG53Vkx6RVZJeCtBeWhoUWExa3QiLCJtYWMiOiJlOWUzZmFlYWIxMTRjMjI2ZjE0OWRlZWU5MmRkYzMzN2I0MzVlODA3ZDhmM2QxMGUxNjg3OTY3YzkzN2QxOWRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1467759644 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PVt0h9icZb5VqFqziyPToWE7TqW2lYjvh7uvnOnw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467759644\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-300534241 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZwTUJ4SmNLM3NQVldhd0I4dVFRelE9PSIsInZhbHVlIjoibWlqSlBuZFEzbVlBd3pOdll1MHZaMmVTaXhhdGdrc0NuUE9CUGRtTzRMcVBwdmx0Q2tqSnI0RU4rMHBFSG8yL1U5TWUrT3gydnY4WGRUVDczUU13ZUFhNzVKdGRmNGJuNGhXa2FEWXNwVEUvYUlZSkhPeGRGTkNSQ2p1M21GT3JCYXBkckpLbjVLMCtBZ3NmTnk4S0lDbjgwOHpOcDNCWDVQU3hLMzZwMEhSUXhHdUtBMWwwR3JCY3NJYmZFbDBjditIL0hMQkRIYlorUXFqM09SSGE1TURQMmhFOXpZd0NBdFMrZmZKeHVlMnU3YVlXUXlncFMxeitUd2ltT2EzTXZrdjZYYVNFSUFTYVpCaENQU3BieGFwUk5BOVA2TlcvS2lESDRqcFNJTkhZZDhTdjBPNlZ0VnhhcE9XcllDVldiZXlHRjlDcXZsSFJDa3BpWU11cjBFdWpZWnJDQWd1dHNIUE1XWGdoV3pWaUxZU2NYSVRHY1VXMEJRTDF5TlRtaE1oSkFlMkVxK0hzMXcrcGZiOW84WldTS3JFY0ZzclRxcDNUaGhoK0ptR1Z1UlZSUUMvbzY2dVVwVXoraHhPV2ZTYlY0MEgvdWlIL1VIL3c5MWR6QkxLaVB5MldPd0ZtZ2JXbS9tTS9RbkZHYWcvS3J5NVErNXhiZ0J6ejJqMFYiLCJtYWMiOiI1ODg3MGVkYmU3ZmJlODFlMDdhMzAxOGY3ZmM3NTE1ZTNhY2ExM2FjODY0Yjc2NGI1NmFhYmEyZmVlZjhiMzNiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InoyK1E5R3F0QWZqYXRWei9pY0dpWXc9PSIsInZhbHVlIjoiTm14RUdyOEhaZGxKSjArNWo5UFVpRkxWNXdqWU50ekpwcDZxdHFMVVhML1EwdTFXUHd4RnkzYjVjK1JUT1VyVkdoRmNPWUpYNE5vN0RJYmRlRjc5TStrc09vZ2JmVmdUZkhyK1diN1Mwd2Q3S3RkSXUxTk9LV0FCamRpVEhjbkVsVzkySU55ZEFibUxpVWlLc3M5c1ZDR2k2OTVxc2NxcWNISHdvQytNTnVtNlo1NDVTNVp6Tm9ZOXVmSDB6VStVMzlCT3JiQllrTnlLdW54eDc3bUFmMHpBTWU5dWJTVE5xMVJaWkRQaTFBTG1MWG9qekkvMW5SaUdDM2gyZnFZZHhMV0hTb1JnTGhoTkVVWTRtR1NSbkxQZytRSG1KY2RUYVFLUWNUUGJXOFdYYjF5MFhpUnV3RDFpMG9XSWJ2czNDYmNtd2dueTNGaEs3K1owTHVtV2dLMjFVbW5kVVJkZFdEbVAvTkwyamRxZjYvblg0Nm5yWmhKRFVybkQrM0dRRlF3dFFZcjBkK0tPVmtjeEwzZ0loTk50ZzBBdktLWFdYRHlMOFhFSWNOZzBEd1pDQVM0Wkp0UnlmaUJ0MFBXVnluOW16Rm9IZHRsUVVibDBTNVZsWWMraWsxS3JBeExLNnF0VnFTWFAwZkFwNi9tckJxMlpPU21Qa1J6SWc3b2YiLCJtYWMiOiIyOGM0YjE4OTFmZjZkNjdlOGFjMjk1ZDFhMjBhZDY4ZjhmNjIyNWQwNzU3MWY5OWNkNGRhZDAzOGE4ODZkN2IyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZwTUJ4SmNLM3NQVldhd0I4dVFRelE9PSIsInZhbHVlIjoibWlqSlBuZFEzbVlBd3pOdll1MHZaMmVTaXhhdGdrc0NuUE9CUGRtTzRMcVBwdmx0Q2tqSnI0RU4rMHBFSG8yL1U5TWUrT3gydnY4WGRUVDczUU13ZUFhNzVKdGRmNGJuNGhXa2FEWXNwVEUvYUlZSkhPeGRGTkNSQ2p1M21GT3JCYXBkckpLbjVLMCtBZ3NmTnk4S0lDbjgwOHpOcDNCWDVQU3hLMzZwMEhSUXhHdUtBMWwwR3JCY3NJYmZFbDBjditIL0hMQkRIYlorUXFqM09SSGE1TURQMmhFOXpZd0NBdFMrZmZKeHVlMnU3YVlXUXlncFMxeitUd2ltT2EzTXZrdjZYYVNFSUFTYVpCaENQU3BieGFwUk5BOVA2TlcvS2lESDRqcFNJTkhZZDhTdjBPNlZ0VnhhcE9XcllDVldiZXlHRjlDcXZsSFJDa3BpWU11cjBFdWpZWnJDQWd1dHNIUE1XWGdoV3pWaUxZU2NYSVRHY1VXMEJRTDF5TlRtaE1oSkFlMkVxK0hzMXcrcGZiOW84WldTS3JFY0ZzclRxcDNUaGhoK0ptR1Z1UlZSUUMvbzY2dVVwVXoraHhPV2ZTYlY0MEgvdWlIL1VIL3c5MWR6QkxLaVB5MldPd0ZtZ2JXbS9tTS9RbkZHYWcvS3J5NVErNXhiZ0J6ejJqMFYiLCJtYWMiOiI1ODg3MGVkYmU3ZmJlODFlMDdhMzAxOGY3ZmM3NTE1ZTNhY2ExM2FjODY0Yjc2NGI1NmFhYmEyZmVlZjhiMzNiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InoyK1E5R3F0QWZqYXRWei9pY0dpWXc9PSIsInZhbHVlIjoiTm14RUdyOEhaZGxKSjArNWo5UFVpRkxWNXdqWU50ekpwcDZxdHFMVVhML1EwdTFXUHd4RnkzYjVjK1JUT1VyVkdoRmNPWUpYNE5vN0RJYmRlRjc5TStrc09vZ2JmVmdUZkhyK1diN1Mwd2Q3S3RkSXUxTk9LV0FCamRpVEhjbkVsVzkySU55ZEFibUxpVWlLc3M5c1ZDR2k2OTVxc2NxcWNISHdvQytNTnVtNlo1NDVTNVp6Tm9ZOXVmSDB6VStVMzlCT3JiQllrTnlLdW54eDc3bUFmMHpBTWU5dWJTVE5xMVJaWkRQaTFBTG1MWG9qekkvMW5SaUdDM2gyZnFZZHhMV0hTb1JnTGhoTkVVWTRtR1NSbkxQZytRSG1KY2RUYVFLUWNUUGJXOFdYYjF5MFhpUnV3RDFpMG9XSWJ2czNDYmNtd2dueTNGaEs3K1owTHVtV2dLMjFVbW5kVVJkZFdEbVAvTkwyamRxZjYvblg0Nm5yWmhKRFVybkQrM0dRRlF3dFFZcjBkK0tPVmtjeEwzZ0loTk50ZzBBdktLWFdYRHlMOFhFSWNOZzBEd1pDQVM0Wkp0UnlmaUJ0MFBXVnluOW16Rm9IZHRsUVVibDBTNVZsWWMraWsxS3JBeExLNnF0VnFTWFAwZkFwNi9tckJxMlpPU21Qa1J6SWc3b2YiLCJtYWMiOiIyOGM0YjE4OTFmZjZkNjdlOGFjMjk1ZDFhMjBhZDY4ZjhmNjIyNWQwNzU3MWY5OWNkNGRhZDAzOGE4ODZkN2IyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300534241\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-809206993 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809206993\", {\"maxDepth\":0})</script>\n"}}