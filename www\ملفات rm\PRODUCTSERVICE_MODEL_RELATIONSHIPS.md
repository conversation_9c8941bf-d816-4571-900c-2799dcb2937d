# علاقات نموذج ProductService مع نماذج الإنشاء والتعديل

## 🔗 العلاقات الأساسية في النموذج

### 1. العلاقات المعرفة في ProductService.php

```php
// العلاقات الموجودة
public function taxes()        // علاقة مع جدول taxes
public function unit()         // علاقة مع جدول product_service_units  
public function category()     // علاقة مع جدول product_service_categories
public function warehouse()    // علاقة مع جدول warehouses
public function warehouseProducts() // علاقة مع جدول warehouse_products
```

### 2. الحقول القابلة للتعبئة (Fillable)

```php
protected $fillable = [
    'name',                    // اسم المنتج
    'sku',                     // رمز المنتج
    'sale_price',              // سعر البيع
    'purchase_price',          // سعر الشراء
    'quantity',                // الكمية
    'tax_id',                  // معرف الضريبة
    'category_id',             // معرف الفئة
    'unit_id',                 // معرف الوحدة
    'type',                    // النوع (منتج/خدمة)
    'sale_chartaccount_id',    // حساب الإيرادات
    'expense_chartaccount_id', // حساب المصروفات
    'created_by',              // منشئ السجل
    'expiry_date',             // تاريخ الانتهاء
];
```

## 📝 نماذج الإنشاء والتعديل

### 1. نموذج الإنشاء (create.blade.php)

#### أ. الحقول الأساسية
- **Name**: اسم المنتج (مطلوب)
- **SKU**: رمز المنتج (مطلوب + فريد)
- **Sale Value Helper**: قيمة البيع شاملة الضريبة (مساعد)
- **Sale Price**: سعر البيع قبل الضريبة (مطلوب)
- **Purchase Price**: سعر الشراء (مطلوب)

#### ب. الحسابات المالية
- **Income Account**: حساب الإيرادات (مطلوب)
- **Expense Account**: حساب المصروفات (مطلوب)

#### ج. التصنيفات
- **Tax**: الضريبة (اختياري - متعدد)
- **Category**: الفئة (مطلوب)
- **Unit**: الوحدة (مطلوب)

#### د. التفاصيل الإضافية
- **Product Image**: صورة المنتج (اختياري)
- **Type**: النوع (منتج/خدمة)
- **Quantity**: الكمية (مطلوب للمنتجات فقط)
- **Description**: الوصف (اختياري)

### 2. نموذج التعديل (edit.blade.php)

#### أ. نفس حقول الإنشاء مع:
- **القيم المحملة مسبقاً** من قاعدة البيانات
- **حساب قيمة البيع الشاملة** من السعر والضريبة الحالية
- **إظهار/إخفاء الكمية** حسب النوع المحدد

#### ب. ميزات إضافية في التعديل:
- **عرض الصورة الحالية** للمنتج
- **حساب عكسي** للضريبة عند تغيير السعر
- **تحديث تلقائي** للحقول المترابطة

## 🔧 العلاقات في Controllers

### 1. ProductServiceController

#### أ. في دالة create()
```php
// جلب البيانات المرتبطة
$category = ProductServiceCategory::where('created_by', '=', Auth::user()->creatorId())
    ->where('type', '=', 'product & service')->get()->pluck('name', 'id');

$unit = ProductServiceUnit::where('created_by', '=', Auth::user()->creatorId())
    ->get()->pluck('name', 'id');

$tax = Tax::where('created_by', '=', Auth::user()->creatorId())
    ->get()->pluck('name', 'id');

$incomeChartAccounts = ChartOfAccount::select(DB::raw('CONCAT(code, " - ", name) AS code_name, id'))
    ->where('created_by', Auth::user()->creatorId())->get()->pluck('code_name', 'id');
```

#### ب. في دالة store()
```php
// قواعد التحقق
$rules = [
    'name' => 'required',
    'sku' => ['required', Rule::unique('product_services')->where(function ($query) {
        return $query->where('created_by', \Auth::user()->id);
    })],
    'sale_price' => 'required|numeric',
    'purchase_price' => 'required|numeric',
    'category_id' => 'required',
    'unit_id' => 'required',
    'type' => 'required',
];
```

#### ج. في دالة update()
```php
// قواعد التحقق مع استثناء السجل الحالي
$rules = [
    'name' => 'required',
    'sku' => 'required', Rule::unique('product_services')->ignore($productService->id),
    'sale_price' => 'required|numeric',
    'purchase_price' => 'required|numeric',
    'category_id' => 'required',
    'unit_id' => 'required',
    'type' => 'required',
];
```

### 2. PricingController (صفحة التسعير)

#### أ. استخدام العلاقات
```php
// جلب المنتجات مع العلاقات
$query = ProductService::where('created_by', '=', Auth::user()->creatorId())
    ->with(['category', 'unit']);

// جلب البيانات المرتبطة للفلاتر
$categories = ProductServiceCategory::where('created_by', '=', Auth::user()->creatorId())
    ->where('type', '=', 'product & service')
    ->get()->pluck('name', 'id');

$units = ProductServiceUnit::where('created_by', '=', Auth::user()->creatorId())
    ->get()->pluck('name', 'id');
```

## 🎯 التأثير على صفحة التسعير

### 1. العرض
- **استخدام العلاقات** لعرض أسماء الفئات والوحدات
- **تحميل البيانات المرتبطة** للقوائم المنسدلة
- **فلترة حسب الفئة والنوع**

### 2. التعديل المباشر
- **تحديث الحقول الأساسية** في جدول product_services
- **التحقق من العلاقات** عند تحديث category_id و unit_id
- **الحفاظ على سلامة البيانات** المرتبطة

### 3. اكتشاف المكررات
- **فحص SKU** في نفس الشركة (created_by)
- **تمييز المكررات** بصرياً
- **فلترة المكررات** للمراجعة

## 📊 تأثير التحديثات على النظام

### 1. عند إنشاء منتج جديد:
- **إضافة سجل** في product_services
- **ربط بالفئة** (category_id)
- **ربط بالوحدة** (unit_id)
- **ربط بالضريبة** (tax_id) إن وجدت
- **ربط بالحسابات** (sale_chartaccount_id, expense_chartaccount_id)

### 2. عند تعديل منتج:
- **تحديث البيانات الأساسية**
- **تحديث العلاقات** إذا تغيرت
- **الحفاظ على العلاقات الأخرى** (warehouse_products, etc.)

### 3. عند حذف منتج:
- **حذف السجل الأساسي**
- **تأثير على العلاقات المرتبطة** (warehouse_products, purchase_products, etc.)

## ⚠️ نقاط مهمة للانتباه

### 1. سلامة البيانات
- **التحقق من وجود العلاقات** قبل الحذف
- **منع حذف الفئات/الوحدات** المستخدمة
- **التحقق من فرادة SKU** في نطاق الشركة

### 2. الأداء
- **استخدام Eager Loading** للعلاقات
- **فهرسة الحقول المستخدمة** في البحث
- **تحسين الاستعلامات** للبيانات الكبيرة

### 3. التوافق
- **الحفاظ على العلاقات الموجودة**
- **عدم كسر الوظائف الأخرى** (POS, المشتريات, etc.)
- **اختبار شامل** بعد التحديثات

## ✅ الخلاصة

نموذج ProductService له علاقات واسعة مع:
- ✅ **نماذج الإنشاء والتعديل** (مباشرة)
- ✅ **صفحة التسعير** (للعرض والتعديل المباشر)
- ✅ **نظام POS** (للمبيعات)
- ✅ **نظام المشتريات** (للمخزون)
- ✅ **نظام المستودعات** (لإدارة الكميات)
- ✅ **النظام المحاسبي** (للحسابات والضرائب)

**جميع التحديثات تؤثر على النظام بشكل متكامل! 🔗**
