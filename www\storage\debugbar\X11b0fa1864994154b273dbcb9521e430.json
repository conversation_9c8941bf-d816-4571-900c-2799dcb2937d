{"__meta": {"id": "X11b0fa1864994154b273dbcb9521e430", "datetime": "2025-06-06 20:41:21", "utime": **********.535244, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242479.835668, "end": **********.535274, "duration": 1.699605941772461, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1749242479.835668, "relative_start": 0, "end": **********.319399, "relative_end": **********.319399, "duration": 1.4837310314178467, "duration_str": "1.48s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.31943, "relative_start": 1.4837620258331299, "end": **********.535277, "relative_end": 2.86102294921875e-06, "duration": 0.21584677696228027, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02174, "accumulated_duration_str": "21.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.422576, "duration": 0.01727, "duration_str": "17.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.439}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.470908, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.439, "width_percent": 4.83}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.479274, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 84.269, "width_percent": 6.394}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.505294, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.662, "width_percent": 9.338}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-249222191 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242413154%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImV4WE5ENXBpWVVzbnhQQ0FNVTl3Q3c9PSIsInZhbHVlIjoicFpaVE9IRlcwTHJwcmc2U0phLzgxMllKZVZ0RWIvU21XOWJnendzaSsvdy82M2tGUzNvTU5PNWgrakxXT25VK1hBZVBlZ0d3Z3JpUHArMEc2a0pZRWhPVlNJMFhKSXlEckp0VFFleTlTblVJYUQrYlpib3JzVUYwNWJOcHA5bk9Ccm1PZXM1V0RxcVlIaTJIL2lqekFEUlRkeVBYSHlTNWtVUUlMM3VLNHFJRWVaQXU4S2dWRjlraGM1bkU5Wm5nVlA4VTBTZi9UaFhIUzZtdW5nTzFxTkVXTUxxMTZRYVRsdUcydWJQOFZndlN3TW1RM29mSWZsV0dwNHBaSTIxeGphQTh1ZXl0WXAvMk1NekdNbEU5M25QL1MwckFlT1dPWCt5NWVsYWhyWmdWcmZzZXdtZTViaTU4SzZXMXlxenVlbWc5aWZ4NlJDd0daSXR4Z2hrd25qRllnVGh2WDh2N1JBajg5RDc0c2VZS1cvc3QxMnNLbzBCNk96dXJIaHpMampBSG1VZTROZHpvcjVlY3RsVnZBUW5uWFFodmxJZDlrQ3QzdmQzTGdzZkVzMC8zM0oybldESUpFNmV5MmRkcTBrbWZ6NmR4UmpOdjVnR01FNDAzVVhvdmxKRGRuNjBKYjZ4OEwwYUxMVC9aTUZCc0w1MlRpMmpRSm13SmpwQUEiLCJtYWMiOiJjYWEwMTEyMDZkMDI4MTA3ODdkNGY4NjIwZTQ5NjM3OTBlMjY1NmZmODk4NjVlZTRiOTZjMzUyNTQyYjliMGFlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkUxL0o3U2w5alRuSzBBUlhWb3QrUHc9PSIsInZhbHVlIjoiTHl1RW5jVTJkUGtPOWRkQU5VeWpvem1Rb25NQk5ENThYQUw3TXZzcmgrZU5GaC9oUkdUd2NPcTN0aG1lWlh3UXJkRURxb0o4Vlo0b2JTMjBtYVVZRnFFU3d0RmdqNGt2Rjk2c1B4TGtPNis4ZFNKQ2ErQUtHR05LM2tjV2ZVSk15OEVYRTFmSGlDSXFzWFV1alhNdE9uTTlJN3grNWdwaDM0dGtyZzRWdHVDNC94UE8vWSszanJFRHdNd3FjakdCeGtZMmpQRjhlaHg2QnlHcXdYS3VXc21tZjVlRStLem1MMmtFS2xjbTlEL0lFcnIyUDJmNUdWemxZc3hRMFhMVlZuWUJ2TnJpQUlZMGRJeGFPMVgvbVFPTEl0UkszR05xNTZHNXRlOWdiYW5KMld0bWQ2V1hjOUJxRENCTkU3ZlJJS1VIR3BPdDR3UW8vZUY4K2paSXdhS3p5MzlQbmMxWDAwSmhtd1I4Z0Roc0hJNGZnN2lZR1c2QWFLRHhvZVNPeVVoOWpLTkpxVitUR1htKzB0ZC9HdDAwVWd4M3IvV0gzY1Jja0M1ZlpnVHFvanJva0lIOFZNb0Z6VUZXMVo3MDRiS0NKZWZ1alhxd2RIRk1FRmIvOStWWXhGVVJkU0o0Mi9XNU8wWldBT0pCS3lmT3JIblJ4LzRZS2J4ZVdLeUYiLCJtYWMiOiJiYTY2NTkxYmY1YTJiZTg1NTEwMWQwZmUzYTNhYmI5YzQxN2IyZDcwN2U5ZTgxMjdmYmQ3MzZlNTAwMWVlYTRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249222191\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1417386988 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6Wt4M4bI8NbCUmFc1THCmhEuEOoDYpn2bPL8rDvY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417386988\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-167702967 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:41:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5KbE5BMXJ1aklrSzJmZ1JzQnJ1QkE9PSIsInZhbHVlIjoiUnRhU1JkbUVWc0I5eGNnb1M5RkUvZldMSlpPRloxVXgwbkVOZE5ldGgwZmdWb2NjZ2J0VHI1aWR0UkR6RWkwd2dKYkd0N3RuOW15Y0ZJb2c3MmhYZ3pnb1JzUStlWjB1LzN6dkpKK29tRVJsd2gwdlF3UmFaNlBBc3BRRWxqTWs0WW1kRS8zV2JqWTJ5SFdiaE5WcHllbjRPcHdQWHp4alBGSFRVTEd1R2dNdWg0a0NSelAxSWVhcWdMMU41RnRtWmNibFdYSmVBeDdjdkNQcWYrNG9JV0U0ZlpiQkZuTUZFTTRMUGdQQisvTStLMklPTllxb0ljZFNxR3pDWmdSbnhyQVBYdnMyZnRibjVGejJCS0pZRUJQY1EzeUc3dnpWS0pTNmJma01DVDVXemRyUjJzUGxJV2tlbk9GNERqcjhBZExQSXAxUmJQUE5KMnZhVE9rZnpvU3puQWNNeVowYU56ZjQvN0ZpcndndUN2RDZMdTNPWTRWZ3NEclh3cDhaRm5MbXNuVGZock5oQmovakVFYTdYL1pFQTcvRHZNdm9PT1h6dlpva1IxTG0vUnVwWVNoeExqaXBabmVOMFdJK0hGYnNrZk9WY20wQkpaRXlXRk8vVGNkNGxGdXV4MG5MaStJOGpBNDMyTCtmWE9jNTZwemlaZlVlTi9KZjMzTVgiLCJtYWMiOiJhOTFhM2QyMDczZDczYjU2NTUzMTRjYjRmZGIxYzUxMTg3ZTlmYjQwNmQxMTMyNDA3YzYzMDE4ZTk1YWZhZDEwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im80a1d4TS9GejFqUUFQMGJxMTV3Ymc9PSIsInZhbHVlIjoiZmxsZUlrcFphMVhGVXNWQkNpUkxOYjNBaUlwaC9RVytNMk5uNlEzUWFnazM5Z0xwdHRoK25xdVJxckszeG4vT0dBZWwrdU82R1hRRXpXRlZNVTR2cjIyTWd1SE83aExJNXliVk9kRW04SGE2M0tKZEVnckt4dE1pRExod1lMdVY0TXA0c1JWdGN5SDRSUXY3SVFyTmkwaWk2OTBkR0xKQmsvRUloOEg0ckNZRE5LNkRBaXRRYks5UXFYY3pMRkpoUCtJOGorK3ZHeUdzMHI5L2s4dkVHbGs1MU96VW43QWorQXRXUDVVT29Ocm1QbllJcHZPMDMya1NYVzFIZ3M1bVR6S1JENzVDVEkrU2VRWEwvSy9LdGlGcEs3QjdJTEtCSC91M3BsSU5IWkZRUVVnNFdYL2doK2YyTzRwdFlFY0dsWEIwdXRwMTZab1RhT2s2aWZFZGphRUlWSmh2ZU8vMDN4K09lWDhUUjUwaEhoUm1CWFhEdmtsVXJBdUJlRXU3U2pEZ1pwSlNPT3NGWTlobUpGc2tSaml6cjB3emJlY0VKUVZQVDlQQ2hUUk02TnZCeVZXVW01RzkralhLU3NBRGljZ1JvcldmS3dZMUkvL3h1TW0vcjcvbDRJZ3FzakZMZDZBV0ZLS2NTd0hGWE9DbmtHcUVyZ0VibE5BS0tGcUwiLCJtYWMiOiIxNGE0NzU4ZjNmMmViNzYwODVkZjdmODg3OTZjM2E0ZDAyMWRjMmRhOGU1ZjI1OTBjODJjOWE2ODE5ZDYwOWQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5KbE5BMXJ1aklrSzJmZ1JzQnJ1QkE9PSIsInZhbHVlIjoiUnRhU1JkbUVWc0I5eGNnb1M5RkUvZldMSlpPRloxVXgwbkVOZE5ldGgwZmdWb2NjZ2J0VHI1aWR0UkR6RWkwd2dKYkd0N3RuOW15Y0ZJb2c3MmhYZ3pnb1JzUStlWjB1LzN6dkpKK29tRVJsd2gwdlF3UmFaNlBBc3BRRWxqTWs0WW1kRS8zV2JqWTJ5SFdiaE5WcHllbjRPcHdQWHp4alBGSFRVTEd1R2dNdWg0a0NSelAxSWVhcWdMMU41RnRtWmNibFdYSmVBeDdjdkNQcWYrNG9JV0U0ZlpiQkZuTUZFTTRMUGdQQisvTStLMklPTllxb0ljZFNxR3pDWmdSbnhyQVBYdnMyZnRibjVGejJCS0pZRUJQY1EzeUc3dnpWS0pTNmJma01DVDVXemRyUjJzUGxJV2tlbk9GNERqcjhBZExQSXAxUmJQUE5KMnZhVE9rZnpvU3puQWNNeVowYU56ZjQvN0ZpcndndUN2RDZMdTNPWTRWZ3NEclh3cDhaRm5MbXNuVGZock5oQmovakVFYTdYL1pFQTcvRHZNdm9PT1h6dlpva1IxTG0vUnVwWVNoeExqaXBabmVOMFdJK0hGYnNrZk9WY20wQkpaRXlXRk8vVGNkNGxGdXV4MG5MaStJOGpBNDMyTCtmWE9jNTZwemlaZlVlTi9KZjMzTVgiLCJtYWMiOiJhOTFhM2QyMDczZDczYjU2NTUzMTRjYjRmZGIxYzUxMTg3ZTlmYjQwNmQxMTMyNDA3YzYzMDE4ZTk1YWZhZDEwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im80a1d4TS9GejFqUUFQMGJxMTV3Ymc9PSIsInZhbHVlIjoiZmxsZUlrcFphMVhGVXNWQkNpUkxOYjNBaUlwaC9RVytNMk5uNlEzUWFnazM5Z0xwdHRoK25xdVJxckszeG4vT0dBZWwrdU82R1hRRXpXRlZNVTR2cjIyTWd1SE83aExJNXliVk9kRW04SGE2M0tKZEVnckt4dE1pRExod1lMdVY0TXA0c1JWdGN5SDRSUXY3SVFyTmkwaWk2OTBkR0xKQmsvRUloOEg0ckNZRE5LNkRBaXRRYks5UXFYY3pMRkpoUCtJOGorK3ZHeUdzMHI5L2s4dkVHbGs1MU96VW43QWorQXRXUDVVT29Ocm1QbllJcHZPMDMya1NYVzFIZ3M1bVR6S1JENzVDVEkrU2VRWEwvSy9LdGlGcEs3QjdJTEtCSC91M3BsSU5IWkZRUVVnNFdYL2doK2YyTzRwdFlFY0dsWEIwdXRwMTZab1RhT2s2aWZFZGphRUlWSmh2ZU8vMDN4K09lWDhUUjUwaEhoUm1CWFhEdmtsVXJBdUJlRXU3U2pEZ1pwSlNPT3NGWTlobUpGc2tSaml6cjB3emJlY0VKUVZQVDlQQ2hUUk02TnZCeVZXVW01RzkralhLU3NBRGljZ1JvcldmS3dZMUkvL3h1TW0vcjcvbDRJZ3FzakZMZDZBV0ZLS2NTd0hGWE9DbmtHcUVyZ0VibE5BS0tGcUwiLCJtYWMiOiIxNGE0NzU4ZjNmMmViNzYwODVkZjdmODg3OTZjM2E0ZDAyMWRjMmRhOGU1ZjI1OTBjODJjOWE2ODE5ZDYwOWQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167702967\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}