{"__meta": {"id": "X4714b183ca4d627e3a9b0617a12b0abb", "datetime": "2025-06-06 19:26:29", "utime": **********.09302, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.531895, "end": **********.093056, "duration": 1.****************, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": **********.531895, "relative_start": 0, "end": **********.880823, "relative_end": **********.880823, "duration": 1.****************, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.880851, "relative_start": 1.****************, "end": **********.09306, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "212ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.008280000000000001, "accumulated_duration_str": "8.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9914541, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.217}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.02612, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.217, "width_percent": 14.614}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.066123, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 79.831, "width_percent": 20.169}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237986230%7C25%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxtVGI0UG50SHJST3o4aEhucEJtUHc9PSIsInZhbHVlIjoiUVpWdVFBTGI3Rm8rV1BobnUwQW1lMXhHL2I1R3JsU1RJZjFmaFlQQmdyVUZOTGtwUEt0ZFA0NzlPcCtmTGxVYm5BZWVCU3c1QUhxODBlY3JRN3lXNzA3ckZsYjZDc1licXNzbUxlUWh1MVBzUXlTUHFpTnRnSnhldXNJYUNHQkZLdjI1UCtHM3hkMkxGR3lXT0RGVWRmdlhoQlA5R1RMZFNUTllZOFRKanNtQnVNVTBRM3ZiWTJJbUxwc1BCNVJrYWo4WDFRZmNMRHNTVjFFeWsvS3RZMm5NZkRnWmVkNDBMMEdGakhvNER5TThkS1IvZEx4QlZySlJXVWg3WXpwUVJ6MUZJMTJLRDl1bDdGN3FnYXVRTGpSbm8rZm0rekdMSTFGR2YrU2xDV0FMenV0V29mSFo2ZDZtcEY3bGw1dEZHS3k5NVlZcUtVL1hJUmdVZ1FLZ3lHQjA1N1RoOUpkcmxTaGRkRTFTYmlKNjErVVNrMTNTOE5PMk9FM1JyU0lxRW5NdTV2YzJXanZ2L2tpQ3RVQS9ia25xSEFtcFJ0TFpNOE43cWZXU1F0UmdxMWJKbXNUczFZNHRTeVREWC9CQUUxYkZDOVVWRk03YWZGa1RqYm5sUkdYc1hrU1BnNVY5T1JNTlZTbHFjc0NWUTdOTFpWTWVKTHM0NndOMzFLSzUiLCJtYWMiOiJiYzcxMTI3OTYzNzdhMDkwNDAxZjhkOTZmMDhjMTNmMjFlZjBmOTc2NWZkNWFiODA3ZWZlNjcxZDNjMmY4NzdmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVtMkJ1L0FjU0Q1aGtML2FmeW0weEE9PSIsInZhbHVlIjoiNytuaFlnaFljUGRKRS90RDI4azRRQ0dlK1VYN0pwVy81ZWlEYTJVeDBDaHNxKzEyWEZNK1B0MkVSZDZudXZjN3h6MGhacVYxSGtFWEY2a3drTGVLbDNsZGNlM2ZZdVppYVlKaUdNRGlEQmxrc1hPUEJiRm1nR0hybHNDT3NXUmMvN1QrZjdEUW5Cb2lZSFJrVWh6WHRKZ09xYnZjSXBzSlQ5M0g2VmVIWUhMWkJSTUthbEg5WFhrdFB0Q2dCVm91ZXdSMDl0Z3BCamhGY2VLeVJOU0E3bGZjNTNSTVJMeC9HUmkyLzJwdG4rOXJBZHQzYmFhMndZRDFvTmZBZTVtVHdmZzdZcVdRUEJCbFFJUGRsMXdmSnEybXBiVW04RElFbUdNM21QNUh5cWRHb1M1ZnB5S3hOMXZNcTkyZ2ZIM1lQZ3RsWHBPUDlDakduckNXNHdieXpYRjVIenB3VWxBSnIzSnBUVkNsSWpvSTE4TmVmNmc4Ky9wNng2Q0Z6Y3NBWVFhZUROUHlVSEo4WlZWWmFBL2hYNVY2YjdROWtIbmZDbHMxNDhwdms5SVd3TVRHUlJreEFhR2Z2ak42SjFtZUFyUDJUOHFnL0xhNHFLSnA5ZFdYK0w0Lys3ZUl1bC9CSk1zS0RGaGxPU0tMTDQ4T040dzVqaEVYMGhoOGFXdzgiLCJtYWMiOiIxMmU1NWQ1MjM4ZWQ1MjBlOTZjYTI1M2RlN2ZmZjNhZTk0NThlODUzZjgyMGYwYzhmMDkxN2IzYWQyNmUzYzE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-595726674 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595726674\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-181181765 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:26:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxtWlMxK25uaWtoRkNBVCtQa05pbWc9PSIsInZhbHVlIjoiRzlnVWhzc3NNYUw5dW1VQnhwMERPVS9VN3gzUDJLMnQ2Vm8za3BmN1ZreFFPbTBQSHVCRkhLcXRnYzFQU2hMTUpjS00zNURVaFg1akZiYmxDZGUyYisrWkFWL0EyNU03SlZZQXlyOFB3SVl1MGdPNFFDSUFnVk9BdmgycWlKSnFhZFZHNTBSTnNobXVHaGF2THRqMnVyVGQ0eWdsQ2lpUlArUTM5a3U4eW1JSzljUWhpT3dBeC9Ub0w1MURwLzd4RjhZaGNvQkUycVZ2Y2tBSDNCMHkwZE5leEx1MG1jYncyOURERUVjOHQzMTE5Zm9lVWwzSWx2blBFR04xNE53N1dVcUg1OEd2aWVQanJuUGYvaStZam1FM0NuQW9yWEJ1Nm0rVUFmVHh6TmtmRDZXb24xQ0s4QnlISzFIL2FFWXR3WlBZY0dJekgzWDgvTXZjaDVtU2VFOEtDWWVzSFd0OUwwQWxZQndvamRBVDdXVkZmWGJzcThkR0U1YUZpSGpvdnJ0S2FvM2l4N2pBaldJUDdyYW5BUWpZZ0V5a0RzSXZ0b2NaamxHWndndk1UdmZNNlgvVHVuWUJUNFBWVWdJT095ZXc2QmczUDBVWUkzU3VUcG1nT3diY2YwdytlTkgyOEZXUGFIT1o3NG1ESklPcWxUbFVncHhnSTU3OW1VU3UiLCJtYWMiOiI1ZGZiMGNkNWJmNmMwZmQwZTg1MTVkODZlYzdlMTc5MzQ4MWY0ZTBjZWU5MWUwMzE1ODg5NWZjZWU1MGI5NDRlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpKWVBlWkJKVUJjaFhhL2o2NG5TVFE9PSIsInZhbHVlIjoiK3ZRanVRUzlTMGxVeHNNY1VBUlZLbHVscW5QaFFKRUZMa0ZSdDgwQmFHNDh4RzAzTHdvQUU1MWxBOE1ZNVBBR09FZFg0bWVlRVJKUnYzYXdyWUN3aU8zYzVzY1ZRWnFEV1p2RGwxck1VdnN1UUg2MTZqT3dZWkRVdVNFOVI2ak42U1pWV1o4b01URUtUTTF6VjRMY3E3b2pGcFhObWQxMkVVK2s5TTNHOE1tVnd0Y0ZsWXJjbUVWc2tEak5VbXdXb3N6cUNOaWJzZEdraXZCYkUyVktUL0JWYjVOSkEyaENzRVl4QTdNY3NOS09vd0tnb1lVVEsvejJodXBETVUvS1FKYWprME81Q3E4VTVacEVJbUJtSVA3NXBaMEpyRVlJbVBEUmpvSElGUnh2dDBJVTA3ZmErekhRUGFqMlBhRHVxR0kzNDA0NFlCNFdtNWtyUWJiak9UUzlDVDJJK1crd2hiYS9VMjF1K1hHT0JkbXJzSkgyZ0RWWFZWYlp4RlgvYjJRM0NCdEh3bDFJMVBQeUo2SjNYMXd0WVVoQ2dtRFdLQjlDeXQvMEw5a0U4ekVwaGdqSWRvc1NZdmxrcWx2VU5kMGpNbXpFRnlIQzV1aGFUT05hZ2FaWE1qSnIrUXdhMWtsVkhEL2dYNVh1RnAzSExzQjlxa00reU1maUJVRFoiLCJtYWMiOiIwZmU3MDFiMGQzYmQ5ZGY4M2YxOTM0NjExMjE3ZjZkNjc5ZGMwOWZhZDgzODAyM2ExMmFmNDMxNTllMjdjZGNiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxtWlMxK25uaWtoRkNBVCtQa05pbWc9PSIsInZhbHVlIjoiRzlnVWhzc3NNYUw5dW1VQnhwMERPVS9VN3gzUDJLMnQ2Vm8za3BmN1ZreFFPbTBQSHVCRkhLcXRnYzFQU2hMTUpjS00zNURVaFg1akZiYmxDZGUyYisrWkFWL0EyNU03SlZZQXlyOFB3SVl1MGdPNFFDSUFnVk9BdmgycWlKSnFhZFZHNTBSTnNobXVHaGF2THRqMnVyVGQ0eWdsQ2lpUlArUTM5a3U4eW1JSzljUWhpT3dBeC9Ub0w1MURwLzd4RjhZaGNvQkUycVZ2Y2tBSDNCMHkwZE5leEx1MG1jYncyOURERUVjOHQzMTE5Zm9lVWwzSWx2blBFR04xNE53N1dVcUg1OEd2aWVQanJuUGYvaStZam1FM0NuQW9yWEJ1Nm0rVUFmVHh6TmtmRDZXb24xQ0s4QnlISzFIL2FFWXR3WlBZY0dJekgzWDgvTXZjaDVtU2VFOEtDWWVzSFd0OUwwQWxZQndvamRBVDdXVkZmWGJzcThkR0U1YUZpSGpvdnJ0S2FvM2l4N2pBaldJUDdyYW5BUWpZZ0V5a0RzSXZ0b2NaamxHWndndk1UdmZNNlgvVHVuWUJUNFBWVWdJT095ZXc2QmczUDBVWUkzU3VUcG1nT3diY2YwdytlTkgyOEZXUGFIT1o3NG1ESklPcWxUbFVncHhnSTU3OW1VU3UiLCJtYWMiOiI1ZGZiMGNkNWJmNmMwZmQwZTg1MTVkODZlYzdlMTc5MzQ4MWY0ZTBjZWU5MWUwMzE1ODg5NWZjZWU1MGI5NDRlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpKWVBlWkJKVUJjaFhhL2o2NG5TVFE9PSIsInZhbHVlIjoiK3ZRanVRUzlTMGxVeHNNY1VBUlZLbHVscW5QaFFKRUZMa0ZSdDgwQmFHNDh4RzAzTHdvQUU1MWxBOE1ZNVBBR09FZFg0bWVlRVJKUnYzYXdyWUN3aU8zYzVzY1ZRWnFEV1p2RGwxck1VdnN1UUg2MTZqT3dZWkRVdVNFOVI2ak42U1pWV1o4b01URUtUTTF6VjRMY3E3b2pGcFhObWQxMkVVK2s5TTNHOE1tVnd0Y0ZsWXJjbUVWc2tEak5VbXdXb3N6cUNOaWJzZEdraXZCYkUyVktUL0JWYjVOSkEyaENzRVl4QTdNY3NOS09vd0tnb1lVVEsvejJodXBETVUvS1FKYWprME81Q3E4VTVacEVJbUJtSVA3NXBaMEpyRVlJbVBEUmpvSElGUnh2dDBJVTA3ZmErekhRUGFqMlBhRHVxR0kzNDA0NFlCNFdtNWtyUWJiak9UUzlDVDJJK1crd2hiYS9VMjF1K1hHT0JkbXJzSkgyZ0RWWFZWYlp4RlgvYjJRM0NCdEh3bDFJMVBQeUo2SjNYMXd0WVVoQ2dtRFdLQjlDeXQvMEw5a0U4ekVwaGdqSWRvc1NZdmxrcWx2VU5kMGpNbXpFRnlIQzV1aGFUT05hZ2FaWE1qSnIrUXdhMWtsVkhEL2dYNVh1RnAzSExzQjlxa00reU1maUJVRFoiLCJtYWMiOiIwZmU3MDFiMGQzYmQ5ZGY4M2YxOTM0NjExMjE3ZjZkNjc5ZGMwOWZhZDgzODAyM2ExMmFmNDMxNTllMjdjZGNiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181181765\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-805905950 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805905950\", {\"maxDepth\":0})</script>\n"}}