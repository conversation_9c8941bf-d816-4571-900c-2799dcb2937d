{"__meta": {"id": "X49196f68278541a2261b178a51ff3b16", "datetime": "2025-06-06 20:39:58", "utime": **********.68017, "method": "GET", "uri": "/user-reset-password/eyJpdiI6IkIxWnJVSVd5eUYrMWI5aDAvNVYwK2c9PSIsInZhbHVlIjoiNGJkLzdIaDdCZWsvSEFKYkJ4ZnFjQT09IiwibWFjIjoiMDMxMzQyNDM5ODJmMTM3ODk3ZGE4NzVjMzM1YmQ3NGEzNTNhZDY2MjQyN2YwYjMxYmEzMWFhZDcxYTE0YjhiZiIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242397.086192, "end": **********.680204, "duration": 1.5940120220184326, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1749242397.086192, "relative_start": 0, "end": **********.510829, "relative_end": **********.510829, "duration": 1.4246370792388916, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.510848, "relative_start": 1.4246561527252197, "end": **********.680208, "relative_end": 4.0531158447265625e-06, "duration": 0.16935992240905762, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44544144, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x user.reset", "param_count": null, "params": [], "start": **********.664969, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/user/reset.blade.phpuser.reset", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fuser%2Freset.blade.php&line=1", "ajax": false, "filename": "reset.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.reset"}]}, "route": {"uri": "GET user-reset-password/{id}", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\UserController@userPassword", "namespace": null, "prefix": "", "where": [], "as": "users.reset", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=575\" onclick=\"\">app/Http/Controllers/UserController.php:575-582</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00658, "accumulated_duration_str": "6.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.621894, "duration": 0.00562, "duration_str": "5.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.41}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 578}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.63813, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "UserController.php:578", "source": "app/Http/Controllers/UserController.php:578", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=578", "ajax": false, "filename": "UserController.php", "line": "578"}, "connection": "ty", "start_percent": 85.41, "width_percent": 14.59}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1"}, "request": {"path_info": "/user-reset-password/eyJpdiI6IkIxWnJVSVd5eUYrMWI5aDAvNVYwK2c9PSIsInZhbHVlIjoiNGJkLzdIaDdCZWsvSEFKYkJ4ZnFjQT09IiwibWFjIjoiMDMxMzQyNDM5ODJmMTM3ODk3ZGE4NzVjMzM1YmQ3NGEzNTNhZDY2MjQyN2YwYjMxYmEzMWFhZDcxYTE0YjhiZiIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-448886517 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-448886517\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-191264438 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-191264438\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-45317448 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-45317448\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242395580%7C19%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBFYnZEUXFCNGtHeXVWYm5HaWZCNVE9PSIsInZhbHVlIjoick96ZzlINHAzZWd2bEp2SG1xOUlKQjlSUm1IZXA5NG0vYzJpc0dady80WFdacWJQbm1zcTIvWkhITVg3WC84aXhqdVdCcHRMdGVzUUttUVpaN0hwbUM1MHpyQ0J3ZktwMmNQdTdDWUV0QnNNTkxLTE9vS0dlNzFHK0tPQ0VQVCsrUXdXYU55K1BNd1RQdFFnS3ZuNU9hOEtCZXdwc3dpMWpxai9JdWtXdHIzcDZ3Q0ozaE1GbDc3S2xXUVhKU3lXYklzNnRpUUlFdE1SZ1pFc2NlK3YrczVxRndQNzhCMWFYaGI0WUZkTkVXREE3K0F3TVdzMnlINHRDRW5RSEx0M2VmejlXcUR6YTdRRUxMcDNNZmd6TTJBRHVKREszQnkrL1NPTEtOODYwdzdndSs4bVYxMWdhbnJJNjVGSDRDbE9oaDc3ME5xQmFGZ09adTIzWnJ4akU2R2dKazBpNUlVM0QzYzBONDhLSmdMNzVnZUN3Rk1JTkxYQTNsc2hicWZpMVpOZThFeEM4QVhUNTh3TVFhbE1vemNxcE5IVWVTb2dIeXFTeFVJTVR4UzhOODk1RFF0T2FaVU5XYUpWcEFZQWx6WDQxNzc0SDdyS1VTMTU1cGJrSVB0dWNWM0dSOXk1cnVEbHlJdHV0Z00vcW5xZTZ0RFhocDlhODBxc1FUcDkiLCJtYWMiOiJkNDA2MjIwYzI2N2Y0NTc4NzQ0NDY0OTcxMDZkNjZlNDA4ZWQyYWZjMmNhOGI0MGJmZjUyMzkyZjNiMWNmZGQ0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxBanFDSmlFY2lrelNPSTJJNVl4T3c9PSIsInZhbHVlIjoiMjNaUjFyTnVVM0FVV1h0U1pYMllXU2dTcDVHRFZYdEJnUUgrK3gwMzQxQ1J3Mks5MklITGh6VTBVMUdtNmVKaXA5MDZiMVVXR1pVTE5HMVIzZUpOR0dRNWtUcU5USzhCR0VnbG1YaHA4bmk0WXNKUnF6aDlxTm8xR2U1OVhWbURReCtjM2UzUm9RQ3dOK1pnZ2RCMHlCa0xTYkV0K2ZIOVNpY0tXN2RSWGNzbEx1Y08yVkd3Qzhic3hwWWpJemo3MURqcnVHaW52bGFscXNobC9HdEtydDEwUFJFNmY1WmEvanN6RzZ1dTNpZFV5NTNCNVRwelVQZVRXOUdBWmZBVG45Y3pCaTZTVzJQSnJsY0JHTWtnd2J6Mit5RHVKeTdvN1BEUWVhNkFQcm9XUTZKcER4UlI5V01TMTVBeXJEKzUvdXdTekM5ZjlUS0o4ei9oVjRFV3RPeE16SmdZc0ZzQkxTa1F4TW9BR0xsQzlZcWJxTHV0OHcwb2YvNHgzekpuUndGb0oxeHVmb2hFZytSYTJWYmJYTlBUNUFjSUs0K3d6MTZoOXpJL2RHRlBKbXNOc0d3WEh3L3p2TjJyc2diOVd0TnpidjkwRGxwNURSM201YlpnK21hNTJidm5uUXZIbjZCcFpIZ1lRV2FsWGtDenkrKzVYN1Y5ODg0UlRtT1YiLCJtYWMiOiJjZmYxNWI2Yjg3N2M3OGJjMzk0YTI3OGVmZjhmYmZlNGM2MmExNGVhZTYwZDRlM2NiOTMxMDA4NTE5NTkyMjRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-64840194 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5SyoJjvx9ER6DwaeNylpmlrCKyunmBMNBrM7fYhk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64840194\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1294983158 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:39:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iml3WlMzMGMzZHgzcC9pcldibzY3SXc9PSIsInZhbHVlIjoiSnJ1amRoalJHSDlEMlhUWVAwSTFLL0NacCtNMy9kaTRMenNNQmE2WGZiRmVjcFoxTnlYcG5EMjNhY1J2VUFNR2xjQ25OS05wYUlDUzRqV2VJcWdEV1UvZzFjQ2hSejJnU1dMRVR4NlBDOEdlVmtmU3JxSGlRbmYxeVB6cmZNL0dhZ3FsNjg4eWJmSThOV2lEYkVORjY2VTh2Z3NKelhrdkVxTHM1azRGa1hkb1BOVG02cHVsek5Yb3kveHB4VXhRU3VGdmEwOVZTUFRENllISk1rU0lYUllqZlhTL2d6clZ5bnRrTE9raVlPZEhyN1dHYmFjSkVuTWJKM1NPdUNHaUJtR3ZFMXpUdVlKNFpIcWs4d3pwZkVyeG1MWSt0YWY3OFZScjQ5Y3lBV0d1aDAzUHRXZlRWVUw4WUlIckdCUERoVTlTVks2eUxRTWQ2alN4WmZiUGFLMVoyLzF3eTNsdHFudEdsVm1sL3RzelpSZVdXVFR5RkpFOU5NVUVNb3pBUkNBaW9GeFhlQk1uT3JOc01oY05nbWlvWG5SeWJ4b0hqSmVRb0JQTS92ZS9SU1YwK0pPaFQ0OElueDZ2UzVNeDNWOU1ld1dHK3M2SXVXdmt0SjBOUlQ0cERmbjRCMUxHTFg2bTFXU1dXazBaNjJ2Z1VFTGZMcHBnbVVFRDkrRDgiLCJtYWMiOiI3YTBiZDM2ZDhiNDE0NTM0MzQ3ODdiYWNhMmI5MjlhYjMwNjY2MDU3ZjM3NWEzMGFjZTJjYzMwYmE1OGE1MjhkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxLL25XZGE0S0V5Kzc1ZFMrYk1MVlE9PSIsInZhbHVlIjoia1BvU2NHMHpWRGtWUDRiZm5peENsVys1VzdJTlFPWnFZbjBmazQ5ZVR6ZnlhN2lmbVpFMHdEam5jNE10UVR1OEpVWUxTbjRQejBIYWtjaTdvME9zMjh5dU5sdHBBODRKMi94TlA2UmdKRkNvYVNVY2pHKzFJQ3lkZzNsSGNjRkJSaWpEVnBCUWFEY1NuR2JicFlESTZBdkpwUVhwd0F2anRlMFA4MWFVM3h2V0czWjFjWmF4WU1UYXNQQzhYYkU5VnFGeGIyR090YnRnV3FDSjI4QktYWjBDaFBvNlZhNVp0N2h3RGFJTDZIckU5ekJFeE5TM29QaWkxQVl0bEYxOE8rbkdSdlgvUDhuRmluUW5zbEw0UjJRZmJ3bklFRWQ2emcxa3F5eGZCRVEwZ25zV0JLSHpLNjZCRDNtQkY5dHMwT0JadVlHUXBSVi9NRnRrZldHMEtKd2hsYnRCblAzRnpxMGlEYkR0Z3U4eGdpNm9tUTVzYWt3cXZsMXJCVUROeTdZbFgyYnB2OHllTTRrQTFpU0RsRmVOM2lKVklKRFE0b1Y0cTNSWFNEb0M2RDh3bVBkL3czcHl2ZGRtV2E5WUp4MkJGT1UzZW5XdXdncHgxN2twRHk4MEJOZW9qcTExcUFBNitrR3NZOXNqNyt6WnppRWJTT29vaG5wOHZ4YlYiLCJtYWMiOiJkYmY3ZDkzMDhiODQxOGY3N2IwZTI2ZTRhNzk4NDgxYmNkNDg2MzY2OTI5MjNmZDNjMGY2NjllZjRmYTA1NTEwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iml3WlMzMGMzZHgzcC9pcldibzY3SXc9PSIsInZhbHVlIjoiSnJ1amRoalJHSDlEMlhUWVAwSTFLL0NacCtNMy9kaTRMenNNQmE2WGZiRmVjcFoxTnlYcG5EMjNhY1J2VUFNR2xjQ25OS05wYUlDUzRqV2VJcWdEV1UvZzFjQ2hSejJnU1dMRVR4NlBDOEdlVmtmU3JxSGlRbmYxeVB6cmZNL0dhZ3FsNjg4eWJmSThOV2lEYkVORjY2VTh2Z3NKelhrdkVxTHM1azRGa1hkb1BOVG02cHVsek5Yb3kveHB4VXhRU3VGdmEwOVZTUFRENllISk1rU0lYUllqZlhTL2d6clZ5bnRrTE9raVlPZEhyN1dHYmFjSkVuTWJKM1NPdUNHaUJtR3ZFMXpUdVlKNFpIcWs4d3pwZkVyeG1MWSt0YWY3OFZScjQ5Y3lBV0d1aDAzUHRXZlRWVUw4WUlIckdCUERoVTlTVks2eUxRTWQ2alN4WmZiUGFLMVoyLzF3eTNsdHFudEdsVm1sL3RzelpSZVdXVFR5RkpFOU5NVUVNb3pBUkNBaW9GeFhlQk1uT3JOc01oY05nbWlvWG5SeWJ4b0hqSmVRb0JQTS92ZS9SU1YwK0pPaFQ0OElueDZ2UzVNeDNWOU1ld1dHK3M2SXVXdmt0SjBOUlQ0cERmbjRCMUxHTFg2bTFXU1dXazBaNjJ2Z1VFTGZMcHBnbVVFRDkrRDgiLCJtYWMiOiI3YTBiZDM2ZDhiNDE0NTM0MzQ3ODdiYWNhMmI5MjlhYjMwNjY2MDU3ZjM3NWEzMGFjZTJjYzMwYmE1OGE1MjhkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxLL25XZGE0S0V5Kzc1ZFMrYk1MVlE9PSIsInZhbHVlIjoia1BvU2NHMHpWRGtWUDRiZm5peENsVys1VzdJTlFPWnFZbjBmazQ5ZVR6ZnlhN2lmbVpFMHdEam5jNE10UVR1OEpVWUxTbjRQejBIYWtjaTdvME9zMjh5dU5sdHBBODRKMi94TlA2UmdKRkNvYVNVY2pHKzFJQ3lkZzNsSGNjRkJSaWpEVnBCUWFEY1NuR2JicFlESTZBdkpwUVhwd0F2anRlMFA4MWFVM3h2V0czWjFjWmF4WU1UYXNQQzhYYkU5VnFGeGIyR090YnRnV3FDSjI4QktYWjBDaFBvNlZhNVp0N2h3RGFJTDZIckU5ekJFeE5TM29QaWkxQVl0bEYxOE8rbkdSdlgvUDhuRmluUW5zbEw0UjJRZmJ3bklFRWQ2emcxa3F5eGZCRVEwZ25zV0JLSHpLNjZCRDNtQkY5dHMwT0JadVlHUXBSVi9NRnRrZldHMEtKd2hsYnRCblAzRnpxMGlEYkR0Z3U4eGdpNm9tUTVzYWt3cXZsMXJCVUROeTdZbFgyYnB2OHllTTRrQTFpU0RsRmVOM2lKVklKRFE0b1Y0cTNSWFNEb0M2RDh3bVBkL3czcHl2ZGRtV2E5WUp4MkJGT1UzZW5XdXdncHgxN2twRHk4MEJOZW9qcTExcUFBNitrR3NZOXNqNyt6WnppRWJTT29vaG5wOHZ4YlYiLCJtYWMiOiJkYmY3ZDkzMDhiODQxOGY3N2IwZTI2ZTRhNzk4NDgxYmNkNDg2MzY2OTI5MjNmZDNjMGY2NjllZjRmYTA1NTEwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294983158\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1070127114 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070127114\", {\"maxDepth\":0})</script>\n"}}