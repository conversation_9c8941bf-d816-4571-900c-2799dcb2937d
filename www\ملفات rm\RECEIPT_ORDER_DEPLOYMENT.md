# دليل نشر نظام أوامر الاستلام

## 📋 نظرة عامة

تم إنشاء نظام متكامل لأوامر الاستلام يشمل:
- **أوامر استلام البضاعة**: من الموردين مع تفاصيل الفواتير
- **أوامر نقل البضاعة**: بين المستودعات
- **إدارة المخزون**: تحديث تلقائي للكميات
- **تواريخ الصلاحية**: تتبع صلاحية المنتجات

## 🎯 الوظائف المتاحة

### نموذج إنشاء أمر الاستلام يحتوي على 3 أقسام:

#### القسم الأول - البيانات الأساسية:
- **نوع الأمر**: استلام بضاعة أو نقل بضاعة
- **عند اختيار "استلام بضاعة"**:
  - اسم المورد (من جدول الموردين)
  - رقم الفاتورة
  - إجمالي مبلغ الفاتورة
  - تاريخ الفاتورة
  - هل يوجد مرتجع (نعم/لا)
  - المستودع المستهدف

#### القسم الثاني - المنتجات:
- **جدول تفاعلي** بـ 5 أعمدة:
  - **SKU المنتج**: قائمة منسدلة مع بحث بالباركود
  - **اسم المنتج**: يُجلب تلقائياً عند اختيار SKU
  - **الكمية**: إدخال المستخدم
  - **تاريخ الصلاحية**: من جدول تواريخ الصلاحية
  - **مرتجع**: checkbox لخصم من المستودع

#### القسم الثالث - الملخص:
- **عدد المنتجات المدخلة**
- **تكلفة الوحدة**: (إجمالي المبلغ ÷ إجمالي الكمية)
- **الإجمالي النهائي**

## 📁 الملفات المطلوب نشرها

### 1. الكونترولر الجديد
```
app/Http/Controllers/ReceiptOrderController.php
```

### 2. ملفات العرض الجديدة
```
resources/views/receipt_order/create.blade.php
resources/views/receipt_order/index.blade.php
```

### 3. الملفات المحدثة
```
routes/web.php
resources/views/partials/admin/menu.blade.php
```

## 🚀 خطوات النشر

### الخطوة 1: رفع الملفات الجديدة
```bash
# رفع الكونترولر الجديد
scp app/Http/Controllers/ReceiptOrderController.php user@server:/path/to/project/app/Http/Controllers/

# إنشاء مجلد العرض ورفع الملفات
ssh user@server "mkdir -p /path/to/project/resources/views/receipt_order"
scp resources/views/receipt_order/create.blade.php user@server:/path/to/project/resources/views/receipt_order/
scp resources/views/receipt_order/index.blade.php user@server:/path/to/project/resources/views/receipt_order/
```

### الخطوة 2: تحديث الملفات الموجودة
```bash
# تحديث المسارات
scp routes/web.php user@server:/path/to/project/routes/

# تحديث القائمة الجانبية
scp resources/views/partials/admin/menu.blade.php user@server:/path/to/project/resources/views/partials/admin/
```

### الخطوة 3: تحديث الصلاحيات
```bash
# تحديث صلاحيات الملفات
ssh user@server "chmod 644 /path/to/project/app/Http/Controllers/ReceiptOrderController.php"
ssh user@server "chmod 644 /path/to/project/resources/views/receipt_order/*.blade.php"
```

### الخطوة 4: مسح الكاش
```bash
# مسح كاش Laravel
ssh user@server "cd /path/to/project && php artisan cache:clear"
ssh user@server "cd /path/to/project && php artisan config:clear"
ssh user@server "cd /path/to/project && php artisan route:clear"
ssh user@server "cd /path/to/project && php artisan view:clear"
```

## 🔐 الصلاحيات المطلوبة

النظام يتطلب صلاحية:
- `manage warehouse` (للإنشاء والتعديل)
- `show warehouse` (للعرض فقط)

## 🌐 الوصول للنظام

بعد النشر، يمكن الوصول للنظام من:
- **القائمة الجانبية**: POS System → أوامر الاستلام
- **الرابط المباشر**: `/receipt-order`

## 📊 مصادر البيانات

النظام يتفاعل مع الجداول التالية:
1. **purchases**: أوامر الشراء
2. **purchase_products**: منتجات المشتريات
3. **warehouse_transfers**: عمليات النقل
4. **warehouse_products**: مخزون المستودعات
5. **product_expiry_dates**: تواريخ الصلاحية
6. **venders**: الموردين
7. **warehouses**: المستودعات
8. **product_services**: المنتجات

## ⚙️ الوظائف التقنية

### البحث والتصفية:
- **بحث بالباركود**: فوري عبر AJAX
- **بحث بالاسم**: في قائمة المنتجات
- **فلترة بالمستودع**: تحديث المنتجات المتاحة

### تحديث المخزون:
- **استلام بضاعة**: زيادة المخزون
- **نقل بضاعة**: خصم من المصدر وإضافة للهدف
- **المرتجعات**: خصم من المستودع المحدد

### حسابات تلقائية:
- **تكلفة الوحدة**: إجمالي المبلغ ÷ عدد المنتجات
- **الإجمالي**: مجموع (الكمية × تكلفة الوحدة)
- **عدد المنتجات**: عدد الصفوف المدخلة

## 🔧 نقاط مهمة للاختبار

### اختبار استلام البضاعة:
- [ ] اختيار المورد والمستودع
- [ ] إدخال تفاصيل الفاتورة
- [ ] إضافة المنتجات بالكميات
- [ ] تحديد تواريخ الصلاحية
- [ ] معالجة المرتجعات
- [ ] تحديث المخزون تلقائياً

### اختبار نقل البضاعة:
- [ ] اختيار المستودع المصدر والهدف
- [ ] إضافة المنتجات للنقل
- [ ] تحديث مخزون كلا المستودعين
- [ ] تسجيل عملية النقل

### اختبار البحث والتصفية:
- [ ] البحث بالباركود
- [ ] البحث بالاسم
- [ ] تحديث المنتجات عند تغيير المستودع
- [ ] عرض الكميات الحالية

## 📈 الإحصائيات المتاحة

الصفحة الرئيسية تعرض:
- **إجمالي أوامر الاستلام**
- **عدد أوامر استلام البضاعة**
- **عدد أوامر نقل البضاعة**
- **إجمالي القيمة المالية**
- **النشاط الأخير**

## 🎨 واجهة المستخدم

### التصميم:
- **متجاوب**: يعمل على جميع الأجهزة
- **تفاعلي**: JavaScript متقدم
- **سهل الاستخدام**: واجهة بديهية
- **متوافق**: مع تصميم النظام الحالي

### الألوان والرموز:
- **استلام بضاعة**: أخضر مع رمز الشاحنة
- **نقل بضاعة**: أزرق مع رمز التبديل
- **المرتجعات**: تمييز بلون تحذيري

## ✅ قائمة التحقق النهائية

بعد النشر، تأكد من:
- [ ] ظهور الرابط في القائمة الجانبية
- [ ] تحميل صفحة القائمة بدون أخطاء
- [ ] عمل صفحة الإنشاء
- [ ] تحميل قوائم الموردين والمستودعات
- [ ] عمل البحث بالباركود
- [ ] تحديث المخزون عند الحفظ
- [ ] عرض الإحصائيات بشكل صحيح
- [ ] عمل النوافذ المنبثقة
- [ ] حفظ تواريخ الصلاحية

## 🔄 الخطوات التالية

بعد اكتمال هذا الجزء، سننتقل إلى:
1. **تطوير نقل البضاعة**: تفاصيل أكثر لعمليات النقل
2. **تقارير متقدمة**: تقارير شاملة للمخزون
3. **إشعارات**: تنبيهات انتهاء الصلاحية
4. **تحسينات الأداء**: تحسين سرعة البحث والتحميل

## 🎉 انتهاء النشر

بعد اتباع جميع الخطوات، سيكون نظام أوامر الاستلام جاهزاً للاستخدام بجميع وظائفه المتقدمة!
