{"__meta": {"id": "X05dbed42ea9e776d9e171a37870de731", "datetime": "2025-06-06 19:39:24", "utime": **********.959952, "method": "GET", "uri": "/user-login/eyJpdiI6Ikx1MXhCMG9BcEVwV0JtOVk3KzVjNGc9PSIsInZhbHVlIjoiYTdSUlpxUm9BMlp3alRmcVVEcCt1dz09IiwibWFjIjoiODM1ZTI5YmViY2I5ZTg3OWQ4YmUzOTBkOTM0MzBmYjJlNjdiNTBlOGY4NDFlNTkyNzhmZDAyOTAyOWMxYWQ5YyIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238763.650142, "end": **********.960007, "duration": 1.3098649978637695, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749238763.650142, "relative_start": 0, "end": **********.800678, "relative_end": **********.800678, "duration": 1.150536060333252, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.8007, "relative_start": 1.1505579948425293, "end": **********.960013, "relative_end": 5.9604644775390625e-06, "duration": 0.15931296348571777, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44001952, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET user-login/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UserController@LoginManage", "namespace": null, "prefix": "", "where": [], "as": "users.login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=698\" onclick=\"\">app/Http/Controllers/UserController.php:698-728</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02686, "accumulated_duration_str": "26.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `users`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 701}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.881557, "duration": 0.01882, "duration_str": "18.82ms", "memory": 0, "memory_str": null, "filename": "UserController.php:701", "source": "app/Http/Controllers/UserController.php:701", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=701", "ajax": false, "filename": "UserController.php", "line": "701"}, "connection": "ty", "start_percent": 0, "width_percent": 70.067}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 702}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.914454, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 70.067, "width_percent": 5.175}, {"sql": "update `users` set `is_enable_login` = 0, `users`.`updated_at` = '2025-06-06 19:39:24' where `id` = 17", "type": "query", "params": [], "bindings": ["0", "2025-06-06 19:39:24", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 706}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.924113, "duration": 0.0066500000000000005, "duration_str": "6.65ms", "memory": 0, "memory_str": null, "filename": "UserController.php:706", "source": "app/Http/Controllers/UserController.php:706", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=706", "ajax": false, "filename": "UserController.php", "line": "706"}, "connection": "ty", "start_percent": 75.242, "width_percent": 24.758}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/user-login/eyJpdiI6Ikx1MXhCMG9BcEVwV0JtOVk3KzVjNGc9PSIsInZhbHVlIjoiYTdSUlpxUm9BMlp3alRmcVVEcCt1dz09IiwibWFjIjoiODM1ZTI5YmViY2I5ZTg3OWQ4YmUzOTBkOTM0MzBmYjJlNjdiNTBlOGY4NDFlNTkyNzhmZDAyOTAyOWMxYWQ5YyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User login disable successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user-login/eyJpdiI6Ikx1MXhCMG9BcEVwV0JtOVk3KzVjNGc9PSIsInZhbHVlIjoiYTdSUlpxUm9BMlp3alRmcVVEcCt1dz09IiwibWFjIjoiODM1ZTI5YmViY2I5ZTg3OWQ4YmUzOTBkOTM0MzBmYjJlNjdiNTBlOGY4NDFlNTkyNzhmZDAyOTAyOWMxYWQ5YyIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-896113473 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-896113473\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-77429285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-77429285\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-296864035 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-296864035\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1481145302 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238728265%7C55%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZCZTFkbkl1eDNHaDJJeVlvU3hCY3c9PSIsInZhbHVlIjoicDVvYnd5di9tdk4rL3gwR0JNTDhJekdBbHgyK1kwOVJRZjF2U1hGMWorN2xDZXdlWHJmQklZMUxEVmhzR0g1RFUzZDRDSTZDZDlrU2Y2TmJrbWNjcVVxaExpUXduMmFQMW95WW1USzZ4cGxlSlRaTVQ0bndWMWpleDcwdlRTemtMSVlUc1llS29CSDlIQ3JSNkhGY2ozSll2ZUJaVnJuNXM0YkFtN1ZETDh3OEF4dFNSVVlsaVl5Z2E3WERlRGdEVzAwZWw4MFV1TkRiZVRDN2wyL3FEdzQzR3h3YlhrSlFmRVlVV3I4UHRVbjQvTHcxYXF1NjRHdUM3OFIvcmRCTm9NVTkwWUF4dzV0RFc5Nk1DYlo5NHRzU05pb0RDUlBSbGpiVnJhMm1RVEFON3JVZDZHTGlPdlNpUGNSZlhpdEIvV0VQdjJCWmdTZmRjSTRHY2wzeXUxdFNtUC84eS9KQkhONENnYVpJVVZhWWpWbmtKZjdaY04yQk1tN3NyQ1M1ZnpSVE5jb1ZzTjdsRmZIZ3UzajRORUhxcVArUmdBT3Z0cUlTSktpUnZjQWE1RTYzMkJRcjhUYXREOHJYQUk5Zm1CbElLWUczWmh3dU9HVFhDaGhoZ1R3RzVWSVU1R2REeEhxZk11c3VOS0ZpYlhrZUtmSlY4bE80ZnRxWFQ4ckoiLCJtYWMiOiI3Mzg4ODg3YTYzMjBhZWRjYTFhYzExODM5MmFlYWY3ZDJmYzMxZGM4MDBmNjQ1OGRiMzBmMDQ4MGU4OGE0Y2E5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikxhc1dKcFRGS2xtMEs3Qno3Mi8zN1E9PSIsInZhbHVlIjoiK1R0R0dpc3JMY1FUc0o5UDhmUllzN0VtUG1HZEpxRUNTU0Y4ZTNxS1REcklQcjRCWGVOejVNUEdYWFRtMjQ0Ynp6NnZGQmZsMHJUbXVOUWlvTjVCMG9XOXRVY293NmtxUWh5QlA2eHVLcmEwdG8wNlJ1V093Smw0akJacnNhdSttNjQrQUhpTXduaEJoNFJ3NXNpK1VZSG1qQjBXMkNxLzczbVhxcHdBeFkyVEw1bVRnc2RBdStBU2J6VmVRazh2a09iekRzZTNaa3hDbml5bDNjcnJmYzcwdTMrYnNQR2lXb0NOeW9SazlvV2Z5aEw0SnF5NE51aENEZEM0OVM1V3VJV2pVM1NlcGhQdi80UXlaMTBSdjNXK1R1M3c4cTQ0WUZXU1B1MkdNYXB2SGh6d1J4Y2hpTXJPTGFNTHRxWkkvcG1USW05TWFZVkZQV2lTRmN1Wkx4Nk1pb2FaQkMyTmovb054Um12TGpaTTY1TjlGSStmUE1zbFFQT0ZqZkdkMlpTQ3pQcnd1WDRVUE54TVlSVk9jZ3AvaEU5YW1vL1RWSGRqTTUxY2ZaSFNrOU5UaDlZbDVRMkFaMmpzN2hyeEZHcjFRSlo3Z3MyTTRhQm9iZUNQanppRUxvR2x6UHVhSTFKWkdRVFVQZ3FnNVVrSTg2NzFxWW84WmVmcjdETjAiLCJtYWMiOiJlM2JjYjY2ZWNiYmIyZGUzZGZjMWFiOTgwZjFiNmI5YjQ1N2QxZmVhNTg1Y2YzYzg2YmNmNDgzMDdkMjdlYWUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481145302\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1185483872 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185483872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1467990258 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:39:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InM5dllqUkF6SkxjNWhucGxZSTI0T3c9PSIsInZhbHVlIjoickwyMzZSRmMyNHRyVmNrUFQxSlBHVnBDeTN2MHlVVUxycmpnZVRpQlZ0Y256RGxPY1poUFMxaTdqblpKZ3VyenkxbDVyNUQvOHpNaHQxQ3krT3l5VDdZVUhIWllZbTZ6bFZmN2txbGxjeVk1UTg5aUhUcHNDWUcrODVyRXJGNSswN0Y3L1ZVc3BhbVk5QjRaMHJKSW9USWVzam5SVHEvSlUxc0hyM3p0bU5RWHNSb1hBRVQ3WkRSS1M3d205VmhWaEczeDBUUWFtTzhnOWxaNVROTER1ZWV0b1A0cmlqRllRRDB2ak0rS3duY3Bpb2ZIbnZtVHBSR042MW91TDZwblV4T2Y4UEtucjVDckNSNGFLNTc0M1U1YXNjMzE5czRtRkZkaWhmRHlJZGpJM3hPZ1E5dmdHSGIxd3pZeXh4S01PSHF0dXVTaTVZT1RhczllZ285dHJmZ3Jsb01ROFRJSmU4dUtGWk5HbzZzbVQ3b0tubWlYNmVycWw1aXl4SHVvZURyTEVFRGhnTjI4ektITEVBN1g1bHFSamwyaWdFcWYrelNqQmw4c1JUTTczVFhmZG9YeDlnTjZ3L2lqZFB3c3RyZUZuL1ZrS1NVSitKSmN1U0syQVVsZVhOck5RZzJJVW55dHZZRTJPS0MrcHdIQVJTU3NRbzlIdDBKVmsxUW0iLCJtYWMiOiI2Y2RlNWJjMjA4YjEyMmE0MmE0OTUxMDYxZjJmOWZkMWY0OGQ0MDM4M2QwZTM4Zjc5NzM0MjFiODhhM2ZkOGM3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjQ4ZEFVaHBXL0dabzZhR2FyK2dKY2c9PSIsInZhbHVlIjoibzNoZGp0TmRaNVZEaFQycUNBd29QWnpkeXpSVkFwT3NPc2t2R3RUdUNyY0E3QTdaQlpFeVBRazd2VEFvQ1ZaQWx0MDVTSE1hN0xrRFhHVXFjdFhVSWxTVWhyNmtBengvVEI1QmNtTHpPZjZla3cxR1hTNGdia0hPcGcydnM0RzFsQmVGOTR6b1I1N0wxTkp3RkpCemFVa3hmYytnUjgzTDRrQVVIZjgyb05zVEdIVmIzVHJjRkFEOVV4S0s0STFwTU9ubmlOaTdSZXBVYjFrejQyYS8yM2VSQml4aWJyYXZIbEY1ZmVaYXBsbGtlYmFnc3VXNU9Mc0s3RzBKQkJ0TWp5c25IRHp3VFF6Wk9KY2ZSdkkyNzJOZkEvOHR1bi9US3VjUk1sWXRwbW4xUFgySmkvOFdUa2E3ZURrcWtsbXFZdnBmOEdhc0VVRDRZYnltQjArOFVJRjQyWGhkeXRUOSs3UlFuQUZWYTBYZWt5SkRFell6ZG1QRVJYbCtpdFdOYU1Xa2c5VjVzZG1vZVd1MHc1Mm9SdVh2TVVSeVQxejlyR0ZpQXc1M0FUZG9ZcW1ybk5WaGNrZTNWUVVEV3pDb0d3SkRWQzMxSDlZaVdub2Z1d2FkQUR2RjVkWkhienRpNlRxYzlhSm5PYkhFYWdPK3p5dnNwK2MxblEvS0xZOXUiLCJtYWMiOiIyMzk1Mzc3ZTRkODAwY2E2NTYxNmY4M2M0Nzg2YWEyMTcyOWI2MGFmNTYzYzk2YzdiY2VkMjg0ODc2NDQwODE2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InM5dllqUkF6SkxjNWhucGxZSTI0T3c9PSIsInZhbHVlIjoickwyMzZSRmMyNHRyVmNrUFQxSlBHVnBDeTN2MHlVVUxycmpnZVRpQlZ0Y256RGxPY1poUFMxaTdqblpKZ3VyenkxbDVyNUQvOHpNaHQxQ3krT3l5VDdZVUhIWllZbTZ6bFZmN2txbGxjeVk1UTg5aUhUcHNDWUcrODVyRXJGNSswN0Y3L1ZVc3BhbVk5QjRaMHJKSW9USWVzam5SVHEvSlUxc0hyM3p0bU5RWHNSb1hBRVQ3WkRSS1M3d205VmhWaEczeDBUUWFtTzhnOWxaNVROTER1ZWV0b1A0cmlqRllRRDB2ak0rS3duY3Bpb2ZIbnZtVHBSR042MW91TDZwblV4T2Y4UEtucjVDckNSNGFLNTc0M1U1YXNjMzE5czRtRkZkaWhmRHlJZGpJM3hPZ1E5dmdHSGIxd3pZeXh4S01PSHF0dXVTaTVZT1RhczllZ285dHJmZ3Jsb01ROFRJSmU4dUtGWk5HbzZzbVQ3b0tubWlYNmVycWw1aXl4SHVvZURyTEVFRGhnTjI4ektITEVBN1g1bHFSamwyaWdFcWYrelNqQmw4c1JUTTczVFhmZG9YeDlnTjZ3L2lqZFB3c3RyZUZuL1ZrS1NVSitKSmN1U0syQVVsZVhOck5RZzJJVW55dHZZRTJPS0MrcHdIQVJTU3NRbzlIdDBKVmsxUW0iLCJtYWMiOiI2Y2RlNWJjMjA4YjEyMmE0MmE0OTUxMDYxZjJmOWZkMWY0OGQ0MDM4M2QwZTM4Zjc5NzM0MjFiODhhM2ZkOGM3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjQ4ZEFVaHBXL0dabzZhR2FyK2dKY2c9PSIsInZhbHVlIjoibzNoZGp0TmRaNVZEaFQycUNBd29QWnpkeXpSVkFwT3NPc2t2R3RUdUNyY0E3QTdaQlpFeVBRazd2VEFvQ1ZaQWx0MDVTSE1hN0xrRFhHVXFjdFhVSWxTVWhyNmtBengvVEI1QmNtTHpPZjZla3cxR1hTNGdia0hPcGcydnM0RzFsQmVGOTR6b1I1N0wxTkp3RkpCemFVa3hmYytnUjgzTDRrQVVIZjgyb05zVEdIVmIzVHJjRkFEOVV4S0s0STFwTU9ubmlOaTdSZXBVYjFrejQyYS8yM2VSQml4aWJyYXZIbEY1ZmVaYXBsbGtlYmFnc3VXNU9Mc0s3RzBKQkJ0TWp5c25IRHp3VFF6Wk9KY2ZSdkkyNzJOZkEvOHR1bi9US3VjUk1sWXRwbW4xUFgySmkvOFdUa2E3ZURrcWtsbXFZdnBmOEdhc0VVRDRZYnltQjArOFVJRjQyWGhkeXRUOSs3UlFuQUZWYTBYZWt5SkRFell6ZG1QRVJYbCtpdFdOYU1Xa2c5VjVzZG1vZVd1MHc1Mm9SdVh2TVVSeVQxejlyR0ZpQXc1M0FUZG9ZcW1ybk5WaGNrZTNWUVVEV3pDb0d3SkRWQzMxSDlZaVdub2Z1d2FkQUR2RjVkWkhienRpNlRxYzlhSm5PYkhFYWdPK3p5dnNwK2MxblEvS0xZOXUiLCJtYWMiOiIyMzk1Mzc3ZTRkODAwY2E2NTYxNmY4M2M0Nzg2YWEyMTcyOWI2MGFmNTYzYzk2YzdiY2VkMjg0ODc2NDQwODE2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467990258\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1003056123 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"228 characters\">http://localhost/user-login/eyJpdiI6Ikx1MXhCMG9BcEVwV0JtOVk3KzVjNGc9PSIsInZhbHVlIjoiYTdSUlpxUm9BMlp3alRmcVVEcCt1dz09IiwibWFjIjoiODM1ZTI5YmViY2I5ZTg3OWQ4YmUzOTBkOTM0MzBmYjJlNjdiNTBlOGY4NDFlNTkyNzhmZDAyOTAyOWMxYWQ5YyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"32 characters\">User login disable successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003056123\", {\"maxDepth\":0})</script>\n"}}