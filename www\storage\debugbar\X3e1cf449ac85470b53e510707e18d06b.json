{"__meta": {"id": "X3e1cf449ac85470b53e510707e18d06b", "datetime": "2025-06-06 19:37:44", "utime": **********.402959, "method": "GET", "uri": "/user-reset-password/eyJpdiI6Ill4WHZZRGdqYUkzbFlZNnAzN2kzVHc9PSIsInZhbHVlIjoiU21HUFcxbWlHMXVTNUkveXpsWGM1Zz09IiwibWFjIjoiMWU4ZTY5MDYwODlmMDZjZTA4MjI0ZjBlNzk1Zjg1NDBkNTYwNzEzYTk5YTVhMjZhMjBlNjJjNDA5MGVhZDY2ZiIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238663.046674, "end": **********.402996, "duration": 1.3563220500946045, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749238663.046674, "relative_start": 0, "end": **********.240649, "relative_end": **********.240649, "duration": 1.1939749717712402, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.240671, "relative_start": 1.1939969062805176, "end": **********.403001, "relative_end": 5.0067901611328125e-06, "duration": 0.16233015060424805, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44559184, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x user.reset", "param_count": null, "params": [], "start": **********.38953, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/user/reset.blade.phpuser.reset", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fuser%2Freset.blade.php&line=1", "ajax": false, "filename": "reset.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.reset"}]}, "route": {"uri": "GET user-reset-password/{id}", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\UserController@userPassword", "namespace": null, "prefix": "", "where": [], "as": "users.reset", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=575\" onclick=\"\">app/Http/Controllers/UserController.php:575-582</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02369, "accumulated_duration_str": "23.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.330141, "duration": 0.022269999999999998, "duration_str": "22.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.006}, {"sql": "select * from `users` where `users`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 578}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.362184, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "UserController.php:578", "source": "app/Http/Controllers/UserController.php:578", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=578", "ajax": false, "filename": "UserController.php", "line": "578"}, "connection": "ty", "start_percent": 94.006, "width_percent": 5.994}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/user-reset-password/eyJpdiI6Ill4WHZZRGdqYUkzbFlZNnAzN2kzVHc9PSIsInZhbHVlIjoiU21HUFcxbWlHMXVTNUkveXpsWGM1Zz09IiwibWFjIjoiMWU4ZTY5MDYwODlmMDZjZTA4MjI0ZjBlNzk1Zjg1NDBkNTYwNzEzYTk5YTVhMjZhMjBlNjJjNDA5MGVhZDY2ZiIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-693527367 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-693527367\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1908421775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908421775\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1603724645 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1603724645\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1987374303 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238647866%7C51%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inhab0NSb0hDYW12R0ZLTGlGOW5zWHc9PSIsInZhbHVlIjoiRkxOa1V0UWJLOHlYQ1BRdXZrL3RIbllhMS9hZWVOUUFzQTFncjByZWpwUG9WWHRiZlUzN05kWVlnUUpaVGExdUwxcGJFTHNFS1pvaXpLeS9WYzB6aHpOS3hJQ2lWb3I4V2M4MG9ieC8rNWY1cTJZeSs4c3hWbmdLUlltVVdsM3ZadlQzaTBUeGdWbWxSdWJFYjBTMmtVWEhoeHNEOHNNZ3YvamVDUkJCc2tQMkxqd25LTTZ6aUZxeFd6dUJtSUdsVXBTVlU5MDJCeUh4ZnQxeGlPaHhrN2FSMU94Tm94azZNUmdiNnB5SUoxOFFQZ245MWl6Ukg4aWRNNVVFWVdES3pOcGVGMnJWY1hZZXU4Njc2dmJsN0diWktQcTdNaWtRQW9NRDRSblp5cHhXblBtR1ZwZDRja1A3TkhNeEIrYUZJd0JUUEhOOFFWOGJzQTllK3RIOVFQMWdhd2VGck5jUDdLSTdqZU9wQXNJRjR5TDgxL3NsUFhoTEVFM0h2bzZxU1RwcHUvSVVPRGFQWU1TR1cxWnZQM0NKaDZFVHdrTzk5RU1laW5seldUWUFFMVZGYkdwRnhSckIwdjN2L2NvQzJBaXIzbUFsV0FleFUwTkYzcFhxQ1F6UU1FUjA4KzR0WnVjQWpieGRzdDcxRTJSYXdxZm9ZRkVQRTRDZk94MjciLCJtYWMiOiJiNWZmZmM4YTcyYzkyMjBhYmQ3YTRkNDhjYzJhNDNmODk0ZjU3OWExMWZlMGI5MDk2ZjYwNzIzNjU0MDIyOWRlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdUOWovcGJmdEtMaStZY0N4dHZ3Tnc9PSIsInZhbHVlIjoiOEJaaXVrTjM1T0ZiUEVoQ1dPOE1qUHY5RXd4NHphOHhJZnBLeWlzUkYvblZFazhnbThtM0JvZ1NsMlFjWXllRk04RFlNT1JxLzEzM0JXUjdEUW9NUC9sbFBualFNR2I5NXdhVVdEWVhFMHplTjRldXBqQzdYWHY3SUtIR0ZuRGI0djBodi9VNHhFMlp4SjczVFBMT0VhTzlVekpoWTRjeVVBR1ZtbFBLMUxCOFBpNHRuT2xQYWhrb2Z2ZTV0Zzc4b2RuL0QyTzlVYXZMQ29QbWJ0MUIyaDhTOXB5Q2tWMVBuaGJqbVNBVklxSnJGUU1nSWliMHc2dUVKSStRMTFUZTN4TlJZOThjbFBuL21LZGl1bmhwVHpxTzdhYjl1TUpzRTNWWVd6c3pQb3g5Tk9BWDgvVnpneFZNczY5dTVlY2NhR21pVGgrZEQwSGtqc2duQXZ5WVJTM0w0WXlnbGppWGQ0bFNBc1JDdVR3SXhZeWVNdVRHblRtWlJSZG1HeDNhRjRvYUdKNURhVk5wUXhFYVVWU3ZMYjN5aGhJNW9KRmh1dkRxcU42NHcyczFtc1pBNkY0dmV5NFFTY3Ezb3BOTFB1ZFhNdC81aGtuRDE0UzRZR3dNUjlteXFVVTBWYlNlUVhWbnJBTXAvNVowdGJ5d3ZSTFdUL1lyMU9ZY2Z5aXoiLCJtYWMiOiJhNmVkZDMxNTg1OWY1OWFlYzFkZDRiYmIyNzJiZGZhZWViNWVkOGU0MTM2MjUzMTk2ZGIxM2VmZDkzMDNjMjJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987374303\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-545749392 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545749392\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-701662196 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:37:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ild1WEdnVUJ1dndQSlRwR2JGUEp0Mnc9PSIsInZhbHVlIjoiRHh5YlhLaXlkRFZ0R2RpZ3NIS3c2cHJzN0JKb3FPK2ZjM0thdE1qZXFvdE5ibldGWS9HN3l0WHF5aWh2V0hYdXhPT0YxQzF2aXRpUlUyL3FPNGt0RmJ0K0JEaEJFNzYxaFJJQmdlaVBUWVQvYmZLeEs5bk5TdmJPcmxlRkVUTFZlME1JLzc0ZWRBTlJWaDBrVnVEOHV1cXcxRUgwTkJCOWpmaENnMUNZdEptcnFvaVFEcVhKS29IWVNhblV6dW5EM3JNalZLbEJUUWY4Q0ltKzAzT2E1MitmQVlHRlhUakgyUEFCNXZOa2R5eEtqd2lzUEhhMkoxYkJLNFFyaVowbU5XWFBXSDhMWGFMZDd0aE04SWtXbTJsenRiYkpPNzN4azdTWkE1VUdKbFI1cmV4NmcwYlFsREJUWnhPMDYvOFVxaklTK3c5QytCZEhEQVRXMDY4TC83STJsY1NHazM3c0J4bVRFQ0t5RS9ZcVo2d1hxemVpYXUwek9wbVZES1hTWmlSUWZ3dVRWbEQ4S205VXZkZVJEK0x0Zmo0RXZQanNFejFpOUIxc3R6c3hrQjQwOEZNUHZ3bjYzN0tWcHRNZXZnK0lBeTFlTFJQNStrWkFkLzhvcTdrTEF3YmFlMUlabjBzL3R4b1NSRGlRdTl5RmRxME94VGhTTU1lNDNtU20iLCJtYWMiOiI3MmYyZDBiZmY2ZjQxNTRkNzU5MDk0M2U0M2RhNTY2OTFlNWQ5MTIyNzM2MGMxYzcwZTI0MWE3MzgyOTFjMjVlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlRsMzlldzE5Um9DTjc5T05wV2l0R0E9PSIsInZhbHVlIjoiZG5VSUx6SjNRMGVWWDAzWXZqbWNDMHhzRGJwaUZFQStCNWNRVm5ROTJ2Nzg2UFdZODYvOG1mT1FLUW0vRmdUYzlueDBaeWUwb0ZoZW1KMVNLZ0lLOHUwSmhTcTZYWFo5MXNqUDlIdTVjZGN2bE9OQk9kYW9RZ1V5VW82MzRTOXNJZzJJK3JrcVV5eEZ5RGsvWXdFMXE4ZzQyNHdDZ3dWeEs5Y0pFaEpLL001b0d3STBDbmFLNnVTdGE0TTExZkQ2aENBSU1NNmhYV2RyVmk1M1dJTjhNZ3QvWnhKNzVNYVF4QjNNOGRBSkM0M2NwcGpWQU5pQlVjSHFvQ2ljTmpxdUFqbW8vUXBKVjQ2SUlGZ2RWQjNOT2FSaU14ZkRYUEU0bERxdVJaRTl1RU9uWnY1WnNlaGpGVTd2M0lpc1BvNnA1c3ZEWWRwaS85citNTzVuUFJRM0J6Y0VtTlVsRXFVZUdhRURvT2NWYkZUeFlXYkFDWlhEUWFVdFU2WVZzUVdEb0wyWHl1TlVybnI4WjV0MFBDNkpTZVAwVGZhdXVyREY2WmNTUFlUWW0xL3hRYkZ2OS9vOVFLQUdrY1JWV1NwNFFkUUR3dXFZdVlKU09CZG5hOTN4NjVvblhUR05reXIvVDRsaGdRak1jeCtxajJhMzJTdlBzYXNFeFRRZlJUSngiLCJtYWMiOiI5MjQ2ZDlhZmM3YjY4ZThlNjY4ZDEyYjE4YmQyYTcwMDNmZjRmN2RmNGI0NzQ0ZTdmZWVlODIzOGRiZmU0OWZjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ild1WEdnVUJ1dndQSlRwR2JGUEp0Mnc9PSIsInZhbHVlIjoiRHh5YlhLaXlkRFZ0R2RpZ3NIS3c2cHJzN0JKb3FPK2ZjM0thdE1qZXFvdE5ibldGWS9HN3l0WHF5aWh2V0hYdXhPT0YxQzF2aXRpUlUyL3FPNGt0RmJ0K0JEaEJFNzYxaFJJQmdlaVBUWVQvYmZLeEs5bk5TdmJPcmxlRkVUTFZlME1JLzc0ZWRBTlJWaDBrVnVEOHV1cXcxRUgwTkJCOWpmaENnMUNZdEptcnFvaVFEcVhKS29IWVNhblV6dW5EM3JNalZLbEJUUWY4Q0ltKzAzT2E1MitmQVlHRlhUakgyUEFCNXZOa2R5eEtqd2lzUEhhMkoxYkJLNFFyaVowbU5XWFBXSDhMWGFMZDd0aE04SWtXbTJsenRiYkpPNzN4azdTWkE1VUdKbFI1cmV4NmcwYlFsREJUWnhPMDYvOFVxaklTK3c5QytCZEhEQVRXMDY4TC83STJsY1NHazM3c0J4bVRFQ0t5RS9ZcVo2d1hxemVpYXUwek9wbVZES1hTWmlSUWZ3dVRWbEQ4S205VXZkZVJEK0x0Zmo0RXZQanNFejFpOUIxc3R6c3hrQjQwOEZNUHZ3bjYzN0tWcHRNZXZnK0lBeTFlTFJQNStrWkFkLzhvcTdrTEF3YmFlMUlabjBzL3R4b1NSRGlRdTl5RmRxME94VGhTTU1lNDNtU20iLCJtYWMiOiI3MmYyZDBiZmY2ZjQxNTRkNzU5MDk0M2U0M2RhNTY2OTFlNWQ5MTIyNzM2MGMxYzcwZTI0MWE3MzgyOTFjMjVlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlRsMzlldzE5Um9DTjc5T05wV2l0R0E9PSIsInZhbHVlIjoiZG5VSUx6SjNRMGVWWDAzWXZqbWNDMHhzRGJwaUZFQStCNWNRVm5ROTJ2Nzg2UFdZODYvOG1mT1FLUW0vRmdUYzlueDBaeWUwb0ZoZW1KMVNLZ0lLOHUwSmhTcTZYWFo5MXNqUDlIdTVjZGN2bE9OQk9kYW9RZ1V5VW82MzRTOXNJZzJJK3JrcVV5eEZ5RGsvWXdFMXE4ZzQyNHdDZ3dWeEs5Y0pFaEpLL001b0d3STBDbmFLNnVTdGE0TTExZkQ2aENBSU1NNmhYV2RyVmk1M1dJTjhNZ3QvWnhKNzVNYVF4QjNNOGRBSkM0M2NwcGpWQU5pQlVjSHFvQ2ljTmpxdUFqbW8vUXBKVjQ2SUlGZ2RWQjNOT2FSaU14ZkRYUEU0bERxdVJaRTl1RU9uWnY1WnNlaGpGVTd2M0lpc1BvNnA1c3ZEWWRwaS85citNTzVuUFJRM0J6Y0VtTlVsRXFVZUdhRURvT2NWYkZUeFlXYkFDWlhEUWFVdFU2WVZzUVdEb0wyWHl1TlVybnI4WjV0MFBDNkpTZVAwVGZhdXVyREY2WmNTUFlUWW0xL3hRYkZ2OS9vOVFLQUdrY1JWV1NwNFFkUUR3dXFZdVlKU09CZG5hOTN4NjVvblhUR05reXIvVDRsaGdRak1jeCtxajJhMzJTdlBzYXNFeFRRZlJUSngiLCJtYWMiOiI5MjQ2ZDlhZmM3YjY4ZThlNjY4ZDEyYjE4YmQyYTcwMDNmZjRmN2RmNGI0NzQ0ZTdmZWVlODIzOGRiZmU0OWZjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701662196\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1703481616 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703481616\", {\"maxDepth\":0})</script>\n"}}