{"__meta": {"id": "X01b24be9e7487e48c41729d4d0016428", "datetime": "2025-06-07 04:35:34", "utime": **********.763139, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270933.269382, "end": **********.763173, "duration": 1.493791103363037, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1749270933.269382, "relative_start": 0, "end": **********.540401, "relative_end": **********.540401, "duration": 1.2710189819335938, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.540433, "relative_start": 1.2710509300231934, "end": **********.763176, "relative_end": 2.86102294921875e-06, "duration": 0.22274303436279297, "duration_str": "223ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761088, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02902, "accumulated_duration_str": "29.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.650405, "duration": 0.0264, "duration_str": "26.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.972}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.713082, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.972, "width_percent": 3.515}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.734722, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.487, "width_percent": 5.513}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-702713905 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-702713905\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1547356800 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1547356800\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2068215584 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068215584\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1268285432 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=xq5jkj%7C2%7Cfwk%7C0%7C1984; _clsk=1p8bbqf%7C1749270908302%7C1%7C1%7Cs.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkZreDNwRFhIaW8vclFnQU5GU285dGc9PSIsInZhbHVlIjoiTlIxR1JmNHpRYkluczgwdnlwd1FLRmxGNGtOdzZQbnFqTmRZWjIrclI5TEZmV2ttWWdnSFk0ZmIrbVFMWHVmbVhEMTFVcVdUdWxFRUhmY3huSGQ0Ykl2bkUvQjFiZ0dkbUluTVlJRkN5VURkZXFDdkM4NFhkUldJSWxSbEJldVpnOXNnTTNmbjNHamMvaWlZUnlzbWVlOFFFYWtRZ2d1b3JSblNNZitQdy9Jck03SUcwTFpFUVBwUHVwY0lzRjBDV2xkOFlXbkk1QlMwVXVodlpFSEI0WWZSUEpzSE41MHVvNi9IRVR4KzZkdnJVcWZ6UFQ1bXhUUzZuMUNtVjAwc1UzcWNrcnFTT0pXRVh2UGRpZ2F0a25kWmFUUWJnOEd5MTJGclpFSEh2YVNzNlJzUllTd1NJTWxYemRZdW1BS1VKNWVuWkV6OTBxUXJiV0RyQy9zdDYrQk5LNm5kRnpMcmVmY1BvUEZmSFZ0UkZmT09hUGFxYU5pQzZKUlpqbnhuaXkwWHFCNElZUFBDaDNHeS9DdHFpZGZTczdNcTlHSG42Ylh0dXRRWVN0dWExNkxyYW9Hd0VCQUp6clNib2QzeFgrUFVCYUFHQkxjQmdnN3ZqcUQ0NU43SW9pZ0NURVBFOHluWC9SaGRWUmxVS3poWS80UjFwUDN6SXowdlZXem0iLCJtYWMiOiIyMjVjMmU3ZDJjOGQ4MTNiODFjMTRlNThmYjI1MzFiNGVkZTZmYjgzMGZhMzQ1MTQ1OTAyNjQxZDAxZGYwZmEyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkgrY21tczVCcm9RaElqeFRxT01McEE9PSIsInZhbHVlIjoiUWgzZmNuVnBRQTIyanIydVkwS1YwR2RFai9JYUs0UTJBVW9pNDMxZFZXMUNpY2lRS2VHRytNeFhHak1ubW1RRkdxRkZuaTZacVo5ZHdYV0lNWkF3dzNHdFZFMk9xT3UxU1A0VnllaTBMcGxKMCs2ai9QODBoRHBiQXFodjVTRlpMQlRCVC9EbDV5T2hUWWh2bjhEcllsVzIzUVFpODJDYWdxNHBWWTBmbmZBZkRIV1U3dDB4Q2pCbUx2bDlHMjljOHNPL0E4VnVpMFpSdWZ4M1g5VCt6ajZweXZEK0hTU2swdzN5VmVBRmlYUkVrQXU1Tkk3S1RlUmhRNVBLblEvdWZiYkd2b2NpTFFqQkN6anlXbVpHTWJFeHpDUHJkMTdQR3QyRkdDTW51dTdUbnZFZGk3aUJjdk9vZ0kxdDloM2Q0RWlHS3VCTWFkTzUwNFJ2VGdJNnlNYTA4Y2hoUll6M1cwMTk5SHNsc25udS9qOFRSVTBlazFHeVQxL3p3Q2NRWGQ4YlN2aU5WeG56UmpTdUxSNmd4ODNpMm9OeSt3RGJQd0RUS2QrTzhmdFdUcDkxU1UzeW1QVzMwTEJZZFR2N0VSQjNUMDIxa0tBNnk0enpnR2s0ci9KZTF2bmVDcm81TlRKNXpad2JzN0pxTXFDbWdJWEJBbUhQbStwSUwrblAiLCJtYWMiOiI5MDU0ZGUyNzljODE2MDA2NTMwMzFkNThiZDhkNmQxZjQzNjJmZDhkMTA0NDI4NWU2NjliZjRjMjU5ZWE0M2E5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268285432\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1787136614 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z1ibAMoy16lMUkDWMem3ATVFxZVkTly8GBk5DGQF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787136614\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-179879474 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:35:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVHRXZPU05NQVFDUGdrN09TN1hRUHc9PSIsInZhbHVlIjoiU1MydzZZMVRTVEl5bjROSTBhZUFZYURwNlFTS0g0SmNQRHN4elZyRHBLQjQwNE5xNHRtUnVCQzJ6SVlJMVlxUWdHc0dqL2k3bFkxN3kvN2p1ZlU3T004Q3IyVGlTdGJ2bTRVblVVSmUrRElwSWtRSUtpdlQydVdSM2Uwbk80NUgrOEpjN0JGRlV5UFA3RWxhWi9Ibk1IU1ZLc3g3Vm93WUdpSk1WUXFaUWZGUERFMWJneW9CanJFbVFKSXlpaWlESVBranVma2hZQ29tUm5KQS8xMWZHZXo4MHFqOXlhcjk5akhwOVU1Z1p6YlhGbE4wWWhLYUdHZ1RnTGtmNWxEVHFKTTJLZ0NrbXB2c0ZLS2RLU1JDQjBxK2ZYL1hGRUZQK2hsMjlTZU44NlU4My9tZWw4VU9QeE5pbDdaL3JXS2ZNbitGakFiVjVLc2toMnMxcW1wODI0ZVJTMnl2QmJIRWE2QnQrZVdMS2JpbS9EdWw2bmhqcUpDZkIrL1E2RW5lYWZVRG5sc1N3Y2hibEt1VEQyUWowYkVybDNoU3RyRlYvUW1PcnpjYzNFTW9KLzNpVHRiaWhDeXI4bWxIb0FjK2dCRUJudEM2NElLUWwybHF1WTM4MC9teGwzMTlHLzhLWEs2SGN4dWxqVXRvRXdmdG9Ua3Y2V1BFZFBtOU9LVXkiLCJtYWMiOiJjMzU0YmE1OTRmMDZlN2IxZjk3NjU5MmQzN2M2ODNmMWE0MjUyMzc0NzIxYzljNmQ3MTJjZjhiM2E1NjQxMmIzIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ind5WXFIVldXd1ZJS0xZWjUraDczMXc9PSIsInZhbHVlIjoiT1FQek5xRzhpSHN4UEN1RkU5SjR1SlhRZjNEa1Z1ZmM5dkNUbTdnZ2ZEaGk4Mm9sR1ZXMWQ3M2dNR0JhazVZMC9Xd1lvY2ZubS9xVFZySnhsdTlMK2xYSUVYdDFEYVZoWkRuWVdtSjA4RGV4VDNuTk5uVllxSXI4NTdPZFVKWk9CTzdGTmRwc0VJa3dJL0xWbndXeXFobnVlb2RKZjJFd0RDb3dwVWJWVjRpQVhneG9vNitQOFFLUTVVKzJzT09BRTRiaW1Iam42WS9BY1hMLzhQZE9jM0paR1pwbWZqNWprT3JBdmVNbzhmUkJ2VWxHdnBxUTJYcFpCRGpIaTloNW9uRXR3ZXBYMkQvS3c2alJIOGkwLzRaSXRQZG5rTlhndGcvU2hUWGlsdzJNMWVka2Jqd1Z6d2lxUDVFaW5vUnh6MlhnMDlMRm1Ba1lVQjJ5WERNR0lVN1cyVko0UEdxMlNFK0d0ZzFBNXZBMGYwTDhpS05SU0NXV2FVeHYvaEF4bjlzcVFkWVFlSTR3Z0pqeXJZc2xITVp1MTNSL1VvUS9wejZ6MHRIREx1YzNxbDRoUm5PcmNLVnFCZ1VUTFRVMFRMVHpmT2k5cFBoL2taMHRvcTRkaTlIMklMZ1pQaHJjRzFsTmNoK3VreE9hT2d3Y2hFSjViRE01QXhZaE5kRXUiLCJtYWMiOiIzOTU5ZDdkNWE0YzM2N2E1ZjYxYmNlMzlmMWI4YzVkMzM1MWFhNmU4MzUzY2VmNzQ1YjE1OTg0OTI0MTE5NGMzIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVHRXZPU05NQVFDUGdrN09TN1hRUHc9PSIsInZhbHVlIjoiU1MydzZZMVRTVEl5bjROSTBhZUFZYURwNlFTS0g0SmNQRHN4elZyRHBLQjQwNE5xNHRtUnVCQzJ6SVlJMVlxUWdHc0dqL2k3bFkxN3kvN2p1ZlU3T004Q3IyVGlTdGJ2bTRVblVVSmUrRElwSWtRSUtpdlQydVdSM2Uwbk80NUgrOEpjN0JGRlV5UFA3RWxhWi9Ibk1IU1ZLc3g3Vm93WUdpSk1WUXFaUWZGUERFMWJneW9CanJFbVFKSXlpaWlESVBranVma2hZQ29tUm5KQS8xMWZHZXo4MHFqOXlhcjk5akhwOVU1Z1p6YlhGbE4wWWhLYUdHZ1RnTGtmNWxEVHFKTTJLZ0NrbXB2c0ZLS2RLU1JDQjBxK2ZYL1hGRUZQK2hsMjlTZU44NlU4My9tZWw4VU9QeE5pbDdaL3JXS2ZNbitGakFiVjVLc2toMnMxcW1wODI0ZVJTMnl2QmJIRWE2QnQrZVdMS2JpbS9EdWw2bmhqcUpDZkIrL1E2RW5lYWZVRG5sc1N3Y2hibEt1VEQyUWowYkVybDNoU3RyRlYvUW1PcnpjYzNFTW9KLzNpVHRiaWhDeXI4bWxIb0FjK2dCRUJudEM2NElLUWwybHF1WTM4MC9teGwzMTlHLzhLWEs2SGN4dWxqVXRvRXdmdG9Ua3Y2V1BFZFBtOU9LVXkiLCJtYWMiOiJjMzU0YmE1OTRmMDZlN2IxZjk3NjU5MmQzN2M2ODNmMWE0MjUyMzc0NzIxYzljNmQ3MTJjZjhiM2E1NjQxMmIzIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ind5WXFIVldXd1ZJS0xZWjUraDczMXc9PSIsInZhbHVlIjoiT1FQek5xRzhpSHN4UEN1RkU5SjR1SlhRZjNEa1Z1ZmM5dkNUbTdnZ2ZEaGk4Mm9sR1ZXMWQ3M2dNR0JhazVZMC9Xd1lvY2ZubS9xVFZySnhsdTlMK2xYSUVYdDFEYVZoWkRuWVdtSjA4RGV4VDNuTk5uVllxSXI4NTdPZFVKWk9CTzdGTmRwc0VJa3dJL0xWbndXeXFobnVlb2RKZjJFd0RDb3dwVWJWVjRpQVhneG9vNitQOFFLUTVVKzJzT09BRTRiaW1Iam42WS9BY1hMLzhQZE9jM0paR1pwbWZqNWprT3JBdmVNbzhmUkJ2VWxHdnBxUTJYcFpCRGpIaTloNW9uRXR3ZXBYMkQvS3c2alJIOGkwLzRaSXRQZG5rTlhndGcvU2hUWGlsdzJNMWVka2Jqd1Z6d2lxUDVFaW5vUnh6MlhnMDlMRm1Ba1lVQjJ5WERNR0lVN1cyVko0UEdxMlNFK0d0ZzFBNXZBMGYwTDhpS05SU0NXV2FVeHYvaEF4bjlzcVFkWVFlSTR3Z0pqeXJZc2xITVp1MTNSL1VvUS9wejZ6MHRIREx1YzNxbDRoUm5PcmNLVnFCZ1VUTFRVMFRMVHpmT2k5cFBoL2taMHRvcTRkaTlIMklMZ1pQaHJjRzFsTmNoK3VreE9hT2d3Y2hFSjViRE01QXhZaE5kRXUiLCJtYWMiOiIzOTU5ZDdkNWE0YzM2N2E1ZjYxYmNlMzlmMWI4YzVkMzM1MWFhNmU4MzUzY2VmNzQ1YjE1OTg0OTI0MTE5NGMzIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179879474\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XelvzXJcZdCoMgSd6WZRoCkTf3bmQ3VnUv4q9smN</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}