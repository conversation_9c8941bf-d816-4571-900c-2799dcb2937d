{"__meta": {"id": "X38777efa7370b3f7cb845fe9df4cd1f5", "datetime": "2025-06-07 04:17:19", "utime": **********.178218, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269837.637695, "end": **********.178245, "duration": 1.5405499935150146, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749269837.637695, "relative_start": 0, "end": 1749269838.993629, "relative_end": 1749269838.993629, "duration": 1.3559339046478271, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749269838.993656, "relative_start": 1.3559608459472656, "end": **********.178248, "relative_end": 2.86102294921875e-06, "duration": 0.18459200859069824, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.009630000000000001, "accumulated_duration_str": "9.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.077824, "duration": 0.00526, "duration_str": "5.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 54.621}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.110156, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 54.621, "width_percent": 14.746}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.13886, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 69.367, "width_percent": 14.434}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.158794, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.801, "width_percent": 16.199}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1983331081 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1983331081\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-28457114 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-28457114\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1964831152 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964831152\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-873549202 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269750615%7C4%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZtMlcyVDV0a1ROdXN3Ly9ubTVVTUE9PSIsInZhbHVlIjoiQ3ZLRHIrL1huSXRVRjczUTh0Z3RHUjM1YjJwclZEakVmMkNTWGtxQ25uSHJRS3A2eWR0Q2FPZWhoQXNteEM3cm04aS9JTXd0YWRzQjBFNzVKMkEwZkJIYVJuTCtOK29QNVBvMGdzQVdnOWZuVUExVlJkN3ZUdFVnSkhLdnV0dHpobzcrQkxhcW9waVBDY2lVS0YyYzJuM045d3hBVVRpd1J5RHBxaUlleDR6TVhEUlpOWjBtc1gyb3o5VnBXYk1WNXdpSVJ4dklFUjk3Z2tDQ0x2YTYvZXlvOElNTy9LUEtReHZ5S1RMM2toS0VNalgycVBtZXZaZE1Ca1ZzeUtPcUF3cXdMcDU0Y2F6bi9aSjlnSDVUZ2tpdW50ZzdrWkF6RlQ1R1hqMmpVQjBFL2F6ejc3azUraWNTVlk4dkJORGllVmhWTGJST2FIU2xGWEdvVUJ5U0FHalIycFp6SzBwcFdEclNZVFJ1Y292cWx2ZHRUR003K3lTOFdDTXJqSGdvU3NqSGpVNm5uSGFHZmozRkhLR0E1MlNUbTVHUUdJUTdHTDJHUEJsRzFnYjk4dHIrT1dXOHNBS1FJaEdNd056bDVpUlMrRVJKK0lJTE1ndjdzcGsxOHBZSldmVjVqZERhdzJ6RjdtWUQ0aUl6WUpKU213bXZxeHdndlpKM2UzWVciLCJtYWMiOiJiM2RiN2U5Y2JmNWQ4YTlmOGRjMzJkNDcwOWMxMDRkZWQzYmU2N2Y4ZjA1YzNkZjRkODdhMzQyNTEzMmYxZGI2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldwM1FQYXUxdDVrMnJadk10VGNrcFE9PSIsInZhbHVlIjoiVjM4bUhHSGxPNkIvdVNEVndhU1VxUTdVTUJaOWtkMnI2R2RPYlVqVkRuN1ZpU3ZNVk9sSjVkRFJKbTlONmVXUkw2Qzg3eGZEbzh6UlNMNkgxRkFQTmRmOXFVRWRXcDVzZGh3MkpmOHJjY1ZSdWtNbzNTSVZsWnQ3eXFKQ3RtK1R5ZVZDVVFyNWt1NExoRXNPQ2VaQXJKWVg0ZEh0RXJ1N2VQYmU4cjJyWWJqZTR0dTkrU0JaQ01mTndVbzdKZHRKbERUc0thV1NINURNSWdmWGRUMUx1dlFiclRodkFYSFZJUm5zSFUzd0RHbGtWaUZLREpWUVoxM3RjWlRrTGEzamdTcTFaU0FqZlZEUXBrTnhaWlRyaGYrcXA4WGZXRk5RT1ZoY1B0K3ZPKzBjaVdoV2UxY09sbmhyK2IxTGpXcEIyemR5bzJ1UkdSc1BZUyt1ZmNGNUZwM1JJbE5OV2hSVTc4TFFkUGplenl1VXFjbVNEZzBLY2dRNzdhUGJLSDNrdUR0MXpIalJLeVBvZmdtbGxQeExwamxZeWIrUTBHbkhCakE1d0xBVXpFYmZHRU5GcnlxSGF1M1U4aFRIaXd2Q1JXWHRlWlVNcWxzUXowaXNlMkpqZGtBOCs4VnljRWlhSSs2VlJXdXNWd3ptaW1XejVsaWwyOUVzR1Z3cWgzWWMiLCJtYWMiOiI1MTkwNmZmYzY4MmM3NDAxODFhZTRiNjEyZmM1NzQzYzMyMzI4NGMxYTlhOTUzMjJkZDFhYmNjZDI0NmY5ZmRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873549202\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1997889754 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997889754\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-11174966 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:17:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllFU1d5ZEw0WTJ0aFRQc0xKYllSbGc9PSIsInZhbHVlIjoidmZGaHQyV0hWQ0o0RWM2YXhaY2hORkFNZ240QUxOU09CWXNUZnhXMHhDSUo2SnFwNDNqTjB1RkltcVJ2aFVyWUM0QzFxY1p3VHF5eVZxQm50UVFvTmpEdGZiSHowOUJFU2lZQ2EyOUp2cjQ3VjRRMEI2THRYVCtRNUxNbVp5aTFUczFLUElLa3VlT3FkTzV5MUZvSGNVM293WHJWeWE2ZnhKcmgyOHdCTjlIVUlqMHNuSjgxVXNyQmdBSTh2RmJZNWU2THBQalcvTjJ5RzUwOGI3SmZ3bWpUNnAvMzhpdE0rNnNIdlBLaDkrMDdHb1VlUjJsdWd0TGxQTVp0SklJNDBSdmQvRy8zRDRuaE94V0tIbDZRZWxvaXRYaHljRURBcjBKUUo1SkgxQzdjdlhHNDgxUWwzVTdEMElha3NKbU5MT1d1TmtPdmxtZjdNQjRnZm4yelE0Z3VNbEtCM0tqTnVsMUNpaGp2QXJmZjFmOC9wN2ZiUEcxdTk1OTh0MmpTb3J0MHNlWGYxYTJqV3BTTmlnUFl3V0F5a2swOWM5Y0pIY0hKckJDalQrWFRRUTh3SG1TWXZHSVVtbkQ3akhKTGxEL1c2eXJnWHdMMzlJTVVycytqaVA4a3JRVjZ6b0dMMDJXRlIvRisyeVZxZnFUcjJ1T0FaZFpjQTBycXFaT1giLCJtYWMiOiJlMjNlZTQxNjExZjIwNzhhYjE3OWE5ZGI3YmNhODVkZTFmNjg4MTI1YjJmYWYwMjkzZjc3OWNhOWY5NzljODUyIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:17:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik4ySjVGaW9oU09TLzlSR29rRWQ3L3c9PSIsInZhbHVlIjoibkpIZVNHOVNtRXRJRnJBcUU3NmpjdTdvWmlHWEZxandxa3Z3NUxBb3FzZVcxVm9JUy9KdlVJQmd1SXFBMWxJb2JXUnV6TmROOGtSM0JUUG9GNjR0dTJyM1BUVGQ3azIxUjNnbGoyeGxNYUI2Z0N6WXBUc0ZhUS91K3B5ZnMyV01renJKYzhMc0QwYzl1TWFNbTkyMTZ6d0pLTlNET3p5c0hiYWJsTmROU05TQlJoN1lORUJ3d2NqZTlYakhXRXBnWEp4d0h6MmtBNGdnMlhEWkQ5T0hlYTc4OGZlMnMyL20wK1dUY1QrOGcxNHdERHBZVnNjTWJCYTJnU3hERjFFMFFhZUhCNm81bGpXNHA2cGN4NGxEUXhKa0F3WXREOHFEMUJnbkh6czMxaWF6YU1ZYWorYmQxUTRZK2VjbjBGNzIyb1JERmR1TE4zL1QzWXNPbjRpRUFZNExyeGdyZklpSFd3VTQ3dFJxSEN4N1lMVlRDN2UySFpkbUl5eVBsWi9VeXltNG5PcHhLQnl1aHc5MkFYMm5MeElyNjhaS1N4K2trYUdQQVk3dTIxSXBSbWQ4dGlDczloK29DSnR4WnJlZDlWNlRVRWZhd0VaQnh4UXJ4Z2NFTEFrR0J4VitNMFJXaWtMTTVmR0V0ZFZQa3FwNmIrRUw4SnlmVkdtOVU1bTgiLCJtYWMiOiI1NTk5MzllNzg0NzA2MjYzNjFkZmM1ZGJiYjgxMjZmYWYxNzQyN2UwZGUyYjlhN2JjMGFlOTVlMjhlZjMxN2M3IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:17:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllFU1d5ZEw0WTJ0aFRQc0xKYllSbGc9PSIsInZhbHVlIjoidmZGaHQyV0hWQ0o0RWM2YXhaY2hORkFNZ240QUxOU09CWXNUZnhXMHhDSUo2SnFwNDNqTjB1RkltcVJ2aFVyWUM0QzFxY1p3VHF5eVZxQm50UVFvTmpEdGZiSHowOUJFU2lZQ2EyOUp2cjQ3VjRRMEI2THRYVCtRNUxNbVp5aTFUczFLUElLa3VlT3FkTzV5MUZvSGNVM293WHJWeWE2ZnhKcmgyOHdCTjlIVUlqMHNuSjgxVXNyQmdBSTh2RmJZNWU2THBQalcvTjJ5RzUwOGI3SmZ3bWpUNnAvMzhpdE0rNnNIdlBLaDkrMDdHb1VlUjJsdWd0TGxQTVp0SklJNDBSdmQvRy8zRDRuaE94V0tIbDZRZWxvaXRYaHljRURBcjBKUUo1SkgxQzdjdlhHNDgxUWwzVTdEMElha3NKbU5MT1d1TmtPdmxtZjdNQjRnZm4yelE0Z3VNbEtCM0tqTnVsMUNpaGp2QXJmZjFmOC9wN2ZiUEcxdTk1OTh0MmpTb3J0MHNlWGYxYTJqV3BTTmlnUFl3V0F5a2swOWM5Y0pIY0hKckJDalQrWFRRUTh3SG1TWXZHSVVtbkQ3akhKTGxEL1c2eXJnWHdMMzlJTVVycytqaVA4a3JRVjZ6b0dMMDJXRlIvRisyeVZxZnFUcjJ1T0FaZFpjQTBycXFaT1giLCJtYWMiOiJlMjNlZTQxNjExZjIwNzhhYjE3OWE5ZGI3YmNhODVkZTFmNjg4MTI1YjJmYWYwMjkzZjc3OWNhOWY5NzljODUyIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:17:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik4ySjVGaW9oU09TLzlSR29rRWQ3L3c9PSIsInZhbHVlIjoibkpIZVNHOVNtRXRJRnJBcUU3NmpjdTdvWmlHWEZxandxa3Z3NUxBb3FzZVcxVm9JUy9KdlVJQmd1SXFBMWxJb2JXUnV6TmROOGtSM0JUUG9GNjR0dTJyM1BUVGQ3azIxUjNnbGoyeGxNYUI2Z0N6WXBUc0ZhUS91K3B5ZnMyV01renJKYzhMc0QwYzl1TWFNbTkyMTZ6d0pLTlNET3p5c0hiYWJsTmROU05TQlJoN1lORUJ3d2NqZTlYakhXRXBnWEp4d0h6MmtBNGdnMlhEWkQ5T0hlYTc4OGZlMnMyL20wK1dUY1QrOGcxNHdERHBZVnNjTWJCYTJnU3hERjFFMFFhZUhCNm81bGpXNHA2cGN4NGxEUXhKa0F3WXREOHFEMUJnbkh6czMxaWF6YU1ZYWorYmQxUTRZK2VjbjBGNzIyb1JERmR1TE4zL1QzWXNPbjRpRUFZNExyeGdyZklpSFd3VTQ3dFJxSEN4N1lMVlRDN2UySFpkbUl5eVBsWi9VeXltNG5PcHhLQnl1aHc5MkFYMm5MeElyNjhaS1N4K2trYUdQQVk3dTIxSXBSbWQ4dGlDczloK29DSnR4WnJlZDlWNlRVRWZhd0VaQnh4UXJ4Z2NFTEFrR0J4VitNMFJXaWtMTTVmR0V0ZFZQa3FwNmIrRUw4SnlmVkdtOVU1bTgiLCJtYWMiOiI1NTk5MzllNzg0NzA2MjYzNjFkZmM1ZGJiYjgxMjZmYWYxNzQyN2UwZGUyYjlhN2JjMGFlOTVlMjhlZjMxN2M3IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:17:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11174966\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-229506902 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229506902\", {\"maxDepth\":0})</script>\n"}}