{"__meta": {"id": "Xafb81f26e0717b7c1d6699a201ad69f4", "datetime": "2025-06-06 19:22:07", "utime": **********.483491, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237725.991501, "end": **********.483528, "duration": 1.4920268058776855, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1749237725.991501, "relative_start": 0, "end": **********.307546, "relative_end": **********.307546, "duration": 1.316044807434082, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.307568, "relative_start": 1.3160669803619385, "end": **********.483532, "relative_end": 4.0531158447265625e-06, "duration": 0.1759638786315918, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44752936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00804, "accumulated_duration_str": "8.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.39958, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.279}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.438379, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.279, "width_percent": 15.796}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4585261, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.075, "width_percent": 14.925}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1936927991 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1936927991\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-175718598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-175718598\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1264480351 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264480351\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237675757%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9pU1JMM0hPWVJyVktVNTBjWFRsUHc9PSIsInZhbHVlIjoib2NYd2xWWDNHaWJ4ejZORUtQMGxPM0g0Q3hVNnBGVHJ2bDl5MWFPcHp3ZXhxSEFZdXZDVk1za1BUUFdzMW1KR0VNem8wUXp6bDFjSUJIOThxQW9qamJxQnYxMWl5eUtJRjhaMEpSWjByVVVmWGI5bTdlN25hY0pRdWplYldsRjVVaHJGelhvSkg4dWludE1HVUJqb296bjA4R0VBaVNnTzFjVlAvK2R0Yk1wYms2bGJic0hYeDhFYlBNdkJIbEtuRVluVmxabmh6NWpVczdvUnNxYzVIbWFYOW40d0JXaEo5MjEwaVFLbnMvWW5BZW54WUFNb1h5V01icmVWWEpPdCtZQ0p0bWxPOHBhd2w1NlIzeGMreS9nR2FqdDU4Qm5pdVViZ2tJOGZRcmxQQzlnelZYQ2xHSUVtUGNmVUpkYzRVT2RpOUVvNzdSYk5qWHNub3JYUUpWR2J0VXp0bVJnNFlvZ1ZURVk2aU4vTklRdm4zSjhmVU15SFhJWElLVkVXUStHa29Pc1ovY2c0UzNqTkIzaFFGRVgxUzdzOGd5UVVpQlg4dnk3UldoSFRISm9lT1BMeG5XR2NUSExId05vbWtpTWZmZTRLekxVdmxYT1J2b1JVeFljQ0hIQnUzSURNdWdBRGU3cmRHYjlQN011ODhIS0hGMVhOS2JSaEh4b2giLCJtYWMiOiI3YjczODJkMjQ1ZGMyNTI4ZGNhNmM0NDdmZTMxMGIxMDkxZjQ0MTM1Y2VlMmJhMDAwYzdjZjI1MDQwMmFmZmU0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldQdHlibDVNSFdoZFh6OTJqZ2FCUFE9PSIsInZhbHVlIjoiRGJMazFvYkxLa0xNV09KVFBZcXVGRnBWaG9oOGhrbjhaUFJUQ3hIeVQ4OWFDRjhGd0dqSktFVmZ3blNsZVBtanh3Q3p3WHNKOEVHbVZkN0dwNVFMYW5wVnFjMHRaZk1JWExhV0hkelJYY0dOaGZDUmxGdk9lK0VIVkZqeVd0VUNCdkZmbWtTcnZHVlVnSEU2N2NycFZLK1VueEdhWlhoNm1KZlpHOHRwSk1Nc1dPbGt6VGRkU2EzVlR5RU9vQ05hbEE0eEQ4TjZpZHFhM2RUNng5Q3l2SGRybCtuUno1WUlJZXdBZnBKbkRPdWFUYnplT1pUdTdUMWtUSVRVUDNicjZyZW94eHJtRmNkZGgwMGdZRmNzeG82bzhEb1JmUHltT2Q2Tjk4RkhSVlcwQmh3UVFFa3NOZHlEcjAzNlFPSmVWa3BYbWpCSWRyZDdyREdPdnI0QmEyZEcvQ0FXYUlCNXptMDlHbHJ4ZWJ6RUxoZjR2K2lKWkNwc3Vid0Z0K2N3N08zdWVQKzk2MVhvK2paNVZsak1XTFdKTFNtcGVDYUdrVHFtRzYzeWZjS1h0SU9FangrNWdTMDFXUXZUeVN2OUN3cXNSaFd6WlVEV1FNeDJKR2FtWjEvR1lnQ05zY0lFV3IyQkt2NDN4Uk0vMFB6bFA2S3BxQ3Y0Skx1QTlhb1EiLCJtYWMiOiI3NWYzODIzNTdmN2Y3YTE3MDc0MjI0YTczMDhjNjNmZjRmZGVjMDFmNmVjMTEwYTRiMTNhOTUyNThjMzBmMTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1411510148 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411510148\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-429017332 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:22:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhiTTZrNlRDZUhaK3hWVU11OXZWYnc9PSIsInZhbHVlIjoiSGFqU08wQVVKUUN2WklTcWtlekFsRkl0SFZjRGxsUVpRUHhtOU1ycm4zc3llYkxoYkFGYVlyb2JlbkJEUUlqbE5tLzdqWVBYbjRrU2tYbGhVcGNjVE5ML2RMYVJFUnY5Z1hCbkJ0b3ZsaXBwZFdxOStvZDR6S0FLK3BYQnM4Ti9acE5PRDBDbTM3WkVnbEFyZW5wNlMyNkYzSnVrRHVWMDhQYW1YMlBGbzBmMmxnTFgzdVZJNjBnenNKcks0YlZScFkrbHJ3UnkvdGZMU0p5TmdIeHBGVWNMamxCdFlyQXlOTzdCcTEzMWxsdStxTzhiWEhQMVUzMUZ4SHY0YmMvQmkxa3RJSVNmUFNvYk9hczVpaU0waHJmcU1ZSTdQS3Z1dXBTSDNXR3ZSSEtGZndSSVJEOGZCZ01PYWcrYjMwUnhlRHhmeHhTNmNycmxoOU1Bdmc5bUswUjhHU2Y2WW4xSGRLTktib3l5VkMzZUNQd252ZWl6RFZMdHRpSkJFNG5jdHhXR0l5WCtiaGs3bkpwV2d5TjhHSkVlUWtCejBIeC9tejQ2L1BRbDAvN1lsRGt6SElYaHdQbkw1ay93NkdBSXlmQm1hVkh4ZVBmcWRTSzJEVlBtNUVoZ2gyWURIL3pnOHl5ME1EUjhjb0pTb1JwdkNDV1JnS2RTb3M1S0U4L2MiLCJtYWMiOiJkNTJiNTViNDQ0NzI1N2JlZjBiYzQzYTEwMjVkOTg1MmI1YzNlZDgwZTg3Y2RjZjEzMGZkODFmMzVkZTI3ZTdlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:22:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InBuQUpxdFRWRlBZYUNBWVRWZVhNMkE9PSIsInZhbHVlIjoibjBXSk55UlRrSUdzSGJpaENtNTZ2Mjd6aDlyZmZOYkFLSGthRlpndGNpYkFvbzdyK1BGc0VZN0VYYzFpYXBBSDBIY3ZFL2RoK25iSEVlWVBaTzJuZ1psOUZKQUk4Y05VTVBjR3BSZ0MzRGxVZk9FZHJjOXJOZUhiVzBkUjI0UVRVam54bkFnRTVUVUtHWlRiclN2bGtQMVZkTHVMSE00aEZ0WWNZSkFXeVJPczVpMXo3eUFiUW5pNGhyYVNwUlM2UkJyekhzTjNjTWVETENXN1kyeUt5Z3BRRGFoWTB6cFk1V3U3cUJnNm1LZjVjK1dnMys0YkpMZnkrVTJodXE0OFgvVWkyemZEcmNRSmJBelJLbGd3RVRQUlI5NTBZa3c0U2dTN2I4THZ5WXZyUkpHZmlKT0wrQWZ6SFRTcmV4YXVPVFVSeVFBemtTSktaU1BmUHducTNCZ3h0cnlGWWIyWFlNaDBCZXE4U2VxN3p2SXhOMHliaWN5bE5kWi93bmZBbkNnT3o2UVZDMTZ4cjRySXJmaVVXZm11YWMyWlZ4Z3RHZk1QSElVL3BEWEREakcrek5qSWEvWXJpelMvQ2diTU9wOFdUU3JmQTI1a2pNU2dMelJVMzZ3WEcrL1I1L01xSnFTOWo2ekxFWU1NVVVsejR1SUtuZ2ZFS1Z5eHZOMHIiLCJtYWMiOiIwMzRlZjBjOTJmMzY1NzYyOTRkZTQ5NDAyZDdkNDUzNjkzY2IwN2U1MmVmYjRhYWI1MDg4ZDRhMGMxNDRiYWQ5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:22:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhiTTZrNlRDZUhaK3hWVU11OXZWYnc9PSIsInZhbHVlIjoiSGFqU08wQVVKUUN2WklTcWtlekFsRkl0SFZjRGxsUVpRUHhtOU1ycm4zc3llYkxoYkFGYVlyb2JlbkJEUUlqbE5tLzdqWVBYbjRrU2tYbGhVcGNjVE5ML2RMYVJFUnY5Z1hCbkJ0b3ZsaXBwZFdxOStvZDR6S0FLK3BYQnM4Ti9acE5PRDBDbTM3WkVnbEFyZW5wNlMyNkYzSnVrRHVWMDhQYW1YMlBGbzBmMmxnTFgzdVZJNjBnenNKcks0YlZScFkrbHJ3UnkvdGZMU0p5TmdIeHBGVWNMamxCdFlyQXlOTzdCcTEzMWxsdStxTzhiWEhQMVUzMUZ4SHY0YmMvQmkxa3RJSVNmUFNvYk9hczVpaU0waHJmcU1ZSTdQS3Z1dXBTSDNXR3ZSSEtGZndSSVJEOGZCZ01PYWcrYjMwUnhlRHhmeHhTNmNycmxoOU1Bdmc5bUswUjhHU2Y2WW4xSGRLTktib3l5VkMzZUNQd252ZWl6RFZMdHRpSkJFNG5jdHhXR0l5WCtiaGs3bkpwV2d5TjhHSkVlUWtCejBIeC9tejQ2L1BRbDAvN1lsRGt6SElYaHdQbkw1ay93NkdBSXlmQm1hVkh4ZVBmcWRTSzJEVlBtNUVoZ2gyWURIL3pnOHl5ME1EUjhjb0pTb1JwdkNDV1JnS2RTb3M1S0U4L2MiLCJtYWMiOiJkNTJiNTViNDQ0NzI1N2JlZjBiYzQzYTEwMjVkOTg1MmI1YzNlZDgwZTg3Y2RjZjEzMGZkODFmMzVkZTI3ZTdlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:22:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InBuQUpxdFRWRlBZYUNBWVRWZVhNMkE9PSIsInZhbHVlIjoibjBXSk55UlRrSUdzSGJpaENtNTZ2Mjd6aDlyZmZOYkFLSGthRlpndGNpYkFvbzdyK1BGc0VZN0VYYzFpYXBBSDBIY3ZFL2RoK25iSEVlWVBaTzJuZ1psOUZKQUk4Y05VTVBjR3BSZ0MzRGxVZk9FZHJjOXJOZUhiVzBkUjI0UVRVam54bkFnRTVUVUtHWlRiclN2bGtQMVZkTHVMSE00aEZ0WWNZSkFXeVJPczVpMXo3eUFiUW5pNGhyYVNwUlM2UkJyekhzTjNjTWVETENXN1kyeUt5Z3BRRGFoWTB6cFk1V3U3cUJnNm1LZjVjK1dnMys0YkpMZnkrVTJodXE0OFgvVWkyemZEcmNRSmJBelJLbGd3RVRQUlI5NTBZa3c0U2dTN2I4THZ5WXZyUkpHZmlKT0wrQWZ6SFRTcmV4YXVPVFVSeVFBemtTSktaU1BmUHducTNCZ3h0cnlGWWIyWFlNaDBCZXE4U2VxN3p2SXhOMHliaWN5bE5kWi93bmZBbkNnT3o2UVZDMTZ4cjRySXJmaVVXZm11YWMyWlZ4Z3RHZk1QSElVL3BEWEREakcrek5qSWEvWXJpelMvQ2diTU9wOFdUU3JmQTI1a2pNU2dMelJVMzZ3WEcrL1I1L01xSnFTOWo2ekxFWU1NVVVsejR1SUtuZ2ZFS1Z5eHZOMHIiLCJtYWMiOiIwMzRlZjBjOTJmMzY1NzYyOTRkZTQ5NDAyZDdkNDUzNjkzY2IwN2U1MmVmYjRhYWI1MDg4ZDRhMGMxNDRiYWQ5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:22:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429017332\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-947982201 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-947982201\", {\"maxDepth\":0})</script>\n"}}