{"__meta": {"id": "X9e1fa9e619d0bac018d47f026194a08d", "datetime": "2025-06-06 20:34:34", "utime": **********.875776, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.595307, "end": **********.875824, "duration": 1.****************, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": **********.595307, "relative_start": 0, "end": **********.67617, "relative_end": **********.67617, "duration": 1.****************, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.676192, "relative_start": 1.****************, "end": **********.875828, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01754, "accumulated_duration_str": "17.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7669349, "duration": 0.01426, "duration_str": "14.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.3}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.808706, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.3, "width_percent": 6.043}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.841337, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 87.343, "width_percent": 12.657}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242072398%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjROa3VKMTBmcnNNVFFHaGI1TE9TaEE9PSIsInZhbHVlIjoiMFQ1djB6a0F3SWtJOWhSOUluRGgvdHJySlREaFVKTU9EcjM0UzZOSXdoUndUaTFUbm5GdHlHNVE0c2g5N1FiQ1QvaFY0VjNLeTJMM2twQ2drRE9ZWkNJQ1JTSmlGNWR1YVFad2Vja042K1FMRXB0NEtsNDRwS1hIYjNxWlRsNFJVSG0ya0FLOFE4UjVuckxhcDFxbDBZdnAvRERNWHRpenRQK0lMVm14U1YwTmRIbDAwZzY4c0UyYVNpWGl5NzBva2RYQW9Fa0c2UlIvYTBZYk5nWURKdjF0Z3k5OEMzMFRmMHVqMkpRdmViVm1nUlVpS1ZBR2pGMlUxR1BZQVNYWk5ObGpFSi9zSWZlZ1NYUzk5STZuSmNUeDcwVVcva0FLNG1ta3JoTG5veWFwTGVuU3g1dXZISTBFYjRsbVVPVnVHVFlQbjdrcGIvSDZSbEVrUFVjdGFmalExWkswZlY2Z0phOUhaVUROd2JUUms0eEJLYjJHaDQxa29XU2RwaUpFb2JJcDFvelVKcGIzU1pDSk95SkRLdDZ6MUxUVW5vdWxLT2NWSGxrZlVvWEZ6L0c1REZ1ZGZPWHNLVnVmMnpBWUpkQnFseDR1dW5ITGszVUwwQWdBQTZMdDdUd29ZNE5JdjN1TzdQRlR4bStMT3ZkaHRXY3Qra3pNWVd3MVFOYXciLCJtYWMiOiJlZDU3MzM5MWY1NjFkMDJhZGY4N2NhZGI0ODI5Yjg3YmQwOTZhOTEzMmVhOTA3ZDgzNmUzNzk3ZDZlZTc3ZTdlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFKMmNxNTV6VHArZDBzSTNNblM1SlE9PSIsInZhbHVlIjoiUDU5Z1hHVW5QSC96eko0UW1qQWZ1dEdrUHZ0QUd1dGYyazhLcm9YSVl2SjRNYjNYcU9Vd3JIMjFEc2N2R0pMWEJJdzE0ZXlLelRvZ3pOSkpNQng5RUIzaURqR2x1Q3NUaitMVDV3Q0ZHY3lrSUlKZCtZZWpwOGM2NW1VNWc5YnEyVEhyTW8vaWsvd3U4YlpuK3pSazBzRVZ6c0gxanVRSzBnV3QwZktZSDl4Y3FaRmlIK3p6OXF6NDVTVlcyS0dVR3BPUytNdENpTTR5RFJTNURCb2NIcDl2YzJtOUFManRhZ1BIc0U5TU1xMlN0TUJYYkc5US9PcTBqMnlOcjYxQmNHYjBMc0VldndmaDEySnFRRmgwYk9sck9GLzFHRkdPa3ZuTjk2YkkxaEVGSnZKakU5SGp1dzFnYkR0ekcwVitjMlJVUzlsNzdOVG9rWXE1NWszU2t1OHIzLzdHNDNUU083c09HWGtPNkordEQvQ3BWSHFIQTNxQTNNOUQ5djBsVUpuT3VGMk0yb2xseWhSWFBscS9WS2kyU3BjdkVYalVuNVhzT0tIZEpvM2VLZFJudEJ3TEloWERoeUhFeVluYjdwY2tJaXEvdnZqOEpJam9XMmlXYVEvaUhqcE9PTDM4YlQ2VWlNU1g5THB0SVVhemQwdjFrZDlUSjNGSkIwWk0iLCJtYWMiOiI2ZTdjNmY2M2E4MjNjZTBmN2M5NTc5MDFiZTI0OTc1NTY2MjdjODQ1YTc4NzQyMzMyMDg4NDg1NDliODQ0YTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-415882622 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415882622\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-86227841 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik01OW1SSUJmTGVYWlU3cmcrNVUrRmc9PSIsInZhbHVlIjoiSFpMN1dLbjQ5eUxKTmcxajh3cEtWRWIxUE5laTlvYWFvRkg5RXRlbk54eDYwcGdqa1Y5OFlIOVcwTFFFMEZHNGNPRnpHZCtTTVU3YjN2ZDZ1K2Y3a3JJWHIydVhhMWlhLzhIdHR3TGVtcDRlMEJUVnRJUVJPU00rU2JWSXNDenRzSVduTlVyQ1FuenBJTFE1d3JWYlhmNGJ6RHlFUFFZb3JVakFtSGlqRlZIVXRRV2VnZ0c0K29rdHkyM3FtNlg1MFY3MVFDRzlIME0xOE5IbWJHUlc1MXVTVU4rWkFHU2puaU5PN1Q3NWhMRDVMaS9EeGJxZHpCeFREcVlVTHM3Nkd5aTEwQUlKNzhwTU5jOUcxZ0VsWHVVTkRkZ3lzUDlnRjBnOGJUWnVnVXNpSGNTK2o2U0RUak5JcUJpaEpwZVVIblFvbldjbnVNY01yY2xGM1FseC9MWVA0TG1CdGFma0FKMHRxQzdHZlI0UXRRakZvMFpzN0xqRUhoZXE4Y3gzNVBZeGlMeWtQQy94QytsY25PSm5adjcwVlR6SVNxczZoUG02U051eHlMZEZJR1d0bHZhVlJsaDBzWjgvZmVkb0lKMnpRM1ZWSGhXNmhiMFdWUnBiazVnZlVUSTVsNlArRzZNSWpHai83czZtWU41dEhLNUNtejdtOFUxa3RsUnciLCJtYWMiOiI5YzY1NmI0NTEyMzczMzIzYjI1YzdiMWJiMDBjNGUyYzliNzg1NTlhYWQ4NjAzNzQyMGI2ZTM1OGI5MWNiMmVkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRnWnppZVNuNEp2ODJFb1R1RGNOaFE9PSIsInZhbHVlIjoiZXhhdWxjNnFwck82MGQ1ZzVSby93VnVIYTdlT1pKTS9OSkhYcDEydEhTZTV4RUF0V1FVd2djVW52bWFGVGJURWdsbCs3bTUyZ3d5Q2tZZ0w0RWEzdVU4ZmRVekViRk14Vy9RZWhGbC9iNVcxVkpRK2pJZVozVEV4bGhNUHJtS2dtNHE2R3QxMHlGNUJqNS9Ma2JhQzVxbGJ0YnpvVzlrN2t0N2VIZjBjL1oyaTFpK0V4SFpVRjFTdXByc0h3NmxVYTU1cXdEeXBrZVM4b1ZwR1IzWStCUXJiMDBOQTE5dXAzcjY5SC8zSDNrcnh2VzlzcXFEVzFGMURBcGpwcTl1dFpPZnFKV0J4ODVTVXV5Ylc1RVRkajBCMG9xd3NNZ09KMk05RDhHQ0NrTWthYmlLVFFPOU1UU0FETjY1KzViM1Q0WGp3aE5RMHdqenJCODV0MVhoM3FHd1VIWXNxWkFMS1NUVi9BTStTb250VHZvRzhPSUUyUjhJSm4zLzZJVEVWUGZtRFNnbXR6bUZybEVaRmdjNU9CTUxwVGZTeTJucHYwVThJMkN0MnlBQ24wZWR5Q3hlMzlOeHBnb25BTFU3dnV4RkpKRm0vNDdBZ2Jhc1JkMGgzT0Rhcm1UUGtxSzdYVmdwYWErR0lBRFI5eGJVSzN4eTF3eStDZitCaklKT2IiLCJtYWMiOiIzMTAwZTAzZTJkNTUwMmZiZWNmODAwZGZmNTg4ODM4Mjc4NTEyNWNhODljN2EyNGVlYmVjMDhmNWQxZDY1M2E4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik01OW1SSUJmTGVYWlU3cmcrNVUrRmc9PSIsInZhbHVlIjoiSFpMN1dLbjQ5eUxKTmcxajh3cEtWRWIxUE5laTlvYWFvRkg5RXRlbk54eDYwcGdqa1Y5OFlIOVcwTFFFMEZHNGNPRnpHZCtTTVU3YjN2ZDZ1K2Y3a3JJWHIydVhhMWlhLzhIdHR3TGVtcDRlMEJUVnRJUVJPU00rU2JWSXNDenRzSVduTlVyQ1FuenBJTFE1d3JWYlhmNGJ6RHlFUFFZb3JVakFtSGlqRlZIVXRRV2VnZ0c0K29rdHkyM3FtNlg1MFY3MVFDRzlIME0xOE5IbWJHUlc1MXVTVU4rWkFHU2puaU5PN1Q3NWhMRDVMaS9EeGJxZHpCeFREcVlVTHM3Nkd5aTEwQUlKNzhwTU5jOUcxZ0VsWHVVTkRkZ3lzUDlnRjBnOGJUWnVnVXNpSGNTK2o2U0RUak5JcUJpaEpwZVVIblFvbldjbnVNY01yY2xGM1FseC9MWVA0TG1CdGFma0FKMHRxQzdHZlI0UXRRakZvMFpzN0xqRUhoZXE4Y3gzNVBZeGlMeWtQQy94QytsY25PSm5adjcwVlR6SVNxczZoUG02U051eHlMZEZJR1d0bHZhVlJsaDBzWjgvZmVkb0lKMnpRM1ZWSGhXNmhiMFdWUnBiazVnZlVUSTVsNlArRzZNSWpHai83czZtWU41dEhLNUNtejdtOFUxa3RsUnciLCJtYWMiOiI5YzY1NmI0NTEyMzczMzIzYjI1YzdiMWJiMDBjNGUyYzliNzg1NTlhYWQ4NjAzNzQyMGI2ZTM1OGI5MWNiMmVkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRnWnppZVNuNEp2ODJFb1R1RGNOaFE9PSIsInZhbHVlIjoiZXhhdWxjNnFwck82MGQ1ZzVSby93VnVIYTdlT1pKTS9OSkhYcDEydEhTZTV4RUF0V1FVd2djVW52bWFGVGJURWdsbCs3bTUyZ3d5Q2tZZ0w0RWEzdVU4ZmRVekViRk14Vy9RZWhGbC9iNVcxVkpRK2pJZVozVEV4bGhNUHJtS2dtNHE2R3QxMHlGNUJqNS9Ma2JhQzVxbGJ0YnpvVzlrN2t0N2VIZjBjL1oyaTFpK0V4SFpVRjFTdXByc0h3NmxVYTU1cXdEeXBrZVM4b1ZwR1IzWStCUXJiMDBOQTE5dXAzcjY5SC8zSDNrcnh2VzlzcXFEVzFGMURBcGpwcTl1dFpPZnFKV0J4ODVTVXV5Ylc1RVRkajBCMG9xd3NNZ09KMk05RDhHQ0NrTWthYmlLVFFPOU1UU0FETjY1KzViM1Q0WGp3aE5RMHdqenJCODV0MVhoM3FHd1VIWXNxWkFMS1NUVi9BTStTb250VHZvRzhPSUUyUjhJSm4zLzZJVEVWUGZtRFNnbXR6bUZybEVaRmdjNU9CTUxwVGZTeTJucHYwVThJMkN0MnlBQ24wZWR5Q3hlMzlOeHBnb25BTFU3dnV4RkpKRm0vNDdBZ2Jhc1JkMGgzT0Rhcm1UUGtxSzdYVmdwYWErR0lBRFI5eGJVSzN4eTF3eStDZitCaklKT2IiLCJtYWMiOiIzMTAwZTAzZTJkNTUwMmZiZWNmODAwZGZmNTg4ODM4Mjc4NTEyNWNhODljN2EyNGVlYmVjMDhmNWQxZDY1M2E4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86227841\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1501942505 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501942505\", {\"maxDepth\":0})</script>\n"}}