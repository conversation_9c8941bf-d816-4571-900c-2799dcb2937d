# 📊 هيكل البيانات - شاشة إدارة النقد المتقدمة

## 🎯 الهدف من الشاشة
مراقبة الشفتات، حركة النقد (قبض/صرف)، إجمالي مبيعات نقاط البيع (POS)، وربط كل ذلك بالمستخدمين والمستودعات.

## 📋 مكونات الشاشة المطلوبة

### 1. 🔍 قسم الفلاتر (Header Filters)
```json
{
  "warehouse_filter": {
    "type": "dropdown",
    "source": "warehouses table",
    "default": "all"
  },
  "user_filter": {
    "type": "dropdown", 
    "source": "users table",
    "default": "all"
  },
  "date_range": {
    "type": "daterangepicker",
    "default": "current_month"
  },
  "refresh_button": {
    "action": "reload_all_data"
  }
}
```

### 2. 📊 الإحصائيات السريعة (Quick Stats)
```json
{
  "daily_receipts": {
    "source": "receipt_vouchers + pos_payments (cash)",
    "calculation": "SUM(amount) WHERE date = today",
    "color": "success"
  },
  "daily_payments": {
    "source": "payment_vouchers",
    "calculation": "SUM(amount) WHERE date = today", 
    "color": "danger"
  },
  "net_cash": {
    "calculation": "daily_receipts - daily_payments",
    "color": "info"
  },
  "open_shifts": {
    "source": "shifts",
    "calculation": "COUNT(*) WHERE is_closed = false",
    "color": "warning"
  }
}
```

### 3. ⚠️ التنبيهات (Alerts)
```json
{
  "long_open_shifts": {
    "condition": "shifts WHERE is_closed = false AND opened_at < (NOW() - 12 HOURS)",
    "type": "warning",
    "message": "شفتات مفتوحة لأكثر من 12 ساعة"
  },
  "cash_deficit": {
    "condition": "financial_records WHERE deficit > 0",
    "type": "danger", 
    "message": "يوجد عجز في النقد"
  },
  "high_cash_amount": {
    "condition": "financial_records WHERE current_cash > threshold",
    "type": "info",
    "message": "مبلغ نقدي مرتفع يحتاج إيداع"
  }
}
```

## 📑 الجداول المطلوبة

### 4. 🕐 جدول الشفتات (Shifts Table)
```sql
SELECT 
    s.id as shift_id,
    s.shift_opening_balance,
    s.opened_at,
    s.closed_at,
    s.is_closed,
    u1.name as created_by_name,
    u2.name as closed_by_name,
    w.name as warehouse_name,
    fr.current_cash,
    fr.overnetwork_cash,
    fr.total_cash,
    fr.deficit
FROM shifts s
LEFT JOIN users u1 ON s.created_by = u1.id
LEFT JOIN users u2 ON s.closed_by = u2.id  
LEFT JOIN warehouses w ON s.warehouse_id = w.id
LEFT JOIN financial_records fr ON s.id = fr.shift_id
WHERE s.created_by = ? OR ? = 'all'
AND s.warehouse_id = ? OR ? = 'all'
AND DATE(s.opened_at) BETWEEN ? AND ?
ORDER BY s.opened_at DESC
```

### 5. 📥 جدول سندات القبض (Receipt Vouchers)
```sql
SELECT 
    rv.id,
    rv.voucher_number,
    rv.voucher_date,
    rv.amount,
    rv.payment_method,
    rv.description,
    rv.notes,
    u.name as created_by_name,
    w.name as warehouse_name
FROM receipt_vouchers rv
LEFT JOIN users u ON rv.created_by = u.id
LEFT JOIN warehouses w ON rv.warehouse_id = w.id
WHERE rv.created_by = ? OR ? = 'all'
AND rv.warehouse_id = ? OR ? = 'all'
AND rv.voucher_date BETWEEN ? AND ?
ORDER BY rv.voucher_date DESC
```

### 6. 📤 جدول سندات الصرف (Payment Vouchers)
```sql
SELECT 
    pv.id,
    pv.voucher_number,
    pv.voucher_date,
    pv.amount,
    pv.payment_method,
    pv.description,
    pv.notes,
    u.name as created_by_name,
    w.name as warehouse_name
FROM payment_vouchers pv
LEFT JOIN users u ON pv.created_by = u.id
LEFT JOIN warehouses w ON pv.warehouse_id = w.id
WHERE pv.created_by = ? OR ? = 'all'
AND pv.warehouse_id = ? OR ? = 'all'
AND pv.voucher_date BETWEEN ? AND ?
ORDER BY pv.voucher_date DESC
```

### 7. 🛒 جدول مبيعات POS (POS Sales Summary)
```sql
SELECT 
    DATE(p.pos_date) as sale_date,
    u.name as user_name,
    w.name as warehouse_name,
    COUNT(p.id) as invoice_count,
    SUM(CASE WHEN pp.payment_type = 'cash' THEN pp.cash_amount ELSE 0 END) as total_cash,
    SUM(CASE WHEN pp.payment_type = 'network' THEN pp.network_amount ELSE 0 END) as total_network,
    SUM(CASE WHEN pp.payment_type = 'split' THEN pp.cash_amount + pp.network_amount ELSE pp.amount END) as total_amount
FROM pos p
LEFT JOIN pos_payments pp ON p.id = pp.pos_id
LEFT JOIN users u ON p.created_by = u.id
LEFT JOIN warehouses w ON p.warehouse_id = w.id
WHERE p.created_by = ? OR ? = 'all'
AND p.warehouse_id = ? OR ? = 'all'
AND p.pos_date BETWEEN ? AND ?
GROUP BY DATE(p.pos_date), p.created_by, p.warehouse_id
ORDER BY sale_date DESC
```

## 📈 الرسوم البيانية المطلوبة

### 8. 🥧 رسم دائري لأنواع المدفوعات
```json
{
  "chart_type": "doughnut",
  "data_source": "pos_payments",
  "categories": [
    {
      "label": "نقد",
      "value": "SUM(cash_amount)",
      "color": "#28a745"
    },
    {
      "label": "بطاقة ائتمان", 
      "value": "SUM(network_amount)",
      "color": "#17a2b8"
    },
    {
      "label": "مختلط",
      "value": "COUNT(*) WHERE payment_type = 'split'",
      "color": "#ffc107"
    }
  ]
}
```

### 9. 📊 رسم شريطي للمبيعات اليومية
```json
{
  "chart_type": "bar",
  "data_source": "pos_payments grouped by date",
  "x_axis": "dates in range",
  "y_axis": "total_amount per day",
  "colors": ["#667eea", "#764ba2"]
}
```

## 🔄 تدفق البيانات (Data Flow)

### عند تحميل الصفحة:
1. **تحميل الفلاتر**: جلب قائمة المستودعات والمستخدمين
2. **تحميل الإحصائيات**: حساب الإحصائيات السريعة
3. **فحص التنبيهات**: البحث عن حالات تحتاج تنبيه
4. **تحميل الجداول**: عرض البيانات الافتراضية (آخر 30 يوم)

### عند تطبيق الفلاتر:
1. **إعادة حساب الإحصائيات** بناءً على الفلاتر الجديدة
2. **تحديث جميع الجداول** بالبيانات المفلترة
3. **تحديث الرسوم البيانية** بالبيانات الجديدة
4. **فحص التنبيهات** للفترة المحددة

## 🎨 التصميم والألوان

### نظام الألوان:
- **أخضر (#28a745)**: المقبوضات والأرباح
- **أحمر (#dc3545)**: المصروفات والعجز  
- **أزرق (#17a2b8)**: المعلومات العامة
- **أصفر (#ffc107)**: التنبيهات والتحذيرات
- **بنفسجي (#667eea)**: العناوين الرئيسية

### التفاعل:
- **جداول قابلة للفرز والبحث**
- **أزرار سريعة للإجراءات** (عرض، تعديل، طباعة)
- **تحديث تلقائي** كل 5 دقائق للشفتات المفتوحة
- **تصدير البيانات** إلى Excel/PDF

## 🔧 المتطلبات التقنية

### الجداول المطلوب إنشاؤها:
```sql
-- إذا لم تكن موجودة
CREATE TABLE receipt_vouchers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    voucher_number VARCHAR(50) UNIQUE,
    voucher_date DATE,
    amount DECIMAL(15,2),
    payment_method ENUM('cash', 'bank_transfer', 'check'),
    description TEXT,
    notes TEXT,
    warehouse_id BIGINT,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE payment_vouchers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    voucher_number VARCHAR(50) UNIQUE,
    voucher_date DATE,
    amount DECIMAL(15,2),
    payment_method ENUM('cash', 'bank_transfer', 'check'),
    description TEXT,
    notes TEXT,
    warehouse_id BIGINT,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### الملفات المطلوب إنشاؤها:
1. **Controller**: `AdvancedCashManagementController.php`
2. **Models**: `ReceiptVoucher.php`, `PaymentVoucher.php`
3. **Views**: `financial_operations/advanced_cash_management/index.blade.php`
4. **Routes**: إضافة المسارات المطلوبة
5. **Migrations**: إنشاء الجداول الجديدة
