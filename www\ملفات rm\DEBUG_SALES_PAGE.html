<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص بيانات المبيعات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .debug-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #e6ffe6;
            color: #00b894;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn {
            background: #0984e3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0770c4;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .data-table th {
            background: #f8f9fa;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص بيانات المبيعات</h1>
            <p>أداة لفحص البيانات الموجودة في قاعدة البيانات</p>
        </div>

        <div class="section">
            <h3>📊 فحص البيانات المباشر</h3>
            <button class="btn" onclick="checkRealtimeData()">فحص البيانات المباشرة</button>
            <button class="btn" onclick="checkDebugData()">فحص تفاصيل قاعدة البيانات</button>
            <button class="btn" onclick="checkCurrentUser()">فحص بيانات المستخدم الحالي</button>
            
            <div id="realtime-results"></div>
        </div>

        <div class="section">
            <h3>🗄️ معلومات قاعدة البيانات</h3>
            <div id="database-info"></div>
        </div>

        <div class="section">
            <h3>📈 نتائج التشخيص</h3>
            <div id="debug-results"></div>
        </div>

        <div class="section">
            <h3>💡 التوصيات</h3>
            <div id="recommendations"></div>
        </div>
    </div>

    <script>
        // فحص البيانات المباشرة
        async function checkRealtimeData() {
            try {
                const response = await fetch('/financial-operations/sales-analytics/realtime-dashboard');
                const data = await response.json();
                
                const resultsDiv = document.getElementById('realtime-results');
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ نجح الاتصال بالنظام</h4>
                            <p><strong>مبيعات اليوم:</strong> ${data.data.today.sales} فاتورة</p>
                            <p><strong>مبلغ اليوم:</strong> ${data.data.today.amount} ريال</p>
                            <p><strong>مبيعات الساعة:</strong> ${data.data.current_hour.sales} فاتورة</p>
                            <p><strong>مبلغ الساعة:</strong> ${data.data.current_hour.amount} ريال</p>
                            
                            ${data.data.debug_info ? `
                                <div class="debug-info">
                                    <h5>🔍 معلومات التشخيص:</h5>
                                    <pre>${JSON.stringify(data.data.debug_info, null, 2)}</pre>
                                </div>
                            ` : ''}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ خطأ في جلب البيانات</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('realtime-results').innerHTML = `
                    <div class="error">
                        <h4>❌ خطأ في الاتصال</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // فحص تفاصيل قاعدة البيانات
        async function checkDebugData() {
            try {
                const response = await fetch('/financial-operations/sales-analytics/debug-data');
                const data = await response.json();
                
                const debugDiv = document.getElementById('debug-results');
                
                if (data.success) {
                    const debugData = data.debug_data;
                    
                    debugDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ تم فحص قاعدة البيانات بنجاح</h4>
                            
                            <h5>📋 الجداول الموجودة:</h5>
                            <table class="data-table">
                                <tr><th>الجدول</th><th>موجود</th></tr>
                                <tr><td>pos</td><td>${debugData.tables_exist.pos ? '✅' : '❌'}</td></tr>
                                <tr><td>pos_v2</td><td>${debugData.tables_exist.pos_v2 ? '✅' : '❌'}</td></tr>
                                <tr><td>pos_payments</td><td>${debugData.tables_exist.pos_payments ? '✅' : '❌'}</td></tr>
                                <tr><td>pos_v2_payments</td><td>${debugData.tables_exist.pos_v2_payments ? '✅' : '❌'}</td></tr>
                            </table>

                            <h5>📊 إجمالي السجلات:</h5>
                            <table class="data-table">
                                <tr><th>الجدول</th><th>إجمالي السجلات</th><th>سجلات المستخدم</th><th>سجلات اليوم</th></tr>
                                ${debugData.total_records.pos !== undefined ? `
                                    <tr>
                                        <td>pos</td>
                                        <td>${debugData.total_records.pos}</td>
                                        <td>${debugData.user_records.pos}</td>
                                        <td>${debugData.today_records.pos}</td>
                                    </tr>
                                ` : ''}
                                ${debugData.total_records.pos_v2 !== undefined ? `
                                    <tr>
                                        <td>pos_v2</td>
                                        <td>${debugData.total_records.pos_v2}</td>
                                        <td>${debugData.user_records.pos_v2}</td>
                                        <td>${debugData.today_records.pos_v2}</td>
                                    </tr>
                                ` : ''}
                            </table>

                            <h5>🏪 المستودعات المتاحة:</h5>
                            <table class="data-table">
                                <tr><th>ID</th><th>اسم المستودع</th></tr>
                                ${debugData.warehouses.map(w => `<tr><td>${w.id}</td><td>${w.name}</td></tr>`).join('')}
                            </table>

                            <div class="debug-info">
                                <h5>🔍 البيانات الكاملة:</h5>
                                <pre>${JSON.stringify(debugData, null, 2)}</pre>
                            </div>
                        </div>
                    `;

                    // إضافة التوصيات
                    generateRecommendations(debugData);
                    
                } else {
                    debugDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ خطأ في فحص قاعدة البيانات</h4>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('debug-results').innerHTML = `
                    <div class="error">
                        <h4>❌ خطأ في الاتصال</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // إنشاء التوصيات
        function generateRecommendations(debugData) {
            const recommendationsDiv = document.getElementById('recommendations');
            let recommendations = [];

            // فحص وجود البيانات
            if (debugData.user_records.pos === 0 && debugData.user_records.pos_v2 === 0) {
                recommendations.push('❌ لا توجد فواتير للمستخدم الحالي في أي من النظامين');
                recommendations.push('💡 قم بإنشاء فاتورة تجريبية في نظام POS');
            }

            if (debugData.today_records.pos === 0 && debugData.today_records.pos_v2 === 0) {
                recommendations.push('❌ لا توجد فواتير لليوم الحالي');
                recommendations.push('💡 قم بإنشاء فاتورة جديدة في نظام POS اليوم');
            }

            if (!debugData.tables_exist.pos_v2) {
                recommendations.push('⚠️ جدول pos_v2 غير موجود - النظام يدعم POS Classic فقط');
            }

            if (debugData.user_records.pos > 0 || debugData.user_records.pos_v2 > 0) {
                recommendations.push('✅ يوجد فواتير للمستخدم - تحقق من فلاتر المستودع والتاريخ');
            }

            if (recommendations.length === 0) {
                recommendations.push('✅ النظام يبدو سليماً - تحقق من الفلاتر في الواجهة');
            }

            recommendationsDiv.innerHTML = `
                <div class="debug-info">
                    ${recommendations.map(rec => `<p>${rec}</p>`).join('')}
                </div>
            `;
        }

        // فحص بيانات المستخدم الحالي
        function checkCurrentUser() {
            const userInfo = document.getElementById('database-info');
            userInfo.innerHTML = `
                <div class="debug-info">
                    <h4>👤 معلومات المستخدم الحالي</h4>
                    <p><strong>URL الحالي:</strong> ${window.location.href}</p>
                    <p><strong>التاريخ والوقت:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                    <p><strong>المتصفح:</strong> ${navigator.userAgent}</p>
                </div>
            `;
        }

        // تشغيل الفحص التلقائي عند تحميل الصفحة
        window.onload = function() {
            checkCurrentUser();
            checkRealtimeData();
        };
    </script>
</body>
</html>
