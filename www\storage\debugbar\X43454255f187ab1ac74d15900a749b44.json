{"__meta": {"id": "X43454255f187ab1ac74d15900a749b44", "datetime": "2025-06-06 19:30:56", "utime": **********.970763, "method": "GET", "uri": "/roles/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238255.247311, "end": **********.970792, "duration": 1.7234809398651123, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1749238255.247311, "relative_start": 0, "end": **********.561354, "relative_end": **********.561354, "duration": 1.3140428066253662, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.561373, "relative_start": 1.3140618801116943, "end": **********.970795, "relative_end": 2.86102294921875e-06, "duration": 0.4094219207763672, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53613320, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.create", "param_count": null, "params": [], "start": **********.893487, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/role/create.blade.phprole.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Frole%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.create"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.928592, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.create", "controller": "App\\Http\\Controllers\\RoleController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=31\" onclick=\"\">app/Http/Controllers/RoleController.php:31-57</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02735, "accumulated_duration_str": "27.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.661289, "duration": 0.01418, "duration_str": "14.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 51.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.707614, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 51.846, "width_percent": 3.803}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7165198, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 55.649, "width_percent": 4.68}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7748969, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 60.329, "width_percent": 6.618}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.784414, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 66.947, "width_percent": 3.364}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\RoleController.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.801225, "duration": 0.00701, "duration_str": "7.01ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:45", "source": "app/Http/Controllers/RoleController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=45", "ajax": false, "filename": "RoleController.php", "line": "45"}, "connection": "ty", "start_percent": 70.311, "width_percent": 25.631}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.create", "file": "C:\\laragon\\www\\to\\resources\\views/role/create.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.904419, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 95.941, "width_percent": 4.059}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 510, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 513, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-645528137 data-indent-pad=\"  \"><span class=sf-dump-note>create role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645528137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.798012, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/create", "status_code": "<pre class=sf-dump id=sf-dump-705457379 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-705457379\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-136907010 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-136907010\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-79263609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-79263609\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1014406666 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238252862%7C39%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlluR3ZrTGhjOG1vbVJERGVaNjBreEE9PSIsInZhbHVlIjoiTERLQUVqODZWWFgwVVEvUExUd05aRU0yUUwrOUNZUEhaUFVFOUJVdjJqNEZmdnVKNTZWZWtMY0NrWmNiS3NQK0RFSVlHZCtYZ3E2N21RcEdPbkRYaG1sNEcrdVdlbHFyRlpETlgzWEpSdTBza0xOMWVaTnVoUGlaUjlua1VWdkUvTUp5NmdvazNTR2VMRDdwZHB6MVJ4Uzc3MEEyaE82T3BRS01RT1BEQ0hwVkNkOFVRd0duSUZZeERkd3lzQVNuSzZOODhxN0JYTnovTU9oZU02ejFMcTRFWEVQWWhLV095Vy9PQ0UvR3BoZHdjWmIrcTlEQlZ4bE1ycFJyK3htejRDbnFyOEk3Y2NVVGNGQUhvV1Zoc3RNVHUzTUYwQVRjM0tGbHdHWHdHb3JGZlAzTU9NZ29uRW43Z01PMHU2R0kxSGFiR1paekwrVjMwN3dQdm0zVXlIOWpXYkJydnNFaERsb3A1ZnVBMjdROVloaWRUbmF2ZE9pV2t0QmJxVGY4L3FkS1E3Umc0U3BnZnZpL25XYUtJSDluL0tkKzJ0a3QweXNQc1VEamxabVJyQ0ZtbCtRTCs5TTQ0WEs3d2V4VGxLemRQUmZ0bHpkb20rejZIQURMRXpTOW5ZT01zdUswZmthU0J3MVZsMm9sbXpRWlpqTm9lM1FtaXZZaldNV0oiLCJtYWMiOiI4MmQ0YzYyMDg5YTJhNWMxODQyYjIwYjAzMzNlN2U5OWU3YzU0ZTdjOTU5M2MyMDZmZGNhOWY5NDMxNzVlMWU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndkZEQ4V0tId2xBbjBXQlpoWTRoNVE9PSIsInZhbHVlIjoidThCOEN1a1RkbWw4Unp4Nk44ODFScXpBZ1N1NlJ4Z1lTazJ1YXNhZzU0ZjRHenI5RlIyaFhKUDBzdk12SVVjUTdKekV6Q2pWU0VxVGxOZnZQUTIxcUJMR3dZd2grdTJjbDdIYnR6QlhuT21FQ3Bnakh6ejA1RURscG0yTDdEM1JpenB6dU1RbHNvemhJdUxFMGlUbFRXRzFpUUNhdFYraXNwQmxmcTZWbU1ybGJRNzZiLzdlRVgzM1AzQnhpckgvbzVuazNZdExDRk9USmxxSVJxWVNDbFhLbWxSVi9DeGFlWlVQQ0dTMm1DM1BqQ3Jia3RUNmk0a0pOb2p2c0l4eFhITTdBeEdSOUhRL3lhZ0dsNXFENGNmcTNqZWl4b2RKZlkwbHR6R3gzQnNuOWZuTHFycGhEYWxvd2VZYUQva3lpMFhDQ1I0SEF0ZEgxQlNOZmhqZzJCVFpiQWV2MFlaWVVGaUlwUjhKM1NYMlZ5THIxNmRXcXdOdG5mL1E0RC8xSXI4ZjZIWkVkWWd0aTBEM3VFT0NVN2ZvY3BsTWJtMmUyeEp0UFRsb3VGaWs0dXRHM1JNQWErQkYxcUlRY05NMTF4S3dZTnNpOUNtTDhiOEI1WjdOcUhXUjRyQmZ4bVNJZXpJNTV6UnBQMm5aS3VFWi8rd1M5YUZTM1VrNlQvYzQiLCJtYWMiOiJiMzA3NWFlYjVmZTkwMGI4M2FhMWQ3OTU5ZjUyNGQ1M2ZmMzBkNjA0MmU0MzY0ZWYyNDViMzcyNzRjMmViYWRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014406666\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1492777729 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492777729\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:30:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktpdGNHTFVXMkJRNHRYQmwxMUJUMmc9PSIsInZhbHVlIjoiOCtUd2JvSlVBZVJpaUZ2eS9hZ2VXRGVKemwxL3BXM05xVXdsS0wraVZWaEh3M25mT2lvS0pSVUxrSFZOb3lkZng5dmU0R0lMNUh4QXhpMlJ1NlZOWDluWVhNbWtIcG94NWJXcDRYMk1NMXZJUjRzRFl0YUlaNThpaGdYekdsZzgyd1c1d2prV3J4TWw1dWN1R25qN0V6WTZKOHRxZDBpUW40WlBOZHRBdGRJN2U3UFdxL2dEb3lsclpZNFY4WS95MkR1cEFoRHNxQTh3ZzhkQ3lEY1RhMlJOTW81RDZrcUpSWE84VHl4cmlsUkp2U0xMZ05kWmt3eTY5SW43YWNmQ0t6dGsyUkpPQVc4VHJ2YUtGV3p1VGZadEFVOUk3QUxmYWJIYXdXVFBtVWViSjcwaFpYMENQb0tLVk8rOC95WTlCSjdDZy83djlKbERNSUNWeTBleG1Nd2xkY3dtcEZWMFhjemkzOTR0K3J5TmVjRUFhZng4RWxFZG9HWDN3eEtzbmtHemxyM1czcy9Pd0tQT1EwTGxSZ2VTc0ltMWsrTE1YbkZha0FDRDJmbCtpbnAyNTQ2dlFsQUpaUzRuL1pPVzhSM1ppY2IweTV0blQzckl4bVRNNXI3L0lCUzVzS1J0Z2o5bUVXRk5vWmlmaVA4b2hQNi9DZHFTeSs3L3Jnck0iLCJtYWMiOiI4OWNmMmQzYjY2YjhkM2I2OWEzNjhkNjM0ZDE4MTE3ZjI5MjY4NzVkOGI5YTg3MThhY2RkZDNlNDY2NzkzNGVmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:30:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRsTE50Y01oeXEvZXZQSG8yYjFURkE9PSIsInZhbHVlIjoicU0wNTkyTGdYNXcvSk8rd0lodUQvSWxERnZlU082N3ZlTlIrd3JXU29qS1VET2FxV3ZSUkcyYkRSVGhSaEpmR0hIb1MzYlpIRkF2K2RjSWtPWWkxb0g2OHkwZjJPajBlaTM1QnlaemR5emx5cllXRFFSVDhhNTVaVk45ZnpwckNXZTBkN1RkdFZ0MnpidkRFWHljVmxBcVAxK0ZwZ2NGOTZ4UUFxUytJSEs0VFZMY3VZQ1BCd2x1MjNZUjJ6MGdua1dEZWgrb1I4RVVTY3duUXJhNWlnM0o4UHJIbisyeFdma2pvV1NKdEhSWVMzeE5PemlQV2paSU43WElMa2s2U2Noa0ZNN3NPZkdKa1R2dnNEaEtMdGkyejNKeFQ4WU1OeGd4ei8xdWdlbmNZOGhIQzNYSTY2N0NNVU9sMzF1UkE0RXkvZ2lOYnJUZEgwQk9ESWtIN056WkIrcWdmQlJBcDIzS0VjWlBDQ0t2N3lxTk0yL3E4K00yKzJLdW8yVE9JWVBjemdabE4yTnlPV0xRWTh6dFV6WFhsSyt1ajJ0VjF1b3V4ODZncVVWdmVLVzV0MUFBZHdna1JndUNWd1dUMUF1S2M1WDhWMTh4Sk1vV1JYQ0xGNHRxalVFWndoMVJvQjRBQW55NitSRGpvYzdsK0lvWWRTWjVBaGRmYm9CRmkiLCJtYWMiOiJjZTUxNjNiOWFiODU2NTJjNWVmZGIwNDI1Mjk1YzQ3NWMyY2EwOGYyNjQxOTQyNzY0NGJjN2I2YzBkZTZhZWRmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:30:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktpdGNHTFVXMkJRNHRYQmwxMUJUMmc9PSIsInZhbHVlIjoiOCtUd2JvSlVBZVJpaUZ2eS9hZ2VXRGVKemwxL3BXM05xVXdsS0wraVZWaEh3M25mT2lvS0pSVUxrSFZOb3lkZng5dmU0R0lMNUh4QXhpMlJ1NlZOWDluWVhNbWtIcG94NWJXcDRYMk1NMXZJUjRzRFl0YUlaNThpaGdYekdsZzgyd1c1d2prV3J4TWw1dWN1R25qN0V6WTZKOHRxZDBpUW40WlBOZHRBdGRJN2U3UFdxL2dEb3lsclpZNFY4WS95MkR1cEFoRHNxQTh3ZzhkQ3lEY1RhMlJOTW81RDZrcUpSWE84VHl4cmlsUkp2U0xMZ05kWmt3eTY5SW43YWNmQ0t6dGsyUkpPQVc4VHJ2YUtGV3p1VGZadEFVOUk3QUxmYWJIYXdXVFBtVWViSjcwaFpYMENQb0tLVk8rOC95WTlCSjdDZy83djlKbERNSUNWeTBleG1Nd2xkY3dtcEZWMFhjemkzOTR0K3J5TmVjRUFhZng4RWxFZG9HWDN3eEtzbmtHemxyM1czcy9Pd0tQT1EwTGxSZ2VTc0ltMWsrTE1YbkZha0FDRDJmbCtpbnAyNTQ2dlFsQUpaUzRuL1pPVzhSM1ppY2IweTV0blQzckl4bVRNNXI3L0lCUzVzS1J0Z2o5bUVXRk5vWmlmaVA4b2hQNi9DZHFTeSs3L3Jnck0iLCJtYWMiOiI4OWNmMmQzYjY2YjhkM2I2OWEzNjhkNjM0ZDE4MTE3ZjI5MjY4NzVkOGI5YTg3MThhY2RkZDNlNDY2NzkzNGVmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:30:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRsTE50Y01oeXEvZXZQSG8yYjFURkE9PSIsInZhbHVlIjoicU0wNTkyTGdYNXcvSk8rd0lodUQvSWxERnZlU082N3ZlTlIrd3JXU29qS1VET2FxV3ZSUkcyYkRSVGhSaEpmR0hIb1MzYlpIRkF2K2RjSWtPWWkxb0g2OHkwZjJPajBlaTM1QnlaemR5emx5cllXRFFSVDhhNTVaVk45ZnpwckNXZTBkN1RkdFZ0MnpidkRFWHljVmxBcVAxK0ZwZ2NGOTZ4UUFxUytJSEs0VFZMY3VZQ1BCd2x1MjNZUjJ6MGdua1dEZWgrb1I4RVVTY3duUXJhNWlnM0o4UHJIbisyeFdma2pvV1NKdEhSWVMzeE5PemlQV2paSU43WElMa2s2U2Noa0ZNN3NPZkdKa1R2dnNEaEtMdGkyejNKeFQ4WU1OeGd4ei8xdWdlbmNZOGhIQzNYSTY2N0NNVU9sMzF1UkE0RXkvZ2lOYnJUZEgwQk9ESWtIN056WkIrcWdmQlJBcDIzS0VjWlBDQ0t2N3lxTk0yL3E4K00yKzJLdW8yVE9JWVBjemdabE4yTnlPV0xRWTh6dFV6WFhsSyt1ajJ0VjF1b3V4ODZncVVWdmVLVzV0MUFBZHdna1JndUNWd1dUMUF1S2M1WDhWMTh4Sk1vV1JYQ0xGNHRxalVFWndoMVJvQjRBQW55NitSRGpvYzdsK0lvWWRTWjVBaGRmYm9CRmkiLCJtYWMiOiJjZTUxNjNiOWFiODU2NTJjNWVmZGIwNDI1Mjk1YzQ3NWMyY2EwOGYyNjQxOTQyNzY0NGJjN2I2YzBkZTZhZWRmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:30:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-660837436 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660837436\", {\"maxDepth\":0})</script>\n"}}