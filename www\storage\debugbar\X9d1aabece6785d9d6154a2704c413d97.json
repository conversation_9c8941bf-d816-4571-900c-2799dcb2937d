{"__meta": {"id": "X9d1aabece6785d9d6154a2704c413d97", "datetime": "2025-06-06 20:37:00", "utime": **********.827608, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.131959, "end": **********.827641, "duration": 1.****************, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": **********.131959, "relative_start": 0, "end": **********.626008, "relative_end": **********.626008, "duration": 1.***************, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.626028, "relative_start": 1.****************, "end": **********.827644, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01119, "accumulated_duration_str": "11.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7268872, "duration": 0.00817, "duration_str": "8.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.012}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.767914, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.012, "width_percent": 10.366}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8046012, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 83.378, "width_percent": 16.622}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242217668%7C8%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5KZE5jTXZkczVCbkFGVGpuNXhIa2c9PSIsInZhbHVlIjoiN1lQcmI2S21TMWJKMzFRdFRVYUpYNkxpUGRhWUZQaDlDckNtOGNXdUlIUFdMWlFkUkZCTUE1WXV4bUpnOCtKc040OW9QR3FZb2kva1hsQXpVcHQvZ1NCMXgxK1N4OWpnZGd3QTZCTHRTTFlrZUxiSS9LNHZDVE5TMTVUYXd2SndWME5jd2MrU0pXRVV1dkt2REpyUEdBbllWRnViYVFpNHpldHhhbjEzcm1SamVQTFAvczYxc0kzanZ6TjhsYTF3Qkt1V2VjQ2Q4eXpTR080OWhibnNuUlcwc25GMmkyQXlVYXYwNkdFdldEYnFSalFMVG0xaWRtSFBUZWhVQkYwSzJEZFA4TFBEWFFTazdHYm83SE9MMnFWaUJwN003bEE1dmJBN00xUGpsZXdEYWF3OFRYMnI4UGErK3g4V1VZQWtZSmh5aE5QRzB3Vjlta1FKbUxydDVqd1YxM25ONHhpdDJIR3NQZGZhSktlcm44NzgxKzFCWDRNVm5OM0lKYjBmYUVXbjkzdUFGdVZ0R0VKaWtpRDhHWm1DOWczbmIzOHh1MEJhczhVNk4wYzEyUVg0ZzZ6eUFtVUxnbDdhYUVZVGtzMmgwNDluOGxJSU41VEpNcldFaDRIRkRhTkwvTzJxTjFPSG12RE9uUzlYbXhMV0orZWk5OXhOM3JwcUNmK20iLCJtYWMiOiI0M2Y3ZTMwNDIwNmUzODc1MWY2ZjA2OThjNjhiYjFmYzZjYzliNGI1NzI4NWY1Nzk4ZjM1ZDZlOWY4YzYwNjMzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZ3OUN3WW1KanlRa1J3dTZhNFVSQXc9PSIsInZhbHVlIjoiWUdEbmljL3dzbHRkRENpKzdYVW5TOVlwUWhCL2o1Nmdlb2RCMEhibjNQVG5mblNGRjJ3L0tsbkJJdW4xY0o1aGlIWi9mQm5EakNjODFidmFuZHJ6K1p4WmNJOW8xU0hvUlR5b2tBaVZjd0R4bWdrV3dic3g2K09FN3p3KzdTd3dFcE1hT3VDMXF4S0dxYmgvbzR0ZkkvQ0JrSEpzaWVzaitKa0NMWWtDVFp3UEZvd24rY0QxQm91SmVzMldUT0xoaTZVYTR3eDFETzQvMVRMVU02RG1FOE1TZW1xb0QwMnU4a2I2RDExSEt1V0lhNVlqdHpmc1FPbjFNVVJRL29jZnUxaFpVWlEvL3Bvand5ZnFsMjhBWUYwU2toQVQ2VmRtQ21VaXBreWtYRFZUTzZRUm5BVnRXWEV3Z3RzZFRONTRFZmFVQUZ4UkNBQUFVR1dNK1U1RHpES2t1amhJVzVCejhhS1RhVkdjOWpsY3hTa25FMGFucnA5bUhpRm85elBzVW1kUjJCd0pqR0NWdk5wUzNJaTdTU0FJTitOUFlsWFVqeE1Dc1Q2NnJDb0JZTGhrVHBRaTlVV1F0bGVQSXM0NnhlRmpCWWdmU1dHMWp3a1JkVDJoeFBVTnAzaG5hR053V0FPcC9CdkxVSFZia3M2OFllYnNOdlF5cU9HTTd0UEQiLCJtYWMiOiI0MzQ0OGJmNDdmYTcxYWQ0ZWU2MGJiNzQ5MTJhYmRhODhkN2I1OWMzNzA3MjE3ZjAyYzVkYjc1ODJlYmRiMmExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-231816995 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aXiO7pRs3CsfapKKqE5850VIyuRIvpwAHRxwlR2S</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231816995\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1782231640 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:37:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdpOE5NMXpwajZ0Ky9ONmNGd1FTbUE9PSIsInZhbHVlIjoia1c2SVpHVVYydkZETU9oSDh0ZXJDdUFwNTdubEcxWlZhanFIVGdMTU95bWdGaDVWSWgwMkdTREEyTG1RMHBVNDgxZkJaRnJQSlZFOE5KL2VJYTVpQ1g3OHVaSnhENFUxSHdabnhUTXdNZWtzZERSSjYyQ2QwVTBPRkI4UW1qeVBrMXAzVldaSmlyRzJ5NlBMSWw5M1Y1SW1rOUJkRklUV3p1c2huWVV0aTdCTG15bXBGaVBwclhDQXFFWW5NazFlV0hmL1BzM2s3QThmdllVcEtlOWpKWTVqWjdPM05sNDJiclhOOW15NkY3a21rdkRlOU11SE5iaTJMSjRqdFEwV2RMRkw4Q0YrS3Nxc3p0WW1Ca1cxY2tFWXpjV1dqVTZaZmhXSnoxOGNHa1lZL3VpS3FoSUNZMnZ3dUZ0Z3pNVEdROHExMUJRVEFTUjJqNTdiWmlsY1BwdnJYTmdCeUpGMUZ1UVpoVG0wb21NOVhLUzFoalZiazZJYWNncFVGc1lxQ2orSERHb09NZGlic0tneHRsMkwwTlJGajJRQWROWGt3SjFMNU9jU1pmRlBuZEpHdDAvUzJFazAxcTYxaXJkKzZEQjVvam9TK1hiRCs2YjZuekowVVBVeHlzSnJJME9URU5SWmNpdzh2b1NGUmRDZjVtZkloRFFwa1JROFZaaE4iLCJtYWMiOiI4OTJmOGNkMzAxMWRlZjZlNzA4ZTliZGYwYTNlMzMwODYwMDEzNDRmODg1ZjkzYjMzN2QzZmQyZGVkNjI2OGJkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InhWZkl6dlAvM3NRVUlZOVI1Mkx5YkE9PSIsInZhbHVlIjoidTVLalprbjhYV2hML0dRZjlPRWRmUGpxV3haTndqMEdpdzcrMjZoSUJDK3phTWhid0RCN1ZETXdydjI1L3o2SVlRTGgvM0lCRWNRSUNKUFFzR3FRb2FSTWF0eE00TncrblpqOGJHSEprWlR4eGp5NlpHbVQyMDE3NkEva0pRSkI3bUZpNGI3U3J2UkZsa0FFL0xmbkRIR1N4OFR5dUtWZFJxWUpkTUZXVmRsd2REelBIaDNPbC9pRm5vaTcyYzluaFVSRGpmbDFJRWRhUVJpdXdCSEovM2hCTzZoU1Z4czZCTHBUdldzMkVVOS9tcnl1YWYwT1hWK0ZHRWs2aS9tQ29MekQzTTBTWVRROWVHR0JPV3ZBNXdIY1ZFbzZwS3ZIYTJ5TVhxMmMrc3JWTWNSSmJvVTBNUUMwRTl3czZ2a21QTGdmc215N0o1THh0ODZMVXNUSVJ2TTM4dk5GQjhsTTFaSENaK1BjRG93Vlg4L0oyOVl1dUxOcnpGT3h2Ym0rVlJ4SldNN0dwWXBhNUE5aW00aHlick1OYzZud2dmWDlhSGl3T2pvbk1qc05Ma2V1bGpFTGlkNEtnSEdGMWxtclo4YjhlZTRjZXhEMUxUUnFZdklTZTh2NDg0dlZZTmNLeS83UDVXQ0pCbHVsVGttQWJQaFVVd2tsY0xsTzBLSVAiLCJtYWMiOiJmZTMwOGJhOTNiZThjZWMyYTQzNDdkNTE4ZjM2ZDgxMmM5NGIwMjdjYTY0MTdiYjVjNDZjYWIzY2JhOGEwOTBlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdpOE5NMXpwajZ0Ky9ONmNGd1FTbUE9PSIsInZhbHVlIjoia1c2SVpHVVYydkZETU9oSDh0ZXJDdUFwNTdubEcxWlZhanFIVGdMTU95bWdGaDVWSWgwMkdTREEyTG1RMHBVNDgxZkJaRnJQSlZFOE5KL2VJYTVpQ1g3OHVaSnhENFUxSHdabnhUTXdNZWtzZERSSjYyQ2QwVTBPRkI4UW1qeVBrMXAzVldaSmlyRzJ5NlBMSWw5M1Y1SW1rOUJkRklUV3p1c2huWVV0aTdCTG15bXBGaVBwclhDQXFFWW5NazFlV0hmL1BzM2s3QThmdllVcEtlOWpKWTVqWjdPM05sNDJiclhOOW15NkY3a21rdkRlOU11SE5iaTJMSjRqdFEwV2RMRkw4Q0YrS3Nxc3p0WW1Ca1cxY2tFWXpjV1dqVTZaZmhXSnoxOGNHa1lZL3VpS3FoSUNZMnZ3dUZ0Z3pNVEdROHExMUJRVEFTUjJqNTdiWmlsY1BwdnJYTmdCeUpGMUZ1UVpoVG0wb21NOVhLUzFoalZiazZJYWNncFVGc1lxQ2orSERHb09NZGlic0tneHRsMkwwTlJGajJRQWROWGt3SjFMNU9jU1pmRlBuZEpHdDAvUzJFazAxcTYxaXJkKzZEQjVvam9TK1hiRCs2YjZuekowVVBVeHlzSnJJME9URU5SWmNpdzh2b1NGUmRDZjVtZkloRFFwa1JROFZaaE4iLCJtYWMiOiI4OTJmOGNkMzAxMWRlZjZlNzA4ZTliZGYwYTNlMzMwODYwMDEzNDRmODg1ZjkzYjMzN2QzZmQyZGVkNjI2OGJkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InhWZkl6dlAvM3NRVUlZOVI1Mkx5YkE9PSIsInZhbHVlIjoidTVLalprbjhYV2hML0dRZjlPRWRmUGpxV3haTndqMEdpdzcrMjZoSUJDK3phTWhid0RCN1ZETXdydjI1L3o2SVlRTGgvM0lCRWNRSUNKUFFzR3FRb2FSTWF0eE00TncrblpqOGJHSEprWlR4eGp5NlpHbVQyMDE3NkEva0pRSkI3bUZpNGI3U3J2UkZsa0FFL0xmbkRIR1N4OFR5dUtWZFJxWUpkTUZXVmRsd2REelBIaDNPbC9pRm5vaTcyYzluaFVSRGpmbDFJRWRhUVJpdXdCSEovM2hCTzZoU1Z4czZCTHBUdldzMkVVOS9tcnl1YWYwT1hWK0ZHRWs2aS9tQ29MekQzTTBTWVRROWVHR0JPV3ZBNXdIY1ZFbzZwS3ZIYTJ5TVhxMmMrc3JWTWNSSmJvVTBNUUMwRTl3czZ2a21QTGdmc215N0o1THh0ODZMVXNUSVJ2TTM4dk5GQjhsTTFaSENaK1BjRG93Vlg4L0oyOVl1dUxOcnpGT3h2Ym0rVlJ4SldNN0dwWXBhNUE5aW00aHlick1OYzZud2dmWDlhSGl3T2pvbk1qc05Ma2V1bGpFTGlkNEtnSEdGMWxtclo4YjhlZTRjZXhEMUxUUnFZdklTZTh2NDg0dlZZTmNLeS83UDVXQ0pCbHVsVGttQWJQaFVVd2tsY0xsTzBLSVAiLCJtYWMiOiJmZTMwOGJhOTNiZThjZWMyYTQzNDdkNTE4ZjM2ZDgxMmM5NGIwMjdjYTY0MTdiYjVjNDZjYWIzY2JhOGEwOTBlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782231640\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-699800359 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-699800359\", {\"maxDepth\":0})</script>\n"}}