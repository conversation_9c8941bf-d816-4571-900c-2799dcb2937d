{"__meta": {"id": "X3d9a633fa1b97cdc87510424d950954c", "datetime": "2025-06-06 19:28:58", "utime": **********.976706, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238137.466103, "end": **********.976732, "duration": 1.5106289386749268, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1749238137.466103, "relative_start": 0, "end": **********.782495, "relative_end": **********.782495, "duration": 1.316391944885254, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.782517, "relative_start": 1.3164138793945312, "end": **********.976735, "relative_end": 3.0994415283203125e-06, "duration": 0.19421815872192383, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02905, "accumulated_duration_str": "29.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.870732, "duration": 0.0254, "duration_str": "25.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.435}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.929552, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.435, "width_percent": 4.303}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.937241, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 91.738, "width_percent": 3.614}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.955816, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.353, "width_percent": 4.647}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1840058191 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1840058191\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1226094517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1226094517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1392322036 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392322036\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238091179%7C32%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImwraHBYOHFWYzk0cmVhSWdvSm8rZWc9PSIsInZhbHVlIjoiNHhLcXdpTTg4cGkrNDNQKzlnWVRsL2J6TloxTGYwYzdFdXBFWGpzKzdmNFFmRzd0L3FRTitPbkU5dVlldW1SRkt6WUIva1I2VnpJM3hSVDR5WVIrcmpnZ2k5Wk5odXBEY2tRVTRLZHdnOVg4Y1ovZ01HbjRyKzVJM3VJRmowZlRQcDA1QlM5NmlIeFFQVDhOMk1GVDMzTFh1VVN4VGZVcUtwVHBZK0ZjSGtyaG5odmFWaUJLUndLL1ZEMTJiMmdhOWtJMUdrVWFtb0JsWER1VU5zYTcyNWJ3OThxS3liUUxvUndpSUxNTkE2d2hmakpzUCtKd3Y1QjNJOUhEUmQzU2xsbFlaNXlwb2RpMnFZZUtFMGZQNHJweVQ0ekxLUGhwbGNSaXhTRThRdmluTjVwWUZxaENtR0xDWVJseEpVTkZETjhLK1haR1Rqak5Ec0l1YXBZTnA1Z1UzbC9NQWgzeXVucVN3NEdaRks5TnRac1o2TU92b1FqUmpZalI0WlRPaHlFMVdNSE9YQWN0aVVXbmp2Um5FckZaY0JWUGVadm9XNlo1WDB5bHJ5alF1STRrejBhTmtwamlSZzVLZG4zN0V3aGZQdVpNYXVYell6RW5WZjhZZGN6cmJQc0N0c3kwK2sxd2pHS2dyWE5QUHZ1QWIzaTlkMW5RMEsxaG91VFAiLCJtYWMiOiJiYmM1M2UwNGNjZTQxMWM3NDEzNmRjNDA0MjllOTYwZjYwZWQ1MGIyZTU4MzBiYzBiOWE4YTdlNDdlNDMyMzVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkkrcENsdjZETjFRS1BFd2xkOGp4bEE9PSIsInZhbHVlIjoiOWpCU1ducmQzdzFtT2QxL2lOQlVOUkhCQkNiUjg1Um5sWU5URHFUNTZHcUdBdXNMQ0FzN2hHMzBsWkdhWXduZXRUV2RMUXFCa2F6cGdPcEhtNFZZb25EYk13SGV6dnFTTDBTdnNuTDhHQWtJSHR4OGNiRi9FdThUTnJKT3VDZGpjelhLNnhxM1BuMFBHQlB3Y1VDZEJpMys1cE0yakp5UjdtbUdOaHNQV3BJYmZidUtDNzVUMDduQzJRLy9melF6OTdRVXkrY3l1YXpFWWVKemtFSmNnZTd3OGszcThXR1E1UERpSVhjMkpOdllMT3h1OFpVa2wvd0JDUVg0QVExb3J4anpVMld4SjV2UEN1MGUyVU1HK3FMVzcrZmVmZEY3bWQyOE1uMnNZN1V5RDU3ZDVVV2VSeEdoc0VHRFRINndzZGNOQjN0NzVKVm9PcWU0b2x6ZHZiY1VnUndETndjMlhFL3ZyNG90WjMrS3IxbFVxNkV5akM3R2xXcUVFQ0ZQZjA1NGxBcHpIYmFhemRLNTFERWRYZGNPcFFMRXF1R2JqRlM5cVlrQldlN2ZrRUVjTzBaa1dPRHE4THNwSGpjN2QxaytjRjhXbWlKNWN3dERaTGpPdWRUQVdlZHMxQzltWnRWdk5QYlBjMkxYeStUMnMrWmNwekZkMWVkUVZpRFIiLCJtYWMiOiJlMTU2OGU1ZGE3NTc5MjIzOWI5YzQxNGM4MzBlNDMzM2Q0NzJmNGI4ZTU1MmEzZWMyMDJlMGU2YjlkOTA5NmQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:28:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjloWE9JV2o5c21Tek04d0d5bkJxRUE9PSIsInZhbHVlIjoieGZacWNQWXdNNHNHWkhFR3VOQjhwWTREKzcza0s2RThPY3dkSFZWRDdTRlFscWpmYnZwY0tYY3dRV3FmWnc0MFBUQ0J2dy9XNlBmNTRxdFFMc3kyUTlDV3l3dFA3SHZyRVpXZkZWRmpQb2EyUmZYRk9BbER6dm4rZFFPamxySWUzU2JUeTF4WVI0Tk1WNDRLZEVPN09PQnJseTdtSzFTSDdHQlp2ZFVxWnNaNjBzSUhEeWNraEk5U21BenUzV2QyOXVlVXNIajFQNVhZNi9NQnJkZGdncTYxTHNoVVdDQ3BhR1F1K1d0V1ZQUFI2SjMycFZKVERsdVgzelovRE9IVkFwcGJOOWd1ZmZ3dGlVazhHNjZEV0RlTkpBZ0xLVENJdlpzK1dOQzh4K2tib3FEOHFVTGlLeGFNQms2am16bjMxQ25tenNwTGEyRUJtTU5POXVLZFQvcytaNUl5NDVqdzVMT2J6SFY3S0FiUU54YjBpbU5yUnlONk1tdnZzRkZSczluczI5NStXb0lQSlllYXQ5SkEwSEdPQ01UTEEvQmdIZGhKQUIxT25VQ1E2WThyanV2TUNRK2RrQmRSVURMZDlzam1XNDJNd2pHUDlCVHhqa2tkUlp3eVJOT3RvcHY2UUdETTRvM21RL3pWNHArUlhwMlFoR2wybmVQdGE5dEIiLCJtYWMiOiI1NWRmNjQ3OWYxNWZmNzYxN2Q0ODZjMjA5N2UzOGY3MjExZjYyYWYwODAwODQzOTc4NmU4NThjYTEyN2NmODMyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:28:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9reXVxTERRZnRsbXg1NityajJvelE9PSIsInZhbHVlIjoidTdDdmFyTHVic2FxSTJHSEhORi90b3JWVXE2UW9vK2JmM3Y4VXowdlhvcVdyYkFNTHF2eG12Mlc1ZjN0T0o2TFdyY0tEemJFTzEzMTJmWWZtdVdZV0ErbERoMkN5OHF4YzBqdnFaYVpKSVBpV0Z0N1pRYzhkcWpnU2xTdWZYSjRML3JaUmVZVENheVR6Wm9hc2Uybk5oczkrSWpySnUvNTVDUDBIK0Qwc2MyQnNBblVoUEo1TVVXTW5oaEY2NXoweUxQbE4wOXB4U0ZLajRzV2N0UlcxSWdEYnlTRERmVmNPcEptSDBlNzAyK0txUDVJelpBdGhpQnQxbnhRVC95RWo5SktPRFhPK0Urb21oeml0VVVZTkFKMEhUZU5TVTFZTlZSaUhFYk9JNEN6SnJuQlFCRktJOEpOWFlJb0hkK0I2Vzd6c1lJMDU0RTcrT1UrV3J2U3hsZzR5emx1d2UrMlh0bjZFSEsxdFJuc3Zrejg0RzI0UC9xV2t4cjlwRHJoYmRrODYvTDV0VS9PSkdqeDZVZEhRZ2xkM1BRTmlNZmNWdWhHeWxSVWFYSURKdllPQ0REU0hEdDVsU3ZMakxySEUvajI3cDcwcHJadVdGeU9lMWFjcXNhRld1dUhUVjVkKytSU3NtYnpJMnhuclBnNGdxbm9RRkluSUlrS04zTTkiLCJtYWMiOiI1ZjZlM2ZmMWZjMjY1YjNmYTE2YjVkZTdmZTljMWU4ZDRmNjFkYTk1YjZmOWNjNjVhNmNkZjE4OGRlMmEwNWE5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:28:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjloWE9JV2o5c21Tek04d0d5bkJxRUE9PSIsInZhbHVlIjoieGZacWNQWXdNNHNHWkhFR3VOQjhwWTREKzcza0s2RThPY3dkSFZWRDdTRlFscWpmYnZwY0tYY3dRV3FmWnc0MFBUQ0J2dy9XNlBmNTRxdFFMc3kyUTlDV3l3dFA3SHZyRVpXZkZWRmpQb2EyUmZYRk9BbER6dm4rZFFPamxySWUzU2JUeTF4WVI0Tk1WNDRLZEVPN09PQnJseTdtSzFTSDdHQlp2ZFVxWnNaNjBzSUhEeWNraEk5U21BenUzV2QyOXVlVXNIajFQNVhZNi9NQnJkZGdncTYxTHNoVVdDQ3BhR1F1K1d0V1ZQUFI2SjMycFZKVERsdVgzelovRE9IVkFwcGJOOWd1ZmZ3dGlVazhHNjZEV0RlTkpBZ0xLVENJdlpzK1dOQzh4K2tib3FEOHFVTGlLeGFNQms2am16bjMxQ25tenNwTGEyRUJtTU5POXVLZFQvcytaNUl5NDVqdzVMT2J6SFY3S0FiUU54YjBpbU5yUnlONk1tdnZzRkZSczluczI5NStXb0lQSlllYXQ5SkEwSEdPQ01UTEEvQmdIZGhKQUIxT25VQ1E2WThyanV2TUNRK2RrQmRSVURMZDlzam1XNDJNd2pHUDlCVHhqa2tkUlp3eVJOT3RvcHY2UUdETTRvM21RL3pWNHArUlhwMlFoR2wybmVQdGE5dEIiLCJtYWMiOiI1NWRmNjQ3OWYxNWZmNzYxN2Q0ODZjMjA5N2UzOGY3MjExZjYyYWYwODAwODQzOTc4NmU4NThjYTEyN2NmODMyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:28:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9reXVxTERRZnRsbXg1NityajJvelE9PSIsInZhbHVlIjoidTdDdmFyTHVic2FxSTJHSEhORi90b3JWVXE2UW9vK2JmM3Y4VXowdlhvcVdyYkFNTHF2eG12Mlc1ZjN0T0o2TFdyY0tEemJFTzEzMTJmWWZtdVdZV0ErbERoMkN5OHF4YzBqdnFaYVpKSVBpV0Z0N1pRYzhkcWpnU2xTdWZYSjRML3JaUmVZVENheVR6Wm9hc2Uybk5oczkrSWpySnUvNTVDUDBIK0Qwc2MyQnNBblVoUEo1TVVXTW5oaEY2NXoweUxQbE4wOXB4U0ZLajRzV2N0UlcxSWdEYnlTRERmVmNPcEptSDBlNzAyK0txUDVJelpBdGhpQnQxbnhRVC95RWo5SktPRFhPK0Urb21oeml0VVVZTkFKMEhUZU5TVTFZTlZSaUhFYk9JNEN6SnJuQlFCRktJOEpOWFlJb0hkK0I2Vzd6c1lJMDU0RTcrT1UrV3J2U3hsZzR5emx1d2UrMlh0bjZFSEsxdFJuc3Zrejg0RzI0UC9xV2t4cjlwRHJoYmRrODYvTDV0VS9PSkdqeDZVZEhRZ2xkM1BRTmlNZmNWdWhHeWxSVWFYSURKdllPQ0REU0hEdDVsU3ZMakxySEUvajI3cDcwcHJadVdGeU9lMWFjcXNhRld1dUhUVjVkKytSU3NtYnpJMnhuclBnNGdxbm9RRkluSUlrS04zTTkiLCJtYWMiOiI1ZjZlM2ZmMWZjMjY1YjNmYTE2YjVkZTdmZTljMWU4ZDRmNjFkYTk1YjZmOWNjNjVhNmNkZjE4OGRlMmEwNWE5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:28:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}