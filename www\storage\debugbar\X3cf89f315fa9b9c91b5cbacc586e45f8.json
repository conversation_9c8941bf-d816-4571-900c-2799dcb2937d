{"__meta": {"id": "X3cf89f315fa9b9c91b5cbacc586e45f8", "datetime": "2025-06-06 19:35:17", "utime": 1749238517.060831, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238515.4901, "end": 1749238517.060865, "duration": 1.5707650184631348, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1749238515.4901, "relative_start": 0, "end": **********.879725, "relative_end": **********.879725, "duration": 1.389625072479248, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.879745, "relative_start": 1.3896450996398926, "end": 1749238517.060869, "relative_end": 4.0531158447265625e-06, "duration": 0.18112397193908691, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44778264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0087, "accumulated_duration_str": "8.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.972371, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.724}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749238517.00807, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.724, "width_percent": 10.575}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749238517.016299, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 72.299, "width_percent": 12.299}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749238517.033914, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.598, "width_percent": 15.402}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-534140075 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-534140075\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1262544786 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1262544786\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1560438149 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560438149\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1034428134 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238482606%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9SaG1TeWFZeEUwWDBhdWNQcTBBS1E9PSIsInZhbHVlIjoiRk5KN1lEcStuZ0pVMnRVTkIxbHhEdWY1L3E3bjVtZmxFbmhuZkFvQS83cGNrQTFFQ3YyeDI4Z1FiUElxL1BtdVF0dXY5TjRpYW5CTW5KNitaaHhzOVhPZGUzSVBDUWMwblBqYnh3SHVQOUhWZndMeFNCN01QMlI0YjRlRGhLOWpMVU1lWDZuWWF3WnIxMzRJWWZWenF4dGh5dkhMenFFcUJHZnZiRG5ZaWU1TVNIbXhFUVhxQVVtKy9wOXNMMGhGOHdBR3JXRXA4bFlkYWY2MnM3MGZBRnNLb0lEQnJTQSt3eHRrdDlLbC9oaXBlUDAyMEJmd05neUw4UHBnUVdZL1RTSDdwVTF4NktKMlhyd1J2SlZTM05JSWpTZnJpQThnSzVJSVVheEl2WTZYWkxZU3FpZTZRWkZqeVdScWxZSytVVW00RStRUEFxc3l4Nlh0MU9ueU92MHVTRmMzZEVKK1RWRmFvUHBCOE56RnBnWXJzZlhRdit6eGZLWENMenNPQ0lubFdFeWZUZUFvTXNPWjlsL2llNHFsZ3BTWnRZWW5ISHFWbDg2ZW53ZnpMVStPL09XYmRxU0FzMTBSaWtPaXF6TDNydnBIem5kZTRQOENwWEVVSFdDTG5adWxMU1NKZDhkcU1aL2tTNzB4TG5lTUJCL3VRMmNOYUM1WGhGTGEiLCJtYWMiOiIwMGViYmMyNzNjY2FjNTE5ODZhNGE5OGVhMDdlZGJlODA5MDhmZWRmYTg0YzNhYWIxM2U4NmFlMzMwMjE4MjNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9yZjhGT2FZMVI1TVNYc0lwN3RrR0E9PSIsInZhbHVlIjoiZ0dTT3A1c3NRdGxkNkFyV1I0RkVpRHJwMlNOcHRlWnJmdFJlWjI3OTdtTXlXSW92Q3BNL1F3RVIyeXRtQmQvZG81SUZTT3o4L2ZzMklzTHF1d1JVcjJxSFBodTJ4eHZYbXk4N05FYnZyYzlpK2N1NjgrS1BjTmw0OFNJeU1uOUlkTFlIU05pditXNm9NRDBxWGFycjBKSTFEQUJxSlU2bVA3WENaZVNRZTVtVUl6cENCTW1tNXBHSi9RRUJCNmg0ZHJNYVp3cGdOU0h2Y1BjQ2RYNTlYUm1EN3dSWGNmQTEydE5wVzZIbXlucWs4Q0VhU09tZXV0cCtpdEgvTG9HcmV1OEdCQlN4bjRtRnJEU1g5K3dvTHVJQU83ZkRYdDlDSEduYTU3M2JYOXBaZ3NEOHExK2s1cUFjZXZwUm9hSjR0ZXd5amZRYU4yb1VyK0JqVkgvdEJ5c2dxVGNDV09pR0F5eWs0Sk5EUkdpaWMydExTTVV0cktkSWdKNFJtWVhhR2xETGhFbndMaVpkODVzc2RYZ0VHalpqSlZUT05QVFM0WUI5OHRHNjdlYU15Mk5aM2Y3a2dFVTBRWmkyVlkxTlJhTWxDWHVnTTluT1daOERqbnhvZTE2N21tZVhjR3d1S2xEYTlJSmNUWUx2ajZZbmtEYmU1d2pacHlwcS96a1AiLCJtYWMiOiIwNWYzOTJhOTEwODBkZGU3ZDZiNzFjZWY3MmZlOGViYTE0ZDFlMjI1NzUyMmI4ZGE0MTI0ZjE5OTlhMTlmOTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034428134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1461800295 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461800295\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-413798078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:35:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllVc2tiZEFSTHdQTWV1eHdrZ3A3Y0E9PSIsInZhbHVlIjoiL1I3d0QwZ1pPUFNNSngxOVVLenVaaDhESHpialF2ZHhyNWR0VFczdHRpSU9MdHJDRWE2ZDBZYnlnMndtQ2VaWW5HWnZ4UlR6bDVpZFJGRlI3Tmt2MVdFQmFYeldtM1F3ZGV5a0FpQmFNM05KUkhvaDdLNWtVcktjTXNzUTZIQ3FmM0t3bDFBUGJja3JibytEU1Iyb1pEeUlVQU5lbis3aW5SeVMzOUZOaEsyV3JxWmVtSHlBeXUranpmb092ajdkb0hxa3c0M3lBeFozNUhnanlZQkhlaGk0aEFUMEJmN3luVUZXVHdtQ0NZQkVhY2Y4V0pqdkx4Z3JpdDVkQlkvYzlDRHFXWVhMTDlKZ0kzRVdudGFzUzNXZDR4V0ZRdklvVjJ4M3dSNUZ1S1pPa3RkREJHRnJMMWxLZUN3UzlBQS9ZYzFqL2NXSHM3N1lObDFZcmxJUzFEVVRvUU5wUHUxbmo5b2xDRXBzTGFVUkRLWVZxVEVkOThMYU9JUzIxTzlGTkc4U3JSL2F2bFNXeU9qYXEwQTl0d2hMU08rcFQ1MkxSOUZHVm9PMXN4OExpV3ZrTFFyQ0NnYmJ2SXd6NzVGK0pRbncrVVlGY2tQbHB2T2MzZXdRLzdGTDlEdlVjakdOTnRVK21jajFsYzhzdm5iTVU4aEt1eWV4akhRVFIwQlgiLCJtYWMiOiIwYzQxYmVjMWQxMTNiNDNiNjhhZjc4NDg0NDQ2YjQxMWIwNTExM2UwODRkNjY4YjA0MzFjYmE3NDFkZWM5YmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik51OGJiSXJyWVRNY3RjcDRoWXRkNVE9PSIsInZhbHVlIjoiS0dOcmdDUkxhNG5TRG5CVWhOOERMSG0xc3B1YjVjbjBheWlnd0RVUXVWRHNocFRTcHloRzBlNDI3dmVxeC84aVZUeXNpMm5QUWFJU09LaVdLcDhvdjhRTlBMaTJQOGN4WlJBMlFFa3RtOHRIUEY3Y1E2WFhQWnJ3S3orT2tiNlN5YzNtbkNSV3FoMi9NNndXVXh5SVZ3UjJPSlJyeEVRVW5DT21GRHhFTzZVV1JwWDlSeERPc2ZPK2tIWTd6SFZMTnBoREE3ZlJFOTBIUWl6ZDVpdGVUTEZZb0R6aEZHcVBtbUZxdUczK3I0cnBmSkRKZHNubVBaVFlCVml6ZnZBb0dJUTJ1MDBrZVRFaWRwYktBeng5TDNOci9adUtzOExhRWdaMUVDWUxMcFJuSlVzWTNPTjZRU1MzTkpSZFRxbjcwZ2phNUtHQllRbkZiUFlXcm9OVlpUY05xa3p6cDB6b1FQUFF5bHdHd0JKRTZ6bnZmYUp4QzErMlUzRFFtOVg1Z3ppNTNDYitTUWRtOUtYMU1UWGs3M2p5NjZkMnJmZXE2aXdUbnNFOENIVEx6Yk1LandqWXczekdFaTFyTWFZT0d3aDk0VytpcUV5TzR5VVlWaktMdkZDQW94VWZhOGpIeCttdE1BSGdvenVCNTVhc0VhWXF4cnliZldGNlE0UlIiLCJtYWMiOiI1NjkwZDY3NDFjMmZhMTVjYTZhNzNmNzJhNTI4ZTdiYzIxODA4ZWY1NTU0NzhiOGNkMmJiOTc4ZTQ0ZWQ5NGRkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllVc2tiZEFSTHdQTWV1eHdrZ3A3Y0E9PSIsInZhbHVlIjoiL1I3d0QwZ1pPUFNNSngxOVVLenVaaDhESHpialF2ZHhyNWR0VFczdHRpSU9MdHJDRWE2ZDBZYnlnMndtQ2VaWW5HWnZ4UlR6bDVpZFJGRlI3Tmt2MVdFQmFYeldtM1F3ZGV5a0FpQmFNM05KUkhvaDdLNWtVcktjTXNzUTZIQ3FmM0t3bDFBUGJja3JibytEU1Iyb1pEeUlVQU5lbis3aW5SeVMzOUZOaEsyV3JxWmVtSHlBeXUranpmb092ajdkb0hxa3c0M3lBeFozNUhnanlZQkhlaGk0aEFUMEJmN3luVUZXVHdtQ0NZQkVhY2Y4V0pqdkx4Z3JpdDVkQlkvYzlDRHFXWVhMTDlKZ0kzRVdudGFzUzNXZDR4V0ZRdklvVjJ4M3dSNUZ1S1pPa3RkREJHRnJMMWxLZUN3UzlBQS9ZYzFqL2NXSHM3N1lObDFZcmxJUzFEVVRvUU5wUHUxbmo5b2xDRXBzTGFVUkRLWVZxVEVkOThMYU9JUzIxTzlGTkc4U3JSL2F2bFNXeU9qYXEwQTl0d2hMU08rcFQ1MkxSOUZHVm9PMXN4OExpV3ZrTFFyQ0NnYmJ2SXd6NzVGK0pRbncrVVlGY2tQbHB2T2MzZXdRLzdGTDlEdlVjakdOTnRVK21jajFsYzhzdm5iTVU4aEt1eWV4akhRVFIwQlgiLCJtYWMiOiIwYzQxYmVjMWQxMTNiNDNiNjhhZjc4NDg0NDQ2YjQxMWIwNTExM2UwODRkNjY4YjA0MzFjYmE3NDFkZWM5YmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik51OGJiSXJyWVRNY3RjcDRoWXRkNVE9PSIsInZhbHVlIjoiS0dOcmdDUkxhNG5TRG5CVWhOOERMSG0xc3B1YjVjbjBheWlnd0RVUXVWRHNocFRTcHloRzBlNDI3dmVxeC84aVZUeXNpMm5QUWFJU09LaVdLcDhvdjhRTlBMaTJQOGN4WlJBMlFFa3RtOHRIUEY3Y1E2WFhQWnJ3S3orT2tiNlN5YzNtbkNSV3FoMi9NNndXVXh5SVZ3UjJPSlJyeEVRVW5DT21GRHhFTzZVV1JwWDlSeERPc2ZPK2tIWTd6SFZMTnBoREE3ZlJFOTBIUWl6ZDVpdGVUTEZZb0R6aEZHcVBtbUZxdUczK3I0cnBmSkRKZHNubVBaVFlCVml6ZnZBb0dJUTJ1MDBrZVRFaWRwYktBeng5TDNOci9adUtzOExhRWdaMUVDWUxMcFJuSlVzWTNPTjZRU1MzTkpSZFRxbjcwZ2phNUtHQllRbkZiUFlXcm9OVlpUY05xa3p6cDB6b1FQUFF5bHdHd0JKRTZ6bnZmYUp4QzErMlUzRFFtOVg1Z3ppNTNDYitTUWRtOUtYMU1UWGs3M2p5NjZkMnJmZXE2aXdUbnNFOENIVEx6Yk1LandqWXczekdFaTFyTWFZT0d3aDk0VytpcUV5TzR5VVlWaktMdkZDQW94VWZhOGpIeCttdE1BSGdvenVCNTVhc0VhWXF4cnliZldGNlE0UlIiLCJtYWMiOiI1NjkwZDY3NDFjMmZhMTVjYTZhNzNmNzJhNTI4ZTdiYzIxODA4ZWY1NTU0NzhiOGNkMmJiOTc4ZTQ0ZWQ5NGRkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413798078\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-237303184 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237303184\", {\"maxDepth\":0})</script>\n"}}