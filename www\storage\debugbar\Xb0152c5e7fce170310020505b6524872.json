{"__meta": {"id": "Xb0152c5e7fce170310020505b6524872", "datetime": "2025-06-06 19:26:51", "utime": **********.257496, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238009.454338, "end": **********.257536, "duration": 1.8031978607177734, "duration_str": "1.8s", "measures": [{"label": "Booting", "start": 1749238009.454338, "relative_start": 0, "end": **********.029992, "relative_end": **********.029992, "duration": 1.5756540298461914, "duration_str": "1.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.030018, "relative_start": 1.5756800174713135, "end": **********.25754, "relative_end": 4.0531158447265625e-06, "duration": 0.2275218963623047, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02991, "accumulated_duration_str": "29.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1302981, "duration": 0.026609999999999998, "duration_str": "26.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.967}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1922932, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.967, "width_percent": 3.343}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.201535, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 92.31, "width_percent": 4.045}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.228275, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.356, "width_percent": 3.644}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-666788357 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238000077%7C27%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJtR1ZKc1pHRTRQdThZRFJiNDh4THc9PSIsInZhbHVlIjoiZ1o4OUJtSFp5bWJqV0tjQUxldVpianRZcWlHK256VVE4dE4wRCtJMUpJR2xWTTkwTm16L2VtQW9YSGN4WlRaNEJISWZPUGZOV2RSOEQ4K202RDdTeDhkOGo1RWhFQUpQMDIyTEhaRjB6dEVRaEVnLzZ0WW0xUFpYc1dwNUNSMFNJTW1tenY5ZVBaZXg2cTJ5dGpjNUNZV0p0UjhyVGV4WWRJMUYxWTQ4YXFLOVlEVzlKME5kamdYa2h3Qm5MOFJjVE5CZzV2K3FxbndtcEJCQWhaaXZYNzJqN2JRR0VROTE0cTlWQkxaVnpKS2N3VFBUcDMySmdhdDE4VDA3aStkRHE2MXRhZmFwbFBkekZMcTdtdWgzNjdRdDZLUUhCUm8wTVFaTE9NY2dRc3FOS2I0Y2pMVHl2eWE4VjBmL1Npc200WHIrakR4empuVWpKZVdTMWNPZzd0NXoxWFl1aE5jbGduT3dVT2gwcW5WL242OGthSGxhdHp2dnRvN0Z3U0pXU3pId2V4NmErQ3FGOEFUemdhZ1VXQU9WQU54K3JHR1ROMkU4OEVhUXh6b1pTTnRGVnROQXhzNHczOWRzdGlReEVqWklVMVg1dEkyYVFoQUV2bFlWOUJKTml3T0tjQmhIbVZ5cER4OFhRdk5udURycHN2blBWZ3RsT0xJczk2OU4iLCJtYWMiOiJmYWYyNGM5NzliZTZiZDQ5NjU0NDhlZTY0YmM0ODExZWFiNmNhNjBjZTkwYmRhYTUwNWM4ZDkyMTMyMjQ5OGJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRkNGtaWkJta3NrSzYwMktHazd3bFE9PSIsInZhbHVlIjoiRDlESWJqWkQwdGxIaGJCRGZDTUpoVWNxSTEyMjZJaEhtclE1OTY2M0U3dnVJSXF5NVFWUmM1L1JDZVpjWU0rdnV2UnZtMml4dTYrUUpFTDlTZFBoUjVWRHUxcGhxZkdIM0lSbzg5TmZkRHdVY1FOL1BDc3FSellxeGVzTXI1R0szLzJNTGE0NVBRZXlEVG5ZR3NVbFUxVDFGSFg2Yk9lMkR0SVlhYnNrOFplbEwxc1BFVDd0U1ZGUjcrckxRN3loUWlMY3ZwQys0bEIwVkpXekhXSDVMWWl6QkRjam5vWnhiOFFFWjdLcTYxOHJhUkJmNld0MjdXbzZNWlpwRFJRUFhaOW0vNk9OZTRROTFVblJTR2J5NkF6UDNsd28rd085VzlCamZScDF0YmVVK1VZVkYraTIxRGFIU0NYMkhMTEdxM2MyRkR5a2pIa21NTEtFQkRGQnB0eG9wZ3UxMktab2x0VDVhTzhJTzNkSW4xeVRsUGxZdGxibXliM2VtWXRVc1htSnRReWVtckNsRmhKSHMrc0NJdCt6anpPZjVZeTBqOVMwcG81aDVUUE9rOEh6RzB2SGpOMDYvL1JnTlFXVFpnRGVpZWxFaWU2Z1IrbWRmUFdodGtqMHV0L3pkYytzbDFHVXg4NHk5YzFxSjZjYW0wR2YweUlyTlZZYW5mQ2EiLCJtYWMiOiJkMDI0ZjQ2OTcyNjNmNzkxYThhYmJmMzUzZDU0OGJmNmU0NTczM2VkM2Q4OWNkMzQ2NDJkNTczYTdiMWJkNWRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666788357\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1843162122 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843162122\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1487328970 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:26:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iit4KzJTamJGTTdnSnpKNm91RC9yTmc9PSIsInZhbHVlIjoiUUJ3dm1Ta25BY21HZkJUYXUzcStBenRvMVUyRE42a0p1cVRqWEVGNUF2Uk9xeWtyVkdjZEQxZEMxR2ttektIMzIrc0MxaXl4dzhOVjJoZEo3ZnN5RFN6eGlqZkdjcTFtckRpeXpEeFN4U3FTdUhzQkRSWHRpb3dwbkk0Z0ZhaFBPdjk4RjVWeVFvaU9PR1h2Nk85MTR0T2R0MnZFQ2ZEWWpqdXdGdmpEY2NhNDlBVFJEaEhLY0s5ZDdCZldTZG4ySXNEdm84NktuTFFlaVNNbGQ0S2NUcVJuZEEybjEvR3M3N0FLNTY4V0tCcVhuQmF0S3UyQ01qTEtoMWdvS1NnTXkrUGZsNGtmWkdWVGcrUXNRTjRBYVBpc09rU3NlYlpRdzhDV0ZjTXlqYkkzOWxxdTJDa0JCcVR2NENvNjNMYUdvNU5pM0hxVEFKVi96Q3lVL3VBcCtiNEV1TmRmSkFJNGl2bUY4dlRVak9YYldCVzFYTGYwalIxT2J1TjdQWERaNHB1YUF6V3BHRlZwWG5pTzhaSjJkMlhrQU5Vbzg0SURlTEtBcER3dGdHUG9BbmVWaXJ1ZWNTS1Vla0taaThWZmdHeEdETEFYWSs1azVTamxxMnBQYU43bi8rVi9RNkcyZGNTV3c0Rysxb3BjaTE5UGlGL0xLZVMycGlMb2N2ZlIiLCJtYWMiOiJkYmFmYzMxYTQxZTViMDIxM2RmNTU2M2MzMWRjNTZjYmVhYWExMDJhMmVjMGJkOTdjMDYyNjc2NzI5MDUyNmMwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNxQlVtMnlxaHR4QzdreTFTcDVtaFE9PSIsInZhbHVlIjoieUg1Tlp6VFdZT3cyWmdHNWlCK2NsWkQwc1JWR3FoMElXaEdVdlJmS3crdG5NODA4VW5GSjZ4emFOa1l1SzRTRFFNNW1xQWkzOENvWFlhNHNqZ3hZZVZlRGw1b09WTThtQ0QrSW1aU1JnMmJZTzVtSjkxcS9kU3ZvNThTSVhqTVVzVkY3YlNZWGNIZC9uRlVGRnBEU3AzSUtMQ2puK0RiSU5VZklLYVRrbFhTWjlJYjBjdkUybHY1VzZhdEk4bkM3M1lPR3B4WlVwbVNLU1ZPbzVMM3RUODVhTTRTS3RidXh1YVdhOGkwcmswdUY4S0hsMEs4RVBWYlZ3L1BKd01VVnBiUGxwU0JjRjY1UnlTZUdJVmFDSS8venVrRTVXRDJNK1lYS2orVG95bDJ6TEJRelFuU0k1MlBHQWF1SkFnUm1haTk0a3dKS1NNU0NJNnZOVWlGU203U0p1bmd5MW9QV0NFTzB3SVVQSlVsN0daeUIyM0VKbmd4aXNubFIwRFlFV0pUMkhMN1lqeSt5Qm40RUZaMmJrRUEwR2Nib2hRRkNOUi9rWGFRenJtN1N5WHN5QTduZVdlQXA2RUh4dHBsVkZkTG5PUGhFejBvMXJhLzdsTURRaExSbWJoakIzRitXVS9FZk1BQ2IvUTdja3RSanl6UFV3U2xMQndrNEFib04iLCJtYWMiOiI3NzQ1OGJkYzU5NjYyMGNjMTRmODA0ZTNiYzA5MDBjYjFmOWZkMDdmOWFhOTgyNWIxNGYwZDkwZGIxZjExNmM1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iit4KzJTamJGTTdnSnpKNm91RC9yTmc9PSIsInZhbHVlIjoiUUJ3dm1Ta25BY21HZkJUYXUzcStBenRvMVUyRE42a0p1cVRqWEVGNUF2Uk9xeWtyVkdjZEQxZEMxR2ttektIMzIrc0MxaXl4dzhOVjJoZEo3ZnN5RFN6eGlqZkdjcTFtckRpeXpEeFN4U3FTdUhzQkRSWHRpb3dwbkk0Z0ZhaFBPdjk4RjVWeVFvaU9PR1h2Nk85MTR0T2R0MnZFQ2ZEWWpqdXdGdmpEY2NhNDlBVFJEaEhLY0s5ZDdCZldTZG4ySXNEdm84NktuTFFlaVNNbGQ0S2NUcVJuZEEybjEvR3M3N0FLNTY4V0tCcVhuQmF0S3UyQ01qTEtoMWdvS1NnTXkrUGZsNGtmWkdWVGcrUXNRTjRBYVBpc09rU3NlYlpRdzhDV0ZjTXlqYkkzOWxxdTJDa0JCcVR2NENvNjNMYUdvNU5pM0hxVEFKVi96Q3lVL3VBcCtiNEV1TmRmSkFJNGl2bUY4dlRVak9YYldCVzFYTGYwalIxT2J1TjdQWERaNHB1YUF6V3BHRlZwWG5pTzhaSjJkMlhrQU5Vbzg0SURlTEtBcER3dGdHUG9BbmVWaXJ1ZWNTS1Vla0taaThWZmdHeEdETEFYWSs1azVTamxxMnBQYU43bi8rVi9RNkcyZGNTV3c0Rysxb3BjaTE5UGlGL0xLZVMycGlMb2N2ZlIiLCJtYWMiOiJkYmFmYzMxYTQxZTViMDIxM2RmNTU2M2MzMWRjNTZjYmVhYWExMDJhMmVjMGJkOTdjMDYyNjc2NzI5MDUyNmMwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNxQlVtMnlxaHR4QzdreTFTcDVtaFE9PSIsInZhbHVlIjoieUg1Tlp6VFdZT3cyWmdHNWlCK2NsWkQwc1JWR3FoMElXaEdVdlJmS3crdG5NODA4VW5GSjZ4emFOa1l1SzRTRFFNNW1xQWkzOENvWFlhNHNqZ3hZZVZlRGw1b09WTThtQ0QrSW1aU1JnMmJZTzVtSjkxcS9kU3ZvNThTSVhqTVVzVkY3YlNZWGNIZC9uRlVGRnBEU3AzSUtMQ2puK0RiSU5VZklLYVRrbFhTWjlJYjBjdkUybHY1VzZhdEk4bkM3M1lPR3B4WlVwbVNLU1ZPbzVMM3RUODVhTTRTS3RidXh1YVdhOGkwcmswdUY4S0hsMEs4RVBWYlZ3L1BKd01VVnBiUGxwU0JjRjY1UnlTZUdJVmFDSS8venVrRTVXRDJNK1lYS2orVG95bDJ6TEJRelFuU0k1MlBHQWF1SkFnUm1haTk0a3dKS1NNU0NJNnZOVWlGU203U0p1bmd5MW9QV0NFTzB3SVVQSlVsN0daeUIyM0VKbmd4aXNubFIwRFlFV0pUMkhMN1lqeSt5Qm40RUZaMmJrRUEwR2Nib2hRRkNOUi9rWGFRenJtN1N5WHN5QTduZVdlQXA2RUh4dHBsVkZkTG5PUGhFejBvMXJhLzdsTURRaExSbWJoakIzRitXVS9FZk1BQ2IvUTdja3RSanl6UFV3U2xMQndrNEFib04iLCJtYWMiOiI3NzQ1OGJkYzU5NjYyMGNjMTRmODA0ZTNiYzA5MDBjYjFmOWZkMDdmOWFhOTgyNWIxNGYwZDkwZGIxZjExNmM1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487328970\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}}