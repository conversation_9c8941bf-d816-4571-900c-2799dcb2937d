{"__meta": {"id": "X08dc1299882da2bfca4cb2bb62d383dc", "datetime": "2025-06-06 21:55:18", "utime": **********.148953, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.781446, "end": **********.148985, "duration": 1.****************, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": **********.781446, "relative_start": 0, "end": **********.951939, "relative_end": **********.951939, "duration": 1.****************, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.951957, "relative_start": 1.**************, "end": **********.148988, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02536, "accumulated_duration_str": "25.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.038741, "duration": 0.02246, "duration_str": "22.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.565}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0904381, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.565, "width_percent": 3.864}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.12308, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.429, "width_percent": 7.571}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=v1wjex%7C1749242089260%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdJMEdTaXpRbm1OV0hHdHljZHVVUHc9PSIsInZhbHVlIjoienE5ZXA3cjN6YXovSWI3WENxVURCRXc2R2pERUp3SU9iNWkyUEpPUTljcDBJbUNybzl6U3VCUVFjcENqZWc3dHA0NkEyYkloN291UDYwSkluL0xMeUR6UVgreitGZEpaMVl1TUVoaklpMVllUDBOemZoMFR1WHhNUHMyL3RvKzROYUdlOWZzYlZ5R1l1VmttWlFHU2VLbTkxNmwzUTVDeUF0MkwrVVhLcU9vY2dQVW5zR1l6aERJdVduNTZFRGxsaTZRUnNJdXBpUC8wV1dXbVM0T05SeFZtZ2xVWS9JbHhDeWlFMnU4T1djQW85NThiWDBhSFZkbUZGU2NrK0dUeUVRQnZrTWdZU3ZIZncvTW44SDV5QldQa1JweTZnREw3TzN5T2FOK3UwYkpiK3VGNVVrZVQ2Qzh6cllqSHlDS2JiRFkyUnlkNEZaM25URis0bUd3ZkQzSVE4ay9hQXdLU1E0Z00vd0Z5aXRaVzdnMnJqbTJhbkRJYUw5YzRva3o3OTlYdUZCOU9IUjVINWsvMjdjOTdvOWo3b2VhcTJJdEs2amcyRXd6V3ZReGhuZFZKTzN2amt2WlJvT1JmS2lRbUpyU0E1VEt6elc4WnpidVUyWVVrQVZYNjB4U21PSHJRdVBhV3VwUmMxV2N5blIybVpMbStmL3JkTDY2Sk5qUzQiLCJtYWMiOiIzY2Q1ZjBhZjk3NTNkZmQ1MTEwZjBlYTI3YjZmZDU1NDJhZTUwODNiNzMzNjA3MWFlYzRjZjUyMjE0NWQ1OGE4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllQNUZjNGhCSEUycGhrTEZlKzluS2c9PSIsInZhbHVlIjoiZjI0NWxrV2prb1pQdEQxaWlSMXM5bEpSNHI5dTk1c0hUMDk3ZElNYUx5eEc4MWFVVndYcWprNlRBNnk1UkI3NS9KZkhUTWtrQlcwVEJKV2ZKTTg3Z0tTRDF0NWsyUldMVzg5QVRNOVhUUUVJVkhSbjBaMmswbTZYZDVwdGpHNXVMNFdBeFQrVy9INEduWDBLN2p4UnRZNWZHY3RkYkNZSWxxMENMZjlzOHNXcStBUlVHNXdoVDliTzJ0elVxcGo1MVVRSG1Fd3VsaWh5ZXQxTDdLemw3QjArRExUdHRETG13Mms5eDNxZFYyb1VCdWZLaDRjNEdicGcyQitYdTRHbUlSRTRmTUU0TVBxRUtEaS9mSUx4dTFzTXRSellKUUxGMzM0YjVMWnJqSmVvZFVqaFdKQ0hRdHVLYmNaYVo0U0haUFVjTngvd3V6L3FlMXJrRG9aZFhWNW5xQXBtSGlTalAyUjRMYWM5bEdRVGx1SFJ1N0ZEVTNRdTFDcnFnQ3BSVW5oNndYRDZvQWhsSHAwbG1xNjFEQ3FCU0U2eWZDdEhueWxjcDdDVDl3Y1lTY2ZPNHNkLzk1a0RxN20zYlgzZXNqTWgvc2lJMk5qSXNaQmswM29pRHhNOFliZ0lHQXhQb0Z5eXFBZ01qb0JLSVRkSnkzUjRJb1NXYytFTTJNVngiLCJtYWMiOiJiNjg4Njc2MTY3NWZmNDA1NDAzMjc1MGE5YTgzMjQ1MDc1YTQ3YmQyMGIzYWZjMTcxMDI4ZTlkZmJhMWVhM2ZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1874415498 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hE2sfajo5A8qKfU8aNxLgaH66sUWlFc2M9YW325j</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874415498\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1706353796 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 21:55:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFlYU1PYy9CelNCUmkvWkt4d3FBVXc9PSIsInZhbHVlIjoiTkMwTEROc0xKTU5lT3pYblVHM0lveFhIOFpobVNXc3ZxRW40WnowUjVNMHJQajBDNDZWNUdhNEJDNlJVcUJOdVZYQVRsV2lTRFBGTHJ1UXRqTTgyYjIyNTlrU0xsWjdiNXpxU2lKQ1NzOGlZVVQ1OHhxRzZJcktQTVB0Zkl0WER1TXRiQVVMVHZWSVMwMmJrMmZhdHc2N3JtMWJPZGRrU2ticnd0TzI0Ulc4SktIa1NYYWFhTHlUUkxmbzNEbzdGajJoRlVYNzVmYVEyUklkaWFzMXRVVjdLWVZsRFdRU21vTUQ1L0FoS1pQSWJHcXlkUVNRNkZnckNyZHc1U1gwL0UyTi8rbjhvUHRIRUd2b2FmRDd3b0FzVG9lZU9EOXBFWjVIdTRiYlA5aXM5ZVhUcWhvTDZ2YjRjVCtaUzNSemhrQnlJRXFHL0QxbHlKeWRiekE4QStucVJ2OTE3QjZLSEh0QXAwUWxacjdodnlZSFBWdmtOc0YzV3hZYy9TVjQ2VDhUblpHckxqOHlSWmR1KzlxeEx3cllpOG9VbUwyZ1hvQ0g0UDZMZ2dSMHgwQWptS3RIT00zcDlrWmREamFidUJaaUJ0MERkSlorc1JPd2FEZVNFaWlJSk9qa05ZZEl4K0RqaDg0cW5DSUVkbFlTcjhodGw3aG8rQTkrVllTMnciLCJtYWMiOiJkYjZhYmNiNzM1NDU4NzM0NTQ3Y2NmMmQ0ZDZhYTk5ZTAwZjA0ZTlkYWFjZmE3ZjYwOGI2ODU5ODQ1NDUxMDFhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9iTkZJSUk0ZUUwRHljUEdaQlhKTHc9PSIsInZhbHVlIjoiMS83TWdGdmFZY2lvNElDb2laTE40RjQ4UDFjcFRHQW1zdEo2ZysvSUdMbCtQV2FpekxvemltSlppb29mWmpKZjlzMmJER2ZDZGVQZXFabzliekJodHhaeURxMVpnc285Y0RpRFQ2SytiRERubWl2QjVmVWdUOCtLUjNrTnRMS1N3OXVNYkNKUDg3KzZDMm5odDNvMkZxRGk2U0xmWGJvdGpTYnNoZmhaRzhkRjJLaFBGNnU1L29JbUlLYWhJdy9PaEpVNk93KzRZRVFRVGVKbzJIZUpxdzF3cWovbVBJYk82aTdQRHFHK0xJMHQraXl4dkpkMGNiV3ozMzN3ZFZHTXVvYWx1SVZlVWVaU2pFUVlTcHpld2NzcU14Y0QyZlduWmJHbm5ENHZGM2I3RENrUVZHSXFsdDBqd0dBajM1S0xQelg4ODJGL2dmQ0JvYUJBMm9mMmJFZmZjQUNuMnlhU004bHFHRXQ0YXdVS0Qvano1d09UaHU1Y3lmZzdPVWRmdmlFUVpNU0g4YWRRUm5yTVByOTFNQ1oydVRpVkJGc0dUYW5hS005VytHaDhLZW52MFJyWS9LNHk2amZmTlpFSmhFZ1J5dHlLMENQTkpYenc2cm5xZXpOTjZWUld3d3I1Z3ZZcjN0d0ZkdGN2UDlVQnBtWk5BakdESjdPNTFDUWUiLCJtYWMiOiI2NDI4MWZmNDU3YjA4MDQ3NGIwNDJlYjA0ZGE3OTFlNWJiMDY1MmU1NjY1OTVkM2Q0NjVkZTRkMjFmZGZkMWZjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFlYU1PYy9CelNCUmkvWkt4d3FBVXc9PSIsInZhbHVlIjoiTkMwTEROc0xKTU5lT3pYblVHM0lveFhIOFpobVNXc3ZxRW40WnowUjVNMHJQajBDNDZWNUdhNEJDNlJVcUJOdVZYQVRsV2lTRFBGTHJ1UXRqTTgyYjIyNTlrU0xsWjdiNXpxU2lKQ1NzOGlZVVQ1OHhxRzZJcktQTVB0Zkl0WER1TXRiQVVMVHZWSVMwMmJrMmZhdHc2N3JtMWJPZGRrU2ticnd0TzI0Ulc4SktIa1NYYWFhTHlUUkxmbzNEbzdGajJoRlVYNzVmYVEyUklkaWFzMXRVVjdLWVZsRFdRU21vTUQ1L0FoS1pQSWJHcXlkUVNRNkZnckNyZHc1U1gwL0UyTi8rbjhvUHRIRUd2b2FmRDd3b0FzVG9lZU9EOXBFWjVIdTRiYlA5aXM5ZVhUcWhvTDZ2YjRjVCtaUzNSemhrQnlJRXFHL0QxbHlKeWRiekE4QStucVJ2OTE3QjZLSEh0QXAwUWxacjdodnlZSFBWdmtOc0YzV3hZYy9TVjQ2VDhUblpHckxqOHlSWmR1KzlxeEx3cllpOG9VbUwyZ1hvQ0g0UDZMZ2dSMHgwQWptS3RIT00zcDlrWmREamFidUJaaUJ0MERkSlorc1JPd2FEZVNFaWlJSk9qa05ZZEl4K0RqaDg0cW5DSUVkbFlTcjhodGw3aG8rQTkrVllTMnciLCJtYWMiOiJkYjZhYmNiNzM1NDU4NzM0NTQ3Y2NmMmQ0ZDZhYTk5ZTAwZjA0ZTlkYWFjZmE3ZjYwOGI2ODU5ODQ1NDUxMDFhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9iTkZJSUk0ZUUwRHljUEdaQlhKTHc9PSIsInZhbHVlIjoiMS83TWdGdmFZY2lvNElDb2laTE40RjQ4UDFjcFRHQW1zdEo2ZysvSUdMbCtQV2FpekxvemltSlppb29mWmpKZjlzMmJER2ZDZGVQZXFabzliekJodHhaeURxMVpnc285Y0RpRFQ2SytiRERubWl2QjVmVWdUOCtLUjNrTnRMS1N3OXVNYkNKUDg3KzZDMm5odDNvMkZxRGk2U0xmWGJvdGpTYnNoZmhaRzhkRjJLaFBGNnU1L29JbUlLYWhJdy9PaEpVNk93KzRZRVFRVGVKbzJIZUpxdzF3cWovbVBJYk82aTdQRHFHK0xJMHQraXl4dkpkMGNiV3ozMzN3ZFZHTXVvYWx1SVZlVWVaU2pFUVlTcHpld2NzcU14Y0QyZlduWmJHbm5ENHZGM2I3RENrUVZHSXFsdDBqd0dBajM1S0xQelg4ODJGL2dmQ0JvYUJBMm9mMmJFZmZjQUNuMnlhU004bHFHRXQ0YXdVS0Qvano1d09UaHU1Y3lmZzdPVWRmdmlFUVpNU0g4YWRRUm5yTVByOTFNQ1oydVRpVkJGc0dUYW5hS005VytHaDhLZW52MFJyWS9LNHk2amZmTlpFSmhFZ1J5dHlLMENQTkpYenc2cm5xZXpOTjZWUld3d3I1Z3ZZcjN0d0ZkdGN2UDlVQnBtWk5BakdESjdPNTFDUWUiLCJtYWMiOiI2NDI4MWZmNDU3YjA4MDQ3NGIwNDJlYjA0ZGE3OTFlNWJiMDY1MmU1NjY1OTVkM2Q0NjVkZTRkMjFmZGZkMWZjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706353796\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}