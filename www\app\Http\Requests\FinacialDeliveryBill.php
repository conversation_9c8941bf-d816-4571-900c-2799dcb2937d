<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class FinacialDeliveryBill extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Adjust if you need authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'payment_type' => 'required|string|in:cash,network,split',
            'pos_id' => 'required|integer|exists:pos,id',
            'select_payment' => 'required|string|in:cash,network,split',
            'total_price' => 'required|numeric|min:0',
            'transaction_number' => 'required_if:payment_type,network|string',
            'split_cash_amount' => 'required_if:payment_type,split|numeric|min:0',
            'split_network_amount' => 'required_if:payment_type,split|numeric|min:0',
            'split_transaction_number' => 'required_if:payment_type,split|string',
        ];
    }

    /**
     * Get custom error messages for validation.
     */
    public function messages(): array
    {
        return [
            'payment_type.required' => __('Please select a payment type'),
            'payment_type.in' => __('Payment type must be either cash, network, or split'),
            'pos_id.required' => __('POS ID is required'),
            'pos_id.exists' => __('The selected POS ID does not exist'),
            'select_payment.required' => __('Please select a payment method'),
            'select_payment.in' => __('Selected payment must be either cash, network, or split'),
            'total_price.required' => __('Total price is required'),
            'total_price.numeric' => __('Total price must be a number'),
            'total_price.min' => __('Total price cannot be negative'),
            'transaction_number.required_if' => __('Transaction number is required for network payments'),
            'transaction_number.string' => __('Transaction number must be a string'),
            'split_cash_amount.required_if' => __('Cash amount is required for split payments'),
            'split_cash_amount.numeric' => __('Cash amount must be a number'),
            'split_cash_amount.min' => __('Cash amount cannot be negative'),
            'split_network_amount.required_if' => __('Network amount is required for split payments'),
            'split_network_amount.numeric' => __('Network amount must be a number'),
            'split_network_amount.min' => __('Network amount cannot be negative'),
            'split_transaction_number.required_if' => __('Transaction number is required for the network portion of split payments'),
            'split_transaction_number.string' => __('Transaction number must be a string'),
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        // Redirect back with the first error message for SweetAlert
        throw new HttpResponseException(
            redirect()->back()->with('error', $validator->errors()->first())
        );
    }
}