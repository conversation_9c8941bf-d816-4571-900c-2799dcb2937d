{"__meta": {"id": "X59f28d43ae08bddc69d62728333e6066", "datetime": "2025-06-06 19:38:23", "utime": **********.939645, "method": "POST", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238702.496711, "end": **********.939695, "duration": 1.442983865737915, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749238702.496711, "relative_start": 0, "end": **********.771254, "relative_end": **********.771254, "duration": 1.274543046951294, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.771274, "relative_start": 1.2745630741119385, "end": **********.939699, "relative_end": 4.0531158447265625e-06, "duration": 0.1684248447418213, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44974080, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=75\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:75-235</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00544, "accumulated_duration_str": "5.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.897717, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:78", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=78", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "78"}, "connection": "ty", "start_percent": 0, "width_percent": 77.022}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.911486, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:81", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=81", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "81"}, "connection": "ty", "start_percent": 77.022, "width_percent": 22.978}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"status\"\n  ]\n  \"new\" => []\n]", "status": "Your Account is disable from company.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-270977636 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">140</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=1jor13%7C2%7Cfwj%7C0%7C1983; XSRF-TOKEN=eyJpdiI6IjM4TjRtWjhZclVCQ0t5Z0R4Y2V2N2c9PSIsInZhbHVlIjoicjVWaTh5VGtoRUJXS1pBT0IzOEVuL0ljeVhKMW5hTDBzczVHZ1QyeUNlYnAvVitDaE4vbWNVdjNqM2x6UDVHc2RPaUMrcmtERm5nRVQ4ZGl3K2Mwd0U2NVhMakRHT1Z2WUpkTG9aZU0wKzgyN2F5UEdqNk9mOVkzTjErUzIxWXRnTWg2aG1WT1hkVEVTdTd4dngrdmpPTVBnTHdYeTV5dVlkK2s2ay9QbXk2SlUxN0hiazB4ZFN4OHNrTzE3YzF3b2tRTC9vSkxPUWpmQURzeC85SnZHTnRZQVc4bW15ZUk2MDZtQ3JvV1pSNEVRczN0MkxNWm5zU2Q1Y25qREhOSGRUZU1PUXpnMlFlZ2g4ajBpQUtsRVUwdFF3VnhkVFp6c2Vuamc4Z0FNay9TRjJnbFM0M283UkdnWGU3RW84VlgrRVBxSkhjLzhwTjFONDZGY0hvb2YwRXBiSXJuNkR2SFBLZVJWelFDT3djcWpBU250M2haNW44TjJWdE1CUGRZcjUzVzZueCtrN2NyMU9uV3pCTUxwL2NteGdNRTNpa0htcW9may9JajV2UlZ0bFJRL0VINlcwYW5uVjRCN2p3S2poTkt4TVBkTVg1RTV5MDJHbFM4Qm1ZMVdGTExxUHFvYThrSDVHYnM4YXR3SjZsRERVZFA3c1ZDbDZkWWplc1YiLCJtYWMiOiJkYjg3NDhhYTA2NjA1MmJjZDBhY2IxZDgxZWYyODdiMWY2ZjkwZmZkODkxYWEzODBmYjE2OGI1OWE5NDBkYzE4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklBdExOclNIQXQ1VE4wZzE0RWZBVUE9PSIsInZhbHVlIjoiWEU4aHI5d1BqdnVzYkVQRThHbGx1U2VEZWVMaVErdVV1eEVmSG5CTzk4YTFxSVkzQ3lPbDVlS3h0NWVtOTFyNFRUeHBvKzdWNDVKNk1oRVI3NTJMei81eXhDcE9JU0hudWMxbVpWOTlScEZxTzlMRVdMNW1nM3MxUHA3QnZYTGduQm9qeTFjNW1Zd1NRQ3FTcTZvY3JBdmhNVXBBSjVlQzVXa1ZFTW5GSnFQS0c5RjBIVnVBYS9mVTRxTWJxbmd6OWIrZzhtSVk5MVVnN3N3ZTdNZ3ZVYmRFY3FRUmRad0ZjWjAxZnZ3elFqNE9hYnh2MlViMnRqZ0tTTmMvdkhNQ05OK0pTb1lQNUE3cFljQVF1OEV6ZTQvYlUrdFV2SEIzWGtUUUE4ai9GTjhSdGVzYm9YMk5TbndDNWw5ZXEwQUJhUzRaYmt5SGN5c0w5NmQ3d0F3TXMwYzBha0QvOEpiTTlqaStBekptWTFYUmZhSzlWMFNXNkJ2YXNmL0VZRHFMeXRuTDdYYVBlcjdYSWVUbW1FRGxoOUJSVjl5V3hZWDRzNThPcHdrM0pQSnJBY2wwYytzVXdINTVSdklhVFdRN0xnTTdlQ3hvS1ZmalNyZFh3RXBXN3Yrd2VvaEU0MFlvemhhQ094UzdSbUJkOVlLWFRBaHVsK3NnL1lNQVViRSsiLCJtYWMiOiJmNDdlNGJmNjE2ZDRjOTQ2ZDZiNDM0MzA0NmFmMzIwMmQ1YjFjZjFlYzc1MTQ1ODJjYzllMTMxODljYzBkMWVkIiwidGFnIjoiIn0%3D; _clsk=151odr7%7C1749238683155%7C5%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-270977636\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-17151475 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17151475\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-722827113 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:38:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVJaVpLZVVnZVRqNkh4WSs3eTBsbnc9PSIsInZhbHVlIjoiYnZ0Snd0aXhFek52TVovZlpSckRJbWZwOWplWFY2OTM0aHRtSWhwdldrOTdEd3c3TFNsS1l5ZE5YWEg5NnBBck5PWStzT1hTTlhMTDd3YXVOaWpuMUxRTnVTM2xmS0YvTEdCZnRzT3hFanY1U25Wc3NlWXo4YXFjRTRNRitRVzVzaVNTK2ErcjZqbDltMWtreVVaSEdVMEJTSWIvU0ExbmpwbjJYejNLMWJkaVJTcnhTU3hrVlpiS1FaekRGclFYa08ra0J0aGxFWXFFbUszdDJCTDY1YWt0R3VNb05tUDN6UkxwMUVWc2xLTmFzbFM4OWZGU3ZYK3gzbGJHWmgxZ1NjcnpOT2l3OTdYOEd2K1JMVE5HR2pySnFVelJGNlRUdW1aVHlxRkJyQ2U4RGFnT3JqalJlZ3ZtdzlWVVNEZzdIeEprR3kxWTNQRUlYWjA3NUMyRWRlR3R4MmQ1YUFWY1Rrb05Nb1IxNHRHaWgzU2ZzT0VkOU5sUU9nWUNzQTlsQnFoWHpaamhiQi82OGN0UzErcVcvRnJFRUIxT2syZHB3Vmp1aXR2RUUrTGorWVoya1ltR2Q3SnVLRE1BVXJOemNUak9CZlVOc1plOTJ5TWFSNkIzUHpMdEhzNThMRzRNWUxWc2JwZ2FPWDd5TjI2c2ZxWDBURzZqOW45bmZPRzMiLCJtYWMiOiI2MzI0MTViZDQzZWVkNGYzZDcxZWNhYjE3NDFlM2E4ZjdmYTQ0MTMzNDg4ZmRhZjA4Yzc3MGE3MTVmNTk4NmU4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxoVEx5d0lxRlFIVmFQekNxck5Na1E9PSIsInZhbHVlIjoiUGlWRVRsR3VKcy9HSXIzMkRNb3Y0QlQ0YnEwUjdqZzV4Y00zZnJFUnJ6MFZGSXh3aXRtOXZGQTBKNXhBK1VwM2RHdUFHeGtTTVlIaUhkM0REQUVzWGtuOVA4SERqU0dqWlAwd2RuSjVYZEozWW44czRmSHdiYjQ5ZFdONHNudHVVeGdCb1lUNi9lMUZRbjdhMXdTeFdGNDl0Kzl6ODNUakszWkZCNkhGajFuU1A3NXVPa05SRVNzeUM4UkNBMTBEY0pWWWRacU9QS2V6eGZBbUpNWXRNZDhVSHdtdDF1RWZsTXlYbnhRZlV1K2ZDNys4K2UyTG93U2UvdEVBeUpITEJWY1A4S3NRK0MzTkZSVm95azRWSWJIZlZlUEtzMkVMclh0RTB2eVVqN2pFUDl1VEZocXozQ1FTeGY0V2s0ZGZKTlhMU1RveGYybUx5WkM4RFozMTdqKy9hdG0yZG10Y2U3NWRXaHNNNDYwZmpjR0xocnl5a2w2cW1UWVhFdGlaMHZvalBjMUlEUVdGN3R0SHB2Wlczd25BYi82eW9nd2xtSGJCTEUwYkVuRU9nVXdqcHpKaXBiZzlhS3NzM0NEU0l4aGFUWkkrUGNseTZXdG9Pb3prNkFZUDJqWUcrVTdTUGRpdHN3ckdIMDlHVzJqTlhsTEc0MDVLbWJxcjdXS1giLCJtYWMiOiJhNThhMjZhZDViNTFhODEzOWM3OWUwN2QxNzEzNzkzMDc5OGUwMWM1NTU4MzYyOGJmYjYzMDI0OGRjZGRhODUwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVJaVpLZVVnZVRqNkh4WSs3eTBsbnc9PSIsInZhbHVlIjoiYnZ0Snd0aXhFek52TVovZlpSckRJbWZwOWplWFY2OTM0aHRtSWhwdldrOTdEd3c3TFNsS1l5ZE5YWEg5NnBBck5PWStzT1hTTlhMTDd3YXVOaWpuMUxRTnVTM2xmS0YvTEdCZnRzT3hFanY1U25Wc3NlWXo4YXFjRTRNRitRVzVzaVNTK2ErcjZqbDltMWtreVVaSEdVMEJTSWIvU0ExbmpwbjJYejNLMWJkaVJTcnhTU3hrVlpiS1FaekRGclFYa08ra0J0aGxFWXFFbUszdDJCTDY1YWt0R3VNb05tUDN6UkxwMUVWc2xLTmFzbFM4OWZGU3ZYK3gzbGJHWmgxZ1NjcnpOT2l3OTdYOEd2K1JMVE5HR2pySnFVelJGNlRUdW1aVHlxRkJyQ2U4RGFnT3JqalJlZ3ZtdzlWVVNEZzdIeEprR3kxWTNQRUlYWjA3NUMyRWRlR3R4MmQ1YUFWY1Rrb05Nb1IxNHRHaWgzU2ZzT0VkOU5sUU9nWUNzQTlsQnFoWHpaamhiQi82OGN0UzErcVcvRnJFRUIxT2syZHB3Vmp1aXR2RUUrTGorWVoya1ltR2Q3SnVLRE1BVXJOemNUak9CZlVOc1plOTJ5TWFSNkIzUHpMdEhzNThMRzRNWUxWc2JwZ2FPWDd5TjI2c2ZxWDBURzZqOW45bmZPRzMiLCJtYWMiOiI2MzI0MTViZDQzZWVkNGYzZDcxZWNhYjE3NDFlM2E4ZjdmYTQ0MTMzNDg4ZmRhZjA4Yzc3MGE3MTVmNTk4NmU4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxoVEx5d0lxRlFIVmFQekNxck5Na1E9PSIsInZhbHVlIjoiUGlWRVRsR3VKcy9HSXIzMkRNb3Y0QlQ0YnEwUjdqZzV4Y00zZnJFUnJ6MFZGSXh3aXRtOXZGQTBKNXhBK1VwM2RHdUFHeGtTTVlIaUhkM0REQUVzWGtuOVA4SERqU0dqWlAwd2RuSjVYZEozWW44czRmSHdiYjQ5ZFdONHNudHVVeGdCb1lUNi9lMUZRbjdhMXdTeFdGNDl0Kzl6ODNUakszWkZCNkhGajFuU1A3NXVPa05SRVNzeUM4UkNBMTBEY0pWWWRacU9QS2V6eGZBbUpNWXRNZDhVSHdtdDF1RWZsTXlYbnhRZlV1K2ZDNys4K2UyTG93U2UvdEVBeUpITEJWY1A4S3NRK0MzTkZSVm95azRWSWJIZlZlUEtzMkVMclh0RTB2eVVqN2pFUDl1VEZocXozQ1FTeGY0V2s0ZGZKTlhMU1RveGYybUx5WkM4RFozMTdqKy9hdG0yZG10Y2U3NWRXaHNNNDYwZmpjR0xocnl5a2w2cW1UWVhFdGlaMHZvalBjMUlEUVdGN3R0SHB2Wlczd25BYi82eW9nd2xtSGJCTEUwYkVuRU9nVXdqcHpKaXBiZzlhS3NzM0NEU0l4aGFUWkkrUGNseTZXdG9Pb3prNkFZUDJqWUcrVTdTUGRpdHN3ckdIMDlHVzJqTlhsTEc0MDVLbWJxcjdXS1giLCJtYWMiOiJhNThhMjZhZDViNTFhODEzOWM3OWUwN2QxNzEzNzkzMDc5OGUwMWM1NTU4MzYyOGJmYjYzMDI0OGRjZGRhODUwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722827113\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Your Account is disable from company.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}