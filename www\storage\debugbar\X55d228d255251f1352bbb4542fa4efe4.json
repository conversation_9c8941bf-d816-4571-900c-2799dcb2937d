{"__meta": {"id": "X55d228d255251f1352bbb4542fa4efe4", "datetime": "2025-06-06 19:38:23", "utime": **********.30233, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238701.631711, "end": **********.302363, "duration": 1.670651912689209, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": 1749238701.631711, "relative_start": 0, "end": **********.121254, "relative_end": **********.121254, "duration": 1.4895429611206055, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.121279, "relative_start": 1.4895679950714111, "end": **********.302366, "relative_end": 3.0994415283203125e-06, "duration": 0.18108701705932617, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44778264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00837, "accumulated_duration_str": "8.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.213003, "duration": 0.00478, "duration_str": "4.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.109}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.24748, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.109, "width_percent": 11.47}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.257534, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 68.578, "width_percent": 13.023}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.27519, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.601, "width_percent": 18.399}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-780096992 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-780096992\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-971347140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-971347140\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1966259882 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966259882\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-535452781 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238678455%7C52%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxXRjZSMW82NmhRTkhmano2Y3VkeEE9PSIsInZhbHVlIjoiR3IyWkdMUFJxL1NPT2FPaHpqZXpRTEVWSXM1M3FnMGFwd3BsV1pMNlBnY3Q3L3ZjYU1JYzlFTSszcEpuT3Zad2RNcW1ldnl4TXA1WVJPc3VqY2xuZWR3ZGwwaWUya3oyZGRVcU5TcGVVSGJtNEdjRG1kQ2Jkd24xMnliaFYwWEFlcThuT2taaEF1KzVHYVdDNWNYVW9BZ3VDbEZKUTRMd0NCMVVTL3VRVWZqM3pSNEVQbDVRcDQ5SERFY21PRnpFMjBOd2Y2WVNmTFJYTzhBWStCQXlyRFJvZG81Y3gySDhURzE1VkpNVVEwcnVVamJ2UE1uMWJFZEwxRW9LUGJiQWs5NnpQcVpNeHIybGViLzVKbk1mS1ZJckNXTmtqRWlCU1RzWUc0WW1zeGpNak1CVDlXdWlYNmttSVVrTm10L2s3WCtqb0V4SHJYQmgwRENTS1kxYTlpSlV0M2lGWU8vRmZvTlpIVGp6SzF3K0cxdjdIRzBaQmFpeDdkTy93dXVLcHRNWUJCWUdHWEdZMkdJQnc0cGFWbFU2bk1KNUFhdktCbGtoaWRReERZRGV6cmJnU3BtQWVtK3JRWU9qYUhqYXFwTXo4RmM3amN6YXJCUTdKTndMd3MzZ3NFNzBYcDhvTUpkQ0J4bUhBT3NBQ2RBNndLVlJ1WDl3ZmFYK1o1TnAiLCJtYWMiOiIwMzViMGJmYWI0NTA1YzNlNzI1ODc2MzkwNjAwMjBlMzY4NDYyMGVkOTE3YmUyYWFhMzQ0OTU3YWU0MGY4MWUzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVxclJGOTZHWjh4Zzd5V1QzcXN4YWc9PSIsInZhbHVlIjoiWXV6eFBsaHNPclI3Y0dZZVAwYmhUbFNYRWRaTHRQOEZvbFpXV2szcTZWWVRCQUxCQU52aFp0M3NlRnIxRXdhcEhPTW53cE95c0lSUmdKbHQ0clhBOHliWE80RnJIWjRtbzBGdVhoSVdkSkdMbnlRM3pyQTE3KzY1aXhlNWdNYmpWY2txeVE4N2lPYWJJNWV1eEVKSnBjY2t6Ujg3eS9rOHhZN2VNZnFNOS90NWJydzlzdXpnaml5UnQvbHJoYmRJZVJKV1NDR2tjc3czVlBFMmtXUHBqTjlaSWttcGVVbnZCZmtQYjdPcDIvQzJodjhzN0N3cXRvWGtvbE9ZM3BscXpnRzJaaGoxSzBkaHROS25wbHNocXpVMFd1L29tc1pvWDBoV1pSMTBBeUxDd1lJalErL1JRM2h1clBaU2pKZ0p3THNBeFlWZXJQQzF1b0VDTzgvcER3blNxQ1RCNXJxR1BsTThQejNObFIvclRJUXhFMXZMZHBnSEtpeUhva09UcVdoTFhCazFJUkhiQ014d1Y0Tkd3RzhkU2tXcmpCWEptaUU0a1QvRFVZTFdNNE80M3NXRjlXdlVGeU8wTkRwcVFMZ1lpOEtsWlIydUFDcUFHWUY4VVV5eDdLL1kxSS9NTE1CeFpqeUhMdWNyNnhGaStrdFJWTloxaEp5dEtBUkkiLCJtYWMiOiJlNDY2OTUwY2E5MWNlYzI4NDA4YzRhNGFkM2UwYzYzZDQyZjQ1ZDY4NzNhMWU5MDE1ODNjN2VhZGI1YWY2NjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535452781\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-157792220 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157792220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2023395942 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:38:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZia3VBVDZleTB3UDIwMmZ3aUlPNUE9PSIsInZhbHVlIjoiVDNwbm5EYVdXZm1rdGRuQ3ZSTm02VkR2QVdNcFB5Yyt2RWhCVGVQd0NFeTVhT0pnSzdIOTY5QzhVMzN6YlBEQVpzclVFenFyREZIbVcyL0ROSnNrdGJlL3lhd2F0Tld3THQ1MUhwS0h5TVlpdDYrb3pWQnVMTm1WYVEwR2FvQ01FR0U3dnhndS83Y0hjVDFQeE1ySy9xRkk1dzF4ZEVqYlc4WjBCblJuWUd2dDR1cHE2b0x2eno3SmZabGpuR00zTnIzbDVJdURpck9VTCt0ZDZQSDJlam9mVTZ0c0VLWXhuRS9ialh0UlJ6SDQvS0tlcUJsandiU0o3dCtLQ2UzRWI0NVFZSXJQcFlnK0dsY25aeWVHbDBKb1FEdGd1SXhUV1JteGVjN1lockdrWDh6QUF0UEROUXpLQnlyVWdHNnZHSE01dnRCZFpWU1pROFJzRGRETlFTYWF4UWlXalRzajZpZEU2UExuaU5iVjBkN1gwbG82Snh3THhVZ0h6V0dmbG1qcXBnN056Ync0UzVZTjhsTURzaDZJMUpydGNIRzVsYStST01BR0xSOExIMkhmYXZnQ251Ymxzalc1SkVLeXdhcG14L1o3SjlISXo0a3Q4ekYvc09iNUw0UFpQMVFrU3pFZ0V3Yk5PdE1vaWswcUFuVUUzUG9ISE1yR2NTMkciLCJtYWMiOiJjZTMwZmM4NjY3OGRhMGVjNzE4YWI0MDZiNTkxNzhkYjY4YTQxOTQ5MTUwMzIyYzYxZDAyNTZhZmExODBjYTU3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRQTEg3Y1l5cVRpSUFZbGc0dUN1eWc9PSIsInZhbHVlIjoiRlRuYUg2ZzBsUzF1UWtUcmtLTG9rQVlIMkR5cVZhbGpySzdXalJhV2psTW9lMEl6RzQ4TlZ5THpiN0xmckpyZW1mWVQvZEFZTzkxN2FOMkdmaTZWdlg3UFNQMTc0Qnh5SmFlSlhlRVBGNnFlR0hla1NodFpqK0xnYW9RVENGTFdHTE1qaVJ3UlNTLzFGU04zc1Z2VlBEazdON29KTkhxTXo2VE96ZlRUcVcrYkNTK3VRTGtsdlEzNVR5Z0dxd3V6dWhuckt3UkpTT2I0S0JnT08yZ1pnQnlNK2t6R2NSaUovY0hwcHQwdEZEQXQ1dk5oSlZnZjhYdWRXK2ZxTGhDeHFRcmt5RlUwV0pha2hnR1lWcVVvTWRsdXlRd3JiSzd1TjFCMHN3aDcrQUZ0MEJNWWRIOUtLa0czaEpVZDhFWHRUWWJ0bDg3UTA3SzN0S01sTGc3VlNWTThXckVwOWx6SlpqOXQxZVRDN1dGTElNaEluakJDZjQ3empWUjdGUnh4WVJGV05iSm1NcjVnRGFxb3VJekZtYVFBWFFDRWRwRSswNmFKS2llZmFudmprOXpYRkgwbW9jeGsyS2tjUHl1UXVsTkR1NUMwVkU4R0dxdGZJbm14b2tXQTlUSVJiVk1takM3MWUzMVJQNjVMOHoxQ28wZnBITW5iK0NBZ2F2eGIiLCJtYWMiOiIzZmI3NGI1NzM5N2VjMTAzYWNmOThkODMzMzYyMzY3Yjc1NmQ4ZmQyNWU4OWE5Y2UzYzEwNWUwY2Q5NmE0MThjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZia3VBVDZleTB3UDIwMmZ3aUlPNUE9PSIsInZhbHVlIjoiVDNwbm5EYVdXZm1rdGRuQ3ZSTm02VkR2QVdNcFB5Yyt2RWhCVGVQd0NFeTVhT0pnSzdIOTY5QzhVMzN6YlBEQVpzclVFenFyREZIbVcyL0ROSnNrdGJlL3lhd2F0Tld3THQ1MUhwS0h5TVlpdDYrb3pWQnVMTm1WYVEwR2FvQ01FR0U3dnhndS83Y0hjVDFQeE1ySy9xRkk1dzF4ZEVqYlc4WjBCblJuWUd2dDR1cHE2b0x2eno3SmZabGpuR00zTnIzbDVJdURpck9VTCt0ZDZQSDJlam9mVTZ0c0VLWXhuRS9ialh0UlJ6SDQvS0tlcUJsandiU0o3dCtLQ2UzRWI0NVFZSXJQcFlnK0dsY25aeWVHbDBKb1FEdGd1SXhUV1JteGVjN1lockdrWDh6QUF0UEROUXpLQnlyVWdHNnZHSE01dnRCZFpWU1pROFJzRGRETlFTYWF4UWlXalRzajZpZEU2UExuaU5iVjBkN1gwbG82Snh3THhVZ0h6V0dmbG1qcXBnN056Ync0UzVZTjhsTURzaDZJMUpydGNIRzVsYStST01BR0xSOExIMkhmYXZnQ251Ymxzalc1SkVLeXdhcG14L1o3SjlISXo0a3Q4ekYvc09iNUw0UFpQMVFrU3pFZ0V3Yk5PdE1vaWswcUFuVUUzUG9ISE1yR2NTMkciLCJtYWMiOiJjZTMwZmM4NjY3OGRhMGVjNzE4YWI0MDZiNTkxNzhkYjY4YTQxOTQ5MTUwMzIyYzYxZDAyNTZhZmExODBjYTU3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRQTEg3Y1l5cVRpSUFZbGc0dUN1eWc9PSIsInZhbHVlIjoiRlRuYUg2ZzBsUzF1UWtUcmtLTG9rQVlIMkR5cVZhbGpySzdXalJhV2psTW9lMEl6RzQ4TlZ5THpiN0xmckpyZW1mWVQvZEFZTzkxN2FOMkdmaTZWdlg3UFNQMTc0Qnh5SmFlSlhlRVBGNnFlR0hla1NodFpqK0xnYW9RVENGTFdHTE1qaVJ3UlNTLzFGU04zc1Z2VlBEazdON29KTkhxTXo2VE96ZlRUcVcrYkNTK3VRTGtsdlEzNVR5Z0dxd3V6dWhuckt3UkpTT2I0S0JnT08yZ1pnQnlNK2t6R2NSaUovY0hwcHQwdEZEQXQ1dk5oSlZnZjhYdWRXK2ZxTGhDeHFRcmt5RlUwV0pha2hnR1lWcVVvTWRsdXlRd3JiSzd1TjFCMHN3aDcrQUZ0MEJNWWRIOUtLa0czaEpVZDhFWHRUWWJ0bDg3UTA3SzN0S01sTGc3VlNWTThXckVwOWx6SlpqOXQxZVRDN1dGTElNaEluakJDZjQ3empWUjdGUnh4WVJGV05iSm1NcjVnRGFxb3VJekZtYVFBWFFDRWRwRSswNmFKS2llZmFudmprOXpYRkgwbW9jeGsyS2tjUHl1UXVsTkR1NUMwVkU4R0dxdGZJbm14b2tXQTlUSVJiVk1takM3MWUzMVJQNjVMOHoxQ28wZnBITW5iK0NBZ2F2eGIiLCJtYWMiOiIzZmI3NGI1NzM5N2VjMTAzYWNmOThkODMzMzYyMzY3Yjc1NmQ4ZmQyNWU4OWE5Y2UzYzEwNWUwY2Q5NmE0MThjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023395942\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-783124384 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783124384\", {\"maxDepth\":0})</script>\n"}}