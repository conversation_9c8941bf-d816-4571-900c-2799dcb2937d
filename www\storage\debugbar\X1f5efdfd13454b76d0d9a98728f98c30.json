{"__meta": {"id": "X1f5efdfd13454b76d0d9a98728f98c30", "datetime": "2025-06-07 04:15:06", "utime": **********.824605, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.36077, "end": **********.824686, "duration": 1.****************, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": **********.36077, "relative_start": 0, "end": **********.565401, "relative_end": **********.565401, "duration": 1.****************, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.565419, "relative_start": 1.****************, "end": **********.824692, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "259ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.04127, "accumulated_duration_str": "41.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.665856, "duration": 0.02376, "duration_str": "23.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.572}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.726391, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.572, "width_percent": 4.531}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.768736, "duration": 0.01564, "duration_str": "15.64ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 62.103, "width_percent": 37.897}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269697042%7C2%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjR6SUZKUUh5dzEzUFFFbFUxV0VnSVE9PSIsInZhbHVlIjoiaEk3Qit5cTJxWUhJb09la3VkR3ZhZEhNdFdQcEw5SXRWMzFoU1BiTVF4Y0xqeW1mVkRMUHQ2QnVVc0lqN2NsR1BCWE92azdOOFEwNmdKcnVFSnY1YXpid1hZbVhPNkk2NkdHMVU5WkFIWExUdEdhRGIrMEhCYzlWSmpwQ0pEeXg5cnd0aVE4czFudnFEUzNSN1lRM0xrbjNxb0NVSnp4S2tOc3hHTlJOZ2RpcCtZRGREVUpkeXUyZHRWUHljbjZqbC9HNE12cXlrbGZicm56a1pqY2dPSGZ6dXZna29ZOGU3SG5JVHlPaW9QT3o2NjZFMHhyNTM2eUNPOC9xdmIwNlZVRWRHdW1PU3RSTURsSFhoVThjSzRkdnF6M1RnSGpiQUpYMDlER0wzOGVIR2todjJBb0FVUlcrMjR5Uk4wbEpuaFZXamY3dkNpK1RwS2hteDVIR0xHK0JjVFlLWUZzaVIzMHpzYXRjbTQvNW4wbThKVGlFMFBqT1BqM3ZMVmIrM1RDc1RleVgyUXU2a1QzV0w2aG5EUitFbXprelRzM1U1bG0ydzdINXh1Q2VCQ094cUdKa2gxQ2NtSjVHYk5hTmNTQ0NyZTdMUThPa1h0SnhYbE4rTE16TW1TRTBpM2w3V0k0Nk4wVVpnZ2tSV1lnaldTeHByWUN6WHQrZElSL2wiLCJtYWMiOiJlNWE3MmY0YzM2YWY5NDc3MzQ1NTFjN2M4MzU1NDVmOWM4Mzg2ZWIyNjNiNTdhNDUxMDZkOTFmNDliZmQ1YWU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZlR0dxY3hWVUplNWpKTG1la3BKZlE9PSIsInZhbHVlIjoiNjJzbGtJbDhvUU0rTnJMQkVURlVKT0NVWm85am1KbXlGWVZSL3h0SjZqOVBBbzc5ZG0wcCszR283VGxKeFE2ZnhZN1pzRHN5alRJeTVQQkkyeWZpYlprK2NpMjJOZ1BBVEg2ako3dGNQaW1sR2pJNTIvNXYyRGdSTFplUk5jclcvekZpRTgzeWQzVEhWMkEzekRlczlUTFljNXJVTENpQm90TWlJa3pDVUorZEtiNS9adGFQbktGWlpwbTAwd0JMbHljNVZwajBHWFVyMXQzb0FGemltd3dBdVRzWFJDUU5vUXFTMXF2MzBjNFdrWkd1OWZZQ1ZudzlTRGwrbGdRS0VUTVhIczdqV1A3ajRRd0FpQyt1bktyNWdRbW9mVEhCR0hnb3QzVFBKbVBQYkVYRU12ZnBkZk1RVlZSdm5qS2xVcVZnbW9BaVUyWnhBaEFQdTQrdC9zQ1FWeGdBMlpqL0VZMk05R3pRUWZPbFRwMlpPQjFRSG1SVmtSMjczZDZTZVZFU1ZaSzJlUkE3YnV3RVZrYkF4NG5ORGNrTGUyR0dLQTRreVNvei9GNnpTMWVILzdUeHFycWd0dUoyV2FNak9IZmFCRTBSMzhlSGJZZ3hoaEx1MmZsK0p0TDdDYVJacXVMQ0ZpbXIvVHpodGhnRnl1clV5akFMalllSUJTbjQiLCJtYWMiOiJhOTRlMWFkN2IwZWJhNDViZDRlYTc2N2JhMjdmMDVlN2Q1MDQxNzJiYzQyOWU0YTg0NGYxNjJkZDM0MTY4MWQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-619192466 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619192466\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:15:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNvRUlQTzNGWi9FcnJYcWNQelV3VHc9PSIsInZhbHVlIjoiS1ViSzRjanhOWisrdjFzRnBYczk2KzhMcU1vanpOVGIvWlN0YzdONWg2SGlRQ25TVkFCYmd2SVBSbVZJMW9jQ1VtOVBtaElGaGhOWnZuY0trZEgybTJuRGk1MkxaOGFlSElPM2FzcmpDY1I4NHZXV1ZYcFcxd21uUUJjWTk4Ni9iT3pYbGFsL25YZW53ZVdSNGYrYUNnNHdlN1NPNUpwRGJIeHJZTTVXNGgzSzIwaWpGZ3Bzb0phRHN5V3ZDdzlqU0pBRjRXK3h4WTBsZlRUMmorc3Jab0FtdTVQNXY5R2Z6OThrY1NhNDNLajRCWjZZb2JvMTZRWi9XWmpGZDBNSlBOV2hiWHEzRWVlZ293RDZkb05EYW1oK3RKb25ONmgwRFBkTjFiVDlJZDV1SHRoT3JHZVNodjU1clNMWFVXS1pTTFZKeE01c2xmK1dkaG52d0JIVzQwdDVXNGRZRnlQRVRUMHZ6bWZmVExYWVoxZHhQQWl1SzFGUEliR3dOaGs5WG1qcC9qaXpObDREK0RwMks1eEF4SnBqbGlGdHg1OUg0dUxreGR3OGxIeU9pRW9zOEhSVmk4NWQzZTU2MXVmUUxnU200am43UVRYOU9qQjVhTURjZTVRN0M5NWRWeGlYTUVzZTgzN0RwcUNtUUZremFYWjlHdC9mckJIa1NINHYiLCJtYWMiOiI1NWU2ZGI2MGVhZDVhNGEyZTQ2ZWUwODFlNjUxZGQ3M2I3ZDE0ZmFiZWY3Zjg2NDgzZWZlZjQyOWFiZmQ1NjU2IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJVUThMUCtmcUNpWjBnbVk2NEZ0OVE9PSIsInZhbHVlIjoiZHVpZkc5cXJ0Q3h5eWJITVV2R1lRY1YrWG9UQ21IMFRaTURxcUJia2dCRlE5N2kyT2tmTnA0eWR0OXo4Y0hKS1V6MFNLQWlmMzBxZWpVL01PMnVKUWJYYnFQMC80YWVoNHNnbmtMS0lpdnoyaFFUenh3MCs1SW1QaE02N2d4L0l3M2VObmVhMXBtaUF6aTF5WGF1ZWZLbUhLRTZRTVU4RVdnWE1CTllhNzFmTHNkazA0VEhrSk5EcE5DenZLL3ZLa2tZUWhGdXEra3lkdkgzWlpuQ0sxemtWUDNRbzdDb1l0c3FNbjJ0Ukh4U1plN1VZWkFsQ1pPWEZXVnhUblBIZHFQRjNYQWVIVktPVDgvRjJ0Qi9qSjZ3YVQvSU01ZFlQS3RpeWZYNnIyYkM4K0JVMGFCQWxIak9Zc2ExaXQ5MkpHbTJhRHJTTndUdVVlN1FRNkZ1NitIdlhlTm9CMEJ5eklPdCthYURYUmEvVkZLNGNsZzB3YkpTOGp2VVROUjQvUGNmWEdFamRSOUU3WjFwYk5xWEZvOEtHOC9qTlkwRDFFd2RZTSs0RS9sa21Hek1XRGN2QzUzR0lySzlTd3l6c2d2b09zb3pMQjE4aHJiTlZmMTFRYkE3OTBoT3ZBYmM3Z2UwM01NMnBCZmI5ZnhSN21sR1RrM0NZMVpPN0FMcHMiLCJtYWMiOiJmYzc1Yjg5MTc2ZWZmMGE5NTQ0NzcxMGNjNWQ0ZjQyYWYyMWNmZWU4OWY0OTFjYTE2M2FhYzZmYmUzYjM3ZjU0IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNvRUlQTzNGWi9FcnJYcWNQelV3VHc9PSIsInZhbHVlIjoiS1ViSzRjanhOWisrdjFzRnBYczk2KzhMcU1vanpOVGIvWlN0YzdONWg2SGlRQ25TVkFCYmd2SVBSbVZJMW9jQ1VtOVBtaElGaGhOWnZuY0trZEgybTJuRGk1MkxaOGFlSElPM2FzcmpDY1I4NHZXV1ZYcFcxd21uUUJjWTk4Ni9iT3pYbGFsL25YZW53ZVdSNGYrYUNnNHdlN1NPNUpwRGJIeHJZTTVXNGgzSzIwaWpGZ3Bzb0phRHN5V3ZDdzlqU0pBRjRXK3h4WTBsZlRUMmorc3Jab0FtdTVQNXY5R2Z6OThrY1NhNDNLajRCWjZZb2JvMTZRWi9XWmpGZDBNSlBOV2hiWHEzRWVlZ293RDZkb05EYW1oK3RKb25ONmgwRFBkTjFiVDlJZDV1SHRoT3JHZVNodjU1clNMWFVXS1pTTFZKeE01c2xmK1dkaG52d0JIVzQwdDVXNGRZRnlQRVRUMHZ6bWZmVExYWVoxZHhQQWl1SzFGUEliR3dOaGs5WG1qcC9qaXpObDREK0RwMks1eEF4SnBqbGlGdHg1OUg0dUxreGR3OGxIeU9pRW9zOEhSVmk4NWQzZTU2MXVmUUxnU200am43UVRYOU9qQjVhTURjZTVRN0M5NWRWeGlYTUVzZTgzN0RwcUNtUUZremFYWjlHdC9mckJIa1NINHYiLCJtYWMiOiI1NWU2ZGI2MGVhZDVhNGEyZTQ2ZWUwODFlNjUxZGQ3M2I3ZDE0ZmFiZWY3Zjg2NDgzZWZlZjQyOWFiZmQ1NjU2IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJVUThMUCtmcUNpWjBnbVk2NEZ0OVE9PSIsInZhbHVlIjoiZHVpZkc5cXJ0Q3h5eWJITVV2R1lRY1YrWG9UQ21IMFRaTURxcUJia2dCRlE5N2kyT2tmTnA0eWR0OXo4Y0hKS1V6MFNLQWlmMzBxZWpVL01PMnVKUWJYYnFQMC80YWVoNHNnbmtMS0lpdnoyaFFUenh3MCs1SW1QaE02N2d4L0l3M2VObmVhMXBtaUF6aTF5WGF1ZWZLbUhLRTZRTVU4RVdnWE1CTllhNzFmTHNkazA0VEhrSk5EcE5DenZLL3ZLa2tZUWhGdXEra3lkdkgzWlpuQ0sxemtWUDNRbzdDb1l0c3FNbjJ0Ukh4U1plN1VZWkFsQ1pPWEZXVnhUblBIZHFQRjNYQWVIVktPVDgvRjJ0Qi9qSjZ3YVQvSU01ZFlQS3RpeWZYNnIyYkM4K0JVMGFCQWxIak9Zc2ExaXQ5MkpHbTJhRHJTTndUdVVlN1FRNkZ1NitIdlhlTm9CMEJ5eklPdCthYURYUmEvVkZLNGNsZzB3YkpTOGp2VVROUjQvUGNmWEdFamRSOUU3WjFwYk5xWEZvOEtHOC9qTlkwRDFFd2RZTSs0RS9sa21Hek1XRGN2QzUzR0lySzlTd3l6c2d2b09zb3pMQjE4aHJiTlZmMTFRYkE3OTBoT3ZBYmM3Z2UwM01NMnBCZmI5ZnhSN21sR1RrM0NZMVpPN0FMcHMiLCJtYWMiOiJmYzc1Yjg5MTc2ZWZmMGE5NTQ0NzcxMGNjNWQ0ZjQyYWYyMWNmZWU4OWY0OTFjYTE2M2FhYzZmYmUzYjM3ZjU0IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-18******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18********\", {\"maxDepth\":0})</script>\n"}}