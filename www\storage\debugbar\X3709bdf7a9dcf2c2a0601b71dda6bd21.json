{"__meta": {"id": "X3709bdf7a9dcf2c2a0601b71dda6bd21", "datetime": "2025-06-06 19:35:45", "utime": **********.440319, "method": "GET", "uri": "/users/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238543.468954, "end": **********.440355, "duration": 1.9714009761810303, "duration_str": "1.97s", "measures": [{"label": "Booting", "start": 1749238543.468954, "relative_start": 0, "end": **********.060267, "relative_end": **********.060267, "duration": 1.5913128852844238, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060288, "relative_start": 1.5913338661193848, "end": **********.440359, "relative_end": 4.0531158447265625e-06, "duration": 0.38007116317749023, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51159496, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x user.create", "param_count": null, "params": [], "start": **********.410535, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/user/create.blade.phpuser.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fuser%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.426804, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET users/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.create", "controller": "App\\Http\\Controllers\\UserController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=51\" onclick=\"\">app/Http/Controllers/UserController.php:51-62</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02883, "accumulated_duration_str": "28.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.197878, "duration": 0.02094, "duration_str": "20.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.633}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.257971, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.633, "width_percent": 3.538}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.269139, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 76.171, "width_percent": 4.613}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.281329, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "UserController.php:54", "source": "app/Http/Controllers/UserController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=54", "ajax": false, "filename": "UserController.php", "line": "54"}, "connection": "ty", "start_percent": 80.784, "width_percent": 4.509}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2928722, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "UserController.php:56", "source": "app/Http/Controllers/UserController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=56", "ajax": false, "filename": "UserController.php", "line": "56"}, "connection": "ty", "start_percent": 85.293, "width_percent": 5.238}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.351663, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 90.531, "width_percent": 5.376}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.361696, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.907, "width_percent": 4.093}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-468565460 data-indent-pad=\"  \"><span class=sf-dump-note>create user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468565460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.377519, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/create", "status_code": "<pre class=sf-dump id=sf-dump-423744603 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-423744603\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-672021130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-672021130\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1836448336 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1836448336\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjVsbVNEL1BkelIvQlpndzhYbjRiOEE9PSIsInZhbHVlIjoiS2k4RUp5M2ZnUW94VWxhL0h3ZXppZlJlS1hWWVBEYmdBSE95cEZaV3B4Y0lvSFp6SXVqM01LME1BL05VU0FnOE9mdWw2L0FvdVNmUVZhUWMvNUJjcDdqU1lVM1NnbWJCNmp2UmNNWk5JMzBPUnJqeStSTXlrbmZlRkRYZ3cyQkwyb2ZVL3o5c1JOZkZnTXI2WDdMWnNBVS84RXpGczJ5VzNkTUl2UHFLamlWVGxqWnVOczMvR0htcmNFRDY4VC9TQTBUUHZnVVlmYnM5VG5QdytqbGdUdHlSckQxVXJEMTI1QkhkR2hGeUM1R2x3WSt5M0J2YldoT3dhdThpTmRaTjFJNThodTVXU0JrSFVQakdMaUpGVnJFSXhtREQ3UTRSS3RqUXd2ZW9UMzUybGVBZ2htcDFva25pM1dKbHpPTXpFSEtVamtzVFBtWG96aStWYk02NzBFSmlQeXNURUhySEh2K3FDa0FibnNNbEpTeFZJeUhwM3FZeEc2T1Q4a1grTjdFcFRldloyaS9oMDBjdHlpc3ZDOXlTbEpVYmMrT000NWdOVGJhRitjUUtJN3RXd0JZQ0toMUY4Qi9Bc0wwZmFQNXIwbzNPbWNJUTlKak9qTDB4d3I1YTJzeGlsRXE1ZGVxRDliWnVKbU1vTmNEWHNQVDdSbitBWVB4S0E5ZFciLCJtYWMiOiJjNzdmZDA2MjM4NjIyZTU5M2ZjOWI0YWMxYzUzNTU5YTJlMGU5MWM0YWQwZGZhZDhmZWNhM2NhOGFhMWQ3Mjg0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBsZFJRd2x4RDdzWU5EMUcvYnhCOHc9PSIsInZhbHVlIjoiVExZTDRFbHNSQWcrT3ZRdjNvTGhBcm1iL29Wdy9VMW5ndEdQanNQMjBIa0Z4SDJlZHlJZXpqNmRNNmhlcGxNOVVyU1JTeGJ0bTNhS2Q4WWdkWUFjbHNsMzRlcVBUOUt1S2dsNXQyUEdrck95dlNLNmF2RzlmcFFxOXZ5T1BjdCt6ZElnZUNPRUVmQXd2bCszV1B1QkE2TW56d1YwTUtqc3FVT0NmK2ozM2plQnJtU2RNUjlXdzdVTk5ZTHlLbkJ4bzc4VVE2b3dqZWYzbjYvNmRDRlN2T1Qwdi8yeUJYQ3gxRzZQd2ZKbXhRUDd0TGowQm5yQmxTazJNOEltTFhiTHBqOEpQQ3FudEdvM3REZ3F6NFNva1ZvNHlxQTBGN1hvajJUQTBoMS8wR3o0dnJFT09uZkEzMXlxYmVoOVR2UmNseGNnbjgzTHJ2VUE2VlpnTUZhME85N2tQN1U1blo3Tjg0SmREb0NCd21qQ0FaU1NMKzE1Y1Jqb3h6SXlHYUU0b2tZemtYYXNUb2pRRXprUUo0b2NqNGZnV3gwb3BpelgrVEQxbm9CNmZGMk9qbmlsa01UWFZEWE40NGJRNHdVWDk5bTlwS0c2V2V1WEMrK3dWMklXaDVxa3VwenV5RDRWYUI4LzE0empEQXQwanpvU0IrZ0VSeUV6c1RXN2FuTUsiLCJtYWMiOiJmOWFlNmE4MmMxZjU4MTU2ZWY1Zjk1MmE4NWM3YTk3ZTk2YWZiZmQ1MmVkZGRhYjZhMzc5OWE4NjQxNWExNzRiIiwidGFnIjoiIn0%3D; _clsk=mp51by%7C1749238542561%7C49%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-77887002 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77887002\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1870214037 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:35:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdsMGl5SytiSUJyampDZ2lUZnYvbWc9PSIsInZhbHVlIjoiN2FRMWJFL2VUOTVvb1pEUm4zb3Q0ZEJVeWROUVIwNmdxT0NDVldKNEZubWo3SzZwdGVQbytPNUVYWFBQR1AwT053WTI5Qy9xd05uS3J5WXBjVUxVTlRyT1o4RWdNUTFncW5OWG1HUVdNYmhWcHVFRjExdkZMT21BL2JHNkQxZVJ0bldIc0UvZFNUcFhCYU1jaTV0Ym5UcXh2OHVXbFJjenV2bG0rUTh5TUZuYU1oN2ZwVUdJcHNGVzU2T0REMmhtNGo2cmpQZ2h3QUw5a2padVVBV0tGQU9vZE9XKzMyUUZUeXZnREg4Y3JXU3RnM3dvTkdSMlV5TVAvSytWNEZPR3RXd2YxYi8zSmNZMGRBcG1teTFtTkpHWUdVMXVmcmRlZGViK2hpRDRkOHpxL2NtZ21LMVBodDFJdU5nWmFTaTZFQ0hDdDR5dFIzZVRhcFdGTXdTR0FaTzcyRHBOMXdmazB5Z3Y4ZmpwWVo2YVgxRC82bjMrZ1NsNFQvc3kwczYvYnVuUGpYenF3S1BUK281NWZmZDN0M1A0VDVWcVEyeGtuZXBFV1lPUEpYeERRdVVuWTdGcm4zbDZmNmJiZ2NQVC9Tc0JET3NDKzBhRjJWeEkwQ2hBQS9vTjVRa2xJVnRrWlZqVURUeEQ5bGJVYmJhSzhRZzdpc1MwZ0F6cjIyNHkiLCJtYWMiOiIyMGNiNjVmMzhiMGM5OWNiMDAxM2EwYjhhZWQ4YTc0MDY3YjMzNmFlMWQ5MmM2YmZlNzJjMWFkNWU4ODJhYzE0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhOVU44dzlSd0FjcUNOL2tzQTFPa3c9PSIsInZhbHVlIjoiV3N2WExwc2lYWWFyL1BxOG51SnpPZGh3bTlXMU03RXNtTVpQVE83QWRiTUUydTlqOVEvZVM0YVdXY2VJY0xuZE9TRHlVUVhma284cGUweGU0R1c0bUI1cmtVaTNDODRmTGxWK0pHY2Qxb0pQY2UyajlSZEZZY1lwcjJia3piemp1YU9FbVNGcEJVdmlmeHhmMGFYMjRCREw5d3MwbCtaZ1VSTEdWdGJTbkU2QWFUSlJzSVp5NGxqZVVGRi8wVnduVkdoN1c2dGZNR1ZyZU5vM2diWUkzOXdTLzJJSXRGUkl4aHFacGprbEJNZ0ZZTml5VFB6blB6Tyt1Tk5wTEdoYU1oQ1J1QVh6U1VoS2djN3FGSEgzUkIvZzVKOXRNVFNNUXJxZERETm1kdnY1UHZJT2paajFKQkJENW9nTTEvbVRtaUFIYkdSN05IeGxuWnhxc2lNVHdrUFVnc1JJNUxDSDF3U0pCYmJlOUxZRFo3TFJiV0Y1Y0JvSndrZlhCME5xb1hCWjRtYnFCUzcxOFh3MUNuMVNzTSt4a3JLOERZUEowVG1WUVlEZjRmOGw5ZXgybFlCUEVrT1pXaG9CNkhUUkZ5SEV2U292N2ZucTR2clR1VVIrRmgvdTZSbUNlYytEcXZ0dFlOSjgyR0VBK1JPYi9yMnQyVlozSElRcWhlcFoiLCJtYWMiOiI4ZjdkZDg0NGM3ODJmODg3NDA1NzcyZTQ5NmNjZmMwZWQ1MjUwZTEyNzc3MzlkNGM3OWJjNDg1MjM0NThkZDBjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdsMGl5SytiSUJyampDZ2lUZnYvbWc9PSIsInZhbHVlIjoiN2FRMWJFL2VUOTVvb1pEUm4zb3Q0ZEJVeWROUVIwNmdxT0NDVldKNEZubWo3SzZwdGVQbytPNUVYWFBQR1AwT053WTI5Qy9xd05uS3J5WXBjVUxVTlRyT1o4RWdNUTFncW5OWG1HUVdNYmhWcHVFRjExdkZMT21BL2JHNkQxZVJ0bldIc0UvZFNUcFhCYU1jaTV0Ym5UcXh2OHVXbFJjenV2bG0rUTh5TUZuYU1oN2ZwVUdJcHNGVzU2T0REMmhtNGo2cmpQZ2h3QUw5a2padVVBV0tGQU9vZE9XKzMyUUZUeXZnREg4Y3JXU3RnM3dvTkdSMlV5TVAvSytWNEZPR3RXd2YxYi8zSmNZMGRBcG1teTFtTkpHWUdVMXVmcmRlZGViK2hpRDRkOHpxL2NtZ21LMVBodDFJdU5nWmFTaTZFQ0hDdDR5dFIzZVRhcFdGTXdTR0FaTzcyRHBOMXdmazB5Z3Y4ZmpwWVo2YVgxRC82bjMrZ1NsNFQvc3kwczYvYnVuUGpYenF3S1BUK281NWZmZDN0M1A0VDVWcVEyeGtuZXBFV1lPUEpYeERRdVVuWTdGcm4zbDZmNmJiZ2NQVC9Tc0JET3NDKzBhRjJWeEkwQ2hBQS9vTjVRa2xJVnRrWlZqVURUeEQ5bGJVYmJhSzhRZzdpc1MwZ0F6cjIyNHkiLCJtYWMiOiIyMGNiNjVmMzhiMGM5OWNiMDAxM2EwYjhhZWQ4YTc0MDY3YjMzNmFlMWQ5MmM2YmZlNzJjMWFkNWU4ODJhYzE0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhOVU44dzlSd0FjcUNOL2tzQTFPa3c9PSIsInZhbHVlIjoiV3N2WExwc2lYWWFyL1BxOG51SnpPZGh3bTlXMU03RXNtTVpQVE83QWRiTUUydTlqOVEvZVM0YVdXY2VJY0xuZE9TRHlVUVhma284cGUweGU0R1c0bUI1cmtVaTNDODRmTGxWK0pHY2Qxb0pQY2UyajlSZEZZY1lwcjJia3piemp1YU9FbVNGcEJVdmlmeHhmMGFYMjRCREw5d3MwbCtaZ1VSTEdWdGJTbkU2QWFUSlJzSVp5NGxqZVVGRi8wVnduVkdoN1c2dGZNR1ZyZU5vM2diWUkzOXdTLzJJSXRGUkl4aHFacGprbEJNZ0ZZTml5VFB6blB6Tyt1Tk5wTEdoYU1oQ1J1QVh6U1VoS2djN3FGSEgzUkIvZzVKOXRNVFNNUXJxZERETm1kdnY1UHZJT2paajFKQkJENW9nTTEvbVRtaUFIYkdSN05IeGxuWnhxc2lNVHdrUFVnc1JJNUxDSDF3U0pCYmJlOUxZRFo3TFJiV0Y1Y0JvSndrZlhCME5xb1hCWjRtYnFCUzcxOFh3MUNuMVNzTSt4a3JLOERZUEowVG1WUVlEZjRmOGw5ZXgybFlCUEVrT1pXaG9CNkhUUkZ5SEV2U292N2ZucTR2clR1VVIrRmgvdTZSbUNlYytEcXZ0dFlOSjgyR0VBK1JPYi9yMnQyVlozSElRcWhlcFoiLCJtYWMiOiI4ZjdkZDg0NGM3ODJmODg3NDA1NzcyZTQ5NmNjZmMwZWQ1MjUwZTEyNzc3MzlkNGM3OWJjNDg1MjM0NThkZDBjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870214037\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-483387994 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483387994\", {\"maxDepth\":0})</script>\n"}}