<?php
// إضافة هذا الكود إلى ملف routes/web.php

//for financial record
Route::get('pos-financial-record', [FinancialRecordController::class, 'index'])->name('pos.financial.record')->middleware(['auth', 'XSS']);
Route::post('pos-financial-record', [FinancialRecordController::class, 'SetOpeningBalance'])->name('pos.financial.record.opening.balance')->middleware(['auth', 'XSS']);
Route::post('pos-financial-record/closing-shift', [FinancialRecordController::class, 'closeShift'])->name('pos.financial.record.closing.shift')->middleware(['auth', 'XSS']);
Route::get('pos-payment-type', [PosController::class, 'posBillType'])->name('pos.billtype')->middleware(['auth', 'XSS']);
Route::post('pos-payment-type', [FinancialRecordController::class, 'financialType'])->name('pos.pos-payment-type')->middleware(['auth', 'XSS']);
Route::get('pos-financial-record/opening-balance', [FinancialRecordController::class, 'opinningBalace'])->name('pos.financial.opening.balance')->middleware(['auth', 'XSS']);
Route::get('pos-delevery-pay', [PosController::class, 'deleveryBillType'])->name('pos.delevery.billtype')->middleware(['auth', 'XSS']);
Route::post('pos-delevery-pay', [FinancialRecordController::class, 'finacialdeleveryBill'])->name('pos.delevery.bill')->middleware(['auth', 'XSS']);
