{"__meta": {"id": "X0fe9cf8f237957aeb61eff74d3656e71", "datetime": "2025-06-06 19:34:06", "utime": **********.86579, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238445.014155, "end": **********.865828, "duration": 1.8516731262207031, "duration_str": "1.85s", "measures": [{"label": "Booting", "start": 1749238445.014155, "relative_start": 0, "end": **********.640229, "relative_end": **********.640229, "duration": 1.6260740756988525, "duration_str": "1.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.640256, "relative_start": 1.626101016998291, "end": **********.865833, "relative_end": 5.0067901611328125e-06, "duration": 0.22557711601257324, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01101, "accumulated_duration_str": "11.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.765818, "duration": 0.00592, "duration_str": "5.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 53.769}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.805531, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 53.769, "width_percent": 14.441}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.815433, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 68.211, "width_percent": 10.173}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8378592, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.383, "width_percent": 21.617}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-199989711 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238343986%7C43%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRCZTJqNElHOFpJaFhJVi9KWE53TVE9PSIsInZhbHVlIjoiZXFGd2lhZk9Fb3lIM0FvbU1EL2EyZXBxa2tBWmt3dk5mWk9vYVdCK3lEdlM2ejkzN3ViWHJldTZzZHo2TzNyK215cHgxSGNJcjZvdUw4S1ZhYk9POU1jb1h6OWF4bElTZGlsTHB2eDdQaklJalAyV1hpUXF2a2t4azFwQjBvZ3JGek5wRE54aHh3OUJrcEt0UjY5a3BKSjJmRHd0WURDdTMxQllCQWJTbUQzYnMwTlUrSWFJblI5Y25QbStwNzZFditJY2QralhWdHhPbzhMNnJnRzl6dFNHTVFVR3VmNGcrdmxoQ0Njd01IRGc2RS9Cc21CQjlOTkFjSFB0QTJuOTFmWXlTdlVaejFqMlQ1TnA4QWdCOUkvMWNSaUxhNEtRMjJ2dUNhYnUxR21yMzVHdzRZR1BkcDdzdXVBV29GcUNVRDVXU1ZvTHJhbEpLN0dpdFNJR1hGTWk5TnBSVEFGVTluWFhnR1NFZlFHendYRU0xWmpRb1dVQmI1d1NLZm5iMTlUOHdhTk5tMHNnL2M2bnRJMGdLaG1zUWk2dzNxbzFJeDhUQ203ZEx4ejFZMUU1cjFpeXZ4YlcvUVlYVUNvcXY4NitmaWl6c2Z6MnZrbFNRUlZOSVlQdVRWRTJrUEY1Zjd0bDR1UEVoMnBiQ08zSHVEdTJPZjdTRWFaN0djNjUiLCJtYWMiOiJhOTUzMmZhNWM3NDY4ZjhkMzNhYThhY2I5NDE0ZTkxZjAxODRlMDk5NDU0MjY1YzMxYTJiZGIxYWZkMGE5NzdkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjRWVGp3d2hLcTYzU2RoQlFUZm9ad1E9PSIsInZhbHVlIjoiLzhJT20yNHNBcy9rTmFiT3dUYkt1WU1BQ2lLSzR6NGJyN3JsclJMWHJtTGFmazdBUUN6MUp2QzdHUDVwaS92YUQyZmhWVDNVQmtqSHc0azRqZ0RBSE5qd0lUZTV5dHJhdnVhSWtyK08zTk9iQnpCdGZ4dFRVQmJGbnRnUXdYdHdXU2d2NlZaeERLUGlQU0dRRml6akFpRi81RTdGaHNkQmwzZ0hBaUlDZ1dyUjJRQ1RCcEZxL2laY2gyT1hQSXJmVVVXaEI2aEppNXdtOFlsZzFGN3UvclFHSDEwMXdIcE80NzN6TXVMVElPZXlQM0cvN21TLzNlb1k5NVFHNklHRE5UOHhSekJaTlpSdXY5V05TREVyS0lvZjk0YmJCVHBuVENOZG1YeEVRY3hBN1dBL3d5Tjg2aDlDbkxtQzFXWElYd0ZLWnk4T3dkMFR3VU5SR3JKVWF2S0UwbDV3dW00S2FOYjRlQzZxRHM3UDJ4NmZaRThYc1J5WXI1QUFLbTBjckkycEEvS3QzVk9LdEFUVnBwcy84azgzYUN2cDNiNURoOTVUK2FWMUQ3cm5zRERXY0dQZEpJWmJ4Q2d5N21FRUpMSXdXeC91VjRxWFlFMGl1Z2xWRUwrVFJjTDlHWmpONVo2VURGTElpQkFTYW5UU0w1MHMveXhTRE1xM2lvcVIiLCJtYWMiOiI4ZjhiODQwOTFjMGNhOTllMDcxZWQxYTRhNzExNDFlMDQ2YTE4NzZiMGQ1ZGRkOGE4NDc2MzdlYWIwMDQ5NDkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199989711\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-552050160 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-552050160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-960554674 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:34:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJ6NEhGL3lsT0NzSzA2YjdISU1rRGc9PSIsInZhbHVlIjoiTzlFbzF5RG4vcDNRWHAxRDVzM0ZMNnl2a05mbUJuVzZtR2kxNmwrcGVoOVhlVGUyN1BhYVZ4MXA3aXZHVWdDUmxLcTVQVmEwaXdKd3hWYnNJU0xORk1yaElRNHg1TWl6RlFVTWxhaFV6V2FPSHkzWWw4SmxsN3lnbEFibW1PVTh4UGFjN2t5dVZRVUNVdjBWaWZ2WjRFKzBOWUR3cEt6TTNiMW0yOEFjQ0djNy9yS25sSzkvL3dzL3BrdGxDa3JQL0l0bDBjayt6bjF1b0FGRXhJM1QzV1VHZzZmVXFUT0pCb3R2VElVYW9KM1B2UjVzbU16dk9DcHFnZzRPM1RUNGxkWGFzMjVMZis1MUNEcVovcnIvVE8vYlhFWmcvVmxMYU4vMC9TeVFJbmFDaCtyMDZoVmgvcW9BbERVQy9GS0VrTDZvdFpvWmlHcnJXcmpiUGRvV1lEazdDdHdselV3Mms1NGo0L0NmcnhmdzBDcXIzSGtzd3lSeXl5R2JGaG5SazQzYnBIcHI3dDFpQVozUXJRcS9qRG96NVd0N1AvcEcxcEo3TWJxYTdQb2ZLWndTTkRnbktqanIxY0U1c1pGczY1WitXZkx0eFFjVUlHQUROY3pyOGpObklLcXBrZzh1cUtTSk91WlVMQTV5dFVqSklEbC9XbU42bDh1dzRYQmwiLCJtYWMiOiJlMjRlZTk1YTdhNTBkZGE1NzY3NjJkYmE3MGEzMzc5NzlkOGI1NTQxODJjMGViMzQ1OGQwNjE0YWE5ZTU4YjlhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlYwaTlYRnZDdzFOOUtoRW1KTHUzK0E9PSIsInZhbHVlIjoiZmRkWVNOSnhCTnRQL29hQmhpNzJyeExySm16OHJDL3IvZksxV2hTYnEyQW5DQ0FGTEh0Q25FSUd0TFRaeUZWUHBrbjBPT05rSENaUGRMUlNGNU8vVHd5SkhTNFhESjduWk1pRXU2UDJ3dGJoWmI2dWtqZkNia1g0RG83K0ZkT2xZcm9wR291LzRBb0x3aEsrTFBSdk9MZ0k3b2d3ZHp6RHBpRldaVysrNUFyQWRZSWZMSFF5ZzhKUkNXbEMyRURCSUdnVUx0QlpKMXpiK0c3a0ZwSm1Gb0l0OWVFUFUwaWlSMitFaXJDMUFSd2lFT1hTU2ZiMWVyeUVQNTNZOWRKQjlZaEhxQUpkNW5SUlZaejQ5bkh0ckxZUHlMVlJwbmpsdDA1U3FuTUlRNDN3eURrQTJMV2hNVHNJZ0NPdk1CZUY1VythalRwS3RNNWk5YkJIeTA5ZHordGRpb3cvUjh6ZEtuMzBHTDFXQ3dlVmRuTGIzOENjZ1JTdTd3SEJIczhhS3djVll6ZWtPN3B6Ni9Ua0d5WWJGZm95ZFhWSFRPaE9IdkphTjdpRXQ1YklvdEpaaU1SOE9aeUtwdit4WE50MFlMNnRrZnpNU0QrSDZia09lbGJEeHg2ZE1aU1MxZDYwNlhoN0ZjaHBrcWxyWHBKckFhdGVESm9XanNtelN3RzUiLCJtYWMiOiI4NzA3ZjRmNjcwYWQxYjE0ODBiOWE0ZmEwMjE5Y2Q0OWY0NjZkZGU4YWMzM2U2ZjQ2YTc4YTllYjU3OGIwYjUwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJ6NEhGL3lsT0NzSzA2YjdISU1rRGc9PSIsInZhbHVlIjoiTzlFbzF5RG4vcDNRWHAxRDVzM0ZMNnl2a05mbUJuVzZtR2kxNmwrcGVoOVhlVGUyN1BhYVZ4MXA3aXZHVWdDUmxLcTVQVmEwaXdKd3hWYnNJU0xORk1yaElRNHg1TWl6RlFVTWxhaFV6V2FPSHkzWWw4SmxsN3lnbEFibW1PVTh4UGFjN2t5dVZRVUNVdjBWaWZ2WjRFKzBOWUR3cEt6TTNiMW0yOEFjQ0djNy9yS25sSzkvL3dzL3BrdGxDa3JQL0l0bDBjayt6bjF1b0FGRXhJM1QzV1VHZzZmVXFUT0pCb3R2VElVYW9KM1B2UjVzbU16dk9DcHFnZzRPM1RUNGxkWGFzMjVMZis1MUNEcVovcnIvVE8vYlhFWmcvVmxMYU4vMC9TeVFJbmFDaCtyMDZoVmgvcW9BbERVQy9GS0VrTDZvdFpvWmlHcnJXcmpiUGRvV1lEazdDdHdselV3Mms1NGo0L0NmcnhmdzBDcXIzSGtzd3lSeXl5R2JGaG5SazQzYnBIcHI3dDFpQVozUXJRcS9qRG96NVd0N1AvcEcxcEo3TWJxYTdQb2ZLWndTTkRnbktqanIxY0U1c1pGczY1WitXZkx0eFFjVUlHQUROY3pyOGpObklLcXBrZzh1cUtTSk91WlVMQTV5dFVqSklEbC9XbU42bDh1dzRYQmwiLCJtYWMiOiJlMjRlZTk1YTdhNTBkZGE1NzY3NjJkYmE3MGEzMzc5NzlkOGI1NTQxODJjMGViMzQ1OGQwNjE0YWE5ZTU4YjlhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlYwaTlYRnZDdzFOOUtoRW1KTHUzK0E9PSIsInZhbHVlIjoiZmRkWVNOSnhCTnRQL29hQmhpNzJyeExySm16OHJDL3IvZksxV2hTYnEyQW5DQ0FGTEh0Q25FSUd0TFRaeUZWUHBrbjBPT05rSENaUGRMUlNGNU8vVHd5SkhTNFhESjduWk1pRXU2UDJ3dGJoWmI2dWtqZkNia1g0RG83K0ZkT2xZcm9wR291LzRBb0x3aEsrTFBSdk9MZ0k3b2d3ZHp6RHBpRldaVysrNUFyQWRZSWZMSFF5ZzhKUkNXbEMyRURCSUdnVUx0QlpKMXpiK0c3a0ZwSm1Gb0l0OWVFUFUwaWlSMitFaXJDMUFSd2lFT1hTU2ZiMWVyeUVQNTNZOWRKQjlZaEhxQUpkNW5SUlZaejQ5bkh0ckxZUHlMVlJwbmpsdDA1U3FuTUlRNDN3eURrQTJMV2hNVHNJZ0NPdk1CZUY1VythalRwS3RNNWk5YkJIeTA5ZHordGRpb3cvUjh6ZEtuMzBHTDFXQ3dlVmRuTGIzOENjZ1JTdTd3SEJIczhhS3djVll6ZWtPN3B6Ni9Ua0d5WWJGZm95ZFhWSFRPaE9IdkphTjdpRXQ1YklvdEpaaU1SOE9aeUtwdit4WE50MFlMNnRrZnpNU0QrSDZia09lbGJEeHg2ZE1aU1MxZDYwNlhoN0ZjaHBrcWxyWHBKckFhdGVESm9XanNtelN3RzUiLCJtYWMiOiI4NzA3ZjRmNjcwYWQxYjE0ODBiOWE0ZmEwMjE5Y2Q0OWY0NjZkZGU4YWMzM2U2ZjQ2YTc4YTllYjU3OGIwYjUwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960554674\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1********\", {\"maxDepth\":0})</script>\n"}}