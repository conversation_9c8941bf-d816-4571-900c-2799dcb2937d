<?php
require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Image Display Debug ===\n\n";

// Test user avatar display
echo "=== User Avatar Display Test ===\n";
$users = \App\Models\User::where('type', '!=', 'super admin')->take(3)->get();

foreach ($users as $user) {
    echo "User: {$user->name} (ID: {$user->id})\n";
    echo "Avatar field: " . ($user->avatar ?: 'NULL') . "\n";
    
    // Test the exact code from user/index.blade.php
    $profile = \App\Models\Utility::get_file('uploads/avatar');
    $avatarUrl = !empty($user->avatar) ? 
        \App\Models\Utility::get_file('uploads/avatar').'/'.$user->avatar : 
        asset(\Illuminate\Support\Facades\Storage::url('uploads/avatar/avatar.png'));
    
    echo "Profile path: {$profile}\n";
    echo "Generated avatar URL: {$avatarUrl}\n";
    
    // Check if files exist
    if ($user->avatar) {
        $storageFile = storage_path('uploads/avatar/' . $user->avatar);
        $publicFile = public_path('storage/avatar/' . $user->avatar);
        echo "Storage file exists: " . (file_exists($storageFile) ? 'YES' : 'NO') . "\n";
        echo "Public file exists: " . (file_exists($publicFile) ? 'YES' : 'NO') . "\n";
    }
    echo "---\n";
}

echo "\n=== Company Logo Display Test ===\n";
$companies = \App\Models\User::where('type', 'company')->take(3)->get();

foreach ($companies as $company) {
    echo "Company: {$company->name} (ID: {$company->id})\n";
    
    // Get company logo settings
    $settings = \App\Models\Utility::settings();
    $logo = \App\Models\Utility::get_file('uploads/logo');
    $company_logo = isset($settings['company_logo_dark']) ? $settings['company_logo_dark'] : '';
    
    echo "Company logo setting: " . ($company_logo ?: 'NULL') . "\n";
    echo "Logo path: {$logo}\n";
    
    if ($company_logo) {
        $logoUrl = $logo . '/' . $company_logo;
        echo "Generated logo URL: {$logoUrl}\n";
        
        $storageFile = storage_path('uploads/logo/' . $company_logo);
        $publicFile = public_path('storage/logo/' . $company_logo);
        echo "Storage file exists: " . (file_exists($storageFile) ? 'YES' : 'NO') . "\n";
        echo "Public file exists: " . (file_exists($publicFile) ? 'YES' : 'NO') . "\n";
    }
    echo "---\n";
}

echo "\n=== Personal Info Display Test ===\n";
// Test personal info page avatar display
$user = \App\Models\User::where('avatar', '!=', null)->first();
if ($user) {
    echo "Test user: {$user->name}\n";
    echo "Avatar: {$user->avatar}\n";
    
    // Test the exact code from user/profile.blade.php
    $profile = \App\Models\Utility::get_file('uploads/avatar');
    $avatarUrl = ($user->avatar) ? $profile . '/' . $user->avatar : $profile . '/avatar.png';
    
    echo "Profile path: {$profile}\n";
    echo "Generated avatar URL: {$avatarUrl}\n";
    
    // Check accessibility
    $storageFile = storage_path('uploads/avatar/' . $user->avatar);
    $publicFile = public_path('storage/avatar/' . $user->avatar);
    echo "Storage file exists: " . (file_exists($storageFile) ? 'YES' : 'NO') . "\n";
    echo "Public file exists: " . (file_exists($publicFile) ? 'YES' : 'NO') . "\n";
}

echo "\nDone.\n";
