{"__meta": {"id": "X00c99b020d433593516c021c19514cfd", "datetime": "2025-06-07 04:35:03", "utime": **********.775242, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270902.368327, "end": **********.775275, "duration": 1.4069480895996094, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749270902.368327, "relative_start": 0, "end": **********.648828, "relative_end": **********.648828, "duration": 1.280501127243042, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.648857, "relative_start": 1.2805302143096924, "end": **********.775279, "relative_end": 4.0531158447265625e-06, "duration": 0.12642192840576172, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43109136, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0056, "accumulated_duration_str": "5.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.743808, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dlfDXgpc5LHP5CUsgqs70zFckvDPl2hF5UQUwh9L", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-513824862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-513824862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-709568573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-709568573\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1739505793 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739505793\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2025203609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2025203609\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1523493198 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:35:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFqYlBCeW1Hb09MdG9RdHhlUXdpUkE9PSIsInZhbHVlIjoiaXZkN2x1MjRLVm9VYm5raTEwWkhzajQ1Z3VWU0Z5QkgxL1N6MEppaS9Bd3dDOWVLVmxrMCtFVzVPVTgzNmIzOWVhT0dxNXRST1ZidzJBZWpDczFTRG9aUVR4SlJGVW1CUWF2Z2lYdnJSQ2RqcHFBZ0pTdGtVTE5pSW9NTHJURlNqVitWcm1XM2wvOTE5eXJZY0ZOSFhaTktWR3V6cnpsbXVTRjY4b2xLemRUL25iNy9HNHFob3JEYzZESDRWNXBYSTRVUTNDemhqTEZKTXZ2Sm50RTZZUXYzY3BMK3dFZnZIMWh5bDU1Z1YwVnlrZEFtMXYzTEpZR1FUcnNtSG5wL2UrRWh4TlZzSk9xU2xNQmJFNmRCNkFNYjlBL2tOTEdYZkROaFRaSzZZT3JwR3NxakdwdlZDVzllSDRMTnlsSE9uVFJ6K0xZano3elpZOWZzd0FIZXBYQ3ZYazhkVW16aE1TVWJKWHp6M1FKUUdnbU1KdW5zQVlWNFRmbjBSNkR4SXFFcm54STRhcUs0YkJmTldZZnQzeVUyUTZKMFUxWGtRUHlGV2dnOGlQRis0UzNvelptWVZkRjBaNS90SWQ1TXpqNHJpeGVNYlgvaXJPamQvaTJJZXp1aStWVTgybWZvc21wS25OSkNGVEt5a0hrcmRueUdWNE5PWWxvbW5FTkUiLCJtYWMiOiIwNTE4NDQ1Y2RmZDkwMjEzOGE2M2JhZTJmNDIxMmNhZmI1ZDQ5OGVlYzQ4Y2MyMTJmZTU3Y2QwM2E1YmE1NTUxIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxJVEl1T0ZiNWpxWStRWmRLZnlGVWc9PSIsInZhbHVlIjoidWJxWmYrTXpJQUt1eGJFbmhRVE8veWRBdHdBV3ZUZGpVZ282U0l2ZnBzOFo4c1lWbWxzaEZVYWc5WXdMUVlOWlk3K1huVnU2UFZ5UU13Q3JiNjRvMGF1bUVNb3RmSmtNdFlMbTJjVFhEbXBHRUt5bDhrYmhkVWwwZEV5QnhURTFQWlZ3RVRuK2E4L3RWd2VFVUtDcE9qSFhkWUQ2SzZvWXMrNnlaSmNQNVo0c2QvVm9wT2hYcHF0UjVsWWs3b2JXZGtpQlhpamd0SkcwT3hjY3Erck91aXhMMVJLNUE1NU9RK3V0VWgybE9BbmdDQno5UWJ2Q1lYczR3Mjl3eXUrK3UxMTVkak9xQS9uNDFYWnBtMjhxalJ0VU1nelV3VERzV3pCMXRyTytHMnNSa3pNR0VPcjR2M1F1UGk3ZVNXTnZxeWJsQlIrbXpKemlxL201RkQ5NHArQk5BM291ZFUwV2JiS0NjRkdvVGcwSkVHMkROdEFFclM0c3FGcUlJWXBhL05rUC9HWUhTdVNYZnVscWxZNlRLd1pGcVdqWVNFK01JbmJTZmlqbldHaHlnWXl2RU9Jd2Z0SEkwNkcvcDBHUDloZG9NMnJEeWtON1Q2WkJQNVEyTExPeCtuSlczbjdsQ3hSQVAzc0k3UTBuL3lvNDJwT2FtVm5iQmNzTHJ4dTciLCJtYWMiOiIyNzUzMGVlNDdhNWZiZjM0NzM0MGMyMzEzMThmMDAzYjdiZWY5M2RhZTY0M2ZkZmFiNmQ4M2QxYTJjOGEzZTg1IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:35:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFqYlBCeW1Hb09MdG9RdHhlUXdpUkE9PSIsInZhbHVlIjoiaXZkN2x1MjRLVm9VYm5raTEwWkhzajQ1Z3VWU0Z5QkgxL1N6MEppaS9Bd3dDOWVLVmxrMCtFVzVPVTgzNmIzOWVhT0dxNXRST1ZidzJBZWpDczFTRG9aUVR4SlJGVW1CUWF2Z2lYdnJSQ2RqcHFBZ0pTdGtVTE5pSW9NTHJURlNqVitWcm1XM2wvOTE5eXJZY0ZOSFhaTktWR3V6cnpsbXVTRjY4b2xLemRUL25iNy9HNHFob3JEYzZESDRWNXBYSTRVUTNDemhqTEZKTXZ2Sm50RTZZUXYzY3BMK3dFZnZIMWh5bDU1Z1YwVnlrZEFtMXYzTEpZR1FUcnNtSG5wL2UrRWh4TlZzSk9xU2xNQmJFNmRCNkFNYjlBL2tOTEdYZkROaFRaSzZZT3JwR3NxakdwdlZDVzllSDRMTnlsSE9uVFJ6K0xZano3elpZOWZzd0FIZXBYQ3ZYazhkVW16aE1TVWJKWHp6M1FKUUdnbU1KdW5zQVlWNFRmbjBSNkR4SXFFcm54STRhcUs0YkJmTldZZnQzeVUyUTZKMFUxWGtRUHlGV2dnOGlQRis0UzNvelptWVZkRjBaNS90SWQ1TXpqNHJpeGVNYlgvaXJPamQvaTJJZXp1aStWVTgybWZvc21wS25OSkNGVEt5a0hrcmRueUdWNE5PWWxvbW5FTkUiLCJtYWMiOiIwNTE4NDQ1Y2RmZDkwMjEzOGE2M2JhZTJmNDIxMmNhZmI1ZDQ5OGVlYzQ4Y2MyMTJmZTU3Y2QwM2E1YmE1NTUxIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxJVEl1T0ZiNWpxWStRWmRLZnlGVWc9PSIsInZhbHVlIjoidWJxWmYrTXpJQUt1eGJFbmhRVE8veWRBdHdBV3ZUZGpVZ282U0l2ZnBzOFo4c1lWbWxzaEZVYWc5WXdMUVlOWlk3K1huVnU2UFZ5UU13Q3JiNjRvMGF1bUVNb3RmSmtNdFlMbTJjVFhEbXBHRUt5bDhrYmhkVWwwZEV5QnhURTFQWlZ3RVRuK2E4L3RWd2VFVUtDcE9qSFhkWUQ2SzZvWXMrNnlaSmNQNVo0c2QvVm9wT2hYcHF0UjVsWWs3b2JXZGtpQlhpamd0SkcwT3hjY3Erck91aXhMMVJLNUE1NU9RK3V0VWgybE9BbmdDQno5UWJ2Q1lYczR3Mjl3eXUrK3UxMTVkak9xQS9uNDFYWnBtMjhxalJ0VU1nelV3VERzV3pCMXRyTytHMnNSa3pNR0VPcjR2M1F1UGk3ZVNXTnZxeWJsQlIrbXpKemlxL201RkQ5NHArQk5BM291ZFUwV2JiS0NjRkdvVGcwSkVHMkROdEFFclM0c3FGcUlJWXBhL05rUC9HWUhTdVNYZnVscWxZNlRLd1pGcVdqWVNFK01JbmJTZmlqbldHaHlnWXl2RU9Jd2Z0SEkwNkcvcDBHUDloZG9NMnJEeWtON1Q2WkJQNVEyTExPeCtuSlczbjdsQ3hSQVAzc0k3UTBuL3lvNDJwT2FtVm5iQmNzTHJ4dTciLCJtYWMiOiIyNzUzMGVlNDdhNWZiZjM0NzM0MGMyMzEzMThmMDAzYjdiZWY5M2RhZTY0M2ZkZmFiNmQ4M2QxYTJjOGEzZTg1IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:35:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523493198\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1083442061 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dlfDXgpc5LHP5CUsgqs70zFckvDPl2hF5UQUwh9L</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083442061\", {\"maxDepth\":0})</script>\n"}}