{"__meta": {"id": "X24e4e4fa97feff457df0d20ee119e741", "datetime": "2025-06-06 20:36:07", "utime": **********.32871, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242165.787808, "end": **********.328754, "duration": 1.5409460067749023, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749242165.787808, "relative_start": 0, "end": **********.048989, "relative_end": **********.048989, "duration": 1.261181116104126, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.049008, "relative_start": 1.261199951171875, "end": **********.328759, "relative_end": 5.0067901611328125e-06, "duration": 0.2797510623931885, "duration_str": "280ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45695136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.216692, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.235773, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.299409, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.308817, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.038540000000000005, "accumulated_duration_str": "38.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.138371, "duration": 0.02134, "duration_str": "21.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 55.371}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.169434, "duration": 0.01065, "duration_str": "10.65ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 55.371, "width_percent": 27.634}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.187763, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 83.005, "width_percent": 1.764}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2182899, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 84.769, "width_percent": 2.491}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2376962, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 87.26, "width_percent": 2.517}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.269989, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 89.777, "width_percent": 3.217}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.280328, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 92.994, "width_percent": 2.335}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.286788, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 95.33, "width_percent": 2.309}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.301959, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.639, "width_percent": 2.361}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-2064762953 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2064762953\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-45681653 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-45681653\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1344328158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1344328158\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1850446703 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242138186%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFRWTdhalF3UVgwTEdLaUhiZmFrWWc9PSIsInZhbHVlIjoiTVdEL0xuSEozQVpMTjRuQlgxbC83YjE3Mzc3SnJVMVRqSDFmcWh1dFZtVWs4ZUsxemdLTTNMT1FkL1VNU3JlNmFuYUxOT0pia0t1SmdaRU1MazRwblJBSGYvRzRlMjU3QTJoUTdtODkyRytDdXh6aHhMYWxueUlyTWlPNTZ2eG1ycTRTSW9YTFJLT2txSzE5T0s0Y2dqcit0Nm5xY01NaGtQbEplL0RmNmpnN3dLOU5GaVlkNmd6dzQwSU5aVjZtT0F5UmEzblhTbVVYMXd5V29Ec0pxcGZmaXZ6Rlg2MnlaL2FjazlqVFVqMmtKaXJQaDFjY1p0ODlWZGZKY1RWY1l2Sm9zTHdsTnlGdUhDOW56RnRTL0JRNGViUWVkWEtaRDdlWmQ4NmlvTVlPdHpBZnZqWFVTZlNKNDZKcE1lcmRtWnpJeGF1YXpnQ3IwWWVoSnBaZGNJOWhKZXBqOHZDSjV3TDFhL1RMWExMNTRSS05KTGxsRGd2OVFHN21JRXVJWnh5cXNYOFlLSEhpUW50aHE2TUVHTTN4V251ckJ2WERneUdUVUtaZkhJWEx3enVzVlNCZDVad0lNaHRSd1ZpTlJsUVl4MEhjRGg0T3ZIT1J5TkkvdTVSVFhYTTZlbHR1K0VGak0zNWdiekNvZklGZEZ2ejFUVEVSUXVEZWJNQ3UiLCJtYWMiOiI0YTMwMmZlNmNkNzg3MTc5ZTQxM2MxN2ZhZDQ5OGQ4NmY3MWM4YjJhNWFlNzVmNzU5YjZiMjk2NjQ3MzlmMmNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii9NcVY0a3VucXNjK0o0dlN3RFVTd0E9PSIsInZhbHVlIjoiQ1FMR2hvVW1yeUhoNHNKZ21FeDI0Wm9wcXhnTzl5Ykh6Tk01OVJ3aDdTbVRoVTJJWThkSFdFUlJ0RG9LRkRwVkpwVzJ0djdoazhqbkI3cTlrbjAzWnFRL3QwdlBHYzZOaVE4bThET0VjUjc4V28xM3MvcDlKUUFHaWkzZGs3Q0cvS3lwQTBSdFVMdWlmQlJPbFFydmNoSFB1SXJSSUxGN1JBejQ4OSt0ZFl1a3I0ZjA3bzBDcmRFcWdTS0gzNFFCY3N2d3RweG9zNlc5S21aTVJ0UzVSTjNKZW9xQ3F6eExzTWZibGlJYVJIVldlQ1VKNVlqK0VxeFFnZXYvbitreTlHQ1A4TFBtMGsxUUVrR0dlcFhLZEtoOXplemxxbHhvVWNpblB6OUxmME1lZGpFKzRPdFVxU2ducFo1Nkdtak5GbU03SlA2WFVyVkZtNmlUU3ZoeWZzYTNmY3NIWEt6eEpHRkRIQlhjM0ZvOXlGS2xLSlI5RXpTSlFqOVBWc3hyWTFlazBaRW16RFVEams4ME54VGdYci9XK0NsRzRGM0QzaE1CYTNodFV5Ty9hSkVqRjBRMFMzUm9qYW93cHNjbVNacXY3bEhpckhpaktCNGYzMG9MQkVkUXhvcy9zdUFQbkNyRDluUUlXbVNFVFhMUTBYYUlSWFF0VzkwM3hjWlUiLCJtYWMiOiIwZTgyNzliMWRkOTgwZGRkNGI0YmZkMzE1Nzc1ODZmMWUxOTljNDVkY2M0ZTI5NWM5OWU0ZDRhZGIyYTcwOWEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850446703\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1689191201 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tLaYiGHNkG5boJ6Cz4Xh75W1LbW3TwRWDQTQ1jy7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689191201\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1353143709 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:36:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9MRlBqWUFDT0pBVVZscnVIUjVJQUE9PSIsInZhbHVlIjoiaHU0WCtiMktCQzFWWVh4cWQxRkFicnh1Zjh5dStqSkhWOWZoQmtISS9NRWVqV1lrS1lwYnNVQ2UxUVZ5SENMYXd5dlozVk5FeHJZRjdONUVRbXRmRkZtTkVrY0ZZYUtLeVorNmVsSkJtMkEzYzhWeXNnb1lhK2dZY2ppMkxDeXFxUTA0Z25hZndXaVF4Ni9XSHM0c1RpckhmNnVuUUsvYk9WUU1sN2FnWjlzRU9PeXdUQUVCUEZvZFJaKzVjcjBDM283dnUwQS85MGNPTTQxTGZHaXhlU2Q4S1V4RjZuYWhMams1QTgvSmtjcXZ6a2UwVk5rQnJtZU5RMFhVMGxiSUZySmYwTjRSeXduQXgwY21Sdmh1S0JoMVArVVE1Z0VkNXRXZ05jYi8rcWpVdlJrOHFUallFQnlWME1xc0dnK0djSDJZZUcwZ0pjSUdESUhxSmZud3VCcEFRR1djVHVzVEVyNzNIWFJWZG16dStxRUFoNzBpSC9pcGZFZVVaVURGYXRKM2dqOGxmVHY2K0FNcWMrLzJWbE5XTUFleDd6RzJ6MU5TVVROKzVPZVY2bHN0emdmMVZSMmVjSnZVVDlKRnFMYXpFb2F3RnFSbzNqdzJ5UG5NTG9pS2F1RnBBUFNjb2RBN2NkemJaRENJdk5MTXRQaUVYZWFXVDduMEtuYmgiLCJtYWMiOiIzODFmNTljMWM1MmEyYTA4ODAzNWUxYjdlYTkzMGRiNTQwODlmODEzYzgzYjM1MGIzZGQyMzhlZmQxOTk0NzY0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRwcGd4cTNDczY3VWdFSXdOVnZmd1E9PSIsInZhbHVlIjoiSUl3ZGNvMkEyaStOUEszbWlwcnJGdEdYVzVzaEcxMFBOZkZpZEt2Z1pWRzZ3eHVINkxuSk5aa0x2YUcyUEk0UzVxRXh2WXpMS2tGQStkV0xITVNPN2tDKyt2K3IvLzhYRTU3RjROeDJjSmkwS0ZXWE9SL2RvRjc0aUIvU20zOWZOOHJ6cGdCWk9xVWs5cnF1UDBIdHRKOVRBUHpaZTlNWGIyd1dZSEs1ckg1NlNDb2pVNGo0QW83MGxxcHgxOUg5TDMvTDZYM1RhMFMzSHBMS2VNdFJqUlJEdU9mUUpBVnhJY2o0WGI0YjhKN243UjRYMDJYTFJHdmJ5ZWZNaTZVRmhwNmYyYWNKNWdFOG5HTDhZQnpFT2V1eFMzdk1LKytQYXV4c1p3NEQvRWhYcWZvZzYyYmtUVm1PN0VXQ0VRdmw3ODZKWWNhczAwQ1JBREMyV0RyVVk1SjYycHpsTmY3SGFJMjFRcUpGZWlOYnFrRzl6N0pVYy9JNUpPOEw3eHFzbFRBNkl1ZTdVMnNFNWVJOUFwOXBpbUU5U1QwNFdId3RmOTFHaGpWMTRwL1NzVkN2dmFzVUdGbDhOL3ZTRnpVL0poUHpVUXpOUXN1WDZURHlVQ2F6OEJpQ2g4VEtIMTI0VHptZ0pTdm9KZWZuZFgwUmkyZFFXczhxZXFjMXNMRUQiLCJtYWMiOiJmZWU0NGY2MmZmMjEwYzlkNThiOWVmN2Q5MTgwNTc2MTRlOTQ5NWRlZTRlOTMwNDVhYWNhOTkzMTZjNDEzYjcyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9MRlBqWUFDT0pBVVZscnVIUjVJQUE9PSIsInZhbHVlIjoiaHU0WCtiMktCQzFWWVh4cWQxRkFicnh1Zjh5dStqSkhWOWZoQmtISS9NRWVqV1lrS1lwYnNVQ2UxUVZ5SENMYXd5dlozVk5FeHJZRjdONUVRbXRmRkZtTkVrY0ZZYUtLeVorNmVsSkJtMkEzYzhWeXNnb1lhK2dZY2ppMkxDeXFxUTA0Z25hZndXaVF4Ni9XSHM0c1RpckhmNnVuUUsvYk9WUU1sN2FnWjlzRU9PeXdUQUVCUEZvZFJaKzVjcjBDM283dnUwQS85MGNPTTQxTGZHaXhlU2Q4S1V4RjZuYWhMams1QTgvSmtjcXZ6a2UwVk5rQnJtZU5RMFhVMGxiSUZySmYwTjRSeXduQXgwY21Sdmh1S0JoMVArVVE1Z0VkNXRXZ05jYi8rcWpVdlJrOHFUallFQnlWME1xc0dnK0djSDJZZUcwZ0pjSUdESUhxSmZud3VCcEFRR1djVHVzVEVyNzNIWFJWZG16dStxRUFoNzBpSC9pcGZFZVVaVURGYXRKM2dqOGxmVHY2K0FNcWMrLzJWbE5XTUFleDd6RzJ6MU5TVVROKzVPZVY2bHN0emdmMVZSMmVjSnZVVDlKRnFMYXpFb2F3RnFSbzNqdzJ5UG5NTG9pS2F1RnBBUFNjb2RBN2NkemJaRENJdk5MTXRQaUVYZWFXVDduMEtuYmgiLCJtYWMiOiIzODFmNTljMWM1MmEyYTA4ODAzNWUxYjdlYTkzMGRiNTQwODlmODEzYzgzYjM1MGIzZGQyMzhlZmQxOTk0NzY0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRwcGd4cTNDczY3VWdFSXdOVnZmd1E9PSIsInZhbHVlIjoiSUl3ZGNvMkEyaStOUEszbWlwcnJGdEdYVzVzaEcxMFBOZkZpZEt2Z1pWRzZ3eHVINkxuSk5aa0x2YUcyUEk0UzVxRXh2WXpMS2tGQStkV0xITVNPN2tDKyt2K3IvLzhYRTU3RjROeDJjSmkwS0ZXWE9SL2RvRjc0aUIvU20zOWZOOHJ6cGdCWk9xVWs5cnF1UDBIdHRKOVRBUHpaZTlNWGIyd1dZSEs1ckg1NlNDb2pVNGo0QW83MGxxcHgxOUg5TDMvTDZYM1RhMFMzSHBMS2VNdFJqUlJEdU9mUUpBVnhJY2o0WGI0YjhKN243UjRYMDJYTFJHdmJ5ZWZNaTZVRmhwNmYyYWNKNWdFOG5HTDhZQnpFT2V1eFMzdk1LKytQYXV4c1p3NEQvRWhYcWZvZzYyYmtUVm1PN0VXQ0VRdmw3ODZKWWNhczAwQ1JBREMyV0RyVVk1SjYycHpsTmY3SGFJMjFRcUpGZWlOYnFrRzl6N0pVYy9JNUpPOEw3eHFzbFRBNkl1ZTdVMnNFNWVJOUFwOXBpbUU5U1QwNFdId3RmOTFHaGpWMTRwL1NzVkN2dmFzVUdGbDhOL3ZTRnpVL0poUHpVUXpOUXN1WDZURHlVQ2F6OEJpQ2g4VEtIMTI0VHptZ0pTdm9KZWZuZFgwUmkyZFFXczhxZXFjMXNMRUQiLCJtYWMiOiJmZWU0NGY2MmZmMjEwYzlkNThiOWVmN2Q5MTgwNTc2MTRlOTQ5NWRlZTRlOTMwNDVhYWNhOTkzMTZjNDEzYjcyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353143709\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1943206026 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uVPGjJOdpFrUnV4HJpgzeo3oSe8rJx42DSHIJ2J7</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1943206026\", {\"maxDepth\":0})</script>\n"}}