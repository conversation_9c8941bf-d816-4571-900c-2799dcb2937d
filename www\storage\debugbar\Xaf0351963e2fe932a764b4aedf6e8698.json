{"__meta": {"id": "Xaf0351963e2fe932a764b4aedf6e8698", "datetime": "2025-06-07 07:30:45", "utime": **********.894768, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.03432, "end": **********.894792, "duration": 0.8604719638824463, "duration_str": "860ms", "measures": [{"label": "Booting", "start": **********.03432, "relative_start": 0, "end": **********.798112, "relative_end": **********.798112, "duration": 0.7637917995452881, "duration_str": "764ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.798132, "relative_start": 0.7638118267059326, "end": **********.894795, "relative_end": 2.86102294921875e-06, "duration": 0.09666299819946289, "duration_str": "96.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44776200, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.005979999999999999, "accumulated_duration_str": "5.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.847785, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.04}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.867859, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.04, "width_percent": 16.054}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8798528, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.094, "width_percent": 21.906}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1936736083 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1936736083\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1135948429 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1135948429\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1047759894 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047759894\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1671878240 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281439391%7C7%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkMrYzdCazRacUNlNkk2Q0F2Tm9kY0E9PSIsInZhbHVlIjoiRmkrN3plYmZDc0pQLzl3cEU1VHpRM1R4aHJ3c3QzazNqb012eU5UOWEyc284eFZhZVkzaXZvSDdUcjFyZ25BaTR0LzBNNGFhNDZTSjMwdHFCYzlBMUpBMEpubnhOK1ZrNjRuVEF4b3NRS2NEY1JFQklueld5VXdLRUJTSVBFVHBOK1d2ci82dFlEV2h3OHpKRjdmM3Z3UnRRUC81MEFoT3NHd2VWemRBbTZxUEpwdjVIdWtLTlNZcUlsK2pSZXozZ0t0Q2toWlBxOXlHSWlJM1NWWENUWS8weldxcyt5TzdVdzc0dy9HWm50eG5ld1phVDEyd0xhd3R6Y0R1VGJZOTV5a1VWc0xRQi9RWlhTalBreks0U3lGT3NjNEFZaHhrdHRXWitsZGRPcFBuY1pERDAvZmhHQ0RLMzlEdkJwQk5GUFN2UFIxenVjZ05aY0ZzZXNueUczVlJoY3d1VGNqUXlKQ2xaRVp4SzBJYWVjQjQ5N2h6cUJzamZVY3EzVzdzQmhCSnVRSkIwQ3RWVHdibEVLMTJaMVFSSTB2cm9aMWljR2FJYVYrMUcrZ0xaVjJmUW50WEMza0N6K0gxS3I0R0ZUaUdUNEh6ZkZ2bGFkT1kwYnZDYlBtbzhBOUhPbmZoWTdrcEJiSjI1WTV5MU9ibXVTMUFIb0EwYXI4RDZGdmwiLCJtYWMiOiIwMDRjZGY5YTU4ZWI5YWY4YTE4OTE4ODY1ZGU0MTYwZDc2NTVkOTAxNmI3ZmM1YzFhMDQ2NTlhMjY1YmQ1Nzc1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImswSitWWDVRK3V2dFIrT0x2ZG5GVkE9PSIsInZhbHVlIjoiZFY5MnNabTl1bWxwYUVmaitpRnYySkxkcWNQZkxUdGttSjdsanNNeUVoN3VDTkhYZU5vV3pCYldYaE9FeEVtRDlKNEtueHBrZXBvZzUxTFhtV0NQdnNBSm0rU1FMT0RodjJ6TWI5Tm1Eb3pmWWhNbVJZTUplVHhUMENubk1aaks3Y0ZpNzJ4RE5EK3crdi8wRDdqazhLaGNwTnh5MUt2L0I3VnZhc3EwL1AvRGZQWDlrRi9MM2pQeDZiaTgwdHY2aGwyTGoxOXpzekZPV0xwczUrUjlvYVVxM2ErdnZtek9VTDZobkMzaGNMbGN2dXpEOEpvVDN1VUFvQjdSR3BhU29Zc2JqN2tZUUJCSDhBTWhpTm5ad1YvTmtKMUl5ZENDQnI4QnViNHZmTXRSUHhnMzFSUmo5S2ZVQ2dNcUQ0cXZ1d2g1WmVDY2QveSttd2h1Qjd4YlBkWUxYVG1tM0tLcTFkVmE5R09ZbnNHcUg2YWFQTnZ0cEhIa3pweVdCemJBb3Vxc3U0OGZmRERMbHFKdjJFQkZ0Wjd3eUxEdGMvbUlkTDVCbWxncTlBdVl1WE11LzRCZ2RUR1daek8xZUM4OFptR1RUaVdpV0w2RFNWRHJhWlFFL0FPZ1NpcDVrUU1YdGF1UncyTnZheDJ0WEgzTkNzSGNUYjJOZVd0RURQQ3MiLCJtYWMiOiIyYWY4ODlkNjNjNjBjY2Q3MjE0ZmMzNjE3MzY5N2Q0MzNmNjBlNDQ2ZGQ1MTA2ZjYxNWQ2NGM2ODZjM2JkMDA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1671878240\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-879682345 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bmHYo9T8TH5q6dUCnl8ZG1w46p5IENlShdAmx5wt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879682345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1453304764 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBWRUtHU0ZxTjU1ZGJ6dG9LQ1lKT1E9PSIsInZhbHVlIjoiUTRIR0xEQUdVU0R0SkFBLzVqZnpNR2N2MEd0L0gzUlQxQWI5UVlvaEVEUUdTRU9qTzB6d0NJVGhra0RwMXlmd0t3UUFYRTdjT2hpdXM1cDUrbkhRczlVOEV6MDZvOVRKVjlMZSsvM0hib2hRL000enFCdGNTWVV2bHdUaXROcXdvZzFPUm1uOTJTZlJWOVNsMHRLbGVDaDBHUDhpU1FrN3pjME50Sy9LTWVidlhMc3B4SEV6ZTBlLzllMHhtYWMzTjkxTmprc1FsQVhRdzVIdDQxSnlOQXRRSDJheEloMjVrVjFESG02QWZFR29ZSFZhUlFmQk56WmhjWG5TS3VhT0g1R1lsYUcya1U5ekl1WUxBUlQ0NDM1MTRNRi9NOVVDaUJMdTlLNlJCR29rVHo5L0pPekUrbEMyRFlMQXZXbUVHaEYxWDVHRHpyZHVwWjlyZFpmWFQxdmxTc21XNFVWN2pDeDFpaXVmckdkUmVYMjJZaWdidWFkRXBtMlM3Q2JmcVdnenhsNTdka3I0YkM0KytsZ1kyWU5Mdll3ei9CWjFQbXkvbHhpa0piNjVhY3NPRUVubG1YVXBkUFhPdWpMQ21YRmJ6dms0dkN2VFFaR2dxZy9uOFFaSGw2b1hmeFBMa2wxaFRROHd5KzhuQWZIS2FFWHVyOHNzVFJsMEVINkYiLCJtYWMiOiIzNTUzYTYzZDJjYmJkZWRkOWU1N2NmMDFjYzg3YmY4MzMwMmY1NjQ5NGM2MjlhNTYxYTRjNzczNzAzZTBhYTMzIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxmYlhJVkk4bituRXd3K0dOdWgvd3c9PSIsInZhbHVlIjoidXBzQ25xK3hOK0ZNbVUyVSt0U0VHL3Z4eTJySnY3dXZGNE83Y0U2WW9MTFllNG5sRU5CbGxva1p3OHhkeTZFVDh4aWtZRFE2QUlnM2RHUXpaK2pGaG9QVjJDVXZxcjEvczRrZU1xcnFKRUhwVWh0Ly9uMGV2aG5zUUhQelJBT2tEVmtVUWxEOEdrOFFLTk5HR3YybVhoRmJFcjB2QXFUSzVPTVkxYVNFMHJiOHdoQzJQQWkwMXk0dzdMR0poZGtpS211VjFFcDlEK0Fhd2xkdDRmcWYzeVRZRjJrbjlISWlxWDRNNy9qVVZjdFQwMUpsVlZybjRIQU1qRXdMVXJXNnFkaC9aV2w2TTVxd2U0aVpKM0RIWmRZMVBNdVNPSEMwWEN0YXhnZlJsdEdDNFFncUFKc290SWUvNlYySDNpVXNsQ3FsM2hxV2h4blA3R1F3NkF1T2hwNnlMcVcyVE1xNHhyclloM2xjRFpMSXlEeDcwamlEczlJVllnSG5FbWNDNld2UU51eldNY0NpWU5JTGdVQWQ2UVpMWkx5dlltc1lSVjVyZy9QeThFOGRkdi9HYmZ3V2o2ZW5JZFQyVWlmRnpoOGVqOUV4RWRhK1ZQSVZkOHFvNFB4RW54Z2hMYU1MdkpiR2s1N2NFUFVvdFFNVlBoYmpMQjNPcTB4OHpKVW8iLCJtYWMiOiI3M2YzZDVmMmY5ZGU0ODVjMTIzNDg1OGMzOWVjZDdmYzM5ODAyZGYwZjhjY2U1MzAyMDViZGFiYTAzNjQ1NTZkIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBWRUtHU0ZxTjU1ZGJ6dG9LQ1lKT1E9PSIsInZhbHVlIjoiUTRIR0xEQUdVU0R0SkFBLzVqZnpNR2N2MEd0L0gzUlQxQWI5UVlvaEVEUUdTRU9qTzB6d0NJVGhra0RwMXlmd0t3UUFYRTdjT2hpdXM1cDUrbkhRczlVOEV6MDZvOVRKVjlMZSsvM0hib2hRL000enFCdGNTWVV2bHdUaXROcXdvZzFPUm1uOTJTZlJWOVNsMHRLbGVDaDBHUDhpU1FrN3pjME50Sy9LTWVidlhMc3B4SEV6ZTBlLzllMHhtYWMzTjkxTmprc1FsQVhRdzVIdDQxSnlOQXRRSDJheEloMjVrVjFESG02QWZFR29ZSFZhUlFmQk56WmhjWG5TS3VhT0g1R1lsYUcya1U5ekl1WUxBUlQ0NDM1MTRNRi9NOVVDaUJMdTlLNlJCR29rVHo5L0pPekUrbEMyRFlMQXZXbUVHaEYxWDVHRHpyZHVwWjlyZFpmWFQxdmxTc21XNFVWN2pDeDFpaXVmckdkUmVYMjJZaWdidWFkRXBtMlM3Q2JmcVdnenhsNTdka3I0YkM0KytsZ1kyWU5Mdll3ei9CWjFQbXkvbHhpa0piNjVhY3NPRUVubG1YVXBkUFhPdWpMQ21YRmJ6dms0dkN2VFFaR2dxZy9uOFFaSGw2b1hmeFBMa2wxaFRROHd5KzhuQWZIS2FFWHVyOHNzVFJsMEVINkYiLCJtYWMiOiIzNTUzYTYzZDJjYmJkZWRkOWU1N2NmMDFjYzg3YmY4MzMwMmY1NjQ5NGM2MjlhNTYxYTRjNzczNzAzZTBhYTMzIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxmYlhJVkk4bituRXd3K0dOdWgvd3c9PSIsInZhbHVlIjoidXBzQ25xK3hOK0ZNbVUyVSt0U0VHL3Z4eTJySnY3dXZGNE83Y0U2WW9MTFllNG5sRU5CbGxva1p3OHhkeTZFVDh4aWtZRFE2QUlnM2RHUXpaK2pGaG9QVjJDVXZxcjEvczRrZU1xcnFKRUhwVWh0Ly9uMGV2aG5zUUhQelJBT2tEVmtVUWxEOEdrOFFLTk5HR3YybVhoRmJFcjB2QXFUSzVPTVkxYVNFMHJiOHdoQzJQQWkwMXk0dzdMR0poZGtpS211VjFFcDlEK0Fhd2xkdDRmcWYzeVRZRjJrbjlISWlxWDRNNy9qVVZjdFQwMUpsVlZybjRIQU1qRXdMVXJXNnFkaC9aV2w2TTVxd2U0aVpKM0RIWmRZMVBNdVNPSEMwWEN0YXhnZlJsdEdDNFFncUFKc290SWUvNlYySDNpVXNsQ3FsM2hxV2h4blA3R1F3NkF1T2hwNnlMcVcyVE1xNHhyclloM2xjRFpMSXlEeDcwamlEczlJVllnSG5FbWNDNld2UU51eldNY0NpWU5JTGdVQWQ2UVpMWkx5dlltc1lSVjVyZy9QeThFOGRkdi9HYmZ3V2o2ZW5JZFQyVWlmRnpoOGVqOUV4RWRhK1ZQSVZkOHFvNFB4RW54Z2hMYU1MdkpiR2s1N2NFUFVvdFFNVlBoYmpMQjNPcTB4OHpKVW8iLCJtYWMiOiI3M2YzZDVmMmY5ZGU0ODVjMTIzNDg1OGMzOWVjZDdmYzM5ODAyZGYwZjhjY2U1MzAyMDViZGFiYTAzNjQ1NTZkIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453304764\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1189238805 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189238805\", {\"maxDepth\":0})</script>\n"}}