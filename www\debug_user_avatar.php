<?php
// Debug script for user avatar display issue

echo "<h1>تشخيص مشكلة عرض صورة المستخدم</h1>";

// Check if we're in Laravel environment
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    require_once 'bootstrap/app.php';
    
    try {
        // Get current user
        $user = \Auth::user();
        if (!$user) {
            echo "<p style='color: red;'>المستخدم غير مسجل الدخول</p>";
            exit;
        }
        
        echo "<h2>معلومات المستخدم الحالي:</h2>";
        echo "<p><strong>الاسم:</strong> " . $user->name . "</p>";
        echo "<p><strong>البريد الإلكتروني:</strong> " . $user->email . "</p>";
        echo "<p><strong>حقل الصورة في قاعدة البيانات:</strong> " . ($user->avatar ?: 'فارغ') . "</p>";
        
        // Check storage settings
        $settings = \App\Models\Utility::getStorageSetting();
        echo "<h2>إعدادات التخزين:</h2>";
        echo "<p><strong>نوع التخزين:</strong> " . $settings['storage_setting'] . "</p>";
        
        // Check get_file function result
        $profile = \App\Models\Utility::get_file('uploads/avatar/');
        echo "<h2>نتيجة دالة get_file:</h2>";
        echo "<p><strong>مسار الصور:</strong> " . $profile . "</p>";
        
        // Check if avatar file exists
        if ($user->avatar) {
            $avatarPath = $profile . '/' . $user->avatar;
            echo "<p><strong>مسار الصورة الكامل:</strong> " . $avatarPath . "</p>";
            
            // Check if file exists in storage
            $storagePath = storage_path('uploads/avatar/' . $user->avatar);
            echo "<p><strong>مسار الملف في storage:</strong> " . $storagePath . "</p>";
            echo "<p><strong>هل الملف موجود في storage:</strong> " . (file_exists($storagePath) ? 'نعم' : 'لا') . "</p>";
            
            // Check if file exists in public/storage
            $publicPath = public_path('storage/avatar/' . $user->avatar);
            echo "<p><strong>مسار الملف في public/storage:</strong> " . $publicPath . "</p>";
            echo "<p><strong>هل الملف موجود في public/storage:</strong> " . (file_exists($publicPath) ? 'نعم' : 'لا') . "</p>";
            
            // Test different URL patterns
            echo "<h2>اختبار مسارات مختلفة:</h2>";
            $testPaths = [
                'المسار الحالي' => $avatarPath,
                'مسار مباشر' => url('storage/avatar/' . $user->avatar),
                'مسار asset' => asset('storage/avatar/' . $user->avatar),
                'مسار Laravel Storage' => \Storage::url('uploads/avatar/' . $user->avatar)
            ];

            foreach ($testPaths as $label => $path) {
                echo "<p><strong>$label:</strong> $path</p>";
                echo "<img src='$path' style='max-width: 100px; border: 1px solid blue; margin: 5px;' alt='$label' onerror='this.style.border=\"1px solid red\"; this.alt=\"فشل: $label\";'>";
            }

            // Try to display the image
            echo "<h2>محاولة عرض الصورة:</h2>";
            echo "<img src='" . $avatarPath . "' style='max-width: 200px; border: 2px solid red;' alt='صورة المستخدم'>";
            echo "<p>إذا لم تظهر الصورة أعلاه، فهناك مشكلة في المسار أو الملف</p>";
        } else {
            echo "<p style='color: orange;'>لا توجد صورة محفوظة للمستخدم</p>";
        }
        
        // Check default avatar
        $defaultPath = $profile . 'avatar.png';
        echo "<h2>الصورة الافتراضية:</h2>";
        echo "<p><strong>مسار الصورة الافتراضية:</strong> " . $defaultPath . "</p>";
        echo "<img src='" . $defaultPath . "' style='max-width: 200px; border: 2px solid blue;' alt='الصورة الافتراضية'>";
        
        // List files in avatar directory
        echo "<h2>الملفات الموجودة في مجلد الصور:</h2>";
        $avatarDir = public_path('storage/avatar/');
        if (is_dir($avatarDir)) {
            $files = scandir($avatarDir);
            echo "<ul>";
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    echo "<li>" . $file . "</li>";
                }
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>مجلد الصور غير موجود: " . $avatarDir . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>لا يمكن تحميل Laravel</p>";
}
?>
