{"__meta": {"id": "X3d333f7a9f8c3a7e9ec57d1052d2ad1b", "datetime": "2025-06-06 19:39:33", "utime": **********.448339, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238771.907123, "end": **********.448384, "duration": 1.5412609577178955, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749238771.907123, "relative_start": 0, "end": **********.271461, "relative_end": **********.271461, "duration": 1.3643379211425781, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.271482, "relative_start": 1.364358901977539, "end": **********.448389, "relative_end": 5.0067901611328125e-06, "duration": 0.17690706253051758, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44776104, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00846, "accumulated_duration_str": "8.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.362995, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.875}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.397926, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.875, "width_percent": 14.421}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.406555, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 75.296, "width_percent": 12.766}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.427642, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.061, "width_percent": 11.939}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1301238218 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1301238218\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-474601963 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-474601963\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-732163202 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732163202\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1871108320 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238728265%7C55%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM3SEc2dk55QVZYK0E4OERqcmp0aUE9PSIsInZhbHVlIjoiYTFsNjZGalRCMDY2TE5aSkJwd0xlViszdFFKdnl2QjhzZi96NVZscjhZRzJPSFRvK3hiaFIyTlpISHh4SWZuWkE5ZTFBWGFCYmswL1l1cXN0SlNoTE9TVWxUdVQrQ0x6REMzTnF5MnRNZzNEbWs2d1B5dHRWSnNLcDk3YmllbktLaU90d0JWRHVPUnF4cHY2QmFHWEprNnE4T0lGczRCUlNMem43dHN1UHplQjNWajRYbHQxaXdxNTZ0dE42QXNkUHkwdU1PNG8zeEVRZEtYa3E4ZlpYdGhuTkV2bjk3eVB0MU9OYnlSZTliZ0tmV2xIQ215cDU0TjVLOFlTSXp0STR2Z3B6R2ZyWkhlcGVPbUlxT2VYTUdLTkwxOWx5WFFtbU4xSGkvWUxpRGdpUWZqUFBkQzd3a2xBTWlkM0JXcEpkQ0NnSzZ2TUp5VkloMHBseGcxdWxzY0JrWGpiOWpnYkpyK0hVMnJxM3ZveVV6ZXdUSkNaK2JUNitZQzlaNTdjdTlzUHFLQ1lTYVVuUlM2YnIwQ3V5SFFaZ3BDTmRBVWJ5VFZYb0FyeUk1cjFweG5uUDcxSWg5dWZyNUV2dW5jNkxPTHBCay83UVJmVTNkTUUyNHIrV0lEc3ZXdkdRS016cGl1bzgrYnlRdDdGU2dTSUt5OEdwT2ZHaElESm1IR2MiLCJtYWMiOiIwZDg0ZTlmNGQ1YWZmYjFiMzQ2NGNjM2UyZDVhMTQyMGI2N2U3OTc2NTJlYjQzODAxZTJiOGFiZTVlOGY0OTliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InN0eE4rRDdnMzlPdFF6WWw1UHI0VVE9PSIsInZhbHVlIjoiSkloQUFhdDI1WnN5UE5LQmhnWWpuU2ErRWZnL1Z2Zk1CMHh3eWwzVVR2WHE4ZUFQcCtZdVFDay9aTy9QbzhUTGl1eHFDZERLSkNjY0hZUWxJTFU0NHpDTC80cHdOVE56cGw2MFE1VGgvR05vNlRvbGpmUklVMUEyTmgwWFpIaUgrR0EvZUVxUHNCSUdZdmg4R2ZGVVpRemxFOXJXOFh6SEZ4ajAwU3pLSSszY0l2M2p0TEZ5UXErRGoyZFdaTVkrQWZobzRjVGk3dFBRMFNqR3FKSEJkSzNNMkYwYzA1S3pLUDhnNm54Y2NlOWhOdk1WVE5DMXgraU1nYjdBM1VkcHUzSEdQTXZTN01GZ0RkS2NaTmpLTnlBQUJ3Q0pJeVh5MjBlMmlEbEJIVXZ0UTVhZU5VL1V3OGJUcWVyRGxybUUxOWFMTzBKd01acHNIbnNQUlFESnpyaWxuN2FnZ29BakRTdVFDQzR6QmRHZDFpZXRWYTZ4M2RZOWdVb25RWGowSmxjR2lOTVgrckJCSyt0d1FkdkZuWjc2NUYrRVREQktHUm9teTQ3WWp0WWc5eHYwZ28rWVpoaXhiMkViaFBaS3lpMUZDUnEyUXQwY1plOGdjbWlZNVN2MkU1Y0NKWHVNcDJlMWxiK2dBSjVoQzkrZDk1UWxtRHdMbVcybFp6YloiLCJtYWMiOiI3ZjU3ODViNzUyNGVmNmFhZDNjMzY5NWMwNzI1Y2QwNGRkZWJlZDc0MDBhMzg4MzMzMzY4MjExYmExNzMzYjVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871108320\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1869192463 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869192463\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1034147468 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:39:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkI2aEFLeW94TjF0dDlXU0RtZlIwR3c9PSIsInZhbHVlIjoiQmdtbklKajQxK1QxZGY4b3lFV1dROHhlYmUvSDFpdFd4SVhLbStnbkt6VWk4MlBVQ1MrRDRkNE5kQzcvQVNSZE1ETXRCakZFaytzUjJ4SVk4WmoxMTRFWmU5ZWExQlJDSVpkRXhmc1F1Z3JxSVpKS0ZseXBXYndsczNFdXZoZHM0cWpFMlkvTi9IWjVEWjRZOVBQQ0Vud3phMDYyNU1rMzJ2QmJTdU4wdFBzL2Vxa05CTjVoeWUyVTc1dGxrTUhwQXVlazlwTThVQkNyaGlrT3lGUUp4TDlBVFB1U2ZobmlBMnJnSTBtY1JLbXdPMTJKLytaVzZFMGFFMnBnM1JZWEhhdDBJZUdmRHBXYUtIVGtmOWV3OFRvbWsrQ3ZsRU5VbUpnUjBRNjJWaEpKenY5Z0pTUjA5TzE1VDRIY1FaVHVkWVpMbUZJK1AxMXFmNDhGODNJckZQZjhJaWpTeElEVHZEeXpydmFHSElpb1RYQ2pacTNjU0ZvZFlsR05OS0VOL3V1QkFnMW9xdFhyRndmM0UrZWo3ZGlGMVdWdWwwZWhPWGd1YS9jS1NPUnMwbkppSTA5VlMxQXd6Z1kvTi9JTDFUemp5dDVvcnF3empJb3FKeWR2MSttdEdEWndmZjV3ckJsSWJubHBVUjBHTEs5ZElEZElmbVJrdmYwU2Rwd0EiLCJtYWMiOiJkN2FlZTExM2RkNWEwMGYwNjlhYzFjZDAwYTkxZjM4NzcwOGVjNjk1YmRmNDcxYjQwNzcxMmMwNDVlZmY5MGEwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNVY0txRW5HYXRvbGJvcVBFRkdMRVE9PSIsInZhbHVlIjoiM05iVDhuSWZ1OHQvcDVFOHBHRTUzOHZhK0JyYjZBc1h0akNpb01MbnFjcUsvR1pTWW9ZU2liWTZzM3Q3bUpCenNJMmJzMHhPMjJNeTF4ZDVsc2Y4VTRCZkpDRFJmVzdyQ3dCMmJEbFNwN2JnUld3NkgvMlpWNUJyVThsZElRenBrMGxGaFJoVkR2TlhIdE4ySkRrY2tQWTM0S3JMNWhDTzFXb1gvUjk1LzZJRlhoMUdDdy9uVzVPM2ovck1qb21QQ1M2ZjhCMnR5WFRyemJlZGlQLzY0eG9pZUt6TTZ5bnhTdnFEay9FeHduTVlnOHJ0NGQrUGVEM2JraUpCS08zNkpOK2JITHpjaFRqUzZaRmE5TWdKd2xpUEF2ZWNROG9OWlZCVVB0TG9Fd0ZBZERlcGtra3FZaGNJdFM1NmtXUENYQ1dhYlZuT3pIL2JmL0xVc0JRa0FrNXM4NFFzVHlLZjh4NjdBRnB0VWk2dmxVQ0g0QXVRVjBkb3E0andCVU9vSWo2SE44K2lMaGYyRjYwbGdnYjJjVEVlYVBqeVp4MmV4Yk03SFRibEZKREhWYVpnOVV2aGgrQnBhM2I0eWMwUzNocVhnTmR0UGwxbEhVNHRqZXFrV1BwZndGU1hRR2NDWGVLdEFYamh4MStxSzJLTGpLR0FnNEtVZ0Yvd1F5ZTUiLCJtYWMiOiIzNDI3OTg3MjJkNjk2NDBkYWE4NmEyMjIzYWU5ZjI0YTJlYjYyZjc4MzQ4ZmUxMWJiMzIxYzU4N2JlMzlkMTc2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkI2aEFLeW94TjF0dDlXU0RtZlIwR3c9PSIsInZhbHVlIjoiQmdtbklKajQxK1QxZGY4b3lFV1dROHhlYmUvSDFpdFd4SVhLbStnbkt6VWk4MlBVQ1MrRDRkNE5kQzcvQVNSZE1ETXRCakZFaytzUjJ4SVk4WmoxMTRFWmU5ZWExQlJDSVpkRXhmc1F1Z3JxSVpKS0ZseXBXYndsczNFdXZoZHM0cWpFMlkvTi9IWjVEWjRZOVBQQ0Vud3phMDYyNU1rMzJ2QmJTdU4wdFBzL2Vxa05CTjVoeWUyVTc1dGxrTUhwQXVlazlwTThVQkNyaGlrT3lGUUp4TDlBVFB1U2ZobmlBMnJnSTBtY1JLbXdPMTJKLytaVzZFMGFFMnBnM1JZWEhhdDBJZUdmRHBXYUtIVGtmOWV3OFRvbWsrQ3ZsRU5VbUpnUjBRNjJWaEpKenY5Z0pTUjA5TzE1VDRIY1FaVHVkWVpMbUZJK1AxMXFmNDhGODNJckZQZjhJaWpTeElEVHZEeXpydmFHSElpb1RYQ2pacTNjU0ZvZFlsR05OS0VOL3V1QkFnMW9xdFhyRndmM0UrZWo3ZGlGMVdWdWwwZWhPWGd1YS9jS1NPUnMwbkppSTA5VlMxQXd6Z1kvTi9JTDFUemp5dDVvcnF3empJb3FKeWR2MSttdEdEWndmZjV3ckJsSWJubHBVUjBHTEs5ZElEZElmbVJrdmYwU2Rwd0EiLCJtYWMiOiJkN2FlZTExM2RkNWEwMGYwNjlhYzFjZDAwYTkxZjM4NzcwOGVjNjk1YmRmNDcxYjQwNzcxMmMwNDVlZmY5MGEwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNVY0txRW5HYXRvbGJvcVBFRkdMRVE9PSIsInZhbHVlIjoiM05iVDhuSWZ1OHQvcDVFOHBHRTUzOHZhK0JyYjZBc1h0akNpb01MbnFjcUsvR1pTWW9ZU2liWTZzM3Q3bUpCenNJMmJzMHhPMjJNeTF4ZDVsc2Y4VTRCZkpDRFJmVzdyQ3dCMmJEbFNwN2JnUld3NkgvMlpWNUJyVThsZElRenBrMGxGaFJoVkR2TlhIdE4ySkRrY2tQWTM0S3JMNWhDTzFXb1gvUjk1LzZJRlhoMUdDdy9uVzVPM2ovck1qb21QQ1M2ZjhCMnR5WFRyemJlZGlQLzY0eG9pZUt6TTZ5bnhTdnFEay9FeHduTVlnOHJ0NGQrUGVEM2JraUpCS08zNkpOK2JITHpjaFRqUzZaRmE5TWdKd2xpUEF2ZWNROG9OWlZCVVB0TG9Fd0ZBZERlcGtra3FZaGNJdFM1NmtXUENYQ1dhYlZuT3pIL2JmL0xVc0JRa0FrNXM4NFFzVHlLZjh4NjdBRnB0VWk2dmxVQ0g0QXVRVjBkb3E0andCVU9vSWo2SE44K2lMaGYyRjYwbGdnYjJjVEVlYVBqeVp4MmV4Yk03SFRibEZKREhWYVpnOVV2aGgrQnBhM2I0eWMwUzNocVhnTmR0UGwxbEhVNHRqZXFrV1BwZndGU1hRR2NDWGVLdEFYamh4MStxSzJLTGpLR0FnNEtVZ0Yvd1F5ZTUiLCJtYWMiOiIzNDI3OTg3MjJkNjk2NDBkYWE4NmEyMjIzYWU5ZjI0YTJlYjYyZjc4MzQ4ZmUxMWJiMzIxYzU4N2JlMzlkMTc2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034147468\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}