# تعليمات تشغيل الهجرة لنظام أوامر الاستلام

## 🗃️ الجداول المطلوب إنشاؤها

### 1. جدول `receipt_orders`
- يحتوي على بيانات أوامر الاستلام الأساسية
- يدعم نوعين: استلام بضاعة ونقل بضاعة

### 2. جدول `receipt_order_products`
- يحتوي على تفاصيل المنتجات لكل أمر
- مرتبط بجدول أوامر الاستلام

## 🚀 طرق تشغيل الهجرة

### الطريقة الأولى: استخدام Laravel Artisan (الأفضل)

```bash
# 1. رفع ملفات الهجرة
scp database/migrations/2024_01_15_000001_create_receipt_orders_table.php user@server:/path/to/project/database/migrations/
scp database/migrations/2024_01_15_000002_create_receipt_order_products_table.php user@server:/path/to/project/database/migrations/

# 2. تشغيل الهجرة
ssh user@server "cd /path/to/project && php artisan migrate"

# 3. التحقق من نجاح الهجرة
ssh user@server "cd /path/to/project && php artisan migrate:status"
```

### الطريقة الثانية: تشغيل SQL مباشرة

```bash
# 1. رفع ملف SQL
scp run_migrations.sql user@server:/tmp/

# 2. تشغيل SQL عبر MySQL
ssh user@server "mysql -u username -p database_name < /tmp/run_migrations.sql"

# أو عبر phpMyAdmin
# - افتح phpMyAdmin
# - اختر قاعدة البيانات
# - اذهب إلى تبويب SQL
# - انسخ محتوى ملف run_migrations.sql
# - اضغط تنفيذ
```

### الطريقة الثالثة: إنشاء الجداول يدوياً

#### إنشاء جدول receipt_orders:
```sql
CREATE TABLE `receipt_orders` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_number` varchar(255) NOT NULL,
    `order_type` enum('استلام بضاعة','نقل بضاعة') NOT NULL,
    `vendor_id` bigint(20) UNSIGNED DEFAULT NULL,
    `warehouse_id` bigint(20) UNSIGNED NOT NULL,
    `from_warehouse_id` bigint(20) UNSIGNED DEFAULT NULL,
    `invoice_number` varchar(255) DEFAULT NULL,
    `invoice_total` decimal(15,2) DEFAULT NULL,
    `invoice_date` date DEFAULT NULL,
    `has_return` tinyint(1) NOT NULL DEFAULT 0,
    `total_products` int(11) NOT NULL DEFAULT 0,
    `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
    `notes` text DEFAULT NULL,
    `created_by` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `order_number_unique` (`order_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### إنشاء جدول receipt_order_products:
```sql
CREATE TABLE `receipt_order_products` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `receipt_order_id` bigint(20) UNSIGNED NOT NULL,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `quantity` decimal(15,2) NOT NULL,
    `unit_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `total_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
    `expiry_date` date DEFAULT NULL,
    `is_return` tinyint(1) NOT NULL DEFAULT 0,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `receipt_order_id_index` (`receipt_order_id`),
    KEY `product_id_index` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## ✅ التحقق من نجاح الهجرة

### 1. التحقق من وجود الجداول:
```sql
SHOW TABLES LIKE 'receipt_%';
```

### 2. التحقق من هيكل الجداول:
```sql
DESCRIBE receipt_orders;
DESCRIBE receipt_order_products;
```

### 3. اختبار إدراج بيانات تجريبية:
```sql
-- اختبار جدول receipt_orders
INSERT INTO receipt_orders (
    order_number, order_type, warehouse_id, 
    total_products, total_amount, created_by, 
    created_at, updated_at
) VALUES (
    'RO-2024-000001', 'استلام بضاعة', 1, 
    0, 0.00, 1, 
    NOW(), NOW()
);

-- التحقق من الإدراج
SELECT * FROM receipt_orders WHERE order_number = 'RO-2024-000001';

-- حذف البيانات التجريبية
DELETE FROM receipt_orders WHERE order_number = 'RO-2024-000001';
```

## 🔧 حل المشاكل المحتملة

### مشكلة: الجداول المرجعية غير موجودة
```sql
-- التحقق من وجود الجداول المطلوبة
SHOW TABLES LIKE 'venders';
SHOW TABLES LIKE 'warehouses';
SHOW TABLES LIKE 'product_services';
SHOW TABLES LIKE 'users';
```

### مشكلة: خطأ في المفاتيح الخارجية
```sql
-- إنشاء الجداول بدون مفاتيح خارجية أولاً
-- ثم إضافة المفاتيح الخارجية لاحقاً
ALTER TABLE receipt_orders 
ADD CONSTRAINT fk_receipt_orders_warehouse 
FOREIGN KEY (warehouse_id) REFERENCES warehouses(id);
```

### مشكلة: خطأ في الترميز
```sql
-- تغيير ترميز الجداول
ALTER TABLE receipt_orders CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE receipt_order_products CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📋 قائمة التحقق النهائية

بعد تشغيل الهجرة، تأكد من:

- [ ] وجود جدول `receipt_orders`
- [ ] وجود جدول `receipt_order_products`
- [ ] وجود الفهارس المطلوبة
- [ ] عمل المفاتيح الخارجية (إذا أمكن)
- [ ] إمكانية إدراج بيانات تجريبية
- [ ] عمل صفحة أوامر الاستلام بدون أخطاء

## 🎯 الخطوة التالية

بعد نجاح الهجرة:

1. **اختبار النظام**:
   - افتح صفحة أوامر الاستلام
   - جرب إنشاء أمر جديد
   - تأكد من حفظ البيانات

2. **مسح الكاش**:
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   ```

3. **التحقق من الأخطاء**:
   - راجع ملفات اللوج
   - تأكد من عدم وجود أخطاء في الكونسول

## 🚨 تحذيرات مهمة

- **احتفظ بنسخة احتياطية** من قاعدة البيانات قبل تشغيل الهجرة
- **اختبر على بيئة التطوير** أولاً قبل الإنتاج
- **تأكد من صلاحيات المستخدم** لإنشاء الجداول
- **راجع إعدادات قاعدة البيانات** للتأكد من دعم UTF8MB4
