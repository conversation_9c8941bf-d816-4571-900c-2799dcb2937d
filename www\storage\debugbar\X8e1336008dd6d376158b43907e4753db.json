{"__meta": {"id": "X8e1336008dd6d376158b43907e4753db", "datetime": "2025-06-06 20:34:24", "utime": **********.654684, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.376385, "end": **********.65471, "duration": 1.***************, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": **********.376385, "relative_start": 0, "end": **********.461542, "relative_end": **********.461542, "duration": 1.****************, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.461561, "relative_start": 1.****************, "end": **********.654713, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "193ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0269, "accumulated_duration_str": "26.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.554114, "duration": 0.02401, "duration_str": "24.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.257}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.604599, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.257, "width_percent": 3.606}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.633003, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.862, "width_percent": 7.138}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242062245%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJXU3VoVVZBc3pVNEN0aVNEU1FTa1E9PSIsInZhbHVlIjoiSUEwOHJwRW9WbGFiREtITi9CWHIrUENoZ3IrYXhIZi9sYTc5d3Z1cjZSRzZzRzA2dFEraUpJQWc4ZEE2YStQN29pU2ZxVmRLZjBsK2NHQk95b010M2prdkFFNnY2K3cvdk9uU24rcVUxTzRuYUdabFhjZmU4MnpuU0hvT2NRR0t4MmcwQXhyM0syd0FzWWZEcUlwbG9HM25pTTBHUytqME91NFd5Y3V5MHJ3RGdoNTdmb2paVHFVb2g4elZrVTF6M0k3SitvYnFyNWhWRHVlajhRK29lQ1FJRElzOHZHT2xQQXd1MlFLMytCSEFRd2dEbktvREFLbERPZ1JHelArMXpaVzVacldNMWRqRWgraldYc0thYTRKeDk4MEw2KyszeW9vbHBnSlZkQmFROTVtNVJyVmVkbFVtOHFLc3VSUG1zcmhYNU1DTUZYVFdpZDhjdXlQRG1qbmZzUENaRzN0NDBpZk1TcGVJck5oRk8waDVKazE4YWd0RXl3N2FzT1E2VjVmaUFEWHd6VFpXNXFEMjNmQy9CTHNsMmhYZzB3cnlrZnoxUHdNclNleXBqOEFPdHd2N2QxeWdieGNBSjczRjFDM0RtcDN0cVl5d1o5VGJXY3dnS1VIL1NHcU1zbjY0Vit2ZHhRdkFvVHdYVndEelpvVkNvdWJmbURRTlBEaE0iLCJtYWMiOiI4NzYwMzUyMDFlMTg3MTljNmJlNzQxMDk1MGM5MGQ5NDI5NmViNDYyNjc0OGViZDBmZDM3ODk5NDliYmM3YzgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRiN0NMK2R6Mk81UEJ5M1JUaXV2d2c9PSIsInZhbHVlIjoiSldUUlE5elk1UkV4QUxXa2VHNHM0cUY1K1hjVlByRllpRWxoNFpidWdiYnlSYy9Bb3V2U0ZqMWdXcWwveU9BTkcwR3NWQnRkanluRFNKVkdZSEFjenlGZ09wSlZPajFvOExMS2JSbElGaS9Qd0dPTjNwamZzOUY4YlhjTENXWUVlcEFWdGlNdXZwczhtNHRNTTVjRXdXNkQvSzB2dlZVSTg1N1VCSXE0cVp0d283d2tKZlRUNDlmOSt0YXQ3Sk41Wk80Nnp2NkN3SzFmcUtjUE1GVjdmcGkwZm9Oa3gxQlhEanE5TUx2cmxxNVRmQVJDVi96V21uYXY5WG9GYUIyVjJ4aGNDUGc5UVlyZWlxKzdybmpYNjcxOVU0dWtqSk5sMThoU2Rvbll3OEFkREFaTlFEdCtseE9oSWhHbnFOalZIeWNueGpjR2lmNWUrcVhrS2duTGc5dDRvM0wwQjVDTUJqcm1iRXRGUWhnOTl1Zm9wQ1JmSEFTNFBRKzJnYXZmbzVIYVM2ZGJZU1NkSHJQazBhQ09ZY2ZxamFYNWVMVitYWE9qZk5FdGlWd0ZleEs1MllwdFpGSXVJVXNsQlJSOS9pcVcvN3V4bFFWa1BoaEJVYnFWSWJkN3V2MG5ZRVFsRUx6azVma3g0RFZpeFAxYi9qMGM1ZUtCKzlJaDBEMUoiLCJtYWMiOiIxNjdjYmRhYzM2MTdkNzMzMDM2NTBlYzUwYmJlMGZkZTNmNTc1OWIxZDllYjczZGFiMmVjMGJmN2VlYzBiMTVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1043841905 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043841905\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-256302023 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtQcVBZM2hDYW05OWFCZnBHc1pRUXc9PSIsInZhbHVlIjoieHdPQlREVTQ5UlA2SmJ5MjA4QmhpNGhFZ2g5V1RWaGYxYU1Kb0lPTkk0a0djc0pqYkxRNGlEanZUeEVQZWx2UFlyWnplOWRISVJ4Mnp2dnJ6bHJtZ3YwVlpiU3I0cGxCbGVHM2J5eElXYmVIL1E0eUFvOHk2NWlmRTRIcloyelFjWmhXZ0M4NEVuNUd6ZnV5NlJSc3l4NG5CTDZGbUlpb0YxZGZnQ2xQZVpJbG9zdFRpTnAyUmRUZDlZUFhhb2VmV3QxYkg4WHRNUzJXMHUwRTNtRkRYYUtaVklmay9PTDQ0Z0ZPaVR6anFjaVcyeWhsQWN0Mnk1Q0JyK1d4cTdqbElaUkJRTkFSSTBKQlpiM1pVWCthbkxRNnkybVdobDRlaXl1VlMrVEYybHhTL3R5MVpSTWVGcXVIdE5sdFdpenVoVEJaVGtHUk5WZ1BTdWZkMEVyb09BYmkxSUJOQ29JWnRGMit5TXoxbU52bDJhVC9FR1BRUG5XUFpZYlp5a0tJYUZYRkZlVVFUMzQ4SVJJSUpiK0NXaEZsWGk1YVZLbU5SYVA2dEZ3TG9xaWdoNm9PVXQ1TVZXa2dUY2lJS3hkUUlOd3lkUXE4cEwwa2tzK1FRUE95ZjBUcEtZK0VmU0FpS3ozbC9qRnhCc0laWWU0b3RkMmRlOFhicmNlWDZVTGMiLCJtYWMiOiI2ZGMzZWNkZjk1Mzg3NGVkNjA0NjlhZTE4ZmFkODVjNTg1ZWExYTE2ZmQ1NDAwZTdjOTdmNjZhNmI1MWQ1MzU5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxSRG16T0ZtSWdFK0J6K2dhM05hS0E9PSIsInZhbHVlIjoiQW1uWDVrYno5WGVoeVhpNmJmekhKcDZKQmZDeE9LZFBaSVpad0VSc1JFaEh4ZDl3eUROQjh6STIzZVpTUXE0ZVR6ekpxVEVGakEvaXdTc2RMS2dTamFIb2xramxuWUpCNnNyYkhTWjhjemQvVlhLM2xiaUhnLzR3aDRscm1EaUVEYk1Ubm4rQjJ1Ump5UDV6TWU2UUVEU2ttS3MyWWRzUno3NXNicHhIdkYvWmRub21UOXJ0NkNLbzViSGRRWm1BVmhmWjJmcHNHUTljalR6VTUxQlhIZit5dmNNV1g4YldHL3BTMTF2N21abmxQMU04TEc0cUY2YmthY2tFWnY5YzhNRWpJZmFRNS9qcUhqS2lQVUdNUnRoMFRnK0lWQmkwcG55aUQ3YVRMOWVrUnArN1IxSzZaU1l4UXJzMmhySDR4RC9RS3dKSkZJZEVHa3RiV0NmSGVLMDdLd1ZtQkRmbCtXRHNzVUcvbHgwOG5LNHA2VEl6ZWFaWmZOdW5JYllXd2ZYM0JvUFVhNk9SZkMyNnErZXRaQjJWUVFzOFo4dTNSRDBBelJXQWZHcEVlYzRBNWQ3RGo0NnpjVURiZVBQaU1rU0NYbVZObmtnWUkyZ0VSZGhEcFNEenQwQzZCSC9GMGN4K1Y4MjlEMlRTVklWMjdNTlpTTVozSVIyOG9meXoiLCJtYWMiOiJkYmZkMDBjZjEwYzliMjhlZWY0NzExZTY0NWIzODhlMjJjODU5MTNhMGJmNGEzOTljMGU4YjFiZGFkOTBjOTY3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtQcVBZM2hDYW05OWFCZnBHc1pRUXc9PSIsInZhbHVlIjoieHdPQlREVTQ5UlA2SmJ5MjA4QmhpNGhFZ2g5V1RWaGYxYU1Kb0lPTkk0a0djc0pqYkxRNGlEanZUeEVQZWx2UFlyWnplOWRISVJ4Mnp2dnJ6bHJtZ3YwVlpiU3I0cGxCbGVHM2J5eElXYmVIL1E0eUFvOHk2NWlmRTRIcloyelFjWmhXZ0M4NEVuNUd6ZnV5NlJSc3l4NG5CTDZGbUlpb0YxZGZnQ2xQZVpJbG9zdFRpTnAyUmRUZDlZUFhhb2VmV3QxYkg4WHRNUzJXMHUwRTNtRkRYYUtaVklmay9PTDQ0Z0ZPaVR6anFjaVcyeWhsQWN0Mnk1Q0JyK1d4cTdqbElaUkJRTkFSSTBKQlpiM1pVWCthbkxRNnkybVdobDRlaXl1VlMrVEYybHhTL3R5MVpSTWVGcXVIdE5sdFdpenVoVEJaVGtHUk5WZ1BTdWZkMEVyb09BYmkxSUJOQ29JWnRGMit5TXoxbU52bDJhVC9FR1BRUG5XUFpZYlp5a0tJYUZYRkZlVVFUMzQ4SVJJSUpiK0NXaEZsWGk1YVZLbU5SYVA2dEZ3TG9xaWdoNm9PVXQ1TVZXa2dUY2lJS3hkUUlOd3lkUXE4cEwwa2tzK1FRUE95ZjBUcEtZK0VmU0FpS3ozbC9qRnhCc0laWWU0b3RkMmRlOFhicmNlWDZVTGMiLCJtYWMiOiI2ZGMzZWNkZjk1Mzg3NGVkNjA0NjlhZTE4ZmFkODVjNTg1ZWExYTE2ZmQ1NDAwZTdjOTdmNjZhNmI1MWQ1MzU5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxSRG16T0ZtSWdFK0J6K2dhM05hS0E9PSIsInZhbHVlIjoiQW1uWDVrYno5WGVoeVhpNmJmekhKcDZKQmZDeE9LZFBaSVpad0VSc1JFaEh4ZDl3eUROQjh6STIzZVpTUXE0ZVR6ekpxVEVGakEvaXdTc2RMS2dTamFIb2xramxuWUpCNnNyYkhTWjhjemQvVlhLM2xiaUhnLzR3aDRscm1EaUVEYk1Ubm4rQjJ1Ump5UDV6TWU2UUVEU2ttS3MyWWRzUno3NXNicHhIdkYvWmRub21UOXJ0NkNLbzViSGRRWm1BVmhmWjJmcHNHUTljalR6VTUxQlhIZit5dmNNV1g4YldHL3BTMTF2N21abmxQMU04TEc0cUY2YmthY2tFWnY5YzhNRWpJZmFRNS9qcUhqS2lQVUdNUnRoMFRnK0lWQmkwcG55aUQ3YVRMOWVrUnArN1IxSzZaU1l4UXJzMmhySDR4RC9RS3dKSkZJZEVHa3RiV0NmSGVLMDdLd1ZtQkRmbCtXRHNzVUcvbHgwOG5LNHA2VEl6ZWFaWmZOdW5JYllXd2ZYM0JvUFVhNk9SZkMyNnErZXRaQjJWUVFzOFo4dTNSRDBBelJXQWZHcEVlYzRBNWQ3RGo0NnpjVURiZVBQaU1rU0NYbVZObmtnWUkyZ0VSZGhEcFNEenQwQzZCSC9GMGN4K1Y4MjlEMlRTVklWMjdNTlpTTVozSVIyOG9meXoiLCJtYWMiOiJkYmZkMDBjZjEwYzliMjhlZWY0NzExZTY0NWIzODhlMjJjODU5MTNhMGJmNGEzOTljMGU4YjFiZGFkOTBjOTY3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256302023\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-869844784 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869844784\", {\"maxDepth\":0})</script>\n"}}