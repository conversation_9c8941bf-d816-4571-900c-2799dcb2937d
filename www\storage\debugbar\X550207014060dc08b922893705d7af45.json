{"__meta": {"id": "X550207014060dc08b922893705d7af45", "datetime": "2025-06-06 20:37:38", "utime": 1749242258.036744, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242256.392071, "end": 1749242258.036785, "duration": 1.6447138786315918, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749242256.392071, "relative_start": 0, "end": **********.816885, "relative_end": **********.816885, "duration": 1.424813985824585, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.816918, "relative_start": 1.424846887588501, "end": 1749242258.03679, "relative_end": 5.0067901611328125e-06, "duration": 0.21987199783325195, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763424, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.033839999999999995, "accumulated_duration_str": "33.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.901533, "duration": 0.03037, "duration_str": "30.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.746}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9654698, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.746, "width_percent": 3.251}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.975847, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 92.996, "width_percent": 3.251}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749242258.0035899, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.247, "width_percent": 3.753}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-384429178 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-384429178\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-616552647 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-616552647\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1656755855 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656755855\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242246322%7C10%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9mOFVLcjYzaTZKaCtVbWJNOHB5RWc9PSIsInZhbHVlIjoicW9sWHdlM21yTkRWWWxGd25LRWZPUUlXYU4vSWRPVVhhWkE3K1NlenlHMWpXSjN0a1J0Nmkrdm5kRzNDUGZDbDJUNjNVOUgvRHQ1YUJuTDV0TEN6STRqckNSK01UU1VHVWJTZmJOTHBMeW5wcmZtUkhWU1Y2Tk15WjFGREo4eHBYKzlGVWxseXdMK0FRUkVmK29nejVUYzdEd1o4QVVmWmVrNkdpeStRbnl3REZRNy9MczZoUmVzU2V5WklGcHlPRFljaHpMakdINTZMT1dLRWswTWlYRGRvUWhUdC82bmxEbXNWVTZrT211UndFYjgwckVQc3RsbzhsbTVsUnFlWXlUSU8vTk4yeDZwcEZaL0R2bjk1VHNMcUdGMXJoNlpBakJrcmhRdzlIOTNBR01pVCtwT0t5RGxOOE1OYjlCcDBodWlBSFBva2F5L011c1ZLbmdKQnVjZFhrVFRGeVN1cXNGeE9Sdm9JMkV3TGpWTmVlYWpQRFJUTUVrK01zbm5kTEZVQzh0WkRIemdYMy9jaWpYdk03UGNrSHlGZ0F1dEZPZVBZNlpudmIrTUlYYkI2R256NTdPSWt2Q08ycnlFSWc0Ui9jK2o5WHdpNlpPdSt5UVlLZUpZZkVsakdmTWF1OVdKK3RLVHBRbEMvNDNzdzAzeUQwUFJQakc5cEdnT2EiLCJtYWMiOiI0YjBlOTUzYzVjODE1M2VkZTRiMzQyOTRhYjQ2OTg4NzRjNGRkODMzM2YyMTBiOTQ2MGE5M2RhZjhkMDE5YzUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjgxcVV4Y2hOOXNFeXNodmprZzUvcGc9PSIsInZhbHVlIjoiUmtaNlN5djUyR1pzQzZ1SXR3a1pWbFI2ckRkNndiT1ZTZjNqUzR1V1NwNlloRFQyK3krMUN2MzZxYnYwYTFpbzh3QkEzOVFWWjJSMFRCU2FVZ0V2T0VMOFNwLzFjeVBWVk10aGVlcmJkUHRRMlhHU0tFNEVKbEFxaUJmcnZkRmlXa3JYYWNla1BjUGJRSkduV01vbzBPRzZDbFBNSExkc3RBNVJodVlvWkxHNTZnT3pFQVErYWpqUlpyd3ZaYS94WittVGlWdk0xNzVZVkZzdDJ1cElXQlUwS2h6YzBOVEhEVXErcHJwT20rNmhWNDJPclRhS2hMaXlxOW9kMWRYZUM3ejI1K1I5dm16QThBWUdFTENpYzM0UnRIK0dyZCs1Nk1MTnRmbmgydTUwT0NtcXpBVkhNZGp4UjlaazVESmRVUUVUZmlUU3RmNENCbVR2ajRESGhzazVyc0FUeExDb3ZXSWQ0a2xRSUhDNTFXUlN6Ri9pekozVmdDbHJVclh3OXhWKzU1U1RuZlY3MGMwclF6bVh1WUErb0R2R1ZKV1pHS205VFRoUDJIWlRZNnhuTUY2WUI5QlFHUzh3L29tc3dwdnhFSFlHMjYwd08wd2d4NFlsMVFYZTNTU2J4QWxraXFncmwzYXZqRDQ3d1dDSEVzV1MxYWthTDZ6Q0xUTGciLCJtYWMiOiJmMDcxZGZiYjI1MWIwN2I2OWEyMGNlNGY2MmEyNWJiZDcxZDdjMjBhOGU0MDYzMzJiMmYzMjgwMWQyY2Y0NWNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-349957174 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:37:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlBa0k3bVlzY2FKRmlFcWxTOUZaK1E9PSIsInZhbHVlIjoicS80OWhyZ3pHWmF0ZFBIdXFQZW92S3ZtcHh3Q0FEazgyRjZkSzZBVjZRZ2VoMDVpWmtYcWpVazNZYXF2WXRHZEpCb3pwc29WMThmZWQ2UlpXNUxVS241Z1BMN1pnYjdlQ01ZaHE0ZWVxUFFCRSs3ZEtIdkRjcEUxZEhjOEpqRHFKTjA5N1RMMjRDQklvWXhhc21vRFRsSVp0VjI5N25pTGtEVU84b29ES2xoc1ZlT1lCSittVTdPR2dXdGJ3U3FnZ09qV2IyVlF4ZGxLRUlhdnFMNElLUjZTeVJBdkdFS09yRWdhZXZhMDE2NEZaaDAxNm5CcU9XVEpiTTM0bDZKMGRkS2JiRDFXcWhPRjYrNjQ0YTRuYVFQNnJadGJwZjk2SHlVQUNqdWxWOUxlYkZ4S002S1JLQVgwb1NQWDkrS2NIbDB5WTV1Rm05OVFUV2J2Uy9PRENHYVhqVGpWR2R1Z0VQcCtLMGZtUklDWXI3Ynh5WnlIN29tYmkvczVqQmxqUjVCRDRNUHkrdWFPWThJWUJCMXFpMHlrYUFQL1dVdlJWd3dVTWVVaXZFVWduenJzaktKcFlnSHA5ZkRNME0wMDAwSVh5UlFlQ0dSOWNHUVJmZWNrU2ZEdUFFR05Fbk4rd1YwcXVQcWNCQ1N0REdWbmwzbFNGNTV2NXNSeDc5ZEEiLCJtYWMiOiI1OWM4ZWIyZTQ5OTQ4ZTg0NjUxN2E5ZTQ4YTc4MTFmZWY2MGI5YWJkZjU5NTIzZDdjNjlkNzFmZWI2MDZhNjcxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpYaGd1VDhvaGV4RVF0NU5pcC9GYmc9PSIsInZhbHVlIjoieTBKY1NaYTZPNXdWTy9DVU5xVWFEYXVPNWFCaWloR3FtR3oxWlpoTHNxdjIyYWN4ZzBqcm5iSHROcjE3QmVYMW1xOE1xRUJ2RWZYMDZkVUpPdU5xa1pYa0xoT0xBbWdSZm5MS2JTTUZKNGRHRVlNUk9RdEFmQkszK0Z5cnNadHJBdjVlUXYrQThQM2doaXBSeXRUdThHQkRid205M0RUeGdPQVRUczlRaFlKanZIUmtVVDN1dktWYTZvZGFJd3JjTy9YR2F3QXk4TDVCUWNPYm1zV3hwc1J5VkdwSGtIa1JRMHkwMy92OUNTYlpzUDF6THNuNGlqd0luRjdpV2FJR3plSjR3ZmtOc3lpMlErYXByVlhJTUloM1EwS0RVN0tQV0wzZkl4V0NLdUltTUFmTTF5eU1BZkpJNlFZQ2Fla3VtR0tvSXdkbmtiaVR3RWMvdzBhVlJEYUJuVWhvcExlS0t1dDNqYlNVcFh6N2RXVktQWitveUpsNERGdHlpL0NGaUJGQ2pVcnozWmFPcTRzZGM5ajRON2tGTXI0ZENSMG9hWU9FVWZwbEE2K0drK09uZDZSR1E1VDdMQ0V6MUFSU2hzQ1MxbWd2NEFISklNQVFLaHpNdXRPYkdwalhJYmpZM3hsR3lwS3kzbXhsU0s4OVRwLzIvUit6WVo1cVhIQ2UiLCJtYWMiOiI0NThhYTA5ZWQ5ZDEzM2E3ZDM0Zjc4YzhmMjFiMDFiZDMyNWFkMzYxZTgzMzhlY2RmNTkxYTg4ZjgxN2VlMmVlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlBa0k3bVlzY2FKRmlFcWxTOUZaK1E9PSIsInZhbHVlIjoicS80OWhyZ3pHWmF0ZFBIdXFQZW92S3ZtcHh3Q0FEazgyRjZkSzZBVjZRZ2VoMDVpWmtYcWpVazNZYXF2WXRHZEpCb3pwc29WMThmZWQ2UlpXNUxVS241Z1BMN1pnYjdlQ01ZaHE0ZWVxUFFCRSs3ZEtIdkRjcEUxZEhjOEpqRHFKTjA5N1RMMjRDQklvWXhhc21vRFRsSVp0VjI5N25pTGtEVU84b29ES2xoc1ZlT1lCSittVTdPR2dXdGJ3U3FnZ09qV2IyVlF4ZGxLRUlhdnFMNElLUjZTeVJBdkdFS09yRWdhZXZhMDE2NEZaaDAxNm5CcU9XVEpiTTM0bDZKMGRkS2JiRDFXcWhPRjYrNjQ0YTRuYVFQNnJadGJwZjk2SHlVQUNqdWxWOUxlYkZ4S002S1JLQVgwb1NQWDkrS2NIbDB5WTV1Rm05OVFUV2J2Uy9PRENHYVhqVGpWR2R1Z0VQcCtLMGZtUklDWXI3Ynh5WnlIN29tYmkvczVqQmxqUjVCRDRNUHkrdWFPWThJWUJCMXFpMHlrYUFQL1dVdlJWd3dVTWVVaXZFVWduenJzaktKcFlnSHA5ZkRNME0wMDAwSVh5UlFlQ0dSOWNHUVJmZWNrU2ZEdUFFR05Fbk4rd1YwcXVQcWNCQ1N0REdWbmwzbFNGNTV2NXNSeDc5ZEEiLCJtYWMiOiI1OWM4ZWIyZTQ5OTQ4ZTg0NjUxN2E5ZTQ4YTc4MTFmZWY2MGI5YWJkZjU5NTIzZDdjNjlkNzFmZWI2MDZhNjcxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpYaGd1VDhvaGV4RVF0NU5pcC9GYmc9PSIsInZhbHVlIjoieTBKY1NaYTZPNXdWTy9DVU5xVWFEYXVPNWFCaWloR3FtR3oxWlpoTHNxdjIyYWN4ZzBqcm5iSHROcjE3QmVYMW1xOE1xRUJ2RWZYMDZkVUpPdU5xa1pYa0xoT0xBbWdSZm5MS2JTTUZKNGRHRVlNUk9RdEFmQkszK0Z5cnNadHJBdjVlUXYrQThQM2doaXBSeXRUdThHQkRid205M0RUeGdPQVRUczlRaFlKanZIUmtVVDN1dktWYTZvZGFJd3JjTy9YR2F3QXk4TDVCUWNPYm1zV3hwc1J5VkdwSGtIa1JRMHkwMy92OUNTYlpzUDF6THNuNGlqd0luRjdpV2FJR3plSjR3ZmtOc3lpMlErYXByVlhJTUloM1EwS0RVN0tQV0wzZkl4V0NLdUltTUFmTTF5eU1BZkpJNlFZQ2Fla3VtR0tvSXdkbmtiaVR3RWMvdzBhVlJEYUJuVWhvcExlS0t1dDNqYlNVcFh6N2RXVktQWitveUpsNERGdHlpL0NGaUJGQ2pVcnozWmFPcTRzZGM5ajRON2tGTXI0ZENSMG9hWU9FVWZwbEE2K0drK09uZDZSR1E1VDdMQ0V6MUFSU2hzQ1MxbWd2NEFISklNQVFLaHpNdXRPYkdwalhJYmpZM3hsR3lwS3kzbXhsU0s4OVRwLzIvUit6WVo1cVhIQ2UiLCJtYWMiOiI0NThhYTA5ZWQ5ZDEzM2E3ZDM0Zjc4YzhmMjFiMDFiZDMyNWFkMzYxZTgzMzhlY2RmNTkxYTg4ZjgxN2VlMmVlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349957174\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-931297480 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931297480\", {\"maxDepth\":0})</script>\n"}}