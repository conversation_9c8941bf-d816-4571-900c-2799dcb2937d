{"__meta": {"id": "X05dde56b099224ebee5bf23aae89838b", "datetime": "2025-06-06 19:13:28", "utime": **********.178707, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237206.742794, "end": **********.17874, "duration": 1.435945987701416, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749237206.742794, "relative_start": 0, "end": 1749237207.991776, "relative_end": 1749237207.991776, "duration": 1.2489819526672363, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749237207.991799, "relative_start": 1.2490050792694092, "end": **********.178744, "relative_end": 4.0531158447265625e-06, "duration": 0.18694496154785156, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44775952, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02296, "accumulated_duration_str": "22.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0800111, "duration": 0.019440000000000002, "duration_str": "19.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.669}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.125878, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.669, "width_percent": 4.268}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.133699, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 88.937, "width_percent": 6.185}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.151893, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.122, "width_percent": 4.878}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-832772090 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ims4UU02blN4MVlsRWtGQlkxTFNyZVE9PSIsInZhbHVlIjoiU0hZWUlKUGZXamtGL1c3Q2trZUloMEtoc3hESExhZU9GTElGTFZ1Q0RxczZLM0xTSERhSDJUOE9oVFBjWmw1dFJEbGhRamp0ajFnWDlnekJlTS9zbGRSUnVCZ2NZL3c4bUk2bG1Qc21jOGlKWXJPU2U5aUtmb1A2SW8wNmt1S1lqVmNNQjZ4Ym5PSDNQdGRSQXl3UzdwNk95N1RsYTRhUERjcmNaSGVTdi9tcVpjRkpXdFVYNW5BMExmK2ZiNEx0SkNoT0dNdWdqQ1JNVVdYS0lLQjdta09XS2I0dEsyU29xMjlmSjduL0tCd3VvbXhTTVZ0QjEzcW1nWlpIT1BZS25TVHNOaGVqcTFxNXdQVUV0SjZnM0R4VzhYRElibzZ5bm41cjlDRHYrajBTZllpN1IrRlQxWnFkdkFyV215MSs2TlJydysxTXl4aXZrYWs4ZDBqU24wQ2piQy9xVUlxOEMvUVlSK29KekN0Umd4TnVvSHI4cnBWYU9jT3JKS0oxVm5Rb2tlTmkzdXE1NngvMEpZc1FwRkRrWDBFczMwVDJJS0xkeS9zZVM4VmhqZEI2T3g4ZWtwdDJkanV0R25QcGI4b2hwUVJ0RTBieTdVZ3lCN3VrWkswcThSSGgzZklNYkt4WURpajM1cUxSUjhaNXRGamN1R0VhOEtWL3h6Y0QiLCJtYWMiOiIwNGUxZjhkNzIwNDExNWY1NTMxODBhODllMzNmYWQwMzVhNTA4MzJjMDdhNWQwYWE3NmMyN2Q1M2Y3M2JjMmIzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNSOGZkTVEvckxkbjQ1THhrSElmR1E9PSIsInZhbHVlIjoiWDFDWjdtQUUwTHZMVGRXelpSb3VsdDQ4MTJ6cWNJdjlRU3dJb0VVMDZZeXlicURaZEpSckpFcU9aemdwKzlJRG5qWjdERWhNSW45MTdaWS93VW42ZXlGUU5kczBZQ2FONEtLWWZNSTBhL0NtZ2dNNHoxdTVKaTJaa2RBY1A0MFVjeXkxenZwemxwZG9EUDkwT0dlU1FUaE9GWTgramllL2pMY0owbXlDN3ZUNnBmTFpRM2VPUDVRTEN4L2JwZkxqcUVEdXkrSWRSSmx4WjNZN29ZZFBlT2RDbDRIS0RsdThmRENCSW0zeUN4b2dtaVZLN0h1YzZmWWtjckcweWw2bDJKVlBMMnBuYThRMHdNVWtkVEpldDIyNDNhcXo3clcxSkY2VFJTZTdPcVplRVJ0aDNaa1huMTVrTVp3RWJHQWl3em9kb2V5ajliemE0YWRvZEpUQWlaTjE0S1kzVEh3Mmk5Zlc1LzdGMTRjTTRMUnF4UW5WTnFnc25kL0NKdjJLaDkzRTV3NTg5RUdCUlNIeVE4RHpzZnk1RzhKVmFKWlR6cFJ5L1VGMUdYUVZMVDkvTGgvOFpYVnNMcVdaa0tTdHRQUnNpanR5RHhiSTNEcXV0UkNCcDZia2hWQ0Y3Mmo1RmQ5WnlHb1RMWlVyNndwRG1GaU1ralg0VlZXVjk5S0UiLCJtYWMiOiJhZTQ3MDllYzFmNDNhMTY1NWM1OTllNzU1MDkyYjQ3Nzk2YjIyNWQ5NTFjNmFjN2M5YzhhMmIxZjhhN2RkYWUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832772090\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-983163138 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983163138\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1829200647 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktiMFRRelE0NGx6SVpxQTd2YjVNMWc9PSIsInZhbHVlIjoiaVgyQlFNeGNkWDM0bXQyUGdqc0F4YjdMWnRWYW5HVXR0emdydDhlb3YzaVN0RGtkL3lZRVd6eFJRVFZIZ3llQzcyVk05SmdhVkYvNWZ4blBzUCthUmZFZTVsc0ozOXJFY0NyeFZPQmp6NUdvRWcxc1NqVEZsSmZCK1ZyTkhlTEtWWGVGT2ZrN05vN0orcHFkeDFQNWtEQUVhc0sybzNTbjFwdUVNZDhxNndHQTRyZVJ1SnRvaHNJNjhhVDYrNWFjVlB1SDRwdTQxdkpFYUFndjRuZ1BFMEJpNU1vTGZDUVBKL3IyWVBERW9NVFF0WkUwbUd2eFdnVzBvdzlNdDlXYXY1eDlXTGlvbGRMWXl1MTBqVDZFczdXSDV0UlJid3RzME1kaU9WaGNyYzg5S2Zaa0ljNW1PWDI3VnJvZ0hNNTFYRWxVd3o1Tm8rRkRNUEl4Mzk3TFMyZWFhSi9sTnY5aWdhR3d5aDBkTmUvWTZ3UDJBWVJzRjlDbEF5SjR4OWJqek1VcWZHdWFCZ0J3OVY1dzZMM1RTS1BHc0V1cEVxdzNJZklDK292NElrVExpcDk5KzRjWllnMDFxVkZReGo0MVQwbXlnZWhuT1EwOVVxS3pTMmltdHhFNmVHOFk4dTRoRVVhZjA4Ui92dGxhOGg4dlRaV0ZPN2FRZGMreWlvT3AiLCJtYWMiOiJjYWI0YTBjMjBmMzlhYjhkZGNkODJhYTc0NjExYzk0OGY2NmYzNjQ1YTZkZjY3YzI1YzkyZjFmOWEzYjg1NmZmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5Dc0ZvVmwzRk9vZlZ2TGNlUGR4dlE9PSIsInZhbHVlIjoiQkRvSkNoL1UvUmthUjYzUWhvTEY4R2ZMYzd0MWE0bytRdW02ODZkWnNBVnlSWFFOcEVnZ253QkhrOWplNXdGenNXdyt3Ukp2bHZ6TloyMlFUM3pvbU5YN210Z0VkM2NTZ1NYY0pVY0dua2xzM0E3NEpmZkN2TytyRGlDd2lPZDZ5Y1VUUXNZK1ArU3RySlVoSWNOTTJpbEFGci9VRUNBUEFRN1dyblR2VEpWMkVRbUkxSVRNSExCeVJEaXRtbGwwTmNjTVBxR2dLRjYzVHVnYmFveUZTOE1ZcXVoNHVIU0ZPQkQ1dHhycHg0SkhQTzZMWjQ3ZGxzTGxXZG1VWlA3UEZOLzVVZ0RwNHFOWHlSelZHcDlvNHdHdW5nQ2ZiRFBkMG44YVF4WkZmL3h6eVJPRDhSTTd0UGo0UDF3T2QzSU9lMzBCYXBlU09EV3VaWk5SN2RQZ2lFSlcrZmtEQ212RnJqNWxFN2FTcW1zQ0tpSklkWnM5Vk40MWFLYTk5NHMxNkxJTXQ1ME5McFFwRlFETWYvQkRkS1UrZ1hqZ0RraGVLcC8rN05NMHR6dy81SjEvbi9IL05RblJMdFQxZ21GV3djZ1RQU2NqajVqQmFOT2tCVUVqU01yR1RJQmZ2NWRNOUpoQVlRUTQ4Rm1yQTNCcXdoTlJBTENSMXVHQmsxZEwiLCJtYWMiOiI1MmE2ODc0ZjA3M2VkYjdmYTYwYmM5OGYzMWE0YmJiMzliMGNjMmI2YjRmNWU5NTRiNTFjZDljNjJjM2E5M2E1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktiMFRRelE0NGx6SVpxQTd2YjVNMWc9PSIsInZhbHVlIjoiaVgyQlFNeGNkWDM0bXQyUGdqc0F4YjdMWnRWYW5HVXR0emdydDhlb3YzaVN0RGtkL3lZRVd6eFJRVFZIZ3llQzcyVk05SmdhVkYvNWZ4blBzUCthUmZFZTVsc0ozOXJFY0NyeFZPQmp6NUdvRWcxc1NqVEZsSmZCK1ZyTkhlTEtWWGVGT2ZrN05vN0orcHFkeDFQNWtEQUVhc0sybzNTbjFwdUVNZDhxNndHQTRyZVJ1SnRvaHNJNjhhVDYrNWFjVlB1SDRwdTQxdkpFYUFndjRuZ1BFMEJpNU1vTGZDUVBKL3IyWVBERW9NVFF0WkUwbUd2eFdnVzBvdzlNdDlXYXY1eDlXTGlvbGRMWXl1MTBqVDZFczdXSDV0UlJid3RzME1kaU9WaGNyYzg5S2Zaa0ljNW1PWDI3VnJvZ0hNNTFYRWxVd3o1Tm8rRkRNUEl4Mzk3TFMyZWFhSi9sTnY5aWdhR3d5aDBkTmUvWTZ3UDJBWVJzRjlDbEF5SjR4OWJqek1VcWZHdWFCZ0J3OVY1dzZMM1RTS1BHc0V1cEVxdzNJZklDK292NElrVExpcDk5KzRjWllnMDFxVkZReGo0MVQwbXlnZWhuT1EwOVVxS3pTMmltdHhFNmVHOFk4dTRoRVVhZjA4Ui92dGxhOGg4dlRaV0ZPN2FRZGMreWlvT3AiLCJtYWMiOiJjYWI0YTBjMjBmMzlhYjhkZGNkODJhYTc0NjExYzk0OGY2NmYzNjQ1YTZkZjY3YzI1YzkyZjFmOWEzYjg1NmZmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5Dc0ZvVmwzRk9vZlZ2TGNlUGR4dlE9PSIsInZhbHVlIjoiQkRvSkNoL1UvUmthUjYzUWhvTEY4R2ZMYzd0MWE0bytRdW02ODZkWnNBVnlSWFFOcEVnZ253QkhrOWplNXdGenNXdyt3Ukp2bHZ6TloyMlFUM3pvbU5YN210Z0VkM2NTZ1NYY0pVY0dua2xzM0E3NEpmZkN2TytyRGlDd2lPZDZ5Y1VUUXNZK1ArU3RySlVoSWNOTTJpbEFGci9VRUNBUEFRN1dyblR2VEpWMkVRbUkxSVRNSExCeVJEaXRtbGwwTmNjTVBxR2dLRjYzVHVnYmFveUZTOE1ZcXVoNHVIU0ZPQkQ1dHhycHg0SkhQTzZMWjQ3ZGxzTGxXZG1VWlA3UEZOLzVVZ0RwNHFOWHlSelZHcDlvNHdHdW5nQ2ZiRFBkMG44YVF4WkZmL3h6eVJPRDhSTTd0UGo0UDF3T2QzSU9lMzBCYXBlU09EV3VaWk5SN2RQZ2lFSlcrZmtEQ212RnJqNWxFN2FTcW1zQ0tpSklkWnM5Vk40MWFLYTk5NHMxNkxJTXQ1ME5McFFwRlFETWYvQkRkS1UrZ1hqZ0RraGVLcC8rN05NMHR6dy81SjEvbi9IL05RblJMdFQxZ21GV3djZ1RQU2NqajVqQmFOT2tCVUVqU01yR1RJQmZ2NWRNOUpoQVlRUTQ4Rm1yQTNCcXdoTlJBTENSMXVHQmsxZEwiLCJtYWMiOiI1MmE2ODc0ZjA3M2VkYjdmYTYwYmM5OGYzMWE0YmJiMzliMGNjMmI2YjRmNWU5NTRiNTFjZDljNjJjM2E5M2E1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829200647\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15********\", {\"maxDepth\":0})</script>\n"}}