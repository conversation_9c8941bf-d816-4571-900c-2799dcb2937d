{"__meta": {"id": "X185724f6431113c6c9afa5c1fde94607", "datetime": "2025-06-06 20:40:31", "utime": 1749242431.020736, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242429.382136, "end": 1749242431.020768, "duration": 1.638631820678711, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749242429.382136, "relative_start": 0, "end": **********.819506, "relative_end": **********.819506, "duration": 1.4373698234558105, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.819532, "relative_start": 1.4373958110809326, "end": 1749242431.020773, "relative_end": 5.0067901611328125e-06, "duration": 0.20124101638793945, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45098208, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00918, "accumulated_duration_str": "9.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9302058, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 56.318}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.97192, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 56.318, "width_percent": 12.963}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9824789, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 69.281, "width_percent": 11.874}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9945471, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 81.155, "width_percent": 18.845}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-883370496 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-883370496\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-275286803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-275286803\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-441775394 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441775394\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1437709857 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=swfud1%7C2%7Cfwj%7C0%7C1983; _clsk=1jtjr1w%7C1749242371143%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikx5MEtpYW5XOHl5RHVGVXdubjJZY1E9PSIsInZhbHVlIjoiNTMzeUFaR29LNzJDU0ZYTDNONEZ0TjJ1ZnoxRGQxS25JN0tQRnJKekdZdjBWcjNYSEsrL015QkRNanZrZEx3eHRsa2hyNGlHKzhNQ3IzSDZ0dm82c1BITlhEcUx5VXBNblNsNk1vTWNVZTl4THl2Z1REcXR5SzlNZUwyUEdiZnlyT1RvL01HaXpqblVSeUsvVHNCWnBRU3l1MlN2eXIzcHZ3WmdsWW5KWjFFbW8zRU93dWt1VW1wOExtRE1wMGo3YmtJVFhNMS9nOG1rVXhUUVFiTGM4MkZ5OVNZUXdtbGIwMEVoc211UlI3b0grNSs3TUc3eGRSbUNGQmVSNHNWMHp1UGh6bFd3TTNncjNVbHBxR3RKTlYrcktJQ24yajVjK3R1dGNoTFdTUHhJWGdoV3ZIZE04NW4zNE1EWGtTTWlianIzYXAzNms5YUsxNXh2WkRjZXZoV2h1OFJ6aUFybXQ4K3lJajQ3NkNBTSt2NDFPR1NJemhuVTdXTjFLdVZTT096bzVlVU5PMjVaMUJDd3FkYjFMQzBNMU4wS2RsTHlnMkx0ZHV6azNEK1BtWVV4bWkvQzRaRmlUQzZLTmJMNXJwTFg3Wm9MK3ZCMmVaU0U4K2JGdkJYN3VleDh2cE9vZXlBWStUN1Y5L0RWMnNQK080S0laY2tTZG9KbjlVaVoiLCJtYWMiOiJkNTZjOWRiMGJkM2ViZDkwNGM1NjlhOTQ5MGMwYWU4MDQ2MmZkODhkMzFkNjFkM2I0NjVkMTRjYTVjMDZhMDBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitSd1NUSklKalIrV0xRMGNGdWwvTmc9PSIsInZhbHVlIjoieVpMVVpDK0lvUTIzSDZCQkJvU21sUVUybUlTTm83K0xMRFVsNlZQbEVqbUpjdnk0ZUxhMlhwTHRkVEJscktXYkx5TndQSWFMeU9mc1JNejRvM1E1YWZHbWdldENlLzJ0R1A4T25yeXVYNzhmNUMyRXoyZVpHTlNObzFjTEtEenVscDNpVi9jSXNpMEpmRFVWMFZraFZRWnBhekttNE5LTnh2cnc2aGRRZmdrWURuMmp6UElLRlFCUFpqYWFweTNEMHVKbFBXTDVaaDVSK2RQcUQrNG1kNGY2NnZKaUtMZCt5YkxUVVN1eWhORzlIYmFkcnVaOTFuK3pjUVJSU0JwZXhkTGJWT1NubUExWXlxeUd2cGNKRC9XY3RER3lnQnJxcjhWT3crbFo0eHNqV2VaS2xnRXZOdjJIb01pR2dTdXFrZU9iVXVWc2lzRnA2MUlnMk9NRXVRcDZmSk9SYmxvWlh0TFY3SkdzZFZYZmt3aVd0YktvV09ZOWJjc1dRRkFVeGxzU3NQZ091aU0xSWduUDdOcDBEdERaWVA2WWl6cmM5S1dmWkoxRXdVbjN1SGczdGNTRzQ0RHE2czdnWEJYa2twNmFidEZpeFRkRisveXRGd3k1VzBwQlNGWTA5akplTThRUHlid1RvV2RzQWw4Yy9DelpPbGhGYkVqcTIza0YiLCJtYWMiOiIzNmNjZGMwMGFkNGIxNjFhMmExMTgyOWZmNjJhNGUzMDBmMTA3YWM3MjMyNTRiMWI0YzllNDc5NTA1OWViMGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437709857\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1683968751 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EXNtVcDSCVpTrtQmjXbil1g1yDu1CX4tI4M8cWJU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683968751\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1489577871 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:40:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inp6Q29Ib1pRQlFvaE0wekJXZkx1aHc9PSIsInZhbHVlIjoiQnc3Wlh3Z3lKV2cxVFFhdjJTb1ZLL3VRTFZrM2Zic1I2U0JKT3BxdGtIdWxEcEFKeElrTGRxdFVrN2pDdXZaYmYrVk05OWpJQlpVYklhYjJOOXh6TjErTnY1WXFURnRrdGdjeHM5UGUyaUJaL3E4OCtFQjYxOE1kdkZBSzdHajZHd0J0L0c0TTltZGtIQy9Calhac1pIOFB2SjFOSUluMFRrd0RMVFpQUWRoRzNHMFN2dmFnTHk1VXBkVWtrWFdZUEJ3Rmx6ZVdZYXVzeElRbWRnaGRkYldJNGg5SHl3dEFGbDQ1MHZXbU1wTVpIaS95cmJYSDA3QXJlc1hFWjdFSlJzeFpFTzZnUi9yTExEUmJNdHBLK0hRTGkrR2FtSzZlQUJpY2lHWVNrQmVTT1ZKM1NCclpvNWZRL2xBZDFZaW82RksxUmQ2c0cxN0dYNlBCOTRrUDNLdkFNdWt3WlQzZWU0SmtmeVBUSGkraTBoeHQxQTlheTVTNUErTzkwRGFMY09IRWRsRGtaOEZ4OXUwMm5ac2VOZDBPeXlzZjFBRTZ2QU51dDFMVzJxd0trNVVDZXladmxMS2lscDdqQjdPQUVZTE5HVWlwTFlPb1dkcE05WkpoQ2ZsK09qSWlBc0IvaVVPTHBEdGJFZWlDV3FpY3B6OHJLUkJuT2NwMkpVTEkiLCJtYWMiOiI4NjhmZGM2MDNlOTg1NThkMTlkOTZjZWMzNGQxOGNhYjJiMDgxN2ZlZjZjNTJmYjNkNTgxNTI4YTNkNzk0MmZmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1JVUlsSDZvUmxDRDZJMWlORm1FOUE9PSIsInZhbHVlIjoia2ZFaXlzc21zQzBNSjlka200UHdFWEFjNnczM0V2bE1VcnArSk9UWC9UR2twYlFJZkRDeWlIa2ZZQ1EwaHBLRGZPZnJIczMvMjgraEhBQ3FSOHFZcW5vUGhJMDdBbUpITUJ1M3JaZFJqNEVldDd4b282ZDM0RDhPdHhxZmlJaG42NTBOVXpOS0s3VlBMb1EraTRwOG16b2RBMmdPenVEbWUwaXlzZnBoTU92TGRtditTM2hUbm1iTnlSYUZ6WWxMVzZzNHU1SHplOTgwWm5qcWZEMEMrcndXanBrakFJSXZSdW9xZ2hHWW13eVlLWmxPZCtWMW43UStITEhqYmh6RjZVSnY1bVRKV1MvWDNIVk9CSWNDRXlwSmVXNE8raE5VcDZqTTJPQVpKU1ZDcXk4bU16SDlMZ20rMGlXMU1LVGlUNlFFMnNWbzR6V3ViUllrRmF4ZWYra0J2Tm5oQS9PR1VNSkxtUVl6aE8xMzhRZGY0VW4rTGFvNnh4Z21oT1ZZS3MzN3J1NEFIUzFMY3hEc0dRMWdkcTVEYU41WkRmRERNZW9ZQkF2UlNJQjNUZU5oZ3Iwdy9ZMzNUeFQzZllNWlVhRWtSMjFIS1F4NzhlUXVDSjZEY3QydXBQWnFiMDY2bTNwaEZFYjU5Q0RUMVRVdzJQZG5wZ1RTL2V0bk9iK0giLCJtYWMiOiJkY2UwOTg5YzVlZWI0OGIwMjQ5ZmZmYjc0ZmJlMjA0YTExNjFiMjQ1YzZmMGIwZWQ4MzdhMTA3N2U4NDhhZjQxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inp6Q29Ib1pRQlFvaE0wekJXZkx1aHc9PSIsInZhbHVlIjoiQnc3Wlh3Z3lKV2cxVFFhdjJTb1ZLL3VRTFZrM2Zic1I2U0JKT3BxdGtIdWxEcEFKeElrTGRxdFVrN2pDdXZaYmYrVk05OWpJQlpVYklhYjJOOXh6TjErTnY1WXFURnRrdGdjeHM5UGUyaUJaL3E4OCtFQjYxOE1kdkZBSzdHajZHd0J0L0c0TTltZGtIQy9Calhac1pIOFB2SjFOSUluMFRrd0RMVFpQUWRoRzNHMFN2dmFnTHk1VXBkVWtrWFdZUEJ3Rmx6ZVdZYXVzeElRbWRnaGRkYldJNGg5SHl3dEFGbDQ1MHZXbU1wTVpIaS95cmJYSDA3QXJlc1hFWjdFSlJzeFpFTzZnUi9yTExEUmJNdHBLK0hRTGkrR2FtSzZlQUJpY2lHWVNrQmVTT1ZKM1NCclpvNWZRL2xBZDFZaW82RksxUmQ2c0cxN0dYNlBCOTRrUDNLdkFNdWt3WlQzZWU0SmtmeVBUSGkraTBoeHQxQTlheTVTNUErTzkwRGFMY09IRWRsRGtaOEZ4OXUwMm5ac2VOZDBPeXlzZjFBRTZ2QU51dDFMVzJxd0trNVVDZXladmxMS2lscDdqQjdPQUVZTE5HVWlwTFlPb1dkcE05WkpoQ2ZsK09qSWlBc0IvaVVPTHBEdGJFZWlDV3FpY3B6OHJLUkJuT2NwMkpVTEkiLCJtYWMiOiI4NjhmZGM2MDNlOTg1NThkMTlkOTZjZWMzNGQxOGNhYjJiMDgxN2ZlZjZjNTJmYjNkNTgxNTI4YTNkNzk0MmZmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1JVUlsSDZvUmxDRDZJMWlORm1FOUE9PSIsInZhbHVlIjoia2ZFaXlzc21zQzBNSjlka200UHdFWEFjNnczM0V2bE1VcnArSk9UWC9UR2twYlFJZkRDeWlIa2ZZQ1EwaHBLRGZPZnJIczMvMjgraEhBQ3FSOHFZcW5vUGhJMDdBbUpITUJ1M3JaZFJqNEVldDd4b282ZDM0RDhPdHhxZmlJaG42NTBOVXpOS0s3VlBMb1EraTRwOG16b2RBMmdPenVEbWUwaXlzZnBoTU92TGRtditTM2hUbm1iTnlSYUZ6WWxMVzZzNHU1SHplOTgwWm5qcWZEMEMrcndXanBrakFJSXZSdW9xZ2hHWW13eVlLWmxPZCtWMW43UStITEhqYmh6RjZVSnY1bVRKV1MvWDNIVk9CSWNDRXlwSmVXNE8raE5VcDZqTTJPQVpKU1ZDcXk4bU16SDlMZ20rMGlXMU1LVGlUNlFFMnNWbzR6V3ViUllrRmF4ZWYra0J2Tm5oQS9PR1VNSkxtUVl6aE8xMzhRZGY0VW4rTGFvNnh4Z21oT1ZZS3MzN3J1NEFIUzFMY3hEc0dRMWdkcTVEYU41WkRmRERNZW9ZQkF2UlNJQjNUZU5oZ3Iwdy9ZMzNUeFQzZllNWlVhRWtSMjFIS1F4NzhlUXVDSjZEY3QydXBQWnFiMDY2bTNwaEZFYjU5Q0RUMVRVdzJQZG5wZ1RTL2V0bk9iK0giLCJtYWMiOiJkY2UwOTg5YzVlZWI0OGIwMjQ5ZmZmYjc0ZmJlMjA0YTExNjFiMjQ1YzZmMGIwZWQ4MzdhMTA3N2U4NDhhZjQxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489577871\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1251328423 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251328423\", {\"maxDepth\":0})</script>\n"}}