<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح حذف المنتجات من السلة - الإصدار النهائي</title>
    <meta name="csrf-token" content="test-token">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
        }
        
        .status-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #28a745;
        }
        
        .cart-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .product-row {
            transition: all 0.3s ease;
        }
        
        .product-row:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
        }
        
        .btn-danger {
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
        }
        
        .btn-danger:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        
        .total-section {
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .total-row.final {
            font-size: 24px;
            font-weight: bold;
            border-top: 2px solid rgba(255,255,255,0.3);
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .fade-out {
            opacity: 0;
            transform: scale(0.8);
        }
        
        .success-message {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .test-results {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #2196f3;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .action-buttons .btn {
            margin: 0 10px;
            padding: 12px 30px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .action-buttons .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header-section">
            <h1>🛒 اختبار إصلاح حذف المنتجات من السلة</h1>
            <p class="mb-0">الإصدار النهائي - مع إصلاح مشكلة المسارات</p>
        </div>
        
        <div class="status-section">
            <h4>✅ الإصلاحات المطبقة:</h4>
            <ul class="mb-0">
                <li><strong>إصلاح مسارات JavaScript:</strong> استخدام window.posRoutes بدلاً من Blade syntax</li>
                <li><strong>معالج AJAX محسن:</strong> حذف منتج واحد فقط بدون إعادة تحميل الصفحة</li>
                <li><strong>تحديث المجاميع:</strong> إعادة حساب تلقائية بعد الحذف</li>
                <li><strong>رسائل تأكيد واضحة:</strong> تتضمن اسم المنتج المراد حذفه</li>
            </ul>
        </div>

        <div id="status-indicator" style="display: none;"></div>

        <!-- محاكاة جدول السلة -->
        <div class="cart-table">
            <table class="table table-striped mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>الصورة</th>
                        <th>اسم المنتج</th>
                        <th>الكمية</th>
                        <th>الضريبة</th>
                        <th>السعر</th>
                        <th>المجموع الفرعي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="tbody">
                    <!-- منتج 1 -->
                    <tr class="product-row" id="product-id-1">
                        <td class="text-center">🍟</td>
                        <td class="name"><strong>شيبس ليز كلاسيك</strong></td>
                        <td class="text-center">
                            <input type="number" name="quantity" value="2" min="1" class="form-control" style="width: 80px;">
                        </td>
                        <td class="text-center"><span class="badge bg-primary">ضريبة (15%)</span></td>
                        <td class="price text-center"><strong>5.00</strong></td>
                        <td class="subtotal text-center"><strong>11.50</strong></td>
                        <td class="text-center">
                            <div class="action-btn">
                                <a href="#" class="btn btn-sm btn-danger bs-pass-para-pos"
                                   data-confirm="هل أنت متأكد؟"
                                   data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟"
                                   data-confirm-yes="delete-form-1"
                                   title="حذف" data-id="1">
                                    🗑️
                                </a>
                                <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-1" style="display: none;">
                                    <input name="_method" type="hidden" value="DELETE">
                                    <input name="_token" type="hidden" value="test-token">
                                    <input type="hidden" name="session_key" value="pos">
                                    <input type="hidden" name="id" value="1">
                                </form>
                            </div>
                        </td>
                    </tr>

                    <!-- منتج 2 -->
                    <tr class="product-row" id="product-id-2">
                        <td class="text-center">🥤</td>
                        <td class="name"><strong>عصير برتقال طازج</strong></td>
                        <td class="text-center">
                            <input type="number" name="quantity" value="1" min="1" class="form-control" style="width: 80px;">
                        </td>
                        <td class="text-center"><span class="badge bg-primary">ضريبة (15%)</span></td>
                        <td class="price text-center"><strong>3.50</strong></td>
                        <td class="subtotal text-center"><strong>4.03</strong></td>
                        <td class="text-center">
                            <div class="action-btn">
                                <a href="#" class="btn btn-sm btn-danger bs-pass-para-pos"
                                   data-confirm="هل أنت متأكد؟"
                                   data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟"
                                   data-confirm-yes="delete-form-2"
                                   title="حذف" data-id="2">
                                    🗑️
                                </a>
                                <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-2" style="display: none;">
                                    <input name="_method" type="hidden" value="DELETE">
                                    <input name="_token" type="hidden" value="test-token">
                                    <input type="hidden" name="session_key" value="pos">
                                    <input type="hidden" name="id" value="2">
                                </form>
                            </div>
                        </td>
                    </tr>

                    <!-- منتج 3 -->
                    <tr class="product-row" id="product-id-3">
                        <td class="text-center">🥛</td>
                        <td class="name"><strong>حليب نادك كامل الدسم</strong></td>
                        <td class="text-center">
                            <input type="number" name="quantity" value="3" min="1" class="form-control" style="width: 80px;">
                        </td>
                        <td class="text-center"><span class="badge bg-primary">ضريبة (15%)</span></td>
                        <td class="price text-center"><strong>4.00</strong></td>
                        <td class="subtotal text-center"><strong>13.80</strong></td>
                        <td class="text-center">
                            <div class="action-btn">
                                <a href="#" class="btn btn-sm btn-danger bs-pass-para-pos"
                                   data-confirm="هل أنت متأكد؟"
                                   data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟"
                                   data-confirm-yes="delete-form-3"
                                   title="حذف" data-id="3">
                                    🗑️
                                </a>
                                <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-3" style="display: none;">
                                    <input name="_method" type="hidden" value="DELETE">
                                    <input name="_token" type="hidden" value="test-token">
                                    <input type="hidden" name="session_key" value="pos">
                                    <input type="hidden" name="id" value="3">
                                </form>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- قسم المجاميع -->
        <div class="total-section">
            <h4 class="mb-3">📊 ملخص السلة</h4>
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span class="subtotalamount">29.33 ر.س</span>
            </div>
            <div class="total-row">
                <span>ضريبة القيمة المضافة (15%):</span>
                <span class="taxamount">3.83 ر.س</span>
            </div>
            <div class="total-row final">
                <span>الإجمالي:</span>
                <span class="totalamount">29.33 ر.س</span>
            </div>
        </div>

        <div class="test-results" id="test-results" style="display: none;">
            <h4>📈 نتائج الاختبار:</h4>
            <div id="results-content"></div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-success" onclick="resetTest()">🔄 إعادة تعيين الاختبار</button>
            <button class="btn btn-info" onclick="showTestResults()">📊 عرض النتائج</button>
            <button class="btn btn-warning" onclick="simulateError()">⚠️ محاكاة خطأ</button>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.10/dist/sweetalert2.all.min.js"></script>

    <script>
        // محاكاة المسارات (مثل window.posRoutes في الصفحة الحقيقية)
        window.posRoutes = {
            removeFromCart: '/remove-from-cart',
            emptyCart: '/empty-cart',
            searchProducts: '/search-products'
        };

        // متغيرات الاختبار
        var deletedProducts = [];
        var testResults = [];

        // إعداد CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // معالج حذف المنتجات المحسن (نسخة من الكود الحقيقي)
        $(document).on('click', '.bs-pass-para-pos', function (e) {
            e.preventDefault();
            
            var formId = $(this).data('confirm-yes');
            var form = document.getElementById(formId);
            var button = $(this);

            if (!form) {
                showStatus('error', 'خطأ: لم يتم العثور على النموذج');
                return;
            }

            var productIdInput = form.querySelector('input[name="id"]');
            var sessionKeyInput = form.querySelector('input[name="session_key"]');

            if (!productIdInput || !productIdInput.value) {
                showStatus('error', 'خطأ: معرف المنتج غير موجود');
                return;
            }

            var productId = productIdInput.value;
            var sessionKey = sessionKeyInput ? sessionKeyInput.value : '';
            var productName = $('#product-id-' + productId + ' .name').text() || 'المنتج';

            console.log('Attempting to delete product:', productName, 'ID:', productId);

            // استخدام SweetAlert للتأكيد
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: "سيتم حذف " + productName + " من السلة. لا يمكن التراجع عن هذا الإجراء!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    performProductDeletion(productId, sessionKey, button, productName);
                }
            });
        });

        // دالة تنفيذ حذف المنتج (محاكاة)
        function performProductDeletion(productId, sessionKey, button, productName) {
            var originalHtml = button.html();
            button.prop('disabled', true).html('⏳');
            
            // محاكاة طلب AJAX
            setTimeout(function() {
                // محاكاة استجابة ناجحة
                var response = {
                    code: 200,
                    success: 'تم حذف ' + productName + ' من السلة بنجاح!',
                    remaining_items: $('#tbody tr:not(.no-found)').length - 1
                };
                
                console.log('Product removed successfully:', response);
                
                if (response.code === 200) {
                    // إزالة صف المنتج من الجدول
                    var productRow = $('#product-id-' + productId);
                    productRow.addClass('fade-out');
                    
                    setTimeout(function() {
                        productRow.remove();
                        
                        // تسجيل المنتج المحذوف
                        deletedProducts.push({
                            id: productId,
                            name: productName,
                            time: new Date().toLocaleTimeString()
                        });
                        
                        // التحقق من وجود منتجات أخرى
                        var remainingProducts = $('#tbody tr:not(.no-found)').length;
                        
                        if (remainingProducts === 0) {
                            $('#tbody').html('<tr class="no-found"><td colspan="7" class="text-center text-muted">لا توجد منتجات في السلة</td></tr>');
                        }
                        
                        // إعادة حساب المجاميع
                        updateCartTotalsAfterRemoval();
                        
                        // عرض رسالة نجاح
                        showStatus('success', response.success);
                        
                        // تسجيل نتيجة الاختبار
                        testResults.push({
                            action: 'delete',
                            product: productName,
                            success: true,
                            time: new Date().toLocaleTimeString()
                        });
                        
                    }, 300);
                } else {
                    button.prop('disabled', false).html(originalHtml);
                    showStatus('error', 'فشل في حذف المنتج');
                }
            }, 500);
        }

        // دالة إعادة حساب المجاميع
        function updateCartTotalsAfterRemoval() {
            var subtotal = 0;
            var totalTax = 0;
            
            $('#tbody tr:not(.no-found)').each(function() {
                var row = $(this);
                var subtotalText = row.find('.subtotal').text().replace(/[^\d.-]/g, '');
                var itemSubtotal = parseFloat(subtotalText) || 0;
                
                subtotal += itemSubtotal;
                
                var taxRate = 15;
                var itemTax = (itemSubtotal * taxRate) / (100 + taxRate);
                totalTax += itemTax;
            });
            
            $('.subtotalamount').text(subtotal.toFixed(2) + ' ر.س');
            $('.taxamount').text(totalTax.toFixed(2) + ' ر.س');
            $('.totalamount').text(subtotal.toFixed(2) + ' ر.س');
            
            if (subtotal === 0) {
                $('.subtotalamount').text('0.00 ر.س');
                $('.taxamount').text('0.00 ر.س');
                $('.totalamount').text('0.00 ر.س');
            }
        }

        // دالة عرض الحالة
        function showStatus(type, message) {
            var alertClass = type === 'success' ? 'success-message' : 'alert alert-danger';
            var statusHtml = '<div class="' + alertClass + '">' + message + '</div>';
            
            $('body').append(statusHtml);
            
            setTimeout(function() {
                $('.' + alertClass.split(' ')[0]).fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // إعادة تعيين الاختبار
        function resetTest() {
            location.reload();
        }

        // عرض نتائج الاختبار
        function showTestResults() {
            var resultsHtml = '';
            
            if (deletedProducts.length > 0) {
                resultsHtml += '<h5>المنتجات المحذوفة:</h5><ul>';
                deletedProducts.forEach(function(product) {
                    resultsHtml += '<li><strong>' + product.name + '</strong> - ' + product.time + '</li>';
                });
                resultsHtml += '</ul>';
            } else {
                resultsHtml += '<p>لم يتم حذف أي منتجات بعد.</p>';
            }
            
            var remainingProducts = $('#tbody tr:not(.no-found)').length;
            resultsHtml += '<p><strong>المنتجات المتبقية:</strong> ' + remainingProducts + '</p>';
            
            $('#results-content').html(resultsHtml);
            $('#test-results').show();
        }

        // محاكاة خطأ
        function simulateError() {
            showStatus('error', 'تم محاكاة خطأ لاختبار معالجة الأخطاء');
        }

        // رسالة ترحيب
        $(document).ready(function() {
            setTimeout(function() {
                showStatus('success', '🎉 مرحباً! تم إصلاح مشكلة المسارات. جرب حذف أي منتج الآن!');
            }, 1000);
        });
    </script>
</body>
</html>
