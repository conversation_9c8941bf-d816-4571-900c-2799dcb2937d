{"__meta": {"id": "X80658971cae5b7230338301e257139a7", "datetime": "2025-06-07 07:30:21", "utime": **********.070374, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.154772, "end": **********.070412, "duration": 0.****************, "duration_str": "916ms", "measures": [{"label": "Booting", "start": **********.154772, "relative_start": 0, "end": **********.926738, "relative_end": **********.926738, "duration": 0.****************, "duration_str": "772ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.926755, "relative_start": 0.****************, "end": **********.070416, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02518, "accumulated_duration_str": "25.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.982781, "duration": 0.02308, "duration_str": "23.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.66}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.023901, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.66, "width_percent": 3.336}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.048614, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 94.996, "width_percent": 5.004}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281418691%7C5%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhHUHJTQk9pREVVQ0QxZjF6SVZKdlE9PSIsInZhbHVlIjoiclF1WWV4M2g2QU9NRG9XOVJUOFBBdmdTQVNueXRIRS81bFF1UkI3ajhuWnBNSkxhUnR1a3JnYmtPL1FDd3dKcEFzVUtKOXAwdFdlbWtDUVE2YkxDT0RnUUp6OXpIUmx2amhiamlxV0M3QXN3MWZDT1RzZDB5amxCMHcrRzRhUWIvRml2TWFwYjVDTW0xc2pyOTJPdFdFSGtHbXpnYmRwWjJVcHBEbHZsRloxaXY3VDlHb3B1K05oWjI5T2xrSVJCS1l3dFNlbVFVeGtQdTJUYk9pSTh4OVh5ZVIzZFpDVzBTVkxVV1FzeEpFei9KYTU2ZUtaYUFJTnA0aDZXdmVHYjIzTzUxNzlDZTQralVLWDhaRW9pSFZ1bEt1ZnJ3V1pVTkhIZDRWNW9sSFlDS2RQS2dJVTJKVFBiZ2dXdS9UbVFvb3VsSmYrV1RTaG8wczd6N3cwMHR3NTF3Q243UU1HZUlySmJhUUFZR2Z6OC9LZW9PQ1BWTjVKZUdHcDdLSGNmVk52UUFNMmUvcHJrQSs2cnR4eHlXTGJBQ2d3YUc4TmlxVUZ4WThESUNLdXpmWHZ2UTVJaEg5N2FyMm5hNmd6a3BTZ0pBVjNySW45TE4yNlhmcDJHTjhqK2JzLzR0TlVNZGJvc1NxZ1laUXkwL2tPNE4vMURKRkVkT2NyRVUyNmMiLCJtYWMiOiI3OGM4MWQ0YTE5ZDc2YmEwNTM1NDBjODVhODAzZjRiNzljM2RlMzA2OWY0MWI3NDQ3N2IyMzY4NjQzNjk4Yjg5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5YR1BmendCa3Q2em1IN2NkSWlFNFE9PSIsInZhbHVlIjoiQ1F1Q2VUaHFvNDhLV0ZwUk96UEtRcE90YzgrOFZQZVQ0THVZR0I0U0w4SlMzbnRlQ2VPQ2hRNDFBT1BtYitlQzhVV3poQUJmYjdoNkNETC9ZSDArUU5xWnZ5bkV4U2s4NnF1SHIzMVlMQ1lERGtmMVNYTTMwL1daMUt3VHJhUldvWWxuT004cXVjQ09oeG41Wnpjb010dFdiUlBvYU9HeDNlK1NVQ2hsSWJ5Vk9VWXNtL1V4eGpzMUFQYUo4aUdkNXZqNVBnQVJVL1BIT3JVdFRXcE54ZlhFTDVZNWNPOEFkYUxWTU45eW9Ud3NTeWg4a0liL2liU1dtKzVzS0l3T2xLdFNLN2NKU1EwYnNxZElWekFIM1ZDZnhuU3N1bDE1a0NpWmRjUzF0WDAvbWUxaktmQWFIZmt6cEpOYWhFWHFRZ0RVYVVCMXU4d2Y5N1FiQzBJelRiQkN2T1NkRXB6d1ZIK2lxblBHcXg2akdGM01JcTVmZGZ6K2NLSVpsc0xFSDA0ck9kN3Y0UWt4aldadUJMT1NqaDZUWjhlUHl3NHUwNE9WanlvdmxES2FnTlNSWnNtYm9hdjdTT0RIVEU3SVBHc3RndjJwWkxIUHF3dUJDbTI3WjlHcFhaL3ZqbENBOFpMQzhEbmgzcEp4TG9hZ0NSYVY1U21TMFNMZTBjZHUiLCJtYWMiOiIyYTQ4NzVhMGI3NDEwYTU0YzhjMzhjMzc1NjFkMjY3MjZiNjZjZmU4MDYzYTk5Zjg0YzJjMjUxY2JmYWQzM2U4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1260731788 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YNJBgXOqtFU4b8z8Vx4XcGRnEfxLj9OGGtpNz79H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1260731788\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhTRFg3R0xJcGRTYVFXQjNDRlg4UHc9PSIsInZhbHVlIjoiMzBkOVgxWDhrZ3RIeTlqS2FLWEtSRC9wV05wTXVEUHE5dFlLbWQ1Q1B2NjNtdFZuWFhQc09oNCtKSTN0eVpuRmI2TEtSV0c0eWlJc1pHV2xCOXdWbnViQVlWdDVkemtiM1czZms1Q2FiUmFPM2xLL2hTbzcyQTM5WjVic0k5blhiODNvMXZJVEZnNi81K2dPR2NXS0NteXNQZkdMRXFBRzVDd1BNZmk5amdYTk5WKy9ycjVWUXdGTXNaeTFoeW9BdzdHYzBiTTJkYjNzTldyc25LS2Q3YURoUTZyMmR6cTFJZzRSM0J4M3dJQjJZeW5jUDIwU2U1dVgyMEg1enhxTThqbnlHZGFmUkNpUER0UUhvQzhVcnNUbHpONTBXSEhlZXdaUFYvM3FTWUNRSHdUVkN1QXRsM1VqU2RKTFBVUVRjVlFKYm5vYVJibmhLYnUwZEl4V0ZTREMrcUJuUm82eER2QmVjdjFtUWdpSTlpeFFDZGxGNVhxZ2xiWWE0bzdBckxhbHVpR0hLYU40RzduUEJ4TDdyakczbUlPSFZvNmxVbkV1MlBRTGtPMGkyWXYzWUlMVkRFTTNGS3E1R0praTZVVVlWVmZQT0tnWmhMSTNUZEU1Nis0eWpvRlpuL0w2dEJMeWhudzZvRFlvdXpKc0pHU1RJSndOUEVDV3FNTTAiLCJtYWMiOiI2YTMxOGNmZjU0NjlkODZhNWY0NWRiYzNlMDA2ODg0ZjUwNWQ5ZGYzMDVmNGU4NDhiNTQ4N2FlNTJmNjU5ZGEyIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlEyWE10NXdwY0l5MmpNbFRCcVF1TEE9PSIsInZhbHVlIjoiZUVwbVdkL2I5OTlQVjlVMHp4TllxME8zKzBQem5FMEVxa1ZRY2JUb3VTVks2QnJkSlBYMUVtdFBFdEtvQXh2SmpRUnlSbGEwN1pPSUxkMTZ4enlPVVVnQ21acWR0cmlYY2J0ZXpRNEJwc05GM2g4a0RKVHh1MUhYcHlUaVRkdVAwU3ZIcFlJNFJQbW9pcHhHWktJYjlTZXRBRWdQenJKVDBVTURSaVgwWnV1dWpSZFkxdHlVVGs3QzdTajVOUllrQUlwc3QzcFFhVUtPZzdtejJBaUhhcjgzdXExZmdGajBNVTN5Z1VZMnNNeS9kZWYvN1ZZaWdOTFFRWXFaMWtXSndCYk1xYmNoNDNQbmdvbGdLVjJsTVV5SmllbEV1bmY3OTEzUjliTDAzSVo1aWJqWUs1bWs0UVc1bXNqaDArOU54c29WMFdyZFRzaHA0czlWOUlkNTUyWm5nbFVUWUIzbitWNzlGK3MwaFQ0RlFXcE44empibkFEaHNNMFdqZ1dVdWliOExZUnpTdzlvT09EUm9UOXZTYnBSSkJHUnFQWWpveXl0Rm5wL0lzdzA5RC9RazRSdWlqQStYN015Y1VEbW0xZ1ZvbGVObm1PS2c4RytvMVNNYk0wQUJwRjdSWUNieVU1WDh5eXMwc2FiVndoMlhCb0x1VkN0NFVLNFUyTTQiLCJtYWMiOiIxMTkyMWVjMjY2YTI4NWJjYjVmNzVjNGI2MmQ4MjU3M2QzMmNhYjNmZjhhOWMzNDkyZmNjOTIyMDBkZGQ5NTcyIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhTRFg3R0xJcGRTYVFXQjNDRlg4UHc9PSIsInZhbHVlIjoiMzBkOVgxWDhrZ3RIeTlqS2FLWEtSRC9wV05wTXVEUHE5dFlLbWQ1Q1B2NjNtdFZuWFhQc09oNCtKSTN0eVpuRmI2TEtSV0c0eWlJc1pHV2xCOXdWbnViQVlWdDVkemtiM1czZms1Q2FiUmFPM2xLL2hTbzcyQTM5WjVic0k5blhiODNvMXZJVEZnNi81K2dPR2NXS0NteXNQZkdMRXFBRzVDd1BNZmk5amdYTk5WKy9ycjVWUXdGTXNaeTFoeW9BdzdHYzBiTTJkYjNzTldyc25LS2Q3YURoUTZyMmR6cTFJZzRSM0J4M3dJQjJZeW5jUDIwU2U1dVgyMEg1enhxTThqbnlHZGFmUkNpUER0UUhvQzhVcnNUbHpONTBXSEhlZXdaUFYvM3FTWUNRSHdUVkN1QXRsM1VqU2RKTFBVUVRjVlFKYm5vYVJibmhLYnUwZEl4V0ZTREMrcUJuUm82eER2QmVjdjFtUWdpSTlpeFFDZGxGNVhxZ2xiWWE0bzdBckxhbHVpR0hLYU40RzduUEJ4TDdyakczbUlPSFZvNmxVbkV1MlBRTGtPMGkyWXYzWUlMVkRFTTNGS3E1R0praTZVVVlWVmZQT0tnWmhMSTNUZEU1Nis0eWpvRlpuL0w2dEJMeWhudzZvRFlvdXpKc0pHU1RJSndOUEVDV3FNTTAiLCJtYWMiOiI2YTMxOGNmZjU0NjlkODZhNWY0NWRiYzNlMDA2ODg0ZjUwNWQ5ZGYzMDVmNGU4NDhiNTQ4N2FlNTJmNjU5ZGEyIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlEyWE10NXdwY0l5MmpNbFRCcVF1TEE9PSIsInZhbHVlIjoiZUVwbVdkL2I5OTlQVjlVMHp4TllxME8zKzBQem5FMEVxa1ZRY2JUb3VTVks2QnJkSlBYMUVtdFBFdEtvQXh2SmpRUnlSbGEwN1pPSUxkMTZ4enlPVVVnQ21acWR0cmlYY2J0ZXpRNEJwc05GM2g4a0RKVHh1MUhYcHlUaVRkdVAwU3ZIcFlJNFJQbW9pcHhHWktJYjlTZXRBRWdQenJKVDBVTURSaVgwWnV1dWpSZFkxdHlVVGs3QzdTajVOUllrQUlwc3QzcFFhVUtPZzdtejJBaUhhcjgzdXExZmdGajBNVTN5Z1VZMnNNeS9kZWYvN1ZZaWdOTFFRWXFaMWtXSndCYk1xYmNoNDNQbmdvbGdLVjJsTVV5SmllbEV1bmY3OTEzUjliTDAzSVo1aWJqWUs1bWs0UVc1bXNqaDArOU54c29WMFdyZFRzaHA0czlWOUlkNTUyWm5nbFVUWUIzbitWNzlGK3MwaFQ0RlFXcE44empibkFEaHNNMFdqZ1dVdWliOExZUnpTdzlvT09EUm9UOXZTYnBSSkJHUnFQWWpveXl0Rm5wL0lzdzA5RC9RazRSdWlqQStYN015Y1VEbW0xZ1ZvbGVObm1PS2c4RytvMVNNYk0wQUJwRjdSWUNieVU1WDh5eXMwc2FiVndoMlhCb0x1VkN0NFVLNFUyTTQiLCJtYWMiOiIxMTkyMWVjMjY2YTI4NWJjYjVmNzVjNGI2MmQ4MjU3M2QzMmNhYjNmZjhhOWMzNDkyZmNjOTIyMDBkZGQ5NTcyIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}