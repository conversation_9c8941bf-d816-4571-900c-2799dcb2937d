{"__meta": {"id": "X2599a155971ec2f3f896c03136091b1a", "datetime": "2025-06-07 04:18:47", "utime": **********.258232, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269925.767385, "end": **********.258276, "duration": 1.4908909797668457, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1749269925.767385, "relative_start": 0, "end": **********.060607, "relative_end": **********.060607, "duration": 1.2932219505310059, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.060629, "relative_start": 1.2932438850402832, "end": **********.258281, "relative_end": 5.0067901611328125e-06, "duration": 0.19765210151672363, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44768088, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01931, "accumulated_duration_str": "19.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1599789, "duration": 0.01634, "duration_str": "16.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.619}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2090619, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.619, "width_percent": 7.613}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.234534, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.232, "width_percent": 7.768}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1097009413 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269849313%7C6%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjArUmExbklxY2lFTzRKNldyMEtMdlE9PSIsInZhbHVlIjoicmlqUkk3MUVHbnV1eDdVM001Y1lqdTRYYUtNMDlqZng1anpMUFR1QWhvSzZONWFPMlJuTFNEek5lcUR6NWlxMkozWDh1bmNEaWxrWHFRcXY5L05vT1dGaUFROUFmOXBXcjl4NFJpTmd0M0J5MG1peWE5aG5Dczl5cUJEcWtzWFBKbG9TQVBIVmpwZUZodngrdjg5cXBVcUgxcWNsRWVZNEdPNGtCQ0ZtU01Zd1R5RmdsdituR2VYVnVjOUkzelJPNFM3MlVndHk1MnFZVHEzUXMzMG1oa3R5RXRRZ0haNlVMUlU5MEM5ODRqVUlVRUtyTGk2dEUyYTY0SndScWU2NWRiMlVQakk0TXk3NHpoRHF4MnJNd2FoalFwVytEeERBb2J0bFRtMVFCM3RtMlNydTZIbU81SnZUTTdVaEFESG1mZFpzckl4MXMwN3g2aDhQcmNNd25CekMrWXI5RFZLMHF2QWhBTTE1V083RUwvN0huTGphRThiVXBrZ2grM3pTaUxGbFIvMDRqZXJwT2pQV1VUUncvc2VpMXNtaFBoMlllQ2NwbEF3MTJHUUxySnlub1d5c0VONFF1K01vejZVT0tRc2c3TE5rMysycjJodGUzV3BlSkpuUGRGeVJKTEVjMWcrTE5vV0Nrb2N1T2w1OW1URjZpNmJibkFJdWU1YjMiLCJtYWMiOiJiYzA4ZTdmN2U0ODE5Y2ZjOTYyZDM5ODk5MGZlZjNlMTRhYmU1ZjU2NjE2YTM5NWIwOGE1Njk1OTQzNmE1NGQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldhZnpqV1JOOWxPdEQ5c0tucHlhaXc9PSIsInZhbHVlIjoiVGVKV2g3em1hVDNjZ1p0S1hjckRKZjlPQ2pnS1E2c1VPMXFmZENmZGV1clJseE5lcWlUbDZwMFJwN3d6QndVZTRxZWw0MlNaanNPYWFlK05XZFQ2STFlb3J0RGxBbGpGZzJwRWJDbE1PTmFzUEE4TDQ1UnZmUzVwZ1BTaFlWMFdKUERBUEpMMWtRU3hPVDBxQUJ5dGFZODJXVWppS3hMTFlPKzFZaHR4OGRRVU5RWitxQ3Z2Nk1KZnVLbWdVSkQ5T0c2TmtUdWRGVU5iMzM5ajJBb2E4NDVFQ1ZPNHh0WlZzdkJ0N3hZYlhNUjFoZUg5dUFDOTBZYzkzNVlXWHU0cjJUUXBJbHdqZGtrRkJHdDhKaVpDNmo2VThNNnJRY3JYdmMxaTFsMkZTRDZPT2dMcjdEQVNzM0IzU052TnVCcVZVYWpiQWVwZnBiVjFBN0FNTHY0eXc1azJDdk5yM3VLUzN3UWRtSEVSQ3lia05wQXVZdU9jRnVIdkNFM24vQ3lReWtKRjFzTStWNXhkUUJPVHBMbmlnUDRSNHJqSzZ1R0RHVlQvbXROQ05XcGoxbU5hZk5aUzRrcXhTeStuYXd3cEcvQkx2ZHFUZGgzMGVsc3l5YW9jWlRJQThPaWx3ZXR5emJCclQ1RXdjekRaUVRHQW9YSjA0VlZCSmF0UHBpS3YiLCJtYWMiOiIzMzU0YjdkOTkyNzJmNmM1YzU2MDEwMDY1MzAxMDZjOWZmN2Q5NzVmMWQzMjJiYjkwYWI3ZjViOWE0OGFiNWZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097009413\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1996374660 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QF4lxftbqb9bEiJQUWBNsJwytQRufosJhQtAJ0pY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996374660\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:18:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpPcnVpdFNJWk9ZM1dwbXYzbzgxeHc9PSIsInZhbHVlIjoiQ3dMRnM3SkxrSmZTZjQwNlF1VVJXMFNkcVhwMWxyZk0ySEpSTEdmeFlveXpmM3p6NkJtbE9NbHpvU0g2cFFhUFd6cm14M3h6ZElxNG0vdlNRckFRWXN4S090b2JNTHhlaWM0Z3U4cUtQMzZGRHE0ZitvMFh2M1JwZjB6SmpXSnhxZDJKcWtyUDR1UWNXRGMwMzBhSmJQbE1nRjRlTEJaRlpYc0QrNUhSc1JlalR5ZkZNK3g2Uk9kWlBTaGQrSmhTeFNFMFpSeGVCQkZSSitKOFFpZmh0QjV2ZktiT1FCS25icGJRM0RSc3VtdkNPMytDbitzNUpmb2U3Ulp3REROeGNreWFPRzBQYVZKbk5SWlRiTUlHN1RwazFzeHNTZU4zWVE5Ny95Smd1NmZ3RWxZTDZxZGdQZE12ZTFYMXpkSWxxa05rMit5ZVVZb0M1bEZndjRuSmxqUENRTEk3QldhL0tEWk1pUXJkMDdTa2p6d1U5UklVcEVDT2tDNFg5SkszSkMvUVFnTHRna083OGY2NEswcFlScW8xQzRXV0tMSkl5bFRzNEpUWk12Sm1Ec1hWcHBaSUdBREJoc25pMDhaVnZKMEJTdmVYSkovQ1IzNFN3anZDL21SUVA0bmdtRy8zWEpCV0tSTTdoWmYraDU4bDlXUHEydEhWMnZLczR4UnciLCJtYWMiOiI5MjZjNGExNDUxN2MzNDU2Y2Y3YTM5ZDNiZjA1NjAyNGFkOThiODM1YzU2NzYzNDE5MGJiZmJhMTViODIxODJlIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:18:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitUMHhZMHNhci9ZUmJQSkMxQldXVmc9PSIsInZhbHVlIjoiYmQ5TE9jeTlzQzBQazJnOHJRaUhzWk1uOWNnWEtFcTg5bzJ2elNWSHZnRkc2WmtoUk9Xb05ZdWNKWFJUOThwMHFiNDJYQTRRYkdSSEY1SWF6clhEd3dHaVB3Ulh6MjZybGNVY2lRRHBidzNvblJGVlBjQVM4aER1dE1LaDVGb1lLT092MEhEc2srbGkwWHhrSlpETEs5cmVhbk9LSDlTeXBXM0ZOcmQyOERsY1dqT1J5YittUFg3WDNWVlMwK0g4dHlYN1Y3T1VOMjZLcGF3Y3hjTmZrV1gxdWdtTHBFK3NnblJsS2RPejlSRWhlNGE2ZUFNRTA0UHQ3MEFSTmRCcXBQNVRIL3NzUzdIdjA2bG5RTk5OZHNBMmxjQnRzMVVpZVEva1p3azhYbzNha09DTFhMb0F2OW9hN01rSk1HbGpSYllpNzczOENDWW5xOXNXTVFHUTRsZnMrMEV0SnNqamtJR0JvK1g3aGdPTjFBdlhSK0F1bllsazJvK0ttQmRZQ3prcWZzSXBsSE1sS3RUU2lONkRXT2pwMWFFbmhzRmJpVmt1VzBiRm1LcGtPeEFMeE5XT1hUUkNoNERZOUxYb21TbHkvSDBjYmFPL3NTMlZhN0FBZDhlSmIzSGM2RGZGNUlESmhSYkh4WDdYbVZRQ2JUNzBTeE1HdUFjQ0ZYRDYiLCJtYWMiOiI2Yzg4ZTI0NjE0MTUzZjBmNTdjNjIwNTE0ZDk5MjBiNmRiM2E1NDUyOWQ2MWNmYTM5NGNkZWZkMzg0OThlNjAxIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:18:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpPcnVpdFNJWk9ZM1dwbXYzbzgxeHc9PSIsInZhbHVlIjoiQ3dMRnM3SkxrSmZTZjQwNlF1VVJXMFNkcVhwMWxyZk0ySEpSTEdmeFlveXpmM3p6NkJtbE9NbHpvU0g2cFFhUFd6cm14M3h6ZElxNG0vdlNRckFRWXN4S090b2JNTHhlaWM0Z3U4cUtQMzZGRHE0ZitvMFh2M1JwZjB6SmpXSnhxZDJKcWtyUDR1UWNXRGMwMzBhSmJQbE1nRjRlTEJaRlpYc0QrNUhSc1JlalR5ZkZNK3g2Uk9kWlBTaGQrSmhTeFNFMFpSeGVCQkZSSitKOFFpZmh0QjV2ZktiT1FCS25icGJRM0RSc3VtdkNPMytDbitzNUpmb2U3Ulp3REROeGNreWFPRzBQYVZKbk5SWlRiTUlHN1RwazFzeHNTZU4zWVE5Ny95Smd1NmZ3RWxZTDZxZGdQZE12ZTFYMXpkSWxxa05rMit5ZVVZb0M1bEZndjRuSmxqUENRTEk3QldhL0tEWk1pUXJkMDdTa2p6d1U5UklVcEVDT2tDNFg5SkszSkMvUVFnTHRna083OGY2NEswcFlScW8xQzRXV0tMSkl5bFRzNEpUWk12Sm1Ec1hWcHBaSUdBREJoc25pMDhaVnZKMEJTdmVYSkovQ1IzNFN3anZDL21SUVA0bmdtRy8zWEpCV0tSTTdoWmYraDU4bDlXUHEydEhWMnZLczR4UnciLCJtYWMiOiI5MjZjNGExNDUxN2MzNDU2Y2Y3YTM5ZDNiZjA1NjAyNGFkOThiODM1YzU2NzYzNDE5MGJiZmJhMTViODIxODJlIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:18:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitUMHhZMHNhci9ZUmJQSkMxQldXVmc9PSIsInZhbHVlIjoiYmQ5TE9jeTlzQzBQazJnOHJRaUhzWk1uOWNnWEtFcTg5bzJ2elNWSHZnRkc2WmtoUk9Xb05ZdWNKWFJUOThwMHFiNDJYQTRRYkdSSEY1SWF6clhEd3dHaVB3Ulh6MjZybGNVY2lRRHBidzNvblJGVlBjQVM4aER1dE1LaDVGb1lLT092MEhEc2srbGkwWHhrSlpETEs5cmVhbk9LSDlTeXBXM0ZOcmQyOERsY1dqT1J5YittUFg3WDNWVlMwK0g4dHlYN1Y3T1VOMjZLcGF3Y3hjTmZrV1gxdWdtTHBFK3NnblJsS2RPejlSRWhlNGE2ZUFNRTA0UHQ3MEFSTmRCcXBQNVRIL3NzUzdIdjA2bG5RTk5OZHNBMmxjQnRzMVVpZVEva1p3azhYbzNha09DTFhMb0F2OW9hN01rSk1HbGpSYllpNzczOENDWW5xOXNXTVFHUTRsZnMrMEV0SnNqamtJR0JvK1g3aGdPTjFBdlhSK0F1bllsazJvK0ttQmRZQ3prcWZzSXBsSE1sS3RUU2lONkRXT2pwMWFFbmhzRmJpVmt1VzBiRm1LcGtPeEFMeE5XT1hUUkNoNERZOUxYb21TbHkvSDBjYmFPL3NTMlZhN0FBZDhlSmIzSGM2RGZGNUlESmhSYkh4WDdYbVZRQ2JUNzBTeE1HdUFjQ0ZYRDYiLCJtYWMiOiI2Yzg4ZTI0NjE0MTUzZjBmNTdjNjIwNTE0ZDk5MjBiNmRiM2E1NDUyOWQ2MWNmYTM5NGNkZWZkMzg0OThlNjAxIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:18:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-11******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11********\", {\"maxDepth\":0})</script>\n"}}