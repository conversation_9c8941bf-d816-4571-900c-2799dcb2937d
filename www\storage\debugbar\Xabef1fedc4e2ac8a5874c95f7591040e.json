{"__meta": {"id": "Xabef1fedc4e2ac8a5874c95f7591040e", "datetime": "2025-06-06 20:39:28", "utime": **********.142588, "method": "POST", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242366.846189, "end": **********.142639, "duration": 1.296449899673462, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1749242366.846189, "relative_start": 0, "end": 1749242367.967274, "relative_end": 1749242367.967274, "duration": 1.1210849285125732, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749242367.967295, "relative_start": 1.1211059093475342, "end": **********.142645, "relative_end": 5.9604644775390625e-06, "duration": 0.17534995079040527, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44988960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=75\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:75-235</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00559, "accumulated_duration_str": "5.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.099776, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:78", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=78", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "78"}, "connection": "ty", "start_percent": 0, "width_percent": 79.07}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.114479, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:81", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=81", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "81"}, "connection": "ty", "start_percent": 79.07, "width_percent": 20.93}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SUUVTu80AHiedXP391HuTFHrIH0VO8njAH49hdss", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"status\"\n  ]\n  \"new\" => []\n]", "status": "Your Account is disable from company.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SUUVTu80AHiedXP391HuTFHrIH0VO8njAH49hdss</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1605836067 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">142</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">XSRF-TOKEN=eyJpdiI6Ilp3Ym9zeG5PbGpkTWFqb3cxV3hUQlE9PSIsInZhbHVlIjoiQlZicnVRTU53TDM2dUliVHVPSVdUZDE0RHBtelF4WTFHbys1elhnc2l1dFhZN3UxVmtlaWlYdkJpalVXSWJOMGRKWkNiUElna0ZqMjBKNVVpTG9TRGVZQWEwWUo2U1FMVk5VZURxTnZYbmdKbjBBRDFMZ04ycjJuUmRCNE01QzBwbXZ5UHJYWkN3Sm5rQjIrMFhCRitzaXJ3NVVwVTIxYzZMbnZFZkRNdkdmM2MxbDJuV2l5aC8yRmdvRzVlMExaMDV4M21TanhudFJzb1E1MnJ4ak1ySVBFZ1dJTVo0NnpUekJUbk9hdzlmRmQ3UUE0N0xJQXZiT3BxdmJJMEZqZWc1UmFIREM2WVVWRjFKcWppRnlCT1o0QWtIN1o4Q3F0NDhnalFkd2dvSU5xd2ZJaXRtQVNoalA1OFg0a29FRGFFaC9RaU1nRTFyN2JSaWFHaEVkYTJNVndHQjQ3dHJ0bWpFcFdzejRCVEV1MUQ2RVdvdk56b0wyQlBpWUs1azFWUm1YMXlDVzBMTVFWNlVNUURYaTgvK25ZTjQxYkhSKzY3QWtYOUl4dDgwU0F6V3lsOXdIN2JrOGJ0eUZPd3p6L1M0TVc3NTF1cm1zMU45cTVMM3ZmZktUcmpuN2RVS0VIbG5BelkrVFRSRDNRN0Y0OFMzWDlIMjdQbndsTEtMTjQiLCJtYWMiOiI3MDE2N2Y1ZGIzNmNiN2Y0ZTFlMmZiYjhjZmY3MzE0Y2E0OWM0NDIxMjg1NGEzMzUyNjk5M2NlY2M4YzJmNmVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InMxaTBwckJLaC9mR1ZTcHIyRGxza0E9PSIsInZhbHVlIjoiVEFmUmltN3k4ZVZLZmdicHJub1EyQlBHNTY0aHp3a0QrTW5tcUNhVkFVeldrNHFBWEtYOTZPUHR5UWtPWkMxaWRKQ1FlUVJZdTEwcG8wemNnbWJrZlpmc3l3WFFjaW92YUZTaGhTbkttN0Nra2I5QWhVNllZaUU0R2RIck5ndCtuaU53cHhGenR0UkQvc3l3OUFqbjhOREhpakNCdXU4R1l2R2RDYkVTQ2VITWtUWFJTemdKLytXclgrUnF5RjJabDU4amJaTlYvdDI2ZHZXRmw0MEdvMTlhdWFtUk85RytSNG1TSENJVk9rbFFoZVFZMnZFTHlleDE5UUJWb0N2aDhFazdTNDV5ZWRneFcvdmN1bFdFQ1BCRVY2R0hGd3JiS0ovRXFuTHQ4Mmx3TGVET1pHOGJ6L29heVQzWWlPc2tJa1JmNGdmSWJwaUdkN0NwcmpFZ3VKblFjdTRjV1BoR1F2bnJoZkpBbmFweHRLY0RDMEF6NXMwVC9xY0ZHcUVFN1puMFRLbTNlWkpGQ05pa2tzUjI3KzMxY29uTjJRS1ZOMGd5b2lGZGJpSFRXV3pwdzVxN205VHhJVld0YVo0RkVsQ1JCZm1BcFRiY3Q1SU9ZZmJVVEVLWmZpUUFRWGl1dHdLUU85bnU3OHBYVlU5VVhONnlrc0ZNY2s3S2VBRFIiLCJtYWMiOiIzMmRhMWVjMGNlYzcwMjAwMDg5ZTAxMDRhNjhhMzVjMzgzYWY0NWE5YWIxY2ZjODhjODM4NzljMDMzMjliYzRmIiwidGFnIjoiIn0%3D; _clck=swfud1%7C2%7Cfwj%7C0%7C1983; _clsk=1jtjr1w%7C1749242230782%7C1%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605836067\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SUUVTu80AHiedXP391HuTFHrIH0VO8njAH49hdss</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gIOQUBhbw9zlBSGUVp7E8CKRziTFyLTcR7ERMnrt</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-861713028 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:39:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; expires=Fri, 06 Jun 2025 22:39:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNlaTR5RExHdDBNRk9vS2pVeUJiNXc9PSIsInZhbHVlIjoidXVxMkt6WFV2dUtSY2RxUDRQc2FFNkFZamhIaFNLSy8xbjBUb3dBeGlsWktZMkNkWFlrMG5DWlFGSmpEMVhtSnZCdkNobWU0RG1vTmlvOW9yNUs5bFRqWGVKc2l6WGtSVUFUaENWbU9sZTF5eG5oamJ3MmdzZHVxSCtQL0NyNm16TkV5RXlndkFaV0lLemU2aDFnQXZUM2IrRWk1a01UczZxcmgySVpxL1lzYU1Vb1hsZXBva1c2VDZKNFhYbDROM2ZpY0wwSnQwNFhtVmJuS0RJeTFCendZejVzaFFLU3lkeUtPS2ZJTGVQMk0wL1VIMlZPRW5PdVZpa0VzSUNRTGt2WlBYUUhyRjVOWkpUWW02SDZVeGNFZW5VV0FWZlFvUHc2cjNNNWRaeWdpdTR5MGJuUi9qWTBjeWpkOHdiTUk4aERNYlg5TGZSRDNDL0VrTkh5ZXNRcEwvVGNHQUVJamFXUE44MFF4YmlUOW15UWJ0aWZXVXJLczJEcDRNeTFCUEh5MHc1UGlLWmFNRStnUjB0b3RWMGNwanhrS3FmQmROTWFtYjdVbHZFekNxR0s1b1IzcFNnaUQyRVhkeTVPSzludXVFd0F2cFd1ZXBoRUF1a0NCbi9CdzFXWWF3cFY5N2diSjNyQ0IxVjdvSWFsbWRJbmhZY2taeG5mdUdkWlUiLCJtYWMiOiI3NmM2ZTZlNWQxOGIyZjY5M2M4YjI0ZjYzNWI3NDM1NzM2ZTAyNDdmNDE0Y2M0ZTIwNWNmYTA3YTVlOTliMjE5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; expires=Fri, 06-Jun-2025 22:39:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNlaTR5RExHdDBNRk9vS2pVeUJiNXc9PSIsInZhbHVlIjoidXVxMkt6WFV2dUtSY2RxUDRQc2FFNkFZamhIaFNLSy8xbjBUb3dBeGlsWktZMkNkWFlrMG5DWlFGSmpEMVhtSnZCdkNobWU0RG1vTmlvOW9yNUs5bFRqWGVKc2l6WGtSVUFUaENWbU9sZTF5eG5oamJ3MmdzZHVxSCtQL0NyNm16TkV5RXlndkFaV0lLemU2aDFnQXZUM2IrRWk1a01UczZxcmgySVpxL1lzYU1Vb1hsZXBva1c2VDZKNFhYbDROM2ZpY0wwSnQwNFhtVmJuS0RJeTFCendZejVzaFFLU3lkeUtPS2ZJTGVQMk0wL1VIMlZPRW5PdVZpa0VzSUNRTGt2WlBYUUhyRjVOWkpUWW02SDZVeGNFZW5VV0FWZlFvUHc2cjNNNWRaeWdpdTR5MGJuUi9qWTBjeWpkOHdiTUk4aERNYlg5TGZSRDNDL0VrTkh5ZXNRcEwvVGNHQUVJamFXUE44MFF4YmlUOW15UWJ0aWZXVXJLczJEcDRNeTFCUEh5MHc1UGlLWmFNRStnUjB0b3RWMGNwanhrS3FmQmROTWFtYjdVbHZFekNxR0s1b1IzcFNnaUQyRVhkeTVPSzludXVFd0F2cFd1ZXBoRUF1a0NCbi9CdzFXWWF3cFY5N2diSjNyQ0IxVjdvSWFsbWRJbmhZY2taeG5mdUdkWlUiLCJtYWMiOiI3NmM2ZTZlNWQxOGIyZjY5M2M4YjI0ZjYzNWI3NDM1NzM2ZTAyNDdmNDE0Y2M0ZTIwNWNmYTA3YTVlOTliMjE5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861713028\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SUUVTu80AHiedXP391HuTFHrIH0VO8njAH49hdss</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Your Account is disable from company.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}