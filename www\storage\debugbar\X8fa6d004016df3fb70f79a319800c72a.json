{"__meta": {"id": "X8fa6d004016df3fb70f79a319800c72a", "datetime": "2025-06-06 19:18:14", "utime": **********.825387, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.243723, "end": **********.825427, "duration": 1.****************, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": **********.243723, "relative_start": 0, "end": **********.593106, "relative_end": **********.593106, "duration": 1.****************, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.593134, "relative_start": 1.****************, "end": **********.825431, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02596, "accumulated_duration_str": "25.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.702416, "duration": 0.02281, "duration_str": "22.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.866}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.759551, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.866, "width_percent": 4.507}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.798317, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.373, "width_percent": 7.627}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237490993%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdiSnB6RzJYOWEvaGNTa0NSY2pGdnc9PSIsInZhbHVlIjoiSzRraktPd0I1L25vczBCV2pNbFFDcDJXQzBLZXFyRjRQWnBXWVFLWDk4anZVS1BFVXF4U3ptS0Q3Vk1jZWtaTlhqeTVRbTdrWXp1UFNLaEp5ODhsRnRDUnQ0MUtYeVVMd2x6WGIzYU0xbmlmbkhBbk94WDJKQ1oremYxbGNWbDNpc3kxeXBsSHJZS3RwblBJeHpGR2VtcWZPNXQ0REJoNXdLQi9NT2JKd2FSUmdvbUNmMUxBWEl6ZFBVbzloMDk2Z1hSYzZaRFhsUU9SNE5PSUVVcXp0NTJveXFIMFB5a3hFVUwxUXRuTmJTR3lCNkFrMDduZGNxYnFzOHhTUXBJUEltMlpPSUREWHQyYndYN3djVXJzRjQzbFQ5aHlyQisxRVRXY1NvdUdocDF2VFhjWmpDY0hUMytZOWRlbVVtNG4zaGlnSUJlZmZ2YlB5WnZaaGExU1hleUFsTSsyQlJvL0JSRzZuZGd4R0JUMmJjSXRlOWhkN2pBaGEwajJzNVRBTGR3Z0tUcGxTRnh1c0tqNDdCazRzTEFFY01zNUdiT3UvTVRTUU9Cdm53K0FYRENvSGFDekF0UnAwV1g1eElTZGFEKzVqaGxFK2dleUtXamZ0Nm16YnBCSlJzV3VKS0dBWVhRakhVS20vZXdvZWZDVHFJOVJUckR0SVp4MW5YdTkiLCJtYWMiOiI3NWIxMWJiMTA0Mjc1MzU0MzUyNmQ0OTUzOGYxNzVjMjBmZTZhNDJkMDVkNzk0ZTg4ZDY5NDJmODEyOTE3ZWQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikdzd2tMU2RGTEZPbU9oU0xLNWQvTkE9PSIsInZhbHVlIjoiY2xKK2FpSnlRS2ZmZUhMQzhBclg3VU1vdnhJSVdzaDBiZ2o3ZXJGejNKeEVyWGNUeWwxYlJuZ0xKbDM3K280eDM4T1pPazBuTHdRQ0dXdzZwaFEyNU1SUDBBY1cwY09vUnlCYlhXVkE2SkhCNXZrT3BIQ2hVOVVwVXlNL0VETVdrTmhNQ3JkejZ4RjZ2dFZITllEU0JaQmlnQVRQYm1rOFl4WVN1NnIxMFF0bTJPLzBmeEphcndzU01EZlRYaXBaUHI0REFhaWZKY2ZPS3NpVGZlYWMycjl3c0RvQ3hOait2dDVKRFJBOHZiZDVuR2NlUDBPc3cxNXF5VnAwd3Z0dGd2VkZIbThOL29JVmhpZVhHY1lObXlydjZveG9CUUpYNmpYR2VxaW5pa0g1em1XcUxFV0Y5dnJXYjhwWEMyZ3JTZFl2dE9MTWtoeXBnUkRqcVF5VjEzM3p5Q09jQjNQVFJXNVlnMDkxbVJQMVBOckxjN05Ea25GbExEVVpRN2p0cXRHNFM4SzJ3K0JXNHlpK3o1dGg2b0xqekYxa3RyMFJjd3RXVjJxSFJoN0JrSldvT0hER3piZXNYdWc3V0RIQW9RYSs4d0RzNGtyN3FkYmtkZUN5MUl5c1FMMFQ4MFM5Q1lwcEo5VFF2M2RLU1RnbDVCSWdDTERVMnFuajJVS28iLCJtYWMiOiIyZDAxZjQzOGRjZTQwOTVmZDY1YzI5YjM5YzdmMzIzODg5MDFmNzliNDMzZWU3YTY0N2NmMTJhOGNiYmUyZmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-23566487 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">07zLQ1IzhcCwivgYmqVlKD2YgfwQ5DGdasvfNucP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23566487\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1508995055 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:18:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxZdUJHcVl0Q3IrR2tVbmdoNEhKTmc9PSIsInZhbHVlIjoiLzgzSWd5c2FlTWdzT3RyaFJseS8yTldvU3dNSy9VNkZMcW44Y0lQWXQ4TTJnNDJVMmFxbUFlb2VGd2FzNXhVcnpNSk9hZnRsbjhzdjI4YnJUajVhWVliYTRjUUhxeFdlNTRxa3ZneG1pYUtXb2ZKVWZxYjNCRWE4N3hSck85UFVXVERXOGRJMklwdCtHUkdLbSttMXpGbW50ZFRzZXkwaUJwYTdpUzBPVzl4Ulk2cnQ2NGZGaVpYRkFWdTNuM21aUS8veHUzUm1wcXJDdU5kekJVaTBrZzhxaXdlSHRTdGNXbFNXbER2d29xd2RXQU0xNlpjQ00wSktkRFpUWC94R1JkQWo0YkRSazVFcEtSbm9jMEU3N0hvRVMzdjNtTExOekMyWDNXN0V1UXY1dzN2d1VHVTU4YVJsVXhmeSszUXkyTkhhaTRIaEo5dUduYkx3QXdlTkFkTDB1SmtYaVRpNXRYVCtBVjViOXZId0l0cmJuVkFpMjVYZ2RYcjZPc3BIN01FWlAxYWVVV2hGaHRsSllteDRiL2EvU21OZ05BRzJlRXBhS25vNXFOMHU3STlJbksxaENBUStCSmZtajQ5MnJMcFF0d1E1eXUzeFZoN01WZHVpSWo4Z1NPd1BsNzlZK3JmZ202ZTN0blYrS3cySVE2MXNLZEdTWFJySFVMd3giLCJtYWMiOiI4YjY0MTY5ODBlYzY4ZGNkMGQ0ZjVjMmE5MjBiMjdmY2I2ZjBiOTY5NjI4ZjBhNjQ3MzY5M2RkNGU3OWRkZTMyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVRMjl1UXBkTWVPWjBtQVpYMjFyV3c9PSIsInZhbHVlIjoiZEloQTd3cDBDU09maUZzRHg0dWMxYVRiMG5DTzN2RDBzZFJ3MkVuSFZtS0orUWdWeGNJclh0cTY3dFRRSmJ2UGdlQkRnUFFrV09RUWgzN0lOeFBZMm9oYW91Y3Vnei9pRUFHUnp6ZUNaTHR3bDFTSEE4L1hPUG9aQzJqR3I0Wi9keXU4SWluTjIybFpkZmdBaEd4bG1vaTZJaDVtelVIRFIwRGExZWY0UDJ1bUtDSThhekdUSEMxZ085RFc5R0FNZlR0RHIrdWgrUlVHQUFoRU5qUUN3L2FNM0FiSTdpS25SamZhaUdCV1RoQ1E3bGFuek9wNWNsYjNaUEpqYnZ6UDVyV1BXdEI5ZGY2V2dkK2tvMzByYS85aXhva3YzUTY3bjdlcU9jbmhKQnJTb3UzbmlWR3N2WWUxMjhzQzNFN1l6c1FRZjIxWDVkUnZtdXJ1UVk0OGNQWWRzNVhVbW9ZWkZ4VXI0TWp3aFc2L3F3T3lxRkd1WkJ0d29EUXp3K3hUTVplQ2JPV2Y1aEkvZ0JYZU9aNU10blVlTzNWTHdIZ2JFWExxck8veTVxN2dVb0dIeGVQci9CcngzYmJ2WUcvUUJJVll3YjRHSXdPOVJHMjE4N2hjVUJOQURNSjB4dnRpbG5QSGpYZDhONHd1K2Q5dHdYbVkzbmhUN0M5VENDUmMiLCJtYWMiOiIwMTI0YTI2MzI1Yjk5ZWY4NzA0MTc1ODU4NTU0NTQwZWM1MzRjMWVjMjE3MDljYzNkMWU0YmFiZTlkMWU4ZDk0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxZdUJHcVl0Q3IrR2tVbmdoNEhKTmc9PSIsInZhbHVlIjoiLzgzSWd5c2FlTWdzT3RyaFJseS8yTldvU3dNSy9VNkZMcW44Y0lQWXQ4TTJnNDJVMmFxbUFlb2VGd2FzNXhVcnpNSk9hZnRsbjhzdjI4YnJUajVhWVliYTRjUUhxeFdlNTRxa3ZneG1pYUtXb2ZKVWZxYjNCRWE4N3hSck85UFVXVERXOGRJMklwdCtHUkdLbSttMXpGbW50ZFRzZXkwaUJwYTdpUzBPVzl4Ulk2cnQ2NGZGaVpYRkFWdTNuM21aUS8veHUzUm1wcXJDdU5kekJVaTBrZzhxaXdlSHRTdGNXbFNXbER2d29xd2RXQU0xNlpjQ00wSktkRFpUWC94R1JkQWo0YkRSazVFcEtSbm9jMEU3N0hvRVMzdjNtTExOekMyWDNXN0V1UXY1dzN2d1VHVTU4YVJsVXhmeSszUXkyTkhhaTRIaEo5dUduYkx3QXdlTkFkTDB1SmtYaVRpNXRYVCtBVjViOXZId0l0cmJuVkFpMjVYZ2RYcjZPc3BIN01FWlAxYWVVV2hGaHRsSllteDRiL2EvU21OZ05BRzJlRXBhS25vNXFOMHU3STlJbksxaENBUStCSmZtajQ5MnJMcFF0d1E1eXUzeFZoN01WZHVpSWo4Z1NPd1BsNzlZK3JmZ202ZTN0blYrS3cySVE2MXNLZEdTWFJySFVMd3giLCJtYWMiOiI4YjY0MTY5ODBlYzY4ZGNkMGQ0ZjVjMmE5MjBiMjdmY2I2ZjBiOTY5NjI4ZjBhNjQ3MzY5M2RkNGU3OWRkZTMyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVRMjl1UXBkTWVPWjBtQVpYMjFyV3c9PSIsInZhbHVlIjoiZEloQTd3cDBDU09maUZzRHg0dWMxYVRiMG5DTzN2RDBzZFJ3MkVuSFZtS0orUWdWeGNJclh0cTY3dFRRSmJ2UGdlQkRnUFFrV09RUWgzN0lOeFBZMm9oYW91Y3Vnei9pRUFHUnp6ZUNaTHR3bDFTSEE4L1hPUG9aQzJqR3I0Wi9keXU4SWluTjIybFpkZmdBaEd4bG1vaTZJaDVtelVIRFIwRGExZWY0UDJ1bUtDSThhekdUSEMxZ085RFc5R0FNZlR0RHIrdWgrUlVHQUFoRU5qUUN3L2FNM0FiSTdpS25SamZhaUdCV1RoQ1E3bGFuek9wNWNsYjNaUEpqYnZ6UDVyV1BXdEI5ZGY2V2dkK2tvMzByYS85aXhva3YzUTY3bjdlcU9jbmhKQnJTb3UzbmlWR3N2WWUxMjhzQzNFN1l6c1FRZjIxWDVkUnZtdXJ1UVk0OGNQWWRzNVhVbW9ZWkZ4VXI0TWp3aFc2L3F3T3lxRkd1WkJ0d29EUXp3K3hUTVplQ2JPV2Y1aEkvZ0JYZU9aNU10blVlTzNWTHdIZ2JFWExxck8veTVxN2dVb0dIeGVQci9CcngzYmJ2WUcvUUJJVll3YjRHSXdPOVJHMjE4N2hjVUJOQURNSjB4dnRpbG5QSGpYZDhONHd1K2Q5dHdYbVkzbmhUN0M5VENDUmMiLCJtYWMiOiIwMTI0YTI2MzI1Yjk5ZWY4NzA0MTc1ODU4NTU0NTQwZWM1MzRjMWVjMjE3MDljYzNkMWU0YmFiZTlkMWU4ZDk0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508995055\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-64036691 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64036691\", {\"maxDepth\":0})</script>\n"}}