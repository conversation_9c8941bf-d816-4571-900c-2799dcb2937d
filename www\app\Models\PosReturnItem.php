<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PosReturnItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'pos_return_id',
        'pos_product_id',
        'product_id',
        'quantity',
        'price',
        'tax',
        'return_reason',
    ];

    public function posReturn()
    {
        return $this->belongsTo(PosReturn::class);
    }

    public function posProduct()
    {
        return $this->belongsTo(PosProduct::class);
    }

    public function product()
    {
        return $this->belongsTo(ProductService::class, 'product_id', 'id');
    }
}
