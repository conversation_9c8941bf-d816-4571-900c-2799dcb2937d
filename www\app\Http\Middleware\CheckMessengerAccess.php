<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckMessengerAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (Auth::check()) {
            // Check if user has Delivery or Cashier role
            if (Auth::user()->hasRole('Delivery') || Auth::user()->hasRole('Cashier')) {
                return redirect()->route('dashboard')->with('error', __('Access Denied. You do not have permission to access this feature.'));
            }
        }

        return $next($request);
    }
}
