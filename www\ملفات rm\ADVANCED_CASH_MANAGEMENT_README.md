# 💰 شاشة إدارة النقد المتقدمة

## 📋 نظرة عامة

شاشة إدارة النقد المتقدمة هي نظام شامل لمراقبة وإدارة التدفقات النقدية في الشركة. تتيح هذه الشاشة للمستخدمين مراقبة الشفتات، حركة النقد (قبض/صرف)، وإجمالي مبيعات نقاط البيع (POS) في مكان واحد.

## 🎯 الميزات الرئيسية

### 1. 📊 الإحصائيات السريعة
- **إجمالي المقبوضات**: مجموع سندات القبض + مبيعات POS النقدية
- **إجمالي المصروفات**: مجموع سندات الصرف
- **صافي النقد**: الفرق بين المقبوضات والمصروفات
- **الشفتات المفتوحة**: عدد الشفتات النشطة حالياً

### 2. 🔍 نظام الفلترة المتقدم
- **فلترة حسب المستودع**: عرض بيانات مستودع محدد
- **فلترة حسب المستخدم**: عرض بيانات مستخدم محدد
- **فلترة حسب التاريخ**: اختيار فترة زمنية محددة
- **تحديث فوري**: تطبيق الفلاتر على جميع البيانات

### 3. ⚠️ نظام التنبيهات الذكي
- **شفتات مفتوحة طويلاً**: تنبيه للشفتات المفتوحة أكثر من 12 ساعة
- **عجز في النقد**: تنبيه عند وجود عجز في الصندوق
- **مبالغ نقدية مرتفعة**: تنبيه عند تجاوز النقد حد معين (10,000 ريال)

### 4. 📋 جداول البيانات التفاعلية

#### جدول الشفتات:
- عرض جميع الشفتات مع تفاصيلها
- الرصيد الافتتاحي والنقد الحالي
- حالة الشفت (نشط/مغلق)
- أزرار الإجراءات (عرض/تعديل/طباعة)

#### جدول سندات القبض:
- رقم السند والتاريخ
- المستلم من والمبلغ
- نوع الدفع والغرض
- حالة السند

#### جدول سندات الصرف:
- رقم السند والتاريخ
- المدفوع إلى والمبلغ
- نوع الدفع والغرض
- حالة السند

#### جدول مبيعات POS:
- ملخص المبيعات اليومية
- عدد الفواتير
- إجمالي النقد والبطاقة
- الرسم البياني التفاعلي

### 5. 📈 الرسوم البيانية
- **رسم دائري**: توزيع أنواع المدفوعات (نقد/بطاقة/مختلط)
- **تحديث تلقائي**: تحديث الرسوم عند تغيير الفلاتر

## 🚀 كيفية الاستخدام

### 1. الوصول للشاشة:
1. تسجيل الدخول بحساب له صلاحية `show financial record`
2. الذهاب إلى القائمة الجانبية
3. اختيار "إدارة العمليات المالية"
4. النقر على "إدارة النقد المتقدمة"

### 2. استخدام الفلاتر:
1. **اختيار المستودع**: من القائمة المنسدلة الأولى
2. **اختيار المستخدم**: من القائمة المنسدلة الثانية
3. **تحديد الفترة**: استخدام منتقي التاريخ
4. **تطبيق الفلاتر**: النقر على زر "بحث"

### 3. مراقبة الإحصائيات:
- مراجعة البطاقات الملونة في أعلى الشاشة
- الانتباه للتنبيهات المعروضة
- متابعة التحديث التلقائي كل 5 دقائق

### 4. استعراض البيانات:
- **التنقل بين التبويبات**: الشفتات، سندات القبض، سندات الصرف، مبيعات POS
- **استخدام أزرار الإجراءات**: عرض التفاصيل، التعديل، الطباعة
- **مراجعة الرسوم البيانية**: في تبويب مبيعات POS

## 🔧 المتطلبات التقنية

### الصلاحيات المطلوبة:
- `show financial record`: للوصول للشاشة الرئيسية
- `manage pos`: لبعض الوظائف المتقدمة

### المتصفحات المدعومة:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### الدقة المطلوبة:
- الحد الأدنى: 1024x768
- الموصى به: 1920x1080

## 📱 التصميم المتجاوب

الشاشة مصممة للعمل على جميع الأجهزة:
- **أجهزة سطح المكتب**: عرض كامل لجميع الميزات
- **الأجهزة اللوحية**: تخطيط محسن للشاشات المتوسطة
- **الهواتف الذكية**: واجهة مبسطة ومحسنة للشاشات الصغيرة

## 🔄 التحديث التلقائي

النظام يحدث البيانات تلقائياً:
- **كل 5 دقائق**: للشفتات المفتوحة والإحصائيات
- **عند تغيير الفلاتر**: تحديث فوري لجميع البيانات
- **عند النقر على تحديث**: تحديث يدوي شامل

## 🎨 دليل الألوان

- **أخضر (#28a745)**: المقبوضات والأرباح
- **أحمر (#dc3545)**: المصروفات والعجز
- **أزرق (#17a2b8)**: المعلومات العامة
- **أصفر (#ffc107)**: التنبيهات والتحذيرات
- **بنفسجي (#667eea)**: العناوين الرئيسية

## 🔒 الأمان

### حماية البيانات:
- التحقق من الصلاحيات على مستوى Controller
- حماية من هجمات XSS
- التحقق من صحة البيانات المدخلة

### تسجيل العمليات:
- تسجيل جميع العمليات في logs
- تتبع المستخدم المنفذ للعملية
- حفظ تاريخ ووقت كل عملية

## 🐛 استكشاف الأخطاء

### المشاكل الشائعة:

#### 1. البيانات لا تظهر:
- **السبب**: عدم وجود بيانات في الفترة المحددة
- **الحل**: تغيير فترة التاريخ أو إضافة بيانات تجريبية

#### 2. الرسم البياني لا يظهر:
- **السبب**: عدم وجود مبيعات POS
- **الحل**: التأكد من وجود فواتير POS في الفترة المحددة

#### 3. التنبيهات لا تظهر:
- **السبب**: عدم وجود حالات تستدعي التنبيه
- **الحل**: هذا طبيعي إذا لم تكن هناك مشاكل

#### 4. بطء في التحميل:
- **السبب**: كمية كبيرة من البيانات
- **الحل**: تقليل فترة التاريخ أو استخدام فلاتر أكثر تحديداً

## 📞 الدعم الفني

في حالة مواجهة مشاكل:
1. التحقق من ملف logs: `storage/logs/laravel.log`
2. التأكد من الصلاحيات
3. مراجعة دليل الاختبار: `test_advanced_cash_management.md`
4. التواصل مع فريق التطوير

## 🔄 التحديثات المستقبلية

### المخطط لها:
- إضافة تصدير البيانات (Excel/PDF)
- تقارير متقدمة
- إشعارات فورية
- تطبيق موبايل
- ذكاء اصطناعي للتنبؤ

### طلب ميزات جديدة:
يمكن طلب ميزات جديدة من خلال فريق التطوير مع تحديد:
- وصف الميزة المطلوبة
- الهدف من الميزة
- الأولوية (عالية/متوسطة/منخفضة)

## 📊 إحصائيات الاستخدام

النظام يتتبع:
- عدد مرات الدخول للشاشة
- الفلاتر الأكثر استخداماً
- أوقات الذروة في الاستخدام
- المشاكل الأكثر تكراراً

هذه البيانات تساعد في تحسين النظام وتطويره مستقبلاً.
