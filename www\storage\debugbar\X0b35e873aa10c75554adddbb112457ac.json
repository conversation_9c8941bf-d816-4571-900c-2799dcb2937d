{"__meta": {"id": "X0b35e873aa10c75554adddbb112457ac", "datetime": "2025-06-06 20:40:30", "utime": **********.979647, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242429.386545, "end": **********.979693, "duration": 1.5931479930877686, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1749242429.386545, "relative_start": 0, "end": **********.76903, "relative_end": **********.76903, "duration": 1.3824851512908936, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.769049, "relative_start": 1.3825039863586426, "end": **********.979699, "relative_end": 5.9604644775390625e-06, "duration": 0.21064996719360352, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763080, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.011899999999999999, "accumulated_duration_str": "11.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.878755, "duration": 0.00734, "duration_str": "7.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.681}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.915589, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.681, "width_percent": 8.824}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.923237, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 70.504, "width_percent": 13.361}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.948981, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.866, "width_percent": 16.134}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-484364381 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-484364381\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1181066796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1181066796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1709482939 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709482939\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-19308555 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=swfud1%7C2%7Cfwj%7C0%7C1983; _clsk=1jtjr1w%7C1749242371143%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikx5MEtpYW5XOHl5RHVGVXdubjJZY1E9PSIsInZhbHVlIjoiNTMzeUFaR29LNzJDU0ZYTDNONEZ0TjJ1ZnoxRGQxS25JN0tQRnJKekdZdjBWcjNYSEsrL015QkRNanZrZEx3eHRsa2hyNGlHKzhNQ3IzSDZ0dm82c1BITlhEcUx5VXBNblNsNk1vTWNVZTl4THl2Z1REcXR5SzlNZUwyUEdiZnlyT1RvL01HaXpqblVSeUsvVHNCWnBRU3l1MlN2eXIzcHZ3WmdsWW5KWjFFbW8zRU93dWt1VW1wOExtRE1wMGo3YmtJVFhNMS9nOG1rVXhUUVFiTGM4MkZ5OVNZUXdtbGIwMEVoc211UlI3b0grNSs3TUc3eGRSbUNGQmVSNHNWMHp1UGh6bFd3TTNncjNVbHBxR3RKTlYrcktJQ24yajVjK3R1dGNoTFdTUHhJWGdoV3ZIZE04NW4zNE1EWGtTTWlianIzYXAzNms5YUsxNXh2WkRjZXZoV2h1OFJ6aUFybXQ4K3lJajQ3NkNBTSt2NDFPR1NJemhuVTdXTjFLdVZTT096bzVlVU5PMjVaMUJDd3FkYjFMQzBNMU4wS2RsTHlnMkx0ZHV6azNEK1BtWVV4bWkvQzRaRmlUQzZLTmJMNXJwTFg3Wm9MK3ZCMmVaU0U4K2JGdkJYN3VleDh2cE9vZXlBWStUN1Y5L0RWMnNQK080S0laY2tTZG9KbjlVaVoiLCJtYWMiOiJkNTZjOWRiMGJkM2ViZDkwNGM1NjlhOTQ5MGMwYWU4MDQ2MmZkODhkMzFkNjFkM2I0NjVkMTRjYTVjMDZhMDBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitSd1NUSklKalIrV0xRMGNGdWwvTmc9PSIsInZhbHVlIjoieVpMVVpDK0lvUTIzSDZCQkJvU21sUVUybUlTTm83K0xMRFVsNlZQbEVqbUpjdnk0ZUxhMlhwTHRkVEJscktXYkx5TndQSWFMeU9mc1JNejRvM1E1YWZHbWdldENlLzJ0R1A4T25yeXVYNzhmNUMyRXoyZVpHTlNObzFjTEtEenVscDNpVi9jSXNpMEpmRFVWMFZraFZRWnBhekttNE5LTnh2cnc2aGRRZmdrWURuMmp6UElLRlFCUFpqYWFweTNEMHVKbFBXTDVaaDVSK2RQcUQrNG1kNGY2NnZKaUtMZCt5YkxUVVN1eWhORzlIYmFkcnVaOTFuK3pjUVJSU0JwZXhkTGJWT1NubUExWXlxeUd2cGNKRC9XY3RER3lnQnJxcjhWT3crbFo0eHNqV2VaS2xnRXZOdjJIb01pR2dTdXFrZU9iVXVWc2lzRnA2MUlnMk9NRXVRcDZmSk9SYmxvWlh0TFY3SkdzZFZYZmt3aVd0YktvV09ZOWJjc1dRRkFVeGxzU3NQZ091aU0xSWduUDdOcDBEdERaWVA2WWl6cmM5S1dmWkoxRXdVbjN1SGczdGNTRzQ0RHE2czdnWEJYa2twNmFidEZpeFRkRisveXRGd3k1VzBwQlNGWTA5akplTThRUHlid1RvV2RzQWw4Yy9DelpPbGhGYkVqcTIza0YiLCJtYWMiOiIzNmNjZGMwMGFkNGIxNjFhMmExMTgyOWZmNjJhNGUzMDBmMTA3YWM3MjMyNTRiMWI0YzllNDc5NTA1OWViMGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19308555\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-653174399 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EXNtVcDSCVpTrtQmjXbil1g1yDu1CX4tI4M8cWJU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-653174399\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-54901740 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:40:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InQ4ZW05QVovQzQ5SDlaamdLcEpZdlE9PSIsInZhbHVlIjoiQUYrYUEzZlJYZTNtZ093UGNHYnhpNnd5MFExOWhEUGhiNjI2emFvNHVsZGFHNWtzTGpIeGVqYWpqQkdZeTFoZDdmVXdLWmVFb01ibStLK2lGaXMvbWJWTk1qNFAyMkpFQTd3ZXQvbG9KM3V0NkJ2L2R1WkZsTHJzUHMwNTZnNFphTlVPRVZud2lDdmRRdU1WaFFJMTBDL2l6TWxTNlQvMEVObFRqM2RXOEFaWUJlRzVzVlpNTXdtbG9ZMHFKQXVHdUt4V1dyM3p6eXJoRzU1RFBscE4yajZydWhQMVRySG94dnhPdHRQdjBmL3hnUWFqRzNZKy9HVTdtMEUyVU1jSU5XWU9ibk1SWUN5eDZET1pPS2RXbWNxZlNRbWI0bWczbEpIbDBIUHd6YWZEc0dJN0UrSTUyUnN0OCt3VUozOHUvcGFjTUVVVkovUkNwL29QQW1saVJidHFvSVZzeGRDOXpJWFloNHl6TWt0aERjUk15aWl0Ylc5SG9aMVhZdEFCNGxiK1hDYzVIQUc1VGcrbm5Vc0RwblJpd0ExTG00alFkVGcwNEM5ZGMrWERha3Z6bVFEUzVIUTc4RWhwSnpaNm43M1BTUVZWeStTZFg0MU9LeWJLQVJZZnhGUzR3a0FqNEtaNkVRZ053UmMrYVhMZ2hQTnVJa1lCSEZhQkRrbEUiLCJtYWMiOiJmNjczZWNkYzM5YWJhZDFjNWNlNWNlZGFhNGY0ZjhiYmYxOWVmNjdlNWVmZGM4YmU3NTAxNWI4MmY1NGI0MzVlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNGZWc2Y0ZaRDFrWGQ5WHhVdVZCVFE9PSIsInZhbHVlIjoiQmRBZmdDRno3ZmFuVDlpeHBrMlBzQlA1alBRZHkvcGp6dmxyeWhzeTRvanRVUU1pL25zQnUwMENhWkc3UWhBYVpZTWZKYVdMd3h3engwZVhKdHh0R1JHaDZQUENXeW9RUEIyelpxU3pkbnlKT3VqZ2c4WHltdU1tYXgvOU5BaFk1ZHp4QkVHUGlDMnBDYnUvMjFyUlpvcHFwVWFnS2dyblNab09EZVhxbmZNRUZ4ZDN0TzlHMkNXRWJHeFpSY0JvdGxGc3N2MjJBdWk5dklNbzhzOHgrdFlnWUozM2hLcnlPRm1iTnhjZW9mZUZueElFeGg1dkhGbFNTcVVhTDVNL3pXRVAyeVhmSVo5eWRIbFgvemFWZnNscyt3dGpxK01QUG9HdUU1YnRlSmdkcCt5NGJOeE9CeGM3ZDBIS2V1c0hwZTRhVjAxcUNBZjAxM3JpNmQyVE5nTUVHY2J3dm5wdFdTcW1IbWpEZUN2TTBEYWFac21RZXQ3VjVlS2xxU3pXTVdYVmtDTzlOeEkrR2FpZjYvamJranQ5aE0ySHBWVWRSdndTODhYdG9CZ1diZnRwZWlXOEwzOGtzN2U0bWsxa0lGQnYvSWxoRjJZU3RWVnRneTM0RUFUQ2dWZU9FOTVDNC9jeDRKd3d2Y0t0Y09Kd2dCQ3NLcHZWbUJ2TGRreTIiLCJtYWMiOiJmZjdlMDhlNTU1MDU3YWI3ZTU5NmE0MWExYzkzMTVmZWI3ZDkzODU0YmVlMTk1ZTRlOGEwMWM0ZTRmYzJmNGJlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InQ4ZW05QVovQzQ5SDlaamdLcEpZdlE9PSIsInZhbHVlIjoiQUYrYUEzZlJYZTNtZ093UGNHYnhpNnd5MFExOWhEUGhiNjI2emFvNHVsZGFHNWtzTGpIeGVqYWpqQkdZeTFoZDdmVXdLWmVFb01ibStLK2lGaXMvbWJWTk1qNFAyMkpFQTd3ZXQvbG9KM3V0NkJ2L2R1WkZsTHJzUHMwNTZnNFphTlVPRVZud2lDdmRRdU1WaFFJMTBDL2l6TWxTNlQvMEVObFRqM2RXOEFaWUJlRzVzVlpNTXdtbG9ZMHFKQXVHdUt4V1dyM3p6eXJoRzU1RFBscE4yajZydWhQMVRySG94dnhPdHRQdjBmL3hnUWFqRzNZKy9HVTdtMEUyVU1jSU5XWU9ibk1SWUN5eDZET1pPS2RXbWNxZlNRbWI0bWczbEpIbDBIUHd6YWZEc0dJN0UrSTUyUnN0OCt3VUozOHUvcGFjTUVVVkovUkNwL29QQW1saVJidHFvSVZzeGRDOXpJWFloNHl6TWt0aERjUk15aWl0Ylc5SG9aMVhZdEFCNGxiK1hDYzVIQUc1VGcrbm5Vc0RwblJpd0ExTG00alFkVGcwNEM5ZGMrWERha3Z6bVFEUzVIUTc4RWhwSnpaNm43M1BTUVZWeStTZFg0MU9LeWJLQVJZZnhGUzR3a0FqNEtaNkVRZ053UmMrYVhMZ2hQTnVJa1lCSEZhQkRrbEUiLCJtYWMiOiJmNjczZWNkYzM5YWJhZDFjNWNlNWNlZGFhNGY0ZjhiYmYxOWVmNjdlNWVmZGM4YmU3NTAxNWI4MmY1NGI0MzVlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNGZWc2Y0ZaRDFrWGQ5WHhVdVZCVFE9PSIsInZhbHVlIjoiQmRBZmdDRno3ZmFuVDlpeHBrMlBzQlA1alBRZHkvcGp6dmxyeWhzeTRvanRVUU1pL25zQnUwMENhWkc3UWhBYVpZTWZKYVdMd3h3engwZVhKdHh0R1JHaDZQUENXeW9RUEIyelpxU3pkbnlKT3VqZ2c4WHltdU1tYXgvOU5BaFk1ZHp4QkVHUGlDMnBDYnUvMjFyUlpvcHFwVWFnS2dyblNab09EZVhxbmZNRUZ4ZDN0TzlHMkNXRWJHeFpSY0JvdGxGc3N2MjJBdWk5dklNbzhzOHgrdFlnWUozM2hLcnlPRm1iTnhjZW9mZUZueElFeGg1dkhGbFNTcVVhTDVNL3pXRVAyeVhmSVo5eWRIbFgvemFWZnNscyt3dGpxK01QUG9HdUU1YnRlSmdkcCt5NGJOeE9CeGM3ZDBIS2V1c0hwZTRhVjAxcUNBZjAxM3JpNmQyVE5nTUVHY2J3dm5wdFdTcW1IbWpEZUN2TTBEYWFac21RZXQ3VjVlS2xxU3pXTVdYVmtDTzlOeEkrR2FpZjYvamJranQ5aE0ySHBWVWRSdndTODhYdG9CZ1diZnRwZWlXOEwzOGtzN2U0bWsxa0lGQnYvSWxoRjJZU3RWVnRneTM0RUFUQ2dWZU9FOTVDNC9jeDRKd3d2Y0t0Y09Kd2dCQ3NLcHZWbUJ2TGRreTIiLCJtYWMiOiJmZjdlMDhlNTU1MDU3YWI3ZTU5NmE0MWExYzkzMTVmZWI3ZDkzODU0YmVlMTk1ZTRlOGEwMWM0ZTRmYzJmNGJlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54901740\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1354798805 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354798805\", {\"maxDepth\":0})</script>\n"}}