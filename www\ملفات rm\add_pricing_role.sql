-- إضافة دور التسعير والصلاحيات المرتبطة به
-- تشغيل هذا الملف في قاعدة البيانات مباشرة

-- 1. إضافة الصلاحيات الجديدة
INSERT IGNORE INTO permissions (name, guard_name, created_at, updated_at) VALUES
('manage pricing', 'web', NOW(), NOW()),
('show pricing', 'web', NOW(), NOW()),
('edit pricing', 'web', NOW(), NOW()),
('manage inventory', 'web', NOW(), NOW());

-- 2. البحث عن معرف الشركة الأولى
SET @company_id = (SELECT id FROM users WHERE type = 'company' LIMIT 1);

-- 3. إنشاء دور "Pricing"
INSERT IGNORE INTO roles (name, guard_name, created_by, created_at, updated_at) 
VALUES ('Pricing', 'web', @company_id, NOW(), NOW());

-- 4. الحصول على معرف دور Pricing
SET @pricing_role_id = (SELECT id FROM roles WHERE name = 'Pricing' LIMIT 1);

-- 5. الحصول على معرفات الصلاحيات
SET @manage_pricing_id = (SELECT id FROM permissions WHERE name = 'manage pricing' LIMIT 1);
SET @show_pricing_id = (SELECT id FROM permissions WHERE name = 'show pricing' LIMIT 1);
SET @edit_pricing_id = (SELECT id FROM permissions WHERE name = 'edit pricing' LIMIT 1);
SET @manage_inventory_id = (SELECT id FROM permissions WHERE name = 'manage inventory' LIMIT 1);
SET @manage_product_id = (SELECT id FROM permissions WHERE name = 'manage product & service' LIMIT 1);
SET @edit_product_id = (SELECT id FROM permissions WHERE name = 'edit product & service' LIMIT 1);
SET @show_product_id = (SELECT id FROM permissions WHERE name = 'show product & service' LIMIT 1);

-- 6. ربط الصلاحيات بدور Pricing
INSERT IGNORE INTO role_has_permissions (permission_id, role_id) VALUES
(@manage_pricing_id, @pricing_role_id),
(@show_pricing_id, @pricing_role_id),
(@edit_pricing_id, @pricing_role_id),
(@manage_inventory_id, @pricing_role_id),
(@manage_product_id, @pricing_role_id),
(@edit_product_id, @pricing_role_id),
(@show_product_id, @pricing_role_id);

-- 7. إنشاء مستخدم تجريبي (اختياري)
INSERT IGNORE INTO users (
    name, 
    email, 
    password, 
    type, 
    default_pipeline, 
    lang, 
    avatar, 
    created_by, 
    email_verified_at, 
    created_at, 
    updated_at
) VALUES (
    'Pricing Manager',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 1234
    'Pricing',
    1,
    'en',
    '',
    @company_id,
    NOW(),
    NOW(),
    NOW()
);

-- 8. الحصول على معرف المستخدم الجديد
SET @pricing_user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- 9. تعيين دور Pricing للمستخدم
INSERT IGNORE INTO model_has_roles (role_id, model_type, model_id) VALUES
(@pricing_role_id, 'App\\Models\\User', @pricing_user_id);

-- 10. التحقق من النتائج
SELECT 'تم إنشاء الصلاحيات بنجاح' as status;
SELECT name FROM permissions WHERE name LIKE '%pricing%';

SELECT 'تم إنشاء دور Pricing بنجاح' as status;
SELECT name, created_by FROM roles WHERE name = 'Pricing';

SELECT 'تم إنشاء المستخدم التجريبي بنجاح' as status;
SELECT name, email, type FROM users WHERE email = '<EMAIL>';

SELECT 'صلاحيات دور Pricing' as status;
SELECT p.name as permission_name 
FROM permissions p 
JOIN role_has_permissions rhp ON p.id = rhp.permission_id 
JOIN roles r ON rhp.role_id = r.id 
WHERE r.name = 'Pricing';

-- ملاحظات:
-- 1. كلمة مرور المستخدم التجريبي: 1234
-- 2. البريد الإلكتروني: <EMAIL>
-- 3. يمكن حذف المستخدم التجريبي بعد الاختبار
-- 4. تأكد من وجود شركة في جدول users قبل تشغيل هذا الملف
