{"__meta": {"id": "X432bfe9e7d51c588b62691af39bfa161", "datetime": "2025-06-06 20:38:16", "utime": 1749242296.007892, "method": "GET", "uri": "/warehouse/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242294.601281, "end": 1749242296.007941, "duration": 1.4066600799560547, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749242294.601281, "relative_start": 0, "end": **********.76183, "relative_end": **********.76183, "duration": 1.1605491638183594, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.761855, "relative_start": 1.160573959350586, "end": 1749242296.007945, "relative_end": 4.0531158447265625e-06, "duration": 0.24609017372131348, "duration_str": "246ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46311072, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x warehouse.create", "param_count": null, "params": [], "start": **********.963718, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/warehouse/create.blade.phpwarehouse.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fwarehouse%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "warehouse.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.994884, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET warehouse/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "warehouse.create", "controller": "App\\Http\\Controllers\\WarehouseController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FWarehouseController.php&line=36\" onclick=\"\">app/Http/Controllers/WarehouseController.php:36-39</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.019049999999999997, "accumulated_duration_str": "19.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.878526, "duration": 0.0141, "duration_str": "14.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.016}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9222772, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.016, "width_percent": 6.982}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.930844, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 80.997, "width_percent": 6.037}, {"sql": "select * from `users` where `users`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 5059}, {"index": 21, "namespace": "view", "name": "warehouse.create", "file": "C:\\laragon\\www\\to\\resources\\views/warehouse/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.967119, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5059", "source": "app/Models/Utility.php:5059", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=5059", "ajax": false, "filename": "Utility.php", "line": "5059"}, "connection": "ty", "start_percent": 87.034, "width_percent": 6.719}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 5060}, {"index": 21, "namespace": "view", "name": "warehouse.create", "file": "C:\\laragon\\www\\to\\resources\\views/warehouse/create.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.976744, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5060", "source": "app/Models/Utility.php:5060", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=5060", "ajax": false, "filename": "Utility.php", "line": "5060"}, "connection": "ty", "start_percent": 93.753, "width_percent": 6.247}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/support\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/warehouse/create", "status_code": "<pre class=sf-dump id=sf-dump-1213359177 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1213359177\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1237014279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1237014279\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1334652001 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1334652001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/warehouse</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242266823%7C13%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldhZk1zNkR5VXoyc1pXVDV4Qmt4TEE9PSIsInZhbHVlIjoiNkg1cXg0bE5zcTU2U2p3cmQzdC8vRVZYZkt3NXRiVUl1K2F3MkRtdDJUaVcvSlhwcXV6dWp3TWxPaDYrek9pL0dPaGRSN0JGZWpHckJtL2RhL2ZCdzRXbGFQRkN0VDRjUkZZalhoYjQ4WlQ3eGlsQkxqUkQ2Q1F1aDVTZE9odUVxV1Q4VkFxVzZheWtGVEtvVzFIL09vQnRnYkl4bW9sWkpucnNJanUwS0JXUk04bHdrZDdha09QNWwwUDh6U3B4Y2ZkVVBTYXdZUjdpaElZdU9hVmk5bWNvdGx5d3VPbXlrZlFsaVJqYzFIcXg4RE11T3NXa29LZEFhSjkyZm5rak1ydnJ0eUNRVi8rY2lEMExOaTRKNVBnMVFSaEl4SFRsTzdhNEdQU1RwQ0E1RzRoUTdzWlVyd0Q0d2ROL0lPbFBBcmsxVkhwUjMvMGRkWFA5QmlDV042S1gyQ3hoVmJzZnZiRmJwOWpEQ2NtcWpiMEhLZlVaSGRBZFl1d3QvMktMdFZ6NG9oUjl3cEJGcU1jd200SGVuWHh3T1NYSkxQQVBBaUhxY0hRRjhBK25RR09vRjk4bnpnajhNR05XbjY4SEtzTmdOMTIzMFdZbXcycDBJdnR6R0pDVCsxKzVKK0VHa3VHcHFlaVJKMkFvakhnV1J4R1RzMGZSbkp0azJ1Z3ciLCJtYWMiOiIzOWY2NmQ5NzhlM2VkMmMzZWNjM2YwMjIyNDdmNjgyYWExNzhmY2U3Y2IwYjdmYzBhOTg5NGRlMWNiMDU0MWQ4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktsZnpab1NZTitJbUQ2c3cyeXZ3ZlE9PSIsInZhbHVlIjoiYm9uUkQ3V3FqUGZkcUgrT1pkajNvcmxTQmhpbmhzZkVnZ25ZbGljUUR4VUxZdXJqeTlhTkI1MVJxZXhwa2hlQlJHeGVrTXNDRmZkejJzVDRNMFFiQ1NGSE8xc1kwN2dPTEsvS0ttbnNwc21uRGNXU2M5SlcwMVJiQ0dNSmFoSkwxMi9SVDN4MHRINUlrNnpxaDhka2RNSWtJV1U3bjc2M0ZlNjJ3Tyt5M3ZOdWRZY3Z5ajlvbVFqS0JzYVF1SHdMZ09paldmK2JTdlhDSFYxM0JoUGp5ZnlZb05lV0h3eWREMDJWaC83dUl5RXpXWHNjNDllbWpZZGpJcE55UjZrcVI2OXVocmVZUlEzaVpJLzdRd25UcW9rdE5qczRQMXNDaWVaY3UrRktZdHc2RksrRVNUU3RPSHV5a2tMVUdWVSt6bmw4amV0V2hXcmdydVprQUtzMTVoVndxZ1dWeDI4WVpiQmtlUXZrVGV2M1c2T1JxejU1di96VDNxTmRscE5OekRubzBRRUtrTzM4RXllM1Jxbm55akRWZXhLUDJycHRHU2F0QjJDL3QyUWhLV0JaUVdFQ3NFRlFyNEtQdG5GaXZZRkRoRlBNWEdUTVk2RE41Y3lSSTdaLzd1YzRaWm9PNUxKUDlJWFlwd3BNa1pQYjZZNE5FSExqOGI0eEE2SnUiLCJtYWMiOiIzMjFlYjFlMzcyYmIzYzdiYzVjOWFiODgyZTdkOWE0MWE2ZmE1ZDFjZTZiYzgzNTM5NGY4ZTY2Zjg4NGZiODFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:38:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpJRzVLOUlNVzZhSGxOL3kvNkQ3Y2c9PSIsInZhbHVlIjoiR0lsVkV5c01Ld0RXTkt4VkV4ZUdmZERDSmRNelY5a1gwS0huVDdWUEdOc1NEY3ZaZ1hwTGUzYS9PTndJeDM1Q1U3ZVR4emZMc21jSXdtZ0N1QU45eVYyZ2YwZFlsMjNkTTI1R3pKUG1RWEV3RTdBZjlybDRPMHJmNitrR2hmNTU0RUdFOCtiSGVUc0lTdXRtT3NtUFdybWc0Y05nVUJjOUhhZEtvbDFNVGtyenhxZGtkdUhnSXJzSjJwOHFrZGR2L1FrYlBVUGpMWTdZcDRxTFdjeC81RzJUTmdDVG9mWEJrdnNlNDNPc0lXcnNHVzVEdXc4S3lWcDFDb2o4WUdEUmtPRGVGSHlnQ20wV1U0RUpPYUFZOTVHK1JOSzh4NSttZytreXRoY0NVU0pzM1o4SlNYdmJGZ1U0dnl4UCs3ZktOWXBScTZXaUhVQnBPVVMvMEZ1RElWR213VmFRaXRqZXkzWE1oOUsyTUU1U0g5dEFWbnhMZ3ZzVkVIa1dkK3M2NEtWY0pEZVBEaWVyTFYyeG9WTkRlbEFPbi93OVVYb2xQVU5YTVdDK1BEWktBcDFPTnhZWW44aTVrNlBDNXlXT0NCMlI4TGJuTnQ2UFgzOHlLcUdsc2NCdlpqWTRhOTdvcVJMUEhRUzVWNEVFVTBtY2YvdjljT3R3bDV5alo1M3AiLCJtYWMiOiJkMzcxOGZhN2VlYWQzNzE5NjRlOGE1YjMxNGEzNzY2NTdjMjJjYTc3MDZkZThkYmZkYmU4MmQwZDZmMTNmNGEwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:38:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InBsUXdLSUUrMU1FSWpYdVpyNC9OcFE9PSIsInZhbHVlIjoiNEwra3cwSkZvdlNOVkZIK2M2RlZKZlZxRE1WMU9POXlxN0tXbWlHL2lYdmY3Yk1vTXFKeGp3SFRXSW1TTU00R25HZTYrOHN3YmFZTGY1RnkwNytHbU5qN1VYQkhZdzA3QlpvNXdJYW1YZm12amc2QWV2VUd1cXZzWmptUEQxSzBBZStzY0R2RkM1YlJ3U2pybTJEQ0ZyUnVyU01keXZJSHNsTlpHaUpNb0hMdi9TU1dnaGNaTFF3SmxUY1dMK0F5WHlYc0JLVFNydUk0VW5tZjVjdjVDVnpkdnFtY2NKK21POFZEQ0kyMlZYR04zT3hCY09lVzJGd3VVSnpKT2hrY2U2dzZvdzJnbFREZmZ6Wk5JUWhNV291K3QzdnYxWG1idE9TdjMwUVZ5RTlQMjFURDlIcmkrQWNHVzR2bkxacUZSU1ZJMFppSkhKbDRHOWd4NkRRdkNYWlpBUjhpbFB0N3FzaUdIZW1IVnlJY1FyNm9xKzBUNlJpaDBKdmp0MTFhZkNtaFoyNVRpOUNmMTluM29EVTNHSWRDdjRML096RmNVcFdmb08rTDJwVlpUaUQyNi9RcnllVnEweHVtRHFlS25rYVYwaDh1ckpGK1V0aktzdFlQc1BTOEdENVZvZWZlSmdncG85MjMyMHJtWjBFcGtzeE41UVNVRTBZOWRoMzYiLCJtYWMiOiI2YWQ5YTdjOGFjZjhlZjhiYWNmMWUyNTFhZjViMDZkNzUyN2NkZDI1OTEwYzJiNWY2MTY4MzIwZDNiNDAyMDQ0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:38:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpJRzVLOUlNVzZhSGxOL3kvNkQ3Y2c9PSIsInZhbHVlIjoiR0lsVkV5c01Ld0RXTkt4VkV4ZUdmZERDSmRNelY5a1gwS0huVDdWUEdOc1NEY3ZaZ1hwTGUzYS9PTndJeDM1Q1U3ZVR4emZMc21jSXdtZ0N1QU45eVYyZ2YwZFlsMjNkTTI1R3pKUG1RWEV3RTdBZjlybDRPMHJmNitrR2hmNTU0RUdFOCtiSGVUc0lTdXRtT3NtUFdybWc0Y05nVUJjOUhhZEtvbDFNVGtyenhxZGtkdUhnSXJzSjJwOHFrZGR2L1FrYlBVUGpMWTdZcDRxTFdjeC81RzJUTmdDVG9mWEJrdnNlNDNPc0lXcnNHVzVEdXc4S3lWcDFDb2o4WUdEUmtPRGVGSHlnQ20wV1U0RUpPYUFZOTVHK1JOSzh4NSttZytreXRoY0NVU0pzM1o4SlNYdmJGZ1U0dnl4UCs3ZktOWXBScTZXaUhVQnBPVVMvMEZ1RElWR213VmFRaXRqZXkzWE1oOUsyTUU1U0g5dEFWbnhMZ3ZzVkVIa1dkK3M2NEtWY0pEZVBEaWVyTFYyeG9WTkRlbEFPbi93OVVYb2xQVU5YTVdDK1BEWktBcDFPTnhZWW44aTVrNlBDNXlXT0NCMlI4TGJuTnQ2UFgzOHlLcUdsc2NCdlpqWTRhOTdvcVJMUEhRUzVWNEVFVTBtY2YvdjljT3R3bDV5alo1M3AiLCJtYWMiOiJkMzcxOGZhN2VlYWQzNzE5NjRlOGE1YjMxNGEzNzY2NTdjMjJjYTc3MDZkZThkYmZkYmU4MmQwZDZmMTNmNGEwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:38:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InBsUXdLSUUrMU1FSWpYdVpyNC9OcFE9PSIsInZhbHVlIjoiNEwra3cwSkZvdlNOVkZIK2M2RlZKZlZxRE1WMU9POXlxN0tXbWlHL2lYdmY3Yk1vTXFKeGp3SFRXSW1TTU00R25HZTYrOHN3YmFZTGY1RnkwNytHbU5qN1VYQkhZdzA3QlpvNXdJYW1YZm12amc2QWV2VUd1cXZzWmptUEQxSzBBZStzY0R2RkM1YlJ3U2pybTJEQ0ZyUnVyU01keXZJSHNsTlpHaUpNb0hMdi9TU1dnaGNaTFF3SmxUY1dMK0F5WHlYc0JLVFNydUk0VW5tZjVjdjVDVnpkdnFtY2NKK21POFZEQ0kyMlZYR04zT3hCY09lVzJGd3VVSnpKT2hrY2U2dzZvdzJnbFREZmZ6Wk5JUWhNV291K3QzdnYxWG1idE9TdjMwUVZ5RTlQMjFURDlIcmkrQWNHVzR2bkxacUZSU1ZJMFppSkhKbDRHOWd4NkRRdkNYWlpBUjhpbFB0N3FzaUdIZW1IVnlJY1FyNm9xKzBUNlJpaDBKdmp0MTFhZkNtaFoyNVRpOUNmMTluM29EVTNHSWRDdjRML096RmNVcFdmb08rTDJwVlpUaUQyNi9RcnllVnEweHVtRHFlS25rYVYwaDh1ckpGK1V0aktzdFlQc1BTOEdENVZvZWZlSmdncG85MjMyMHJtWjBFcGtzeE41UVNVRTBZOWRoMzYiLCJtYWMiOiI2YWQ5YTdjOGFjZjhlZjhiYWNmMWUyNTFhZjViMDZkNzUyN2NkZDI1OTEwYzJiNWY2MTY4MzIwZDNiNDAyMDQ0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:38:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/support</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}