{"__meta": {"id": "X423d5b84bf584fa1f2382f703fa3873a", "datetime": "2025-06-06 20:36:51", "utime": **********.856028, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.320852, "end": **********.856064, "duration": 1.****************, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": **********.320852, "relative_start": 0, "end": **********.613005, "relative_end": **********.613005, "duration": 1.****************, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.613029, "relative_start": 1.****************, "end": **********.856067, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02419, "accumulated_duration_str": "24.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.729313, "duration": 0.0209, "duration_str": "20.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.399}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.781521, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.399, "width_percent": 4.754}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.824041, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.153, "width_percent": 8.847}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242206397%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1SM3hXaEhURmt4MHoxa0FyREhOREE9PSIsInZhbHVlIjoid1VxOUNFL2VwSTdMam1aUFFka2FwSHFlRWV5aWJ5UjVkRUswOThsV2xDQ2pzdk82aXJxQUNpS2gweG5ZTWdUV0Ywd1doNU4xOGRlM3dVbTRjUkdmRmhNUFhqT3BSbVY5cmtwREJQc3ZHVzZZUm9ualVCV3VuWmNWSVZkMW5YcFVMUEkrRFYrbDR1eWFneWlxMzJQU3FUWElxY1JsajNBYkVjOHFscytpUGwwbjRjTGxvRm52a0I4dXJRbUQ0N3J4eEZUMDlDdlF1d3NTT0Fra0dHR1MvQkFENEN4OTRRMzFEY1EyTE5sZE90UnJJeGRMc2RsTWE4MTFyOEpIemdlTUZEajltanlhOXR6ZFowcTNxV3ZIS2szRDVtOFc2ZFV2Rkt6T25ieVJMaVgwV1VUOTd4cmxpVmk3dGxFTnFsSzdld2hXeTM3NkZpR09Kb0s5VmJSMFJhTGJuWk5yZFF5WkxkdUxvbGpwOHY0dC9DMEovRkZLTXBRRHZvMW1qR0RnWVNQTUpDSDM2c1c4ZnIxTGJuSkM1aHAxZ1MvVHNiMFhuYWVwZzRlVHc1NC9hRkVqU1VQNjFyNGZINDllVkh4Z2hvNWZUUFNTMUZqWmdnRndWWDFWeW55Tis5eEhOcGp0SlFwZUZuYW04TGoxTU5MeUVIRFFFNHIwd2kxeERMSGUiLCJtYWMiOiIzZWFhMTY0MDcxNjk5MTI3ODNjMGY2ODVjZWU3YjlkN2U2MzE3MWFiOTM0OWM3NDJjMjMxNzQxOWM2OGFlZTgxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InkxbW1LMmltQVp4VkRFOGM3WFdpUWc9PSIsInZhbHVlIjoiZzVWay9pNmlJY2xidTFoVkVlWWNQYkRIb2J1Tm5yWER6ekttQVhOQnFtNzVpb2JhQU1XSk5RUzNtc1U1d3ZtM25iejRicDcrWTFDeG5uVDVjc2Q4TDJOSlFjV3NtQ29SaG0vcWFQYzBsVWNvRUpVdWxaanlqMXRra3F0RzdqdFFXdXU5ZEZUL0FMRFFoemVueEQyS0RFSGhWMHFXdW5hZE9LdU5aL0NuN1lCMDBQQ3k1THV2eDEvbCtSa3NNR3pKekpvcVUrTmlhZCtOR0lrbzBXUmFobDM0Wkx6ckY3TER6NHkrejRJRkhxd0t1ems5UmdaY0t5MUx3WWdYc2FUZHUwNlpuendhVjIwdzY0ejNZSytFSWk3NVRYM1VrclVNd1k3aE5Uem52aXN5ZStjNEVVdFBvcHpWLzQrWmZhN0JNOVIzckdJejY1RnowTndhZnhYOGRJRFY4MVJ0Kzl3ZkJVUVRlajY3M2xDcU9qSWdHMVN5bTVjK1hOMUh2YU9HeDlkSDZnL3UydkpLa3ZFUEpDcUFUTkdTUnU1ekxsMW0zYnQwNDRwaDJKSXRiK3NSbFg0MStyRkJQa08ybVJOMmhNUUVSbUIyQmxESUVQU2drQUM4QWV0YjRSMU1pOWtYVDJtUCtXU1ExUDVyNmpPbW5lYlVLWkM4R2NKL1VKZHQiLCJtYWMiOiIxMDU0ODNkMTVhNjMxNWYxZGU4YTFkM2MyMTE2NTNkM2VjMjhiNDAyYTdjYTliY2FhMzFkMTI5Y2I0YTU3NGRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1286950940 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aXiO7pRs3CsfapKKqE5850VIyuRIvpwAHRxwlR2S</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286950940\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-812063177 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:36:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNIUjJiZUllOXY0NWhYaFJWQ29zV0E9PSIsInZhbHVlIjoiVSswVVVuRnNBRHFhM01WdWFreEF1SEhoajdTdDZLZ1M2MTgzTkRkeEN6eHI0c3QxZHVWWVZ5emdtZlBiWXBjMklsUG9pUmIxWG9IRWpHV0U2aGFEeU1zU1AzTFNIZVBZdGxTVjNvYzR3OG9Vc292cE8yL2VmeVNNTkVoZjJHa2phdkZwVE9TQ01VQXhoK3A3MkpEYVV0c05BdCtlRFErOVdvTmxQT21LTHhGcW9hWHk1SmwrcjBEcTVkbnM2T1hjZnhKazhtMmxlVVBMM3VQTCsyTVc2L05TRjNSRm03Ujk4SEpRVFY0Y0RpTzZtQy9ObitmMHdHRllDb3hvSnBXZG55MGYvT2I2SFJya3lZRGllZ3FuaUU0VU51ZFd6NHJUY1VEY2M1TTFWeGxQdGRGTzZHL01DaWcxeWpvWHp0L0xBUEF4U2hkUXNEdUlPYXcrL2VFWTc5WFlqRUFoRFJMNDU3UEt4VGM1Y2xUTy9wL2dWYmd4enRocVpvK2tpQ2V0MlFqQkJveVEra2J6dmh1ZW1MSmtBSGE3U0t5N3MzYkxLTnlHeXVYR1FSZXVMbG8rNS9GQ2d3eUhLWHRxTVpEU01nTE9MWG1yNFpKelhjaTdJK2ZuYzVzdlhiTEErelN6MTNzUjArZEdDMERUSXF0QU02RDY5ekFiL1BjTC9CSDMiLCJtYWMiOiI3NTRjNGJlODczOTAxYjQxNjFkYzQyZmU0NmY4NGY2YmEwM2FjMTI4NDFkODc2NmYyOWMwOTg0MWQyNjAwZjA2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjkxSUF2aklwYVMzekd2YWhmZzA1Q1E9PSIsInZhbHVlIjoiTk5BWlZwMU5FdDZDWVl2eXFhOEdLeVh0a1BCd1puSC9Bb2RQcWJwS0U1RDdDZlVLTlNpSkZXLzBQS2hPSlRKazYrdkhGWVpkUXVwWjl3U2oxMkJxN1QzWlRySGlwU0xGc2Q4SUFMelFHQXVnRTZCYzdCQU5OS0RtRHdkbWczRTdzNHZFT2FGVTdOenVNMGMxdVd6MWRneW4zcWZQbTFBcFhVd2lkYlJaQytHenRsVGNaMnNwZHFpQWUrNnN0cmdjLzNXVHVjY281QWpQUURHcno1bmR6QU14TEk4YnJINXlMa2ZNVTA3bkNIa0haeUpoSWtEc24xeWpDWW1FdEtIZjV5N3hxZk1aVHB6M3ZWQTdjdVV2ejZnYVJCYXVxSDlheU5HTzNpUlhLcVBPWVFRdzhrNUE3UDdFUjdUR3FZaGZHb1pGWUI4M3hOL3pmdFdJeVNrNVluOWoxcC9xYmJMV0hHdUhmZjdyUjR5MzdmQ29oNEE0NmFKaUpWWEtqTVBKRGpFNHFaNjR4b3JPVFNXTHZEVkh3eFN6L2ZGM3ZTNVhnVHZuWTN1UG04Y1c1VkFDNVpXa09FejRyNUtlYnc1VGtZejV6MzYzU3ZGZW5lT3NvVkZSUXQ5c2d6RTFUWDQ3MzM1QnZXNlo0Y3NlV2t3SVV1N05mVG1ERHlpdlJySHQiLCJtYWMiOiI3ZGJhN2FlZGE0NWJkYmYyMTFiODAzM2UzZTliYjU2MGI2Njk2ZDE4ODFhYmYwMGVjNTJmNDNiZWY5NDlkMjI0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:36:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNIUjJiZUllOXY0NWhYaFJWQ29zV0E9PSIsInZhbHVlIjoiVSswVVVuRnNBRHFhM01WdWFreEF1SEhoajdTdDZLZ1M2MTgzTkRkeEN6eHI0c3QxZHVWWVZ5emdtZlBiWXBjMklsUG9pUmIxWG9IRWpHV0U2aGFEeU1zU1AzTFNIZVBZdGxTVjNvYzR3OG9Vc292cE8yL2VmeVNNTkVoZjJHa2phdkZwVE9TQ01VQXhoK3A3MkpEYVV0c05BdCtlRFErOVdvTmxQT21LTHhGcW9hWHk1SmwrcjBEcTVkbnM2T1hjZnhKazhtMmxlVVBMM3VQTCsyTVc2L05TRjNSRm03Ujk4SEpRVFY0Y0RpTzZtQy9ObitmMHdHRllDb3hvSnBXZG55MGYvT2I2SFJya3lZRGllZ3FuaUU0VU51ZFd6NHJUY1VEY2M1TTFWeGxQdGRGTzZHL01DaWcxeWpvWHp0L0xBUEF4U2hkUXNEdUlPYXcrL2VFWTc5WFlqRUFoRFJMNDU3UEt4VGM1Y2xUTy9wL2dWYmd4enRocVpvK2tpQ2V0MlFqQkJveVEra2J6dmh1ZW1MSmtBSGE3U0t5N3MzYkxLTnlHeXVYR1FSZXVMbG8rNS9GQ2d3eUhLWHRxTVpEU01nTE9MWG1yNFpKelhjaTdJK2ZuYzVzdlhiTEErelN6MTNzUjArZEdDMERUSXF0QU02RDY5ekFiL1BjTC9CSDMiLCJtYWMiOiI3NTRjNGJlODczOTAxYjQxNjFkYzQyZmU0NmY4NGY2YmEwM2FjMTI4NDFkODc2NmYyOWMwOTg0MWQyNjAwZjA2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjkxSUF2aklwYVMzekd2YWhmZzA1Q1E9PSIsInZhbHVlIjoiTk5BWlZwMU5FdDZDWVl2eXFhOEdLeVh0a1BCd1puSC9Bb2RQcWJwS0U1RDdDZlVLTlNpSkZXLzBQS2hPSlRKazYrdkhGWVpkUXVwWjl3U2oxMkJxN1QzWlRySGlwU0xGc2Q4SUFMelFHQXVnRTZCYzdCQU5OS0RtRHdkbWczRTdzNHZFT2FGVTdOenVNMGMxdVd6MWRneW4zcWZQbTFBcFhVd2lkYlJaQytHenRsVGNaMnNwZHFpQWUrNnN0cmdjLzNXVHVjY281QWpQUURHcno1bmR6QU14TEk4YnJINXlMa2ZNVTA3bkNIa0haeUpoSWtEc24xeWpDWW1FdEtIZjV5N3hxZk1aVHB6M3ZWQTdjdVV2ejZnYVJCYXVxSDlheU5HTzNpUlhLcVBPWVFRdzhrNUE3UDdFUjdUR3FZaGZHb1pGWUI4M3hOL3pmdFdJeVNrNVluOWoxcC9xYmJMV0hHdUhmZjdyUjR5MzdmQ29oNEE0NmFKaUpWWEtqTVBKRGpFNHFaNjR4b3JPVFNXTHZEVkh3eFN6L2ZGM3ZTNVhnVHZuWTN1UG04Y1c1VkFDNVpXa09FejRyNUtlYnc1VGtZejV6MzYzU3ZGZW5lT3NvVkZSUXQ5c2d6RTFUWDQ3MzM1QnZXNlo0Y3NlV2t3SVV1N05mVG1ERHlpdlJySHQiLCJtYWMiOiI3ZGJhN2FlZGE0NWJkYmYyMTFiODAzM2UzZTliYjU2MGI2Njk2ZDE4ODFhYmYwMGVjNTJmNDNiZWY5NDlkMjI0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:36:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-812063177\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-836258851 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836258851\", {\"maxDepth\":0})</script>\n"}}