# إصلاح مشكلة عدم ظهور النماذج

## 🎯 المشكلة
النماذج لا تظهر بعد الإنشاء لأن النظام لا يتحقق بشكل صحيح من الأدوار والصلاحيات.

## 🔧 الإصلاحات المطبقة

### 1. تحسين Form Model
**الملف:** `app/Models/Form.php`

#### أ. تحسين method getVisibleForms:
```php
public static function getVisibleForms()
{
    $user = auth()->user();
    
    if (!$user) {
        return collect();
    }

    // If user is company type, show all forms
    if ($user->type == 'company') {
        return self::orderBy('created_at', 'desc')->get();
    }

    // Get user roles
    $userRoles = $user->getRoleNames()->toArray();
    
    return self::where(function($query) use ($userRoles) {
        // Check for 'all' first
        $query->whereJsonContains('visible_to_roles', 'all');
        
        // Then check for specific roles
        foreach ($userRoles as $role) {
            $query->orWhereJsonContains('visible_to_roles', $role);
        }
    })->orderBy('created_at', 'desc')->get();
}
```

#### ب. إضافة method canUserView:
```php
public function canUserView($user = null)
{
    if (!$user) {
        $user = auth()->user();
    }
    
    if (!$user) {
        return false;
    }

    // Company users can see all forms
    if ($user->type == 'company') {
        return true;
    }

    // Check if form is visible to 'all'
    if (in_array('all', $this->visible_to_roles)) {
        return true;
    }

    // Check user roles
    $userRoles = $user->getRoleNames()->toArray();
    foreach ($userRoles as $role) {
        if (in_array($role, $this->visible_to_roles)) {
            return true;
        }
    }

    return false;
}
```

### 2. تحسين FormController
**الملف:** `app/Http/Controllers/FormController.php`

#### تبسيط method show:
```php
public function show(Form $form)
{
    // Check if user can view this form
    if (!$form->canUserView()) {
        return redirect()->back()->with('error', 'غير مصرح لك بعرض هذا النموذج');
    }

    return response()->file(storage_path('app/public/' . $form->file_path));
}
```

### 3. إضافة route للتشخيص
**الملف:** `routes/web.php`

```php
// Debug route (temporary)
Route::get('/debug-forms', function() {
    $user = auth()->user();
    $forms = \App\Models\Form::all();
    $visibleForms = \App\Models\Form::getVisibleForms();
    
    return response()->json([
        'user' => [
            'id' => $user->id,
            'name' => $user->name,
            'type' => $user->type,
            'roles' => $user->getRoleNames()->toArray()
        ],
        'all_forms' => $forms,
        'visible_forms' => $visibleForms,
        'forms_count' => [
            'total' => $forms->count(),
            'visible' => $visibleForms->count()
        ]
    ]);
})->middleware(['auth']);
```

## 🧪 كيفية التشخيص

### 1. استخدام صفحة التشخيص:
```
http://your-domain.com/debug_forms.php
```

### 2. استخدام route التشخيص:
```
http://your-domain.com/debug-forms
```

### 3. فحص قاعدة البيانات مباشرة:
```sql
-- فحص النماذج
SELECT * FROM forms;

-- فحص المستخدمين
SELECT * FROM users WHERE type = 'company';

-- فحص الأدوار
SELECT u.name, u.type, GROUP_CONCAT(r.name) as roles
FROM users u
LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id
LEFT JOIN roles r ON mhr.role_id = r.id
WHERE u.type IN ('company', 'accountant', 'Employee')
GROUP BY u.id;
```

## 🎯 خطوات الاختبار

### للمستخدمين من نوع Company:
1. سجل دخول كمستخدم من نوع 'company'
2. اذهب لإنشاء نموذج جديد
3. املأ البيانات واختر الأدوار
4. احفظ النموذج
5. تحقق من ظهوره في قائمة النماذج

### للمستخدمين الآخرين:
1. سجل دخول بمستخدم له دور محدد (مثل Cashier)
2. اذهب لقائمة النماذج
3. يجب أن ترى النماذج المخصصة لدورك أو المخصصة للجميع

## 🔍 نقاط التحقق

### 1. تحقق من نوع المستخدم:
- المستخدم من نوع 'company' يرى جميع النماذج
- المستخدمون الآخرون يرون النماذج حسب أدوارهم

### 2. تحقق من الأدوار:
- `Cashier` - كاشير
- `supervisor` - سوبر فايزر  
- `Delivery` - دليفري
- `accountant` - محاسب
- `all` - الجميع

### 3. تحقق من JSON في قاعدة البيانات:
```sql
SELECT name, visible_to_roles FROM forms;
```

يجب أن يكون الشكل:
```json
["Cashier", "accountant"]
```
أو
```json
["all"]
```

## ⚠️ نصائح لحل المشاكل

1. **تأكد من نوع المستخدم:** `type = 'company'` وليس دور
2. **تأكد من أسماء الأدوار:** استخدم الأسماء الصحيحة
3. **امسح الكاش:** `php artisan cache:clear`
4. **تحقق من logs:** `storage/logs/laravel.log`
5. **استخدم أدوات التشخيص** المرفقة

## 📁 الملفات المحدثة

```
app/Models/Form.php
app/Http/Controllers/FormController.php
routes/web.php
debug_forms.php (جديد)
```

## 🚀 للنشر على السيرفر

1. انقل الملفات المحدثة
2. شغل الأوامر:
```bash
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```
3. اختبر النظام باستخدام أدوات التشخيص
