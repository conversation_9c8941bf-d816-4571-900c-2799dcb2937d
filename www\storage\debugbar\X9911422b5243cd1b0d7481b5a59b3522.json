{"__meta": {"id": "X9911422b5243cd1b0d7481b5a59b3522", "datetime": "2025-06-06 19:38:14", "utime": **********.581584, "method": "GET", "uri": "/user-login/eyJpdiI6ImRpS0FHelltNDM4ZUJ6ZFE4L0RHWVE9PSIsInZhbHVlIjoiTmVUSUVnU21nUEczc1lUTWtWVUxnQT09IiwibWFjIjoiMDVkMjMzODg1NzFlYmE2NjNmZTllMzA2YjY5NjkyMjllMGRiMmY0MDQ1MWExMDFiYjRjMDQxNzI2NTU0ODU4YSIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238693.239822, "end": **********.581621, "duration": 1.341799020767212, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1749238693.239822, "relative_start": 0, "end": **********.414044, "relative_end": **********.414044, "duration": 1.1742219924926758, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414067, "relative_start": 1.1742451190948486, "end": **********.581625, "relative_end": 4.0531158447265625e-06, "duration": 0.167557954788208, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44001952, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET user-login/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UserController@LoginManage", "namespace": null, "prefix": "", "where": [], "as": "users.login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=698\" onclick=\"\">app/Http/Controllers/UserController.php:698-728</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028130000000000002, "accumulated_duration_str": "28.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `users`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 701}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.500476, "duration": 0.01533, "duration_str": "15.33ms", "memory": 0, "memory_str": null, "filename": "UserController.php:701", "source": "app/Http/Controllers/UserController.php:701", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=701", "ajax": false, "filename": "UserController.php", "line": "701"}, "connection": "ty", "start_percent": 0, "width_percent": 54.497}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 702}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.53267, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 54.497, "width_percent": 4.799}, {"sql": "update `users` set `is_enable_login` = 0, `users`.`updated_at` = '2025-06-06 19:38:14' where `id` = 16", "type": "query", "params": [], "bindings": ["0", "2025-06-06 19:38:14", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 706}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5446699, "duration": 0.01145, "duration_str": "11.45ms", "memory": 0, "memory_str": null, "filename": "UserController.php:706", "source": "app/Http/Controllers/UserController.php:706", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=706", "ajax": false, "filename": "UserController.php", "line": "706"}, "connection": "ty", "start_percent": 59.296, "width_percent": 40.704}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/user-login/eyJpdiI6ImRpS0FHelltNDM4ZUJ6ZFE4L0RHWVE9PSIsInZhbHVlIjoiTmVUSUVnU21nUEczc1lUTWtWVUxnQT09IiwibWFjIjoiMDVkMjMzODg1NzFlYmE2NjNmZTllMzA2YjY5NjkyMjllMGRiMmY0MDQ1MWExMDFiYjRjMDQxNzI2NTU0ODU4YSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User login disable successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user-login/eyJpdiI6ImRpS0FHelltNDM4ZUJ6ZFE4L0RHWVE9PSIsInZhbHVlIjoiTmVUSUVnU21nUEczc1lUTWtWVUxnQT09IiwibWFjIjoiMDVkMjMzODg1NzFlYmE2NjNmZTllMzA2YjY5NjkyMjllMGRiMmY0MDQ1MWExMDFiYjRjMDQxNzI2NTU0ODU4YSIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-951261993 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-951261993\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-481058743 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-481058743\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-888988838 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-888988838\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-798456211 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238678455%7C52%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNKR3FQZ2JIL2Zib2FNLzlkdVUyOXc9PSIsInZhbHVlIjoibUtxT0EzWlZmMGtjOUJ3VndWOVNTZEl3djRhYkhDdC8xdDRqeUxCZzdyOFFCSjJDbHBTZ2puZGpFcDA5UDl4aDdDTlcxOFJldW0zMFdVNFlFaDkrTHM2Qk1sVG5VaUFrNmFJQm90RmFGb0tTRkFBQlNOMXA4N2ExTEM1OVA4TnZ0ek13MGl6YUEzczVaSUV0VUxuTTV2MkltaVFRY01IbHlVSjRxbUMxMmVmQllYRFVNSUVUdDVZQ0dUOTN0clpnZS9aWWhLZ0JnaU1McUdkRGR2Q1dnYkh5MWwwKzdlZVcxT00zOVlZQUlBNnliOUZ3OHJ4aGh6THhNdEVNN1ZzRlhvTkpHWHoweW04ZjM0Q2t2eld2OVk1SVZPRWdCL1Y3QnpkSGUvUGJsclYyU2kvNjY0Ny9lTGRqVFZ4SUprVDFWZTJDN3c0aVNMNmViZEdBSFl2UTR5ZmpEY0s4V25CWDUyUXdJQnRncnRLRy95ZkxpKzZEdCtuK256TUtoLy9HbzE2ZWNtc1VTSmNERXpkdXRDZ3FzblZ5bXB1Z0xmV0xaQXhqRHM0OVNEVEk5VlNzak40Qm1ldWtEOXJOSTBrSzh3VmVqNncxb0E3UlhFT1paejArYTBpMjlvdVZwOVp0T2lFTmEyOVVqdlpKTG9KdjRGWWlXMDJWY1J5TnBDYXgiLCJtYWMiOiJmZjBhNWU4N2NkODAxZWE3MWIyOTFkM2Y2NjVmNDc0OWQ2NDBkYWVmYzU3Y2Y4MjA5NjhjZjcyMmVlNWI3MDQ4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InV0OEJQYnYySlFrODB3SldRelVHeVE9PSIsInZhbHVlIjoiQTluMGJqWTAvaVBjZUlGMmpMaHFLb2gzYnBUbkhOdCtEbWNQOVlFZnZ6OGxjUWdGUDBWbnp5UUdxREFjSXlmemFuU0JTVyt6ZDdDSnZ2VXZlTmlzM0tvbzhacUFoaENuZms2WGt5cG1pWGJtTTJTeHJlSnpkR0h6TmtGRUppU0wyTTVFUkRpM1MwVkdKNUx2d1hrNzZ4aVhGVU5WN21GUzdXVWZ4clZJb0x3aVZZQkx0WG44Y2luMy9CaHEzd3BJNXJKLzEwWUw3Y2UwMmM5amcxMGRZWkgzVDJ4bjVickwyYlZWb0k3NjNySjJOQmtMRDBXTWZRbjQ2NDVDekhIY1NCSUdtdXp0SVR0UEpoYkttQUVRR2xmSmxodVViSndBMzBVQ3hSYVBzRTltRVNpNnpEVVA2SmVQTWJhdWMvZ1AxZlFDQU5nbnZQUTBCUFhXM2kzcmNRQlZpRWFBWklsc2MwUm5uUkw2ZGJ1aEE5OUYxUlNoOEltaC9hSG9BMmdNVE9zVlZJbGd3ZDlZd2RFbXZjb1p3SnFtV1lORXY1UmE5TmdFQk9jSmNiK3NrUDZGU1U4WnF2TWk4dDFnMXF2aTZ5cFFzbzZDME1TT2FUdmxQRUlGc3dCZGVQOWlRYVp2RzZTeTFrdGY0NGVreE9sK2NDR283NkdodUhjd3FmbWIiLCJtYWMiOiIyYTY1ZDUyM2I2MjMzODA4NjU0YTUxNzFjMWU4OWU1M2EyM2MyNzg0MGM2NGY1YWJmY2I3NTVmODU4MDRhOTVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798456211\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-52908129 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52908129\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-513026612 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:38:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InIxcjhGZXI2eFpKU2xzaWQ1Z053Z1E9PSIsInZhbHVlIjoiWUJYQ1VJT2c0WFdxRkRDQzVpMldCY2hNbHhHL3FHWU04ZGwwT3EwQUU3bjJQbzdzR1pSTHlSTTJEL3ZSTndnNzNicGw3ZXUrbnRUU1BMV3dRbHlOOXhDMXI1bFNFYWs4QlR1ajVnR0RpbjBtbSsxa2xLaUpOM2s3WS9SQm9kYWlZYVhJWTFpbDRmcDJNQ3lQQ09GenhnREluUWN5OXl3UTdNNnYxd1QrbTBHbGJEbUM3TC80RXB4L3phdUlhdm8rMG9WblBRd1NWbC9rTGNub0JmSnBnVWc0Q0x1T2VkenBLSWVnYlEvYzJhd0ZxS2xnMmUrbXkwK3VBK0FtbUI4eDVvSXV1cGpNSEJpN0dscDN1K2dNbGNhajQ2VmgzaUNHWVNQVTFjM0E4dlFicWE2Q2d0U1REd09jbFdiRmpTcHcxUzd2WkpHNUozTytGSVkzODQzMXM1Uk92cWx1YkkrNHhSdzNxejZkeUJ5V0lidnA1Z283eGFqdDNzS0MrZzl4ZUE5QS9Wd09meGpVTGQvemZWdmlUR2w0SUFIY3BocTJlcDNNQ1VWZG43Mk5uUlI5dVNNQzVkb0R1M3ZIQ3JJYzVpR05VVnRVVHRQT1JTbit5V3R6d0MwaHJYVW02eUVZdnJhV21aeXcyWnhTRGFGemg0dzdwMW0vcVBkTkdUYmwiLCJtYWMiOiJiZjZiMjI4NDhjZjk5MTkxOTg0NTQxNDNmNTFjOTdhZmJlNDcyYjBlNzFkNzNlZTVkMTk3ZDlhNjY0YThhMWFhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldFcnZsMGNuTmlnOW9BanUzdUZXTWc9PSIsInZhbHVlIjoiZGtCTU5zbmdaeUpFa2Q1S3RMYndSMzJuR05GcHduV1JDQlBzVmNYK0NxbThqanJJL2hoTWpkU0xOV3hTb1MvR0UrN1hLem9yVzJianNsUjc5V1YwbGJyTDJUd2cyTDB5dG1ZcU9QSEttWW00ODJMVHNtNWIyR3diOEl0NlNJZnZtNm1jK0c3MjNJcldXMkgwUWEzdGd0VG9UVnpOVmgrbXBGZ0hOSFQ2WTYrckp5Q1VxZFpFZGUwNk56enFVQmtUOEdsUC9BQ0JDaVpHbU5SRGYrMmRwcnc3emNtR1JMM2pZcmY5a2ZJSDY2aTVZRXEzekpyVDRtTG1nbm1MNDg3Z3FXc1BESWNNbUM0aUE2ZFJrZEJ1N0ZReHdDMWRSbS9mR3JYdlZNa25BNW00UlIzWmVJVkcxaHhuVUhweFZDSGhiT0UrVWJjV0s3OTlXWHZUaDJaWlNlbUJuZkNqUHhyeU42dVhBcmY0blZNdEFlM25rWlJZMlE2ZitjakYyYkpVSWpDZEhjSC8yeEo1RGt2OU1SVnVOUi9FMmY2MUVrc2VDU1ZvRVhLU2VLcXM1cGhyZm4rcDcwYTExd1U0RmdHSWFCWWRMMjVjZEdvaVl1UjFOeWJrYkZCL2RzcHlBVkd6VDdEZnpSa3ZvUXM3VEFvU29JTjduZ1d0OVRubnJjR0giLCJtYWMiOiI1ZGZlYWU4ODQ5ZmU1NTlmNmViNzcyOWQ4MWY5ZmUxNjgyZDY2ZGVjM2E1ZGI1OTk0OWI3MjYyOWExMTVkYmE5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InIxcjhGZXI2eFpKU2xzaWQ1Z053Z1E9PSIsInZhbHVlIjoiWUJYQ1VJT2c0WFdxRkRDQzVpMldCY2hNbHhHL3FHWU04ZGwwT3EwQUU3bjJQbzdzR1pSTHlSTTJEL3ZSTndnNzNicGw3ZXUrbnRUU1BMV3dRbHlOOXhDMXI1bFNFYWs4QlR1ajVnR0RpbjBtbSsxa2xLaUpOM2s3WS9SQm9kYWlZYVhJWTFpbDRmcDJNQ3lQQ09GenhnREluUWN5OXl3UTdNNnYxd1QrbTBHbGJEbUM3TC80RXB4L3phdUlhdm8rMG9WblBRd1NWbC9rTGNub0JmSnBnVWc0Q0x1T2VkenBLSWVnYlEvYzJhd0ZxS2xnMmUrbXkwK3VBK0FtbUI4eDVvSXV1cGpNSEJpN0dscDN1K2dNbGNhajQ2VmgzaUNHWVNQVTFjM0E4dlFicWE2Q2d0U1REd09jbFdiRmpTcHcxUzd2WkpHNUozTytGSVkzODQzMXM1Uk92cWx1YkkrNHhSdzNxejZkeUJ5V0lidnA1Z283eGFqdDNzS0MrZzl4ZUE5QS9Wd09meGpVTGQvemZWdmlUR2w0SUFIY3BocTJlcDNNQ1VWZG43Mk5uUlI5dVNNQzVkb0R1M3ZIQ3JJYzVpR05VVnRVVHRQT1JTbit5V3R6d0MwaHJYVW02eUVZdnJhV21aeXcyWnhTRGFGemg0dzdwMW0vcVBkTkdUYmwiLCJtYWMiOiJiZjZiMjI4NDhjZjk5MTkxOTg0NTQxNDNmNTFjOTdhZmJlNDcyYjBlNzFkNzNlZTVkMTk3ZDlhNjY0YThhMWFhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldFcnZsMGNuTmlnOW9BanUzdUZXTWc9PSIsInZhbHVlIjoiZGtCTU5zbmdaeUpFa2Q1S3RMYndSMzJuR05GcHduV1JDQlBzVmNYK0NxbThqanJJL2hoTWpkU0xOV3hTb1MvR0UrN1hLem9yVzJianNsUjc5V1YwbGJyTDJUd2cyTDB5dG1ZcU9QSEttWW00ODJMVHNtNWIyR3diOEl0NlNJZnZtNm1jK0c3MjNJcldXMkgwUWEzdGd0VG9UVnpOVmgrbXBGZ0hOSFQ2WTYrckp5Q1VxZFpFZGUwNk56enFVQmtUOEdsUC9BQ0JDaVpHbU5SRGYrMmRwcnc3emNtR1JMM2pZcmY5a2ZJSDY2aTVZRXEzekpyVDRtTG1nbm1MNDg3Z3FXc1BESWNNbUM0aUE2ZFJrZEJ1N0ZReHdDMWRSbS9mR3JYdlZNa25BNW00UlIzWmVJVkcxaHhuVUhweFZDSGhiT0UrVWJjV0s3OTlXWHZUaDJaWlNlbUJuZkNqUHhyeU42dVhBcmY0blZNdEFlM25rWlJZMlE2ZitjakYyYkpVSWpDZEhjSC8yeEo1RGt2OU1SVnVOUi9FMmY2MUVrc2VDU1ZvRVhLU2VLcXM1cGhyZm4rcDcwYTExd1U0RmdHSWFCWWRMMjVjZEdvaVl1UjFOeWJrYkZCL2RzcHlBVkd6VDdEZnpSa3ZvUXM3VEFvU29JTjduZ1d0OVRubnJjR0giLCJtYWMiOiI1ZGZlYWU4ODQ5ZmU1NTlmNmViNzcyOWQ4MWY5ZmUxNjgyZDY2ZGVjM2E1ZGI1OTk0OWI3MjYyOWExMTVkYmE5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-513026612\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1878707413 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"228 characters\">http://localhost/user-login/eyJpdiI6ImRpS0FHelltNDM4ZUJ6ZFE4L0RHWVE9PSIsInZhbHVlIjoiTmVUSUVnU21nUEczc1lUTWtWVUxnQT09IiwibWFjIjoiMDVkMjMzODg1NzFlYmE2NjNmZTllMzA2YjY5NjkyMjllMGRiMmY0MDQ1MWExMDFiYjRjMDQxNzI2NTU0ODU4YSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"32 characters\">User login disable successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878707413\", {\"maxDepth\":0})</script>\n"}}