{"__meta": {"id": "X442610c444cdc5849bc83f5f9c6d291b", "datetime": "2025-06-07 04:32:55", "utime": **********.799968, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270774.386572, "end": **********.800001, "duration": 1.4134290218353271, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749270774.386572, "relative_start": 0, "end": **********.582148, "relative_end": **********.582148, "duration": 1.1955761909484863, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.582166, "relative_start": 1.195594072341919, "end": **********.800005, "relative_end": 4.0531158447265625e-06, "duration": 0.21783900260925293, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44812440, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02919, "accumulated_duration_str": "29.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.665385, "duration": 0.02526, "duration_str": "25.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.536}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.719224, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.536, "width_percent": 4.282}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7559848, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.819, "width_percent": 5.687}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.77677, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.506, "width_percent": 3.494}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-514588352 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-514588352\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1264796685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1264796685\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1021628393 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021628393\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1620403328 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270763756%7C11%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNpS3dJSVNBNDcxNXIwSk96dTJBaXc9PSIsInZhbHVlIjoiTFBuWTczUmtITXltcFhmd2d4aWlkZXUrdVBxTElmbjRRZFp0M3gvUnVoRW02SXVjRXBJbUR3OFExKzBMVXEyTzlPQmdQZ2xLTjA3cVM0VndweVJyUEZCTU94UlpyNUw0a1VqUkRKdXVReGFBUFhnS1E5a21SS0lhKzdZZEJKbTZjZ0dpT3YzVTRiT3Z3alNLcjRDSndiR2Qyc2RVeUlEaGd0ekpJRmFUd3lRM2swUk9QOEpBVndUM0NnUW52WUR5YUh1UlR1S2JQNHVKa3NlTG80amtTbGdDRjM5MFpzMnJKZURQcGU1OXYyVFFjRzhrZW9zMmN0bVgwZW14c0VCcnR3L1orWXhVQkVxTUMzV0szZG5Ld1d5SGxMY2RJUHk3aWlCeXhkVzlRMEluZU1lZkNyUjlLVFpVbnpGTkdzeDNLcTNiQmZUalpNekhQUUxteFlLeXZWTXdRaE9lT00rUzZpbkdsUkFVQ2dNMm5GWUhjaHBTWnZXOHNROEkzVUQvTXUwckM2M0dzVG5RR2kyenVwTGRZYXBicTdQRWVqUUNEUlh0RW12a3M5dlBvWWJ4OXBGN0ZhV2N2K0hoeGtCMUV5RHJiQmgvbkoyWEVXeDljY0doWFZUZ2FJOUkyZ2VMQUR6VllTYXAzVTVWVXllYjZITFVHS0pmRE1iOEIvMmciLCJtYWMiOiIyMzhlYWI3MWUwNjE1YjBlNWRiNjlkMDM0ZDdhZDQ2Y2ZjNmI4NjRlZjExYWQ0Y2IwMzAyMzMxNzM4ODI0OWUwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik82QTg1SlI4SUxUL1hQMGFrbFZ0NUE9PSIsInZhbHVlIjoidmxkc29qazdQRzJTQUtEZmdEUHdWV0VQdHpGa1FLVWxTdHlhNkswdk04ZFppYWswWkh5YUtCM21rYVNtR1FFaDFGS1BwRUZKN1JDMTVvWmZvOWpRcnVWWmF1NTVDcDZYYkFzVlpkMDVPa29UZ1VZN1FzY2tqb3BlTW1NNStGSHZHbCtJU1EzSElmNWtFM3VEWCtianJOd2hnRElxeFJPL0FEZ0FBN0dqVjQ3b0ZCejlwSXI3eGw0VWNNYkVVdk5LcXZXRmFzQmk5WUhFcjBVeUdCVTZVVkp4SWZLMHFtV21ySmZNV1YxVGFyVmpwb0d4UEtBbU8rL1pENVJmTFVZVkhmaG43S1haSTVmMjJoM0Z2YVlrMjdQK1phRjMzNFZqeE0vVlpsY1o0YnRBRHpndFhGTjB2ajFEZ2JIVlRRWTdoY2Z5Vk5kQ2N0d0FGTjZOcU4xdTMrSEFVZzRtcXJPRDJrQ0FOL0Z5VFZXa2pzMml0Rlh1SFhkSnYzVElFTDREWEcrNFVhMTVrQ1pTWVYrbDVRZFZMeHl2U3g0UWlSSXNqcWFXak9mRzhLcnZsMDlTYm5KS2x6aHR6TzU4L1NlQldqQkd3OVVMY0pUL3o0UGxCUWs3SmRnMG9QM0s3dWU0d01pQ3l2dkpyaDFSTXZoSnpnc2crbWhFR09SZGlRYlAiLCJtYWMiOiIxZjdiOGIyY2RhMGNlNzYzYTdmYTAzYjc1Y2UwNzA5YzUyNWIxMGFiYzc5MGU0NzQ0ZjQxOTMxM2FmZWEwNDc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620403328\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-681405233 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4BV3LLZZXyEMe10MCK9nf8wqbVP7f9AxZbbeIoE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681405233\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2062139078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:32:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlovMVBpMS8rYjZXTHZ2N08rellnZ2c9PSIsInZhbHVlIjoibE4vTElkVXhXbVlSc0NtK1BxWWNGb05zak1MRVFUNFl2MGZ1R3EzR2dHekQwM2lvNmxPdGQzSVpwWVEwQ0FDVmh2cXpucTNOWWdqY1QvcmE1ZTV2NWV1OHBoMUZxeHFPbjRtR0NZWUd4R3BaN2FKNVNPUHo0QU9UbzBrRm9lRnNzbXVCYXhPNnVpbDZWU1B0WVBZTlhaZWVNeFk1ZUJTQ05MUVArYXRhbERIQzhoZ2VnZmJGQlZ1dnRYWkd5LzBNVVZnb1g3aERqYnE5RVhIYnhyM1F0V21vRWxWSE56YlFCU29mR2dmMVdXVzMxVEV4L1p3VEt5bG1QNTR2NURpN0tzN3NlVTJ3cVhpN0o2bldZWVUwWUc4VjBPcXBKZExtQi9Hbi91d3RFdmlaTTNBUjJVL1dKVVJrVEtKWkFiditjbmlwMTROUEMyaVlyZER4c1I4UXpaalNScEF4bWZDYlhid1dwY3R1SEhjRm5vb0VqbGRFR294dXFyWnpmdTB4RlFwdmFXT01lcWM4dm1tZ29DZkpqRzJ1Qk1tM2JMR1pDcXZVQml2ZWlQbStqcVVyb1dMNEcxck1rUjYwc2FRREllQkVXeHZzbEJkMW5McFpCWEttdkRvNExrTCtPYzNCcDN3ak9aYkdpSHpBRFlUSm5VQWpadWZPK2ZXK0x2Z04iLCJtYWMiOiJmOWRiYjAzYzUyZDhkYjE4YzcyZGEyNTMwOWFhNzgyNTNlMmE1MGUwYjc1YTEwNzkwMGU0NWRhOWMzMjM3NmY2IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ild6eGxVMHlwYUcyTXIwRms0STl0OEE9PSIsInZhbHVlIjoiSVBjUDdVQ2d6eGw3aDBMWHc0Nkkwa1ZINUliRFhTQ1BQV2YvWmE1emsxOFJ1emFZWHh4QW91MGtSd282TmkwZ09sbW5ua0JBOWtTTUc1T29FMDk5Q2F1V0JPVWVEbXFnZ0hhU012cFpiVWpTb3AwQkRLRVFFOUUrd29ZVkZZYVJ3emxhT2tTQUxRTGtLVVRYRmFRMFR4bGhVWUZFMzMyMnFqQTVGa09wMCtQQjNXNWtpby9iRjdxNEN2cng2ZFJhMjRGQWZJaUV4ZjF3UjQ2L0NUYTJZNHRxRUpsUzZneER0M3NhOWw4ZFdPM3JTNmVhMW9uWG0zdUpwdGVUaThQRU9iKzYvK1RjUzZSVVpLY3pzb1RXQnFZNnBVZnNTNHlvVm04OVZxT2dzdkhCdEZFa2s1bnhDYTZnaDBGc3FnZ2I1bEUzTlN2S05LbEFtelNEMWkvYVRFcFR6eG9ObFRuZ2twUGlFUlQzaUE5amdFQWgyanFSdFg2UUVuWUNLVkpHN2xnWG15VjBwdDFXUmxRTmJBVmpxZ0dyL3VGQlpVT3dEM0lSY0txMzNoUGdDV25HYVpkaUpBdTNrQmdXajVBbXEvNFU3V1FsSTVEbVZKeExENVJQYlFkYVhqckZXeitJbmdub3dSOVVPUDBDeUY4K2xMV1EyRHNZbEdnbnI1WmIiLCJtYWMiOiI2Y2JiNzA0ZDYwNzg4ZTVmNDhkYWIzODEzOTUyZmU2NjY4MDAzYzBhZmQwOWNkZmM2YzI4NDE3Mzc0OGExNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlovMVBpMS8rYjZXTHZ2N08rellnZ2c9PSIsInZhbHVlIjoibE4vTElkVXhXbVlSc0NtK1BxWWNGb05zak1MRVFUNFl2MGZ1R3EzR2dHekQwM2lvNmxPdGQzSVpwWVEwQ0FDVmh2cXpucTNOWWdqY1QvcmE1ZTV2NWV1OHBoMUZxeHFPbjRtR0NZWUd4R3BaN2FKNVNPUHo0QU9UbzBrRm9lRnNzbXVCYXhPNnVpbDZWU1B0WVBZTlhaZWVNeFk1ZUJTQ05MUVArYXRhbERIQzhoZ2VnZmJGQlZ1dnRYWkd5LzBNVVZnb1g3aERqYnE5RVhIYnhyM1F0V21vRWxWSE56YlFCU29mR2dmMVdXVzMxVEV4L1p3VEt5bG1QNTR2NURpN0tzN3NlVTJ3cVhpN0o2bldZWVUwWUc4VjBPcXBKZExtQi9Hbi91d3RFdmlaTTNBUjJVL1dKVVJrVEtKWkFiditjbmlwMTROUEMyaVlyZER4c1I4UXpaalNScEF4bWZDYlhid1dwY3R1SEhjRm5vb0VqbGRFR294dXFyWnpmdTB4RlFwdmFXT01lcWM4dm1tZ29DZkpqRzJ1Qk1tM2JMR1pDcXZVQml2ZWlQbStqcVVyb1dMNEcxck1rUjYwc2FRREllQkVXeHZzbEJkMW5McFpCWEttdkRvNExrTCtPYzNCcDN3ak9aYkdpSHpBRFlUSm5VQWpadWZPK2ZXK0x2Z04iLCJtYWMiOiJmOWRiYjAzYzUyZDhkYjE4YzcyZGEyNTMwOWFhNzgyNTNlMmE1MGUwYjc1YTEwNzkwMGU0NWRhOWMzMjM3NmY2IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ild6eGxVMHlwYUcyTXIwRms0STl0OEE9PSIsInZhbHVlIjoiSVBjUDdVQ2d6eGw3aDBMWHc0Nkkwa1ZINUliRFhTQ1BQV2YvWmE1emsxOFJ1emFZWHh4QW91MGtSd282TmkwZ09sbW5ua0JBOWtTTUc1T29FMDk5Q2F1V0JPVWVEbXFnZ0hhU012cFpiVWpTb3AwQkRLRVFFOUUrd29ZVkZZYVJ3emxhT2tTQUxRTGtLVVRYRmFRMFR4bGhVWUZFMzMyMnFqQTVGa09wMCtQQjNXNWtpby9iRjdxNEN2cng2ZFJhMjRGQWZJaUV4ZjF3UjQ2L0NUYTJZNHRxRUpsUzZneER0M3NhOWw4ZFdPM3JTNmVhMW9uWG0zdUpwdGVUaThQRU9iKzYvK1RjUzZSVVpLY3pzb1RXQnFZNnBVZnNTNHlvVm04OVZxT2dzdkhCdEZFa2s1bnhDYTZnaDBGc3FnZ2I1bEUzTlN2S05LbEFtelNEMWkvYVRFcFR6eG9ObFRuZ2twUGlFUlQzaUE5amdFQWgyanFSdFg2UUVuWUNLVkpHN2xnWG15VjBwdDFXUmxRTmJBVmpxZ0dyL3VGQlpVT3dEM0lSY0txMzNoUGdDV25HYVpkaUpBdTNrQmdXajVBbXEvNFU3V1FsSTVEbVZKeExENVJQYlFkYVhqckZXeitJbmdub3dSOVVPUDBDeUY4K2xMV1EyRHNZbEdnbnI1WmIiLCJtYWMiOiI2Y2JiNzA0ZDYwNzg4ZTVmNDhkYWIzODEzOTUyZmU2NjY4MDAzYzBhZmQwOWNkZmM2YzI4NDE3Mzc0OGExNDk0IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062139078\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2058040676 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058040676\", {\"maxDepth\":0})</script>\n"}}