<!DOCTYPE html>
<?php
    use App\Models\Utility;

    $setting = Utility::settings();
    $company_logo = $setting['company_logo_dark'] ?? '';
    $company_logos = $setting['company_logo_light'] ?? '';
    $company_favicon = $setting['company_favicon'] ?? '';

    $logo = \App\Models\Utility::get_file('uploads/logo/');

    $color = !empty($setting['color']) ? $setting['color'] : 'theme-3';

    if(isset($setting['color_flag']) && $setting['color_flag'] == 'true')
    {
        $themeColor = 'custom-color';
    }
    else {
        $themeColor = $color;
    }

    $company_logo = \App\Models\Utility::GetLogo();
    $SITE_RTL = isset($setting['SITE_RTL']) ? $setting['SITE_RTL'] : 'off';

    $lang = \App::getLocale('lang');
    if ($lang == 'ar' || $lang == 'he') {
        $SITE_RTL = 'on';
    }
    elseif($SITE_RTL == 'on')
    {
        $SITE_RTL = 'on';
    }
    else {
        $SITE_RTL = 'off';
    }

    $metatitle = isset($setting['meta_title']) ? $setting['meta_title'] : '';
    $metsdesc = isset($setting['meta_desc']) ? $setting['meta_desc'] : '';
    $meta_image = \App\Models\Utility::get_file('uploads/meta/');
    $meta_logo = isset($setting['meta_image']) ? $setting['meta_image'] : '';
    $get_cookie = isset($setting['enable_cookie']) ? $setting['enable_cookie'] : '';

    // Login Page Customization Settings
    $login_customization_enabled = isset($setting['enable_login_customization']) && $setting['enable_login_customization'] == 'on';
    $login_primary_color = isset($setting['login_primary_color']) ? $setting['login_primary_color'] : '#007bff';
    $login_background_color = isset($setting['login_background_color']) ? $setting['login_background_color'] : '#ffffff';
    $login_bg_animation = isset($setting['login_bg_animation']) ? $setting['login_bg_animation'] : 'off';

    // Custom login assets
    $login_custom_logo = '';
    $login_favicon = '';
    $login_bg_images = [];

    if ($login_customization_enabled) {
        // Use helper class for better image management
        $login_custom_logo = \App\Helpers\LoginCustomizationHelper::getCustomLogo();
        $login_favicon = \App\Helpers\LoginCustomizationHelper::getCustomFavicon();
        $login_bg_images = \App\Helpers\LoginCustomizationHelper::getBackgroundImages();
    }

?>


<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>"
    dir="<?php echo e($SITE_RTL == 'on' ? 'rtl' : ''); ?>">

<head>
    <title>
        <?php echo e(Utility::getValByName('title_text') ? Utility::getValByName('title_text') : config('app.name', 'ERPGO')); ?>

        - <?php echo $__env->yieldContent('page-title'); ?></title>

    <meta name="title" content="<?php echo e($metatitle); ?>">
    <meta name="description" content="<?php echo e($metsdesc); ?>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo e(env('APP_URL')); ?>">
    <meta property="og:title" content="<?php echo e($metatitle); ?>">
    <meta property="og:description" content="<?php echo e($metsdesc); ?>">
    <meta property="og:image" content="<?php echo e($meta_image . $meta_logo); ?>">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo e(env('APP_URL')); ?>">
    <meta property="twitter:title" content="<?php echo e($metatitle); ?>">
    <meta property="twitter:description" content="<?php echo e($metsdesc); ?>">
    <meta property="twitter:image" content="<?php echo e($meta_image . $meta_logo); ?>">


    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Dashboard Template Description" />
    <meta name="keywords" content="Dashboard Template" />
    <meta name="author" content="WorkDo" />

    <!-- Favicon icon -->
    <link rel="icon"
        href="<?php echo e($login_customization_enabled && !empty($login_favicon) ? $login_favicon : $logo . '/' . (isset($company_favicon) && !empty($company_favicon) ? $company_favicon : 'favicon.png')); ?>"
        type="image/x-icon" />

    <!-- font css -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/fonts/tabler-icons.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/fonts/feather.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/fonts/fontawesome.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/fonts/material.css')); ?>">

    <!-- vendor css -->

    <?php if($SITE_RTL == 'on'): ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/style-rtl.css')); ?>" id="main-style-link">
    <?php endif; ?>

    <?php if($setting['cust_darklayout'] == 'on'): ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/style-dark.css')); ?>">
    <?php endif; ?>

    <?php if($SITE_RTL != 'on' && $setting['cust_darklayout'] != 'on'): ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/style.css')); ?>" id="main-style-link">
    <?php endif; ?>


    <?php if($SITE_RTL == 'on'): ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/custom-auth-rtl.css')); ?>" id="main-style-link">
        <?php else: ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/custom-auth.css')); ?>" id="main-style-link">
    <?php endif; ?>

    <?php if($setting['cust_darklayout'] == 'on'): ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/custom-auth-dark.css')); ?>" id="main-style-link">
    <?php endif; ?>

    <style>
        :root {
            --color-customColor: <?= $color ?>;
            <?php if($login_customization_enabled): ?>
            --login-primary-color: <?php echo e($login_primary_color); ?>;
            --login-background-color: <?php echo e($login_background_color); ?>;
            <?php endif; ?>
        }

        <?php if($login_customization_enabled): ?>
        /* Login Page Customization Styles */
        .custom-login .bg-login {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        .custom-login .bg-primary {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        .custom-login .card {
            background-color: <?php echo e($login_background_color); ?> !important;
        }

        .custom-login .btn-primary {
            background-color: <?php echo e($login_primary_color); ?> !important;
            border-color: <?php echo e($login_primary_color); ?> !important;
        }

        .custom-login .btn-primary:hover {
            background-color: <?php echo e($login_primary_color); ?>dd !important;
            border-color: <?php echo e($login_primary_color); ?>dd !important;
        }

        .custom-login .form-control:focus {
            border-color: <?php echo e($login_primary_color); ?> !important;
            box-shadow: 0 0 0 0.2rem <?php echo e($login_primary_color); ?>25 !important;
        }

        .custom-login a {
            color: <?php echo e($login_primary_color); ?> !important;
        }

        /* Override the entire background */
        .custom-login {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        /* Make sure the login area has the custom background color */
        .custom-login .custom-login-inner {
            background: <?php echo e($login_background_color); ?> !important;
        }

        /* Override body background when customization is enabled */
        body.custom-login-body {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        body.custom-login {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        /* Ensure the main wrapper uses custom colors */
        .custom-login .custom-wrapper {
            background: transparent !important;
        }

        /* Header styling */
        .custom-login header.dash-header {
            background: transparent !important;
        }

        .custom-login .navbar {
            background: transparent !important;
        }

        /* Force background color on all elements */
        .custom-login,
        .custom-login .bg-login,
        .custom-login .bg-primary,
        .custom-login::before,
        .custom-login::after {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        /* Specific targeting for the problematic div */
        .custom-login div.bg-login.bg-primary {
            background: <?php echo e($login_primary_color); ?> !important;
            background-color: <?php echo e($login_primary_color); ?> !important;
        }

        /* Additional overrides for stubborn elements */
        html {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        /* Override Bootstrap primary color */
        .bg-primary {
            background-color: <?php echo e($login_primary_color); ?> !important;
        }

        /* Target all possible background elements */
        .custom-login *[class*="bg-"] {
            background: <?php echo e($login_primary_color); ?> !important;
        }

        <?php if(!empty($login_bg_images)): ?>
        .custom-login {
            position: relative;
            overflow: hidden;
        }

        .custom-login::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('<?php echo e($login_bg_images[0]); ?>');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
            opacity: 0.15;
            z-index: -1;
            <?php if($login_bg_animation == 'slideshow' && count($login_bg_images) > 1): ?>
            animation: loginBgSlideshow <?php echo e(count($login_bg_images) * 5); ?>s infinite;
            <?php elseif($login_bg_animation == 'fade' && count($login_bg_images) > 1): ?>
            animation: loginBgFade <?php echo e(count($login_bg_images) * 3); ?>s infinite;
            <?php endif; ?>
        }

        /* Add overlay for better text readability */
        .custom-login::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, <?php echo e($login_primary_color); ?>80, <?php echo e($login_primary_color); ?>60);
            z-index: -1;
        }

        <?php if($login_bg_animation == 'slideshow' && count($login_bg_images) > 1): ?>
        @keyframes loginBgSlideshow {
            <?php for($i = 0; $i < count($login_bg_images); $i++): ?>
            <?php echo e(($i * 100 / count($login_bg_images))); ?>% {
                background-image: url('<?php echo e($login_bg_images[$i]); ?>');
            }
            <?php endfor; ?>
        }
        <?php endif; ?>

        <?php if($login_bg_animation == 'fade' && count($login_bg_images) > 1): ?>
        @keyframes loginBgFade {
            <?php for($i = 0; $i < count($login_bg_images); $i++): ?>
            <?php echo e(($i * 100 / count($login_bg_images))); ?>%, <?php echo e((($i + 1) * 100 / count($login_bg_images) - 10)); ?>% {
                background-image: url('<?php echo e($login_bg_images[$i]); ?>');
                opacity: 0.1;
            }
            <?php echo e((($i + 1) * 100 / count($login_bg_images) - 5)); ?>% {
                background-image: url('<?php echo e($login_bg_images[$i]); ?>');
                opacity: 0.05;
            }
            <?php endfor; ?>
        }
        <?php endif; ?>
        <?php endif; ?>
        <?php endif; ?>
    </style>

    <link rel="stylesheet" href="<?php echo e(asset('css/custom-color.css')); ?>">

    <link rel="stylesheet" href="<?php echo e(asset('assets/css/customizer.css')); ?>">

    <link rel="stylesheet" href="<?php echo e(asset('css/custom.css')); ?>">

    <!-- Microsoft Clarity Tracking Code -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "rji5u8kka7");
    </script>
</head>

<body class="<?php echo e($themeColor); ?> <?php if($login_customization_enabled): ?> custom-login-body <?php endif; ?>" <?php if($login_customization_enabled): ?> style="background: <?php echo e($login_primary_color); ?> !important;" <?php endif; ?>>
    <div class="custom-login" <?php if($login_customization_enabled): ?> style="background: <?php echo e($login_primary_color); ?> !important;" <?php endif; ?>>
        <div class="login-bg-img" style="display: none;">
            <img src="<?php echo e(isset($setting['color_flag']) && $setting['color_flag'] == 'false' ? asset('assets/images/auth/'.$color.'.svg') : asset('assets/images/auth/theme-3.svg')); ?>" class="login-bg-1">
            <img src="<?php echo e(asset('assets/images/auth/common.svg')); ?>" class="login-bg-2">
        </div>
        <div class="bg-login bg-primary" <?php if($login_customization_enabled): ?> style="background: <?php echo e($login_primary_color); ?> !important; background-color: <?php echo e($login_primary_color); ?> !important;" <?php endif; ?>></div>
        <div class="custom-login-inner">
            <header class="dash-header">
                <nav class="navbar navbar-expand-md default">
                    <div class="container">
                        <div class="navbar-brand">

                        <a class="navbar-brand" href="#">
                            <?php if($login_customization_enabled && !empty($login_custom_logo)): ?>
                                <img class="logo" src="<?php echo e($login_custom_logo); ?>" alt="" loading="lazy"/>
                            <?php elseif($setting['cust_darklayout'] == 'on'): ?>
                                <img class="logo"
                                    src="<?php echo e($logo . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-light.png') . '?' . time()); ?>"
                                    alt="" loading="lazy"/>
                            <?php else: ?>
                                <img class="logo"
                                    src="<?php echo e($logo . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png') . '?' . time()); ?>"
                                    alt="" loading="lazy"/>
                            <?php endif; ?>
                        </a>


                        </div>
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                            data-bs-target="#navbarlogin">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarlogin">
                            <ul class="navbar-nav align-items-center ms-auto mb-2 mb-lg-0">
                                <?php echo $__env->make('landingpage::layouts.buttons', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php echo $__env->yieldContent('language-bar'); ?>
                            </ul>
                        </div>
                    </div>
                </nav>
            </header>
            <main class="custom-wrapper">
                <div class="custom-row">
                    <div class="card">
                        <?php echo $__env->yieldContent('content'); ?>
                    </div>
                </div>
            </main>
            <footer>
                <div class="auth-footer">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <span>&copy; <?php echo e(date('Y')); ?>

                                    <?php echo e(App\Models\Utility::getValByName('footer_text') ? App\Models\Utility::getValByName('footer_text') : config('app.name', 'Storego Saas')); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <?php if($get_cookie == 'on'): ?>
        <?php echo $__env->make('layouts.cookie_consent', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <!-- [ auth-signup ] end -->

    <!-- Required Js -->
    <script src="<?php echo e(asset('assets/js/vendor-all.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins/bootstrap.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins/feather.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('js/custom.js')); ?>"></script>


    <script>
        feather.replace();
    </script>

    <?php if(\App\Models\Utility::getValByName('cust_darklayout') == 'on'): ?>
        <style>
            .g-recaptcha {
                filter: invert(1) hue-rotate(180deg) !important;
            }
        </style>
    <?php endif; ?>


    <script>
        feather.replace();
        var pctoggle = document.querySelector("#pct-toggler");
        if (pctoggle) {
            pctoggle.addEventListener("click", function() {
                if (
                    !document.querySelector(".pct-customizer").classList.contains("active")
                ) {
                    document.querySelector(".pct-customizer").classList.add("active");
                } else {
                    document.querySelector(".pct-customizer").classList.remove("active");
                }
            });
        }

        var themescolors = document.querySelectorAll(".themes-color > a");
        for (var h = 0; h < themescolors.length; h++) {
            var c = themescolors[h];

            c.addEventListener("click", function(event) {
                var targetElement = event.target;
                if (targetElement.tagName == "SPAN") {
                    targetElement = targetElement.parentNode;
                }
                var temp = targetElement.getAttribute("data-value");
                removeClassByPrefix(document.querySelector("body"), "theme-");
                document.querySelector("body").classList.add(temp);
            });
        }
        function removeClassByPrefix(node, prefix) {
            for (let i = 0; i < node.classList.length; i++) {
                let value = node.classList[i];
                if (value.startsWith(prefix)) {
                    node.classList.remove(value);
                }
            }
        }
    </script>
    <?php echo $__env->yieldPushContent('custom-scripts'); ?>

</body>

</html>
<?php /**PATH C:\laragon\www\to\resources\views/layouts/auth.blade.php ENDPATH**/ ?>