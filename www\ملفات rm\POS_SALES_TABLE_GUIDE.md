# 🛒 دليل شامل لجدول مبيعات POS في إدارة النقد المتقدمة

## 🎯 نظرة عامة

جدول مبيعات POS يعرض **ملخص يومي** لجميع المبيعات التي تمت عبر نظام نقاط البيع، مع تجميع البيانات حسب التاريخ والمستخدم والمستودع.

## 📊 أعمدة الجدول

### 1. 📅 **التاريخ (sale_date)**
- **المصدر**: `DATE(pos.pos_date)`
- **الوصف**: تاريخ المبيعات مجمعة يومياً
- **التنسيق**: YYYY-MM-DD
- **مثال**: `2024-01-15`

### 2. 👤 **المستخدم (user_name)**
- **المصدر**: `users.name`
- **الوصف**: اسم الكاشير أو المستخدم الذي قام بالبيع
- **العلاقة**: `pos.created_by = users.id`
- **مثال**: `أحمد محمد`

### 3. 🏪 **المستودع (warehouse_name)**
- **المصدر**: `warehouses.name`
- **الوصف**: اسم المستودع أو الفرع الذي تمت فيه المبيعات
- **العلاقة**: `pos.warehouse_id = warehouses.id`
- **مثال**: `المستودع الرئيسي`

### 4. 📋 **عدد الفواتير (invoice_count)**
- **المصدر**: `COUNT(pos.id)`
- **الوصف**: عدد الفواتير المباعة في ذلك اليوم
- **الحساب**: عدد السجلات في جدول `pos`
- **مثال**: `25` فاتورة

### 5. 💵 **إجمالي النقد (total_cash)**
- **المصدر**: `SUM(pos_payments.cash_amount)`
- **الوصف**: مجموع المبالغ المدفوعة نقداً
- **الشروط**: 
  - `payment_type = 'cash'` أو
  - `payment_type = 'split'` (الجزء النقدي)
- **مثال**: `8,500.00` ريال

### 6. 💳 **إجمالي البطاقة (total_network)**
- **المصدر**: `SUM(pos_payments.network_amount)`
- **الوصف**: مجموع المبالغ المدفوعة عبر البطاقة/الشبكة
- **الشروط**:
  - `payment_type = 'network'` أو
  - `payment_type = 'split'` (الجزء الشبكي)
- **مثال**: `3,200.00` ريال

### 7. 💰 **الإجمالي الكلي (total_amount)**
- **المصدر**: `SUM(pos_payments.amount)`
- **الوصف**: إجمالي جميع المبيعات (نقد + شبكة)
- **الحساب**: `total_cash + total_network`
- **مثال**: `11,700.00` ريال

## 🔍 الاستعلام المستخدم

```sql
SELECT 
    DATE(pos.pos_date) as sale_date,
    users.name as user_name,
    warehouses.name as warehouse_name,
    COUNT(pos.id) as invoice_count,
    SUM(CASE 
        WHEN pos_payments.payment_type = "cash" OR pos_payments.cash_amount > 0 
        THEN pos_payments.cash_amount 
        ELSE 0 
    END) as total_cash,
    SUM(CASE 
        WHEN pos_payments.payment_type = "network" OR pos_payments.network_amount > 0 
        THEN pos_payments.network_amount 
        ELSE 0 
    END) as total_network,
    SUM(pos_payments.amount) as total_amount
FROM pos
JOIN pos_payments ON pos.id = pos_payments.pos_id
JOIN users ON pos.created_by = users.id
JOIN warehouses ON pos.warehouse_id = warehouses.id
WHERE pos.pos_date BETWEEN ? AND ?
GROUP BY DATE(pos.pos_date), pos.created_by, pos.warehouse_id
ORDER BY sale_date DESC
```

## 📋 الجداول المرتبطة

### 1. **جدول `pos`** (الفواتير الرئيسية)
```sql
- id: معرف الفاتورة
- pos_id: رقم الفاتورة
- customer_id: معرف العميل
- warehouse_id: معرف المستودع
- pos_date: تاريخ الفاتورة
- created_by: المستخدم المنشئ
- shift_id: معرف الشفت
```

### 2. **جدول `pos_payments`** (المدفوعات)
```sql
- id: معرف الدفعة
- pos_id: معرف الفاتورة المرتبطة
- amount: إجمالي المبلغ
- payment_type: نوع الدفع (cash/network/split)
- cash_amount: المبلغ النقدي
- network_amount: المبلغ عبر الشبكة
- transaction_number: رقم المعاملة (للشبكة)
```

### 3. **جدول `users`** (المستخدمين)
```sql
- id: معرف المستخدم
- name: اسم المستخدم
- warehouse_id: المستودع المخصص
```

### 4. **جدول `warehouses`** (المستودعات)
```sql
- id: معرف المستودع
- name: اسم المستودع
```

## 📊 أنواع المدفوعات

### 1. **نقدي (Cash)**
```php
payment_type = 'cash'
cash_amount = المبلغ الكامل
network_amount = 0
```

### 2. **شبكة (Network)**
```php
payment_type = 'network'
cash_amount = 0
network_amount = المبلغ الكامل
transaction_number = رقم المعاملة
```

### 3. **مختلط (Split)**
```php
payment_type = 'split'
cash_amount = الجزء النقدي
network_amount = الجزء الشبكي
amount = cash_amount + network_amount
```

## 🔄 التجميع والفلترة

### التجميع حسب:
1. **التاريخ**: `DATE(pos.pos_date)`
2. **المستخدم**: `pos.created_by`
3. **المستودع**: `pos.warehouse_id`

### الفلترة حسب:
1. **فترة التاريخ**: `pos.pos_date BETWEEN start_date AND end_date`
2. **المستودع**: `pos.warehouse_id = ?`
3. **المستخدم**: `pos.created_by = ?`

## 📈 مثال عملي

### البيانات الخام:
```
فاتورة 1: 150 ريال نقد
فاتورة 2: 200 ريال (120 نقد + 80 شبكة)
فاتورة 3: 300 ريال شبكة
```

### النتيجة في الجدول:
```
التاريخ: 2024-01-15
المستخدم: أحمد محمد
المستودع: المستودع الرئيسي
عدد الفواتير: 3
إجمالي النقد: 270.00 (150 + 120)
إجمالي البطاقة: 380.00 (80 + 300)
الإجمالي الكلي: 650.00 (150 + 200 + 300)
```

## 🎨 الرسم البياني المرافق

### نوع الرسم: **دائري (Doughnut Chart)**
- **البيانات**: توزيع أنواع المدفوعات
- **الألوان**:
  - 🟢 أخضر: النقد
  - 🔵 أزرق: البطاقة
  - 🟡 أصفر: المختلط

### الكود:
```javascript
const salesChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['نقد', 'بطاقة ائتمان', 'مختلط'],
        datasets: [{
            data: [totalCash, totalNetwork, splitCount],
            backgroundColor: ['#28a745', '#17a2b8', '#ffc107']
        }]
    }
});
```

## 🔧 الكود المسؤول

### Controller Method:
```php
// app/Http/Controllers/AdvancedCashManagementController.php
public function getPOSSalesData(Request $request)
{
    $query = DB::table('pos')
        ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
        ->join('users', 'pos.created_by', '=', 'users.id')
        ->join('warehouses', 'pos.warehouse_id', '=', 'warehouses.id')
        ->select(/* الحقول المطلوبة */)
        ->whereBetween('pos.pos_date', [$startDate, $endDate])
        ->groupBy('sale_date', 'pos.created_by', 'pos.warehouse_id')
        ->orderBy('sale_date', 'desc');
    
    return response()->json(['data' => $salesData]);
}
```

### JavaScript Display:
```javascript
populatePOSTable(data) {
    data.forEach(sale => {
        const row = `
            <tr>
                <td>${sale.sale_date}</td>
                <td>${sale.user_name}</td>
                <td>${sale.warehouse_name}</td>
                <td>${sale.invoice_count}</td>
                <td>${sale.total_cash}</td>
                <td>${sale.total_network}</td>
                <td>${sale.total_amount}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
```

## 💡 فوائد هذا الجدول

### 1. **مراقبة الأداء اليومي**
- متابعة مبيعات كل كاشير
- مقارنة أداء المستودعات المختلفة
- تحليل اتجاهات المبيعات

### 2. **إدارة النقد**
- معرفة كمية النقد المتوقعة
- تخطيط عمليات الإيداع
- مراقبة توزيع أنواع الدفع

### 3. **التحليل المالي**
- حساب متوسط قيمة الفاتورة
- تحليل تفضيلات العملاء للدفع
- مراقبة نمو المبيعات

### 4. **اتخاذ القرارات**
- تحديد أفضل أوقات العمل
- تخصيص الموارد بكفاءة
- تحسين خدمة العملاء

هذا الجدول يوفر رؤية شاملة وملخصة لجميع مبيعات POS بطريقة منظمة وسهلة الفهم! 📊
