{"__meta": {"id": "X23e4e9f940705ebe21e42e1f99a2cabf", "datetime": "2025-06-06 19:34:23", "utime": **********.881058, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238462.430013, "end": **********.881088, "duration": 1.4510750770568848, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749238462.430013, "relative_start": 0, "end": **********.704011, "relative_end": **********.704011, "duration": 1.2739980220794678, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.704031, "relative_start": 1.2740180492401123, "end": **********.881091, "relative_end": 3.0994415283203125e-06, "duration": 0.17706012725830078, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01827, "accumulated_duration_str": "18.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.792172, "duration": 0.01477, "duration_str": "14.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.843}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.834153, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.843, "width_percent": 5.255}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8424, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 86.097, "width_percent": 5.911}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.859957, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.009, "width_percent": 7.991}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1388349617 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1388349617\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1725223495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725223495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1415765061 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415765061\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1348269490 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238445287%7C44%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlIrTThTVlE4T2dpZE1BUi9xRkFjbHc9PSIsInZhbHVlIjoibFA4WWhCMjkxNjdhQUtCbW5QNzIrdERYV3JZMnY1NExTY2REYU5oQ0Z6VG5KNTlHVnNRRGE4VkVxTHZSSzA1UG13UFdIMkZhVUNQUUNCbDdNa1lrRlNEK1Vaazk4VGo5em5ydVBvMitacERjVnJMelNCVHR1L0FWOG9wWFF3N3A3NndLZ0pTQjlPNnVpNStyRllYanc3TUxWSXpiUWJUSWxLQkZvQ28xQ3JZNEtQamt6OE5MaTFMNlNMTjE4b0tKRXlPY1VDYUZLR1hYUml1dG1YSHJLUGJoUzQrWnhrZTIvaEVEVklESVFmQm8zWDFmTFF4RVF4QmZUZzUxOGZET2NqSHR5aWtoL1ZKVTZEUXpETTFPUEFuc0FoSElNN3dxZDdhV09UZ2dSbmd5eFpici9DdUhkNlF0TE1MSEZvNmNJeGVwUXFCeWlEeVp2Y29MMHJ2aXJEei9aV3E1RWgvZUR1amNKakRxMnJITEZYYW02NlVPUnBUZnp6emZPMldiMmUzVkRJNzdpQTl3ZTZRaTFFKzJzMnJ5ZEVOOVlwZW1tYlZvd0hRc1NRSUhMM2NHclJjOFozc293ekg4bVJHczYzc2h0TUs1aG9aU0NramtQOVFvYkFoZTFENUNkanAwa3Vkald1dE02ZEM5SWpvMFBNdlI0K0QrTVdUOE1OeXAiLCJtYWMiOiJmNDZhNTE3OGViMzAyNThjNmExMmMzNzBjZmExNWVjMzJkZWM0ZjViNTdlMjE0ZGM2YzRhZTk0MGQ5ZTkzODE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBQTVRRMCtlOG5lZEZULzdEZ3NMRHc9PSIsInZhbHVlIjoibUZaZ0FQN0FiRVdWbDdkTGhESWZqeVpRNzFzdi9nMzZ5R1hINTd3SlM0SXFjc0dUZm1rUHAyV1N2dHBuZVB0T2dBZnJUMDBPNTdxRFRxVFhlWkl2S3VaOVIreW16Mk40YTZ3dHZZdWRDbEx5b0daU3cvR0wxRVNydEV4MHhDVngvZ0VLakVBSEc2ZU5ocXVWUng2ek14K29ZUlVFandaY3JhaHRQZjN5dmtQNXRuWno4WXFYOHF4WExFMGZZbmo2QnUydXlzeVdQUC9HTnhyTStCL2VTVDZIZitqN3dRK3dxZlpvZWpOaDN6d25uSVR4Ym1RTDRrUkc3Y3NzYUJyZ3J6YitrV1pQMWl0TUxubEZkSjRrOXhqRys0QTk5R1d3NjRWYU9LQ2loaHdCZGdzUzZQdWJpTlBOSG9TSENmN3dGb2kzNElQbUUvbVBjVTF4T3NIeEREUlpXOXB3SFdhV1VHY29XbXFsY3JEYTJld09qMU92T1Y4clNGQjQralFpTUxTaEdzY0JNZzA3VTEwTXRWdURMRjRCNHl4em1OUFJRWHVwTWY3RWlPUFdNalRMWWVQUkZxcGx0SmppaXZ2UitqMmlxUlRTWFJGSlB1bU92YW5xeWtteHdwRTg4YzZMQU1NVkRRR0pRaGxXVTF0ZXljV01pWXUvVXRCNnFzWWgiLCJtYWMiOiJjYzkxZDlmYWRkODM1N2Q1Njg1YjQ4Y2E3NzFhZjFiOWYxNzIyOTM2ZjM0Y2ExOTUwZTdlNDZlMDVjOThjNTAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348269490\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-46038786 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46038786\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-410731539 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:34:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpWdHdpWXlQSEtxY2o5Y3JrVDRSQVE9PSIsInZhbHVlIjoiUkRDd1d0TXdudEVpa2h2QUkyT0s2UWJ3d2UrSFhxODZYZEZSbC81dXR3WEFMbm1OdkIrV2w5WEdhUlhZcVc0dGttUjhJVm1PYmFxK05GbWlMVjNTNWUya0drejBVekluZzZ0MHZuWS9rTHNkZGM4VS9nT1IrbTYxbmNWMG93QkozQlVLNXRBNEoxcGROa0NBbW0wZXgzY1JxQm1kVTZuZTJ2NHVZQ3FjQjI2b1lnc3RHL09LS2JFcWVHSVRHTmZLZG1taWJzK2xtRkhVWW9oNjFDRW5udkNLMlZhb0RUQlJBQ0laZllDSm9VY0NNcjMwQjlkNDR5WEdpdGlYdVM1RkFHZU1lVWlRcXdyckc1aGtXWVBNVGQ5OERrcEF0Q0s3NWdhZ3hncndBbFlHWVg4L1lwb0FtdHRaMllJelZIb1dlY3pVQW81MGRVZGpJVEhxWmxnR3ZtWjBQOExlWUVLNUdyWGZQQ2lqbmcyemowRFJhT1EzS1VsOWdzMElLUFZkU3ZRMkZEQ1VDbmFqZW8yWnpTSnBJYnlJc3JFSytoYjVzS25XSlU2cHBBNm9uT2J3VVpFWWNYeHdoejgyd0NUeVg2S0tpU1dZY0pXWDdwZ1pEV3dxSThnYURUMlVmYmtDL2xDUGI0cVV0cUxjdGM5OVBGYTl5aXN2U3EvR01OeVUiLCJtYWMiOiI0OTIxZTc2ODQwY2RkMmJlYWY0OTFjZTRiMTA0ZjUwYjVkMjA4NGJkNjRjOGZjMDZkMGU3ZDA0ZWQyYzRkY2VkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFiM01OTU9jY0Q4QkdONnZBbWxaOXc9PSIsInZhbHVlIjoidkVhL3dzMHZnZjVWanhwYmZ2YUdPUWZwZjBQS0FVb3NKaW0wUFRBOWpLTzE1WXg5cUxJYk0raXlib0d3ZFc2dzZ4VDN6YU96K2p2VUQrVHZKUEhOQ1R1cmVnRWNQc1UzZm5jSFJwcFVWSUpzMDQyOEZpaWpqREw3cGQ0enlEK0xjaUhCdHZwTXJJcnNLc2RnbFowOEtMNHlvZkpEcXduM3FDV2FXZXMzT2xrYWdSV2FXZVRwdi9ZUVRZM3J0Nk1FTFFuVE5qTWFUaUhBT3RIUEF6YStlcEU2ZXRDTm5jMStLY29kUDlwR2ZzODhzWjF4eDhQdHRsRWtkcjd0R1FTMWFjbWsvOTV0V25SZDl2TzkrODAxNFdSUXRJSkd4REI4bGlMQW5XK1VkMHpOdFBqUk1rRmJOd3UyWGNBWXV4NzRDZWtvOEp6Mm5rOHBacmcwOXdBUUxwYU1jSzZuclh4NDB6TWlTL0NyQXRUeXljcS9OYjhHK1lrWmpjOWVkaDh1RmRpMXVtcUh2Vzk2bkRabERFL3NmbVdVbC9EK2hNclprMGEzVkRURml3WDFEMDhBcVQ1WU9aUmthaUdlc1FWNWVYU3U3Y1hKK05xUGRub0NKWGlGRFlOekRXTWhLWmEzc0VDdy84cFV4VkhoUzBmU2NLT2xFUHA2U0pRSlVwTWMiLCJtYWMiOiJlZWI1NmQyYWNkZGVmZTU2Zjk0Yzc5MzU2YzE1ODM0NmJkNTE1YzM3YTgzOWY3OTFhN2I4NTZhMjIwZTAzMDVjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpWdHdpWXlQSEtxY2o5Y3JrVDRSQVE9PSIsInZhbHVlIjoiUkRDd1d0TXdudEVpa2h2QUkyT0s2UWJ3d2UrSFhxODZYZEZSbC81dXR3WEFMbm1OdkIrV2w5WEdhUlhZcVc0dGttUjhJVm1PYmFxK05GbWlMVjNTNWUya0drejBVekluZzZ0MHZuWS9rTHNkZGM4VS9nT1IrbTYxbmNWMG93QkozQlVLNXRBNEoxcGROa0NBbW0wZXgzY1JxQm1kVTZuZTJ2NHVZQ3FjQjI2b1lnc3RHL09LS2JFcWVHSVRHTmZLZG1taWJzK2xtRkhVWW9oNjFDRW5udkNLMlZhb0RUQlJBQ0laZllDSm9VY0NNcjMwQjlkNDR5WEdpdGlYdVM1RkFHZU1lVWlRcXdyckc1aGtXWVBNVGQ5OERrcEF0Q0s3NWdhZ3hncndBbFlHWVg4L1lwb0FtdHRaMllJelZIb1dlY3pVQW81MGRVZGpJVEhxWmxnR3ZtWjBQOExlWUVLNUdyWGZQQ2lqbmcyemowRFJhT1EzS1VsOWdzMElLUFZkU3ZRMkZEQ1VDbmFqZW8yWnpTSnBJYnlJc3JFSytoYjVzS25XSlU2cHBBNm9uT2J3VVpFWWNYeHdoejgyd0NUeVg2S0tpU1dZY0pXWDdwZ1pEV3dxSThnYURUMlVmYmtDL2xDUGI0cVV0cUxjdGM5OVBGYTl5aXN2U3EvR01OeVUiLCJtYWMiOiI0OTIxZTc2ODQwY2RkMmJlYWY0OTFjZTRiMTA0ZjUwYjVkMjA4NGJkNjRjOGZjMDZkMGU3ZDA0ZWQyYzRkY2VkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFiM01OTU9jY0Q4QkdONnZBbWxaOXc9PSIsInZhbHVlIjoidkVhL3dzMHZnZjVWanhwYmZ2YUdPUWZwZjBQS0FVb3NKaW0wUFRBOWpLTzE1WXg5cUxJYk0raXlib0d3ZFc2dzZ4VDN6YU96K2p2VUQrVHZKUEhOQ1R1cmVnRWNQc1UzZm5jSFJwcFVWSUpzMDQyOEZpaWpqREw3cGQ0enlEK0xjaUhCdHZwTXJJcnNLc2RnbFowOEtMNHlvZkpEcXduM3FDV2FXZXMzT2xrYWdSV2FXZVRwdi9ZUVRZM3J0Nk1FTFFuVE5qTWFUaUhBT3RIUEF6YStlcEU2ZXRDTm5jMStLY29kUDlwR2ZzODhzWjF4eDhQdHRsRWtkcjd0R1FTMWFjbWsvOTV0V25SZDl2TzkrODAxNFdSUXRJSkd4REI4bGlMQW5XK1VkMHpOdFBqUk1rRmJOd3UyWGNBWXV4NzRDZWtvOEp6Mm5rOHBacmcwOXdBUUxwYU1jSzZuclh4NDB6TWlTL0NyQXRUeXljcS9OYjhHK1lrWmpjOWVkaDh1RmRpMXVtcUh2Vzk2bkRabERFL3NmbVdVbC9EK2hNclprMGEzVkRURml3WDFEMDhBcVQ1WU9aUmthaUdlc1FWNWVYU3U3Y1hKK05xUGRub0NKWGlGRFlOekRXTWhLWmEzc0VDdy84cFV4VkhoUzBmU2NLT2xFUHA2U0pRSlVwTWMiLCJtYWMiOiJlZWI1NmQyYWNkZGVmZTU2Zjk0Yzc5MzU2YzE1ODM0NmJkNTE1YzM3YTgzOWY3OTFhN2I4NTZhMjIwZTAzMDVjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410731539\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-398192949 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-398192949\", {\"maxDepth\":0})</script>\n"}}