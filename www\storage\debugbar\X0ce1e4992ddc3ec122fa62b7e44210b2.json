{"__meta": {"id": "X0ce1e4992ddc3ec122fa62b7e44210b2", "datetime": "2025-06-06 19:30:54", "utime": **********.107353, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238252.496528, "end": **********.107393, "duration": 1.6108651161193848, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1749238252.496528, "relative_start": 0, "end": 1749238253.900057, "relative_end": 1749238253.900057, "duration": 1.403529167175293, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749238253.900079, "relative_start": 1.4035511016845703, "end": **********.107397, "relative_end": 4.0531158447265625e-06, "duration": 0.20731806755065918, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02146, "accumulated_duration_str": "21.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.010194, "duration": 0.01767, "duration_str": "17.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.339}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.05354, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.339, "width_percent": 5.452}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.063227, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 87.791, "width_percent": 6.477}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.080388, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.268, "width_percent": 5.732}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1349925423 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1349925423\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-672596167 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-672596167\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-432695897 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432695897\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-826669138 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238218778%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndyYnp5UFZ6Tk9qUDhnNldtalF0cXc9PSIsInZhbHVlIjoiaHhXc2pTWUp2K1NJV2pxT01UVDhycjVoajhvSkpOT0I1eXIzNEw1ZkNsRklSRnAwNytaeWNpZXFTSURZNXhHOWxYUEl0aGlZc2lGTklHS085TDg5OU5qbjMzN1hQR3VGWUorb0hKNlVVZTV3czNjaWxXYnRNRWlFTGl6MzJxYTZUZklUcnhNQzdRazNFaE9lTWJIeWpkWHBLa3QvbEEwOFRkYWRhT25sb2x0L3FNc3ZmOW8rWUwrU3dHMHgzM1RJL1pEci9RSlp3VjI4cDl5U0kvT1k1MVRlQmQ3ZFdsQnNFU2Q4TFRsS3VKekY4c3ZCRlo1YzYxc1J2cUNLelhkVWhTaVJiaVkxTG5WRVBkOFZ5ZmtHdGN4M2ZDVmNaK0hobHhlcXpid084SmMxNTRnKzdHWm1va0hxYVVYYlYvN3FhTWdyK2VjUTdwbVd2aFg3TXZ5UklrUnlERldvbWk4MGQ1N1E2ekIxKzd6cXpDUHNlNkVmMlAwM3JGa3dXK0d6VVB3SFlEVlZSOE9oTHp0TXhxWnk4VDU5Q0VQZmsyRGU3OHU4cEp1R3k3VVdNUnFZRStkdnJZT1FDNkhNS2ZuZEkrN3pnMk52ckREVmcvYXp3UzZYT1FMU1JVSzBmeVVrdWtoell1a0IwbWRZeHY3bW9ia0t2VHdpb1d3WXZYeWkiLCJtYWMiOiIwNDIzYTM1MzgyYWMxMTdkYWExOTc2MDRiMDEyMTIxOGZlODJkOTE2ZGQwMDM3YjViMzM2NGYwYzMyZmNjMzI5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVtWWd0d3RTTGhiN3lDYzBHeGRqV0E9PSIsInZhbHVlIjoiL29MMmJaUEtEMmlJWFZ2d3JFdkZSM2xMdnBwYVg2cG14SEVBQ3dpUCsxck5SNlFudlUxZnMwY20yQVVZeEdEcUxSbXBISUJhYW12c0ZFcFNDSDYxc3FJMVRlc2xFN1diQ3VKVEVpRmZHVm5laFJUT092Wmx0T3RlLytwSDc5QUxEWXA1T2hRaHBrVXA5RmdQOWUwWWcvVS9iclhpY2xUYWRwZ21SWVNnUk1mUzRybVhCdEJscVduUnRuRmwyVmRrN1lDNlYvV3VkZzVkS2p0TUsvNlJqU3QrMkRQc1U5MDJXREZVWFNGdExVSExlUWp4cTU3Vzk4cTI1RnA1Y0hsYVJoOVFVNXVCWVZSZlhyYVhYNXBFdEpNSGJacE8xazlvWnlzOWVCd05heExucDc4NEJzN1pHc1V4RnlZTStHdTZZQXhmaUlNN0l1UXYyU3l5WmVEaDdPbzJQVDU1RmhURytOeTZQZUdXK0FyR3piZmIzcGdjVTMwV2NPYldPRmh6Q3NST0UrNjZyOG1zUmxockRCTmVjNmVsY1d6RXV2a0hFRU42L091WElkalhXb3pubHBrc1dXVitTTGtJVllrOHFXS1BVTXdYODkxSGdlRVpwUWw5YWtIKzZnbDByWVBZNlZWTkNvR1RhL3hBZHZxZmlReXgxMWF0TGdQWFRnVXMiLCJtYWMiOiIxOGFmZjQzY2Y5ZWNiY2YyMTNhNzcxYzFkMDRmYjk0YTJlYzc5NTFlYzk4ZGUwZjgzMTA1NDlmYjE1YmEyYTM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826669138\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1480820249 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480820249\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-938451438 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:30:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhWZ0EvS25kcU5JVmlxdXJ0RWRFdGc9PSIsInZhbHVlIjoiRDJhSGNGUzI1OUZLdnQ2bzEycUEybkN1aTZGYmhiT0l4cjU1bWl5V3UxeHRydnlvcnBUN3dWQTBpRng5RUhHcUh0OCt0MHRONlRqSWhvUkNIcEJoOGMrMVJkRjJPVnFjVFBFV2Q1TTZxb1UxWVAxZFRKUWl6R0hWeU1yc253RzhrUW90TjJGdUlTWHp5UWVEUFVQVjNDQTJJZ0ExZGhDSWNiT2JmMk43emhieW9hU3Ezb1JqWnA4cWJxYUg0YmR1aFFLTDJ1UjVSVDJPNjFPNFBYdllyQnV5Yit4QTJ2UEtqei84Zys3SCtNRkdUcHBYNmJmWEx4NnRzNjIxcURQL1pxUDl3ZE5xK3h3K01oYUxBVHBLZ0x0SVQzUU1NMjNLVGMrLysyM001ZnhycE90QTYwenVndHArWlJ4K1o5UmxUUFN1ZUw1dzdjTGRqMWhoZkRmZldmU0Z0aXdZSFY5OGppUXFlWmJ0U1pIUEMvYnJYemxiazRwMVpXYzAwK3J1VHJWRmFRN0ZiaEpFYkVOMUcxM0c5eDZEbjkvemNVd0lDQjQ2bXZlTnUrOW5sdFgzRGhWbmIyVVhTUGphMk9WMVV0N09aZWc5NGZuQW1YRWRjdGxQeFlTeTdmYXJNdG1qYmg3KzFtVTB0aVJXSmNpWERZYWw2SWlQWUc5MFhwMVQiLCJtYWMiOiJkNmJkMDk0MzZhZmRjNDI3MTVmNzViZDU4OWEwNmYzZGFhYWE2MzU1ZGY3ZTFjZDQ5ZjVjODkzZjc0YmFlN2ZjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:30:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkxSU3Q1M0VGT05JUGlXNldrZXI1dWc9PSIsInZhbHVlIjoiTzFqNmFpMGs4WUFPcEpwWnI5VlcvQUVkdW9CYkEyYk9uektsNUYvd3dOMTcyN3NMTEkram85bVdmb3psYnBmQUlrTW5HM0R2OUdpeWRjZ251TWJVMHhFMzNIdS8yNmpkNExyL3VkbU9MWTZCWjF4WStuR21oSmdvUWpzblU2bHVxTXh3VEtQMEVhbTU3b2xtWHcySkZKeWdpUXVmUm9Mc1U5QmxlSC9tVEc2bDhvVzBrNVZNbERHWEFHM2xSWjVVUFVtS1d1S2hsT0RSaGp3WjNETlNvU2o1Mi9RL09HUFRpRGlmTkVKbm1FcUkydDJ4N3FBVlV0TTY5L28xTE9YNWxJa1lmZmxaRWJWamxRN2N2Q0VwcHlISmdkOXhJOVVFUnhYcGY4Q0dLeC9ib2FnVEUvV3YvSzFYS21ndUVqUnIvbXRHRmtReTl6OEJPWnR1Ly9idUxPMnRPckl4U1pkbG9zUU02SGpUbDgvbmNzNkc0N0kzUjZpeHlveE41aHZETkl2UlRBYm8ycU5QNWVDNnlUaHlIRWFyd0xIc0FDdkRldTlxaUF0aWtJQlAzNkpqdGpNZ29waVN1ZE1oMEVNUllxSkNORWhuOEpjTGhPdzdBenlWaUIwSWZUQWtMdkNKN1hCUis4RTMwNG9OaFRvVFhpMDloZXhvdmg3SCtPNDIiLCJtYWMiOiJmOWI1NDQ1NmU2MDcyZjg2ODg1NWI5MzQ1ZmMzNzcxOWEzZjYxOGI2ZTNkZTkxZDRjNTkxMTMzMzBjN2M5OGE0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:30:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhWZ0EvS25kcU5JVmlxdXJ0RWRFdGc9PSIsInZhbHVlIjoiRDJhSGNGUzI1OUZLdnQ2bzEycUEybkN1aTZGYmhiT0l4cjU1bWl5V3UxeHRydnlvcnBUN3dWQTBpRng5RUhHcUh0OCt0MHRONlRqSWhvUkNIcEJoOGMrMVJkRjJPVnFjVFBFV2Q1TTZxb1UxWVAxZFRKUWl6R0hWeU1yc253RzhrUW90TjJGdUlTWHp5UWVEUFVQVjNDQTJJZ0ExZGhDSWNiT2JmMk43emhieW9hU3Ezb1JqWnA4cWJxYUg0YmR1aFFLTDJ1UjVSVDJPNjFPNFBYdllyQnV5Yit4QTJ2UEtqei84Zys3SCtNRkdUcHBYNmJmWEx4NnRzNjIxcURQL1pxUDl3ZE5xK3h3K01oYUxBVHBLZ0x0SVQzUU1NMjNLVGMrLysyM001ZnhycE90QTYwenVndHArWlJ4K1o5UmxUUFN1ZUw1dzdjTGRqMWhoZkRmZldmU0Z0aXdZSFY5OGppUXFlWmJ0U1pIUEMvYnJYemxiazRwMVpXYzAwK3J1VHJWRmFRN0ZiaEpFYkVOMUcxM0c5eDZEbjkvemNVd0lDQjQ2bXZlTnUrOW5sdFgzRGhWbmIyVVhTUGphMk9WMVV0N09aZWc5NGZuQW1YRWRjdGxQeFlTeTdmYXJNdG1qYmg3KzFtVTB0aVJXSmNpWERZYWw2SWlQWUc5MFhwMVQiLCJtYWMiOiJkNmJkMDk0MzZhZmRjNDI3MTVmNzViZDU4OWEwNmYzZGFhYWE2MzU1ZGY3ZTFjZDQ5ZjVjODkzZjc0YmFlN2ZjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:30:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkxSU3Q1M0VGT05JUGlXNldrZXI1dWc9PSIsInZhbHVlIjoiTzFqNmFpMGs4WUFPcEpwWnI5VlcvQUVkdW9CYkEyYk9uektsNUYvd3dOMTcyN3NMTEkram85bVdmb3psYnBmQUlrTW5HM0R2OUdpeWRjZ251TWJVMHhFMzNIdS8yNmpkNExyL3VkbU9MWTZCWjF4WStuR21oSmdvUWpzblU2bHVxTXh3VEtQMEVhbTU3b2xtWHcySkZKeWdpUXVmUm9Mc1U5QmxlSC9tVEc2bDhvVzBrNVZNbERHWEFHM2xSWjVVUFVtS1d1S2hsT0RSaGp3WjNETlNvU2o1Mi9RL09HUFRpRGlmTkVKbm1FcUkydDJ4N3FBVlV0TTY5L28xTE9YNWxJa1lmZmxaRWJWamxRN2N2Q0VwcHlISmdkOXhJOVVFUnhYcGY4Q0dLeC9ib2FnVEUvV3YvSzFYS21ndUVqUnIvbXRHRmtReTl6OEJPWnR1Ly9idUxPMnRPckl4U1pkbG9zUU02SGpUbDgvbmNzNkc0N0kzUjZpeHlveE41aHZETkl2UlRBYm8ycU5QNWVDNnlUaHlIRWFyd0xIc0FDdkRldTlxaUF0aWtJQlAzNkpqdGpNZ29waVN1ZE1oMEVNUllxSkNORWhuOEpjTGhPdzdBenlWaUIwSWZUQWtMdkNKN1hCUis4RTMwNG9OaFRvVFhpMDloZXhvdmg3SCtPNDIiLCJtYWMiOiJmOWI1NDQ1NmU2MDcyZjg2ODg1NWI5MzQ1ZmMzNzcxOWEzZjYxOGI2ZTNkZTkxZDRjNTkxMTMzMzBjN2M5OGE0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:30:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938451438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1291516901 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291516901\", {\"maxDepth\":0})</script>\n"}}