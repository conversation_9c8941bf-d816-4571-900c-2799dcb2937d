{"__meta": {"id": "X0234842cbe79155703cfde10529f5aae", "datetime": "2025-06-07 04:19:50", "utime": **********.677532, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269989.27745, "end": **********.677582, "duration": 1.4001319408416748, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1749269989.27745, "relative_start": 0, "end": **********.476926, "relative_end": **********.476926, "duration": 1.1994760036468506, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.476946, "relative_start": 1.1994960308074951, "end": **********.67759, "relative_end": 7.867813110351562e-06, "duration": 0.20064377784729004, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44768040, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01665, "accumulated_duration_str": "16.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.582113, "duration": 0.01469, "duration_str": "14.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.228}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.635186, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.228, "width_percent": 5.766}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.654814, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.994, "width_percent": 6.006}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1996757419 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1996757419\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-295483310 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-295483310\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1216280156 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216280156\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-327621244 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269951479%7C8%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxrWkdpQzl1dkduRGtocmNuNUIyYXc9PSIsInZhbHVlIjoib0U2NmxkT0pZa3o1MnRpWVZhQmthVTFOWmQyZ1VpYUREd2hsSExSakV0TVI2cFFkTW9TUjdrZzNMQitkOHlDTlJjYmZUcFpZTEppeTZMa3l1eGZMa2tpdEhyVUprWHJiSHZXVytLOW1MRDFSQ1BUVjlFbGQwTHZFd2ZOZU9yNG92MVFuT2FLVGdQRnVXSmlvMU1aQ25sNjFsVk1vMFR0Z3dLNC9GN0p3VW4xMStnZzIxV2VHbDR0UHBtdU1NVnhtRXVxK2IvaXNMQTNhMEYxMU5xb0cvMDJFZlJLRDlVNWhyd3d4RFY4SVdBcERtR1d6V0VQTnk3RFpxZmY4Q0d2QkkvUEpFMCtMODk3MUZWMytQWkdMOHR1TWd3NkhzTERFeXA5TUFmRklNUEV3WW56bFkrOWlxNVNrTldUNll2QTNxOTFMbFBwNS9mMWppWlU1VWZCZHFSd2pWUDVXK2FWekc5VWRwaDVHZW5XL01qVTJsTVRGbStYQUtFSzQxV0RDVEdPeXVLckVVSGcwODhYeHpiSGpxWkZEZUkrd2cwTytaVDdST0pDTmhIL3dUaWFsTHFWdzFpVnBJdFJYMFdXYmd4b3F5QUpsTFZWREFlbWEzZXNRSmd5Mk10M3VSTkErYVJlOUhSVC9ybVVoY01BdXNmN0pXMElkbHUxOU9SaFAiLCJtYWMiOiI3MWI5MDJmYWY2ODYyZjdmNjgyYjcxMDFiOGY1NjdkYjFlM2I4YjZkYzk2NjE5NzE0YTI0ZGM3Mjk0YzNhZTQ0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFrZXlIUDFJODc1VDk0ZExKcWZIYkE9PSIsInZhbHVlIjoiOUFVcG1aQWx3MG1MRS94RTVGVUp5NXFDNVhzT29rV1dwdHZ4WTJxTFdENjQxbDR2UTdMOGRNeVB0RkJ1dUtDY2hRYjFRY0dyOXpwK0hkZ3A4citqbWZQYW02ckZvbWtzKzNiL3NQeUFjR1pPNzVPSTk5RXhuODk1ZCtoRUVnQWxiR0Ezb25MbG9OSGNCVzFYckRJR0VzaVF5bnlQTEpWN2laK2lnVGcwbDlSWk9JOW1HcTh1UEgvaXNlYjMxMTMzTjd5TEhUaHBNeURscDVTUVEwMlkzdm1icitSWkh0KzVhQ0Rpa3NPR1ZCWWhuMVVLMG9jUmIwaGlpcElVVVh5Y2xuVXdhTDMvbnkzOTVaL0U3U0o2TW1FNHNUcnhPNG1zYlpNU2tqWWZmOXY3VmJMa0l2QUdTTjNRZ3dUa1gvUjJFMUcwT29TR2lWR2JLOFM0VlVFTkM5RjhYQytIbCt6U1VVVWVDT1hMVEY1eEtSTGtPNXkzeEZRTCtPd0VGaWtBNjVubllJakZ2M3JHVTlTdGVuVXJTbUJPRjlrSFI1b00wK1ZOTlJvaE9oeEM2c3JoRHNJNlBDOE5HakZ3WDZ6MHU0S1QwUTlXYSt5d1lQSDlDR3lNUURPSW1sczhzRzNKcGlWVGVuQkJIdkE5REZ5WTZxamNnelg5cExydFJqRWkiLCJtYWMiOiJiYzU2YjgxNWJhZmRmMGJkZmJkYmQyZmRiZDNiYmM4ZTFiYjVhODg5M2NiZjNjNjhiMGFmY2FiYjEyNTExODVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327621244\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1003687766 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QF4lxftbqb9bEiJQUWBNsJwytQRufosJhQtAJ0pY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003687766\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:19:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVCZkNjMG1xRHdvQkkvNkVIbmVXaWc9PSIsInZhbHVlIjoiT1k0cEg0L2RNTmdVY25KeWszVmhCR1hIUlBCNzVJa0dWRVJweG1PQlpYRHVPQ0NsR0NNYzZBQnQvRjhwVGRWcjlPN2dQRFpEVnVzZkoydG5YN001Z3BBekVwdVpHM3pqSFBVOHZRVTlFSnFnbmw3TGZmcG9GYmJ6YzczV0lYd1V4YXRMdExOVmxvRElTWCt6RlVZcTU4MHhVWEQreTBFQ0E0TU9wMmdzcUt4TmFxRHRKZWk1dkFERUlIWlNOdnkrVXRIUnZTTU1QNm1uTWcwNmRWRkZqOXUxbWd6ZSt2ZmZ2KzI4TWh6ZUxqeTFaZWFZUjZQdGtiV001M0VxZEhkWWxrYnMzYXF5eE1tdHZYSk1oZWNmZjBENFh4TTI5NG8ydk9xcjZmaXRJYmNTWStxU2h3OENCK092cFpGN3JudzRoV2l3RnREM2tDY0pFRnBHWEU0NyswNmRPUjVPekRxSS9sL1FQTjJFL3BRT2czTUgxQm1MVHU4a0FrZVhFODNVdmJrVS9Hd2VsSzViWVdnMlE1SnFicmJOUytTWHlxK1JCV3FadW45eXpVMEg3ajVkY1BJZVFQTkxBRU1IVkNkcE8vQUI0a1BBcVhFdWEyZzVWUmRQbVVQVElRTUtXRnhkUWJWamRtOU1VYmNtbVVPOFhEeHMxelFRSWxDeTlIRTQiLCJtYWMiOiJlYTQ4MWRjZTBmZjIxMzNhMmRmYzNjNjJiNDJlNGJkYTQ3OTg1MjE5ZGUwNDZhOTg2ODFjNmRlMDllNGZjOWEwIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:19:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkwwZFlBaXF4YjdIN25EREoxQ01RckE9PSIsInZhbHVlIjoiUFZtYjR6QlpZOEU0OEpvY1F5d21HZnlOZnBCMWNzczQyWDZzWnRRSG05aU5ONzU0R2IvNkJVNEppd1hURTdIb3pSRy96VXRLVEsrT3lZRldIeGJnZ3JHREMyaWFqWWs4OXJpYTFsRDYvZ0p3b2pxcEVtMEhMU1hMUzJmNFN5N0UzQVlkV1I1N0pMRno0WGx0dzh0WmMxenphSnExZERkTDEvT1c5M2R5UWMrQko3bENGaTAxakszZkdnSEtZdld5Q29MRjJwVVcxcUxDUE9ZQjRLMjJLMUFiUlczZk1xSDA1RnBINk42bCsveldSNk5Wc0pydUFuRXVxVkxwbkY4amVIbkpRbUx4UG5KRm5lZ21mOGRDRUlwRE9WVGloV0o5VGxOTllyZG9xNXRwRDRDRkJzMkpsNk9rdFhuQlYrekFTYWJwRGhJSFZUTjdEWDlXVm4zUDloYUFmWEJXMUR6WkFGZEJBZFVUYjkwaU51bXlwMmRnTVJIOXAyWEdqemdSdTd0bjJYbmMwWFlzejBuZlZ4Tllzbmk4WGZTVTIwQUlleWpnQVJCbEJTcnJDSFREQk5kdmVBSWhZbGdrTnVNS1JxaUJYMkZVRHFnRlJsMUkzYkhPR0tmY0l5TmZMZVdtZzVxemhoWUlCOVo4bEZlMlo1RGFZUk9yMUk4SmtRMHMiLCJtYWMiOiJhNjM2OTNkNmRjNWMzNzZmNjUzMTg3NjJjNDAzNDk4YWI0YzkwZWFkOTI0ZTA1ZDIxNmQ3OWU0NTEyMGJiMTRmIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:19:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVCZkNjMG1xRHdvQkkvNkVIbmVXaWc9PSIsInZhbHVlIjoiT1k0cEg0L2RNTmdVY25KeWszVmhCR1hIUlBCNzVJa0dWRVJweG1PQlpYRHVPQ0NsR0NNYzZBQnQvRjhwVGRWcjlPN2dQRFpEVnVzZkoydG5YN001Z3BBekVwdVpHM3pqSFBVOHZRVTlFSnFnbmw3TGZmcG9GYmJ6YzczV0lYd1V4YXRMdExOVmxvRElTWCt6RlVZcTU4MHhVWEQreTBFQ0E0TU9wMmdzcUt4TmFxRHRKZWk1dkFERUlIWlNOdnkrVXRIUnZTTU1QNm1uTWcwNmRWRkZqOXUxbWd6ZSt2ZmZ2KzI4TWh6ZUxqeTFaZWFZUjZQdGtiV001M0VxZEhkWWxrYnMzYXF5eE1tdHZYSk1oZWNmZjBENFh4TTI5NG8ydk9xcjZmaXRJYmNTWStxU2h3OENCK092cFpGN3JudzRoV2l3RnREM2tDY0pFRnBHWEU0NyswNmRPUjVPekRxSS9sL1FQTjJFL3BRT2czTUgxQm1MVHU4a0FrZVhFODNVdmJrVS9Hd2VsSzViWVdnMlE1SnFicmJOUytTWHlxK1JCV3FadW45eXpVMEg3ajVkY1BJZVFQTkxBRU1IVkNkcE8vQUI0a1BBcVhFdWEyZzVWUmRQbVVQVElRTUtXRnhkUWJWamRtOU1VYmNtbVVPOFhEeHMxelFRSWxDeTlIRTQiLCJtYWMiOiJlYTQ4MWRjZTBmZjIxMzNhMmRmYzNjNjJiNDJlNGJkYTQ3OTg1MjE5ZGUwNDZhOTg2ODFjNmRlMDllNGZjOWEwIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:19:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkwwZFlBaXF4YjdIN25EREoxQ01RckE9PSIsInZhbHVlIjoiUFZtYjR6QlpZOEU0OEpvY1F5d21HZnlOZnBCMWNzczQyWDZzWnRRSG05aU5ONzU0R2IvNkJVNEppd1hURTdIb3pSRy96VXRLVEsrT3lZRldIeGJnZ3JHREMyaWFqWWs4OXJpYTFsRDYvZ0p3b2pxcEVtMEhMU1hMUzJmNFN5N0UzQVlkV1I1N0pMRno0WGx0dzh0WmMxenphSnExZERkTDEvT1c5M2R5UWMrQko3bENGaTAxakszZkdnSEtZdld5Q29MRjJwVVcxcUxDUE9ZQjRLMjJLMUFiUlczZk1xSDA1RnBINk42bCsveldSNk5Wc0pydUFuRXVxVkxwbkY4amVIbkpRbUx4UG5KRm5lZ21mOGRDRUlwRE9WVGloV0o5VGxOTllyZG9xNXRwRDRDRkJzMkpsNk9rdFhuQlYrekFTYWJwRGhJSFZUTjdEWDlXVm4zUDloYUFmWEJXMUR6WkFGZEJBZFVUYjkwaU51bXlwMmRnTVJIOXAyWEdqemdSdTd0bjJYbmMwWFlzejBuZlZ4Tllzbmk4WGZTVTIwQUlleWpnQVJCbEJTcnJDSFREQk5kdmVBSWhZbGdrTnVNS1JxaUJYMkZVRHFnRlJsMUkzYkhPR0tmY0l5TmZMZVdtZzVxemhoWUlCOVo4bEZlMlo1RGFZUk9yMUk4SmtRMHMiLCJtYWMiOiJhNjM2OTNkNmRjNWMzNzZmNjUzMTg3NjJjNDAzNDk4YWI0YzkwZWFkOTI0ZTA1ZDIxNmQ3OWU0NTEyMGJiMTRmIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:19:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1190291590 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190291590\", {\"maxDepth\":0})</script>\n"}}