{"__meta": {"id": "X9f1f322c8a1815b6b092f539b6801824", "datetime": "2025-06-06 19:29:41", "utime": **********.728161, "method": "GET", "uri": "/roles/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238179.916336, "end": **********.728191, "duration": 1.8118548393249512, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1749238179.916336, "relative_start": 0, "end": **********.302094, "relative_end": **********.302094, "duration": 1.3857579231262207, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302118, "relative_start": 1.38578200340271, "end": **********.728194, "relative_end": 3.0994415283203125e-06, "duration": 0.42607593536376953, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53610712, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.create", "param_count": null, "params": [], "start": **********.655164, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/role/create.blade.phprole.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Frole%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.create"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.685727, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.create", "controller": "App\\Http\\Controllers\\RoleController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=31\" onclick=\"\">app/Http/Controllers/RoleController.php:31-57</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.03547, "accumulated_duration_str": "35.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.408189, "duration": 0.02, "duration_str": "20ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 56.386}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4599938, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 56.386, "width_percent": 3.073}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.469355, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 59.459, "width_percent": 3.299}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.531454, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 62.757, "width_percent": 4.962}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.539156, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 67.719, "width_percent": 4.426}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\RoleController.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5565212, "duration": 0.00873, "duration_str": "8.73ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:45", "source": "app/Http/Controllers/RoleController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=45", "ajax": false, "filename": "RoleController.php", "line": "45"}, "connection": "ty", "start_percent": 72.145, "width_percent": 24.612}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.create", "file": "C:\\laragon\\www\\to\\resources\\views/role/create.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.6656852, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 96.758, "width_percent": 3.242}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 510, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 513, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1362003154 data-indent-pad=\"  \"><span class=sf-dump-note>create role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362003154\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.554787, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/create", "status_code": "<pre class=sf-dump id=sf-dump-780048781 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-780048781\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-11638567 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-11638567\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2111442833 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2111442833\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-136447415 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238175432%7C35%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBhdHprMFp0Q1owL2U3RXVHczZkaEE9PSIsInZhbHVlIjoiVXc2blRVR3FoeVdoQTZzcmFFNFNtZmpERUh4UHhGOFpUOWZhTndacGVJRi9YalB1S3VRQWNwTjJYdFNKV3BMSm9kb0ZhZ0pNVGN4S3BOSU1wR2lpOU1WWFgxNGFOV2swRVpRM3kyQ2VJNXA2bUFDRXBOU2tweUxucityYnVaUkNOS0lkSFI1R2M4UVZUM0MwVnc1RDY3OVN1dG5EVXpNY1Y0K2RLMVN2L2RMZ2JqR0h2QVcvR2hLdDZRY0VqZE1tS3dkd1pOTy9odEVLcW8xaElsbTRUN2F0K0hob3RtVjV6UzZNeEJpOTBETi9JZG5FVGkxMnJQR1RSZGZCb0dlcXdKRWxqWjYwK29ZTTZVUmFTRWVyYXNPdVgvdjZBclJFSGIzaWlIRk9WOXQ1SEZZbXYrZXdSZW8yM1h5M1Fid0ZaK1ZaVE42SENGZ08rM3BHTzNnTUdLZ3d3MmZMZVQ1NVBzZnhIdC9sSEc4dk5TdlRvT3FwVGt2bmYySXZxNUlDdGM2STFHUGcwQmF5Y0liTkNDL0V0emhQeE5Rcit5MDZKWmJQcVdQWXduaWN5Q1hkR3gxOXhWVlJMa25Wd21QQmVBeEE3YUFjaXgwM1dUUWgwMjBMRUdaS005d3hXdVkwdFFIcHFtM2pydTF6eWtudktNaGZNMHJ6ZmV4ZlhYVEMiLCJtYWMiOiJiYTIyZTEzNjg3MWQ5YjJmZjQ1ZWY4ZWY0ZTIwMDY0NTc1MmMwMTRiOGI0OWQ0NzdlZjMyZmFjNjUyZmQ0NTkxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhvMC9zTEpXTkJNdWhxMFZsaWVwa0E9PSIsInZhbHVlIjoiVmZZVDlpbDhkRktoQ1c4NW5RQUJkOFRxNjN6Y3RCQ0pheUw5cDg5OUJVV0psQTZvMEtEbWlYUmxBNVJDYkkxdlhCUTBsWFlmbTRBaW5IL3Q4RnNOS2ViU2VtSmZUNkVJb3dndWlEdWVnN3Y2VmVlMTBCRDFwRVZPRTNLanh6K1VLUzFIVUgza2lTTGhQbjJHU3M0TFU0ckk0YlR5VlJnZExLUXdaVnZyNXJVK2NZVEYvT3ZUVk9oYjBXdzFwTHR5OFFseHJSMDNQUlFSbVN4eXd2RHM2dUEraXlXZlkvQnhVaWxUeVdPbWNLYnEwRHgwa2ExYlNNS0E5OWIvRk11ZlUzYkFhU2VTVjd4T0laSE5tenowaEJkQitZa2Q3eGlpWm5oYXlBWmlwaVBVRU9tbWhnT0hxOGdMRUlpbVR6NVJoN3M1RFVvNVhGa251cjZLYmU4M1p5NVkrZ2R1L08rMDJKUWRvdVpyc25mZ2FWTXkrTGFNVEFod0IrekZMVU5wRFlaaCt0WlRiMWF5eVZtRGFkTFJtV1BpNCtCOGpuNFV4aXA3c1JDODFnOE1mRWh3VUlGK1Myb0Z0VEJoRUJpU25HSmtmak1lMjUvL1J0b0ZzdlVTRCswYS9OUWNYZU5mOTh3MS9qWEFkR2ExZG5NSHJmMHBOLzRJYWZzVCtXclkiLCJtYWMiOiJiMzY3MTFjOWNlOTcyY2Q2ZjQ2YzhjNWRiYjg5NTUwNzc1NDE1ZWUwODgxODE5NmZkYjM0YWYzMGI3ZTQ5ZjkyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136447415\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2021075904 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021075904\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-70182550 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:29:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9hWmNGMWExN1NGK1BNTHVlTG1Rcnc9PSIsInZhbHVlIjoiTWdpdm9QWC8zb2t4QlQyQVQxWm9rRzIwazNsNzVPZmYyV3FBTWtKNHkxa29hbDBXdml1aUdXOE0yeUFtbTNsLzFHdFJGK000OUpqVW8yTit4RHB6ZitLWS9SQjRqTW1VejhXeDFmUmUycUZxUG4wcHBvU1FJZFZzenYwcmtxU084R2RheDlLNWczTHJSOVJjZkRwcGZ2dkNUcDR4LzdSUTlBL0JDS2FhRkQrMDYybVgydjJ3eVJRbVB2UHpZVytrcjlUeVVUQTBoN0V0bHArRUYraTVqRG5IRG1oUnMvb1JzeGlMRzlLVEU2c04raDBValR1RnZ0VDVYamUyRjBuallZOXR1RWYwMHB2MmZ1a1lrYmlzbVRhTVdmNkVoeHNqSTgrSGV5Y0p3Ym1WWm5ZWUE5ci9mKzNNb01kVWJydFF6SGJJRUR2ZFZTT1laS2RwSEJhNGg5V2lRc0pyVzMweFlUc2FncmFYdmdIM1JSaTdscU5KbmpQUExQZnZMMWxYNWdzb2JGUmJzK2NLUmE3Q0h1ZEkvTEpMWTNlTGVtNzlPZGVuSXhmZUx0YmMzak5TNUIvRHQ4YmExMHp6UDg3RllnWjlXQ2duZlREMThvdVJFdUdTUUhGSThmbVAyK2x3UXl1RlBCaEtFUUJidkVRaEhES1Q0YkNEZGhmZUtENjQiLCJtYWMiOiI4NzM0ZTE5NTZhM2MxOTIzOWU5OGMxZmZkMzk2ZmFmZmY5OTFiMGRiZjU0ZTBiMGMzZThkOTEzNDA0OTJhMWFlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:29:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZpS3B0MUJNNG05WjdtdStoWWM1TVE9PSIsInZhbHVlIjoiMFc2bFpUbEhORURFQkk4TnJTREVJdW42NnpSUEd6VVpzMVhUbEpYMGVBd09pMG9haitlNzZ0Yy9adDFaSUlyYkhXYjRRNE85WU55WktoUFNIZmtPUXEzbVIrNGdmaGh2QXlNbG9RcU1XeHY1ZDY2eEV0VzdFOVMxZXZ5UlhkdGV3b1dWcW1mSml4S1BTWUgxcEwzQVZDWGJvemtRREhsNTh0djQwOUx2djU0VWlFUmQ3NkhBb0Y1elRnYTBlTE83OUpINFI4R1N5ejYvakRNbTB4QkZSQ3N5SmR3N0U2SmczSWExL0pXQVNRb2tVd0E1NTlSLzlQOHdOQ3RreDRpTHl0WmhJT2lMVzczaFRmcHNmQ21meXpTa0ZQVGlpRnplSytoYkZYQkxqWG01RHJBVmNaMzFyUG9JOFVXT2FKZ0JNMldUNlBKemdJdGsyQ0xqdTFoa0VVYUQ3NUkrY0NiRW1aY2NzZS9ndWk0MXZ3UWlmVEhjZzkwcHJlbXAyTXAzWDloZU9QcDQreXZvcFNtZUNWTHRJbTBHSzZ0ZWorditCNTFLdjNGa3FPZUNFdWJROVRkbXVYaEpTK01NMXVCcWdiMXRKM3VTc0tvK3c0QXBhY09rd0NCOW9YaGlQOERCZ2JEQy9NbUNZZGkyZTAzRHdyc2Exa3ovMXdScmxQd3QiLCJtYWMiOiJiMjQ1ZmY3NTRlNDBhYjc2YjQyNWU4ZDM2NDFmZWQ2ZTNjNDY4ZDE1NzU1Y2QzMDJjMGM3OTNiMTY4YmJkZTU0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:29:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9hWmNGMWExN1NGK1BNTHVlTG1Rcnc9PSIsInZhbHVlIjoiTWdpdm9QWC8zb2t4QlQyQVQxWm9rRzIwazNsNzVPZmYyV3FBTWtKNHkxa29hbDBXdml1aUdXOE0yeUFtbTNsLzFHdFJGK000OUpqVW8yTit4RHB6ZitLWS9SQjRqTW1VejhXeDFmUmUycUZxUG4wcHBvU1FJZFZzenYwcmtxU084R2RheDlLNWczTHJSOVJjZkRwcGZ2dkNUcDR4LzdSUTlBL0JDS2FhRkQrMDYybVgydjJ3eVJRbVB2UHpZVytrcjlUeVVUQTBoN0V0bHArRUYraTVqRG5IRG1oUnMvb1JzeGlMRzlLVEU2c04raDBValR1RnZ0VDVYamUyRjBuallZOXR1RWYwMHB2MmZ1a1lrYmlzbVRhTVdmNkVoeHNqSTgrSGV5Y0p3Ym1WWm5ZWUE5ci9mKzNNb01kVWJydFF6SGJJRUR2ZFZTT1laS2RwSEJhNGg5V2lRc0pyVzMweFlUc2FncmFYdmdIM1JSaTdscU5KbmpQUExQZnZMMWxYNWdzb2JGUmJzK2NLUmE3Q0h1ZEkvTEpMWTNlTGVtNzlPZGVuSXhmZUx0YmMzak5TNUIvRHQ4YmExMHp6UDg3RllnWjlXQ2duZlREMThvdVJFdUdTUUhGSThmbVAyK2x3UXl1RlBCaEtFUUJidkVRaEhES1Q0YkNEZGhmZUtENjQiLCJtYWMiOiI4NzM0ZTE5NTZhM2MxOTIzOWU5OGMxZmZkMzk2ZmFmZmY5OTFiMGRiZjU0ZTBiMGMzZThkOTEzNDA0OTJhMWFlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:29:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZpS3B0MUJNNG05WjdtdStoWWM1TVE9PSIsInZhbHVlIjoiMFc2bFpUbEhORURFQkk4TnJTREVJdW42NnpSUEd6VVpzMVhUbEpYMGVBd09pMG9haitlNzZ0Yy9adDFaSUlyYkhXYjRRNE85WU55WktoUFNIZmtPUXEzbVIrNGdmaGh2QXlNbG9RcU1XeHY1ZDY2eEV0VzdFOVMxZXZ5UlhkdGV3b1dWcW1mSml4S1BTWUgxcEwzQVZDWGJvemtRREhsNTh0djQwOUx2djU0VWlFUmQ3NkhBb0Y1elRnYTBlTE83OUpINFI4R1N5ejYvakRNbTB4QkZSQ3N5SmR3N0U2SmczSWExL0pXQVNRb2tVd0E1NTlSLzlQOHdOQ3RreDRpTHl0WmhJT2lMVzczaFRmcHNmQ21meXpTa0ZQVGlpRnplSytoYkZYQkxqWG01RHJBVmNaMzFyUG9JOFVXT2FKZ0JNMldUNlBKemdJdGsyQ0xqdTFoa0VVYUQ3NUkrY0NiRW1aY2NzZS9ndWk0MXZ3UWlmVEhjZzkwcHJlbXAyTXAzWDloZU9QcDQreXZvcFNtZUNWTHRJbTBHSzZ0ZWorditCNTFLdjNGa3FPZUNFdWJROVRkbXVYaEpTK01NMXVCcWdiMXRKM3VTc0tvK3c0QXBhY09rd0NCOW9YaGlQOERCZ2JEQy9NbUNZZGkyZTAzRHdyc2Exa3ovMXdScmxQd3QiLCJtYWMiOiJiMjQ1ZmY3NTRlNDBhYjc2YjQyNWU4ZDM2NDFmZWQ2ZTNjNDY4ZDE1NzU1Y2QzMDJjMGM3OTNiMTY4YmJkZTU0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:29:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70182550\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1379178313 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379178313\", {\"maxDepth\":0})</script>\n"}}