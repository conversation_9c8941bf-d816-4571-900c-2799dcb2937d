{"__meta": {"id": "X4f9119df907b8310e27fe7449fd9441a", "datetime": "2025-06-06 20:40:31", "utime": 1749242431.018653, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242429.3852, "end": 1749242431.018688, "duration": 1.6334879398345947, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1749242429.3852, "relative_start": 0, "end": **********.816465, "relative_end": **********.816465, "duration": 1.431264877319336, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.816486, "relative_start": 1.4312858581542969, "end": 1749242431.018692, "relative_end": 4.0531158447265625e-06, "duration": 0.20220613479614258, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44760920, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.021389999999999996, "accumulated_duration_str": "21.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9120219, "duration": 0.01705, "duration_str": "17.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.71}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9616249, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.71, "width_percent": 5.283}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9689472, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 84.993, "width_percent": 4.535}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.992171, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.528, "width_percent": 10.472}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-510583376 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-510583376\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1625175714 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1625175714\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-165846197 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165846197\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1725343683 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=swfud1%7C2%7Cfwj%7C0%7C1983; _clsk=1jtjr1w%7C1749242371143%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikx5MEtpYW5XOHl5RHVGVXdubjJZY1E9PSIsInZhbHVlIjoiNTMzeUFaR29LNzJDU0ZYTDNONEZ0TjJ1ZnoxRGQxS25JN0tQRnJKekdZdjBWcjNYSEsrL015QkRNanZrZEx3eHRsa2hyNGlHKzhNQ3IzSDZ0dm82c1BITlhEcUx5VXBNblNsNk1vTWNVZTl4THl2Z1REcXR5SzlNZUwyUEdiZnlyT1RvL01HaXpqblVSeUsvVHNCWnBRU3l1MlN2eXIzcHZ3WmdsWW5KWjFFbW8zRU93dWt1VW1wOExtRE1wMGo3YmtJVFhNMS9nOG1rVXhUUVFiTGM4MkZ5OVNZUXdtbGIwMEVoc211UlI3b0grNSs3TUc3eGRSbUNGQmVSNHNWMHp1UGh6bFd3TTNncjNVbHBxR3RKTlYrcktJQ24yajVjK3R1dGNoTFdTUHhJWGdoV3ZIZE04NW4zNE1EWGtTTWlianIzYXAzNms5YUsxNXh2WkRjZXZoV2h1OFJ6aUFybXQ4K3lJajQ3NkNBTSt2NDFPR1NJemhuVTdXTjFLdVZTT096bzVlVU5PMjVaMUJDd3FkYjFMQzBNMU4wS2RsTHlnMkx0ZHV6azNEK1BtWVV4bWkvQzRaRmlUQzZLTmJMNXJwTFg3Wm9MK3ZCMmVaU0U4K2JGdkJYN3VleDh2cE9vZXlBWStUN1Y5L0RWMnNQK080S0laY2tTZG9KbjlVaVoiLCJtYWMiOiJkNTZjOWRiMGJkM2ViZDkwNGM1NjlhOTQ5MGMwYWU4MDQ2MmZkODhkMzFkNjFkM2I0NjVkMTRjYTVjMDZhMDBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitSd1NUSklKalIrV0xRMGNGdWwvTmc9PSIsInZhbHVlIjoieVpMVVpDK0lvUTIzSDZCQkJvU21sUVUybUlTTm83K0xMRFVsNlZQbEVqbUpjdnk0ZUxhMlhwTHRkVEJscktXYkx5TndQSWFMeU9mc1JNejRvM1E1YWZHbWdldENlLzJ0R1A4T25yeXVYNzhmNUMyRXoyZVpHTlNObzFjTEtEenVscDNpVi9jSXNpMEpmRFVWMFZraFZRWnBhekttNE5LTnh2cnc2aGRRZmdrWURuMmp6UElLRlFCUFpqYWFweTNEMHVKbFBXTDVaaDVSK2RQcUQrNG1kNGY2NnZKaUtMZCt5YkxUVVN1eWhORzlIYmFkcnVaOTFuK3pjUVJSU0JwZXhkTGJWT1NubUExWXlxeUd2cGNKRC9XY3RER3lnQnJxcjhWT3crbFo0eHNqV2VaS2xnRXZOdjJIb01pR2dTdXFrZU9iVXVWc2lzRnA2MUlnMk9NRXVRcDZmSk9SYmxvWlh0TFY3SkdzZFZYZmt3aVd0YktvV09ZOWJjc1dRRkFVeGxzU3NQZ091aU0xSWduUDdOcDBEdERaWVA2WWl6cmM5S1dmWkoxRXdVbjN1SGczdGNTRzQ0RHE2czdnWEJYa2twNmFidEZpeFRkRisveXRGd3k1VzBwQlNGWTA5akplTThRUHlid1RvV2RzQWw4Yy9DelpPbGhGYkVqcTIza0YiLCJtYWMiOiIzNmNjZGMwMGFkNGIxNjFhMmExMTgyOWZmNjJhNGUzMDBmMTA3YWM3MjMyNTRiMWI0YzllNDc5NTA1OWViMGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725343683\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-868392019 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EXNtVcDSCVpTrtQmjXbil1g1yDu1CX4tI4M8cWJU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868392019\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1518100591 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:40:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVxQkk2UENWNTVIT1BrR2cvYW1RMkE9PSIsInZhbHVlIjoiMXVkU0p2NVppQmJuSE5oTFg3eWw0QjZ4N3M1M2FuQjM0SU4vSzBjM3JSVDB3Y0hWQjBLTEExdVhKTmlJUzdMOVRMV3pBSnhnVytQZjE4Q2tlRmNLcnRJZGtSRHZBc1VzKzZ0ZmV6TFpQUXI2R293TVNHS3QzVXo4RkN4c2I4Vit5TmE2d0Z6Nkhtbjh0ZmdOZXQ3UTdBejBaUEdYL2pTZFVzSlp2ZnJiRUFwVUdoSTJqZzk5ZStUei9JOWJodzMzQldrUlBudkhsaXhYNnpCb0tQcnJneGNNcksyUlArSHVhbSsvTmdDK3RPdE1Pb3BSaUxEU2ZNTVR5SzlHMnpGa0RWays0bmVrOXV4SHFnOUxJWWorcGw2aTBmSFVNd1ZmTVhlN2g2WUx0UXBlM2hiQUQ5STFJcE9JMklKNXZ4RDRMSW5wTmpKcmlnKzlNREdmc3N3ek9kQStYQVZGVnZPZjFKeTB4UFpsNk16Tms0R054YlRvOVlzMmc3Zy9mNGFscC9BYlVCN3NHQWJCNDVRMjZjQ3E1cmNYWVFCUmlEM2lCb2tneWpQa295cTlSN3NxOC83dkpDdi9CWmQ0L2crc2xQUG1RMHI0QWpMc3dHY0NuTWMyK0ZmT2lZQkVkb2RTQjQvYlQyYmtnaFRvZGpYeklZNTNwanBQM0Z4VFlCelUiLCJtYWMiOiJjODMxNWJjYjYyYjVhYjk2ZGZkY2RlMTcyYzYyNzkwM2UzMTAzNGY2M2NlMTYxN2U3NjQ3MjNmZjY3ZTI4YTgyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpSeGJrR0RmejBtK3VqeTVKTUdlc1E9PSIsInZhbHVlIjoiQWdUeVl3b2RUSU4yeUZSdFNkV0w1THlNSGdDZXZzVE90RU40ekdFK2FvSG9WS3ZLT3F0T3dHWGxvN25xUTFvc0dCWW5uSURzYUxpSm56MVIwYmZwa2ZzZEdmcDVKS1QyNUNlU1pZeVdYbFlMVGxwY1ZUajNtN041RGJsOHB5czlBci9tMGloL2MrUTRWdEhiNS84SjdCZ1B6aVdtMHNlTEV3YjBjWi9SQWtFUTAzOURKREZRS1o4emgyeHBncitlQW9TazhCN3hRa3VWbFlDckFsQzJ5SFVySFB3N3Z2cWVrMjlvRnFaeVNtUFR4V3VRN1lRYmFhSWtjVktRS3ZSbWRraXlLd3E1NkZqckJZNEx5SFN0VEFON0l3OFJRc3dyZzlzZTR5c2FCM0xXUXk1amozK25GUERudjJCNGhPY0d2S2ZrOFF0dlF0QlVoc2sxRDkwOWpiM0FRK0N1d1krNCtFRzlvYzNDbFJPalFSdGd5a3VtSG56V2Z0N3lIQTkyMm9WM2ZyM1d6ZGQyT3lJY2E1cGttb21iUWZGcXpOZFgxMGw5NFJESksvdnNaM0dGQ1ArUklRYTFFcGhiMmgyckZvL2RHbThwWk05STIxVlRwUkpIQkhyY2VseUhZQWI5Z1puUW5BZXVLdlZBK25FQ2N0TnhiQ0dTUDY3U0o3cGEiLCJtYWMiOiJkYWE5ZmExMmRmMjg0MzMzZGJhNzNmZWI5MGYzYjk3MWFlOTI2MWQzZTY0NzBmZGIyODIxOWI1MDQ0YTQwYjg4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:40:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVxQkk2UENWNTVIT1BrR2cvYW1RMkE9PSIsInZhbHVlIjoiMXVkU0p2NVppQmJuSE5oTFg3eWw0QjZ4N3M1M2FuQjM0SU4vSzBjM3JSVDB3Y0hWQjBLTEExdVhKTmlJUzdMOVRMV3pBSnhnVytQZjE4Q2tlRmNLcnRJZGtSRHZBc1VzKzZ0ZmV6TFpQUXI2R293TVNHS3QzVXo4RkN4c2I4Vit5TmE2d0Z6Nkhtbjh0ZmdOZXQ3UTdBejBaUEdYL2pTZFVzSlp2ZnJiRUFwVUdoSTJqZzk5ZStUei9JOWJodzMzQldrUlBudkhsaXhYNnpCb0tQcnJneGNNcksyUlArSHVhbSsvTmdDK3RPdE1Pb3BSaUxEU2ZNTVR5SzlHMnpGa0RWays0bmVrOXV4SHFnOUxJWWorcGw2aTBmSFVNd1ZmTVhlN2g2WUx0UXBlM2hiQUQ5STFJcE9JMklKNXZ4RDRMSW5wTmpKcmlnKzlNREdmc3N3ek9kQStYQVZGVnZPZjFKeTB4UFpsNk16Tms0R054YlRvOVlzMmc3Zy9mNGFscC9BYlVCN3NHQWJCNDVRMjZjQ3E1cmNYWVFCUmlEM2lCb2tneWpQa295cTlSN3NxOC83dkpDdi9CWmQ0L2crc2xQUG1RMHI0QWpMc3dHY0NuTWMyK0ZmT2lZQkVkb2RTQjQvYlQyYmtnaFRvZGpYeklZNTNwanBQM0Z4VFlCelUiLCJtYWMiOiJjODMxNWJjYjYyYjVhYjk2ZGZkY2RlMTcyYzYyNzkwM2UzMTAzNGY2M2NlMTYxN2U3NjQ3MjNmZjY3ZTI4YTgyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpSeGJrR0RmejBtK3VqeTVKTUdlc1E9PSIsInZhbHVlIjoiQWdUeVl3b2RUSU4yeUZSdFNkV0w1THlNSGdDZXZzVE90RU40ekdFK2FvSG9WS3ZLT3F0T3dHWGxvN25xUTFvc0dCWW5uSURzYUxpSm56MVIwYmZwa2ZzZEdmcDVKS1QyNUNlU1pZeVdYbFlMVGxwY1ZUajNtN041RGJsOHB5czlBci9tMGloL2MrUTRWdEhiNS84SjdCZ1B6aVdtMHNlTEV3YjBjWi9SQWtFUTAzOURKREZRS1o4emgyeHBncitlQW9TazhCN3hRa3VWbFlDckFsQzJ5SFVySFB3N3Z2cWVrMjlvRnFaeVNtUFR4V3VRN1lRYmFhSWtjVktRS3ZSbWRraXlLd3E1NkZqckJZNEx5SFN0VEFON0l3OFJRc3dyZzlzZTR5c2FCM0xXUXk1amozK25GUERudjJCNGhPY0d2S2ZrOFF0dlF0QlVoc2sxRDkwOWpiM0FRK0N1d1krNCtFRzlvYzNDbFJPalFSdGd5a3VtSG56V2Z0N3lIQTkyMm9WM2ZyM1d6ZGQyT3lJY2E1cGttb21iUWZGcXpOZFgxMGw5NFJESksvdnNaM0dGQ1ArUklRYTFFcGhiMmgyckZvL2RHbThwWk05STIxVlRwUkpIQkhyY2VseUhZQWI5Z1puUW5BZXVLdlZBK25FQ2N0TnhiQ0dTUDY3U0o3cGEiLCJtYWMiOiJkYWE5ZmExMmRmMjg0MzMzZGJhNzNmZWI5MGYzYjk3MWFlOTI2MWQzZTY0NzBmZGIyODIxOWI1MDQ0YTQwYjg4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:40:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518100591\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1974730437 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iTJurukWpbgdqGe50npdM3kzHf053ryUr0sV4Z7v</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974730437\", {\"maxDepth\":0})</script>\n"}}