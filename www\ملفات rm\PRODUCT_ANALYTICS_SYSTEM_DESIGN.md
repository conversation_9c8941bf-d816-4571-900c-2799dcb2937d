# 📊 نظام تحليل أداء المنتجات المتقدم

## 🔗 **العلاقات المكتشفة في قاعدة البيانات:**

### **الجداول الرئيسية:**
```
📦 product_services (المنتجات)
├── id, name, sku, sale_price, purchase_price
├── quantity, category_id, unit_id, expiry_date
└── created_by

📂 product_service_categories (الفئات)
├── id, name, type, color
├── warehouse_ids (JSON)
└── show_in_pos

📏 product_service_units (الوحدات)
├── id, name
└── created_by

🏪 warehouse_products (مخزون المستودعات)
├── id, warehouse_id, product_id
├── quantity
└── created_by

🛒 pos_products (منتجات المبيعات)
├── id, pos_id, product_id
├── quantity, price, tax, discount
└── description

🏢 warehouses (المستودعات)
├── id, name, address, city
└── created_by
```

### **العلاقات:**
```
product_services → product_service_categories (category_id)
product_services → product_service_units (unit_id)
product_services → warehouse_products (product_id)
warehouse_products → warehouses (warehouse_id)
pos_products → product_services (product_id)
pos_products → pos (pos_id)
```

---

## 📈 **التحليلات المطلوبة:**

### **1. 📊 تصنيف المنتجات حسب الفئات:**
- مشروبات (مياه، عصائر، مشروبات غازية)
- ألبان وأجبان
- لحوم ودواجن
- مواد تموينية (أرز، سكر، دقيق)
- حلويات ومقرمشات
- منظفات ومستلزمات منزلية
- خضار وفواكه

### **2. 📈 تحليل حركة المبيعات:**
- المنتجات الأكثر مبيعًا (Top Sellers)
- المنتجات الأقل مبيعًا أو الراكدة
- التغيرات الموسمية في المبيعات
- تقارير أسبوعية/شهرية للكميات المباعة

### **3. 🔄 تحليل دوران المخزون:**
- دوران المخزون = إجمالي المبيعات ÷ متوسط قيمة المخزون
- معدل دوران عالي = المنتج يتحرك بسرعة
- معدل دوران منخفض = المنتج لا يُباع بسرعة

### **4. 🕒 تقييم المنتجات القريبة من الانتهاء:**
- تتبع المنتجات التي تقترب من انتهاء الصلاحية
- استراتيجيات التصريف (خصومات، عروض)

### **5. ⚖️ مقارنة العرض والطلب:**
- منتجات مطلوبة كثيرًا ولكنها غير متوفرة
- منتجات مخزنة بكثرة ولكن لا تُباع

### **6. 📦 تحليل الخسائر والتلف:**
- كمية المنتجات المتلفة
- أسباب التلف (انتهاء صلاحية، سوء تخزين)
- طرق تقليل الفاقد

### **7. 📅 التنبؤ بالمستقبل:**
- توقع حجم الطلب في المواسم القادمة
- المنتجات المتوقع زيادتها أو انخفاضها

---

## 🛠️ **التصميم التقني:**

### **الاستعلامات المطلوبة:**

#### **1. أفضل المنتجات مبيعاً:**
```sql
SELECT 
    ps.id, ps.name, ps.sku, psc.name as category_name,
    SUM(pp.quantity) as total_sold,
    SUM(pp.quantity * pp.price) as total_revenue,
    COUNT(DISTINCT pp.pos_id) as order_count,
    AVG(pp.price) as avg_price
FROM product_services ps
JOIN pos_products pp ON ps.id = pp.product_id
JOIN pos p ON pp.pos_id = p.id
LEFT JOIN product_service_categories psc ON ps.category_id = psc.id
WHERE p.created_by = ? AND p.pos_date BETWEEN ? AND ?
GROUP BY ps.id
ORDER BY total_sold DESC
```

#### **2. تحليل دوران المخزون:**
```sql
SELECT 
    ps.id, ps.name, ps.sku,
    wp.quantity as current_stock,
    COALESCE(SUM(pp.quantity), 0) as total_sold,
    ps.purchase_price * wp.quantity as stock_value,
    CASE 
        WHEN wp.quantity > 0 THEN COALESCE(SUM(pp.quantity), 0) / wp.quantity
        ELSE 0 
    END as turnover_ratio
FROM product_services ps
LEFT JOIN warehouse_products wp ON ps.id = wp.product_id
LEFT JOIN pos_products pp ON ps.id = pp.product_id
LEFT JOIN pos p ON pp.pos_id = p.id
WHERE ps.created_by = ? AND (p.pos_date BETWEEN ? AND ? OR p.pos_date IS NULL)
GROUP BY ps.id, wp.quantity
```

#### **3. المنتجات الراكدة:**
```sql
SELECT 
    ps.id, ps.name, ps.sku, psc.name as category_name,
    wp.quantity as current_stock,
    ps.purchase_price * wp.quantity as stock_value,
    DATEDIFF(NOW(), ps.updated_at) as days_since_update
FROM product_services ps
LEFT JOIN warehouse_products wp ON ps.id = wp.product_id
LEFT JOIN product_service_categories psc ON ps.category_id = psc.id
LEFT JOIN pos_products pp ON ps.id = pp.product_id
WHERE ps.created_by = ? AND pp.id IS NULL
ORDER BY stock_value DESC
```

#### **4. المنتجات القريبة من الانتهاء:**
```sql
SELECT 
    ps.id, ps.name, ps.sku, ps.expiry_date,
    wp.quantity as current_stock,
    DATEDIFF(ps.expiry_date, NOW()) as days_to_expiry,
    ps.purchase_price * wp.quantity as potential_loss
FROM product_services ps
JOIN warehouse_products wp ON ps.id = wp.product_id
WHERE ps.created_by = ? 
    AND ps.expiry_date IS NOT NULL 
    AND ps.expiry_date <= DATE_ADD(NOW(), INTERVAL 30 DAY)
ORDER BY days_to_expiry ASC
```

---

## 📊 **واجهة المستخدم المطلوبة:**

### **1. لوحة المعلومات الرئيسية:**
- إجمالي المنتجات
- إجمالي قيمة المخزون
- المنتجات الأكثر مبيعاً (أفضل 10)
- المنتجات الراكدة
- تنبيهات انتهاء الصلاحية

### **2. تبويبات التحليل:**
- **تحليل الفئات:** أداء كل فئة منتجات
- **دوران المخزون:** معدلات الدوران لكل منتج
- **المنتجات الراكدة:** قائمة المنتجات غير المباعة
- **انتهاء الصلاحية:** المنتجات القريبة من الانتهاء
- **التنبؤات:** توقعات المبيعات المستقبلية

### **3. الفلاتر:**
- فلتر المستودع
- فلتر الفئة
- فلتر التاريخ (من - إلى)
- فلتر حالة المنتج (نشط، راكد، منتهي الصلاحية)

### **4. الرسوم البيانية:**
- رسم بياني لأداء الفئات
- رسم بياني لدوران المخزون
- رسم بياني للمبيعات الشهرية
- رسم بياني للتنبؤات

---

## 🎯 **المؤشرات الرئيسية (KPIs):**

### **مؤشرات الأداء:**
- **معدل دوران المخزون:** عدد مرات بيع المخزون سنوياً
- **قيمة المخزون الراكد:** قيمة المنتجات غير المباعة
- **نسبة المنتجات النشطة:** المنتجات التي تُباع بانتظام
- **متوسط فترة البقاء:** متوسط الوقت من الشراء للبيع

### **مؤشرات الربحية:**
- **هامش الربح الإجمالي:** (سعر البيع - سعر الشراء) / سعر البيع
- **العائد على الاستثمار:** الربح / قيمة المخزون
- **أفضل المنتجات ربحية:** المنتجات ذات أعلى هامش ربح

### **مؤشرات المخاطر:**
- **قيمة المنتجات منتهية الصلاحية:** الخسائر المحتملة
- **نسبة المخزون الراكد:** المنتجات غير المتحركة
- **معدل التلف:** نسبة المنتجات المتلفة

---

## 🔧 **خطة التنفيذ:**

### **المرحلة الأولى: البنية الأساسية**
1. إنشاء كونترولر ProductAnalyticsController
2. إنشاء الواجهة الأساسية مع التبويبات
3. تطبيق الفلاتر الأساسية (مستودع، تاريخ)

### **المرحلة الثانية: التحليلات الأساسية**
1. تحليل أفضل المنتجات مبيعاً
2. تحليل المنتجات الراكدة
3. تحليل دوران المخزون

### **المرحلة الثالثة: التحليلات المتقدمة**
1. تحليل الفئات
2. تحليل انتهاء الصلاحية
3. مقارنة العرض والطلب

### **المرحلة الرابعة: التنبؤات والذكاء الاصطناعي**
1. التنبؤ بالمبيعات
2. توصيات إعادة الطلب
3. تحسين المخزون

---

## 📋 **الملفات المطلوب إنشاؤها:**

### **الكونترولر:**
- `app/Http/Controllers/ProductAnalyticsController.php`

### **الواجهات:**
- `resources/views/financial_operations/product_analytics/index.blade.php`
- `resources/views/financial_operations/product_analytics/category_analysis.blade.php`
- `resources/views/financial_operations/product_analytics/inventory_turnover.blade.php`

### **النماذج (إذا لزم الأمر):**
- `app/Models/ProductAnalytics.php`
- `app/Models/InventoryTurnover.php`

### **الطرق (Routes):**
- إضافة طرق جديدة في `web.php`

هذا التصميم يوفر نظام تحليل شامل ومتقدم لأداء المنتجات مع مراعاة جميع العلاقات في قاعدة البيانات والمتطلبات المحددة.
