{"__meta": {"id": "X013e3f3b329fc79cb6800ce55c39fff3", "datetime": "2025-06-07 07:30:13", "utime": **********.403325, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749281412.403088, "end": **********.403355, "duration": 1.0002667903900146, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1749281412.403088, "relative_start": 0, "end": **********.26017, "relative_end": **********.26017, "duration": 0.8570818901062012, "duration_str": "857ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.260186, "relative_start": 0.857097864151001, "end": **********.403359, "relative_end": 4.0531158447265625e-06, "duration": 0.1431729793548584, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44806320, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.026489999999999996, "accumulated_duration_str": "26.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.314364, "duration": 0.023129999999999998, "duration_str": "23.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.316}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.353905, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.316, "width_percent": 3.964}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3749158, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.28, "width_percent": 4.794}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.387917, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.074, "width_percent": 3.926}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1081480528 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1081480528\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2076802541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2076802541\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1122142037 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122142037\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-326487833 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=tsr4t3%7C1749281407057%7C3%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlM5K3B3NU5mc05VblAvM2ZCd0gzY1E9PSIsInZhbHVlIjoiRVpuODFCUDB2OWlYbUQxcGlqR3kxOU1EUUVnblZNb0ZKV045aTVTekdmbmVFdlZNcFJHME1NbzFjb1ZiWm9zdHlnR2V4VUhEVzZ3YkJQZWdOU2ZwZS9OdlpBYms3L08wYU9IeUlLbE56azU1L21DYWpmWjBNVTdOdHkzWitLYlFEeXdiZGRGRzNZSWI3QVhsRWt2UXN1Nlg1NEdXaFJqeElxSnJzdENBYjFyZGx6WlA1d0ZBN0h4WXpEVlRkS0xLOXlzRVFoaFJMdmJ4RVlpVEV4M1poYk5zQmRDcGxDOUg4dlVPMm94cUF3ejQyYWxuVmpSa2xmWVpZYWZDZWFIY3ZwaXFRNVpCUHVPYWNGM0ZjenJJcEhvcnlaQkdQNlJvNnF6citCMzJmYkE0VkZTbnRqUTI2SDRkVVpEV2V3RHYwRGJPQlVsVDZ6ZHg0d1gwaHMzeU5kWEhIZlRLUDd3bmNEVUJYNmNQSm1lS3lMNGIwbTlsazlwQzBhRitTUkpUMFY0Y2lKVFdoSTV5cjZ6b0I3c3B0ejNEcnM2MlQxTnVuUVBRM3Vvb0xxZjRDNHNSajUzQlNTbEZKcjFtSDVBMUtEdWxFVlQzbmZVVTNKcjJvMGxVL1ljUXU5U3Z4RFgySGhxZ1lLWGtXcmxJai9iN1cvN1h2d3FvSXM1TFlXNTQiLCJtYWMiOiI3ZDkyODJjZjQyY2Y5ZjY0MzVkNWViZGM1MWIzMWYyMjRiNjFiZDI2OWY4NDA3YWFkMDg2NGZjYmYxOTdiNWZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVaS1QzTEFLRE4rTHgvRmQwdTZ1dkE9PSIsInZhbHVlIjoiakJkNkFQNVV4a2hIN3dhY1NsWnZrSStvaWk4ODdRdEc3N1h4UUdUbFlkUW1FU04yWEozQU5Fc20rZTExY3E5NTlIYkFoblhPRGVIc1pNaU5wWVUvSkJORUwzcUIwNHRXd0YzLytPaC9oTEVBSVF4YVduR0NGWWR1YU12K05PVDNvcjgyalMrckFFSnFoRFR0SXpubE9ZWjRweTJWb00rak1JWjVJdDJSaFV1NCtwbU82ZndrZkZMYUhhai93bE0xUVVUMzcwY2pNVzhpKzVBYmlmUHFLeExMU3hXeDBvTkhUMmZxaGRZWDVCSEFSdGFrd2phUnl3MVdrTjN6ZWJxWHUwUXFuQld6NW44anhXYkNGVkFsRXFIZmwveTRmQ1ZGQ1JhSkR4S1hQc0V1VDlVVk5ZWkRxZEtma2cvaFp5RE0yMk9qcGpEWXlNUjZNcUNjekk4Z3NkSW56WlVGV3RIeFdUSnhudEFWU29sclRrc3pjcGEyRTI0VDlLSThCUlJLdGpVK3JrYnAxN3FXa2h1SGo1NTNJNTRqbUhDMVBQSzNYdEROTU5Sc1JKK0RqRVZsc0dSYlpvSzRGbEJIRmxWZ3hZKzY3ekVPSlI5cjg1Y2pzRlBDZlpzb1djL2prbytIUkMwMGx3SDZCc0Y1UkFqT0FHVlNMYWY3S1lhbXVWUE8iLCJtYWMiOiJiNmM5YTdlNzVkMTk0NzUzMWE3MTRiMGZkMzEwNmM2MTZmOWIxNjFhZWM3NGEwOGM3NzFmNjViYmY1ODVkZDc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326487833\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1498591286 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YNJBgXOqtFU4b8z8Vx4XcGRnEfxLj9OGGtpNz79H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498591286\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1519539128 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 07:30:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjY3cEI4UURCQXhOZ2VqRWtXTlRjSWc9PSIsInZhbHVlIjoiR0xiUTF5WTl5bVRlSk1pSUNYZTFTcUlSQll1NUtXM0JxckxXMkhmM3FOdzlOZGFiclE4L1EzcDNWdStWZ09YNmdJNzhjQ2FlMFFFOHA4V2VBYlE0elJ3L252NEZKUEoyKzVHMlNua3FOQmVPQldoako1NUhtNDZteit0U2ZQSXkvKytNT1RybnFKSTFTRnkzUVpQY0Fja3NIZ3BzZkF5aXczNXE5cVVrYnJnWStOdFhQYU9DSC9xWGJzd09xNEdkYW5UTTFwNmZ3MjBybUtMNE5mcUs3cmtPRHdManpOdmRZRHh0cWMvRlFLRVFaRkhHZFh3bDlMSkFmRnVyYVp1dVRFd0NrZDBYT0VGUzRERGluaGFKVHpRSXJjREdUK2FkOWNzRU5nZzlwQTg5c21YenptcVJZdjh1Ym1UTGcyN2dDMFZxMEh4NjE0U3I1U3lyaW9CUW5pRG9RaWZ6cjFncFBoQ1VEMkppazVHSVBRSExkOWl4NUVXVHkzRnBFcXZ4QXlYTDR5NFMvNHlXaUR3Wjg2SG9SR2l0OHgyZVhOOE1Vb2N3MU5QeGJOSjFTWERTWVUxQnh6ZEhVY20zNjFPK2kxVW5PSzdpNjZoOW9WNDlXOEg1RVdWMEh6YVhBVjBPaWluUndSQzAwcHBzSmFseXRpRGRkamd3UVFkWkdXQTIiLCJtYWMiOiJhNzUwN2VkYjUxOTZhOTFmY2ZjNjcwMTkyY2NiOGUyZTk5MjZkZThmMmM4NWZmNmJjYzUwMzRkMDQyMzgxZjZiIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imd4dVl0L0UrVG5TdUwrUHpLa05BMFE9PSIsInZhbHVlIjoib2FhRDlySXdGOXBKc01Pbnh1UlFPV1NLbFJKRXFJZ1RvYkxlQjlEMlBIdGIvMTUxcG05cnlGN2F1U0dTQ09lMVA1TUlBenpuNmorY3pPVUJoNE9NbU9LTEVHTlIvSHFDdzZYYTFoY3B6R3cyVWF4OERxeDJ3a25Kb013OUxPWEhWbW82cDV6RUthU1pKemFPelZIblcwYTNjMTdZOWRyNGJ5c2Q4bEtSUnkyN0todnY2YytITUFkeFRJdjhGVGx0OCtLMHFFYXBOcnhLY2ttZkFoa2V6V3FySTlvak9PTkVoUzRjVFhLL0ppT0xWbDR1Z1BRVWQ2MzNySS92SGFOVHg3MFdGcThzdGhvdWpBYjlFR2JKTFh0QUdMcnpzbGgrMmVsUC9qdmRSLzVSS2srWmJhNTczNlAxOWtpbDFkOGxNMVJBU25LbUxwUTFtRUhDOTlsU3ZRUi9CcWp3Y1FSWUlWVEVsd251Qnl2clZGcmdmT3NjUVJ0R29DNWxHemdQY3JKSTVnSlRLRktFUWduZGdhNGRFY0JxM1YvUzcyQ2ZXUk4wN3pyTmJRL2JFdjU3QlBRRVZsaHNwcWt1a3VZVjlIY0pMd1ZrMTBTK2x6cmNVYUt0eExEWFlGNlVDU1kvVm1KaThSSVkrWHFqM2t4d29qMmlYVnBCcjNLbDcwUWoiLCJtYWMiOiJhM2UwOGFiZGZjOWY0NTdlYzc5ZGUzYmQ1Y2E0Mjk1OWMzN2VkZGRhMDNmZTRmMDYyMzA3MmM3NmIwNjQyNTljIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 09:30:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjY3cEI4UURCQXhOZ2VqRWtXTlRjSWc9PSIsInZhbHVlIjoiR0xiUTF5WTl5bVRlSk1pSUNYZTFTcUlSQll1NUtXM0JxckxXMkhmM3FOdzlOZGFiclE4L1EzcDNWdStWZ09YNmdJNzhjQ2FlMFFFOHA4V2VBYlE0elJ3L252NEZKUEoyKzVHMlNua3FOQmVPQldoako1NUhtNDZteit0U2ZQSXkvKytNT1RybnFKSTFTRnkzUVpQY0Fja3NIZ3BzZkF5aXczNXE5cVVrYnJnWStOdFhQYU9DSC9xWGJzd09xNEdkYW5UTTFwNmZ3MjBybUtMNE5mcUs3cmtPRHdManpOdmRZRHh0cWMvRlFLRVFaRkhHZFh3bDlMSkFmRnVyYVp1dVRFd0NrZDBYT0VGUzRERGluaGFKVHpRSXJjREdUK2FkOWNzRU5nZzlwQTg5c21YenptcVJZdjh1Ym1UTGcyN2dDMFZxMEh4NjE0U3I1U3lyaW9CUW5pRG9RaWZ6cjFncFBoQ1VEMkppazVHSVBRSExkOWl4NUVXVHkzRnBFcXZ4QXlYTDR5NFMvNHlXaUR3Wjg2SG9SR2l0OHgyZVhOOE1Vb2N3MU5QeGJOSjFTWERTWVUxQnh6ZEhVY20zNjFPK2kxVW5PSzdpNjZoOW9WNDlXOEg1RVdWMEh6YVhBVjBPaWluUndSQzAwcHBzSmFseXRpRGRkamd3UVFkWkdXQTIiLCJtYWMiOiJhNzUwN2VkYjUxOTZhOTFmY2ZjNjcwMTkyY2NiOGUyZTk5MjZkZThmMmM4NWZmNmJjYzUwMzRkMDQyMzgxZjZiIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imd4dVl0L0UrVG5TdUwrUHpLa05BMFE9PSIsInZhbHVlIjoib2FhRDlySXdGOXBKc01Pbnh1UlFPV1NLbFJKRXFJZ1RvYkxlQjlEMlBIdGIvMTUxcG05cnlGN2F1U0dTQ09lMVA1TUlBenpuNmorY3pPVUJoNE9NbU9LTEVHTlIvSHFDdzZYYTFoY3B6R3cyVWF4OERxeDJ3a25Kb013OUxPWEhWbW82cDV6RUthU1pKemFPelZIblcwYTNjMTdZOWRyNGJ5c2Q4bEtSUnkyN0todnY2YytITUFkeFRJdjhGVGx0OCtLMHFFYXBOcnhLY2ttZkFoa2V6V3FySTlvak9PTkVoUzRjVFhLL0ppT0xWbDR1Z1BRVWQ2MzNySS92SGFOVHg3MFdGcThzdGhvdWpBYjlFR2JKTFh0QUdMcnpzbGgrMmVsUC9qdmRSLzVSS2srWmJhNTczNlAxOWtpbDFkOGxNMVJBU25LbUxwUTFtRUhDOTlsU3ZRUi9CcWp3Y1FSWUlWVEVsd251Qnl2clZGcmdmT3NjUVJ0R29DNWxHemdQY3JKSTVnSlRLRktFUWduZGdhNGRFY0JxM1YvUzcyQ2ZXUk4wN3pyTmJRL2JFdjU3QlBRRVZsaHNwcWt1a3VZVjlIY0pMd1ZrMTBTK2x6cmNVYUt0eExEWFlGNlVDU1kvVm1KaThSSVkrWHFqM2t4d29qMmlYVnBCcjNLbDcwUWoiLCJtYWMiOiJhM2UwOGFiZGZjOWY0NTdlYzc5ZGUzYmQ1Y2E0Mjk1OWMzN2VkZGRhMDNmZTRmMDYyMzA3MmM3NmIwNjQyNTljIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 09:30:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519539128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1781049213 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zWZwfcUELIVstpAUF4axAurx5oJnVna9QRgpIxn6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781049213\", {\"maxDepth\":0})</script>\n"}}