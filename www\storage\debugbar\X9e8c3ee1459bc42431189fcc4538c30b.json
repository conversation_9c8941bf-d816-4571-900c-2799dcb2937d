{"__meta": {"id": "X9e8c3ee1459bc42431189fcc4538c30b", "datetime": "2025-06-06 19:34:43", "utime": **********.634713, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238482.220069, "end": **********.634749, "duration": 1.414680004119873, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749238482.220069, "relative_start": 0, "end": **********.428453, "relative_end": **********.428453, "duration": 1.2083840370178223, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.428474, "relative_start": 1.2084050178527832, "end": **********.634752, "relative_end": 3.0994415283203125e-06, "duration": 0.20627808570861816, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.009829999999999998, "accumulated_duration_str": "9.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.540761, "duration": 0.00596, "duration_str": "5.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.631}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5778892, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.631, "width_percent": 8.24}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.587859, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 68.871, "width_percent": 12.716}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6074228, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.587, "width_percent": 18.413}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-433595030 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-433595030\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1075525633 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1075525633\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1083435677 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083435677\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2051748714 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238462862%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InprUWVETzdwaXB1NmdmNnQyN1UvUmc9PSIsInZhbHVlIjoiSEUzTmpVeTY1TTlUZ0Uxalo5SzV3ZGpYZmtFSTBnMEpBMEpEY1RSZUJGaXhQbmQwaFRia1Noem1aaGhuQ3EvbXdzdXRKNjFKWHArV0xIZjhnMjZvRU5LdW5NVDFINEEzdnY4N3pUU2pET2tncUZXWGZuWk1qNjRJeXUyQ3AzTVVnNXJlWTZIMmtrV0ZxWi9MMWZYc3VweUkvL3hWaEdXNnNUaTZka1NTdVJ1VlN4RnZJZHFLb2Zjemo1SkhOeFpFNVRjOTd5N01ibDd1RmRtZnRxMlN2VFdOcCtkTW1idkR3UE4xcmpZakM1KzRZMG9sUHRoZTVDSlpNYjc5TWJhU2VHVDh0SStpdytlYmFqcEJ0NjR4eSttaVJwazV4V0pTL2xkV1FtLytEVFhOVlIzbDZRc1lNZy96d1Bna1JsTzA4R1A5UEVHZTl4MWxZOVh6cUo1SStNSURQalVPM1lhaGhxOElPNDY0V3JvM0huWm95ZE9EN05TUmdZRTZ3bVFJU1lPT0p0QzZMRklIUHNHRnlVOThsS0NjL2Z2QnhrZTlNaklrRE1FWkg5NVdKQmdTbjBiNFFRQUlBZHU5UnFTV0ZtUGhFUDF1ajJzblVjUDNZT2lLQlZjOVFScFdrZGFrWVdHZTg1Mlpxay93SkczR1VLQmRpK2RWWG9xbmlOTmsiLCJtYWMiOiJkZGFkZTllYjYzNGY2ZGM2NTVkNTAzMGYyZjc1YmNhM2I1MTk4NDU2MjgyODY0MDk1ODdhY2Q3YWJjOTZkZDRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhMSjkwYkVtVW5UUnFSNVovYWZMVHc9PSIsInZhbHVlIjoiSldSbVNzcjU5Y0tqTWNVQ00renkxUjlsblRoYmlsK2lXOGZUTG5sUmZzV0N6V0FXeG8xMEFSUnpjY2V5Y1NMOTZPeVJ5YS9lZ1RQMHBIb3Z3YlBTUkFsUndFTjhzR2lXOFRCNTNFa00wakNGaE0zZ0wyeVlETktSbGR2UFRjRVNnRGJEUnlXcTErUHNxcFFtTWgrUEdVVnVMdUlyK0duczN3VjZObGtaalRLQU0xZ1EwRzNnQzIrVFp2djNwWXR1RHpyRUZDT0MvZFBDclA1bUYzODhuWWFGLzZobnltV1ZCMW5TRG94MWxXZjFPS3VJTHovOVZEVi9ETGR5RjRwVVNZV3BkRU5mUWlFUjdtS1dTVDFsd1RXQnNpbXg0dVBKN1l4cjJKOWFsSlBlOUh6ZWM3eHRoMWZBSGVOMkVsdTNucWRkTVdYTnZsMW5WOFpXdndWNG16cHZOSHlneXI1YkRjSGhKNHlGRFp1dy8xSUJ0SkRPNjhTc1ZWaGtDT0ZPZGgyREM4T1E1QXJiWVExNklscTdQM1AzWkZidWNHNzlWNmYxRFBpWWRha0lDNzlFcCsxbHQ5K2xqdlNMT0MzVUtaYWkrcFZKaU8zelJuM2RreWEzbnRmSTRjM0kvMWRYcHFDRlAwbnBIRDVhMVB2bEdFUVNyZy9mWmN6V0lIMHAiLCJtYWMiOiI0YzIzYzg3OWQxNDkwMTdlY2ExYzZjZTlhODU5Y2EwZjcyNWQ4ZmFkNTVmNDc3NTIzY2IyZmEwNGU4ZGE0ZmRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051748714\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-517731185 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517731185\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2101565413 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:34:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlIwd0pnWXNaT0lmZjRUVSt1bEE4ZEE9PSIsInZhbHVlIjoiUVRQa25leXB4VFN3cnM2YVNJY2ZPTU5UMGJ5cjVIWm9MTDgwemFLVmM5RmVGdzRJaHZzL3hKYnVYRGtBZFpaZEE1RTB5VDF0cWoxL1B3RUFQUFZIR3VDYkx6WWlYTllTSXhpbXNSQnY1TkFkckdHbXRFSHVVcEQzSURSNkwrWW1XOGJ5ZU9Mdm1BaXhxMTloMm9qSG5YK0ZSTE9XMFl1Z2lHU21IT2t4QnExNExDbUNpcDJDQ3dodS8wSFoxeHd2M0FkeHZCMHhQWllSL3l4ZzRhTjNVRFhRMnc3NEZUT28zSExnd1V6TVV3NkUra2NCVDd6NnlXOWoyek5vUyt2QU9iTWRTcnoxbWFnVkJra0tkSk50M1FYb3JvZUxZTFA0MjV6U3Z2dTRDbk5ubTNCVVFCMU1wdjhOUWZidkx6SVFhOE51ZmNickFGdnE5NjNwbzJsZSswV0RWV0ZUMjg0K2g5WnVpOEIxL2NsMVNBdXFGS25KWWsvUGt1Nk9TVDQwa04ySitVZzU0b3FyTFh5citCajdmekc5UHlHT0I5UmVPQ0x1dzd2aFk3dXNERUszK29BckovRjR6bVpEU2g2dkFHaWhWNGFnTEcvN1JjeEcrWXJwSmt1NURjZnN1VU5RVHJ3Vkwwdm4yZWMwd3Y1MnhDTHdWakhEWUc2OGQ1WC8iLCJtYWMiOiI3NTRiOTM1NzFjOGIxZmM1ZWY0MGNiZTM3MDFhOGYxZDdkMWI5YzU5MGZjMWUwMmEwODBlMWFmNDljOTFhYTllIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkgweHA1T1krY3ZaNGcrQ1VFa0E2UVE9PSIsInZhbHVlIjoiS2ZYVFdKQ29PbE56YlZXS0xlQnlOOHNmelpJcjdzN2xrdmtDcnFJSGFwNTJvM1lZdC81RmltY0tJS3ViWUUwYVhJWUhDM3duR0c5dEh0a0pqZlczdUlvRUZLaW9iZUt1V2toTFluZ0pzOWFKZnlWSEwwS3lSTUVYMzNPdzFMZXRsVkJLZ1RCUUN3djFwUmloRE5PU1ZXZnNobnYrQWgrNy8vZGpUQmRrTjlPTVVQTWd2UU5UYWtCUVJ5NzY2ZHdsYmIxaVdkUnU3eDdWM21wMVozQ1JCVDdKU3p5b3E1V3hTZzJBanI5VkhOSGxQV1ZWajgrdVFtdUdZL2pDOXJodk82M0UrSWNBZVp1R3VpMVpkdWNsV3dkKzI2a29IN0ZrdENZR3lJV3NRbXdGa3JDbGlHd0xkN0c4bkRHczd0dDV0MEltL24xZStJRUMwNEN5RDJZUVRGQXU0LzRHSDE0QW9ETnlNcTljSkZKNDRxR0FFSVRNMnh3dEZ4aEZmbUJHZXZ3Zy9CVW1RcTVXOEtFTWZ2b0ZjaG1IcUh2N1JwMWUrenRwUzNpdG8rcVhOWVE3QlpYaksxNGVXbTJyVkVobVN0bEo1ZzFRbXJZOXRpazlHdFZhYm1tZllLYXNjWGFjNVdLaExoVjNqTHdwZFp3WXM5c2ZLZmZYdGM1VzZUazIiLCJtYWMiOiJhNDc3MjdjM2VlYTgzMWZkZDA2ZjIyZTBiMmE1YmJkYTA4YjcyYzllMjU5NDk2YjgzNTlkMThlOTkyYTA1YzI0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlIwd0pnWXNaT0lmZjRUVSt1bEE4ZEE9PSIsInZhbHVlIjoiUVRQa25leXB4VFN3cnM2YVNJY2ZPTU5UMGJ5cjVIWm9MTDgwemFLVmM5RmVGdzRJaHZzL3hKYnVYRGtBZFpaZEE1RTB5VDF0cWoxL1B3RUFQUFZIR3VDYkx6WWlYTllTSXhpbXNSQnY1TkFkckdHbXRFSHVVcEQzSURSNkwrWW1XOGJ5ZU9Mdm1BaXhxMTloMm9qSG5YK0ZSTE9XMFl1Z2lHU21IT2t4QnExNExDbUNpcDJDQ3dodS8wSFoxeHd2M0FkeHZCMHhQWllSL3l4ZzRhTjNVRFhRMnc3NEZUT28zSExnd1V6TVV3NkUra2NCVDd6NnlXOWoyek5vUyt2QU9iTWRTcnoxbWFnVkJra0tkSk50M1FYb3JvZUxZTFA0MjV6U3Z2dTRDbk5ubTNCVVFCMU1wdjhOUWZidkx6SVFhOE51ZmNickFGdnE5NjNwbzJsZSswV0RWV0ZUMjg0K2g5WnVpOEIxL2NsMVNBdXFGS25KWWsvUGt1Nk9TVDQwa04ySitVZzU0b3FyTFh5citCajdmekc5UHlHT0I5UmVPQ0x1dzd2aFk3dXNERUszK29BckovRjR6bVpEU2g2dkFHaWhWNGFnTEcvN1JjeEcrWXJwSmt1NURjZnN1VU5RVHJ3Vkwwdm4yZWMwd3Y1MnhDTHdWakhEWUc2OGQ1WC8iLCJtYWMiOiI3NTRiOTM1NzFjOGIxZmM1ZWY0MGNiZTM3MDFhOGYxZDdkMWI5YzU5MGZjMWUwMmEwODBlMWFmNDljOTFhYTllIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkgweHA1T1krY3ZaNGcrQ1VFa0E2UVE9PSIsInZhbHVlIjoiS2ZYVFdKQ29PbE56YlZXS0xlQnlOOHNmelpJcjdzN2xrdmtDcnFJSGFwNTJvM1lZdC81RmltY0tJS3ViWUUwYVhJWUhDM3duR0c5dEh0a0pqZlczdUlvRUZLaW9iZUt1V2toTFluZ0pzOWFKZnlWSEwwS3lSTUVYMzNPdzFMZXRsVkJLZ1RCUUN3djFwUmloRE5PU1ZXZnNobnYrQWgrNy8vZGpUQmRrTjlPTVVQTWd2UU5UYWtCUVJ5NzY2ZHdsYmIxaVdkUnU3eDdWM21wMVozQ1JCVDdKU3p5b3E1V3hTZzJBanI5VkhOSGxQV1ZWajgrdVFtdUdZL2pDOXJodk82M0UrSWNBZVp1R3VpMVpkdWNsV3dkKzI2a29IN0ZrdENZR3lJV3NRbXdGa3JDbGlHd0xkN0c4bkRHczd0dDV0MEltL24xZStJRUMwNEN5RDJZUVRGQXU0LzRHSDE0QW9ETnlNcTljSkZKNDRxR0FFSVRNMnh3dEZ4aEZmbUJHZXZ3Zy9CVW1RcTVXOEtFTWZ2b0ZjaG1IcUh2N1JwMWUrenRwUzNpdG8rcVhOWVE3QlpYaksxNGVXbTJyVkVobVN0bEo1ZzFRbXJZOXRpazlHdFZhYm1tZllLYXNjWGFjNVdLaExoVjNqTHdwZFp3WXM5c2ZLZmZYdGM1VzZUazIiLCJtYWMiOiJhNDc3MjdjM2VlYTgzMWZkZDA2ZjIyZTBiMmE1YmJkYTA4YjcyYzllMjU5NDk2YjgzNTlkMThlOTkyYTA1YzI0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101565413\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2135446821 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135446821\", {\"maxDepth\":0})</script>\n"}}