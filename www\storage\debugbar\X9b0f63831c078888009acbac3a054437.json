{"__meta": {"id": "X9b0f63831c078888009acbac3a054437", "datetime": "2025-06-06 19:24:41", "utime": **********.386286, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237879.757489, "end": **********.386315, "duration": 1.6288261413574219, "duration_str": "1.63s", "measures": [{"label": "Booting", "start": 1749237879.757489, "relative_start": 0, "end": **********.13226, "relative_end": **********.13226, "duration": 1.3747711181640625, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.132283, "relative_start": 1.3747940063476562, "end": **********.386318, "relative_end": 2.86102294921875e-06, "duration": 0.25403499603271484, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44800120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01168, "accumulated_duration_str": "11.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.250219, "duration": 0.00641, "duration_str": "6.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 54.88}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.293694, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 54.88, "width_percent": 10.959}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.324088, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 65.839, "width_percent": 19.521}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.346909, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.36, "width_percent": 14.64}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-897207152 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-897207152\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-75357677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-75357677\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-959275010 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959275010\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-184477822 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237867275%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM4Q01yY0YvSDlDQXVWSzh4QmgzMFE9PSIsInZhbHVlIjoiWkk1V1NBTFJ6U3BxQmJsM1E3L0dXczdwT0QrMVhqK0dkcXk4RHRpNUxIZEtvOWNaOHFnTFVjeERTbHFPUmlpSGwra0tObC9hcEVoejhWSEJ3V2NRRndWblRZNkJyM3ZjeHNhRCs2ZEpzUER6VjB0UkZxd2YyVUh1ZzR0WUpHajl6N2NTMzlvbVgxUmdBa1JXNzIrODRzUjNIMk53RmNHZ0dpbVN1ZGpqdlkyZjVhMDFBRUtkQWo1TkVTQlQyTUhiY2VhNFlIOXAzdkVja2M3MW54U2JBOU1FVlQwU1ZBdW02Zi9nUjVNTXNrRjRKZWFRSkRVWUs3NGYwNEwyTVh1Q3hSWWh6bXpJb3Y4dytRZVV4VSt0NmRhOUZQQ3pianpKRVFza21FV2ZlYmZzMzVmRGZubGw1N2JDQXg4RnRSRWJHcnhjZnVjQXIyVnR3QWJqTXljdEprV25NMEIzNENudDdTcGtmeVQwenZVS1h0RVJrS2NjcFpMWGhZcEFOcWxNd1BVTXJsV04yMUhVcWpnWG9VU20va3lwdTFGWlRHalZHbFJERDY4RHVaNlVocytaSGI2ZjJpQ1pTU2lFRTBza3QwR2NtWGg2WGwvSVRKVGpLbUZCUklmU09zZmY1MTdYZXphYVN0RGJVV21xZFFBSjROYjNPSWM4QjNYbVMzTnQiLCJtYWMiOiI0ZjUwMjdjZjZjMGY4YWE0NzJlZjdlNDVjZjNiNjc2ZTI0OGMwYzUyNzI5MjM4M2U3ZTU4ZGJlM2ViMzNkZDQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilo3VHlaWGlDRSt5V2tRY0ptcVlXdEE9PSIsInZhbHVlIjoiaTZhK3N3dDFBU3YrTlZsM0xvNVlVRDZGd2ZTa3RETHJBdWF3clZhY3NXSXhrZXVqdXlORThkUnVhK0F2b1Q0a1BMTUh2MEJUa2ppTTQycUJhR29DdnM5UDB5emVybTllK09mOW92K0ZSd1MrL0dYZk96aUZCcTcrSFRFNjhyR0d2dmF5UUJ2Nk1wQTJiTzduWW5YbmM5K3hkc1pETlhZajFmMDZzVENJSEVIcHVZUUIwamhuZXplY0ZLQXhmbUtrQ3I2cU12K09ZVlkxWmIxNnU5Qktnd25JRnZMbzFHc1VBK1lSVFVtRUdLVUxPUGgrQ2ZqalBlNUJpSjZwM0U5TE9YV3BlcmcwYUN3K25PU2ZmOTc0a01xYnRvT1lPQmI5TU9iSDVJa215WmE4NG5GaldmWWJBRWdnZEN0NjJldmJoOGk1QTg2RW9jS3JHSW1ZMXE0VEQ2QVJuS2U4Mm9sbmlucGxyTk4yYU5CbjRKQTBkY1ZOYUVHZk5RSzVBU3I4N0ljRnozL1ZseWdOdTBCRWd1L2NiaGxmSFRPNHVmdjYwdFo2aFY4UHlTN3ZRMU14TlFPa0dKQ2dzWUpvS05FYlREQ1l2WFF3Z2dnSGFVelpDWG5TRG9DclpCaS9qSHFoSnlzS3dkK2xaV3ZLOG1PdGJvS3B0QWFGSXZmTWpNMlkiLCJtYWMiOiI5Zjg4NzJiNzE4MGQ3MmJmYzZhMzQ1NWUxNzM2MzFjMzgyYWE0OGIzMTgzYzc0MWYzNzM4NjQyMTRmN2ZhMzIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184477822\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-443216686 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bqimaRKNMYDaY70bMn4neav5Q7SUSQNahmKxKJLG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443216686\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-572607014 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:24:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFocSt4RDAzTmVtYTZha0ZjWjlMOFE9PSIsInZhbHVlIjoiM1IzbjJUMEl6KzlRcWd0Sm0zdkdSSk5Qd3JUd2tHN1ViMnIxNStBSHdETmJ2NC9CZkJVQ3FFenkyQTBFbXl3dXNkRGVOWW9hR2l2QmZpQ3hvODZqdEQvQXdnVUhkVDM5Q3VnUU9zbHE2b052dHN3RU80N253ZFhFNXJyYU9EVGJGb2p6bjk1dVZSM2s3U3V1dStxK3VPaU5ZTWFES0dGejBkVXJKVStqZklaTVBkOXpVSGh2cXNXdGFoL1JUV1ZTeG1CUjJEc3pwdzB5QVJwVkpqREpsYWpiUTVDOEtLaFk2VzB3ZElUMHRpOTgzTnVSS2hkWG1JMTZMSkxzU0d4VUxOQmFaWVFoeW9ab2VTZ1lwUUd1R0V5MFVHUXozNUxrdUVwYkZWSE9nYWFVVGdvQm5xVUJRaEFHZXVxemhZOTVIdExlZ3hPMDBKd3NEUjgwTTVJOUd0a2p6M0FlaEt6YnorWmxpa0QyUTZRalIvREl0QUVpMSt1Mk5ld0xqT2kzYVVKQlZrQ0Z4dk0vWFVUeFd0ZlJldkxwY1pSUy9VUmlsZmoxaW1VUnJ6QnVtMGlrWktQQklnRWUyRXFNdGt3QXkwZitmbUwyVFVmaEkxQ0hzSWJwVTllcittU01jTnFaK2hDTGsvb1gxRmJVQmRPOE11K1JTcE5UVS9qREZWVmwiLCJtYWMiOiJlOTJhOThjYTZmODg4NjhjYWJiNmQxZmJiYTBmMTYyY2NmNmI3ZmVjNzU4YWI0OWE4ZDM0NGNhNWJhZjgxOTFjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtOUTdwalg1UVZpV2pRUGl5bktjOEE9PSIsInZhbHVlIjoiZytKVDdVbE1UMmd2Uml1Z1dnTFpoNlE4S2RYVVcwSDJ1UExjc2hRNWRrOXNjeFBoaEFvTEZ6YkNXOEhOOEtZUk1IRmdwU09DRXhka0xkc2poWFJYT0Y1dHA2NmlQRlNUMENzcTNscUJxQ0Myc0JrOVhjUFEvZ2luZFRCZ2RQUzBMUG9CSHcyampoQ01mTm9hNVJEMThVTVNKSStodUVlNjV2NFVYbjlmYlpMbnZmcnl6UG90MFBqUjdSbkxscmZjYjJEbmpNdWhnVGVqc2YzckdwQ1ZwZk5VWkRteXpBWWYwR0J3Y0hSYnRBZEZXdDZ2NVpISWhqYmdhUDNldFRROXNFM2tiUHZWZWpZVDErQlcxa1pqazhUWGpyN015L2ZPYWhwMXdpMkxrdFRtOEFuanVyK3JFTnFLNm5Xck1rV1VoR1RqUVlheGF4OVBodHlEMmlrZFpPaU5XNld6N0hpdVJ4R0FqSndBT2JyUGp5ci9raXBDUHduWTlZcE1lYjBpYW1GMlhIdzUwTCtWVStEeGRSN1Qwc00yWHloNkZDRUtBRmpHay9DcXEwa0s1STNwdiswcU15UDQvcTlPQTRCZGdNQmwwUzFnWjJOU2JIQnNUTW5Xb0s0VWlNL1lsaTEvZ29iaXIzQTlyWnZqR2MwbU1SNW9xSUFpRllENXVoaGkiLCJtYWMiOiI3NWQ5NDJjZjUwZDE0ZTQ5YjQ5NTBlZTZkZjZlOGEwYzJiOTIyOTY2ZjIwYmNkNmE1YTFmOTk4MTIyZDZmNWIzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFocSt4RDAzTmVtYTZha0ZjWjlMOFE9PSIsInZhbHVlIjoiM1IzbjJUMEl6KzlRcWd0Sm0zdkdSSk5Qd3JUd2tHN1ViMnIxNStBSHdETmJ2NC9CZkJVQ3FFenkyQTBFbXl3dXNkRGVOWW9hR2l2QmZpQ3hvODZqdEQvQXdnVUhkVDM5Q3VnUU9zbHE2b052dHN3RU80N253ZFhFNXJyYU9EVGJGb2p6bjk1dVZSM2s3U3V1dStxK3VPaU5ZTWFES0dGejBkVXJKVStqZklaTVBkOXpVSGh2cXNXdGFoL1JUV1ZTeG1CUjJEc3pwdzB5QVJwVkpqREpsYWpiUTVDOEtLaFk2VzB3ZElUMHRpOTgzTnVSS2hkWG1JMTZMSkxzU0d4VUxOQmFaWVFoeW9ab2VTZ1lwUUd1R0V5MFVHUXozNUxrdUVwYkZWSE9nYWFVVGdvQm5xVUJRaEFHZXVxemhZOTVIdExlZ3hPMDBKd3NEUjgwTTVJOUd0a2p6M0FlaEt6YnorWmxpa0QyUTZRalIvREl0QUVpMSt1Mk5ld0xqT2kzYVVKQlZrQ0Z4dk0vWFVUeFd0ZlJldkxwY1pSUy9VUmlsZmoxaW1VUnJ6QnVtMGlrWktQQklnRWUyRXFNdGt3QXkwZitmbUwyVFVmaEkxQ0hzSWJwVTllcittU01jTnFaK2hDTGsvb1gxRmJVQmRPOE11K1JTcE5UVS9qREZWVmwiLCJtYWMiOiJlOTJhOThjYTZmODg4NjhjYWJiNmQxZmJiYTBmMTYyY2NmNmI3ZmVjNzU4YWI0OWE4ZDM0NGNhNWJhZjgxOTFjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtOUTdwalg1UVZpV2pRUGl5bktjOEE9PSIsInZhbHVlIjoiZytKVDdVbE1UMmd2Uml1Z1dnTFpoNlE4S2RYVVcwSDJ1UExjc2hRNWRrOXNjeFBoaEFvTEZ6YkNXOEhOOEtZUk1IRmdwU09DRXhka0xkc2poWFJYT0Y1dHA2NmlQRlNUMENzcTNscUJxQ0Myc0JrOVhjUFEvZ2luZFRCZ2RQUzBMUG9CSHcyampoQ01mTm9hNVJEMThVTVNKSStodUVlNjV2NFVYbjlmYlpMbnZmcnl6UG90MFBqUjdSbkxscmZjYjJEbmpNdWhnVGVqc2YzckdwQ1ZwZk5VWkRteXpBWWYwR0J3Y0hSYnRBZEZXdDZ2NVpISWhqYmdhUDNldFRROXNFM2tiUHZWZWpZVDErQlcxa1pqazhUWGpyN015L2ZPYWhwMXdpMkxrdFRtOEFuanVyK3JFTnFLNm5Xck1rV1VoR1RqUVlheGF4OVBodHlEMmlrZFpPaU5XNld6N0hpdVJ4R0FqSndBT2JyUGp5ci9raXBDUHduWTlZcE1lYjBpYW1GMlhIdzUwTCtWVStEeGRSN1Qwc00yWHloNkZDRUtBRmpHay9DcXEwa0s1STNwdiswcU15UDQvcTlPQTRCZGdNQmwwUzFnWjJOU2JIQnNUTW5Xb0s0VWlNL1lsaTEvZ29iaXIzQTlyWnZqR2MwbU1SNW9xSUFpRllENXVoaGkiLCJtYWMiOiI3NWQ5NDJjZjUwZDE0ZTQ5YjQ5NTBlZTZkZjZlOGEwYzJiOTIyOTY2ZjIwYmNkNmE1YTFmOTk4MTIyZDZmNWIzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572607014\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1324539052 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324539052\", {\"maxDepth\":0})</script>\n"}}