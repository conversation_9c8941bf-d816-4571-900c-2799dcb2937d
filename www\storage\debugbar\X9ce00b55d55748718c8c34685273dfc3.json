{"__meta": {"id": "X9ce00b55d55748718c8c34685273dfc3", "datetime": "2025-06-06 19:14:40", "utime": 1749237280.05818, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237278.744642, "end": 1749237280.058211, "duration": 1.3135690689086914, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1749237278.744642, "relative_start": 0, "end": **********.875274, "relative_end": **********.875274, "duration": 1.130631923675537, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875294, "relative_start": 1.1306519508361816, "end": 1749237280.058214, "relative_end": 2.86102294921875e-06, "duration": 0.18291997909545898, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028480000000000002, "accumulated_duration_str": "28.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.963188, "duration": 0.02519, "duration_str": "25.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.448}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749237280.017013, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.448, "width_percent": 5.899}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749237280.035722, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.347, "width_percent": 5.653}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1575127003 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1575127003\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1476562757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1476562757\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-682568257 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682568257\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-585493243 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii85MVVrWTFGK25sQ1k2ZCtMUXhaamc9PSIsInZhbHVlIjoiQzBJQkNaaE5vWmlGQTJMUk9zM3ZRb3oraWxhWFJtOWhDeUJoK0lFbVFtUVM5dU5wTTRjNC9uQ1FFeHdIRWxjdnVxNGJ3RCsrNHRWRGVLcG9YL0NQalM2bFBGRVAwaCtBd2xwbFFJUlhQbnVLSEMwdkc5RExVSjg0Mi9lcHhRd1ZRT1pCODM4VWx6eXkwWVJVODk4eDJUekhhM3FrTjJWR2xFWTVCSGh3Qyt3MVZReHh1MXcrT0gvWFl2QndOeW9YTFZqODZXK200WlhXdnV1b3JwdFJXU244TEFzbjhFbUpmNWJ4WDBsOWFXRWNuWkplaGxyVUc3TUwzaGZXdVNSdHR5azRWbCttdWRvWFpHd01vMjRodDIrTXBlYlBpb0lNcXlRTWljTzhpbkwyNGtCWjFMOEg0eGNZYXVSMkRqRXEvaWlyU3JHRWhBNjdjUDJQZnhUTWEvdnU2MXQ4QWM0bnRrYjZyUzN2STk3bFN4eFU0R2Z1ZmJWS0ZMRUltK0lFbG14bzhVTGt3VHVXOHlwQU0xeTh3RkxpRHQyWUVVdE9WQ3ZDK011UnVocFNpOEhsVk56bC9CQWtaaHdDcjZxUzdXTHZYRmVia3JsbmZzcCtnTHA1NEZoVjdKSzdjSi9rR3NzM2dVcHNDUVZ1OFBxbzEyVUF5MzdXd2pMUng5MXMiLCJtYWMiOiJlMmFjMTc2YjhjMmZkZDRiMTBmNTYyNjQ4YjZiMjc4ZTM2ZjM3ZmVjZWYwODUyN2RhMWNhZDdjMTcxODQ1Y2ZlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlhHLzZlWHBKL25uVHhOTTErYjZJTlE9PSIsInZhbHVlIjoiR1dZRFpZL29peG82RjF1SUQwMTYwdWUzeGxJd0pmTXFyQm9iYUNZUmI1Y3dHd245VkVOOExYZHhRSWU5N2EzdFJvM1hXRFIvK0szdWtuUTRWKzUxYmgrV282Z0gwSHNlUlZBVC9wZ2E1OHdudkhVQkl5TitwUVplODFQNE96SEVXOStQZTlmdW1OenBxN0tSQ1FtZVhlQnBOdUtlRENjNUhPNDJpZG5wMHhibmJScG9IS2o1blZDeGd6NWM0eTE0N1dNczZCdlY1aFlRcFhXNElrL1FzbmNiSm00RDdKaVJoTEVMcFVvdmRYQUdsclpCQWM3T1cvMG5yYnN4b1M1VDRrQ21ydlBmTmF6QS95RnlBTjh4c0NMMVpGaGZwVEdnK3ZCUytxNGhIeWZjTVNwNlpWVkxIMFJvQ2REandqQlFTdURUcjE4Mzk5Mm5hYlBDaHgwWWNiTmxNb3F4SnVPWUFTclFtMUY5c0VjUkpIOU02Q1ptYjJCMk9QWlFRREREbHpiYnJFNE8vWjE1bkx1ZzJySjRCMTJ5dTNrZFdVWlFnakp0N2dzWFIxR2VhdDBzMDF3eDB5VWd1dzdqS3BZcERqdkdvbEJXL2lrWlRySXBLOHhxMGR5Mm1pTFhJRU1iUWt0VWhWRzhuVmR1YWlONlgyNzA0cmlwVnp5SGFHQTMiLCJtYWMiOiJhNDdjMmZjMDI1NmVhYTIzODYyODNhODVjYzM5MzhjNTY1NGZmMDZmZjc2ODRiNzI0YTVjZWE3ZDc4NWE4ZjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585493243\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1083369311 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083369311\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1049705872 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:14:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iko1OGxNWUM1VDVEZTg0WUlBUlU2OEE9PSIsInZhbHVlIjoiRWd5Wm1zZW5JWnlIWjZpbm1FOXluTzZzT0xoSnlUTnorcTgzblNiNDdldWx6MXBmZmVnY0ViU3hENEwvRDZkZHpiZGs4WldGSnhHN2dQb0JFVG4wNXNLN2Roa2xTTlZkZmxKZTB4dXdCdFFCKzhJUEVkZURSdUYzdzdnY29EZHJjRXJlemhEVW90NjJYbmtkckFFNHptQ0hKNkh0QjdFWGtOcC9KU0VTcXJRWmtYMWc1RnBzNUw3VHNmRjVkeHNFODFMZ3hudDdsWGdFNGNZcDZEQmhJUW9XUjlGLzVzaGFIN3ZZQnJQdWpTTWV4Uk15bUlhQ1FuL0lPNVVqTlBwN1RYckpCaCtIRnFyL3dxMmR0REZaam52eURtSW10Q3crRjF4bVZOVTE5czNUSzBhZHdaUlZhVVFDMnlTVUpaZWxnY2JyRlVXVmxLcURTcmdXOEpMUktXd2lwWEhhU3RWK2swYnV3am56R0pSTkFDYUIvNXBxNXZ0VWdhWHBvWEJHdXRFWnFWVE5EMFdZZmRLTUJSWi9SenQ3NVkvSTUxV1NZUHBBQUJHc0JqMHQ4N2htMTVCT2VvanBTTGJmbDEzS0IwTnJ4S2xCclR2dmduaEZ3SlJ3ZEwwU2p2dE03RVJKNHJaU0dqWjQyMjJmcEJIK1RGS0lXQUVncFhRcDZ3UzciLCJtYWMiOiIxMDAxNGM5YWRlOWNlMmYwZGNmZDY5NjBjNGUzY2I0YjY3Y2FmMzg5MzBhNzNkODA4ZjBkNjE0NTRhYjkzMjI2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im91Z0dJZWdsdGVnSGd4ZU02cGR4clE9PSIsInZhbHVlIjoiZktFQTlIbkk5b0xJWXU4R1JBeVBxVDVHY0Z1bVp4ZWQrS0ovQXc4UUNhUks5UzFHLzZHNjdjUndLQ0RROUhTQm5aQktTL0lUVzBoOStaeDlMU1Y4VWVmamtBei9aTDRMSytRUytvWE1PdjgyVnVmWURKNndoQ3RoVXhZVGZOUm9DcitMYUNBa2UvT2ZrOXlHRnZuTjdEQ1hkK3lOYzV1WldwQjVrNE9rWi9yNGp3a2l0My9iMklmbVRDVkNJMmwrSXRnV0Jpdzh1ekdqVWNvYTh3UC8rUGs0YXZBa3RZcTdWNlcrd3hCczVwakN1VzRzN3dGZ2dzT1oyNnJmaGM2c1lieG0zbTJuRFJ2R0g4N1NDV0xlbEdmRDNxSE8yR21GZEU2Z0x4YzhBNTA3Vm12cmlDYkF6WXVieEs4Q1Z4cDNGUSsvbHZkTnRpenNwL3hJTnZoNFZQY1lXMEpjRi9hVkRwY0o5c1gxWXh2RVFBL0FGTVY1eTRSTGVWY3lUU0lIVDR5VHBGa1hKYVZOYVZxRG5hY2JEc1BDbG5oeUJnalR4Q3RWd2U4UmMvdy81U3BmbXdOZkNyQytkVmJZUndCMVVldWR2Y3BDRzRlSTIyYnhWV0Nna1RDcjRoVGQ0NDE0WTF3THJiWE41STZ6U0doNE1wVVpXVzVGN3hPQmU1SWUiLCJtYWMiOiI4YmEwMWJmYzE2MjNkY2FkYTA1MzljMmZmOGU3YTllZjdiYTc3ZDI3ZDVlNTZmZTFlN2YzYjNlYzhmNjkyMTAxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iko1OGxNWUM1VDVEZTg0WUlBUlU2OEE9PSIsInZhbHVlIjoiRWd5Wm1zZW5JWnlIWjZpbm1FOXluTzZzT0xoSnlUTnorcTgzblNiNDdldWx6MXBmZmVnY0ViU3hENEwvRDZkZHpiZGs4WldGSnhHN2dQb0JFVG4wNXNLN2Roa2xTTlZkZmxKZTB4dXdCdFFCKzhJUEVkZURSdUYzdzdnY29EZHJjRXJlemhEVW90NjJYbmtkckFFNHptQ0hKNkh0QjdFWGtOcC9KU0VTcXJRWmtYMWc1RnBzNUw3VHNmRjVkeHNFODFMZ3hudDdsWGdFNGNZcDZEQmhJUW9XUjlGLzVzaGFIN3ZZQnJQdWpTTWV4Uk15bUlhQ1FuL0lPNVVqTlBwN1RYckpCaCtIRnFyL3dxMmR0REZaam52eURtSW10Q3crRjF4bVZOVTE5czNUSzBhZHdaUlZhVVFDMnlTVUpaZWxnY2JyRlVXVmxLcURTcmdXOEpMUktXd2lwWEhhU3RWK2swYnV3am56R0pSTkFDYUIvNXBxNXZ0VWdhWHBvWEJHdXRFWnFWVE5EMFdZZmRLTUJSWi9SenQ3NVkvSTUxV1NZUHBBQUJHc0JqMHQ4N2htMTVCT2VvanBTTGJmbDEzS0IwTnJ4S2xCclR2dmduaEZ3SlJ3ZEwwU2p2dE03RVJKNHJaU0dqWjQyMjJmcEJIK1RGS0lXQUVncFhRcDZ3UzciLCJtYWMiOiIxMDAxNGM5YWRlOWNlMmYwZGNmZDY5NjBjNGUzY2I0YjY3Y2FmMzg5MzBhNzNkODA4ZjBkNjE0NTRhYjkzMjI2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im91Z0dJZWdsdGVnSGd4ZU02cGR4clE9PSIsInZhbHVlIjoiZktFQTlIbkk5b0xJWXU4R1JBeVBxVDVHY0Z1bVp4ZWQrS0ovQXc4UUNhUks5UzFHLzZHNjdjUndLQ0RROUhTQm5aQktTL0lUVzBoOStaeDlMU1Y4VWVmamtBei9aTDRMSytRUytvWE1PdjgyVnVmWURKNndoQ3RoVXhZVGZOUm9DcitMYUNBa2UvT2ZrOXlHRnZuTjdEQ1hkK3lOYzV1WldwQjVrNE9rWi9yNGp3a2l0My9iMklmbVRDVkNJMmwrSXRnV0Jpdzh1ekdqVWNvYTh3UC8rUGs0YXZBa3RZcTdWNlcrd3hCczVwakN1VzRzN3dGZ2dzT1oyNnJmaGM2c1lieG0zbTJuRFJ2R0g4N1NDV0xlbEdmRDNxSE8yR21GZEU2Z0x4YzhBNTA3Vm12cmlDYkF6WXVieEs4Q1Z4cDNGUSsvbHZkTnRpenNwL3hJTnZoNFZQY1lXMEpjRi9hVkRwY0o5c1gxWXh2RVFBL0FGTVY1eTRSTGVWY3lUU0lIVDR5VHBGa1hKYVZOYVZxRG5hY2JEc1BDbG5oeUJnalR4Q3RWd2U4UmMvdy81U3BmbXdOZkNyQytkVmJZUndCMVVldWR2Y3BDRzRlSTIyYnhWV0Nna1RDcjRoVGQ0NDE0WTF3THJiWE41STZ6U0doNE1wVVpXVzVGN3hPQmU1SWUiLCJtYWMiOiI4YmEwMWJmYzE2MjNkY2FkYTA1MzljMmZmOGU3YTllZjdiYTc3ZDI3ZDVlNTZmZTFlN2YzYjNlYzhmNjkyMTAxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049705872\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}