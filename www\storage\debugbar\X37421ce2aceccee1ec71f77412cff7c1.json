{"__meta": {"id": "X37421ce2aceccee1ec71f77412cff7c1", "datetime": "2025-06-06 20:39:09", "utime": **********.654631, "method": "PUT", "uri": "/users/16", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242348.104637, "end": **********.654664, "duration": 1.5500271320343018, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1749242348.104637, "relative_start": 0, "end": **********.27935, "relative_end": **********.27935, "duration": 1.174713134765625, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.279371, "relative_start": 1.174734115600586, "end": **********.654683, "relative_end": 1.9073486328125e-05, "duration": 0.37531208992004395, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51227840, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT users/{user}", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.update", "controller": "App\\Http\\Controllers\\UserController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=252\" onclick=\"\">app/Http/Controllers/UserController.php:252-315</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.056709999999999997, "accumulated_duration_str": "56.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3654768, "duration": 0.02958, "duration_str": "29.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52.16}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4246979, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 52.16, "width_percent": 2.01}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.435519, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 54.17, "width_percent": 2.116}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.50072, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 56.286, "width_percent": 2.363}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5085769, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 58.649, "width_percent": 3.791}, {"sql": "select * from `users` where `users`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 284}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.523997, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "UserController.php:284", "source": "app/Http/Controllers/UserController.php:284", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=284", "ajax": false, "filename": "UserController.php", "line": "284"}, "connection": "ty", "start_percent": 62.44, "width_percent": 2.187}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>' and `id` <> '16'", "type": "query", "params": [], "bindings": ["<EMAIL>", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.5708928, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 64.627, "width_percent": 1.375}, {"sql": "select * from `roles` where `id` = '16' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["16", "web"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 120}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 298}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5808768, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": "vendor/spatie/laravel-permission/src/Models/Role.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "ty", "start_percent": 66.002, "width_percent": 1.393}, {"sql": "update `users` set `warehouse_id` = '8', `users`.`updated_at` = '2025-06-06 20:39:09' where `id` = 16", "type": "query", "params": [], "bindings": ["8", "2025-06-06 20:39:09", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 301}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.587595, "duration": 0.009609999999999999, "duration_str": "9.61ms", "memory": 0, "memory_str": null, "filename": "UserController.php:301", "source": "app/Http/Controllers/UserController.php:301", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=301", "ajax": false, "filename": "UserController.php", "line": "301"}, "connection": "ty", "start_percent": 67.396, "width_percent": 16.946}, {"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 3059}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6034, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3059", "source": "app/Models/Utility.php:3059", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=3059", "ajax": false, "filename": "Utility.php", "line": "3059"}, "connection": "ty", "start_percent": 84.341, "width_percent": 1.799}, {"sql": "update `employees` set `name` = '<PERSON><PERSON>', `email` = '<EMAIL>', `employees`.`updated_at` = '2025-06-06 20:39:09' where `user_id` = 16", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "<EMAIL>", "2025-06-06 20:39:09", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 3061}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.614485, "duration": 0.00671, "duration_str": "6.71ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3061", "source": "app/Models/Utility.php:3061", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=3061", "ajax": false, "filename": "Utility.php", "line": "3061"}, "connection": "ty", "start_percent": 86.14, "width_percent": 11.832}, {"sql": "select * from `model_has_roles` where `model_has_roles`.`model_id` = 16 and `model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.63029, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 97.972, "width_percent": 2.028}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-638040467 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-638040467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.522042, "xdebug_link": null}]}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/16", "status_code": "<pre class=sf-dump id=sf-dump-139768039 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-139768039\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1510123882 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1510123882\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-771235930 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\"><PERSON><PERSON>asi<PERSON></span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"2 characters\">16</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771235930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-493523223 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">135</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242339070%7C15%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZaWG93dW43QXRhc1N5RlNoSDdzdFE9PSIsInZhbHVlIjoiMTJsd0Zkb1k3aEQ2WTVhZDNrUzM4M0YySE1RSWEwdGx1aktiMXZ4djJTeWJOZzdzQkJxYyswb25hTThYNFg4ck1FRFFMblVOQWtaZ1MvQjBxQ2xSaTM4bzFLL0ZVYzcxNlJ2dGpNWUlLSjdPNkFKbjNqU2pGa1ZNQXdMZlhXTmh2eStoMW42bjh2N2NJMlR5SlNLQVZZdC9yWURnejBtN3ZvdkdKUHZaSEhNZk5Wb2lzSlhHbU5qazdqWFkxQ2tCZ2FJSzAyKzFtSlJvMnh5YUl4SkNPK29RbGpxcVVSbWJveGZHMERaeGVwOXlReU9EeFpzZDRqcUs1M1c4cWJTYlVqUDRlUVg5S3ViaWlYcWU2aGFxSFU2RERXWEJJSHRGck12VmJRSUtKN01mYnc4Vkt3MlNXWEJXSXQzbUhKelZObGgvcnowSU95VHBRRzh0SHZialB3UXpuR1YwT21RMm9hV3BDS3BsT3J3VFNFTWdtTVQxS25EMmZuQTUyVFVJS1JPL0xMazh3b05CRmlJdkxPNFdDaEtLcTRQaHhYR2RNc0d4TGROQUVYUEo1cDZXT3Y1RUhTclV5c05Bb3RkYzJkZFNLQ1J0SVhPckNaTnM2LzhKNUFjNlJoZjF3Y09mVDdxVy9GMjhRdTYzMDJOWGpDVjFrQ1BVcUllQ0hKeFoiLCJtYWMiOiJlMjhjZmJmMmQzYjk1ZDBhZjVlOTA4MDFlZWVlMjY0N2ZhOWU0MzRjOWVjZGZiNWMxMmQ1NjhiYjI1NmJmNDM5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpMbnZPRzU1b3BOUHI2UUZwOUdaZ1E9PSIsInZhbHVlIjoiVWdPaDgwNkFHRTZ0b09YS3NNTmh1S3NHMDBLYk5ac29GUXY2b1B4T3BqWThJbERiSlkwNGYxWllOTzNjeHdmdHlYQ0hpZVQrazlxTzFBS1lGa1Nha281RzVmd05vTVg5RGlOdjZ1cFFhalEyTURMdGNIVlorQVpTRnd2cHZCZDMvSFk5d0c5cVNueDNRdzQ1WXZwYTY1U1ZBR1JaRnF5ck5iS1RPaDErcFVRN0tvd2hpd1dsYUc0WGFpVXZJTGdEZHdpdW1aK2hERGhtQk9mUjRUZUFhcURwVWlKc3RqK0NmeWdUNkc2S0pRTlpudU5ObXNGR0pXcmJnM1p5azdNMXkreWNTc21jQmwwKzcyWmVHbGJXNUJNN0VXeXRWZGl1NE84elZldmVwWDJpejhZcXROT0o5M0tURVJuSE45ZUdwNXdoY3dscEdSMXFRYWY4c3VabTBwa1ZZK3FHUmYxYVRzM0drNmV6YWhTcHZYRTdQVk1oWktsd2xudW1VRnBBNWJ5NDlJUVZuMFRPcTY4UjZxaDlveGt6Q2pUWjlBMWZOcVRLd0lsNExWZHAzZ1pVNUhWbUZ5ZUNKNXpFbXRjcHdINEdPWTFtVnNFQk1MaVVVUnZ1REdYZnJsdFFZTjVickMzMGlDY2w3M3N1MjMyOUZSSnJsdGZmTGJCSUNHaUgiLCJtYWMiOiJmZWI1ZjI1ODBiODM0YTk4ZDQxNjMwYTQ1N2RmZjgxOGRmYmMzZjI2NDYyNjMwODk2MGJiZDIwYTIyYzkxMzhkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493523223\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-37059929 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37059929\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1636196071 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:39:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZmd1ErQTVrSytNdGJ4ZnZlejdyY3c9PSIsInZhbHVlIjoiVUJzN1A5dytnMzU0SkU1amMwM28xUHR5ZkJwKy93WFVyd2ZKeXE3TnZBZjJkZzJzdDh4eDhKOE03cWNTVE0xTEV2VDAvVEdlcnpZbEwrTzl1dEtDSlM3MGgwODhURDFUQmYraWYwd3FJMTFDY1JDbmM1UmxXbUhVZFMwME5QMVUxc2pPM0ZjYzVZRkxPY1FFb0VCY2xQUzFrWkt0Wk9CTzUyekN2RkhLbmtkNmx2QW50VSt5UitRdjBWVW56NDNwbXltTVhmRlpJY3RrZ1hOcFdqMlRpcVR6K3cvckJrT2liTGJPV3R2emk2VEF4cGdZY2JseEJOSlEzaElQU3Jjanh4ck9vWlFYTWczM1ltdXhPRXRmNGx4Nmt1RUJjMGFuRDFNbGR3Sy9TWWhZaDYzS0Y5b0p1QnZuM3RQMWZzQUxuMWNZcmNwYittektMT05OMkxuUDBoQmc4MTZZMVoxMXgvZ3BOL0tzRTRKUFp0UDFUUDRZNklTZmx2L1NZK3p5QnR0ZmJ3cXJyN3cyV0dxSGM2ZVJ1T3dwT2pLOWNkMmJvcTFlekRTdGMrOXdRaE4wL1NWZTd4K1ZLVUFTdTFIWlkwVEtvNUdHK2p2U0JveWwyL3ZQRXJQRGxJZ3dJekQvYzJuT1RTVHRkcDgwNHFsR2NGTFFaa2NmdE5pNGJER3MiLCJtYWMiOiI2ZDI5OTlmM2JlN2E5YTAzYThhOTlmOTFjMmRiMTRjOWMwNDk5NDM5YTllZjdhZWZjM2JhZDNjMjViODI4M2IyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNsRGNrbHZCTDZpVnZrNFkwRGRVQ3c9PSIsInZhbHVlIjoidHc1dGJ3MW1jaHdwZld5NTNkSXJ2VXNBTlFDQi9KWmY3c3FvUXhlN2p5UGdoTjJVMmQ5NlhaRVpKNTMva0pDVSt4Q2txRjE3dHpGa0JIck55clNwS28rOXBEWlZwRldwQTQ5Y3g1b3FyTk9vak9EdlkzMTNZK3JoZ3FkZUFmVDl4UFNTT2d2dGlMcDJ3WWFBeW1FZEZ2QUk4NTJ0dVlTNDg0bHA3ZVBIV3ZzM1AvbHZNRGZqdEpicjJEdDFOZldKM3lMbG93aEVYK0JjV1crUVlaNVhNbndwNnJ1RnhmbDhqYnR3aXFhbS94MEVCVlg1UE4wOVovQnZPOTI3QTNXdVdNLzJ6SmZQdEhkQUNISXVjRTNTZlNSWTNCY0lqaXNDYnNXT2dNWXBqWXlDVWQ2WkxwYlNNOG9CNVIvcDdCLzNpNUNMRWEvTzFuRlg0bHZ3UVdlT3NYSDgwS2Z2d0ttQ3FZUUtzd1NmamRKQ3lrN1pndytDa1BhQS8yeUJ5ZjNaenF4a2NqQnFNNlUrSWwvOE0vak9NS3UyRmZHcW5obUlEbWdQMUcxcTkyUnEySmZoRFFaQk1sY3JuRlduN1lFa1NGeWdqaElhSDJqR0diMkN4SWxrTHljSm1zY0tNR3FJcmVWY21IdGgvUy9FekQzazUwR3VWS2JYZlVyWEFtVFoiLCJtYWMiOiIzOTJkYTRiOGRiZDNiZjQ3ZWY3MWRlYzBjY2U4ZDFjOTAzZDhlMjM0ZjliMzM4NDZlY2VlZTZiNjFiNDBkN2U4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZmd1ErQTVrSytNdGJ4ZnZlejdyY3c9PSIsInZhbHVlIjoiVUJzN1A5dytnMzU0SkU1amMwM28xUHR5ZkJwKy93WFVyd2ZKeXE3TnZBZjJkZzJzdDh4eDhKOE03cWNTVE0xTEV2VDAvVEdlcnpZbEwrTzl1dEtDSlM3MGgwODhURDFUQmYraWYwd3FJMTFDY1JDbmM1UmxXbUhVZFMwME5QMVUxc2pPM0ZjYzVZRkxPY1FFb0VCY2xQUzFrWkt0Wk9CTzUyekN2RkhLbmtkNmx2QW50VSt5UitRdjBWVW56NDNwbXltTVhmRlpJY3RrZ1hOcFdqMlRpcVR6K3cvckJrT2liTGJPV3R2emk2VEF4cGdZY2JseEJOSlEzaElQU3Jjanh4ck9vWlFYTWczM1ltdXhPRXRmNGx4Nmt1RUJjMGFuRDFNbGR3Sy9TWWhZaDYzS0Y5b0p1QnZuM3RQMWZzQUxuMWNZcmNwYittektMT05OMkxuUDBoQmc4MTZZMVoxMXgvZ3BOL0tzRTRKUFp0UDFUUDRZNklTZmx2L1NZK3p5QnR0ZmJ3cXJyN3cyV0dxSGM2ZVJ1T3dwT2pLOWNkMmJvcTFlekRTdGMrOXdRaE4wL1NWZTd4K1ZLVUFTdTFIWlkwVEtvNUdHK2p2U0JveWwyL3ZQRXJQRGxJZ3dJekQvYzJuT1RTVHRkcDgwNHFsR2NGTFFaa2NmdE5pNGJER3MiLCJtYWMiOiI2ZDI5OTlmM2JlN2E5YTAzYThhOTlmOTFjMmRiMTRjOWMwNDk5NDM5YTllZjdhZWZjM2JhZDNjMjViODI4M2IyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNsRGNrbHZCTDZpVnZrNFkwRGRVQ3c9PSIsInZhbHVlIjoidHc1dGJ3MW1jaHdwZld5NTNkSXJ2VXNBTlFDQi9KWmY3c3FvUXhlN2p5UGdoTjJVMmQ5NlhaRVpKNTMva0pDVSt4Q2txRjE3dHpGa0JIck55clNwS28rOXBEWlZwRldwQTQ5Y3g1b3FyTk9vak9EdlkzMTNZK3JoZ3FkZUFmVDl4UFNTT2d2dGlMcDJ3WWFBeW1FZEZ2QUk4NTJ0dVlTNDg0bHA3ZVBIV3ZzM1AvbHZNRGZqdEpicjJEdDFOZldKM3lMbG93aEVYK0JjV1crUVlaNVhNbndwNnJ1RnhmbDhqYnR3aXFhbS94MEVCVlg1UE4wOVovQnZPOTI3QTNXdVdNLzJ6SmZQdEhkQUNISXVjRTNTZlNSWTNCY0lqaXNDYnNXT2dNWXBqWXlDVWQ2WkxwYlNNOG9CNVIvcDdCLzNpNUNMRWEvTzFuRlg0bHZ3UVdlT3NYSDgwS2Z2d0ttQ3FZUUtzd1NmamRKQ3lrN1pndytDa1BhQS8yeUJ5ZjNaenF4a2NqQnFNNlUrSWwvOE0vak9NS3UyRmZHcW5obUlEbWdQMUcxcTkyUnEySmZoRFFaQk1sY3JuRlduN1lFa1NGeWdqaElhSDJqR0diMkN4SWxrTHljSm1zY0tNR3FJcmVWY21IdGgvUy9FekQzazUwR3VWS2JYZlVyWEFtVFoiLCJtYWMiOiIzOTJkYTRiOGRiZDNiZjQ3ZWY3MWRlYzBjY2U4ZDFjOTAzZDhlMjM0ZjliMzM4NDZlY2VlZTZiNjFiNDBkN2U4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636196071\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-741564316 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">User successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741564316\", {\"maxDepth\":0})</script>\n"}}