{"__meta": {"id": "X023bd623ee80b7e7deb3f4fcd5d053d9", "datetime": "2025-06-06 19:26:42", "utime": **********.666233, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.397038, "end": **********.666264, "duration": 1.**************, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": **********.397038, "relative_start": 0, "end": **********.492025, "relative_end": **********.492025, "duration": 1.***************, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.492044, "relative_start": 1.***************, "end": **********.666267, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016900000000000002, "accumulated_duration_str": "16.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.574701, "duration": 0.01388, "duration_str": "13.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.13}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.613624, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.13, "width_percent": 7.278}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6444309, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 89.408, "width_percent": 10.592}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238000077%7C27%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhBMHhqbG5aRjQreHFPL1pvNWtuZEE9PSIsInZhbHVlIjoiRWpUZVdvcVZCSkRDYkRnbmlMV08rbHZDYUJUcVhOdUdrYnVHWEQzSWlQRWh6eFordmIrMFNjL2RuTFhoMVR5ajdrT3IvQVMxemdBZnZlRVEyc3lwdWxmWlZaZXNsb1AwZDZoOG0zQXlRYktTS1RZQUxxMnVMOGJHd0VvNytSRkF4WEVlZHl2dnZ5bGc0dk9BWG5IS3ZJcUxBcW9jazhBMHhqRmI2bHdqenljSG5SMEgxOGdvSFVPQnJBaEJzeUJMZnBVK3JQUHFaL2lmN3VxRHRpckpWaHpsMHM0c1dnZ0MxVGtkbVNtN3VQMHRIOWRnUXJyclBnWVBQYVRLZHZQb0pnN2E0VkJCd0pTYi9TTjlzcTNEZllGellUNHZrc0tzekZVRFdIT0RrVkswVEJsSDBobTZEN0NvVUVodFhvdVU4a1dyYVg2UVBmUjJZSGdsbHB3TXQrZjMyYzE3cWlwV1dkRkozRTVKTkpuMW1TKzZnM1MwWTZjL0pScFJzcWlrTnJ6bG5jOGlYMEdiNnl0ZUZTSURzOEVvUUREU29LMnhEbHd1dHB0VmVKbzhyUWJualhwTVUwekJIZnZHQUNpZEJrREZtZ1VqUitDN3BtMU5LdmdwRTlxbkVHZUdHYzhnTU1kOG5KK1gxaERHVmFRNmphVVJjRGh3UmkvYmFwbHAiLCJtYWMiOiI1NjQ5MTkzNTRlNTliMzY5OTMzYTM0Yzk0MTYwMjY1NDlhMTUzM2Y4MmNjNGE3MmUyN2YzYmY1MGRmYWM0MDI2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtzTHZZMisyb3I5dXUvTXdWSk9JQnc9PSIsInZhbHVlIjoiSmtwWEUyR2dxU2cvVXlpdS93K1RkbHZ4bWJRdldhNGsvdGtkTG1BQVBRSTFuZXg3Q2pnWUJTczduN3ZndHk0aU5qaUZjQnpqUWo0WGd4a1ZtK1Bhemx1cDE0Yk5MTVYzcWRxempHZ3R2QnRBcE05M2o0OFh4YTY4Wkp0MWgxU0ZUVExES2w1Tk9ZVGkrbm9CUVFJbWNQTnNzb2ZxbTNDZU92azBrOEl4My9FNnhRbW9iUkxGN0FGRDRMb2swdkIySEgwaVc5UTl2K3BweW0vWkUwS3ovK241SW5xSXYzV0xwQ1J1akZoamRmeDUxWDAvZk94b1gySWE5VUtuejhXMmlGY2RzbWxzMEtaLzNvdzUzaWdwNCthZWR4ZjhCS3QzMTFnOURQeHdTblE2NnJlWWthd1R4VHk1NzdacjZHazhLOTNhalBHblNUY1JYakd1T2pVSlBGL2s1RyszMkVGUm9yQmtEc3Rka3VicG9tTG90My9rOGhjY3hqZUU0aURJenM2M3NUVWJaU3IvRlVCdWdvSVUrdU1jVmhjNHlqUlV1MHFkWEU1VzhCcDZSMXU3S0JWQTdWaVBaQ3ZtWkZDSGpTRGQ0ZVRMd1FMTkFlemVMWTdVYUhGaXFmL3VFODlSeFREckR0RU94N0dOTlRManBHd2d1ekNUcHorM0ZNaXMiLCJtYWMiOiI4ZjgzZjE4ZjY4MzcyMTczM2FjOTQ4NmMxM2Q2NWQ3MDhiYzEzZjQxZGZkNTA3Nzg5ZDVkM2NmZWY5ZjQyODFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1268818041 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:26:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii95VVFCUDM0S0o4Z1BOWHdmSm1PUXc9PSIsInZhbHVlIjoiVWQ1c3o4a29JRHFRWWpsKzZoVVYxaFBvRTR4blJ6TWx2SFJnY1gyTE4yQnU4b05kamRidHJTTldUY3NCbi8rb0pZelM1Z3ExYW52d3FHdWtwUEZIdzZFa1FPNk8ya1J6Tk9nODFrdTJ0MSt4dE9RQS9Gb2R0OTUrSjdDR1Vwa28vNkNtaVJZM2pNb1IvTk9GSUJSUDRXUHpPWnBYUWRlRVJpWVpJVEhtWS94NEFMeitubFZLTTJWWCtnQnQ0M2pHRTEyNlFBRHlwcnVyeW1iOFBLbUJUZEpvOWJHTUgzWXZranQ3Uk1NSGU4RW8wMVA4dWZOV2QvNmdFWUd3eGI2RkJGalBNQnVydkFwVW1oeE1uTWVsdG1UbVgvVTRYZDlHZHFhMFI0UFkvM0JqdERWSEJ6UHFZU21jd1VpUGVMV3I1NFZIYnhwbXVJUVZRNFpOUTl2TCtSLzlCemZKVUxiOUdCR051b2F6Q1lCUDVaMksvZWtweWNhYzNZKzlKRC9OZmYyT1Y0bDhGOGhWWDBhU1NZOVJqWCt3ZTJkNkowQUdqbkVSdFltbG0ydlp0YVA2NzgzdXV5bDZSRnhWd05ndDF3QkZ3YUhhcXhLVnBvVjY3NjBoNmFLYnJEZHZzTmg5WHIyY1RKNlRTdXdvcTg0L0RGMitlNjAvYmQrMXhsWG4iLCJtYWMiOiI4Mzg0MTA3ZTE3NGJlMTE4YTJhZmVlYzljYTg1N2Y3MTE2ZTMyNjJiMDQ2MGI5OTc0MDI3ZmEzMjIyYjgxY2I0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpwdmcxVldYWFBIbEVsMnNMYXBQMWc9PSIsInZhbHVlIjoiUUhCRGRPMUpJRzJ4RzVmdG5NUFpPUW8zckFvejVpRld0cWNhblpDL1FOQ2FrdWlhVUNrUzVBYjRCczR3RmxLNUZlUlVNUGdjRzNnZXJ2Ym5DZTloR3ZUVDJacVU0WUxOdjYrSkRMem1jVjkvb3NCem5qdDBrZkF6MEZvdWxUSHNwalZmeG5GSnpGMmJIbXZ3b2pSTVVtTDdXK2pWMUlvVFFRUTNyTDRINkxZQ1JLNmZMc2xvV1dqVWxCM2ZTSWhxeHpCTlN6SUpTejBPRXFmMktKcC9xNEIzR2Q3UHpLUGFvRFRrMEhEdzFSVWVkQnFmVDAyYVJmOHZtUTVRTi9oVDZoWVM0czVDUHJjL0hnODZ1TTY2OG5ET05tdkVLcXBXUnVodTZyaG9lYXpNTW5Lem9pc0dkamc5SjVYT0NKOUc3QmFVY0IrZ1crV3d2bTBwdkRrdmVCSE9UOWRRcWhDMVBtby9BYTF2V05NdW9NQ1UwZU9iemdCb3NNQmZxV0pTSmhpZlc4N3pTNzBCbU9vSGlVRjk0OG1NRkp0Znh5U0I3WktMd3ZtK1JoeisvejdGaW1ZSFRLc200eWZtRkRGenFWMFZ4aGNGR3NLdStrbzN4cG1uSWE1aHRPVlpROVJwUmJjNlRRT2ZIR3ZhekN5TG9ybmNicEZOcFRBeFdKYk4iLCJtYWMiOiJkYjhkOTc1ZTE5NmQ3Mjg2YWRhNzM1OWViZDhkNTYxOTg2NTE0MGIxMDAxOGFjODhmMGZjMWY0MGMwYzk1MTAzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii95VVFCUDM0S0o4Z1BOWHdmSm1PUXc9PSIsInZhbHVlIjoiVWQ1c3o4a29JRHFRWWpsKzZoVVYxaFBvRTR4blJ6TWx2SFJnY1gyTE4yQnU4b05kamRidHJTTldUY3NCbi8rb0pZelM1Z3ExYW52d3FHdWtwUEZIdzZFa1FPNk8ya1J6Tk9nODFrdTJ0MSt4dE9RQS9Gb2R0OTUrSjdDR1Vwa28vNkNtaVJZM2pNb1IvTk9GSUJSUDRXUHpPWnBYUWRlRVJpWVpJVEhtWS94NEFMeitubFZLTTJWWCtnQnQ0M2pHRTEyNlFBRHlwcnVyeW1iOFBLbUJUZEpvOWJHTUgzWXZranQ3Uk1NSGU4RW8wMVA4dWZOV2QvNmdFWUd3eGI2RkJGalBNQnVydkFwVW1oeE1uTWVsdG1UbVgvVTRYZDlHZHFhMFI0UFkvM0JqdERWSEJ6UHFZU21jd1VpUGVMV3I1NFZIYnhwbXVJUVZRNFpOUTl2TCtSLzlCemZKVUxiOUdCR051b2F6Q1lCUDVaMksvZWtweWNhYzNZKzlKRC9OZmYyT1Y0bDhGOGhWWDBhU1NZOVJqWCt3ZTJkNkowQUdqbkVSdFltbG0ydlp0YVA2NzgzdXV5bDZSRnhWd05ndDF3QkZ3YUhhcXhLVnBvVjY3NjBoNmFLYnJEZHZzTmg5WHIyY1RKNlRTdXdvcTg0L0RGMitlNjAvYmQrMXhsWG4iLCJtYWMiOiI4Mzg0MTA3ZTE3NGJlMTE4YTJhZmVlYzljYTg1N2Y3MTE2ZTMyNjJiMDQ2MGI5OTc0MDI3ZmEzMjIyYjgxY2I0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpwdmcxVldYWFBIbEVsMnNMYXBQMWc9PSIsInZhbHVlIjoiUUhCRGRPMUpJRzJ4RzVmdG5NUFpPUW8zckFvejVpRld0cWNhblpDL1FOQ2FrdWlhVUNrUzVBYjRCczR3RmxLNUZlUlVNUGdjRzNnZXJ2Ym5DZTloR3ZUVDJacVU0WUxOdjYrSkRMem1jVjkvb3NCem5qdDBrZkF6MEZvdWxUSHNwalZmeG5GSnpGMmJIbXZ3b2pSTVVtTDdXK2pWMUlvVFFRUTNyTDRINkxZQ1JLNmZMc2xvV1dqVWxCM2ZTSWhxeHpCTlN6SUpTejBPRXFmMktKcC9xNEIzR2Q3UHpLUGFvRFRrMEhEdzFSVWVkQnFmVDAyYVJmOHZtUTVRTi9oVDZoWVM0czVDUHJjL0hnODZ1TTY2OG5ET05tdkVLcXBXUnVodTZyaG9lYXpNTW5Lem9pc0dkamc5SjVYT0NKOUc3QmFVY0IrZ1crV3d2bTBwdkRrdmVCSE9UOWRRcWhDMVBtby9BYTF2V05NdW9NQ1UwZU9iemdCb3NNQmZxV0pTSmhpZlc4N3pTNzBCbU9vSGlVRjk0OG1NRkp0Znh5U0I3WktMd3ZtK1JoeisvejdGaW1ZSFRLc200eWZtRkRGenFWMFZ4aGNGR3NLdStrbzN4cG1uSWE1aHRPVlpROVJwUmJjNlRRT2ZIR3ZhekN5TG9ybmNicEZOcFRBeFdKYk4iLCJtYWMiOiJkYjhkOTc1ZTE5NmQ3Mjg2YWRhNzM1OWViZDhkNTYxOTg2NTE0MGIxMDAxOGFjODhmMGZjMWY0MGMwYzk1MTAzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268818041\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1293926023 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293926023\", {\"maxDepth\":0})</script>\n"}}