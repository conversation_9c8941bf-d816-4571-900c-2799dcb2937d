{"__meta": {"id": "X16f3a0cee76d4cdc5a7ed469c4988fa4", "datetime": "2025-06-06 19:13:36", "utime": **********.997308, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237215.46567, "end": **********.997341, "duration": 1.5316708087921143, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": 1749237215.46567, "relative_start": 0, "end": **********.795353, "relative_end": **********.795353, "duration": 1.3296828269958496, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.795375, "relative_start": 1.329704999923706, "end": **********.997344, "relative_end": 3.0994415283203125e-06, "duration": 0.20196890830993652, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44776656, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00926, "accumulated_duration_str": "9.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.911899, "duration": 0.00571, "duration_str": "5.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.663}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.948055, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.663, "width_percent": 11.771}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.955164, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 73.434, "width_percent": 10.907}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9729118, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.341, "width_percent": 15.659}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2056382902 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2056382902\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-984344492 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-984344492\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-965453018 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965453018\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-289808276 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im12SHBIWElreDYyMVYxeHk5SnZRZHc9PSIsInZhbHVlIjoiQ3FER1B4T0ZCZU1qakJvSWlQTUd2U3FyK0ZPaUY3cktieUMycXdpeU1WZjVhNllzK0lpRm9MdC9rTlFNSllqTjIvZk1EV3VNdXpyY2lmdExnamtxWHJ2d2xUUVVOcXppVkpXc3hmZDRLVHBMaERCMEdvNkpENXNYeExhTGNxMVgycVU4WStsZmhGU3FxeGVkZkRsaldFK013aUNBREpjOVFMUzlDcitXVEZybzdRcnQ1enR4TzQ0Sm52MW9jS0ZTRlh0QzlDc2QzVndiWUFYdGpvanB4cys5WTZXZ05hcVgxcyt3cTZqWFg0SStnVGVmU3JpclZ4cHZVYVVmcUN2elREUjNJS2ZXNHE4L2RCRUM2REcwK0FvaVQrbFJVQmQ2ZzE2UUxTV3lFK3VtSFByNnUrWEtTajJSa0tSWjJvbjdNWFY5RGwxazJQSTlhTGkwVzJ1b3FER3JkQjlyT09Wcm1ycGttbWZjWDdDT1c4ZldoOUN3ci9NUllPWFZpU1hKWDZraGUvQTduWC9QS1lJSVFRSmsyRGF5WWlaTmRwd2dXUFA5TDZnR1MxYlo3NG44QVpTWFhsMFBDalZ0WGtaNVdJUTM0MzlGR255aXZiTE9kUHlqZlZSZ2ZkbGo1UkhJd0ZBRjVreWlqSDh3V1BlbnNieDAvSUR6VTNjNEpCL0MiLCJtYWMiOiIzMmRmN2FmNjEzYTM0MmI1NmM4YzcxMDhmYjZkMmM3ZmVjZDMxNGRjOWYyZGU3NjM0OTU3YmVkZGEzYzQ4ZWY1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkY3YVhjdG4rTXBqSEpjODE5aVN3QlE9PSIsInZhbHVlIjoiMEs1TFJteWE3SzVrNFZ2R2I4aTF5Y1Y2ZjVqSjlkRUtqNStEZG12UXRIRHAwR0hIRktGNUZVN3gxT2RidG1FTnpCV2p4UFJxMldKOWZuaXV3YnpLL3BNSENCeEViaXNrc3VKY2l4SHdQRHlqSWFvT3JFdDdFbkZRaVF0RW4xN2lDRmtHNTgrcEtobVJtNDlWUXNWbVNvTE5kajlJVmt4TERTaVVoT0ZPM3lydU5XM0k4K3FmN0xFTGFGaHBMV3dwbVpqcFNtdEZ3R1ltNVZnaHh4RERWNld2TlBCQ2c3eHJEOENaWFV6TFZJSUFTSmVqT1ozblplU0dTNU5Ndm94aTUwMWVGbG05elZHd1hlR2xPRHZWT1VqaGtvMG8zd2drcTZNRjJOWTBkQVJ2QUlsMitYSlF3TDNrZmFPczdyMEl6OG1kNWpocDBja3JFRVRUSGd0YUdRMXoySzBGSitheGpubE5vSm9sYjAzZUVlZjl6SWp2bndCWVhtbWN0d01FNUtxajU4MEM3UytSQUxBUGhJZUYvN0txd0ZuYWJXMHlDY1dvOU8wMHNsaEo4SC9CK2wrbXJYMjhzYUo3d251d3hKZm9GWVlQRkZJVlN3dDZFUzNEZ3hEVFlYbjE5V3RId2MwcC9FNHpmdVVhalJIMnpHVlZzaWExODRTMnl5cVkiLCJtYWMiOiJiOWJiNThhNGMyZTZlYWViZDcwZDVlOTRhNTdhMWQwZTUzNzJhMjFmNzNjZjM3YmNiZTJiMGE0ZmFiMDlmOWFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289808276\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1273575585 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273575585\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1658351481 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJRTUNudVRCUjFiaklkQmo2U3R3cVE9PSIsInZhbHVlIjoiVmtRTXczblZCOXVBZWxZKzk4U2pUYTZibkNSajRQMitwSWtxNVRmbElzMGtURGNiNHJCQk9CR3ZCL1dyek1GUjIrWXgxVy9ONnVTOVNuTlJGS3JkNHR0T3BrM2VNUEJiZFR4YVZsWHdyQmN2WTQ1V3l1T3JUQjZXV2lLTnIwemtrdE5ObnM5NXdaM0dGbmhaY0tXOE1IN1pGdURIZjhIa3E4aVJMRUdiZXlkZ1JVRFJuNjNCOGRjUzl0OWVhajMvT1F1UUZwdUZqUzlLM09FU1p2NEVsdGo3MTJHWDJUSFA5SGZZcXdpSHhXUnlJVk1CZjJLSE9CZnJZVWZHaFV5RDVzNTNRTC8ya2pYTVRRK2V5U3hlblZTck9VTnM1T2NzZlRiM01zQUNKTEE4bW9SUnE3S2x6alpRV2lrb3B0SmE0QldUSnkvRmMwUFpic25Ddk1VeWt1VnJDU1owNXRrNmE1SUh1UEZncHZOVkljVzMxdE96c3phckdiNElsTUpCZzFmZDMwcTRxdjBTVFI1S2ZtZlhzb0p5aGxTM3NxZVRjcWJrSzc3NFMra1dqcUZxUEZRcTZZNmg1dGJVMzJnbEtndWE3anlLNytLY1A1b014WDd1TyswMGpISlZvckNmdDc5TWY0ekk2UGdqUVRRWFMzV2tNNnp2NjFXTExoY1giLCJtYWMiOiI4YjI3MjlmMzhiYTZkYzc1NTkwYzdlZTc1NDA2ZjI5Yzk0ZWZlNWNhODI5ZjcwNDZjZDczN2YxMmI3MTI3ZDVjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:36 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InEyNXArRHZWRG91Q3ZScDNMRllxOEE9PSIsInZhbHVlIjoiTnZBMGQ2ampzTUQrVjJhcmRCOEVMdUN0UDNIY3FJZy93azVtcnlROFlKekpEbzdvdG0zdmJjRlZ2K09oc0p5ZjJVbjJaY1k4TWFtSnhhbHEzTnRvQXNoRjZFa1BCcDdrOTNyV2tsTzB0K2hoS2twK3NBM3E1Yjl0anZsQm9hdlh2NHNLNzg3cnRodDNQUnhkMVZWTHZac1Z2UExPc2J0TkNFbUg5YUJ0SzlIUHVwWkl5KzVPNUFHOFMxRlhBQXlET3VFVThrOGhBa2ZqVkI0b1oxcVhlNGYvTkZSTDdYSThsbkpEL1VVc0srMUVhM3FqVW14NDNjSEtBNVB1RkFoM3ZPdE9hWlBhenlRMjFvK2lkOVMzSko4V1diRE5uN2haL0JIVWZha29lV1h5emdHMW5KQU5Zc3FWcU9ROFdiS0ZlNVpjT3FVcVNyNFpGTXcyckhtNC8vMTEweVlOYXlLMjJFbGRNY2NQNWhLeC9VRm1SK2RsOGdjVGM3Q3FHM0pNYkRnSEFXcGNjMktoYThoYm9SNXEvcmoxZFNtamsvazIvMytzamo5aS9BNStCeUN3MUxWWDdDcWo1SU0rOEhNV21BWng0VUdNY2NkTXZQODNadjZBRnRic3RGK0p1TWJSMGdJUmt5S0dKYktzb2pGRmVqZXRPNUFZbU52Ymd4TzciLCJtYWMiOiJlMjNiODkxNzA2MjhkZDRlZTFkYjlkMGU5NmUwYjcwNWViYmI5YTI2YTRiMmEzMzljYjdiNjdkYzliNmIzYjI5IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:36 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJRTUNudVRCUjFiaklkQmo2U3R3cVE9PSIsInZhbHVlIjoiVmtRTXczblZCOXVBZWxZKzk4U2pUYTZibkNSajRQMitwSWtxNVRmbElzMGtURGNiNHJCQk9CR3ZCL1dyek1GUjIrWXgxVy9ONnVTOVNuTlJGS3JkNHR0T3BrM2VNUEJiZFR4YVZsWHdyQmN2WTQ1V3l1T3JUQjZXV2lLTnIwemtrdE5ObnM5NXdaM0dGbmhaY0tXOE1IN1pGdURIZjhIa3E4aVJMRUdiZXlkZ1JVRFJuNjNCOGRjUzl0OWVhajMvT1F1UUZwdUZqUzlLM09FU1p2NEVsdGo3MTJHWDJUSFA5SGZZcXdpSHhXUnlJVk1CZjJLSE9CZnJZVWZHaFV5RDVzNTNRTC8ya2pYTVRRK2V5U3hlblZTck9VTnM1T2NzZlRiM01zQUNKTEE4bW9SUnE3S2x6alpRV2lrb3B0SmE0QldUSnkvRmMwUFpic25Ddk1VeWt1VnJDU1owNXRrNmE1SUh1UEZncHZOVkljVzMxdE96c3phckdiNElsTUpCZzFmZDMwcTRxdjBTVFI1S2ZtZlhzb0p5aGxTM3NxZVRjcWJrSzc3NFMra1dqcUZxUEZRcTZZNmg1dGJVMzJnbEtndWE3anlLNytLY1A1b014WDd1TyswMGpISlZvckNmdDc5TWY0ekk2UGdqUVRRWFMzV2tNNnp2NjFXTExoY1giLCJtYWMiOiI4YjI3MjlmMzhiYTZkYzc1NTkwYzdlZTc1NDA2ZjI5Yzk0ZWZlNWNhODI5ZjcwNDZjZDczN2YxMmI3MTI3ZDVjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InEyNXArRHZWRG91Q3ZScDNMRllxOEE9PSIsInZhbHVlIjoiTnZBMGQ2ampzTUQrVjJhcmRCOEVMdUN0UDNIY3FJZy93azVtcnlROFlKekpEbzdvdG0zdmJjRlZ2K09oc0p5ZjJVbjJaY1k4TWFtSnhhbHEzTnRvQXNoRjZFa1BCcDdrOTNyV2tsTzB0K2hoS2twK3NBM3E1Yjl0anZsQm9hdlh2NHNLNzg3cnRodDNQUnhkMVZWTHZac1Z2UExPc2J0TkNFbUg5YUJ0SzlIUHVwWkl5KzVPNUFHOFMxRlhBQXlET3VFVThrOGhBa2ZqVkI0b1oxcVhlNGYvTkZSTDdYSThsbkpEL1VVc0srMUVhM3FqVW14NDNjSEtBNVB1RkFoM3ZPdE9hWlBhenlRMjFvK2lkOVMzSko4V1diRE5uN2haL0JIVWZha29lV1h5emdHMW5KQU5Zc3FWcU9ROFdiS0ZlNVpjT3FVcVNyNFpGTXcyckhtNC8vMTEweVlOYXlLMjJFbGRNY2NQNWhLeC9VRm1SK2RsOGdjVGM3Q3FHM0pNYkRnSEFXcGNjMktoYThoYm9SNXEvcmoxZFNtamsvazIvMytzamo5aS9BNStCeUN3MUxWWDdDcWo1SU0rOEhNV21BWng0VUdNY2NkTXZQODNadjZBRnRic3RGK0p1TWJSMGdJUmt5S0dKYktzb2pGRmVqZXRPNUFZbU52Ymd4TzciLCJtYWMiOiJlMjNiODkxNzA2MjhkZDRlZTFkYjlkMGU5NmUwYjcwNWViYmI5YTI2YTRiMmEzMzljYjdiNjdkYzliNmIzYjI5IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658351481\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-463169635 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463169635\", {\"maxDepth\":0})</script>\n"}}