{"__meta": {"id": "X2d9272621a02fd5793c1db67eb07fc9b", "datetime": "2025-06-06 19:35:40", "utime": **********.126546, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238538.548571, "end": **********.126572, "duration": 1.578000783920288, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1749238538.548571, "relative_start": 0, "end": 1749238539.956512, "relative_end": 1749238539.956512, "duration": 1.4079408645629883, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749238539.956537, "relative_start": 1.407965898513794, "end": **********.126575, "relative_end": 3.0994415283203125e-06, "duration": 0.17003798484802246, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.008190000000000001, "accumulated_duration_str": "8.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0402792, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.143}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.07708, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.143, "width_percent": 11.6}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.084723, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 68.742, "width_percent": 17.094}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1068501, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.836, "width_percent": 14.164}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-640229017 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-640229017\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1592546097 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1592546097\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-7750435 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7750435\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1245348264 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238516680%7C47%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRFaEdZSW14TWp2YUFXYTkzZWlWbnc9PSIsInZhbHVlIjoiaXhiOWY5L1lYcXh5bUhXbm42T1F5L1VZSjZrYjRtNU5qdmV2NzNPVVIvWm5VbDBNUVkydXNTcFNvME1kc09Pc3VjeHpMWlJwaktnU2UwdlVsYUJRTFo4NzB2eUF2bUxRV3ZrVXVuc0NvWE1LdWQrNmNOdkE3cWZTbTBLa1lGSjZLN0grVFZ3ckxKOGxic2ExMHFQMWU4TW16NHMxd3lYNzBFcXUxbTF1L3o5OUJXczlWNmtuZDAxTE9WdmxtUGp5UEdxeDZROG5Xc1hPelNTRlVKQ2hsM3Vtdzd6dFp5TjdZTVVadC9qRzN1WUw4dklqbHVwcVZ4c2EvYmI3UFdaa2V3dUlwempXWE90UFVuUVhXZ05rUUdjd1Rrd1JSMVdOTmdKaEpqR0VBeUlxenc2ajl0N1RJNitrYWVLTXhxUXptL0NlZU16bzJ1MGY2Y0VjM3ZGc0JwTXpLTm01a2d3bXI0K3QzaW95dU1Tb3E5eUFBNk56ajhCZ1hHMlBjYlorV0I1QlgwdC9BQ3RnR25Md0FuZ2QyMklwWlIwaHFFWjlZUHdYTGp2RXJVUzZlRVBKc3MzeUxmM0U3b3VZZEJSZmllT2l5VnJFanh1MTcvY05vUVR5OW4yZjhmUGF6cTdFVzgxSDBFei83WGtvY3A5YmQvcTAzTm9pSkhab1BHRVQiLCJtYWMiOiI3NjAyNjY0MTdmNWU1NzUwM2RjNGIyNGI4ZWM1ZDVhYmVjZWE0ZWJkZTVlNTEwMjFmMDJjNTJhNDY0NGMwYTk1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJCSDRSejFKVmNXRXIvYlVROHFRbkE9PSIsInZhbHVlIjoiY2hXTVpZZEJIdkdpenh2U3EvSGsrSTFtUmorSFFiRnViV2NJemJsbXpJRXlnajFQejdyeS9LUlZyNnFqVjFGRCs3c3l3ZlUzb1hzZE01ajRVM3NmcmpOc1RTVmFDaUJjK3RqNEF1ZnRyazR0TWEwUUhnRmlQVU12cHdocFBHeVA3UTVkTU5tWkZpL1NHei94Y1U1eTg0QVRkOGxMNEtieTUzeVllTGxTbFJwQVJOTWlVNlBjTXY4RzE1SWFuU0Q1Smt5dWxNdHNTOUF1TE9ETHkzeklBQ2prQmdBc0JGdWZRdVRiN3hIN0xaRHJTMzh5OTdGR1dvOWExZTVML1h5TXp5MURKcXBkQThGTkt2M3VuQkJ1REo5ZkVqaThEZ0dBbGcyWlVZZ3N3cGtJbGFYa0ZiVEliRFZPRmthU1dNeDltYTNsenkyU1RGaHRFSFBmSUs2TUR0YW5FQWdSWWxIRVJFdjY0dnBmb2R6aVhqWnhwM2lWUjVGTktJSGtDeUVHU1A1WVd6ZlRXR3ZiMmFZRXBpQTNHRUlkRWZieHdUTG8yNUZLcERnQnNEdS9yV3ByOFBmV0hQb1NUUlMxbTBwdndZa2w0ZWpHK3hpbzd4VmdqcTZra1pybTdDTSsxcUI3cWI0RzVQNGJPbFZZeEtYT056NytWTEtzdXN4T0lWSm0iLCJtYWMiOiI0NTNiODBjN2E2MjBiYzVlOWI5ZWE1N2RmN2RhM2ViYzRiZmRiMTY0OTAyMTU1MzBkMDQ4OTliYTQxYmY5MDlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245348264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1904346641 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904346641\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:35:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVsbVNEL1BkelIvQlpndzhYbjRiOEE9PSIsInZhbHVlIjoiS2k4RUp5M2ZnUW94VWxhL0h3ZXppZlJlS1hWWVBEYmdBSE95cEZaV3B4Y0lvSFp6SXVqM01LME1BL05VU0FnOE9mdWw2L0FvdVNmUVZhUWMvNUJjcDdqU1lVM1NnbWJCNmp2UmNNWk5JMzBPUnJqeStSTXlrbmZlRkRYZ3cyQkwyb2ZVL3o5c1JOZkZnTXI2WDdMWnNBVS84RXpGczJ5VzNkTUl2UHFLamlWVGxqWnVOczMvR0htcmNFRDY4VC9TQTBUUHZnVVlmYnM5VG5QdytqbGdUdHlSckQxVXJEMTI1QkhkR2hGeUM1R2x3WSt5M0J2YldoT3dhdThpTmRaTjFJNThodTVXU0JrSFVQakdMaUpGVnJFSXhtREQ3UTRSS3RqUXd2ZW9UMzUybGVBZ2htcDFva25pM1dKbHpPTXpFSEtVamtzVFBtWG96aStWYk02NzBFSmlQeXNURUhySEh2K3FDa0FibnNNbEpTeFZJeUhwM3FZeEc2T1Q4a1grTjdFcFRldloyaS9oMDBjdHlpc3ZDOXlTbEpVYmMrT000NWdOVGJhRitjUUtJN3RXd0JZQ0toMUY4Qi9Bc0wwZmFQNXIwbzNPbWNJUTlKak9qTDB4d3I1YTJzeGlsRXE1ZGVxRDliWnVKbU1vTmNEWHNQVDdSbitBWVB4S0E5ZFciLCJtYWMiOiJjNzdmZDA2MjM4NjIyZTU5M2ZjOWI0YWMxYzUzNTU5YTJlMGU5MWM0YWQwZGZhZDhmZWNhM2NhOGFhMWQ3Mjg0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBsZFJRd2x4RDdzWU5EMUcvYnhCOHc9PSIsInZhbHVlIjoiVExZTDRFbHNSQWcrT3ZRdjNvTGhBcm1iL29Wdy9VMW5ndEdQanNQMjBIa0Z4SDJlZHlJZXpqNmRNNmhlcGxNOVVyU1JTeGJ0bTNhS2Q4WWdkWUFjbHNsMzRlcVBUOUt1S2dsNXQyUEdrck95dlNLNmF2RzlmcFFxOXZ5T1BjdCt6ZElnZUNPRUVmQXd2bCszV1B1QkE2TW56d1YwTUtqc3FVT0NmK2ozM2plQnJtU2RNUjlXdzdVTk5ZTHlLbkJ4bzc4VVE2b3dqZWYzbjYvNmRDRlN2T1Qwdi8yeUJYQ3gxRzZQd2ZKbXhRUDd0TGowQm5yQmxTazJNOEltTFhiTHBqOEpQQ3FudEdvM3REZ3F6NFNva1ZvNHlxQTBGN1hvajJUQTBoMS8wR3o0dnJFT09uZkEzMXlxYmVoOVR2UmNseGNnbjgzTHJ2VUE2VlpnTUZhME85N2tQN1U1blo3Tjg0SmREb0NCd21qQ0FaU1NMKzE1Y1Jqb3h6SXlHYUU0b2tZemtYYXNUb2pRRXprUUo0b2NqNGZnV3gwb3BpelgrVEQxbm9CNmZGMk9qbmlsa01UWFZEWE40NGJRNHdVWDk5bTlwS0c2V2V1WEMrK3dWMklXaDVxa3VwenV5RDRWYUI4LzE0empEQXQwanpvU0IrZ0VSeUV6c1RXN2FuTUsiLCJtYWMiOiJmOWFlNmE4MmMxZjU4MTU2ZWY1Zjk1MmE4NWM3YTk3ZTk2YWZiZmQ1MmVkZGRhYjZhMzc5OWE4NjQxNWExNzRiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVsbVNEL1BkelIvQlpndzhYbjRiOEE9PSIsInZhbHVlIjoiS2k4RUp5M2ZnUW94VWxhL0h3ZXppZlJlS1hWWVBEYmdBSE95cEZaV3B4Y0lvSFp6SXVqM01LME1BL05VU0FnOE9mdWw2L0FvdVNmUVZhUWMvNUJjcDdqU1lVM1NnbWJCNmp2UmNNWk5JMzBPUnJqeStSTXlrbmZlRkRYZ3cyQkwyb2ZVL3o5c1JOZkZnTXI2WDdMWnNBVS84RXpGczJ5VzNkTUl2UHFLamlWVGxqWnVOczMvR0htcmNFRDY4VC9TQTBUUHZnVVlmYnM5VG5QdytqbGdUdHlSckQxVXJEMTI1QkhkR2hGeUM1R2x3WSt5M0J2YldoT3dhdThpTmRaTjFJNThodTVXU0JrSFVQakdMaUpGVnJFSXhtREQ3UTRSS3RqUXd2ZW9UMzUybGVBZ2htcDFva25pM1dKbHpPTXpFSEtVamtzVFBtWG96aStWYk02NzBFSmlQeXNURUhySEh2K3FDa0FibnNNbEpTeFZJeUhwM3FZeEc2T1Q4a1grTjdFcFRldloyaS9oMDBjdHlpc3ZDOXlTbEpVYmMrT000NWdOVGJhRitjUUtJN3RXd0JZQ0toMUY4Qi9Bc0wwZmFQNXIwbzNPbWNJUTlKak9qTDB4d3I1YTJzeGlsRXE1ZGVxRDliWnVKbU1vTmNEWHNQVDdSbitBWVB4S0E5ZFciLCJtYWMiOiJjNzdmZDA2MjM4NjIyZTU5M2ZjOWI0YWMxYzUzNTU5YTJlMGU5MWM0YWQwZGZhZDhmZWNhM2NhOGFhMWQ3Mjg0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBsZFJRd2x4RDdzWU5EMUcvYnhCOHc9PSIsInZhbHVlIjoiVExZTDRFbHNSQWcrT3ZRdjNvTGhBcm1iL29Wdy9VMW5ndEdQanNQMjBIa0Z4SDJlZHlJZXpqNmRNNmhlcGxNOVVyU1JTeGJ0bTNhS2Q4WWdkWUFjbHNsMzRlcVBUOUt1S2dsNXQyUEdrck95dlNLNmF2RzlmcFFxOXZ5T1BjdCt6ZElnZUNPRUVmQXd2bCszV1B1QkE2TW56d1YwTUtqc3FVT0NmK2ozM2plQnJtU2RNUjlXdzdVTk5ZTHlLbkJ4bzc4VVE2b3dqZWYzbjYvNmRDRlN2T1Qwdi8yeUJYQ3gxRzZQd2ZKbXhRUDd0TGowQm5yQmxTazJNOEltTFhiTHBqOEpQQ3FudEdvM3REZ3F6NFNva1ZvNHlxQTBGN1hvajJUQTBoMS8wR3o0dnJFT09uZkEzMXlxYmVoOVR2UmNseGNnbjgzTHJ2VUE2VlpnTUZhME85N2tQN1U1blo3Tjg0SmREb0NCd21qQ0FaU1NMKzE1Y1Jqb3h6SXlHYUU0b2tZemtYYXNUb2pRRXprUUo0b2NqNGZnV3gwb3BpelgrVEQxbm9CNmZGMk9qbmlsa01UWFZEWE40NGJRNHdVWDk5bTlwS0c2V2V1WEMrK3dWMklXaDVxa3VwenV5RDRWYUI4LzE0empEQXQwanpvU0IrZ0VSeUV6c1RXN2FuTUsiLCJtYWMiOiJmOWFlNmE4MmMxZjU4MTU2ZWY1Zjk1MmE4NWM3YTk3ZTk2YWZiZmQ1MmVkZGRhYjZhMzc5OWE4NjQxNWExNzRiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-243506912 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243506912\", {\"maxDepth\":0})</script>\n"}}