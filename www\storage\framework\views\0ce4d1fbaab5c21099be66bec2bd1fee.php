<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('System Settings')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('System Settings')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/summernote/summernote-bs4.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/login-customization.css')); ?>">
    <style>
        .list-group-item {
            font-weight: 500;
            color: #495057;
        }
        .list-group-item.active {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
        .card {
            margin-bottom: 1.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('css/summernote/summernote-bs4.js')); ?>"></script>
    <script>
        $(document).ready(function() {
            // Storage settings toggle
            function toggleStorageSettings() {
                var storageType = $('#storage_setting').val();
                $('.s3-settings, .wasabi-settings').hide();

                if (storageType === 's3') {
                    $('.s3-settings').show();
                } else if (storageType === 'wasabi') {
                    $('.wasabi-settings').show();
                }
            }

            // Initialize on page load
            toggleStorageSettings();

            // Toggle on change
            $('#storage_setting').change(function() {
                toggleStorageSettings();
            });

            // Smooth scrolling for navigation links
            $('#useradd-sidenav a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);

                    // Update active state
                    $('#useradd-sidenav a').removeClass('active');
                    $(this).addClass('active');
                }
            });

            // Update active navigation on scroll
            $(window).scroll(function() {
                var scrollPos = $(document).scrollTop() + 150;
                $('#useradd-sidenav a[href^="#"]').each(function() {
                    var currLink = $(this);
                    var refElement = $(currLink.attr("href"));
                    if (refElement.position() && refElement.position().top <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {
                        $('#useradd-sidenav a').removeClass("active");
                        currLink.addClass("active");
                    }
                });
            });

            // Initialize Select2 if available
            if (typeof $.fn.select2 !== 'undefined') {
                $('.select2').select2();
            }

            // File upload preview
            $('.file').on('change', function() {
                var input = this;
                var filename = $(this).data('filename');

                if (input.files && input.files[0]) {
                    var reader = new FileReader();
                    var file = input.files[0];

                    // Update filename display
                    $('.' + filename).text(file.name);

                    // Show image preview for image files
                    if (file.type.startsWith('image/')) {
                        reader.onload = function(e) {
                            var imgElement = $(input).closest('.form-group').find('.logo-content img');
                            if (imgElement.length) {
                                imgElement.attr('src', e.target.result);
                            } else {
                                // Create new image preview if doesn't exist
                                var logoContent = $(input).closest('.form-group').find('.logo-content');
                                logoContent.html('<img src="' + e.target.result + '" class="img-fluid rounded" style="max-height: 150px;">');
                            }
                        };
                        reader.readAsDataURL(file);
                    }

                    // Add success animation
                    $(input).closest('.choose-files').addClass('success');
                    setTimeout(function() {
                        $(input).closest('.choose-files').removeClass('success');
                    }, 600);
                }
            });

            // Color picker preview
            $('#login_primary_color, #login_background_color').on('change', function() {
                var colorValue = $(this).val();
                var colorName = $(this).attr('id');

                // Show color preview
                if (colorName === 'login_primary_color') {
                    $(this).css('border-color', colorValue);
                } else if (colorName === 'login_background_color') {
                    $(this).css('background-color', colorValue);
                }
            });

            // Login customization form validation
            $('#login-customization form').on('submit', function(e) {
                var hasImages = false;
                var hasColors = $('#login_primary_color').val() && $('#login_background_color').val();

                // Check if at least one background image is uploaded
                $('#login-customization input[type="file"]').each(function() {
                    if (this.files && this.files.length > 0) {
                        hasImages = true;
                        return false;
                    }
                });

                if ($('#enable_login_customization').val() === 'on' && !hasImages && !hasColors) {
                    e.preventDefault();
                    alert('<?php echo e(__("Please upload at least one background image or set custom colors when enabling login customization.")); ?>');
                    return false;
                }
            });

            // Image preview functionality
            function previewImage(input, previewContainer, isIcon = false) {
                if (input.files && input.files[0]) {
                    // Show loading state
                    $(previewContainer).addClass('uploading');

                    var reader = new FileReader();
                    reader.onload = function(e) {
                        // Remove loading state
                        $(previewContainer).removeClass('uploading');

                        var imageStyle = isIcon ?
                            'width="64px" class="img-fluid" style="filter: drop-shadow(2px 3px 7px #011c4b);"' :
                            'class="img-fluid rounded" style="max-height: 150px; filter: drop-shadow(2px 3px 7px #011c4b);"';

                        var imageHtml = '<a href="' + e.target.result + '" target="_blank">' +
                                       '<img src="' + e.target.result + '" ' + imageStyle + '>' +
                                       '</a>';
                        $(previewContainer).html(imageHtml);

                        // Add success animation
                        $(previewContainer).find('img').hide().fadeIn(500);
                    }
                    reader.readAsDataURL(input.files[0]);
                } else {
                    // Reset to default state if no file selected
                    var defaultHtml = '<div class="text-center p-3 border rounded">' +
                                     '<i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>' +
                                     '<p class="text-muted mt-2"><?php echo e(__("No image uploaded")); ?></p>' +
                                     '</div>';
                    $(previewContainer).html(defaultHtml);
                }
            }

            // Add event listeners for image preview
            $('#login_bg_image_1').change(function() {
                previewImage(this, '#login-customization .logo-content:eq(0)');
            });

            $('#login_bg_image_2').change(function() {
                previewImage(this, '#login-customization .logo-content:eq(1)');
            });

            $('#login_bg_image_3').change(function() {
                previewImage(this, '#login-customization .logo-content:eq(2)');
            });

            $('#login_custom_logo').change(function() {
                previewImage(this, '#login-customization .logo-content:eq(3)');
            });

            $('#login_favicon').change(function() {
                previewImage(this, '#login-customization .logo-content:eq(4)', true);
            });

            // File validation
            $('input[type="file"]').change(function() {
                var file = this.files[0];
                if (file) {
                    var fileSize = file.size / 1024 / 1024; // Convert to MB
                    var maxSize = 20; // 20MB

                    if (fileSize > maxSize) {
                        alert('<?php echo e(__("File size must be less than 20MB")); ?>');
                        $(this).val('');
                        return false;
                    }

                    var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
                    if (this.id === 'login_favicon') {
                        allowedTypes.push('image/x-icon', 'image/vnd.microsoft.icon');
                    }

                    if (allowedTypes.indexOf(file.type) === -1) {
                        alert('<?php echo e(__("Please select a valid image file")); ?>');
                        $(this).val('');
                        return false;
                    }
                }
            });
        });
    </script>

    <!-- Login Customization Enhanced JavaScript -->
    <script src="<?php echo e(asset('assets/js/login-customization.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="row">
                <div class="col-xl-3">
                    <div class="card sticky-top" style="top:30px; z-index:unset;">
                        <div class="list-group list-group-flush" id="useradd-sidenav">
                            <a href="#brand-settings"
                                class="list-group-item list-group-item-action border-0">Brand Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#system-settings"
                                class="list-group-item list-group-item-action border-0">System Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#email-settings"
                                class="list-group-item list-group-item-action border-0">Email Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#payment-settings"
                                class="list-group-item list-group-item-action border-0">Payment Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#storage-settings"
                                class="list-group-item list-group-item-action border-0">Storage Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#pusher-settings"
                                class="list-group-item list-group-item-action border-0">Pusher Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#recaptcha-settings"
                                class="list-group-item list-group-item-action border-0">reCAPTCHA Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#seo-settings"
                                class="list-group-item list-group-item-action border-0">SEO Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#cookie-settings"
                                class="list-group-item list-group-item-action border-0">Cookie Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <?php if(\Auth::user()->type == 'super admin'): ?>
                            <a href="#login-customization"
                                class="list-group-item list-group-item-action border-0">Login Page Customization
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <?php endif; ?>
                            <a href="#cache-settings"
                                class="list-group-item list-group-item-action border-0">Cache Settings
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-xl-9">
                    <!-- Brand Settings -->
                    <div id="brand-settings" class="card">
                        <?php echo e(Form::open(['route' => 'systems.store', 'method' => 'POST', 'enctype' => 'multipart/form-data'])); ?>

                        <div class="card-header">
                            <h5>Brand Settings</h5>
                            <small class="text-muted">Edit your brand settings including logos, colors, and themes</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('logo_dark', 'Logo Dark', ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="logo_dark">
                                                <div class="bg-primary company_logo_update"> <i class="ti ti-upload px-1"></i>Choose file here</div>
                                                <input type="file" class="form-control file" name="logo_dark" id="logo_dark" data-filename="logo_dark_update" accept="image/*">
                                            </label>
                                            <p class="logo_dark_update"></p>
                                            <small class="text-muted">Supported formats: JPG, JPEG, PNG, GIF, BMP, WEBP (Max: 20MB)</small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php if(file_exists(public_path('storage/logo/logo-dark.png'))): ?>
                                                <a href="<?php echo e(asset('storage/logo/logo-dark.png')); ?>" target="_blank">
                                                    <img src="<?php echo e(asset('storage/logo/logo-dark.png')); ?>" class="logo logo-lg" style="filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2">No logo uploaded</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('logo_light', 'Logo Light', ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="logo_light">
                                                <div class="bg-primary company_logo_update"> <i class="ti ti-upload px-1"></i>Choose file here</div>
                                                <input type="file" class="form-control file" name="logo_light" id="logo_light" data-filename="logo_light_update" accept="image/*">
                                            </label>
                                            <p class="logo_light_update"></p>
                                            <small class="text-muted">Supported formats: JPG, JPEG, PNG, GIF, BMP, WEBP (Max: 20MB)</small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php if(file_exists(public_path('storage/logo/logo-light.png'))): ?>
                                                <a href="<?php echo e(asset('storage/logo/logo-light.png')); ?>" target="_blank">
                                                    <img src="<?php echo e(asset('storage/logo/logo-light.png')); ?>" class="logo logo-lg" style="filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2">No logo uploaded</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('favicon', 'Favicon', ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="favicon">
                                                <div class="bg-primary company_logo_update"> <i class="ti ti-upload px-1"></i>Choose file here</div>
                                                <input type="file" class="form-control file" name="favicon" id="favicon" data-filename="favicon_update" accept="image/*,.ico">
                                            </label>
                                            <p class="favicon_update"></p>
                                            <small class="text-muted">Supported formats: ICO, PNG, JPG, JPEG, GIF, BMP, WEBP (Max: 20MB)</small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php if(file_exists(public_path('storage/logo/favicon.png'))): ?>
                                                <a href="<?php echo e(asset('storage/logo/favicon.png')); ?>" target="_blank">
                                                    <img src="<?php echo e(asset('storage/logo/favicon.png')); ?>" width="80px" class="logo logo-lg" style="filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2">No favicon uploaded</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('title_text', 'Application Name *', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('title_text', isset($settings['title_text']) ? $settings['title_text'] : '', ['class' => 'form-control', 'required' => 'required', 'placeholder' => 'Enter Application Name'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('footer_text', 'Application Description', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('footer_text', isset($settings['footer_text']) ? $settings['footer_text'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Application Description'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('default_language', 'Default Language', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('default_language', ['en' => 'English', 'ar' => 'Arabic', 'es' => 'Spanish', 'fr' => 'French', 'de' => 'German'], isset($settings['default_language']) ? $settings['default_language'] : 'en', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('SITE_RTL', 'Enable RTL', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('SITE_RTL', ['off' => 'Disable', 'on' => 'Enable'], isset($settings['SITE_RTL']) ? $settings['SITE_RTL'] : 'off', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('display_landing_page', 'Enable Landing Page', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('display_landing_page', ['off' => 'Disable', 'on' => 'Enable'], isset($settings['display_landing_page']) ? $settings['display_landing_page'] : 'on', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('enable_signup', 'Enable User Registration', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('enable_signup', ['off' => 'Disable', 'on' => 'Enable'], isset($settings['enable_signup']) ? $settings['enable_signup'] : 'on', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('email_verification', 'Email Verification', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('email_verification', ['off' => 'Disable', 'on' => 'Enable'], isset($settings['email_verification']) ? $settings['email_verification'] : 'off', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                Save Changes
                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- System Settings -->
                    <div id="system-settings" class="card">
                        <?php echo e(Form::open(['route' => 'system.settings', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5><?php echo e(__('System Settings')); ?></h5>
                            <small class="text-muted"><?php echo e(__('Edit your system settings')); ?></small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('title_text', __('Application Name *'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('title_text', isset($settings['title_text']) ? $settings['title_text'] : '', ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Application Name')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('footer_text', __('Application Description'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('footer_text', isset($settings['footer_text']) ? $settings['footer_text'] : '', ['class' => 'form-control', 'placeholder' => __('Enter Application Description')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('default_language', __('Default Language'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('default_language', ['en' => 'English', 'ar' => 'Arabic', 'es' => 'Spanish', 'fr' => 'French', 'de' => 'German'], isset($settings['default_language']) ? $settings['default_language'] : 'en', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('timezone', __('Timezone'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('timezone', ['UTC' => 'UTC', 'Asia/Riyadh' => 'Asia/Riyadh', 'America/New_York' => 'America/New_York', 'Europe/London' => 'Europe/London'], isset($settings['timezone']) ? $settings['timezone'] : 'UTC', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('site_currency', __('Currency *'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('site_currency', isset($settings['site_currency']) ? $settings['site_currency'] : '', ['class' => 'form-control font-style', 'required' => 'required', 'placeholder' => __('Enter Currency Code (e.g., USD)')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('site_currency_symbol', __('Currency Symbol *'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('site_currency_symbol', isset($settings['site_currency_symbol']) ? $settings['site_currency_symbol'] : '', ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Currency Symbol (e.g., $)')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('site_currency_symbol_position', __('Currency Symbol Position'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('site_currency_symbol_position', ['pre' => __('Before Amount ($100)'), 'post' => __('After Amount (100$)')], isset($settings['site_currency_symbol_position']) ? $settings['site_currency_symbol_position'] : 'pre', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('decimal_number', __('Decimal Places'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('decimal_number', ['0' => '0', '1' => '1', '2' => '2', '3' => '3', '4' => '4'], isset($settings['decimal_number']) ? $settings['decimal_number'] : '2', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('site_date_format', __('Date Format'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('site_date_format', ['M j, Y' => 'Jan 1, 2015', 'd-m-Y' => '01-01-2015', 'm-d-Y' => '01-01-2015', 'Y-m-d' => '2015-01-01'], isset($settings['site_date_format']) ? $settings['site_date_format'] : 'M j, Y', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('site_time_format', __('Time Format'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('site_time_format', ['g:i A' => '12:01 PM', 'g:i a' => '12:01 pm', 'H:i' => '12:01'], isset($settings['site_time_format']) ? $settings['site_time_format'] : 'g:i A', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('enable_signup', __('Enable User Registration'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('enable_signup', ['on' => __('Enable'), 'off' => __('Disable')], isset($settings['enable_signup']) ? $settings['enable_signup'] : 'on', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('email_verification', __('Email Verification'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('email_verification', ['on' => __('Enable'), 'off' => __('Disable')], isset($settings['email_verification']) ? $settings['email_verification'] : 'off', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                <?php echo e(__('Save Changes')); ?>

                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- Email Settings -->
                    <div id="email-settings" class="card">
                        <?php echo e(Form::open(['route' => 'email.settings', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5>Email Settings</h5>
                            <small class="text-muted">Configure your SMTP email settings</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_driver', 'Mail Driver', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('mail_driver', isset($settings['mail_driver']) ? $settings['mail_driver'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Mail Driver (e.g., smtp)'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_host', 'Mail Host', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('mail_host', isset($settings['mail_host']) ? $settings['mail_host'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Mail Host'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_port', 'Mail Port', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('mail_port', isset($settings['mail_port']) ? $settings['mail_port'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Mail Port'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_username', 'Mail Username', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('mail_username', isset($settings['mail_username']) ? $settings['mail_username'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Mail Username'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_password', 'Mail Password', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::password('mail_password', ['class' => 'form-control', 'placeholder' => 'Enter Mail Password', 'value' => isset($settings['mail_password']) ? $settings['mail_password'] : ''])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_encryption', 'Mail Encryption', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('mail_encryption', ['tls' => 'TLS', 'ssl' => 'SSL'], isset($settings['mail_encryption']) ? $settings['mail_encryption'] : 'tls', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_from_address', 'Mail From Address', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::email('mail_from_address', isset($settings['mail_from_address']) ? $settings['mail_from_address'] : '', ['class' => 'form-control', 'placeholder' => 'Enter From Email Address'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('mail_from_name', 'Mail From Name', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('mail_from_name', isset($settings['mail_from_name']) ? $settings['mail_from_name'] : '', ['class' => 'form-control', 'placeholder' => 'Enter From Name'])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                Save Changes
                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- Payment Settings -->
                    <div id="payment-settings" class="card">
                        <?php echo e(Form::open(['route' => 'payment.settings', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5><?php echo e(__('Payment Settings')); ?></h5>
                            <small class="text-muted"><?php echo e(__('Edit your payment gateway settings')); ?></small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('currency', __('Payment Currency'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('currency', isset($admin_payment_setting['currency']) ? $admin_payment_setting['currency'] : '', ['class' => 'form-control font-style', 'placeholder' => __('Enter Currency Code (e.g., USD)')])); ?>

                                        <small class="text-xs">
                                            <?php echo e(__('Note: Add currency code as per three-letter ISO code')); ?>

                                            <a href="https://stripe.com/docs/currencies" target="_blank"><?php echo e(__('you can find out here')); ?></a>.
                                        </small>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('enable_stripe', __('Enable Stripe'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('enable_stripe', ['on' => __('Enable'), 'off' => __('Disable')], isset($admin_payment_setting['enable_stripe']) ? $admin_payment_setting['enable_stripe'] : 'off', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('stripe_key', __('Stripe Publishable Key'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('stripe_key', isset($admin_payment_setting['stripe_key']) ? $admin_payment_setting['stripe_key'] : '', ['class' => 'form-control', 'placeholder' => __('Enter Stripe Publishable Key')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('stripe_secret', __('Stripe Secret Key'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::password('stripe_secret', ['class' => 'form-control', 'placeholder' => __('Enter Stripe Secret Key'), 'value' => isset($admin_payment_setting['stripe_secret']) ? $admin_payment_setting['stripe_secret'] : ''])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('enable_paypal', __('Enable PayPal'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('enable_paypal', ['on' => __('Enable'), 'off' => __('Disable')], isset($admin_payment_setting['enable_paypal']) ? $admin_payment_setting['enable_paypal'] : 'off', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('paypal_mode', __('PayPal Mode'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('paypal_mode', ['sandbox' => __('Sandbox'), 'live' => __('Live')], isset($admin_payment_setting['paypal_mode']) ? $admin_payment_setting['paypal_mode'] : 'sandbox', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('paypal_client_id', __('PayPal Client ID'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('paypal_client_id', isset($admin_payment_setting['paypal_client_id']) ? $admin_payment_setting['paypal_client_id'] : '', ['class' => 'form-control', 'placeholder' => __('Enter PayPal Client ID')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('paypal_secret_key', __('PayPal Secret Key'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::password('paypal_secret_key', ['class' => 'form-control', 'placeholder' => __('Enter PayPal Secret Key'), 'value' => isset($admin_payment_setting['paypal_secret_key']) ? $admin_payment_setting['paypal_secret_key'] : ''])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                <?php echo e(__('Save Changes')); ?>

                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- Storage Settings -->
                    <div id="storage-settings" class="card">
                        <?php echo e(Form::open(['route' => 'storage.setting.store', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5><?php echo e(__('Storage Settings')); ?></h5>
                            <small class="text-muted"><?php echo e(__('Configure your file storage settings')); ?></small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('storage_setting', __('Storage Provider'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('storage_setting', ['local' => __('Local Storage'), 's3' => __('AWS S3'), 'wasabi' => __('Wasabi Cloud Storage')], isset($settings['storage_setting']) ? $settings['storage_setting'] : 'local', ['class' => 'form-control select2', 'id' => 'storage_setting'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('local_storage_max_upload_size', __('Max Upload Size (KB)'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::number('local_storage_max_upload_size', isset($settings['local_storage_max_upload_size']) ? $settings['local_storage_max_upload_size'] : '102400', ['class' => 'form-control', 'placeholder' => __('Enter max upload size in KB')])); ?>

                                        <small class="text-muted"><?php echo e(__('Current: 100MB (102400 KB)')); ?></small>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('local_storage_validation', __('Allowed File Types'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('local_storage_validation', isset($settings['local_storage_validation']) ? $settings['local_storage_validation'] : 'jpg,jpeg,png,xlsx,xls,csv,pdf,docx,doc,gif,bmp,tiff,webp', ['class' => 'form-control', 'placeholder' => __('Enter allowed file extensions separated by commas')])); ?>

                                        <small class="text-muted"><?php echo e(__('Example: jpg,jpeg,png,pdf,docx,xlsx')); ?></small>
                                    </div>
                                </div>

                                <!-- AWS S3 Settings -->
                                <div class="col-12 s3-settings" style="display: none;">
                                    <hr>
                                    <h6><?php echo e(__('AWS S3 Configuration')); ?></h6>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 s3-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('s3_key', __('AWS Access Key'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('s3_key', isset($settings['s3_key']) ? $settings['s3_key'] : '', ['class' => 'form-control', 'placeholder' => __('Enter AWS Access Key')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 s3-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('s3_secret', __('AWS Secret Key'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::password('s3_secret', ['class' => 'form-control', 'placeholder' => __('Enter AWS Secret Key'), 'value' => isset($settings['s3_secret']) ? $settings['s3_secret'] : ''])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 s3-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('s3_region', __('AWS Region'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('s3_region', isset($settings['s3_region']) ? $settings['s3_region'] : '', ['class' => 'form-control', 'placeholder' => __('Enter AWS Region (e.g., us-east-1)')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 s3-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('s3_bucket', __('AWS Bucket Name'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('s3_bucket', isset($settings['s3_bucket']) ? $settings['s3_bucket'] : '', ['class' => 'form-control', 'placeholder' => __('Enter AWS Bucket Name')])); ?>

                                    </div>
                                </div>

                                <!-- Wasabi Settings -->
                                <div class="col-12 wasabi-settings" style="display: none;">
                                    <hr>
                                    <h6><?php echo e(__('Wasabi Cloud Storage Configuration')); ?></h6>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 wasabi-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('wasabi_key', __('Wasabi Access Key'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('wasabi_key', isset($settings['wasabi_key']) ? $settings['wasabi_key'] : '', ['class' => 'form-control', 'placeholder' => __('Enter Wasabi Access Key')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 wasabi-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('wasabi_secret', __('Wasabi Secret Key'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::password('wasabi_secret', ['class' => 'form-control', 'placeholder' => __('Enter Wasabi Secret Key'), 'value' => isset($settings['wasabi_secret']) ? $settings['wasabi_secret'] : ''])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 wasabi-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('wasabi_region', __('Wasabi Region'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('wasabi_region', isset($settings['wasabi_region']) ? $settings['wasabi_region'] : '', ['class' => 'form-control', 'placeholder' => __('Enter Wasabi Region')])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 wasabi-settings" style="display: none;">
                                    <div class="form-group">
                                        <?php echo e(Form::label('wasabi_bucket', __('Wasabi Bucket Name'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('wasabi_bucket', isset($settings['wasabi_bucket']) ? $settings['wasabi_bucket'] : '', ['class' => 'form-control', 'placeholder' => __('Enter Wasabi Bucket Name')])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                <?php echo e(__('Save Changes')); ?>

                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- Pusher Settings -->
                    <div id="pusher-settings" class="card">
                        <?php echo e(Form::open(['route' => 'pusher.setting', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5>Pusher Settings</h5>
                            <small class="text-muted">Configure your Pusher settings for real-time notifications</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('pusher_app_id', 'Pusher App ID', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('pusher_app_id', isset($settings['pusher_app_id']) ? $settings['pusher_app_id'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Pusher App ID'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('pusher_app_key', 'Pusher App Key', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('pusher_app_key', isset($settings['pusher_app_key']) ? $settings['pusher_app_key'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Pusher App Key'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('pusher_app_secret', 'Pusher App Secret', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::password('pusher_app_secret', ['class' => 'form-control', 'placeholder' => 'Enter Pusher App Secret', 'value' => isset($settings['pusher_app_secret']) ? $settings['pusher_app_secret'] : ''])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('pusher_app_cluster', 'Pusher App Cluster', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('pusher_app_cluster', isset($settings['pusher_app_cluster']) ? $settings['pusher_app_cluster'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Pusher App Cluster'])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                Save Changes
                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- reCAPTCHA Settings -->
                    <div id="recaptcha-settings" class="card">
                        <?php echo e(Form::open(['route' => 'recaptcha.settings.store', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5>reCAPTCHA Settings</h5>
                            <small class="text-muted">Configure Google reCAPTCHA for security</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('recaptcha_module', 'Enable reCAPTCHA', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('recaptcha_module', ['off' => 'Disable', 'on' => 'Enable'], isset($settings['recaptcha_module']) ? $settings['recaptcha_module'] : 'off', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('recaptcha_secret_key', 'reCAPTCHA Secret Key', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('recaptcha_secret_key', isset($settings['recaptcha_secret_key']) ? $settings['recaptcha_secret_key'] : '', ['class' => 'form-control', 'placeholder' => 'Enter reCAPTCHA Secret Key'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('recaptcha_site_key', 'reCAPTCHA Site Key', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('recaptcha_site_key', isset($settings['recaptcha_site_key']) ? $settings['recaptcha_site_key'] : '', ['class' => 'form-control', 'placeholder' => 'Enter reCAPTCHA Site Key'])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                Save Changes
                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- SEO Settings -->
                    <div id="seo-settings" class="card">
                        <?php echo e(Form::open(['route' => 'seo.settings.store', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5>SEO Settings</h5>
                            <small class="text-muted">Configure SEO meta tags for better search engine optimization</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('meta_title', 'Meta Title', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::text('meta_title', isset($settings['meta_title']) ? $settings['meta_title'] : '', ['class' => 'form-control', 'placeholder' => 'Enter Meta Title'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('meta_desc', 'Meta Description', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::textarea('meta_desc', isset($settings['meta_desc']) ? $settings['meta_desc'] : '', ['class' => 'form-control', 'rows' => 3, 'placeholder' => 'Enter Meta Description'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('meta_image', 'Meta Image', ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="meta_image">
                                                <div class="bg-primary company_logo_update"> <i class="ti ti-upload px-1"></i>Choose file here</div>
                                                <input type="file" class="form-control file" name="meta_image" id="meta_image" data-filename="meta_image_update">
                                            </label>
                                            <p class="meta_image_update"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                Save Changes
                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- Cookie Settings -->
                    <div id="cookie-settings" class="card">
                        <?php echo e(Form::open(['route' => 'cookie.setting', 'method' => 'POST'])); ?>

                        <div class="card-header">
                            <h5>Cookie Settings</h5>
                            <small class="text-muted">Configure GDPR cookie consent settings</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('gdpr_cookie', 'Enable Cookie Consent', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('gdpr_cookie', ['off' => 'Disable', 'on' => 'Enable'], isset($settings['gdpr_cookie']) ? $settings['gdpr_cookie'] : 'off', ['class' => 'form-control select2'])); ?>

                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('cookie_text', 'Cookie Text', ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::textarea('cookie_text', isset($settings['cookie_text']) ? $settings['cookie_text'] : '', ['class' => 'form-control', 'rows' => 3, 'placeholder' => 'Enter Cookie Consent Text'])); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                Save Changes
                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>

                    <!-- Login Page Customization -->
                    <?php if(\Auth::user()->type == 'super admin'): ?>
                    <div id="login-customization" class="card">
                        <?php echo e(Form::open(['route' => 'login.customization.store', 'method' => 'POST', 'enctype' => 'multipart/form-data'])); ?>

                        <div class="card-header">
                            <h5><?php echo e(__('Login Page Customization')); ?></h5>
                            <small class="text-muted"><?php echo e(__('Customize the appearance of your login page')); ?></small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Login Background Images -->
                                <div class="col-12">
                                    <h6 class="mb-3">
                                        <i class="ti ti-photo" class="me-2"></i><?php echo e(__('Background Images')); ?>

                                    </h6>
                                    <hr class="mb-4">
                                </div>

                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_bg_image_1', __('Background Image 1'), ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="login_bg_image_1">
                                                <div class="bg-primary company_logo_update">
                                                    <i class="ti ti-upload px-1"></i><?php echo e(__('Choose file here')); ?>

                                                </div>
                                                <input type="file" class="form-control file" name="login_bg_image_1"
                                                       id="login_bg_image_1" data-filename="login_bg_image_1_update" accept="image/*">
                                            </label>
                                            <p class="login_bg_image_1_update"></p>
                                            <small class="text-muted"><?php echo e(__('Supported formats: JPG, JPEG, PNG, GIF, BMP, WEBP (Max: 20MB)')); ?></small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php
                                                $bg_image_1_path = '';
                                                $imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
                                                foreach ($imageExtensions as $ext) {
                                                    if (file_exists(public_path('storage/login_customization/login_bg_image_1.' . $ext))) {
                                                        $bg_image_1_path = asset('storage/login_customization/login_bg_image_1.' . $ext);
                                                        break;
                                                    }
                                                }
                                            ?>
                                            <?php if($bg_image_1_path): ?>
                                                <a href="<?php echo e($bg_image_1_path); ?>" target="_blank">
                                                    <img src="<?php echo e($bg_image_1_path); ?>"
                                                         class="img-fluid rounded" style="max-height: 150px; filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2"><?php echo e(__('No image uploaded')); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_bg_image_2', __('Background Image 2'), ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="login_bg_image_2">
                                                <div class="bg-primary company_logo_update">
                                                    <i class="ti ti-upload px-1"></i><?php echo e(__('Choose file here')); ?>

                                                </div>
                                                <input type="file" class="form-control file" name="login_bg_image_2"
                                                       id="login_bg_image_2" data-filename="login_bg_image_2_update" accept="image/*">
                                            </label>
                                            <p class="login_bg_image_2_update"></p>
                                            <small class="text-muted"><?php echo e(__('Supported formats: JPG, JPEG, PNG, GIF, BMP, WEBP (Max: 20MB)')); ?></small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php
                                                $bg_image_2_path = '';
                                                $imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
                                                foreach ($imageExtensions as $ext) {
                                                    if (file_exists(public_path('storage/login_customization/login_bg_image_2.' . $ext))) {
                                                        $bg_image_2_path = asset('storage/login_customization/login_bg_image_2.' . $ext);
                                                        break;
                                                    }
                                                }
                                            ?>
                                            <?php if($bg_image_2_path): ?>
                                                <a href="<?php echo e($bg_image_2_path); ?>" target="_blank">
                                                    <img src="<?php echo e($bg_image_2_path); ?>"
                                                         class="img-fluid rounded" style="max-height: 150px; filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2"><?php echo e(__('No image uploaded')); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_bg_image_3', __('Background Image 3'), ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="login_bg_image_3">
                                                <div class="bg-primary company_logo_update">
                                                    <i class="ti ti-upload px-1"></i><?php echo e(__('Choose file here')); ?>

                                                </div>
                                                <input type="file" class="form-control file" name="login_bg_image_3"
                                                       id="login_bg_image_3" data-filename="login_bg_image_3_update" accept="image/*">
                                            </label>
                                            <p class="login_bg_image_3_update"></p>
                                            <small class="text-muted"><?php echo e(__('Supported formats: JPG, JPEG, PNG, GIF, BMP, WEBP (Max: 20MB)')); ?></small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php
                                                $bg_image_3_path = '';
                                                $imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
                                                foreach ($imageExtensions as $ext) {
                                                    if (file_exists(public_path('storage/login_customization/login_bg_image_3.' . $ext))) {
                                                        $bg_image_3_path = asset('storage/login_customization/login_bg_image_3.' . $ext);
                                                        break;
                                                    }
                                                }
                                            ?>
                                            <?php if($bg_image_3_path): ?>
                                                <a href="<?php echo e($bg_image_3_path); ?>" target="_blank">
                                                    <img src="<?php echo e($bg_image_3_path); ?>"
                                                         class="img-fluid rounded" style="max-height: 150px; filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2"><?php echo e(__('No image uploaded')); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Color Customization -->
                                <div class="col-12 mt-4">
                                    <h6 class="mb-3">
                                        <i class="ti ti-palette" class="me-2"></i><?php echo e(__('Color Customization')); ?>

                                    </h6>
                                    <hr class="mb-4">
                                </div>

                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_primary_color', __('Primary Color'), ['class' => 'form-label'])); ?>

                                        <input type="color" class="form-control" name="login_primary_color"
                                               id="login_primary_color"
                                               value="<?php echo e(isset($settings['login_primary_color']) ? $settings['login_primary_color'] : '#007bff'); ?>">
                                        <small class="text-muted"><?php echo e(__('Main color for buttons and highlights')); ?></small>
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_background_color', __('Background Color'), ['class' => 'form-label'])); ?>

                                        <input type="color" class="form-control" name="login_background_color"
                                               id="login_background_color"
                                               value="<?php echo e(isset($settings['login_background_color']) ? $settings['login_background_color'] : '#ffffff'); ?>">
                                        <small class="text-muted"><?php echo e(__('Background color for the login area')); ?></small>
                                    </div>
                                </div>

                                <!-- Logo Customization -->
                                <div class="col-12 mt-4">
                                    <h6 class="mb-3">
                                        <i class="ti ti-brand-abstract" class="me-2"></i><?php echo e(__('Logo Customization')); ?>

                                    </h6>
                                    <hr class="mb-4">
                                </div>

                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_custom_logo', __('Custom Login Logo'), ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="login_custom_logo">
                                                <div class="bg-primary company_logo_update">
                                                    <i class="ti ti-upload px-1"></i><?php echo e(__('Choose file here')); ?>

                                                </div>
                                                <input type="file" class="form-control file" name="login_custom_logo"
                                                       id="login_custom_logo" data-filename="login_custom_logo_update" accept="image/*">
                                            </label>
                                            <p class="login_custom_logo_update"></p>
                                            <small class="text-muted"><?php echo e(__('Supported formats: JPG, JPEG, PNG, GIF, BMP, WEBP (Max: 20MB)')); ?></small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php
                                                $custom_logo_path = '';
                                                $logoExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
                                                foreach ($logoExtensions as $ext) {
                                                    if (file_exists(public_path('storage/login_customization/login_custom_logo.' . $ext))) {
                                                        $custom_logo_path = asset('storage/login_customization/login_custom_logo.' . $ext);
                                                        break;
                                                    }
                                                }
                                            ?>
                                            <?php if($custom_logo_path): ?>
                                                <a href="<?php echo e($custom_logo_path); ?>" target="_blank">
                                                    <img src="<?php echo e($custom_logo_path); ?>"
                                                         class="img-fluid" style="max-height: 100px; filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2"><?php echo e(__('No logo uploaded')); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_favicon', __('Custom Favicon'), ['class' => 'form-label'])); ?>

                                        <div class="choose-files">
                                            <label for="login_favicon">
                                                <div class="bg-primary company_logo_update">
                                                    <i class="ti ti-upload px-1"></i><?php echo e(__('Choose file here')); ?>

                                                </div>
                                                <input type="file" class="form-control file" name="login_favicon"
                                                       id="login_favicon" data-filename="login_favicon_update" accept="image/*,.ico">
                                            </label>
                                            <p class="login_favicon_update"></p>
                                            <small class="text-muted"><?php echo e(__('Supported formats: ICO, PNG, JPG, JPEG, GIF, BMP, WEBP (Max: 20MB)')); ?></small>
                                        </div>
                                        <div class="logo-content mt-4">
                                            <?php
                                                $favicon_path = '';
                                                $faviconExtensions = ['ico', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
                                                foreach ($faviconExtensions as $ext) {
                                                    if (file_exists(public_path('storage/login_customization/login_favicon.' . $ext))) {
                                                        $favicon_path = asset('storage/login_customization/login_favicon.' . $ext);
                                                        break;
                                                    }
                                                }
                                            ?>
                                            <?php if($favicon_path): ?>
                                                <a href="<?php echo e($favicon_path); ?>" target="_blank">
                                                    <img src="<?php echo e($favicon_path); ?>"
                                                         width="64px" class="img-fluid" style="filter: drop-shadow(2px 3px 7px #011c4b);">
                                                </a>
                                            <?php else: ?>
                                                <div class="text-center p-3 border rounded">
                                                    <i class="ti ti-photo-off text-muted" style="font-size: 48px;"></i>
                                                    <p class="text-muted mt-2"><?php echo e(__('No favicon uploaded')); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Display Options -->
                                <div class="col-12 mt-4">
                                    <h6 class="mb-3">
                                        <i class="ti ti-settings" class="me-2"></i><?php echo e(__('Display Options')); ?>

                                    </h6>
                                    <hr class="mb-4">
                                </div>

                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('enable_login_customization', __('Enable Login Customization'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('enable_login_customization', ['off' => __('Disable'), 'on' => __('Enable')], isset($settings['enable_login_customization']) ? $settings['enable_login_customization'] : 'off', ['class' => 'form-control select2'])); ?>

                                        <small class="text-muted"><?php echo e(__('Enable or disable custom login page styling')); ?></small>
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <?php echo e(Form::label('login_bg_animation', __('Background Animation'), ['class' => 'form-label'])); ?>

                                        <?php echo e(Form::select('login_bg_animation', ['off' => __('Disable'), 'slideshow' => __('Slideshow'), 'fade' => __('Fade Effect')], isset($settings['login_bg_animation']) ? $settings['login_bg_animation'] : 'off', ['class' => 'form-control select2'])); ?>

                                        <small class="text-muted"><?php echo e(__('Animation effect for background images')); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                <?php echo e(__('Save Changes')); ?>

                            </button>
                        </div>
                        <?php echo e(Form::close()); ?>

                    </div>
                    <?php endif; ?>

                    <!-- Cache Settings -->
                    <div id="cache-settings" class="card">
                        <div class="card-header">
                            <h5><?php echo e(__('Cache Settings')); ?></h5>
                            <small class="text-muted"><?php echo e(__('Manage system cache')); ?></small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label"><?php echo e(__('Current Cache Size')); ?></label>
                                        <p class="text-muted"><?php echo e($file_size); ?> <?php echo e(__('MB')); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <?php echo e(Form::open(['route' => 'cache.settings.store', 'method' => 'POST'])); ?>

                            <button class="btn btn-danger" type="submit">
                                <?php echo e(__('Clear Cache')); ?>

                            </button>
                            <?php echo e(Form::close()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\up20251\resources\views/settings/index.blade.php ENDPATH**/ ?>