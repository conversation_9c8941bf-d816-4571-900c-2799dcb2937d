{"__meta": {"id": "X0997270b6c8141bee3f975e167e337e7", "datetime": "2025-06-07 04:15:13", "utime": **********.532786, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.252065, "end": **********.532815, "duration": 1.***************, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": **********.252065, "relative_start": 0, "end": **********.344405, "relative_end": **********.344405, "duration": 1.****************, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.34443, "relative_start": 1.***************, "end": **********.532819, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02176, "accumulated_duration_str": "21.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4310708, "duration": 0.018170000000000002, "duration_str": "18.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.502}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4750009, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.502, "width_percent": 7.399}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5068178, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.901, "width_percent": 9.099}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269710707%7C3%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZQRXJyRy9ObkxicUQ5dDBCQ2dYWkE9PSIsInZhbHVlIjoiclpFYiszY2pMcWxzUUJiSDNETXAzK3RYNXM1K2hPdmhXa3ltRGtQdXVneVFjZGwrQzhyYnJZSWd5a0YvMXlCVjZnNmlwRjJxSEpYc2FMeS9qSm1PL0dxdXFaWjQ0aGxhZ1NvN0JqSGg5UW9xbDN2RWZNRGlnbHgzWFo4andBQ0VJTWdFWDBLNFR6NWdESWdORHo2aldnQUFWdWkyTURSVTliNTFoSGU5OFVYK2JsTTNrcTVvN1EyMS8xYWxIZmpaU1ljd2kwazBFd3VOdHRqWHdab3FpbXVpN1JKQStmdmc0TERIWVFUUEJQQi8yK3pBS2ZVVE10SklneUtja0V4dU9admZtN3cyY3ppUThMQnpNUFFjT3VRdFQrUkxQOEJEZWF4TjVVN1hCWlkrWlFqUUZZT2V4TUpvVG8wRWFlY0x0NW1aMXpUZ3JoeWRJNlY1Q2FyaERvNGVNMGZTaFVSaUxVaVFqMkVGdmRtRG9LSHUwMDdxeDd6N0tSd09LQjNPWkM5QmhRWDRIWkVIZTlGajVBOW9qZTJwYXQrRWNkdWRNZ0V0VEs5RmZ4ekdLUjZTWmxodGg4bStLVHoxbFJFbGVjczlZUytBTTVHQVVGcGlOaGNjMW9zeW5jeXc3dlAxeVkybTdhaEM4RVJSSjFoWXB6VEc5ZCtrWEozdy9nbFYiLCJtYWMiOiJlZDQ1NzFmYTM3MTc3OTM2YzYzYmMzZTRkOTY3MWRiNjM5MDg4Mjg2NGQ4ZTRiNDVmYjIxMWYxZWNlOTA4ZDM0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFmUHR2NXJNczZDQVpybGFJdFh1RGc9PSIsInZhbHVlIjoieVNvT2tRMUpRTWJTT01FdnI0QlBJbG1zd1kxaWs1QUFtekd0K1Y3Q1gvWmY3cDdCWFdQcEZFNkZrRHdBbnFmRVVOMHBvNU5NTHNyUENHejZaUUd4OU5DNEFDSEFUbUNuMEFianZNY3V1NjdPQld1akI2cGZCc0FzeG9sN01XZGp5K0t5TWhBcktLcUtqcW90djF1dkkzU3hQek9HYS9LYzNLTlcxeU92R3FrZVlLTnlEUnJFRzA0UDJqcmI4TSs0SHpXcmlVSkQvWGgyb3ZWZWg1QUxGbFZtSjFzaGlNZWF5NFdLNGdsN1JPNldDa001aGhwdURkdyszNUVLamdhMlFJbzU5QXJwcU01cEhoT3BiWU11VXYrSTZNN1NSVllBcEQrdGlQRUZLVW1kSlJ5N2ErVThmQ3VqWThtbG5MbEpBbUpJUHJGQ3JPV2pSbUZzaW5MR2FyZDJOTjI4Q0xHYitKRk44Z0p2OUhXdm5IcFZ4MDVrU1FnbUpRQWMyZUdvUWFCOWNtNTBlMkpoZkh2UitaNDB3NkRMUHgxSHpMQnhOWUxhS1RJcm9HOUlOUnhJNllxZVBNN3ZzaVMzV2pCUEVnK1lhUVB6cGZrL1ZhM2hkSlRRTTN3RlFwUjJUT0xIc201Wkt2UjlySmN0czdMU2lQenN6eW5XQS95RTZ2bFMiLCJtYWMiOiI0NzkxNGJkZWExMmY1NWQyMDNmNDJkOWNjMmIyNzA2ZTJmYjM3MDc3M2YxNjM5ODVjNzA3Y2JjOWYyMjQ0MDVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-255076014 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255076014\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1541683112 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:15:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFPekF3UHlLQW5pNGhEelpodE1TRWc9PSIsInZhbHVlIjoiN3lDTC9CYVlmK2FERWd0aXNhNGZIRnBabkVHRnVoZ2FvK0N2T05UdnhSaS9YQXNKZ2RLWjBxakgzTlQ0ZXBVVFlhQmh4ekRVZkRSVWNXVzl1Vml2RE9BUlVlaTlkSmRKSysxUXVvK0l6TWFLUEJ2aXVsemdncFBxMXp0dzAwS0dSaUJQNjY0LzlvbFpxRmlMdDhaRFBFZU5EU2pVdFk4QWFTZFBiQzZ5eFU3RU1qSVpHUDFvSEU3R1VNRGpZdTY3cVg5cURuL0YxN0RoRzFFKzhNbzVwVHFMcHY0RCszbkNPRTBtQmZTcGk4b3pTK0hHTmRYUmM0aUY0VTVCTm8xZjNMd1JaUmp0dHZXaVl4c3gyWTVuZ1M5bENBdnhWRkl1bW5kTkxheXdzT2gwZVVFaU9xeUFHeld5SXlvKzZ6QmtGT0p4SXdYdGV3Z21lbWNwa0tJMWZRT1JSdjR2QitMdWlCSnU2MURqaW5IZnR0TUtzSXZCc2VqNFd4SytlNkhuSzVvRHBTSGNONERlNlBaa0g0NjNLOFFFYzU4Mm54aTNTZWhSa0duc1ZEVmZQKzZlN2NEU3VBbmxjQ0poblE1REFhMFRjRjlLODJrcm5tRWZLS3hBTGx5Q0lzSHU4Q2hjSmR4cnlIVkxOdmZoWDV0S2xoZjdrUzdnemtEUFBpQ00iLCJtYWMiOiJjMGQyMGNkZjNjNWM1YjE3NjIyNTU1OWFjMDZkZTBjNjJkZWZjZTVjNzM2ZjY2MWJkMTVlNTI3MmEzY2UyNTc2IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFaWXlmZEJtVm9ZdWYwZE1VMm9QL0E9PSIsInZhbHVlIjoibE53NDFkVzY3WWF5OHpjZFp3MGlEZnhDRmZ3d2FRMDc0QWlTNFdhREc0ZVRLVzRaUWR0THFxdGJZUjBEaDkwRmJXNEprMFhxd25UbEFMeElDVGxsQ2hpaEZtTEFYTFZneDBudlE3RHJHRDRhZGhDRjRaanNrUTJFWURaNXpHRzRjNnlnVWxrTHhYYVFjb2pHdVZJbmhrOTZ0cHlLWE9RQ284VjJJSUNiNU5EUkVDa1REeWQyZm5wNmk5R21LbzdsK0Vod3UwSlZaTnExS0lkNjVHMWVPYUEyOHduclAwYkVyNDJKR29tKzI4a1dJak0zMjNUTGtWZWswNDhyZ3JhWmEyMXZMTThhamN3Ky84TFNnQ3ZRN2lzdmhqdjVWTEdDUWRodEJHa1E4ZXVsbjdKQ0ZyZmJ5a3BDQ2wwM0Zsb25iTlVkWFJvQVhhRmFkbFZWY1hBb3lGZERNUnNHeEVSK3ZURjlFblFSclhjOHBzTjBadnFtQXlUSjNXWGpCUmtXMFA5SW1PS1pLS2x2TmZHb2YvQWlaZjIrY3hBamNhQm1JTlpkWVZ6blZCNzJncUI4TDlpR0tGVTZVS1B4bE51V3RHQ2I0UGtxVWZGSTgyWXpwOXFUd1JKNmtuRlBvSmp1UnIyWkdmdE5VRzk1Nk9xSHYvdHp5NnlldjNwWkJRc1QiLCJtYWMiOiJlY2I3NjI0M2FkZDIwY2EyODNmNDI5YmI2YjFjYzI2YzkyN2M4OTdhZGJlYTA5ZDE3ZjM5MGI1Nzg4M2ZhYmFiIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFPekF3UHlLQW5pNGhEelpodE1TRWc9PSIsInZhbHVlIjoiN3lDTC9CYVlmK2FERWd0aXNhNGZIRnBabkVHRnVoZ2FvK0N2T05UdnhSaS9YQXNKZ2RLWjBxakgzTlQ0ZXBVVFlhQmh4ekRVZkRSVWNXVzl1Vml2RE9BUlVlaTlkSmRKSysxUXVvK0l6TWFLUEJ2aXVsemdncFBxMXp0dzAwS0dSaUJQNjY0LzlvbFpxRmlMdDhaRFBFZU5EU2pVdFk4QWFTZFBiQzZ5eFU3RU1qSVpHUDFvSEU3R1VNRGpZdTY3cVg5cURuL0YxN0RoRzFFKzhNbzVwVHFMcHY0RCszbkNPRTBtQmZTcGk4b3pTK0hHTmRYUmM0aUY0VTVCTm8xZjNMd1JaUmp0dHZXaVl4c3gyWTVuZ1M5bENBdnhWRkl1bW5kTkxheXdzT2gwZVVFaU9xeUFHeld5SXlvKzZ6QmtGT0p4SXdYdGV3Z21lbWNwa0tJMWZRT1JSdjR2QitMdWlCSnU2MURqaW5IZnR0TUtzSXZCc2VqNFd4SytlNkhuSzVvRHBTSGNONERlNlBaa0g0NjNLOFFFYzU4Mm54aTNTZWhSa0duc1ZEVmZQKzZlN2NEU3VBbmxjQ0poblE1REFhMFRjRjlLODJrcm5tRWZLS3hBTGx5Q0lzSHU4Q2hjSmR4cnlIVkxOdmZoWDV0S2xoZjdrUzdnemtEUFBpQ00iLCJtYWMiOiJjMGQyMGNkZjNjNWM1YjE3NjIyNTU1OWFjMDZkZTBjNjJkZWZjZTVjNzM2ZjY2MWJkMTVlNTI3MmEzY2UyNTc2IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFaWXlmZEJtVm9ZdWYwZE1VMm9QL0E9PSIsInZhbHVlIjoibE53NDFkVzY3WWF5OHpjZFp3MGlEZnhDRmZ3d2FRMDc0QWlTNFdhREc0ZVRLVzRaUWR0THFxdGJZUjBEaDkwRmJXNEprMFhxd25UbEFMeElDVGxsQ2hpaEZtTEFYTFZneDBudlE3RHJHRDRhZGhDRjRaanNrUTJFWURaNXpHRzRjNnlnVWxrTHhYYVFjb2pHdVZJbmhrOTZ0cHlLWE9RQ284VjJJSUNiNU5EUkVDa1REeWQyZm5wNmk5R21LbzdsK0Vod3UwSlZaTnExS0lkNjVHMWVPYUEyOHduclAwYkVyNDJKR29tKzI4a1dJak0zMjNUTGtWZWswNDhyZ3JhWmEyMXZMTThhamN3Ky84TFNnQ3ZRN2lzdmhqdjVWTEdDUWRodEJHa1E4ZXVsbjdKQ0ZyZmJ5a3BDQ2wwM0Zsb25iTlVkWFJvQVhhRmFkbFZWY1hBb3lGZERNUnNHeEVSK3ZURjlFblFSclhjOHBzTjBadnFtQXlUSjNXWGpCUmtXMFA5SW1PS1pLS2x2TmZHb2YvQWlaZjIrY3hBamNhQm1JTlpkWVZ6blZCNzJncUI4TDlpR0tGVTZVS1B4bE51V3RHQ2I0UGtxVWZGSTgyWXpwOXFUd1JKNmtuRlBvSmp1UnIyWkdmdE5VRzk1Nk9xSHYvdHp5NnlldjNwWkJRc1QiLCJtYWMiOiJlY2I3NjI0M2FkZDIwY2EyODNmNDI5YmI2YjFjYzI2YzkyN2M4OTdhZGJlYTA5ZDE3ZjM5MGI1Nzg4M2ZhYmFiIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541683112\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1711789294 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711789294\", {\"maxDepth\":0})</script>\n"}}