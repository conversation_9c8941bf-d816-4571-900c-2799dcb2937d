{"__meta": {"id": "X2b5814eedffd8462445661e6ecc531f5", "datetime": "2025-06-06 20:42:27", "utime": **********.449532, "method": "POST", "uri": "/edit-profile", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242545.718054, "end": **********.449578, "duration": 1.7315239906311035, "duration_str": "1.73s", "measures": [{"label": "Booting", "start": 1749242545.718054, "relative_start": 0, "end": **********.120965, "relative_end": **********.120965, "duration": 1.4029109477996826, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120998, "relative_start": 1.4029438495635986, "end": **********.449584, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET edit-profile", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\UserController@editprofile", "namespace": null, "prefix": "", "where": [], "as": "update.account", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=377\" onclick=\"\">app/Http/Controllers/UserController.php:377-432</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02597, "accumulated_duration_str": "25.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.229533, "duration": 0.0068, "duration_str": "6.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 26.184}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.271609, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 26.184, "width_percent": 7.432}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.282952, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 33.616, "width_percent": 5.814}, {"sql": "select * from `users` where `users`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 380}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.29506, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "UserController.php:380", "source": "app/Http/Controllers/UserController.php:380", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=380", "ajax": false, "filename": "UserController.php", "line": "380"}, "connection": "ty", "start_percent": 39.43, "width_percent": 4.659}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>' and `id` <> '16'", "type": "query", "params": [], "bindings": ["<EMAIL>", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.351437, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 44.089, "width_percent": 5.237}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 399}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.362344, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 49.326, "width_percent": 5.853}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 413}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.372968, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 55.179, "width_percent": 6.238}, {"sql": "update `users` set `avatar` = 'لقطة شاشة 2025-06-06 223615_**********.png', `users`.`updated_at` = '2025-06-06 20:42:27' where `id` = 16", "type": "query", "params": [], "bindings": ["لقطة شاشة 2025-06-06 223615_**********.png", "2025-06-06 20:42:27", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 426}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.409229, "duration": 0.01002, "duration_str": "10.02ms", "memory": 0, "memory_str": null, "filename": "UserController.php:426", "source": "app/Http/Controllers/UserController.php:426", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=426", "ajax": false, "filename": "UserController.php", "line": "426"}, "connection": "ty", "start_percent": 61.417, "width_percent": 38.583}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "success": "Profile successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/edit-profile", "status_code": "<pre class=sf-dump id=sf-dump-1845484528 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1845484528\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2123784096 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2123784096\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1225664306 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Ham<PERSON>oud Qasim</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>profile</span>\" => <span class=sf-dump-note title=\"Illuminate\\Http\\UploadedFile\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UploadedFile</span> {<a class=sf-dump-ref>#167</a><samp data-depth=2 class=sf-dump-compact>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">test</span>: <span class=sf-dump-const>false</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalName</span>: \"<span class=sf-dump-str title=\"31 characters\">&#1604;&#1602;&#1591;&#1577; &#1588;&#1575;&#1588;&#1577; 2025-06-06 223615.png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">mimeType</span>: \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">error</span>: <span class=sf-dump-num>0</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\File\\UploadedFile`\">originalPath</span>: \"<span class=sf-dump-str title=\"31 characters\">&#1604;&#1602;&#1591;&#1577; &#1588;&#1575;&#1588;&#1577; 2025-06-06 223615.png</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">hashName</span>: <span class=sf-dump-const>null</span>\n    <span class=sf-dump-meta>path</span>: \"<span class=sf-dump-str title=\"33 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp</span>\"\n    <span class=sf-dump-meta>filename</span>: \"<span class=sf-dump-str title=\"11 characters\">php9BDE.tmp</span>\"\n    <span class=sf-dump-meta>basename</span>: \"<span class=sf-dump-str title=\"11 characters\">php9BDE.tmp</span>\"\n    <span class=sf-dump-meta>pathname</span>: \"<span class=sf-dump-str title=\"45 characters\">C:\\Users\\<USER>\\AppData\\Local\\Temp\\php9BDE.tmp</span>\"\n    <span class=sf-dump-meta>extension</span>: \"<span class=sf-dump-str title=\"3 characters\">tmp</span>\"\n    <span class=sf-dump-meta>realPath</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>writable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>readable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>executable</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>file</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>dir</span>: <span class=sf-dump-const>false</span>\n    <span class=sf-dump-meta>link</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225664306\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1214540943 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">195167</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryr4NX9vPoxrvwu3W2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=xo2xva%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=8pzxed%7C1749242535108%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im8yODh2SDF3dHpHdDVENGcxV0oyTWc9PSIsInZhbHVlIjoiUnVNWWorZVZwUWw0aEt5MFduMTVIMnBTOFU0aXY0aUo1WjRtRys4b2J0MVk3NTZiODdpeTUvRGcvVFBmb0N2bVZhVUh0VDhpZURxZDRzUTJMR2E3a1BLcnVJK0tzelF0amh5dmMxZGZXTFc4TXdzTmlrcEorVXByV3JuaUIxMjdabkFWVTIzMitJVWdybUJGZ0pLclA3cTQ4aUZPWUVydklDRG1yOWlxSmFmVC9takY2ckIyNURnb2YwVlZmTDR2Y0hRekwwbGs0QjFVU1JCd0dMWU9wRFMrSVg3TklyVC9CNE9SR1RvMkRsNFFzdSswMzhCbnhudUl1eHR0dTJpaGN3VlF2d2hmNXpPK0NNYkZIeHd6L1I3QjZDWmV2S2VXczFkT3VvbXpDSXY5dnkrVWs3cTJDbFExWjYwY0lxV2ZvZUYvZitXRTJiZmFGVzBXeFlnUHVDVUw5SDd2cWpKbmFsMVBoR2pvN3BreHNvWXBjMGZuYlhFazZ2RzA0UmREd1dWYW96T0xiaTVURVVhTmlDSUppNkJxY2hmTDVoYW9mck9sc0swUXJpaDlTSXRQVm4rR3J6cFpSODBnek1PNnVMR1JFRHZFVFFQKzVSVk9EZWZXN3dlWmZXZ0lFNlVtb3JYZGQwc3FDVTR1STNEdVNxdXpYMlpsUktWbFdNcTQiLCJtYWMiOiJlMGUyZWM4NmE3MjQ5NWU5YTMwOGJkZDg1NjA4MzY2MzZkOWM1MjA0ZjJmMzI1NDFhOWMxOTRiZDg4OWQwY2I5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlQxcDdUUG42cU4rYkl6UnRJSXdieEE9PSIsInZhbHVlIjoicG9GZDVsVDhzaDhyRkdOaklDTTcyNGFmS1FzdVhnQlpxRlc5ZXR4TVoxRU9KaGZBaEM1VTRGMzVmdmZaNXRkcy9PcFpKWlJIR2VqZHkwREgwK3ZZZzdrbHZlYW5sZ0FsS3VJYVU0Rjl3ZjBDYUpyZUtWQ3RJYjV3bHZaejVTY2FwTStvMHZNcG0xMTNDSFQzc1ZyS2hXSjZqUlhLYkl3MGdMc1d6dmdHV0lkZVRBbUtiRmZpbkZrZ0JEelRkMWNQTTZaZkliRGFKUE5QdnJtV3VWemM3TWZFYTY4VkNXMkcwbGMzN0Z0MExDV3pvZ0RzeWo1MjVIbWlML21va1h1MWN4bHdPR21aQmR0aWM2YWgvQ3IvRFJZcWMrTVVUWUlaVUlqRXM1eTd1WnB2Q2lmbXhWSWVSVG5LQ2ZpSVo4UGJqMVpYaDRMRWpyQjFSTUcvZVVMdFVkUjlTNHVuTWVaeFcvRFJLVlVtWmRJNXBTZkoyWmltNklFdndPUzNhY0dVTStXVjV1QnVEaUJURFlVaGZCbHZxRm1HV2lmNUxKazZuL2FKcENkNW1nYlRoOUwyWnlneGJoN1h3eDVyRFo2d0wvdDV6VEN1SmVJTXpwRHlDcWI4SnowUmcyWjU3N2EwQUJIcW9Rd3p2TU0yNXZaOUVTVndkNGRPYlUxamhPeXUiLCJtYWMiOiIxOWJmMTA5MWU2NjVmNGNkN2ZiYWI2NjY0YjM3OTkzYmE0NzM5ZTg5YTY3NTBiMTVjMTk3YjEyMDE5Yjc0ZWFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214540943\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1Mgwy0E1kOADJg4wSzllo3ylSTAx8J3JTYC2bXcn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:42:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/profile?16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9GZG5FdEZDdUZuNUpwNElpSHowd0E9PSIsInZhbHVlIjoiek1RWGpTZVY0YzcvOU52R2pwUFlDRkdxS2ljUGoybW5TYUl4RlRVeVNWOUZVb3RBL1d5ZGlCT1J6OHZrOGF1QUZMWm5xUDNmK2lEYXdzeGlLcDJEdk1tdDBLTkRLT3dyZFowcWxRRXJKS2JxNlp0bEk3d0NLbE4vOHRjMG9MNUU0dDFMVTlWTlVzTkhTeXQ3Y2F0aGk3V3g4Z1RNT243dGpJd1ArVGVyZVo4YmFNM1JQdWpLdGF6WmdHWUZxZ1Z5WGRsMkJXbUlBN0k2RHdZSkNDc3M0eGJwOTB6d0srbWh4TW9MOEgxcU9ReUwxazZuanYwWmg5QmhJaC95bWpRcjdvZG5oU0JHTjUvQk5vcGRyeEhtYmVyWmRuMStyZ3IvNWw4TDBDUjUwczBRcmpJSHhybFo5MUl3YVB5bkszay9pWGtMM0lhT1Y0eFllRlpKWmxwOHVpMmFlVm8rL2hzMzlMY1FqM0xTb2tyVTA1YkFTaEdRRnNJTmlvbzNhTjdJZThXeTh2OWRvZ3BXc2F5cVF4enJBT2U3eU15NHpZMVdtS0dSY3dZUGI0TWVOYVF1ZEhNdlV3bFZQMlR5b3F6NFp4K1l1ZTRTWVZIOTcrc0l4MTB1VUJqeVYxT3l4OVMvM1RKSEE0Q3d0b1NkZzRzNHNkREVpYU1oRW16ZlM4eTYiLCJtYWMiOiJiMjIzYjc3ZmEyOWQ3ZjU0Y2QxMmE0NmZjMWYwMTgzMDhjMGU1NDA5MGE0NGMzN2VkZTc5MjAxNjJjNzFkZWUzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:42:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjV5akxVWkVYUWZ2aVhickxmdkl6aVE9PSIsInZhbHVlIjoiK0NyUmZESWpwVXU2MXlVL1o1UUVlNnp5WEFZTGZMMFo4YW8ydHZiSGs5dHZyQ2FrbTZVY2tpOUEzQURlM1FyK1JjSjhYOC9YN2NGb1ZEazEzVjhEb2ZrTnd4S2U5L1ZaNmdlV29BTjVTS3NPa0tkQzVvRHlybkMyajVzTnFlcFU4THYvUHhmTElHQjVKdU1YRVBCZVhzMTBDaExadlZvNml0Wjl1R1IrYmlKcjN6Tkk5dFNKSjRLdVNYSnVmc1VGOS9ReGN0VVlPT1dUVzkxS0JKdGJFVVZwZ0s4UUF3cUJTQjJaQU9yeXIrQnIwSmpXaUQ4TDlqcUVqWUdqM3pJY3FXclRRdlc4aE94S1hMMmpGWWY3enRWMXQ5T3FXYzZsZVJZK0t6ck1FdmJEdDdIdFg4Q1lzVEhIOXl0RHM1NE5SZmN0V2RIbm05NHFoekFIcGhNSm9XSXgxVUlLM01PenZuZXdHTllNZjViR3VNbWttVzAreUNYWS9zeDV1SUVvSlpnZkRXd081K0hzM3hCdlk4eXNnQ1FZcjJzWnVFdTRkQVBVYzZMY21qMEpHVkRsQU54QlJ1RzJ3MjNoaUhURkwvc0NmcmRTNVlwNWQzUElyQTNOOE5vWWl3Nk1VT2NvbHh0VzlrS2UrWWYxM1FKY1RNNCsxZVc1SmRwTnR1YmYiLCJtYWMiOiJhNDQ5ZjhmODZiMDQ4OGI1YWE1MDQ0Yjk0ZWM5M2Q0NTg1MjFlNTQxYWQ0MGI5MGNlNGNhZjk4Zjk3NmZkNzZjIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:42:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9GZG5FdEZDdUZuNUpwNElpSHowd0E9PSIsInZhbHVlIjoiek1RWGpTZVY0YzcvOU52R2pwUFlDRkdxS2ljUGoybW5TYUl4RlRVeVNWOUZVb3RBL1d5ZGlCT1J6OHZrOGF1QUZMWm5xUDNmK2lEYXdzeGlLcDJEdk1tdDBLTkRLT3dyZFowcWxRRXJKS2JxNlp0bEk3d0NLbE4vOHRjMG9MNUU0dDFMVTlWTlVzTkhTeXQ3Y2F0aGk3V3g4Z1RNT243dGpJd1ArVGVyZVo4YmFNM1JQdWpLdGF6WmdHWUZxZ1Z5WGRsMkJXbUlBN0k2RHdZSkNDc3M0eGJwOTB6d0srbWh4TW9MOEgxcU9ReUwxazZuanYwWmg5QmhJaC95bWpRcjdvZG5oU0JHTjUvQk5vcGRyeEhtYmVyWmRuMStyZ3IvNWw4TDBDUjUwczBRcmpJSHhybFo5MUl3YVB5bkszay9pWGtMM0lhT1Y0eFllRlpKWmxwOHVpMmFlVm8rL2hzMzlMY1FqM0xTb2tyVTA1YkFTaEdRRnNJTmlvbzNhTjdJZThXeTh2OWRvZ3BXc2F5cVF4enJBT2U3eU15NHpZMVdtS0dSY3dZUGI0TWVOYVF1ZEhNdlV3bFZQMlR5b3F6NFp4K1l1ZTRTWVZIOTcrc0l4MTB1VUJqeVYxT3l4OVMvM1RKSEE0Q3d0b1NkZzRzNHNkREVpYU1oRW16ZlM4eTYiLCJtYWMiOiJiMjIzYjc3ZmEyOWQ3ZjU0Y2QxMmE0NmZjMWYwMTgzMDhjMGU1NDA5MGE0NGMzN2VkZTc5MjAxNjJjNzFkZWUzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:42:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjV5akxVWkVYUWZ2aVhickxmdkl6aVE9PSIsInZhbHVlIjoiK0NyUmZESWpwVXU2MXlVL1o1UUVlNnp5WEFZTGZMMFo4YW8ydHZiSGs5dHZyQ2FrbTZVY2tpOUEzQURlM1FyK1JjSjhYOC9YN2NGb1ZEazEzVjhEb2ZrTnd4S2U5L1ZaNmdlV29BTjVTS3NPa0tkQzVvRHlybkMyajVzTnFlcFU4THYvUHhmTElHQjVKdU1YRVBCZVhzMTBDaExadlZvNml0Wjl1R1IrYmlKcjN6Tkk5dFNKSjRLdVNYSnVmc1VGOS9ReGN0VVlPT1dUVzkxS0JKdGJFVVZwZ0s4UUF3cUJTQjJaQU9yeXIrQnIwSmpXaUQ4TDlqcUVqWUdqM3pJY3FXclRRdlc4aE94S1hMMmpGWWY3enRWMXQ5T3FXYzZsZVJZK0t6ck1FdmJEdDdIdFg4Q1lzVEhIOXl0RHM1NE5SZmN0V2RIbm05NHFoekFIcGhNSm9XSXgxVUlLM01PenZuZXdHTllNZjViR3VNbWttVzAreUNYWS9zeDV1SUVvSlpnZkRXd081K0hzM3hCdlk4eXNnQ1FZcjJzWnVFdTRkQVBVYzZMY21qMEpHVkRsQU54QlJ1RzJ3MjNoaUhURkwvc0NmcmRTNVlwNWQzUElyQTNOOE5vWWl3Nk1VT2NvbHh0VzlrS2UrWWYxM1FKY1RNNCsxZVc1SmRwTnR1YmYiLCJtYWMiOiJhNDQ5ZjhmODZiMDQ4OGI1YWE1MDQ0Yjk0ZWM5M2Q0NTg1MjFlNTQxYWQ0MGI5MGNlNGNhZjk4Zjk3NmZkNzZjIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:42:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-353787669 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Profile successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353787669\", {\"maxDepth\":0})</script>\n"}}