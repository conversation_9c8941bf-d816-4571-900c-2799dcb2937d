{"__meta": {"id": "X01ca8210f944c3cfb816abb860fab4e0", "datetime": "2025-06-06 19:12:53", "utime": **********.59912, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237172.169839, "end": **********.599155, "duration": 1.4293160438537598, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749237172.169839, "relative_start": 0, "end": **********.317512, "relative_end": **********.317512, "duration": 1.1476731300354004, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.317533, "relative_start": 1.1476941108703613, "end": **********.599158, "relative_end": 3.0994415283203125e-06, "duration": 0.28162503242492676, "duration_str": "282ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45698120, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.483535, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.501472, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.570147, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.580581, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.04272, "accumulated_duration_str": "42.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4049888, "duration": 0.02168, "duration_str": "21.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 50.749}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.433079, "duration": 0.01139, "duration_str": "11.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 50.749, "width_percent": 26.662}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.453051, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 77.411, "width_percent": 3.605}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.485214, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 81.016, "width_percent": 2.786}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.503482, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 83.801, "width_percent": 3.722}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5375, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 87.523, "width_percent": 4.073}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.548146, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 91.596, "width_percent": 2.2}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.556777, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 93.797, "width_percent": 2.926}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.57372, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.723, "width_percent": 3.277}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XVsuooi9r4Xbc3TiFLlAVnTr0BIPLZg0Hyn0zo5r", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-161609367 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-161609367\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-534299104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-534299104\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-945689308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-945689308\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-976570539 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InMzN2ZWeVI2ZGFpRVdCSkNFcXMvVEE9PSIsInZhbHVlIjoiQlZ0WjlyRWFzck9YS096SDNSclp4b1NjWXl3ejdycnR5bkhPamlsUjFibnpwZytDYWJsRFdZRE1NalRXQ282UzJGa2NNbnRUL0VuMHJ1NlhITnE1Q1V6MjA1N2ZNM2tkT0JZNVg3dFBsWWxRdXA1WWlmblJpakR5UUIxeGJqaXJEcWJTNDFQVG9JNXBmMDZ2VlAxY2thNnhuL05xSFY2MVd6aXRTZEVzdENmenQydDR0cVQ4cytaZE4wM0tMaGhXbmcwRjdjUjNqcFhCQTFMUGNtdjV4MmJOMFpnZ2M5cFc5ZGg4QUVZbUk3Q05lMW5iUlB5M1RlSmRYSEJmOGo2WS9YUUZwZWdlUTV3c0ZxaGM5cnZPR1RwaW13NFY0ZkhFdGp0b29LeGltby9nTXVJbjltbXM5cDk0TVk4SER3MFdtWm5BNzdGYmtTQlJYQUY0U2V2KzdQK3I5dVNpdVg1dGVaajl2SlQ0TnZYTktIRmJYc3FuSWxrOE96NW5xaHljbDVwYWZOazVqT2pYTXdxREVrME1GWG9oWktZeW9lVTJOclIvQ0R0am55eTVmNERRWUswTHp5Ym0zTCtkSFJQdnZtNndsQlhWemxBYmo0N1c2cGtNdmczaWhDb1NVT0poZWg3cGlSNFEyT0JxQ0NTOHE2eG1BNlRSVUpPaU4yV3IiLCJtYWMiOiI2ZGY0NGQ0NDA0OTQxYmJlMWMxNzQ4YjdlNTNlNDI0NjZmZTQ2YzMwMDdmNTExNjdiMjgzMTEyZDE1NjA3NGUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJ0Y1V1VjMvRlMwWXZ3R0pYK3gvSkE9PSIsInZhbHVlIjoiK09rbnhlU3hBZjBqV1NkbTU4S2Qwa21iR3hMaGh0QnVtaHVWM3d1UCs2b09RbnY4YlNLbitqU3dmYnF4SkJzdlFMZk5HeENibzdwWGI5TFc4cmp5cnFQeGxFeGtlZ1B6OGpxZ3ZPV05nQ1RLSC9mT09rYWY2bXNGdjc0b3ZnMHVsUFB5MFJIZXFuNFJLVmJwdGFnLzY4bXNaMzZjZHFNZmNJQjl4YzRXTVlJMzVzbEp2VWhiVWhwT21rRTVhVmhXMThCT3ExWHVGdUplaXRtNFl2ZThQaU9BeWxpa0RPRXBqbjVDVW4vOG9xSjRGTkg4Zk14TFFER1MyTzhrK2h4VFQ5VDIzUFhpNnJSQWhEcmZPdHgwWTNrM1cyWEZsUmhoK3l5bEdKOVQ1a0dGWWJFdnRvZSs2dEJSb0lmaG9jaU1aQTBoNldWbW1YN0grRXQzZjQrN1pKMGJqeUxwVzlxWTVieXU3bVRScTdsd1N0TUlBY1pOSG43bERNTk82M2VEMmluY2lPZ0dnN3M5MEZmejlSSkxKN0Rvd2orQWhGN3dHTjBndUg3WVNRNTJvQzhkM0hKaXMrWnY4K2Rtd3JYT0dzR0IwMjV1V0ZSTlBVUStuTFdvMDgrQTArYXkvMjZlZ0lWaUhFN1BEVHo3NElIWXlOdGhnVWN6aXVmbGZCNkEiLCJtYWMiOiJiOTUwODE4NTI1YmEwODZmODMyODNiMzkxODNmODBlYjU1M2IyYjJhMWEyZDUxYzBkNDNmZTFlZjU5ODA0ZjJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976570539\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-236962914 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XVsuooi9r4Xbc3TiFLlAVnTr0BIPLZg0Hyn0zo5r</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IwjKA1ErVXZF6rgCHegY81ncT3BZuCdehJsJGL0c</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236962914\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-95964462 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:12:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlZVXJuekY5U05wd01VclUxZDgyU2c9PSIsInZhbHVlIjoiSGorSXRaUVhzS3ZpdlhLK2lHRXR6Z2wxeVdkUk52ZmU3bGRZY1FaTEtHNDRiL2xhYTljeGZsOUwyZjhseGxQRHhCNTFpeU42Vmx5RlRXNVBjSEVEY1pOOWgwUmpWZ0tMbDgvSnQyMXQ2ckRXMTExNFZCV28vUEcrQis0VHdSUHVGUFJudkw3Q3NpVnZhdVdiT1E0MjBLRWx3Qk5pQ1E4dU05d2lkVDh5YmlEZUxHRFVpUE43VUdmQVIvMUVkU3BqUVdjc1lDL3ZOYlZnYVVSTndOekZxWTk2VU9ySXNFNGVndnhaeWVkTytJamF5V3MyanFZenZzN3VKU0ZHcTRERGxaZ1ZOd3VNdllQb2dTdmlDTnRyNnBER0F3NWVKQUhuTzVkVG0wbTdURklab3ZDemdZVzhWc1U5V3VEdURVMTZ2OW80Q1RxZTc1YkpnWXZrZjExbldUSEhhYlBPNnV0NTJRdFMxZElBSzlwam5uSm13UnJ0bG9OdFR6dldQR3BtOXIvWFlYbTZtQnRjRFcxclF3ZklOa01rV3hQa2JhSWJqUndyelhIZGZHQVBBdmo4aUJuZzk3ZlZ2Vk5Kd3p2a2lMMmVwZFpFY0pOM3VScG0xWXNPTlBTUUlkTHRNV3laU2tQSGRRcXBzYzM0U3JtdXJJQURxMzg2Wk1WUmxaMzMiLCJtYWMiOiJmODM1Y2RjNzU5YjJmNjI1MWI1YTkyOGExYTkyN2Q3ZjA4Y2NkNTJhZjgzZmU5NjIyODQ2MDZkNzlkZjViMWYwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:12:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkQ3bmVWVVdFMHAwd1JPelhFMnQ3bEE9PSIsInZhbHVlIjoiVS9CY1duQXNRMGpYbzByU1lEb1RFcjJYb0pMM1RIenJ2QnVzbTlVY09PbG5lOUdpY0U3a2NwVVg4bXB5MjFReEFlMGg4TWVZekEycFYyemRZOUZKZTBTMUJsWFZTK2RubkhnVlh5UTNrTFhhbTBmSVg2Z0NJZnFVUFNVVjc3Zk1tajZsaVdiK1R1S2VkUGZrbUUzcjBhNUM4NlRIS24wUWUrRGF1NEVXTWpRbjNTL2xDaGhUbG14QnRsZ25YeFBsZVRZWDhQcy83M2ErdEk4SXpudFE1U0pyZ282NlNVSUpHUGdnUmw3OHJxdUU2NkY2NUw2Q3N6d1hrRENJUFhsNmRmVXplQ0k4bVhGRWlDSTdibWJLZXRnci85MVNTL2NTS2ZLcVVKby93aHNLQXpxYmxpZ3c2b2NvS2daZURFbXV0bHpINkFqZU5kL01CMTVJbDZpZHVkQmJ4TEVsb1h5QlA5N0dsRHYwVWJwUW5tUnVQYndDRWcvT3EvUllkejgramFaQkVrYXRlTFJ4bXNnY3hac2ZrN1RzeXQyeFgzMzdLZUZWVTcxeWRhRHRaMTQxOTFYZFZ1bVhGTkJpakVJb3VEVW5aRDY3R1JQQXY5OVpYY25nT05ncFcyM2JEcUZHZ0kzSW95MWRqSnZTQi9qUDZBYTB1bTdOaWhrTWRSdm0iLCJtYWMiOiIzOWZkYzIzNjVjNjQzMDY2YmM4OTJiNTgwNjAxM2IzMDAwOWFjNTc3YWRjZmY2YmQ4YTRiZDBiZjRkZDQyN2QxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:12:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlZVXJuekY5U05wd01VclUxZDgyU2c9PSIsInZhbHVlIjoiSGorSXRaUVhzS3ZpdlhLK2lHRXR6Z2wxeVdkUk52ZmU3bGRZY1FaTEtHNDRiL2xhYTljeGZsOUwyZjhseGxQRHhCNTFpeU42Vmx5RlRXNVBjSEVEY1pOOWgwUmpWZ0tMbDgvSnQyMXQ2ckRXMTExNFZCV28vUEcrQis0VHdSUHVGUFJudkw3Q3NpVnZhdVdiT1E0MjBLRWx3Qk5pQ1E4dU05d2lkVDh5YmlEZUxHRFVpUE43VUdmQVIvMUVkU3BqUVdjc1lDL3ZOYlZnYVVSTndOekZxWTk2VU9ySXNFNGVndnhaeWVkTytJamF5V3MyanFZenZzN3VKU0ZHcTRERGxaZ1ZOd3VNdllQb2dTdmlDTnRyNnBER0F3NWVKQUhuTzVkVG0wbTdURklab3ZDemdZVzhWc1U5V3VEdURVMTZ2OW80Q1RxZTc1YkpnWXZrZjExbldUSEhhYlBPNnV0NTJRdFMxZElBSzlwam5uSm13UnJ0bG9OdFR6dldQR3BtOXIvWFlYbTZtQnRjRFcxclF3ZklOa01rV3hQa2JhSWJqUndyelhIZGZHQVBBdmo4aUJuZzk3ZlZ2Vk5Kd3p2a2lMMmVwZFpFY0pOM3VScG0xWXNPTlBTUUlkTHRNV3laU2tQSGRRcXBzYzM0U3JtdXJJQURxMzg2Wk1WUmxaMzMiLCJtYWMiOiJmODM1Y2RjNzU5YjJmNjI1MWI1YTkyOGExYTkyN2Q3ZjA4Y2NkNTJhZjgzZmU5NjIyODQ2MDZkNzlkZjViMWYwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:12:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkQ3bmVWVVdFMHAwd1JPelhFMnQ3bEE9PSIsInZhbHVlIjoiVS9CY1duQXNRMGpYbzByU1lEb1RFcjJYb0pMM1RIenJ2QnVzbTlVY09PbG5lOUdpY0U3a2NwVVg4bXB5MjFReEFlMGg4TWVZekEycFYyemRZOUZKZTBTMUJsWFZTK2RubkhnVlh5UTNrTFhhbTBmSVg2Z0NJZnFVUFNVVjc3Zk1tajZsaVdiK1R1S2VkUGZrbUUzcjBhNUM4NlRIS24wUWUrRGF1NEVXTWpRbjNTL2xDaGhUbG14QnRsZ25YeFBsZVRZWDhQcy83M2ErdEk4SXpudFE1U0pyZ282NlNVSUpHUGdnUmw3OHJxdUU2NkY2NUw2Q3N6d1hrRENJUFhsNmRmVXplQ0k4bVhGRWlDSTdibWJLZXRnci85MVNTL2NTS2ZLcVVKby93aHNLQXpxYmxpZ3c2b2NvS2daZURFbXV0bHpINkFqZU5kL01CMTVJbDZpZHVkQmJ4TEVsb1h5QlA5N0dsRHYwVWJwUW5tUnVQYndDRWcvT3EvUllkejgramFaQkVrYXRlTFJ4bXNnY3hac2ZrN1RzeXQyeFgzMzdLZUZWVTcxeWRhRHRaMTQxOTFYZFZ1bVhGTkJpakVJb3VEVW5aRDY3R1JQQXY5OVpYY25nT05ncFcyM2JEcUZHZ0kzSW95MWRqSnZTQi9qUDZBYTB1bTdOaWhrTWRSdm0iLCJtYWMiOiIzOWZkYzIzNjVjNjQzMDY2YmM4OTJiNTgwNjAxM2IzMDAwOWFjNTc3YWRjZmY2YmQ4YTRiZDBiZjRkZDQyN2QxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:12:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95964462\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1798711166 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XVsuooi9r4Xbc3TiFLlAVnTr0BIPLZg0Hyn0zo5r</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798711166\", {\"maxDepth\":0})</script>\n"}}