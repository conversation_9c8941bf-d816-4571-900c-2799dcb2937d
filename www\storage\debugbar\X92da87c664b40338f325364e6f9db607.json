{"__meta": {"id": "X92da87c664b40338f325364e6f9db607", "datetime": "2025-06-06 19:25:36", "utime": **********.805185, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237935.263555, "end": **********.805223, "duration": 1.5416679382324219, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749237935.263555, "relative_start": 0, "end": **********.590459, "relative_end": **********.590459, "duration": 1.326904058456421, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.590494, "relative_start": 1.3269388675689697, "end": **********.805227, "relative_end": 4.0531158447265625e-06, "duration": 0.21473312377929688, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44814960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00984, "accumulated_duration_str": "9.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6820142, "duration": 0.00536, "duration_str": "5.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 54.472}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.723017, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 54.472, "width_percent": 11.28}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.753041, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 65.752, "width_percent": 17.073}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.777235, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.825, "width_percent": 17.175}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-46395633 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-46395633\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1163117030 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1163117030\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1096114941 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096114941\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1790100430 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237923174%7C22%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFMM2VPZ0w4NG9FWjBCS0tPSTB6RUE9PSIsInZhbHVlIjoiWnBjV3hmNVpzd0dFdE5JWVZ6ejZVajNybDZJMVRvSHI1Mm0zeGxLMTY1UThQeTN1WWZsL1A3YUhuOWhnSkNxblpYS1dscmcvSW0yMnN2LzhKL3YrbWVZWDBCNEp0ci9sRitGS2Z2My9XNzZnd2hnQnZWY0w2Y09EZnl4ODBqZE96UDBlTHU0T1ZaWEZ4VU8wZWQzMVhYT3hrY3B0RGk4MHhoZnU3cldub3U0TDA5YTMzdC96b2xGNzNmeVBQTTJIMWpwQm5CQUJUTVZLVzhKVFl5N3RKVi91TFpQTTdZTDN5MjQ5aTN4aDlyRnBZNVhBWEhpUlR4MGE0aDd0Y3JMNWk3SzkwQ1NxZmhSZ3lwVENiREhJYmJIUE9mcTFGMmFzZW8zTERGSzZ2SjRVS1dVWmQxdFVMcThWZHFLZzJibW85N3I0T2hhcVhzVWdTT21BYUF5eEc5bzkwSXd4UFBEcFNPZjVPK2d1ZUQrUVRReUxuLy9UczhSMUJzVEhzcUhZODQ5ek9KTjkxOFlGSmgxQzB4aEJ2V1ZOcUpicnd6Sm5FK2g3UDZ1Z05aQXpIMXMvdlFrUXl0ZHhFKzlBQ3hleW9CeGUyZWtBRkczRXdON0ZkMFN0UU1za0k0Ukl5T2xYeFNkYlYxSDJFT1owd2JBdk5XQVdLK2JBU21HVTM5SWgiLCJtYWMiOiIzMzQ3MWNhNTZmYmE4MmM2ZmY1MGNjMjcyY2U4YzE2MmNmMDhhYjNhOWVmZjdjNjg2ZmEzMjYzM2RiYjk1ZTQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkxuekM1Y3FyV1hobnN3UWcrQ2lLakE9PSIsInZhbHVlIjoiZjZTMmpkeVQzT2YvVUhQNnQ5L3VESUFyLy9XODhvSzRQU1NXVUZHR0VMb2F6V0hHYTNqV25oUHEwV2pqeHFCbFVla09zajBGSDNmdWgvOGVPbTU2azh5WVhja0M1ZDAxM1RXM05yQ2dBZ050bGxSTzM4ZlJ2NlBkaW5sbzhwTGtSMWY4VmpLZnFFdDNrQUQ1Y29kVCtaZHAyc3MrREhDSVppc3REbFpvUUpNS1l4UjFuUDUxVGU1bllwZ3ZUNWgwYWxtTXpUNzRDeVF1WDdmVkxRTnB1MVNBNWl1MkN1NzA3L3RTdTlHOWh6ZWZNRjkxaURVWTZHY1VOYVY2Zm5BUkFob1hGWGVMTVRuaGlwWnJiTVdwWGJvdU52d2I3bS9SZ1JYbmNSTzlwY0psVU00OCtTV3NKTlZDRnZsOHlOaVc1aTFzT2h6QjlFSFp1UWRFZjMzS3RIVUtZbk5SVDU5ZnNQTE9YdUdhamZOQUt3WE53MzVSUDJBSVFkWVVUTXBKNDdlVXo4bTEwMFA5b1Z3QUJUd0JRVUplcWdkZ2c1bWtJTndkVG01Z1RMdFpheHJScEJFcUJzOTlSZWFKYnA4elJJamo0d2dUOWNYZ2MyaW85OFQrMEg2TkVCQTZDUHRrcm1BZEttOTRIblh4ckpaajZmb0tGZGFzUWovcXlLRTIiLCJtYWMiOiJiZjY5YzYzNzZmZGViYzYyNmY5Njc4ZjZjYWY1OWEyODNiM2E2NzVmYzdjZTUzNWU3MTJkMjg4NmMxYzVkNjI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790100430\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1561011772 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561011772\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-418264308 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:25:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImYvc0pmRjFSMGorTXZ5L0poMC96MkE9PSIsInZhbHVlIjoiZWFtSURMRDI1RXJnVGpVLzJOdVhMeW03QUx2U1E4MVd1NXZPKys3VU5Ubi9rUklkSGtaQTZuc3dlN1p4QkE3T0pBbEJqc2R4VGFDK2hHTFc2aGNjb1BqTTAzdXRXZ2hOaFBQMEhrSlVHUXdQSWYzWDZlV2VPb0dzQ0FPUStoaDVKQXZ2ZWEyZkI4VzdxYlpaSW9IUXk3UnQ0SWc5KzJIRitGVHl3RGJ1UFB4YkJrZklHS2pIeGthK0JFTzcySUhXK3YzMlJtWFNsWTQ4dnlwcHZzMkpRMm82b21JSlpuejVaZ2Y4VmNFVXVTUDFTdUh0V1BleHlaVHF4eDNkRGp5cjdId3NEUk84ZDk5dXNzN1NMb0ZUdW9DRlpJdWdMeW1qSERQbFlQano5Z1crMU1qa01HMXd0NEpGZEJFcjIwMDJ2cnZQcnNUL1FJQm9UbVdHRUx1cXJTeWd5Z0Q1d2sxSkhpTDc0TnVXV1pObDFnblI4WUZmT1pBK0pzNDFGczh6MDhub2E4QU0wb2ZDQVg1VU1KQ0diTkcySWljQkdxL3R3YkVROG9LMFZVTjE3ZVpZYW1wRFgwTzFNbjA2VHY4R1l6UEVPRU9lVmlRUVh5eW1yS0MxMUJkZTUzYmFkYmxJV3FzNjZTOXpSNzJEZVBXc2IyWk9jaERMYnpqbWp2UHIiLCJtYWMiOiI4ZGM1MTYwYmI0YjJhYTA4ZGJlNmY5YzYzNTQzYmU5OTRiZjc0OTk2M2ZhZDkzYzdmNzZhYzA4ZDU3ZTlmNmQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:25:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJvYThmNkdzUjdPUkJ5eXRYMDFDUVE9PSIsInZhbHVlIjoiQ0VwVUplNVBNWmUvcVA5cXV4eUFWckluM1NQTzlMcVlpMWl6c1NaTGZjS2haWE9rSmxkcXJqRmw1LzVGSDVlTk9FUDh2cSt6clhNdFBKZC9iRDJaRm9rNmJlVjNsTnlPOXpyN0p6VnVJS21JQjRDWkJQOFRDSVcxM3psa0dsK1p4T2tXYXZkTGpmakdRVWVPTVVsRXMwd25GaVlBTDNGL28yOTVncnE4UklVN0JmN3laUisvYVpTT0Z2TW96bWk3WSthOXk0OFNhL2JwcVNZbnFhU3M0b0tpU0NkV3U4UElnY0xhRnhBLzQwYnI5L01BMTdpZzZTQlk4alpncGF4alZ5TGMwbEZ5SlhLdU1aNGNKS0ZWdDQ0YkRWejJKSGtSa0xuLzIrZ3BaNXRtTFd0YnA4QjhvWE42dDV4ODFCc1NhR3ZJUWNoaEJiTkRWOEgvcFk5VEloTUFJL3UvcVVVQnM3OVJYMFFaL1hGK0RiYjVjSXZiR2htLzBVTHlsclNnd0xEUWNFQXY4UkNvT1c3WHc0OGxuTmtkZzZXQ2Y4RTYxTnpHUkZWQ280cERpWW1aS3ZaZjk3TndiSUFXUysvTm9Ld3lYSmJOK1lMTzY4RitIZUowM1BoU2h0bC9PTzVsUTlabjV5b0VPenRRMmxwUXFSL1NjTUppZ25sQUFPRGsiLCJtYWMiOiI1MTNkNWZhYmEzNTA0NDYzZDQ5MDc3MTQ1MTZjMmI3MWJiZTg4ZTYxM2Q4NDAxOTdjMmYwNTgwNmQwNTdmZGZlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:25:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImYvc0pmRjFSMGorTXZ5L0poMC96MkE9PSIsInZhbHVlIjoiZWFtSURMRDI1RXJnVGpVLzJOdVhMeW03QUx2U1E4MVd1NXZPKys3VU5Ubi9rUklkSGtaQTZuc3dlN1p4QkE3T0pBbEJqc2R4VGFDK2hHTFc2aGNjb1BqTTAzdXRXZ2hOaFBQMEhrSlVHUXdQSWYzWDZlV2VPb0dzQ0FPUStoaDVKQXZ2ZWEyZkI4VzdxYlpaSW9IUXk3UnQ0SWc5KzJIRitGVHl3RGJ1UFB4YkJrZklHS2pIeGthK0JFTzcySUhXK3YzMlJtWFNsWTQ4dnlwcHZzMkpRMm82b21JSlpuejVaZ2Y4VmNFVXVTUDFTdUh0V1BleHlaVHF4eDNkRGp5cjdId3NEUk84ZDk5dXNzN1NMb0ZUdW9DRlpJdWdMeW1qSERQbFlQano5Z1crMU1qa01HMXd0NEpGZEJFcjIwMDJ2cnZQcnNUL1FJQm9UbVdHRUx1cXJTeWd5Z0Q1d2sxSkhpTDc0TnVXV1pObDFnblI4WUZmT1pBK0pzNDFGczh6MDhub2E4QU0wb2ZDQVg1VU1KQ0diTkcySWljQkdxL3R3YkVROG9LMFZVTjE3ZVpZYW1wRFgwTzFNbjA2VHY4R1l6UEVPRU9lVmlRUVh5eW1yS0MxMUJkZTUzYmFkYmxJV3FzNjZTOXpSNzJEZVBXc2IyWk9jaERMYnpqbWp2UHIiLCJtYWMiOiI4ZGM1MTYwYmI0YjJhYTA4ZGJlNmY5YzYzNTQzYmU5OTRiZjc0OTk2M2ZhZDkzYzdmNzZhYzA4ZDU3ZTlmNmQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:25:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJvYThmNkdzUjdPUkJ5eXRYMDFDUVE9PSIsInZhbHVlIjoiQ0VwVUplNVBNWmUvcVA5cXV4eUFWckluM1NQTzlMcVlpMWl6c1NaTGZjS2haWE9rSmxkcXJqRmw1LzVGSDVlTk9FUDh2cSt6clhNdFBKZC9iRDJaRm9rNmJlVjNsTnlPOXpyN0p6VnVJS21JQjRDWkJQOFRDSVcxM3psa0dsK1p4T2tXYXZkTGpmakdRVWVPTVVsRXMwd25GaVlBTDNGL28yOTVncnE4UklVN0JmN3laUisvYVpTT0Z2TW96bWk3WSthOXk0OFNhL2JwcVNZbnFhU3M0b0tpU0NkV3U4UElnY0xhRnhBLzQwYnI5L01BMTdpZzZTQlk4alpncGF4alZ5TGMwbEZ5SlhLdU1aNGNKS0ZWdDQ0YkRWejJKSGtSa0xuLzIrZ3BaNXRtTFd0YnA4QjhvWE42dDV4ODFCc1NhR3ZJUWNoaEJiTkRWOEgvcFk5VEloTUFJL3UvcVVVQnM3OVJYMFFaL1hGK0RiYjVjSXZiR2htLzBVTHlsclNnd0xEUWNFQXY4UkNvT1c3WHc0OGxuTmtkZzZXQ2Y4RTYxTnpHUkZWQ280cERpWW1aS3ZaZjk3TndiSUFXUysvTm9Ld3lYSmJOK1lMTzY4RitIZUowM1BoU2h0bC9PTzVsUTlabjV5b0VPenRRMmxwUXFSL1NjTUppZ25sQUFPRGsiLCJtYWMiOiI1MTNkNWZhYmEzNTA0NDYzZDQ5MDc3MTQ1MTZjMmI3MWJiZTg4ZTYxM2Q4NDAxOTdjMmYwNTgwNmQwNTdmZGZlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:25:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418264308\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2000417302 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000417302\", {\"maxDepth\":0})</script>\n"}}