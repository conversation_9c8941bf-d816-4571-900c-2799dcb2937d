<?php

/**
 * سكريبت اختبار تحسينات أوامر الاستلام
 * يجب تشغيله من مجلد المشروع الرئيسي
 * php test_receipt_order_improvements.php
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\warehouse;
use App\Models\ReceiptOrder;

// ألوان للعرض
define('RED', "\033[0;31m");
define('GREEN', "\033[0;32m");
define('YELLOW', "\033[1;33m");
define('BLUE', "\033[0;34m");
define('NC', "\033[0m"); // No Color

function printStatus($message) {
    echo BLUE . "[INFO]" . NC . " $message\n";
}

function printSuccess($message) {
    echo GREEN . "[SUCCESS]" . NC . " $message\n";
}

function printError($message) {
    echo RED . "[ERROR]" . NC . " $message\n";
}

function printWarning($message) {
    echo YELLOW . "[WARNING]" . NC . " $message\n";
}

echo BLUE . "================================\n";
echo "    اختبار تحسينات أوامر الاستلام    \n";
echo "================================" . NC . "\n\n";

try {
    // اختبار 1: التحقق من وجود العلاقة creator في نموذج ReceiptOrder
    printStatus("🔍 اختبار 1: التحقق من العلاقة creator...");
    
    $receiptOrderModel = new ReceiptOrder();
    $relations = $receiptOrderModel->getRelations();
    
    if (method_exists($receiptOrderModel, 'creator')) {
        printSuccess("العلاقة creator موجودة في نموذج ReceiptOrder");
    } else {
        printError("العلاقة creator غير موجودة في نموذج ReceiptOrder");
    }

    // اختبار 2: التحقق من وجود حقل warehouse_id في جدول المستخدمين
    printStatus("🔍 اختبار 2: التحقق من حقل warehouse_id...");
    
    $userModel = new User();
    $fillable = $userModel->getFillable();
    
    if (in_array('warehouse_id', $fillable)) {
        printSuccess("حقل warehouse_id موجود في نموذج User");
    } else {
        printError("حقل warehouse_id غير موجود في نموذج User");
    }

    // اختبار 3: اختبار جلب أوامر الاستلام مع العلاقات
    printStatus("🔍 اختبار 3: اختبار جلب أوامر الاستلام مع العلاقات...");
    
    try {
        $receiptOrders = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator'])
            ->limit(5)
            ->get();
        
        printSuccess("تم جلب أوامر الاستلام مع العلاقات بنجاح");
        
        foreach ($receiptOrders as $order) {
            $creatorName = $order->creator ? $order->creator->name : 'غير محدد';
            echo "  - أمر رقم: {$order->order_number}, المنشئ: {$creatorName}\n";
        }
        
    } catch (Exception $e) {
        printError("خطأ في جلب أوامر الاستلام: " . $e->getMessage());
    }

    // اختبار 4: اختبار المستخدمين والمستودعات
    printStatus("🔍 اختبار 4: اختبار المستخدمين والمستودعات...");
    
    try {
        $usersWithWarehouse = User::whereNotNull('warehouse_id')->count();
        $usersWithoutWarehouse = User::whereNull('warehouse_id')->count();
        $totalWarehouses = warehouse::count();
        
        echo "  - مستخدمين بمستودع محدد: {$usersWithWarehouse}\n";
        echo "  - مستخدمين بدون مستودع محدد: {$usersWithoutWarehouse}\n";
        echo "  - إجمالي المستودعات: {$totalWarehouses}\n";
        
        printSuccess("تم جلب إحصائيات المستخدمين والمستودعات");
        
    } catch (Exception $e) {
        printError("خطأ في جلب إحصائيات المستخدمين: " . $e->getMessage());
    }

    // اختبار 5: محاكاة منطق تخصيص المستودعات
    printStatus("🔍 اختبار 5: محاكاة منطق تخصيص المستودعات...");
    
    try {
        // اختبار مستخدم بمستودع محدد
        $userWithWarehouse = User::whereNotNull('warehouse_id')->first();
        if ($userWithWarehouse) {
            $warehousesForUser = warehouse::where('id', $userWithWarehouse->warehouse_id)
                ->where('created_by', $userWithWarehouse->creatorId())
                ->get();
            
            echo "  - مستخدم '{$userWithWarehouse->name}' لديه مستودع محدد: {$warehousesForUser->count()} مستودع\n";
        }
        
        // اختبار مستخدم بدون مستودع محدد
        $userWithoutWarehouse = User::whereNull('warehouse_id')->first();
        if ($userWithoutWarehouse) {
            $allWarehouses = warehouse::where('created_by', $userWithoutWarehouse->creatorId())->get();
            echo "  - مستخدم '{$userWithoutWarehouse->name}' بدون مستودع محدد: {$allWarehouses->count()} مستودع متاح\n";
        }
        
        printSuccess("تم اختبار منطق تخصيص المستودعات بنجاح");
        
    } catch (Exception $e) {
        printError("خطأ في اختبار منطق المستودعات: " . $e->getMessage());
    }

    // اختبار 6: التحقق من أداء الاستعلامات
    printStatus("🔍 اختبار 6: التحقق من أداء الاستعلامات...");
    
    try {
        $startTime = microtime(true);
        
        $receiptOrders = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator'])
            ->limit(10)
            ->get()
            ->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'creator_name' => $order->creator ? $order->creator->name : 'غير محدد',
                    'warehouse_name' => $order->warehouse->name ?? 'غير محدد',
                ];
            });
        
        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);
        
        printSuccess("تم تنفيذ الاستعلام في {$executionTime} ميلي ثانية");
        
    } catch (Exception $e) {
        printError("خطأ في اختبار الأداء: " . $e->getMessage());
    }

    echo "\n" . GREEN . "================================\n";
    echo "       انتهى الاختبار بنجاح! 🎉        \n";
    echo "================================" . NC . "\n\n";

    printSuccess("جميع الاختبارات تمت بنجاح");
    printWarning("يرجى اختبار الواجهة يدوياً للتأكد من عمل التحسينات");

} catch (Exception $e) {
    printError("خطأ عام في الاختبار: " . $e->getMessage());
    exit(1);
}

echo "\n" . BLUE . "📋 ملخص التحسينات:" . NC . "\n";
echo "✅ إصلاح عرض اسم المنشئ باستخدام العلاقة creator\n";
echo "✅ تخصيص المستودعات حسب warehouse_id للمستخدم\n";
echo "✅ تحسين الأداء باستخدام eager loading\n";
echo "✅ تحسين تجربة المستخدم\n";

echo "\n" . BLUE . "🧪 خطوات الاختبار اليدوي:" . NC . "\n";
echo "1. تسجيل الدخول بحساب Cashier\n";
echo "2. فتح صفحة أوامر الاستلام\n";
echo "3. التحقق من عرض أسماء المنشئين\n";
echo "4. إنشاء أمر استلام جديد\n";
echo "5. التحقق من المستودعات المتاحة\n";

echo "\n" . BLUE . "انتهى الاختبار! ✨" . NC . "\n";
