<?php
/**
 * اختبار التعرف على المستخدمين في جدول النماذج التشغيلية
 */

// تحديد المسار الأساسي
$basePath = __DIR__;

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار التعرف على المستخدمين - النماذج التشغيلية</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".info { color: #17a2b8; background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }";
echo "th { background: #f8f9fa; font-weight: bold; }";
echo ".test-case { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }";
echo ".badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; margin: 2px; }";
echo ".badge-success { background: #28a745; color: white; }";
echo ".badge-info { background: #17a2b8; color: white; }";
echo ".badge-warning { background: #ffc107; color: black; }";
echo ".badge-danger { background: #dc3545; color: white; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 اختبار التعرف على المستخدمين - النماذج التشغيلية</h1>";

// فحص قاعدة البيانات
try {
    // محاولة الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=localhost;dbname=up20251", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // فحص النماذج التشغيلية
    echo "<div class='info'>";
    echo "<h2>📋 النماذج التشغيلية في قاعدة البيانات:</h2>";
    
    $stmt = $pdo->query("
        SELECT f.*, u.name as creator_name, u.type as creator_type
        FROM forms f
        LEFT JOIN users u ON f.created_by = u.id
        WHERE f.type = 'operational'
        ORDER BY f.created_at DESC
    ");
    $operationalForms = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($operationalForms) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم النموذج</th><th>الأدوار المسموح لها</th><th>منشئ النموذج</th><th>نوع المنشئ</th><th>تاريخ الإنشاء</th></tr>";
        
        foreach ($operationalForms as $form) {
            $visibleRoles = json_decode($form['visible_to_roles'], true);
            $rolesText = '';
            
            if (is_array($visibleRoles)) {
                foreach ($visibleRoles as $role) {
                    if ($role == 'all') {
                        $rolesText .= "<span class='badge badge-success'>All</span> ";
                    } else {
                        $rolesText .= "<span class='badge badge-info'>{$role}</span> ";
                    }
                }
            } else {
                $rolesText = "<span class='badge badge-warning'>غير محدد</span>";
            }
            
            echo "<tr>";
            echo "<td>{$form['id']}</td>";
            echo "<td><strong>{$form['name']}</strong></td>";
            echo "<td>{$rolesText}</td>";
            echo "<td>{$form['creator_name']}</td>";
            echo "<td><span class='badge badge-info'>{$form['creator_type']}</span></td>";
            echo "<td>{$form['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد نماذج تشغيلية في قاعدة البيانات</div>";
    }
    echo "</div>";
    
    // فحص النماذج المالية
    echo "<div class='info'>";
    echo "<h2>💰 النماذج المالية في قاعدة البيانات:</h2>";
    
    $stmt = $pdo->query("
        SELECT f.*, u.name as creator_name, u.type as creator_type
        FROM forms f
        LEFT JOIN users u ON f.created_by = u.id
        WHERE f.type = 'financial'
        ORDER BY f.created_at DESC
    ");
    $financialForms = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($financialForms) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم النموذج</th><th>الأدوار المسموح لها</th><th>منشئ النموذج</th><th>نوع المنشئ</th><th>تاريخ الإنشاء</th></tr>";
        
        foreach ($financialForms as $form) {
            $visibleRoles = json_decode($form['visible_to_roles'], true);
            $rolesText = '';
            
            if (is_array($visibleRoles)) {
                foreach ($visibleRoles as $role) {
                    if ($role == 'all') {
                        $rolesText .= "<span class='badge badge-success'>All</span> ";
                    } else {
                        $rolesText .= "<span class='badge badge-info'>{$role}</span> ";
                    }
                }
            } else {
                $rolesText = "<span class='badge badge-warning'>غير محدد</span>";
            }
            
            echo "<tr>";
            echo "<td>{$form['id']}</td>";
            echo "<td><strong>{$form['name']}</strong></td>";
            echo "<td>{$rolesText}</td>";
            echo "<td>{$form['creator_name']}</td>";
            echo "<td><span class='badge badge-info'>{$form['creator_type']}</span></td>";
            echo "<td>{$form['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد نماذج مالية في قاعدة البيانات</div>";
    }
    echo "</div>";
    
    // فحص المستخدمين والأدوار
    echo "<div class='info'>";
    echo "<h2>👥 المستخدمين والأدوار المتاحة:</h2>";
    
    $stmt = $pdo->query("
        SELECT u.id, u.name, u.email, u.type, 
               GROUP_CONCAT(r.name) as roles
        FROM users u
        LEFT JOIN model_has_roles mhr ON u.id = mhr.model_id AND mhr.model_type = 'App\\\\Models\\\\User'
        LEFT JOIN roles r ON mhr.role_id = r.id
        WHERE u.type IN ('company', 'accountant', 'Employee', 'Cashier', 'Delivery')
        GROUP BY u.id
        ORDER BY u.type, u.name
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>النوع</th><th>الأدوار</th></tr>";
        
        foreach ($users as $user) {
            $userType = $user['type'];
            $typeClass = $userType == 'company' ? 'badge-success' : 'badge-info';
            
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td><span class='badge {$typeClass}'>{$userType}</span></td>";
            echo "<td>" . ($user['roles'] ?: '<span class="badge badge-warning">لا توجد أدوار</span>') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>⚠️ لا توجد مستخدمين</div>";
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
}

// اختبارات التحقق
echo "<div class='success'>";
echo "<h2>🧪 اختبارات التحقق:</h2>";

echo "<div class='test-case'>";
echo "<h3>1. اختبار عرض النماذج للمستخدمين من نوع Company:</h3>";
echo "<p><strong>التوقع:</strong> يجب أن يرى جميع النماذج (التشغيلية والمالية)</p>";
echo "<p><strong>الكود:</strong></p>";
echo "<div class='code'>";
echo "if (Auth::user()->type == 'company') {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;// يرى جميع النماذج<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;return self::orderBy('created_at', 'desc')->get();<br>";
echo "}";
echo "</div>";
echo "</div>";

echo "<div class='test-case'>";
echo "<h3>2. اختبار عرض النماذج للمستخدمين الآخرين:</h3>";
echo "<p><strong>التوقع:</strong> يرى النماذج المخصصة لدوره أو المخصصة للجميع فقط</p>";
echo "<p><strong>الكود:</strong></p>";
echo "<div class='code'>";
echo "// فحص الأدوار المخصصة للمستخدم<br>";
echo "\$userRoles = \$user->getRoleNames()->toArray();<br>";
echo "// فحص النماذج المرئية للأدوار<br>";
echo "whereJsonContains('visible_to_roles', 'all')<br>";
echo "orWhereJsonContains('visible_to_roles', \$role)";
echo "</div>";
echo "</div>";

echo "<div class='test-case'>";
echo "<h3>3. اختبار العلاقة مع منشئ النموذج:</h3>";
echo "<p><strong>التوقع:</strong> يجب عرض اسم منشئ النموذج بشكل صحيح</p>";
echo "<p><strong>الكود:</strong></p>";
echo "<div class='code'>";
echo "// في Form Model<br>";
echo "public function creator() {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;return \$this->belongsTo(User::class, 'created_by');<br>";
echo "}<br><br>";
echo "// في Blade<br>";
echo "{{ \$form->creator->name ?? 'غير محدد' }}";
echo "</div>";
echo "</div>";
echo "</div>";

// إرشادات الاختبار
echo "<div class='warning'>";
echo "<h2>📋 خطوات الاختبار اليدوي:</h2>";
echo "<ol>";
echo "<li><strong>اختبار مستخدم Company:</strong>";
echo "<ul>";
echo "<li>سجل دخول كمستخدم من نوع 'company'</li>";
echo "<li>اذهب لصفحة النماذج</li>";
echo "<li>يجب أن ترى جميع النماذج في الجدولين (التشغيلية والمالية)</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>اختبار مستخدم Cashier:</strong>";
echo "<ul>";
echo "<li>سجل دخول كمستخدم له دور 'Cashier'</li>";
echo "<li>اذهب لصفحة النماذج</li>";
echo "<li>يجب أن ترى النماذج المخصصة لـ Cashier أو All فقط</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>اختبار مستخدم Accountant:</strong>";
echo "<ul>";
echo "<li>سجل دخول كمستخدم له دور 'Accountant'</li>";
echo "<li>اذهب لصفحة النماذج</li>";
echo "<li>يجب أن ترى النماذج المخصصة لـ Accountant أو All فقط</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<div class='info'>";
echo "<h2>🔗 روابط مفيدة للاختبار:</h2>";
echo "<ul>";
echo "<li><a href='/forms' target='_blank'>صفحة النماذج الرئيسية</a></li>";
echo "<li><a href='/forms/create' target='_blank'>إنشاء نموذج جديد</a></li>";
echo "<li><a href='/debug-forms' target='_blank'>API التشخيص</a></li>";
echo "<li><a href='/dashboard' target='_blank'>الشاشة الرئيسية</a></li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
