{"__meta": {"id": "X58c576402fa0cd31ad82fc144d5178c0", "datetime": "2025-06-06 20:39:47", "utime": **********.953425, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242386.200154, "end": **********.953469, "duration": 1.7533149719238281, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1749242386.200154, "relative_start": 0, "end": **********.656768, "relative_end": **********.656768, "duration": 1.4566140174865723, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.656787, "relative_start": 1.4566328525543213, "end": **********.953475, "relative_end": 5.9604644775390625e-06, "duration": 0.2966880798339844, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.022500000000000003, "accumulated_duration_str": "22.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7606518, "duration": 0.01623, "duration_str": "16.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 72.133}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8034492, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.133, "width_percent": 4.444}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.896415, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 76.578, "width_percent": 15.2}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.923294, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.778, "width_percent": 8.222}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-570708672 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-570708672\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1594743721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1594743721\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-284124984 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284124984\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-309718175 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242380855%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitlY3U1WGt0K3laMmM0L3BJR1piQ1E9PSIsInZhbHVlIjoiNTl0Q3MvSVpuUVV4Y1VLSkdsajBhcVg1a2Z3SFlSMFNMYU9iYjJOM0pHRkZkaHhlZU5ZOE44bWk2d0dNTnd3NWdzNkRlSFhrbExLNFh4UFFwZWMxMnRtMTI3VVY0Q2FvUE1lZTNZNSszTHY2d3cxSGJiTnJtcDZSa1JsVi9GNHRTeUFXTEk5TmtJbHpVbG41QmFLTzBpbk9hamovQytvbHJwQzU1S25RMDFwZEdxcVNmRWpITkFmR3FCMjQyZG5iY0J5Ri90RnB1QStFS3Z5dWVkOThKaE55OTJGeEdjU2NDUEs4ZHhYa1N3UXNMN1NVWWhCTjhUSkVNOEtMZVJpTFYzWk9sWkZFMHhtclBXcjcwYjNiWE5uTkt5eTJPeDgvbmprZjNmNnMvUmNtSVVRcmduTisreC9DeGJqMmdnR25WZDg0ZVMyRFY1V3dZdUxpV0NpeDJqN3htbDIxV2ZXa2ZwT2V6Q3BXVkVsTDU0WFBnNEErbndTVHRualV6b2g1elovMEd4QjZXbGdLck9JMnZLNGQzcy9LNzREcmNxTDY5eXhVZGJxQVl2Y3MvWkFLd0JFTCtUWGxiMmY0U0tMY0hNRWhQN3FOY0RGQWFhMCtGNkl3K0tvTG50V3JEZysrUlNxT1lOMHUwcHFnQytFRmJNNzdOZ2N5aExnOGlEQkkiLCJtYWMiOiI4MTc4NTIyN2E2MjdkNjdmOWZhMzE1OWEzMTQ4NTA5ZDg3Y2Y0YTUyMTE0OTkxYjQwZDkyZDg4ZTg2MTZlOTcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkllbkFqWXN5WGlsLzNtNlZnRlk5eGc9PSIsInZhbHVlIjoiWFBvOEFTRmp3MnRqbXBnRHcvUjlXeGNKTytTa1lOWDlnNW9MZWtKcEhremJ6WkZETDBXYTVVUHUyYzQrNk5iMnN1VE5UbHh2alRGM3ZqcXpiQVRBcU9WdU53YklpaEx4eUdPek9ScVM2Ym05dW5EZkdlWWE3cXU4ek9ZM0lUK0t6VUkrRHZWUEQ2bHJqaEVZaDZ4cXl1bG5FZFFJdDg4NDU4b2daalVEd2FtUFFLNWFxMW9BZ3hLSU94N1U1UzhPbmR6bE53dzEzYWxzVkRyNlNjTG9QcHRnVTNqbmdxWTJVQVBhbDdaMWZraUo1WnIyQUkxbytBRHlLWk14aFZyN1JzTWlxbitndUtDMVp3dE40T3BCUG1CKzY0TnNEZUtXRWlReG1VU2tHN2lBYmdrZEN0aHkwdDlnaVhmL1N0TngwTGJRZ1ZWMUFMVXQveTFhU0pVNk9vZTB2OVlaSVNQbzJSM25SZUUrelZqdFBYUjh1aTNBWHljL1hUVHhkVDR5aW5MMEo0WkRXeUNFdytpSC9ObHZrazdGd2xEZmhNRStvbmhQWkd0WmFWMy9Id3k0bU5kMkVTMVlRcVg3VEk5d3lxZHZ6TjBOT094RTFWTllSNmpsZmF0RXMrWGwzQUZSc0dUTHdmQ0JzTENqMUxpZFpmaVpMY1FyT3RHSnZDRXciLCJtYWMiOiJlYzBiY2IyMDdiMTFjOGQ4OTU1NTBiZDZiOGNlYTY5YWIwNjM2MDAyZTQ5N2VlMDUwMjM5YzdkYzE4ZjE5ZTQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309718175\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1939823792 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5SyoJjvx9ER6DwaeNylpmlrCKyunmBMNBrM7fYhk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939823792\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1389604534 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:39:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1jYnd6a2xSNkVQaVZZaGphc2NtQWc9PSIsInZhbHVlIjoiMEVkRkNOS1kySWhHTVNGM083bm9rOUxzNklIYnRyUUgwayswOWcvKzNvcURGOWNjSUMxcE1Jb0h4UjkxMFoxZHk3SzJsZE45U05vcDV4eTFMa2hzVHNmOWZTYVVJKzM4emVMZlg2WjBDeGN5T1hib2JjMG5kTURlZnF2YkZ5UytLMjJuL01SUkprcmFDVlJneUZrdzAyelB0cTVKTGF3ZXhJU25Qa0hQQUNHZE9DZmVUL1Y3aE90NGs0YTcyMFRrYk5IbjE0ejVCYlgzTVQ0R0tEcWErV2pKTlM2Y0FIWWhmUUttWDNDSHNYSVY2SGhkWmtQZWpDRkRHZlBMbXcxd0FMMnFwZGRLUFl0NnhubVY4M2xqb1J6K0wzc29ORDBPdG83Y0o2Z3dKMWYrYnBqNEthc3hnUzQ1MlBNY3Y4MmhoN2NNdDBkcXNQcmtQcVRubE5XWGtuQ1dURm9reVdPUzZENUIwVmFkQmp0Umt1WEJKY3FzM083VkVxenFub2x4UXI4eUcwK2k0RWNjZHhTYWFRYUhpMnFBallrb1RCc3A1SXZSRk1QKzFLODlwaGJyZ0k2anp0QS9CbHZFV2MydUxEYXYrSDI3UmpKQ0ZOcUhLWjEzeG93MkVydm5JdmJHUW9ZM3pqYWRaQ0U3OS9LRUE2NENram93Y3pCY2x2NUsiLCJtYWMiOiI3MTY0MTZhMDAyZTIwZjJmODg0ZmQ4NmUyNjllNDZjZTAxNzhjNjRhZTFhNWQ5M2RiNGEyYjE4MjY5ZmE1NjM2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InEvWjNHMHhIdWgzazhYcGM0R0pob2c9PSIsInZhbHVlIjoiNlFTa3RDT3c4Ni8yZU1XNEE2RU02SHNjQ1pkRzBFdGpzVmpidkpzbW9JcXlqRE1SbFZ6V3JoQmx1RWZ1QlZQeGxlb0x1eDBoOEYwSWdpYWVDUEMxU1lRTFFqMjlnVndUMVFrN0tQVmZJSkhCd1RzOHVrT1NYK2hRSXNxSTVWNVlsS1pndHBiWFJjUHN2N2FnakNCQzM2QmNBTGZ2UWZ3U2dsM0JPSy9NL2xzcFNMRzRIemtSdkVGckJDV0xtbUJ6ME5pc1drYmc2T0ZmeE4zK09ubXQ1RG1VYWJxbnVianIrVTdtaG01Zk9lRi9mRzBsMmVtWU5sNlBWZHFwSmlWWFNkdG92UzlxMytXbXU3SmttalRuMGluV05MOFJiYldkMlZicldxSlB6eGlBa29pVkVFUTFFKzBjS3FXdmxWb3dtcTBFVHRrZ3NtbktncmppWHFUaUxSUVFheFFJZEY1dFJxa2VMWE90bFpnVXlNY1lId01rd0FGdWVOV2d1NFQzNHpqcklDUitXV0hVUHNjc2k2aDlwVFJVOXdtREVFV1ZRa1dRYXY5aFBSdGRGUmUyK1VzZTBQTlVubnNyeWZiRHQzc2RiZ091Yjk2dWUzb0xLVUpSQStLTzd1R2FHUDNmU2NKOUFrQmpGUFFXZ2lXRXNsTHplcThrUE44N3lVL3QiLCJtYWMiOiI4MTFhYmU3ZDQ5YmE0YmQzMjA4YmFhYmZjNzZkYTE3YmM3NjZjNzY4ZTE1NzU5NDNlNjIzM2JhZmM1YjFhZGQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:39:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1jYnd6a2xSNkVQaVZZaGphc2NtQWc9PSIsInZhbHVlIjoiMEVkRkNOS1kySWhHTVNGM083bm9rOUxzNklIYnRyUUgwayswOWcvKzNvcURGOWNjSUMxcE1Jb0h4UjkxMFoxZHk3SzJsZE45U05vcDV4eTFMa2hzVHNmOWZTYVVJKzM4emVMZlg2WjBDeGN5T1hib2JjMG5kTURlZnF2YkZ5UytLMjJuL01SUkprcmFDVlJneUZrdzAyelB0cTVKTGF3ZXhJU25Qa0hQQUNHZE9DZmVUL1Y3aE90NGs0YTcyMFRrYk5IbjE0ejVCYlgzTVQ0R0tEcWErV2pKTlM2Y0FIWWhmUUttWDNDSHNYSVY2SGhkWmtQZWpDRkRHZlBMbXcxd0FMMnFwZGRLUFl0NnhubVY4M2xqb1J6K0wzc29ORDBPdG83Y0o2Z3dKMWYrYnBqNEthc3hnUzQ1MlBNY3Y4MmhoN2NNdDBkcXNQcmtQcVRubE5XWGtuQ1dURm9reVdPUzZENUIwVmFkQmp0Umt1WEJKY3FzM083VkVxenFub2x4UXI4eUcwK2k0RWNjZHhTYWFRYUhpMnFBallrb1RCc3A1SXZSRk1QKzFLODlwaGJyZ0k2anp0QS9CbHZFV2MydUxEYXYrSDI3UmpKQ0ZOcUhLWjEzeG93MkVydm5JdmJHUW9ZM3pqYWRaQ0U3OS9LRUE2NENram93Y3pCY2x2NUsiLCJtYWMiOiI3MTY0MTZhMDAyZTIwZjJmODg0ZmQ4NmUyNjllNDZjZTAxNzhjNjRhZTFhNWQ5M2RiNGEyYjE4MjY5ZmE1NjM2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InEvWjNHMHhIdWgzazhYcGM0R0pob2c9PSIsInZhbHVlIjoiNlFTa3RDT3c4Ni8yZU1XNEE2RU02SHNjQ1pkRzBFdGpzVmpidkpzbW9JcXlqRE1SbFZ6V3JoQmx1RWZ1QlZQeGxlb0x1eDBoOEYwSWdpYWVDUEMxU1lRTFFqMjlnVndUMVFrN0tQVmZJSkhCd1RzOHVrT1NYK2hRSXNxSTVWNVlsS1pndHBiWFJjUHN2N2FnakNCQzM2QmNBTGZ2UWZ3U2dsM0JPSy9NL2xzcFNMRzRIemtSdkVGckJDV0xtbUJ6ME5pc1drYmc2T0ZmeE4zK09ubXQ1RG1VYWJxbnVianIrVTdtaG01Zk9lRi9mRzBsMmVtWU5sNlBWZHFwSmlWWFNkdG92UzlxMytXbXU3SmttalRuMGluV05MOFJiYldkMlZicldxSlB6eGlBa29pVkVFUTFFKzBjS3FXdmxWb3dtcTBFVHRrZ3NtbktncmppWHFUaUxSUVFheFFJZEY1dFJxa2VMWE90bFpnVXlNY1lId01rd0FGdWVOV2d1NFQzNHpqcklDUitXV0hVUHNjc2k2aDlwVFJVOXdtREVFV1ZRa1dRYXY5aFBSdGRGUmUyK1VzZTBQTlVubnNyeWZiRHQzc2RiZ091Yjk2dWUzb0xLVUpSQStLTzd1R2FHUDNmU2NKOUFrQmpGUFFXZ2lXRXNsTHplcThrUE44N3lVL3QiLCJtYWMiOiI4MTFhYmU3ZDQ5YmE0YmQzMjA4YmFhYmZjNzZkYTE3YmM3NjZjNzY4ZTE1NzU5NDNlNjIzM2JhZmM1YjFhZGQ1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:39:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389604534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1510633192 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510633192\", {\"maxDepth\":0})</script>\n"}}