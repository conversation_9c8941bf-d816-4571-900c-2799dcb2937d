# 🛒 دليل جدول مبيعات POS الجديد - منطق العجز والفائض

## 🎯 نظرة عامة

تم تطوير جدول مبيعات POS ليعكس الوضع الحقيقي للنقد والعجز/الفائض بدلاً من مجرد عرض أنواع المدفوعات.

## 📊 الأعمدة الجديدة

### 1. 📅 **التاريخ**
- **بدون تغيير**: تاريخ المبيعات مجمعة يومياً

### 2. 👤 **المستخدم**
- **بدون تغيير**: اسم الكاشير المسؤول

### 3. 🏪 **المستودع**
- **بدون تغيير**: اسم المستودع أو الفرع

### 4. 📋 **عدد الفواتير**
- **بدون تغيير**: عدد الفواتير المباعة

### 5. 💰 **إجمالي المبيعات** (جديد)
- **المصدر**: `SUM(pos_payments.amount)`
- **الوصف**: إجمالي قيمة جميع الفواتير المباعة
- **يشمل**: جميع أنواع الدفع (نقد + شبكة + مختلط + توصيل)
- **مثال**: `1,000.00` ريال

### 6. 💵 **إجمالي المحصل** (جديد)
- **المصدر**: `current_cash + delivery_cash + overnetwork_cash`
- **الوصف**: إجمالي النقد والشبكة المحصل فعلياً
- **يشمل**: 
  - النقد في الصندوق (`current_cash`)
  - النقد لدى موظفي التوصيل (`delivery_cash`)
  - المبالغ عبر الشبكة (`overnetwork_cash`)
- **مثال**: `900.00` ريال

### 7. ⚖️ **العجز/الفائض** (جديد)
- **الحساب**: `إجمالي المبيعات - إجمالي المحصل`
- **الوصف**: الفرق بين المبيعات والمحصل
- **الحالات**:
  - **عجز**: قيمة موجبة (المبيعات أكثر من المحصل)
  - **فائض**: قيمة سالبة (المحصل أكثر من المبيعات)
  - **متوازن**: صفر (المبيعات = المحصل)

## 🔍 أمثلة عملية

### مثال 1: حالة العجز
```
إجمالي المبيعات: 1,000.00 ريال
إجمالي المحصل: 900.00 ريال
العجز/الفائض: +100.00 ريال (عجز)
```
**التفسير**: يوجد عجز 100 ريال (ربما نقد لم يُحصل بعد من التوصيل)

### مثال 2: حالة الفائض
```
إجمالي المبيعات: 800.00 ريال
إجمالي المحصل: 850.00 ريال
العجز/الفائض: -50.00 ريال (فائض)
```
**التفسير**: يوجد فائض 50 ريال (ربما نقد إضافي من مصادر أخرى)

### مثال 3: حالة التوازن
```
إجمالي المبيعات: 1,200.00 ريال
إجمالي المحصل: 1,200.00 ريال
العجز/الفائض: 0.00 ريال (متوازن)
```
**التفسير**: الوضع مثالي - جميع المبيعات محصلة

## 🎨 التصميم البصري

### ألوان الصفوف:
- **🔴 أحمر**: العجز (قيمة موجبة)
- **🟡 أصفر**: الفائض (قيمة سالبة)
- **🟢 أخضر**: متوازن (صفر)

### الأيقونات:
- **⚠️ تحذير**: للعجز
- **➕ زائد**: للفائض
- **✅ صح**: للتوازن

### CSS Classes:
```css
.deficit-cell {
    background-color: rgba(220, 53, 69, 0.05);
    border-left: 3px solid #dc3545;
}

.surplus-cell {
    background-color: rgba(255, 193, 7, 0.05);
    border-left: 3px solid #ffc107;
}

.balanced-cell {
    background-color: rgba(40, 167, 69, 0.05);
    border-left: 3px solid #28a745;
}
```

## 🔧 الاستعلام الجديد

```sql
SELECT 
    DATE(pos.pos_date) as sale_date,
    users.name as user_name,
    warehouses.name as warehouse_name,
    shifts.id as shift_id,
    COUNT(DISTINCT pos.id) as invoice_count,
    -- إجمالي مبيعات اليوم
    SUM(pos_payments.amount) as total_sales,
    -- النقد المحصل فعلياً
    MAX(COALESCE(financial_records.current_cash, 0) + 
        COALESCE(financial_records.delivery_cash, 0)) as actual_cash_collected,
    -- النقد عبر الشبكة
    MAX(COALESCE(financial_records.overnetwork_cash, 0)) as network_cash
FROM pos
JOIN pos_payments ON pos.id = pos_payments.pos_id
JOIN users ON pos.created_by = users.id
JOIN warehouses ON pos.warehouse_id = warehouses.id
JOIN shifts ON pos.shift_id = shifts.id
LEFT JOIN financial_records ON shifts.id = financial_records.shift_id
WHERE pos.pos_date BETWEEN ? AND ?
GROUP BY sale_date, pos.created_by, pos.warehouse_id, shifts.id
ORDER BY sale_date DESC
```

## 📊 منطق الحساب

### في Controller:
```php
$totalSales = floatval($sale->total_sales);
$actualCashCollected = floatval($sale->actual_cash_collected);
$networkCash = floatval($sale->network_cash);

// حساب إجمالي النقد والشبكة المحصل
$totalCollected = $actualCashCollected + $networkCash;

// حساب العجز أو الفائض
$deficitOrSurplus = $totalSales - $totalCollected;
```

### في JavaScript:
```javascript
const deficitSurplusValue = parseFloat(sale.deficit_surplus_raw || 0);

if (deficitSurplusValue > 0) {
    // عجز
    deficitSurplusClass = 'text-danger fw-bold';
    deficitSurplusIcon = '<i class="fas fa-exclamation-triangle me-1"></i>';
    rowClass = 'deficit-cell';
} else if (deficitSurplusValue < 0) {
    // فائض
    deficitSurplusClass = 'text-warning fw-bold';
    deficitSurplusIcon = '<i class="fas fa-plus-circle me-1"></i>';
    rowClass = 'surplus-cell';
} else {
    // متوازن
    rowClass = 'balanced-cell';
}
```

## 🔍 أسباب العجز المحتملة

### 1. **طلبات التوصيل**
- فواتير توصيل لم يُحصل نقدها بعد
- موظف التوصيل لم يسلم النقد

### 2. **مشاكل تقنية**
- عدم تسجيل دفعة بشكل صحيح
- خطأ في نوع الدفع

### 3. **مشاكل إدارية**
- نقد مفقود أو مسروق
- خطأ في العد

## 🔍 أسباب الفائض المحتملة

### 1. **مصادر إضافية**
- سندات قبض نقدية
- نقد من مصادر أخرى

### 2. **أخطاء في التسجيل**
- تسجيل دفعة مرتين
- خطأ في المبلغ

## 📈 فوائد النظام الجديد

### 1. **مراقبة دقيقة**
- رؤية فورية للعجز/الفائض
- تحديد المشاكل بسرعة

### 2. **إدارة أفضل للنقد**
- معرفة النقد الفعلي المتاح
- تخطيط أفضل للإيداعات

### 3. **تحليل الأداء**
- مقارنة أداء الكاشيرين
- تحديد الأنماط والمشاكل

### 4. **اتخاذ قرارات مدروسة**
- بيانات دقيقة للإدارة
- تحسين العمليات

## 🚨 التنبيهات والإجراءات

### عند وجود عجز:
1. **تحقق من طلبات التوصيل**
2. **راجع سندات القبض**
3. **تأكد من صحة التسجيل**
4. **اتصل بموظفي التوصيل**

### عند وجود فائض:
1. **راجع مصادر النقد الإضافي**
2. **تحقق من عدم التسجيل المزدوج**
3. **راجع سندات القبض**

### للحفاظ على التوازن:
1. **تسجيل دقيق للمعاملات**
2. **متابعة يومية للأرقام**
3. **تدريب الموظفين**
4. **مراجعة دورية للعمليات**

هذا النظام الجديد يوفر رؤية حقيقية ودقيقة للوضع المالي ويساعد في اتخاذ قرارات مدروسة! 📊
