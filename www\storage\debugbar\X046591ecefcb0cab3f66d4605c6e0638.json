{"__meta": {"id": "X046591ecefcb0cab3f66d4605c6e0638", "datetime": "2025-06-06 19:37:33", "utime": **********.496828, "method": "POST", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238651.695351, "end": **********.496861, "duration": 1.8015100955963135, "duration_str": "1.8s", "measures": [{"label": "Booting", "start": 1749238651.695351, "relative_start": 0, "end": **********.264441, "relative_end": **********.264441, "duration": 1.5690901279449463, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.26447, "relative_start": 1.5691192150115967, "end": **********.496864, "relative_end": 3.0994415283203125e-06, "duration": 0.23239398002624512, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44988944, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=75\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:75-235</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.021530000000000004, "accumulated_duration_str": "21.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4237401, "duration": 0.020460000000000002, "duration_str": "20.46ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:78", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=78", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "78"}, "connection": "ty", "start_percent": 0, "width_percent": 95.03}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.465163, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:81", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=81", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "81"}, "connection": "ty", "start_percent": 95.03, "width_percent": 4.97}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"status\"\n  ]\n  \"new\" => []\n]", "status": "Your Account is disable from company.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1541496009 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">140</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=1jor13%7C2%7Cfwj%7C0%7C1983; XSRF-TOKEN=eyJpdiI6Ilh1ckVQQVh0WlJSdkREdldXWFY4K2c9PSIsInZhbHVlIjoiR1k4OGg4OHM0Yi9KbGZZcFdKZEc5RjQyZFcxaFQwM2JmNjlOY2tTd0ZmVWJEUzd3MEU1cXI4TjY1MmNwYTI1cVErSXFkL1RiaWVwYTBXWnBJRnJsbkJ2aEUxUFhWa0JvR0lyRnloSDYzaDVKdVBqeU1zdFBTMDlxc1JTalJFT1VFdVhodTMyWElxeXhSblRUZzZEZTNCczBab29wT1MwZzhhRkhvcXRrd2d2K09EeGJBZGIwS1F6RnhaQnprdkpvcENFMCs3YVRVZ2NodkNCYXc0dFlha0dmbSt1T0laMFAyZXZPbDFxbnVpQ1BFZWtER1RjYVNEdmlUUldDczJUVFl3b0RLLzFONFo4SFg2RzBxbXJQbm1iVDJ4dUppcmNDNnJUc0RmM25uL1Z1b2hFT2FlV1dicVhsVjR0aTRVdEg5N0E1S2poOWdTT1hBdm1ZTUFmNDk5U0pmcTlqL2hLODcwMHlBR3pmemNiZllteGNadzRvdlB5UmdsOVBSaVNhRDVPbmE3L2hEK2doSUhoSGpCeGh1bCsydVpQdWRFbjRYK1AvcWl2NktYQWhNbEdoK2hzRFJhaTdzVXlDR3p0NmQ1SGVJQUkwblVxSlpZM0s1b0ZxR3c2UU51QkpWeWFCb1l6R1h3aFlBMVQ3SzZ1bEdZb2VFL2RCeWZ5dEowZkYiLCJtYWMiOiI3ZGE1NGFmZWRlMThjNDE5M2IyMDE3MGYyYmYyNGYyYWViZTMyYTEyOTIwY2E0YzAwOWM3Njk2YjJjNjZkNThiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik95VWdscmRSWCtVVVVLdlErZkdwQ0E9PSIsInZhbHVlIjoibXJ6dFRiYTV5Z3dtb09pOCtnT2VsdzdROWZWTFM5T0YyNWFVbFZMZTBCZjdzamMyTTAyRVozYnZyRjZjUnlEMVlmZkZuVTV5MXZLelNWYU9LT2FDS3lYOFp5bVdwc0dReStxSWxrMHFBNWxtb1p6ZTZ2MmNoNjFsYlVxekVOcjNIU3N0Um5QVi9oRUdTdTRzVmZueXZOWVdNNkd6MTJtMHhFWWRDcHpzYjZLQzlTUTcrUFNUSWF5ZzlxckRDVVhpQ1JrUUJvd3ZrNUI0Ti91aVZGL05laDk0Q2JYK2JTNi9LRmJpbHNIbDNWb1Ryb2tiWTFFYzFKOVBoNitXL2hSOCtlWDR4V2lCYlA2YTVzU3lNZW8zajJ2TU5RcXRzenk0VVN2d2tZcjlKSjBqYVoyNHozUjhhRjZRZEJYdnJGQVNVN1ppY0RocVpqWU5jakNSVDRzdTJkWW1wWUxibkpJbGxnckxieXVRbnN3ZHJQNEZvSFRRZDBjZU01QTZjQTArQkxuWFArcmpyb0VvRFJoNmc2NmYzU2lhSzQ4enFzUEpId1Fobi9aNkhFOWh1dGJESWNhWkxZb2dOVnVnc3lOcXVPd1VaZWI1QWRIRHpQNUxpb1duT2UwUTNyTzJNU09zcnJCUmlDMjViZjRVUEdhOXVldDk0c05tUTk0RWJrL20iLCJtYWMiOiJlZmMyMjYyMWI4ZDQwMWQwODBiMjg3Y2JhMGE4NzMzOTczMDgxZmQ5ZGU4MTdiNTNhOTc5YzJlMjM1NzdmNTQ4IiwidGFnIjoiIn0%3D; _clsk=151odr7%7C1749238626577%7C3%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541496009\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-496684539 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496684539\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-450816266 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:37:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlkrMTdETEVzZVVUakhncWxLME9Sc0E9PSIsInZhbHVlIjoiU0ZHNU5qZVBidkZmSGQ2dFhuNm9uOVRyc0tyaGw1QWZpVDcwRGNweDl0aHo4aWloM3ljWTdOelphUE5oYWJHdDJOWTltTXIwWkFsUk9MSVBZdkdZRUMxd28rbTRIL25aK0x4Nyt3NloydVdmMTlzaFJ6WDIwOXM2VHV2SUx5NUFhU0JTRlNwREJ6VzRGdm95cHlCUlA3bVdHekEwYnEyUFcvWnlmNGxoWXJjRkxqMEtNVzBJejRYVlFrZzVjMXEzK2RXa1lmcWVLVXB3YjR4VEVqN2Qvd3luMjhyZnZvR1lybHJ2Nnk1bE1vY2pTV3hWZ2d0WjhJNkdGT2FiZkttRmJWQ3FjcERwM0dNTG5paHk5RDRJZFlMQ1phK3J2eUR0eHlGUWhTS0V3VWQxa3AveVUxcDBiaEFmZmUveXUrM0FhWmZMSVVWMUVkZENXbGx4WnY0ejlDakJjQ2s5UG4wSGZkU2xxajBpUSs2WHIrQ1paK3p0WWVIalF5RUlIVC9nQ2M0RVpaSlpCQTR2ak5mMW9WQzFhMDhaaXdaRTRxeUJwQXVaZlRPNnF5ckVZMEkyUC9zSUpiajdHU1lqTE96dFJMeVZMTjltOEluaWtlSXRXa1R2QVMwZW5UUUZGYlUyUkl0L1JUaElZY25NTUg3ZVIyOWk2Vk1kYU5UYyt1Mm8iLCJtYWMiOiI2NzYwZjRhZmIyMmE3NWFiZmE1MmVlMmJkY2Y3OGI5YzY2NmFlYmFkYzgzZDUwZjg5MmI4YzMyNzgwZGYwMTk1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFsY3hOeVd5U21va0RiRGtOZ2NSRUE9PSIsInZhbHVlIjoiTjdzSjFKYWpPKzdiYUZ4T085SXJ1VDVKQ2FKdnpVUTd5THhmQTlGZDZSWUp5aUowV0RpUXVNZ0RQcUJDNHlMQmpIYUxIWUppdWRpMk8vOFEvcHhNUzltcWc0OEoxSzNnM3Y5am1ldm50dmdzbjQ3aDdITGFiTlg2VVFwbnZMUE5iTHNTRjRRUzV1azBuRWZuL0toVnVVRzdaVTk1Ny8wbGNaQjNxQ3lsaFpzUXJ4MXB3STlGSVVLais3L3JiNzlUWVBOeW9GVDhOd0NWQlVxVnhZQkI4UHhWaVRTZkI3SDlSRVdJVnpqNHhVZ1dvWWRGTURiRzlneDRlUmN4TVdIcmdFTE9ic0h1WnJvMW0xenliMUNjN000NU9uY3d5VnhsVG9CVXBwVFZsOGQxYzZzMzBjTmIrZ1hzazZwOTUwZXRiSlZSc21PM3p2SWJMalVhMnplcTJiMFlDd3NWbDh4WENQUTJLd3NzVldpYVB1b2RueTVkblpBazFaVGU4dGdSaVpZTjBPRW9hNmpwbGV5RnR6WDlLaUF5ZXBFdmFNSDBQalZ0QzRKSkp1d05WY1VLREROUDBCU1d0Qmh3VFhyS2dQcWZxc2JpVTdmMWQ2Uzc3VDdNZUtIUEt3clpQaGgzWTltMUFnZjQ2MlJ0L0ZiT0NXRWtEbmRISkJNYXNpTmUiLCJtYWMiOiJmOTYxYjZkM2UzN2NiZWU3MTgwNGY4YzYwNTJlNTM4OTI1NTE4ZjNjMWEwYzk2NDAxMGIxMDU1OTQxZTBmYzFiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlkrMTdETEVzZVVUakhncWxLME9Sc0E9PSIsInZhbHVlIjoiU0ZHNU5qZVBidkZmSGQ2dFhuNm9uOVRyc0tyaGw1QWZpVDcwRGNweDl0aHo4aWloM3ljWTdOelphUE5oYWJHdDJOWTltTXIwWkFsUk9MSVBZdkdZRUMxd28rbTRIL25aK0x4Nyt3NloydVdmMTlzaFJ6WDIwOXM2VHV2SUx5NUFhU0JTRlNwREJ6VzRGdm95cHlCUlA3bVdHekEwYnEyUFcvWnlmNGxoWXJjRkxqMEtNVzBJejRYVlFrZzVjMXEzK2RXa1lmcWVLVXB3YjR4VEVqN2Qvd3luMjhyZnZvR1lybHJ2Nnk1bE1vY2pTV3hWZ2d0WjhJNkdGT2FiZkttRmJWQ3FjcERwM0dNTG5paHk5RDRJZFlMQ1phK3J2eUR0eHlGUWhTS0V3VWQxa3AveVUxcDBiaEFmZmUveXUrM0FhWmZMSVVWMUVkZENXbGx4WnY0ejlDakJjQ2s5UG4wSGZkU2xxajBpUSs2WHIrQ1paK3p0WWVIalF5RUlIVC9nQ2M0RVpaSlpCQTR2ak5mMW9WQzFhMDhaaXdaRTRxeUJwQXVaZlRPNnF5ckVZMEkyUC9zSUpiajdHU1lqTE96dFJMeVZMTjltOEluaWtlSXRXa1R2QVMwZW5UUUZGYlUyUkl0L1JUaElZY25NTUg3ZVIyOWk2Vk1kYU5UYyt1Mm8iLCJtYWMiOiI2NzYwZjRhZmIyMmE3NWFiZmE1MmVlMmJkY2Y3OGI5YzY2NmFlYmFkYzgzZDUwZjg5MmI4YzMyNzgwZGYwMTk1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFsY3hOeVd5U21va0RiRGtOZ2NSRUE9PSIsInZhbHVlIjoiTjdzSjFKYWpPKzdiYUZ4T085SXJ1VDVKQ2FKdnpVUTd5THhmQTlGZDZSWUp5aUowV0RpUXVNZ0RQcUJDNHlMQmpIYUxIWUppdWRpMk8vOFEvcHhNUzltcWc0OEoxSzNnM3Y5am1ldm50dmdzbjQ3aDdITGFiTlg2VVFwbnZMUE5iTHNTRjRRUzV1azBuRWZuL0toVnVVRzdaVTk1Ny8wbGNaQjNxQ3lsaFpzUXJ4MXB3STlGSVVLais3L3JiNzlUWVBOeW9GVDhOd0NWQlVxVnhZQkI4UHhWaVRTZkI3SDlSRVdJVnpqNHhVZ1dvWWRGTURiRzlneDRlUmN4TVdIcmdFTE9ic0h1WnJvMW0xenliMUNjN000NU9uY3d5VnhsVG9CVXBwVFZsOGQxYzZzMzBjTmIrZ1hzazZwOTUwZXRiSlZSc21PM3p2SWJMalVhMnplcTJiMFlDd3NWbDh4WENQUTJLd3NzVldpYVB1b2RueTVkblpBazFaVGU4dGdSaVpZTjBPRW9hNmpwbGV5RnR6WDlLaUF5ZXBFdmFNSDBQalZ0QzRKSkp1d05WY1VLREROUDBCU1d0Qmh3VFhyS2dQcWZxc2JpVTdmMWQ2Uzc3VDdNZUtIUEt3clpQaGgzWTltMUFnZjQ2MlJ0L0ZiT0NXRWtEbmRISkJNYXNpTmUiLCJtYWMiOiJmOTYxYjZkM2UzN2NiZWU3MTgwNGY4YzYwNTJlNTM4OTI1NTE4ZjNjMWEwYzk2NDAxMGIxMDU1OTQxZTBmYzFiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450816266\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Your Account is disable from company.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}