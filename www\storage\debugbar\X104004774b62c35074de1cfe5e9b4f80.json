{"__meta": {"id": "X104004774b62c35074de1cfe5e9b4f80", "datetime": "2025-06-07 04:33:42", "utime": **********.106473, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270820.778442, "end": **********.106513, "duration": 1.328071117401123, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749270820.778442, "relative_start": 0, "end": 1749270821.947651, "relative_end": 1749270821.947651, "duration": 1.1692090034484863, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749270821.947669, "relative_start": 1.169227123260498, "end": **********.106518, "relative_end": 5.0067901611328125e-06, "duration": 0.15884900093078613, "duration_str": "159ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44765320, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00684, "accumulated_duration_str": "6.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.022338, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.69}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.056416, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.69, "width_percent": 16.813}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.074931, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.503, "width_percent": 15.497}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1959494214 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1959494214\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1637559346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1637559346\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-32778827 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32778827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1222264504 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270800184%7C15%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJKaGh0amJMR3cxTGw4d1RSajF2V1E9PSIsInZhbHVlIjoiRm5GZmZRcUp3QnF1aUNHUmFOeGdzMldzYWZxU0dyd1dyaU9ZS2EvUjU1cXBSVlo1akFuMXd3bXBCL0tXY3BYWW1WRFkvWXVYTC9ocitDT0ovSFFwTGtZUzJra2NnbE1NS3E3b3JvUDAwcUhCeVRvOWRUbFdkZkVIaS85MHp0SmtCQ3hDOWpIdjNHZmxGcTBsZy9oTXJnSTRvQXpCQklLZm5nd01yRzJoRU00ZTgrMk9GL3B4bVJsak1IWlAyVUljUnNVdFUvRUlZQWYrMWR3YnNzOURoRElBREVZcmVTYVI0YXNTWldYY21EeEIvTnM2ZmdMc2FMcWdwMkVlT21xVlJVK0N4Zk4xaGNjQ2xCM1NONjUxZkdqY3lyZXVtbWI4Y2ZGUzBIeE42WW1sZmx6ZHdIeVl4V3NGalBWU1BPdE96dXNXL2xGbWpST29wU0RjSTNPazlSbkx4eXc4TUc1N2lYZWxXTDNnTVJnN2toR0V3QzVBMDFNZEdVUUtWRkZ2M1ZGbndqZUQrUWtmMGJUWVpDNEdqaTFDRHBjdzJMMURFUW95TkR1Vk5VbnlWWnJkdUw2ZjdUMWQxNTJlSXl3ZGpjdTQ1eXdmNENueGZ3ZysvaFdwdGpVamF3dWR4b1h4dURxaGlOeS9BeGl5U1B2OGlBK1lmYVRwK2w5dVFnWXgiLCJtYWMiOiI0MDAwNTIyYzE0ZTczN2EyYmVjMmU5OGQ1ZDhkZmRlNWJhYTI4MjA5MTFkNWY0MjcyMDY5MmY4ZWE5OTY5YjI3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpaaCthQzJ2c2M4ejh0Tmk2bll6UXc9PSIsInZhbHVlIjoibVBka0ZPSmYxRk00RVV4Vmd6NW1HcmkwSHA5bnByUXVncS94YndDWTk1Q3BaMkhvazNjdkNDOUV0QjNhYnduaG1senQ1cHZLU0Q5ZG9Ed0xTR09pSFpKc2xHSnNrM2NoNkFtMVJWNnVWekdZcHdZamptcENYV2hMbkdoemgvREFlNTdNY2FydFVmbWh5NkQ1dGFrZzhIUnBGZ0gvMStINTdIdDQ0elRaQUhaalpFb1RydW9keFhwYTlLbU5oMU9jVFZGZy9wNHovVXduOHhmNGRqQ0RicTgzUld0anZ4MUlvNitldVVEMkJQQjlEYmhZOEQ2ZkhMNU5XT2xNMUppRkpFRXJTRHBrMDZpOFRHSGFJWUdNWVlMZ0ZLSVJEL0NRK1pyZ29mbStLaTZ5Z3RDQVlNZlFiMXZjMXJHQWtxeElkcVlrdFdIcVgzTVRoWEpSNVc4VHN6aU02UVBkY3o2UmRSYWJxbkRNajdYSnJvdm9IVUg4MXh2SnBaYmp2Qi9GNm1QbGM5TnMzN1BOd3FBdSs0UnRYWWhsL2FLcXp4Mlg4VmZXaUFiWkRtZ1hyOTY5Q0EyL0k0MVNUTTRvWTJoZWJidXRvY3RFUTlCZmgyNDRaeTRza2dPeGtoQ0tYUXJTYXRReGw4d2ZHMUJoQStYUjU5R1lRbitPMkdMbGhMREkiLCJtYWMiOiI0OWY2ZTdkMmI1YzZlMmZjNjVjM2MxNDJjNWM4YTA4YTBhMTdjZGY5N2Y2YmY4MGM1OGU0N2JmMmY1MWEyZDIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222264504\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-85684455 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zyhWlMrUupyybJnMtPnBjJ716zpTnMkeCiqRAqWb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85684455\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1113938212 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:33:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklHRXJ4QWM5b3JvT2NoVi9jNDl6V1E9PSIsInZhbHVlIjoiRmIxT0doenMzVVZiZHFuL3pIVldFbW5KcGkyeXpIdzhETGZreGpRWlRDT3RyR1JYVHRpNEs4Y3c2Zi9OdXhwY3VKWnpXK1dGc3AxVkdwWmh3UXdqOE52SVZ4d0JmQWlpcmlTL3BPQjBaZ253RjgzTVFQYVRIZXhSNDFxU2FDZmtjZTZtK0wxeHVvM3pzNTd0TXhYU3c1eDFHdDE1U2YvTWE3M01qbWx4dzg5Snp6MldRWStpVjZLNDBJNUt2TjQraDNiT3VtMHkxZGVuT3VKSzBSYzJMbno0WVZ5MDNNdXFTUUdwZTdYK3E3Nk1USFlDVEdMNnRGZXlpa1BGQU13SklPaHpZUi91KzRtVHNROTJaQ3ZCc2lBeWxZRmpKcHFMWHpOVU4va21mVy9xcmpNeXBhYTZvaGs4cTYwY1c4U25rS1l1MUdwZ01pUXpBMGdUMHpETUFRWkxDbDdsdzdLeHRyTWpwVmRVZlB6RHExdXp5TXRhdEd4VHU2WWJWYTNtWE9kRFppMFh3Uno0M1cwR3FKdForcWFQZDlmTTczWHdEWXZZZjk4cDZYdnZoN1o1dWJWT0k1eldWUWhZeG9yaWZ5Y0VnTTFBMWtEMjJHWXJlMlM2NE9DL3dTRzM2UUlSZ0pmRkVhS09pSkFmVXlHRWJpRldDR3pOeUtMaWxPc2QiLCJtYWMiOiIzNTNkZTIwMDMwMDk0ZTdhZjRiZjcxM2VlMTUyMDg5MzkwNzZlOTVjYTI0NmNjMjFkNjdkMTY5MGI3ODU1M2U3IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVpYmwvYXVZZ3BLOHVBS2ErcUxWYWc9PSIsInZhbHVlIjoiTzlqQVhzYytSVkhsMktvU1U1N3RUZlNZMHFjMXNQckFhY0xrS29vZnQzTnhvMnRyR1Z3VUFma1d0Sk04dUVQNWpmd3V6bXlqL21JTnROOGVFNnVNbjl4a0FpY3ZjRmNVcmtPMlFvYWV4NHJHeE9zdklneitrVUtGNE5vQTVReEx6VndBbDJOcGdrUEd3cEpvM0Q5ZU5GbXh5WVNuYWRQQSt4UHVZem5IL0xuN0ZGSHkxbnJqeERmLzZ3bkN2Q0VDYm5kRXE2TE9kd0NXcmYwSTE3ZG8wZC9WVU5HMVdnNGVyWis2K0NTc01zMCtkRjRpeTN6WlA3c3U2RlQvVU0rOXZlZEk0Q1RuQ3RhejdUcE5xRmxFQmhZc3dLTmo5TFlTRHRlcnhZWnIxazd0V1E2S0FXYThwd25QQlpsWGtqWGY3Q0NwNU5OYXhjWFZrMHZ0WEh2bms4T3hMNVNXYWdReVUrb3BWdjRuUFQ4cEpLb1FremVjaUVEdEt6NnBQY1Vua050ZzljNzlkUW9QZmNmL0pVaElFZjEvMGZod2xyMG1rZzVSMXF1OWFNL1hsN3VrWHJSbldPdVNKZ3lOaThIOW9mcXJsY3NDajhlUmMyM1BxWjBqTytjRW5rRFp0NnBSZlZjcGgxbEhQZHRNMUJYTXprSFJtMkpvUlVKeVlFdkMiLCJtYWMiOiI2YTE1ZGRhYjc0NWMzZDliNzczNGMxNTVlMzg0NDI3NGQzZWNmMmQ4YWJjNjQ2N2JhN2I2OGI4YmQxMjQ2ZGVjIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:33:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklHRXJ4QWM5b3JvT2NoVi9jNDl6V1E9PSIsInZhbHVlIjoiRmIxT0doenMzVVZiZHFuL3pIVldFbW5KcGkyeXpIdzhETGZreGpRWlRDT3RyR1JYVHRpNEs4Y3c2Zi9OdXhwY3VKWnpXK1dGc3AxVkdwWmh3UXdqOE52SVZ4d0JmQWlpcmlTL3BPQjBaZ253RjgzTVFQYVRIZXhSNDFxU2FDZmtjZTZtK0wxeHVvM3pzNTd0TXhYU3c1eDFHdDE1U2YvTWE3M01qbWx4dzg5Snp6MldRWStpVjZLNDBJNUt2TjQraDNiT3VtMHkxZGVuT3VKSzBSYzJMbno0WVZ5MDNNdXFTUUdwZTdYK3E3Nk1USFlDVEdMNnRGZXlpa1BGQU13SklPaHpZUi91KzRtVHNROTJaQ3ZCc2lBeWxZRmpKcHFMWHpOVU4va21mVy9xcmpNeXBhYTZvaGs4cTYwY1c4U25rS1l1MUdwZ01pUXpBMGdUMHpETUFRWkxDbDdsdzdLeHRyTWpwVmRVZlB6RHExdXp5TXRhdEd4VHU2WWJWYTNtWE9kRFppMFh3Uno0M1cwR3FKdForcWFQZDlmTTczWHdEWXZZZjk4cDZYdnZoN1o1dWJWT0k1eldWUWhZeG9yaWZ5Y0VnTTFBMWtEMjJHWXJlMlM2NE9DL3dTRzM2UUlSZ0pmRkVhS09pSkFmVXlHRWJpRldDR3pOeUtMaWxPc2QiLCJtYWMiOiIzNTNkZTIwMDMwMDk0ZTdhZjRiZjcxM2VlMTUyMDg5MzkwNzZlOTVjYTI0NmNjMjFkNjdkMTY5MGI3ODU1M2U3IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVpYmwvYXVZZ3BLOHVBS2ErcUxWYWc9PSIsInZhbHVlIjoiTzlqQVhzYytSVkhsMktvU1U1N3RUZlNZMHFjMXNQckFhY0xrS29vZnQzTnhvMnRyR1Z3VUFma1d0Sk04dUVQNWpmd3V6bXlqL21JTnROOGVFNnVNbjl4a0FpY3ZjRmNVcmtPMlFvYWV4NHJHeE9zdklneitrVUtGNE5vQTVReEx6VndBbDJOcGdrUEd3cEpvM0Q5ZU5GbXh5WVNuYWRQQSt4UHVZem5IL0xuN0ZGSHkxbnJqeERmLzZ3bkN2Q0VDYm5kRXE2TE9kd0NXcmYwSTE3ZG8wZC9WVU5HMVdnNGVyWis2K0NTc01zMCtkRjRpeTN6WlA3c3U2RlQvVU0rOXZlZEk0Q1RuQ3RhejdUcE5xRmxFQmhZc3dLTmo5TFlTRHRlcnhZWnIxazd0V1E2S0FXYThwd25QQlpsWGtqWGY3Q0NwNU5OYXhjWFZrMHZ0WEh2bms4T3hMNVNXYWdReVUrb3BWdjRuUFQ4cEpLb1FremVjaUVEdEt6NnBQY1Vua050ZzljNzlkUW9QZmNmL0pVaElFZjEvMGZod2xyMG1rZzVSMXF1OWFNL1hsN3VrWHJSbldPdVNKZ3lOaThIOW9mcXJsY3NDajhlUmMyM1BxWjBqTytjRW5rRFp0NnBSZlZjcGgxbEhQZHRNMUJYTXprSFJtMkpvUlVKeVlFdkMiLCJtYWMiOiI2YTE1ZGRhYjc0NWMzZDliNzczNGMxNTVlMzg0NDI3NGQzZWNmMmQ4YWJjNjQ2N2JhN2I2OGI4YmQxMjQ2ZGVjIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:33:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113938212\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1142322439 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142322439\", {\"maxDepth\":0})</script>\n"}}