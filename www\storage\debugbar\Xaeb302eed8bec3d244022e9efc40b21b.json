{"__meta": {"id": "Xaeb302eed8bec3d244022e9efc40b21b", "datetime": "2025-06-06 19:14:30", "utime": **********.460677, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237268.822875, "end": **********.460712, "duration": 1.6378369331359863, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749237268.822875, "relative_start": 0, "end": **********.239609, "relative_end": **********.239609, "duration": 1.416733980178833, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.239638, "relative_start": 1.4167630672454834, "end": **********.460715, "relative_end": 3.0994415283203125e-06, "duration": 0.22107696533203125, "duration_str": "221ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44752448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02607, "accumulated_duration_str": "26.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3571708, "duration": 0.02341, "duration_str": "23.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.797}, {"sql": "select * from `settings` where `created_by` = 14", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.416213, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.797, "width_percent": 4.411}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (14) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.436448, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.208, "width_percent": 5.792}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-904120014 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-904120014\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-526730075 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-526730075\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-282457403 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-282457403\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1425015730 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRGTlpvWVFVblU5RFZ3QWpiNlZHaFE9PSIsInZhbHVlIjoibVVMUnc0bU9WY1NqK2RHWEh5TU9Wak9xNWJwekRBNTN0V09ObmVYOTRLd3pPWGxzQmhzSkJPTUhMUHBCa3BTbXdFZ3QyTjVsNHNnTmpMd29QaldkOFdEUDRhbU1TeVpoOWlRRi9JYnhkWWZZckhFLzRpSkNnSHF5ZWxNK0g4MXRMY29WaVFFQmhSYnROWUs3UEFjZWwxYVYxTm1zenMxYnBpUS84L2w2Q1RPdWhadHpxdDZnRUVHc2hWNHdqSUhrckpyMlZpajBmQWNzdy9URWRMLzdaU3RvZE5wTTk4aDNWQm9oQ0xKdUtvTlB4QUkzZVpkeFN6TTJSdGRSc0JUekVXa04zaEhjMk85bFJXOWJBaWhNUUduRENXZGdGdDFzcmRpYlMvL3BkQjA0YnJ4bklyV0FwK2JuNlNYeTZ4UFpiVXZRNzgxSVVLeTIvaitJZC9teUhCem1TRTJFOTEzWEt3UzQvZFBsSytEUmhueHhJNzVSdW9vRzF0cjh6eG4xanRNWlhBMVAwaS9XTEV1UzlLVUtRcWtHM1JlYVd4OHR6SnV5RGtlYnp4MGxNcXJsb3ZYejkrUUZ4eGlHTlpVUlB2bG4vYWV0a25nRUdDb1hMYlJBQlhrWlc0RStqRE1wb1ZNZGhQd3owS3ZFb0pyeTJBNzZ4dG9PWk16VTY1MXQiLCJtYWMiOiJmMmRlNzI1MTYzNzMzNTk2OGRlODY3MTRkNTlkOTUyZmQ2NGYyOGM4NWIwMzk2ZTZjNmVkYWY2YjZhNDg0MjJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNXSnhlL1NmTVZVemNqOTgrUzl0RFE9PSIsInZhbHVlIjoiQmI3Qkc5WllnV2tZMWhBZTNiUU0wcXlVQ0dQb3QvNGxxd1NEb2JLanJyeE5uTzU5ZkhKSlJTQlhsVW1pOS9PY3c4YnJHZWUrOWNUaDRncDRPMnpQZkc5cWxFczd0eW1zOHcrLzFOUlM4TVNDMU1KbklJN013T2ZiT0N0MVlsL1RSS0NndXp2R09pS3BndW5CYi8yRnhuekE5cW14OW9GakFuQUhod3JneVhNWE5LM2FyeUo1d2RCRytaTGp4cXY5U0JmOUFEUkV2S2VKNTkybHNLZjdoVGs4Mk96MndGa2FHSWVCWVd1S0VBQVA0Y3ZFVHVsamNpVG1MZTQxS3RleGgyMlBIWUpha0RtNGlrYjMrbEhtQ0dDb1dMZm5XaC8xcFFyWGdCVmttdzR0NHdJeFlKTVR2Zy9iajg0RHFvUW1oK0wxelBZTUpkamh4Tmk1QWxjM1NkRHAveWdyZFR4cUhSWVFFNktVOTNOL1hEcTh3bm1EcFY5dHpCdXhvdDY1MzFydVpZL1ptV0JHc1Q5WXYzbGNINURwNkQ1TUNkdWZnUnJjZDdGdjZLeTRsMGQvR213cFZDK3FsMDgzU0RkN0xmbjkwUzZ0TFN5UThFTU9iRWlTVnlZaTQwakxZa1JZQWw4a205Vk1Hd1N3UGI3YkRuVm04TVZ0aXlZOUd5SWciLCJtYWMiOiIxZTg4ODEwYmJiZTU5OTUwYjU2OWIwOTg3YjBmODQ4N2IxNWU1MWYwN2RlOGNlODE0NjAxNDA5N2JiZmQxM2U1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425015730\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1905458768 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zIcPizjUuP36LmzYoUV7mN5b6EUrGSqs8aa1I11K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905458768\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1965025528 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:14:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFhUXlMZ3ZKdzRSTWZ4RzdPY0JObWc9PSIsInZhbHVlIjoiVjVBNWNMVjA0Zy9lWkQ0clhGQzZreGFLaHpiLzdkOTUrS0ljMThFcjF6WVZ4d0FjMFB6MHV1eGNRbkhMTjAwcTZQdXcxZmhKbExBQlk5WVFlQ0dVR3ZNRWVxVy8rZVd4WUd2dzhKYmhlekJGd2RMZG9wM1U0SGpveUFhTk10RGx2amdPSzZ5Ym9neVdITWhodVBQVXFQQUJEZTJPd05QYWlXNGF4RUtEVHJzZ2M0OWJHTG1oZ0s4MUdMZHVtdHBxdEQvOFI3TXRGUVNhajVvSjg1QVRRUlZnZlgzZy9lMURHWi82SGRrckRBVHRCOEJxaHA0V1gwblBRTng3R1FlWEp6VzBEUm5xVHdpOFR4WHFxNmxhSklRZGJ1OWpHKy9FN3ZCV0JTd2dkTHBRTXg0UHlBUnMvL0NaNzN0R2xIaFUrY3gvT0w5UWdDTHhMMnBjVHlLQkx1ZkRyeHg1ajZCL2k2L1UvTWcwc2M2NjBDZElVU3BLb2ZqaGVUT2RmZTE0bmdIR1dYdHpCbnRaa2ZMbG5ITFpUem1DekVHZDB0aXkrT2hnOGl3Sk5PZk91T0xXV2o0RTJkS3VyVVIxcHhUd0F3bDFIc2pXVEJienNIbUVDRjBObUxzTzB2dUc0SXhsZ2NXTDYwMWhvYm03ZGZvdDhid3VSeUFRcHI3eWppZVkiLCJtYWMiOiJlZjUyMzU5ZjE1MzE3ZmE0ZTU5NWIzOTg3OWYyODAxZDExNzdkYjIwNjdmMDBlOWMwZTIwNzFmYmFhOTBhZWM1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijc4aThpekZpUDJNbFlmUkNNaHdRRGc9PSIsInZhbHVlIjoiblFQUG1zcU9UU0JPZ2paTWM4ZW85VFI0WVNsTVFtU2l3ZTFlYnFHcHVaaHBvcHdTTmpMUm1FK0VzK3lkcW1RTmdVSzZIU1dOWGFYWmN0b3JjT2pHQVR4VUVOa25Hc3VSdlM5c3F5bHl6bzBBc2JIajZIV3pDakFVbU9zTlk2L3lNNVd4K29LK2FYbStlSTVvdDYwVmxDNFo0blZTa0xZL2tKU1grRDEzeXQrT1FQVGhMTW9aZUF3dDRsWEhDNDc3TGoyZk13OEMwTFBNT0huRGp2YXB4Y0VWMkp0VXpMSXQ2MmxoSHIrNDYxVnpmWWMrOENyYVJ5L2loSmVJSFJ1b0hoV1JjYmxlVUFXYlRqdmZSSkxKaS84TEhqVFBqUTVFWDMxaWJOREl3c0VqamZ3UkFsaVBFNE5Ody9QdWFMZmxsNVVveVdybnkra1diUmxoTWZrd1NEZzVnZWtDYjg2SUtQaVhvUVgzYWxDOTJkTlhQS2IyR2lWcWp2RGJhQTZCaE8xS3k3UEtYeHdaY2pBQi8xckhhVytjeXBJTjFWTDMxU290NzQ5bzBSdGlWSnk0bDdVVUQ4T2pZa3VXREtXK01QV05GK25SS3RqUm1LQk1GVE5UQzMydTQwUDNnUHZjcjRBU2JZR1lHb1JhWkJ1czJKbFhYdGNod0htNFFDcFYiLCJtYWMiOiI1Y2MzNDg2Njg2MTJiYWYzNjlkZDQ1MzgxMDJmNmQxYTEyYzMxMjY5MWRjYjdmYWMxYTk2MWYzMjQ1YWRkZmY4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:14:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFhUXlMZ3ZKdzRSTWZ4RzdPY0JObWc9PSIsInZhbHVlIjoiVjVBNWNMVjA0Zy9lWkQ0clhGQzZreGFLaHpiLzdkOTUrS0ljMThFcjF6WVZ4d0FjMFB6MHV1eGNRbkhMTjAwcTZQdXcxZmhKbExBQlk5WVFlQ0dVR3ZNRWVxVy8rZVd4WUd2dzhKYmhlekJGd2RMZG9wM1U0SGpveUFhTk10RGx2amdPSzZ5Ym9neVdITWhodVBQVXFQQUJEZTJPd05QYWlXNGF4RUtEVHJzZ2M0OWJHTG1oZ0s4MUdMZHVtdHBxdEQvOFI3TXRGUVNhajVvSjg1QVRRUlZnZlgzZy9lMURHWi82SGRrckRBVHRCOEJxaHA0V1gwblBRTng3R1FlWEp6VzBEUm5xVHdpOFR4WHFxNmxhSklRZGJ1OWpHKy9FN3ZCV0JTd2dkTHBRTXg0UHlBUnMvL0NaNzN0R2xIaFUrY3gvT0w5UWdDTHhMMnBjVHlLQkx1ZkRyeHg1ajZCL2k2L1UvTWcwc2M2NjBDZElVU3BLb2ZqaGVUT2RmZTE0bmdIR1dYdHpCbnRaa2ZMbG5ITFpUem1DekVHZDB0aXkrT2hnOGl3Sk5PZk91T0xXV2o0RTJkS3VyVVIxcHhUd0F3bDFIc2pXVEJienNIbUVDRjBObUxzTzB2dUc0SXhsZ2NXTDYwMWhvYm03ZGZvdDhid3VSeUFRcHI3eWppZVkiLCJtYWMiOiJlZjUyMzU5ZjE1MzE3ZmE0ZTU5NWIzOTg3OWYyODAxZDExNzdkYjIwNjdmMDBlOWMwZTIwNzFmYmFhOTBhZWM1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijc4aThpekZpUDJNbFlmUkNNaHdRRGc9PSIsInZhbHVlIjoiblFQUG1zcU9UU0JPZ2paTWM4ZW85VFI0WVNsTVFtU2l3ZTFlYnFHcHVaaHBvcHdTTmpMUm1FK0VzK3lkcW1RTmdVSzZIU1dOWGFYWmN0b3JjT2pHQVR4VUVOa25Hc3VSdlM5c3F5bHl6bzBBc2JIajZIV3pDakFVbU9zTlk2L3lNNVd4K29LK2FYbStlSTVvdDYwVmxDNFo0blZTa0xZL2tKU1grRDEzeXQrT1FQVGhMTW9aZUF3dDRsWEhDNDc3TGoyZk13OEMwTFBNT0huRGp2YXB4Y0VWMkp0VXpMSXQ2MmxoSHIrNDYxVnpmWWMrOENyYVJ5L2loSmVJSFJ1b0hoV1JjYmxlVUFXYlRqdmZSSkxKaS84TEhqVFBqUTVFWDMxaWJOREl3c0VqamZ3UkFsaVBFNE5Ody9QdWFMZmxsNVVveVdybnkra1diUmxoTWZrd1NEZzVnZWtDYjg2SUtQaVhvUVgzYWxDOTJkTlhQS2IyR2lWcWp2RGJhQTZCaE8xS3k3UEtYeHdaY2pBQi8xckhhVytjeXBJTjFWTDMxU290NzQ5bzBSdGlWSnk0bDdVVUQ4T2pZa3VXREtXK01QV05GK25SS3RqUm1LQk1GVE5UQzMydTQwUDNnUHZjcjRBU2JZR1lHb1JhWkJ1czJKbFhYdGNod0htNFFDcFYiLCJtYWMiOiI1Y2MzNDg2Njg2MTJiYWYzNjlkZDQ1MzgxMDJmNmQxYTEyYzMxMjY5MWRjYjdmYWMxYTk2MWYzMjQ1YWRkZmY4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:14:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965025528\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-666365762 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666365762\", {\"maxDepth\":0})</script>\n"}}