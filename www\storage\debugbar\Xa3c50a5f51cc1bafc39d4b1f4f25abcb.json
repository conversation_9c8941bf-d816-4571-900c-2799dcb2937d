{"__meta": {"id": "Xa3c50a5f51cc1bafc39d4b1f4f25abcb", "datetime": "2025-06-06 19:39:01", "utime": **********.370732, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238739.942487, "end": **********.370786, "duration": 1.4282989501953125, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1749238739.942487, "relative_start": 0, "end": **********.09186, "relative_end": **********.09186, "duration": 1.1493730545043945, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.091881, "relative_start": 1.1493940353393555, "end": **********.37079, "relative_end": 4.0531158447265625e-06, "duration": 0.27890896797180176, "duration_str": "279ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45694744, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.255344, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.27639, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.343674, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.35571, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.041679999999999995, "accumulated_duration_str": "41.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1788578, "duration": 0.0215, "duration_str": "21.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 51.583}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.208766, "duration": 0.0107, "duration_str": "10.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 51.583, "width_percent": 25.672}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.22765, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 77.255, "width_percent": 2.183}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.257567, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 79.439, "width_percent": 3.047}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2785232, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 82.486, "width_percent": 3.623}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.307919, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 86.108, "width_percent": 4.175}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.320184, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 90.283, "width_percent": 3.047}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.328519, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 93.33, "width_percent": 2.927}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.347369, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 96.257, "width_percent": 3.743}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-759271281 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-759271281\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-826617862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826617862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1838498684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1838498684\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1326423548 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=1jor13%7C2%7Cfwj%7C0%7C1983; XSRF-TOKEN=eyJpdiI6IjhDd3VnVVdzSERvTitLOU9NNENIdnc9PSIsInZhbHVlIjoibmZ6cC9HTWlRbGc2aWlMdFBnVyt2RmJMODlsSEdhYURleXVtMC85MWxDOW51Tm1ld0NQVzh6Z1MrV2ZhMUtlTUtrTDNZc01vbHlaWDNwNDZIVHgySWp1MWhudDIreGxDMDNsL3dHR3pMUS9xdDlFY1pyQS9GblFUVjBzTHZqSklRZUNzYU1vd3FUaEU5Yzk5Qnp2dzc2SHFtQzJlTUxwcjZ3UmN2SjRnenFvby9TWGo5M2dsd3F6QW5wdkI2d2o2cVVMR3d3WlFtVWZsMEpVb29SR2JUdWJKR3RRL0pmZnd6MXJ4eEJPMm01YmVTbDU4NnRxZEo4NmJOUDNxTFhidVNOMHBnWkJvMXVYa2NPZWkzcmRiVElpYXc0cTZpUzJtL3lSb0N5TWdXZE8veWNIcUhySENXRmVtdkRvSmJMVnpBTG42RzA3dE1QV3FGYkNyTXA0MDNSdXo2Znp0dGpiY0NHb3h5MUZoUGx4V3N0UkhZZWFzTmJtSngzOTloYWFtUnFhQ3JaeDlNWW9MNUttL1pVOW1ZVlNQNmVnc1ZXVENDQmttbjlJV0Vqa1RCSk5YYzlxT281R0JKR2p5ZGNpSW5DNkc0cFcrM1l5QUJ6b2FZdWZPL25hdHdpYXZFVno3a2o0SXBnVC9sY3h3UmRWdDNWcC9FQnpvQnNzdmtKbGwiLCJtYWMiOiJkZDRjNjBhZTFmMmUwMWMzOWI3MjI3NTFjYjY4YjdjOWIxNmVhYjg4M2M4NWYxYWYyZDI4YzdhMjUxNWUyYWFhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imt2b3FzTW1BSW4rMTVZWDdqbVJ5MlE9PSIsInZhbHVlIjoiOENkSjlRVUVadVM3L1VhMDdkcFRweW1tencwR3VIQWt0RUF6Z2F6WHp2NWl2QlllK05GM21KQkUwNzZJTGo0Q1lkRWpCRXRma2g3VjBGbUtaK3hpMWdFV080eEIxd29UWWJ4YW1QcnVvTWQwUjdoZWFNMXUwaVZlU0ozWVJtVytsR2g0eG1Vbzg4UW1JODFCK3I4Qmg0NXhWcTBLbDBaUEJQR0JOWkt1Q0ZNQzhRQVJ2aFlKUFM0RkZJZEZPeW1PSDBjMklpY1I1QmR0c2VheTRhNnNJRU1TNXJPWE1vaGtSZWlISU5nMU1MRzJiWVRFdTJkWWNmNzJiSzA5aEQxbnFlVUFkNFVpYkQyaTFudUplZDJOcEpuRis2a1A5ZDNhRlhwQXFaTXdXNTZ1Nlc2ZFYwcTNNM3NmbEFsbXZKN1NGcUpTbU16TmtLbWNFa2E4RkNraFByeHkvcFdUdjViQW54aW5iczIwdnFLcGREdDhMN2dFUjRGUG9xTmNqbnVGVWtvZUVINzBOV3k2Ny9VM2lhZ1JDbmVDUnJjR0xzK3o3Wk1lRHhOeXc4UWM2Z1U2dkVhZ242bm5zbmVCemVRYUREZlljMld6YkpoTkhGZnFHRmZJUHlKclpDVnpwanZqMHdEMVNON3h2cksrQ1BLa2NkVnN6cmVnVDRwNGtGQVkiLCJtYWMiOiI0NGU0NTk1ODliOTQ3Y2E5MzNkNjkxODZlNDJhZjU1MTI5MDdhOWE0NzI1ZTY2NjQ5MDMzNDlhZTFhNWNmOTIwIiwidGFnIjoiIn0%3D; _clsk=151odr7%7C1749238731352%7C7%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326423548\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-176297072 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176297072\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1987954046 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:39:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFXOVVlZGYzRUNOTENYYWI3VXZueHc9PSIsInZhbHVlIjoiWjZGNERqRlRGSHhyczZVcjkyT0FqcWhqNXBwWmJEYlF1dnpSRXlxbmNuSy83SlB1QWdieXQ3ajU2QUswNFhtdlMwK2VraUJqWXdxSHUzRHUzMkVoc0k0N0xzTThhRldWWEt3MUtXSHdneHJLNHFUZStUUTJ6Q3I4cXQ3V2pVLyszMndyV2d0YlZiZUJ5dTBWTjNFcjB5WUk3NHVMWWtWNnlxN3ZDTVBFcnZ3SHdzaytlY0c1RTZ4R2dKR0IvaTZFS0w5ZThSbDZ4dWpZcGhOa0pMeGJQZnZaZmJHcFNTaUxXdlAzY2hVNGFBbHJzUm1xbGkwRmY1VlpFWDRmS0hMRlR3eU5oT05GblU5b3hJbE1aVm5Od01nbXlBRGs3QmRDUXBhVWxHV3VkUldxU1V6TkpLTFJpQy9OOCtYc0JGOHRPSEV0elM1UTlUVXZJb2htOEppMFZFNS9sMDl6dFlacTdaYU41bzRhbkJUWXhDWW15OGpORGdRY0M4b2htM0pWZEhsRExHbUhOU1JWSlBzV1M4eTFacnZiNFJIdSsxM1UzMGJIM1JLZGVsMnFvdG05YlA5bHF0OTUyQldyUlFZWWRxYjNzbHlaREZCeSs5b2JsTzFldEE3dER3ZmNCZDZYcDUrR0xhb1JqbGxtaTY4dnM3QURBcTUrZWFyZXZHYUkiLCJtYWMiOiJiMDg4MGYxYzc3YjZiZjQ0N2FkYTliMGFkMTg3N2RiZDQxY2RlZWNkNTBhODgyYjM2YTE2OWI0ZDEyZmU5MzAwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklUOW9lYmRDOTF3Z2l0T3dGb0I4aXc9PSIsInZhbHVlIjoiZjhxdmhpV1UyQmVDMEp1dE55TVlVSTd4cTl1L21XSDF6UWtLeW5yTTFkcE5VeGpnRkJ2WlFtZHBSY3lwdFFuRFN0UUxPb25mVWc5Rlh4S1ZCRktra3pTdzJyNDZYRDNhVXp3RUJhV2d0YTdibVUydENYdmhuZit2eW0rb2VJUmE0aUhKVHZ5MEdNYlVpck95RDhHRitVSkNYdXJzaXZwWUd4RWg3NUlLUCtJS25jOHhScEx2Mzg5cnVBR3FlZ1Q1NGpWSlJ1TlFWYWVJYlJvR21UWml5YVdwbDRTTE15RFBnZ0hOUEwxWFBERU52T0pRdWJGQ0dBcUlPYUU1SUNJbWYrL0tNZnVkc1l3MDBzMWxLM1BCTVg0TXNTS3BBQkJ5OXRna3ZQazkzR1g2T0txaCtLMUVHeXNJbGhuWFV5eWNNV09TSDZzNDlsVm9ISUZMY2ovY3ZsWWdncHAxYjlaSzE4WGNGa2w1MmRrWlFGeWdveGpuRHF1dUo1RmcyOEhsWVRGbk5aUWNnMEZITGorRW14d2YxY0tSRlZNZUg4NndxUTdNQ09FMkxjSW9BdUlSeFVwa0UzRk02bVFoNDNFWUcySkdDdk4yd0FPVnJhTDRhMCsrZVk2bmdRL3JqWFpHaDJYUjFSRHkvT0kvU2dpUnRsRU05T2RTYXBaNjNyYzEiLCJtYWMiOiJiZDFiMmJiMTMyYjEwMjdmYThjYTZlY2VkM2RlYTdmMmY2NWEyYjAxZGI3YWJmMzFiYzJmMjM4OWY5OTQyMTU1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFXOVVlZGYzRUNOTENYYWI3VXZueHc9PSIsInZhbHVlIjoiWjZGNERqRlRGSHhyczZVcjkyT0FqcWhqNXBwWmJEYlF1dnpSRXlxbmNuSy83SlB1QWdieXQ3ajU2QUswNFhtdlMwK2VraUJqWXdxSHUzRHUzMkVoc0k0N0xzTThhRldWWEt3MUtXSHdneHJLNHFUZStUUTJ6Q3I4cXQ3V2pVLyszMndyV2d0YlZiZUJ5dTBWTjNFcjB5WUk3NHVMWWtWNnlxN3ZDTVBFcnZ3SHdzaytlY0c1RTZ4R2dKR0IvaTZFS0w5ZThSbDZ4dWpZcGhOa0pMeGJQZnZaZmJHcFNTaUxXdlAzY2hVNGFBbHJzUm1xbGkwRmY1VlpFWDRmS0hMRlR3eU5oT05GblU5b3hJbE1aVm5Od01nbXlBRGs3QmRDUXBhVWxHV3VkUldxU1V6TkpLTFJpQy9OOCtYc0JGOHRPSEV0elM1UTlUVXZJb2htOEppMFZFNS9sMDl6dFlacTdaYU41bzRhbkJUWXhDWW15OGpORGdRY0M4b2htM0pWZEhsRExHbUhOU1JWSlBzV1M4eTFacnZiNFJIdSsxM1UzMGJIM1JLZGVsMnFvdG05YlA5bHF0OTUyQldyUlFZWWRxYjNzbHlaREZCeSs5b2JsTzFldEE3dER3ZmNCZDZYcDUrR0xhb1JqbGxtaTY4dnM3QURBcTUrZWFyZXZHYUkiLCJtYWMiOiJiMDg4MGYxYzc3YjZiZjQ0N2FkYTliMGFkMTg3N2RiZDQxY2RlZWNkNTBhODgyYjM2YTE2OWI0ZDEyZmU5MzAwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklUOW9lYmRDOTF3Z2l0T3dGb0I4aXc9PSIsInZhbHVlIjoiZjhxdmhpV1UyQmVDMEp1dE55TVlVSTd4cTl1L21XSDF6UWtLeW5yTTFkcE5VeGpnRkJ2WlFtZHBSY3lwdFFuRFN0UUxPb25mVWc5Rlh4S1ZCRktra3pTdzJyNDZYRDNhVXp3RUJhV2d0YTdibVUydENYdmhuZit2eW0rb2VJUmE0aUhKVHZ5MEdNYlVpck95RDhHRitVSkNYdXJzaXZwWUd4RWg3NUlLUCtJS25jOHhScEx2Mzg5cnVBR3FlZ1Q1NGpWSlJ1TlFWYWVJYlJvR21UWml5YVdwbDRTTE15RFBnZ0hOUEwxWFBERU52T0pRdWJGQ0dBcUlPYUU1SUNJbWYrL0tNZnVkc1l3MDBzMWxLM1BCTVg0TXNTS3BBQkJ5OXRna3ZQazkzR1g2T0txaCtLMUVHeXNJbGhuWFV5eWNNV09TSDZzNDlsVm9ISUZMY2ovY3ZsWWdncHAxYjlaSzE4WGNGa2w1MmRrWlFGeWdveGpuRHF1dUo1RmcyOEhsWVRGbk5aUWNnMEZITGorRW14d2YxY0tSRlZNZUg4NndxUTdNQ09FMkxjSW9BdUlSeFVwa0UzRk02bVFoNDNFWUcySkdDdk4yd0FPVnJhTDRhMCsrZVk2bmdRL3JqWFpHaDJYUjFSRHkvT0kvU2dpUnRsRU05T2RTYXBaNjNyYzEiLCJtYWMiOiJiZDFiMmJiMTMyYjEwMjdmYThjYTZlY2VkM2RlYTdmMmY2NWEyYjAxZGI3YWJmMzFiYzJmMjM4OWY5OTQyMTU1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987954046\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2020818689 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020818689\", {\"maxDepth\":0})</script>\n"}}