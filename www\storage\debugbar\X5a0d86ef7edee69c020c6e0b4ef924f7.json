{"__meta": {"id": "X5a0d86ef7edee69c020c6e0b4ef924f7", "datetime": "2025-06-06 20:41:03", "utime": **********.385218, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242461.94283, "end": **********.385257, "duration": 1.4424269199371338, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749242461.94283, "relative_start": 0, "end": **********.236374, "relative_end": **********.236374, "duration": 1.293543815612793, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.2364, "relative_start": 1.293569803237915, "end": **********.385262, "relative_end": 5.0067901611328125e-06, "duration": 0.14886212348937988, "duration_str": "149ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43100736, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00562, "accumulated_duration_str": "5.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.344237, "duration": 0.00562, "duration_str": "5.62ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "urdX1KlEV93dFessht7rRiSdbCNw6oZ0HPKGiTP4", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1149823842 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1149823842\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1287626796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287626796\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1154324437 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154324437\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1602452498 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1602452498\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-309761042 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:41:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkplQWVQcEQwUHlHSmY5QXhtbVFFZUE9PSIsInZhbHVlIjoiZzFWSEFwM25lRTZ1eTF3dlk4Z2JhU1lGUDZSd3hsSVVDcm5wbGlISURQMEkrb2gvN1d2WjBzeExOR1hWNiszejN6KzBIYkV3VjYzcTZaM0EzS0N5V3RoRHE2bVJDaEpWeURzdG1vZHQ0b1BWQTR0MnliUmxBV09ObGNQOWU0UDYrcXhodUMvT2FXME5FSVgwVTY4WkhTNDVrWG91QkxWQzFnNEhDTmpwNklwQ1BtZTJscGdSK3lwTktXdVE1RnhIS0RjdTBxWWxFVVc1b3Vha0Z2eXJJeUhjcUk0bi9iam54WHRQeDc0Yk9TVllEMTBsMHpIc2dvZHJnR0pIL2ZqZEVGbGY5LzJmNExPQ3piYkZJNXpSZ2RDeG50dnI4TGd3TjhHcnNqYUVtWm16WFR3MUJHcjBNOWNtZnYwRFhRaytOUzkxOEQ2Q08vY0ZDUkN1K2VqNTUxVFFRRFdXbUthdE1tVVlGUEYveE1WSUl2NXFMUjRYWkZaV2FqSWtnaG1LanlpQlY0cUtRMVYyVUFrMWlXN3ZDRHEzRHhLZTNCQzRwcUZwdEpQckc4VExoS2RteVBkUDlNZHRTaGl3Z2lSK3hSUldBamFBd2p4MkhMWnhiMHdUa0drN0hWYWI4blZqSEVFcEVBbGhERXdZY2NFSFJyUndocWlvY3pHMTRmZUwiLCJtYWMiOiJjMzI3MDdjMTgxOTk3OGIzM2M4NDNkNzkzZTQ2OTVlNTM5MTNjYjg0ODRiODM4MzNkYmNlMTkwNDI2ZWExMGQzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklnWHlURzJpVjdDSHN3aXRMc0hXR1E9PSIsInZhbHVlIjoibUJpaVBPMmdqSVNDTVVjZEdQYmNjOVpmZzR4dStoYko0b215YlFlRnoxN293ejBwMlJFMVdUUXNjeFFqMWh1OFFta3djQkJpYmZOQ2lKT0tYVGFSam4xRjJtYWJFbklpZjVMSzd1UjFkY01yZzI0dENTL2twd3MxQWFTbUNBV1QvUzcxbFpCRUd6RW1KcjJpSFgwcEVyUDNWSldHM296UWZsaHUrOUVVUEdONElEVjNBS2ZIcTlUVUllS2VaSnJDRWlnRElQa0VBUExBOC9pTWhLZjNmWHJ6aUw2ZW84SnZWaE9sRUU5aVFMNEF6bk56RXRZY05NWURqQTQ1UnV0TG5Ma1YxMlJNa0lCRDRWamZqbkFoQjlyeXZRQ3phVmplcSsrb1JkR2o2clRrazE5Ynh1WVFiQTQxcHI4RHVRRkpHeVFoNlVIQzhzdjVURkpEb1hLSDRCK2FNSkc0cnhBMjZRalJTTElNeTUzMGRWcUdaY0NLMVp4aWgzYzZ3T2xRTjZpWlZSU3YydGlOTWhNVkdPbUZQdzd6Tmk4RWYzSkkvVGdESnRVQ1prazhoRXFON0VkSTdIakZoVHN5K0dFTWNMUHFQNWRCUVl6UWpjNXk2SitKdHp3aDZkOXptNnhVNUp1ZzNOZUZaZGkrU3NlcTY5UjJUZ0RRQXNPVmtpVkwiLCJtYWMiOiJiYzNiMDNlNTk4NzQ3MzBlYmZlYzdjODFjMjgwMmY3ZGJlN2I1MTE5MjBjMjhiOTU5NGFmN2Q5ZjBiZTMzZTVmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkplQWVQcEQwUHlHSmY5QXhtbVFFZUE9PSIsInZhbHVlIjoiZzFWSEFwM25lRTZ1eTF3dlk4Z2JhU1lGUDZSd3hsSVVDcm5wbGlISURQMEkrb2gvN1d2WjBzeExOR1hWNiszejN6KzBIYkV3VjYzcTZaM0EzS0N5V3RoRHE2bVJDaEpWeURzdG1vZHQ0b1BWQTR0MnliUmxBV09ObGNQOWU0UDYrcXhodUMvT2FXME5FSVgwVTY4WkhTNDVrWG91QkxWQzFnNEhDTmpwNklwQ1BtZTJscGdSK3lwTktXdVE1RnhIS0RjdTBxWWxFVVc1b3Vha0Z2eXJJeUhjcUk0bi9iam54WHRQeDc0Yk9TVllEMTBsMHpIc2dvZHJnR0pIL2ZqZEVGbGY5LzJmNExPQ3piYkZJNXpSZ2RDeG50dnI4TGd3TjhHcnNqYUVtWm16WFR3MUJHcjBNOWNtZnYwRFhRaytOUzkxOEQ2Q08vY0ZDUkN1K2VqNTUxVFFRRFdXbUthdE1tVVlGUEYveE1WSUl2NXFMUjRYWkZaV2FqSWtnaG1LanlpQlY0cUtRMVYyVUFrMWlXN3ZDRHEzRHhLZTNCQzRwcUZwdEpQckc4VExoS2RteVBkUDlNZHRTaGl3Z2lSK3hSUldBamFBd2p4MkhMWnhiMHdUa0drN0hWYWI4blZqSEVFcEVBbGhERXdZY2NFSFJyUndocWlvY3pHMTRmZUwiLCJtYWMiOiJjMzI3MDdjMTgxOTk3OGIzM2M4NDNkNzkzZTQ2OTVlNTM5MTNjYjg0ODRiODM4MzNkYmNlMTkwNDI2ZWExMGQzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklnWHlURzJpVjdDSHN3aXRMc0hXR1E9PSIsInZhbHVlIjoibUJpaVBPMmdqSVNDTVVjZEdQYmNjOVpmZzR4dStoYko0b215YlFlRnoxN293ejBwMlJFMVdUUXNjeFFqMWh1OFFta3djQkJpYmZOQ2lKT0tYVGFSam4xRjJtYWJFbklpZjVMSzd1UjFkY01yZzI0dENTL2twd3MxQWFTbUNBV1QvUzcxbFpCRUd6RW1KcjJpSFgwcEVyUDNWSldHM296UWZsaHUrOUVVUEdONElEVjNBS2ZIcTlUVUllS2VaSnJDRWlnRElQa0VBUExBOC9pTWhLZjNmWHJ6aUw2ZW84SnZWaE9sRUU5aVFMNEF6bk56RXRZY05NWURqQTQ1UnV0TG5Ma1YxMlJNa0lCRDRWamZqbkFoQjlyeXZRQ3phVmplcSsrb1JkR2o2clRrazE5Ynh1WVFiQTQxcHI4RHVRRkpHeVFoNlVIQzhzdjVURkpEb1hLSDRCK2FNSkc0cnhBMjZRalJTTElNeTUzMGRWcUdaY0NLMVp4aWgzYzZ3T2xRTjZpWlZSU3YydGlOTWhNVkdPbUZQdzd6Tmk4RWYzSkkvVGdESnRVQ1prazhoRXFON0VkSTdIakZoVHN5K0dFTWNMUHFQNWRCUVl6UWpjNXk2SitKdHp3aDZkOXptNnhVNUp1ZzNOZUZaZGkrU3NlcTY5UjJUZ0RRQXNPVmtpVkwiLCJtYWMiOiJiYzNiMDNlNTk4NzQ3MzBlYmZlYzdjODFjMjgwMmY3ZGJlN2I1MTE5MjBjMjhiOTU5NGFmN2Q5ZjBiZTMzZTVmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-309761042\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-489957823 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">urdX1KlEV93dFessht7rRiSdbCNw6oZ0HPKGiTP4</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-489957823\", {\"maxDepth\":0})</script>\n"}}