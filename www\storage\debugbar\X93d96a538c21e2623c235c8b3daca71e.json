{"__meta": {"id": "X93d96a538c21e2623c235c8b3daca71e", "datetime": "2025-06-07 04:32:58", "utime": **********.746584, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.322659, "end": **********.746615, "duration": 1.****************, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": **********.322659, "relative_start": 0, "end": **********.553106, "relative_end": **********.553106, "duration": 1.****************, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.553126, "relative_start": 1.****************, "end": **********.746618, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "193ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01945, "accumulated_duration_str": "19.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.641, "duration": 0.01656, "duration_str": "16.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.141}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.687083, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.141, "width_percent": 5.81}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.721916, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.951, "width_percent": 9.049}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270763756%7C11%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9RbTErKzFQVmQwS0wzS2dKOE0vUnc9PSIsInZhbHVlIjoiM0Zxam1RYy9tZ0ZUN1IyL0JYVng4UEZwOEhUYXUxUWtzdGRKaklKZWVJaE9HNmM1VnVuMDF6MG0xMkpQSGsxckR3bVRNK3VrcSt6aTlqU3FEeW1XTjd3ZExWSzFSb2N1bWgzVjMraWtwU0ZoQlB6SXZrbEhkaGdldVVmMVZRcnZ4akhrV3c1Wk43aUxraXRYcE42Wm5LWDBRNXkxN0tvWlFTVjJ3WDJ6QmhWZXoyK0tYVVhNQnV3WUxHa1U2SkVoZ2oxMWF3NWFFLzRRSEw1SHJiM2pUSGwzenc1aEhwU0U0UVE0aVRRM3FpYUZ3NzlGanZjMittK0NQTUlINXRBTW1YQjdWNEx6QVdrNWgvNDJXSGsyQkVrT0NkYlhkdkVteFZKYjZxdHlqMEswUndpVTZtbjNLNHFNbmlacUVpNVl4M0xsS0JZRkZKWHVLS1IrZ2FSZzYvdjhRV01ncXpYZ0QzRlk0RXN5YW50c1htVmtYZWpYcjd2RVVtTEluclNLRDd4V09ldFBSVjAyTFlGRHA5SVVxeVJkMWw5NldCUXIwL2oxTWVkOXk5Vi9pcU9oaW1leThqRmw5WDNIM2crVXpjR2NlTXBicEJzZ3BaSVFuc1dvM1BBTGFPUjRWVTdWK1M3bm5kS3M0VndRK3BhME10dUc3ejlBZEFjTmpmdmEiLCJtYWMiOiI5Mzc0Njg1YWNmYTczODI4ODI4YTI3N2RiZmJlYTg3ODBlMGU5ZjZkMDU1OTAzNzk3NmU0Nzk5ZTQ5NjM5M2MzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJId0poVithaVhtMDhmYllkclVCWGc9PSIsInZhbHVlIjoiVUtEbHBFeTB3eC9ZWlB6UDYyVzV0cTJIZjFJZjU5Wk4zTFZTSEtoQUQ4RlBtWC9TU0pIcW1ZdHVwSDQxNFBPakg5Ty9VdmdDRGVUQlIvSzIyT0FpQ1NZK3pnYXE4UEN1UVJ5MU5tV1dMdUYzcE1BOUh3dmpybVFKeEpCSXlDd3ZTOGZwMnpFRE9rQlNYVkNraHNtMTdyc09YQW10ZXR1NlBlOU40YVVCU05sTGpYVlUyWjI0alJCQitOV0JSQlkySjFheTR6TGNaY3NKUS9kWGRGK3FwSlBLeGhFcWlIemgrQ25EaG0yREZScmpJQVliTFRVc3E5MzNvOUZaMjFXMktpekg5dS92RG5zdERYcE9zWXdTc0dqUHdjMi9hKzI2SHNWWFhrZCtpUzNyVU5Ba05uSkxQZXJUaDhHaVcrVTdQK1ZDa1BuRCtudFJtTnN4aW9OSkx6aittTjVzR0JsQ2NSWUV2SVUyRjFvYm1sS1VCSGRydGNJQ3J5L2JzSWFDaXhuajhYdkk3SXRwMkduSTE2N0p3SWx2M284amNQQWNOd3JMc1J5d2NIM0h1V0xSUHdyWXRWVDB1dVVwTWZTaXRDVkk3UzJ2YjU5UDJnc05vbTMvTkkrR2hibXlkTG0vMW8zejBHRmpjQ0ZkZ2dzZzFqK1JIeUt5N1pIcmlvN1YiLCJtYWMiOiI2YzkzOTg1NTUyNDZjMDU0ZjQ5MWQ2ZjdhM2FiNjE3ZTI2OWEwNTNkOWUyYmQ5ZTQzZTRiYWE4OTQ2ZGIxMjRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1187177424 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bsxxeW3Vxtj1F448bETxKZY3vCBI5tQzu5FzKxQU</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4BV3LLZZXyEMe10MCK9nf8wqbVP7f9AxZbbeIoE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187177424\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1431424831 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:32:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRwek9HWWFSaVVBQVQzcTRYa1FnSXc9PSIsInZhbHVlIjoidFNTQmREWmV0L1EzS3dDbzVJSlZEYmNuVzE1c3l1RzcvZHZ6NUVEdzk2dFc3dDVuV0xrL200OTVKUEVQd3Foa0diR0JFMzJLNXZCQ1BLR3RtbnRieUVMRWJQalJybktsN21MQ0d3eVBvVHA1S3NNQ01wOStZSytHemVEN1grQ0p6YUN3a3FBUUlMTkxka2gya0djTVVZNGU4VU9RWmh5Nm9GdUcydVdYVzJTSGRBdFhqWDM5UUxIamM5OUZpNUlMSUdtSE9zdVY0OUMyTW1ZSXBQM2VJbjlZR25POTNZbVZNYTMrVFNFMVVMakk5eHhqS0xRU3hBY21JdGlFNVNmbDhVUEYwNTBTOFdvbnEvaXRuLy9ZOXV5T1pnRnpGemx2aEFoMmE4eUJVQ0drdmY0dWIyc3IxNm96SVZqaU1HY2FTNndFNUFrOTMzWjlnT3hZYXptY1UwTEdrZmd5WUcwZWE4d1ZTZHg1dmNDTmd4QlI0NWJiZkxNWWRSTDVsNGtaYjlLekdJMFBhWlV6YXc0ekFCajhkMERLdysxZk42ZVI1dnBXWVZiVWtvaUUrL3ZBNjBFaGdRUkdrY0RDdmxBS2VsdjJKWXZiNjRGYXlMTmVGSnNwNFh4U2R5Z3VyczhzSmk5YjNhb0E3WlFXcXNnVThseUd1bHNPSm44Tk1saFgiLCJtYWMiOiI5NzJiZTEyZWFmZjNlNGZlMzE3MWIwNzcyMDZmN2Q1YmZjOTY3M2ZlNDM2ZTJlMDA1ZTZkZjVhYWNhNzQzNGM5IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjQ4M3dsb1JpdFJYN01EeUtpMVh2S3c9PSIsInZhbHVlIjoiK3VTcWtzTnBmenhiVmE4R0E1SnQvVzkwMmEzeFhsZkZ2MW9JeVRpdVJBTll5QW5KV2x0eUlmTlFPRkZvTkZlRVR5SCtuVGxnYndsVU1vMU05QTcrZXhvRXp5bER5ZUVlK0ltOE95WjlXcThCcjVZdnoyMlh1YW5HOXVZZmRFVGkzNElYaFQyM2VUcURvY3hZY1N3Vm4rR0Zqdyt3RUlTSThCVmNVLzJpaEhxQTRiRXlvWmZYQjVIdTNyaWN1Ly9sRW9tMkNVME9jQzV5Q2hzMEQ3NWdGaFgrVmpzb0dqSkNaUUdRbk9NZjVjWlJ4TE1OdCtaOWo5N0JQcDFqUys5bkQ0UjBhYytCMmxTejNHZjlXbkFGRVlIcmVNbjhSY0dkTEVBcHVFUDI2NW5RcjlaRjNweVhBSWlTVU10TVlFcjgvSEwzSFFHMVZ2Tytoc2VMMXcyczd1NlZWQlAvNVdEWUgwRlAybFVVUHdma0F6N2ZaNytodG5zK0Zsa3NHZzczSTg5RDVwT2RaRU9oTWlpZUFLUTdMaExqK0Y4Vyt6eXFsemRGOTBBYmtqYjdVdTJGRitxc052UXJuU1FseFdSM3o1eDBVcDNaUzBoalZGVVhyaU1KQzhuUDJWelZnY0RZMlpLcUhFOUkzYXg3YXNJQk9FOXpUdWNmYlpiVXhFeGQiLCJtYWMiOiI4OWQ1ODM1MGQ0ODdlZDg2MDgxYWNiNDc0NTYwODIyZTBlYzMxYTJmZDI3OGM3MDJhZGQ0OGQ1M2E4YzFkNDU1IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:32:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRwek9HWWFSaVVBQVQzcTRYa1FnSXc9PSIsInZhbHVlIjoidFNTQmREWmV0L1EzS3dDbzVJSlZEYmNuVzE1c3l1RzcvZHZ6NUVEdzk2dFc3dDVuV0xrL200OTVKUEVQd3Foa0diR0JFMzJLNXZCQ1BLR3RtbnRieUVMRWJQalJybktsN21MQ0d3eVBvVHA1S3NNQ01wOStZSytHemVEN1grQ0p6YUN3a3FBUUlMTkxka2gya0djTVVZNGU4VU9RWmh5Nm9GdUcydVdYVzJTSGRBdFhqWDM5UUxIamM5OUZpNUlMSUdtSE9zdVY0OUMyTW1ZSXBQM2VJbjlZR25POTNZbVZNYTMrVFNFMVVMakk5eHhqS0xRU3hBY21JdGlFNVNmbDhVUEYwNTBTOFdvbnEvaXRuLy9ZOXV5T1pnRnpGemx2aEFoMmE4eUJVQ0drdmY0dWIyc3IxNm96SVZqaU1HY2FTNndFNUFrOTMzWjlnT3hZYXptY1UwTEdrZmd5WUcwZWE4d1ZTZHg1dmNDTmd4QlI0NWJiZkxNWWRSTDVsNGtaYjlLekdJMFBhWlV6YXc0ekFCajhkMERLdysxZk42ZVI1dnBXWVZiVWtvaUUrL3ZBNjBFaGdRUkdrY0RDdmxBS2VsdjJKWXZiNjRGYXlMTmVGSnNwNFh4U2R5Z3VyczhzSmk5YjNhb0E3WlFXcXNnVThseUd1bHNPSm44Tk1saFgiLCJtYWMiOiI5NzJiZTEyZWFmZjNlNGZlMzE3MWIwNzcyMDZmN2Q1YmZjOTY3M2ZlNDM2ZTJlMDA1ZTZkZjVhYWNhNzQzNGM5IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjQ4M3dsb1JpdFJYN01EeUtpMVh2S3c9PSIsInZhbHVlIjoiK3VTcWtzTnBmenhiVmE4R0E1SnQvVzkwMmEzeFhsZkZ2MW9JeVRpdVJBTll5QW5KV2x0eUlmTlFPRkZvTkZlRVR5SCtuVGxnYndsVU1vMU05QTcrZXhvRXp5bER5ZUVlK0ltOE95WjlXcThCcjVZdnoyMlh1YW5HOXVZZmRFVGkzNElYaFQyM2VUcURvY3hZY1N3Vm4rR0Zqdyt3RUlTSThCVmNVLzJpaEhxQTRiRXlvWmZYQjVIdTNyaWN1Ly9sRW9tMkNVME9jQzV5Q2hzMEQ3NWdGaFgrVmpzb0dqSkNaUUdRbk9NZjVjWlJ4TE1OdCtaOWo5N0JQcDFqUys5bkQ0UjBhYytCMmxTejNHZjlXbkFGRVlIcmVNbjhSY0dkTEVBcHVFUDI2NW5RcjlaRjNweVhBSWlTVU10TVlFcjgvSEwzSFFHMVZ2Tytoc2VMMXcyczd1NlZWQlAvNVdEWUgwRlAybFVVUHdma0F6N2ZaNytodG5zK0Zsa3NHZzczSTg5RDVwT2RaRU9oTWlpZUFLUTdMaExqK0Y4Vyt6eXFsemRGOTBBYmtqYjdVdTJGRitxc052UXJuU1FseFdSM3o1eDBVcDNaUzBoalZGVVhyaU1KQzhuUDJWelZnY0RZMlpLcUhFOUkzYXg3YXNJQk9FOXpUdWNmYlpiVXhFeGQiLCJtYWMiOiI4OWQ1ODM1MGQ0ODdlZDg2MDgxYWNiNDc0NTYwODIyZTBlYzMxYTJmZDI3OGM3MDJhZGQ0OGQ1M2E4YzFkNDU1IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:32:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431424831\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-868237257 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868237257\", {\"maxDepth\":0})</script>\n"}}