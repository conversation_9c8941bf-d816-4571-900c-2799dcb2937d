{"__meta": {"id": "X88a3ff264141001830299a630eea2e16", "datetime": "2025-06-06 19:31:14", "utime": **********.844146, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238273.35443, "end": **********.844186, "duration": 1.4897561073303223, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1749238273.35443, "relative_start": 0, "end": **********.643397, "relative_end": **********.643397, "duration": 1.2889671325683594, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.64342, "relative_start": 1.2889900207519531, "end": **********.844191, "relative_end": 5.0067901611328125e-06, "duration": 0.20077109336853027, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00939, "accumulated_duration_str": "9.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.751614, "duration": 0.00597, "duration_str": "5.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.578}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7851262, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.578, "width_percent": 10.33}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7926722, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 73.908, "width_percent": 12.567}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.81422, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.475, "width_percent": 13.525}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1892374022 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1892374022\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1907053014 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1907053014\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1008596740 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008596740\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1871880494 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238255845%7C40%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1HaHdqeE5uM1pKZCs1TjNzQ21RQVE9PSIsInZhbHVlIjoiNlQ5MzFISi8vbHViTzVEN3N3TEdkMy8wQzZSNmhnWFZ2SkZCZTMycDVrVDZ0dWJTbm9GVFRUR2wvYTREYXhCYjh3aDNDd2I5R29OazhWZGowdS9pWWVqR1pJeDRKZWhsRHhTTWhMNmwzKzcweVhvQjZjdFd6MkpZS3RaRzNhTDgyRXM4OGJqeldZeDc1MVlmZjd1dWd6OUM1Wmt1QWk0VDlTeXNScGhTSGpwejFzTkUrWGRZODBwTXhBdzVrM3VTRnA4WHRUaFRHRm5qc2JiakZmMWdCK21UTjBvSmJGck5nTVRvNjBwcWxiOFFnZVdXZ2dVaTBPVkJnWWxmbnZXUFc1MVZtYkxMUmV5aGdla1lMY1JsZU1IaFFLMWwxcmV4dFlnMFppa0FmME5kcUVhY1VmN2ZBaUY4K1VBVWROY0VjY0E4c2Z1MnU0aWppalE0L0xBalNaREVWRXN5NFhJdys3VHRaWnBBYXN1TXRMOEk4YTAvMGlFeThwcTNPQjdBYUUrTHlpQmczRzhHa3N4YmYrWms4Q0V6b2VnK2ZRSFovYUVxd2lEczVpaVJMZEZqV2JYWXEwZi9TeEN3clpxclR2c1h6Vkx5NVhRTHZzVFZOeXFJUnJhbUd0dHYrcUhReTczUnptTUZJZ2dUckN1THg2NzV2UGlSVWx6VEkybUUiLCJtYWMiOiIyNjgwYWYzNDE3NzNlNjlkYmFjYThiMzlmNjAxNzI5OGE0N2EyYzkzN2UwNjMwYjkyZWI0ZDNmNjJkYWQ2YTY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZUaDFacmRQcVE4QSsrK0tLM1lCTEE9PSIsInZhbHVlIjoiUWlBYUVRUWF1KzdXU1FMZzJRb2haU2V2ZGszcFNxSE82ZXNic2FQTHVyR094YkRTMy9rRkpUYk84bHdHUEtIbTU1QmRNSDVvRk9PazdWVXRYMWw5WGFYVWtjZXlwNzNsZTZrUmh1TFRrMUpJM0U4TFI2eE5HeEh5MEMyeEVVQmFNMjhlOHVVSkE1Ti9tamJvZ1JZWWdxbkhGUDYzUTlWTTdTV2g4K2VhM1R1SjNRWnM0eTFGZ1VsVEJkOW1DM3JnRTdJRWRmRFlrM1N4NzhpV29uWEw2eVVScFJ6Kzl0SGtBN2pwckdYeHpJdVRCN3owUVZLbXJVWWx0K3l2am9sRFhBM1BJMjdILzBXMEduODYvN0M4WnN3UW9wQkRGclNaU2dYMG5RbWtMb3hPUFVOekxUbW9lY1kzWFFVZjNsVUFvREhpTllBZVVXY2hCd2JpcDBpV1hrTlNTeFBoRUh2TEh6T0gyaXVJOFRqT3NuUTFQNDVwZnJqeGdnTWVhS1FOQm1uYVFHZUdMU0pLU0tOVU1BS0p5bWQ5RnRUUVNDOEdqTHJUbVdqYlc5WTNUS3ppSUIvVCt1T2ZCNHRsYy9kZHBtN2JpNXZRcDlaWWNaVmtTTmVDc0t6NWlWT0d6ZUJ4bnJuQmZhaWd5K1ZWMFN2cS9IK2FHMVJ1RFQzM3paOWgiLCJtYWMiOiJlMzY3ZDVjN2U0ZWI1OGM1ZmUxODkwZDQ3NjM5YWM1NDY3YmNkNWM0NWE1N2Y0ZWEyZDgwOGYyMDU1ZDAzMzZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871880494\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1467598657 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467598657\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:31:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9tRzdVN2NMRVN5L0w2SHhtaWNYRlE9PSIsInZhbHVlIjoiRTFWQ00wbTFZWWowWjBmMno2YUowaHZaekpTTGhYZ0tNUTlvVS9zUUUrQlUzeE82dXp1TmFoRHJGcFJHQVBFZENpRHJBVTg3L1NxVFc1ZUp6SWxsNHBKUzdDNmlmVVZJTStSRjZGSFlnSzhhTUk5MEpTaWg5QlNrSmRsWHNUWmNGaC9WeFg3L0crZ0xlWEYyTzlqdDJFSlRGdjZwOUJhQmVSNlZzOUo2eTBYYlM5RWJsM2NpQWIrbjRzcURGdjhMNFVhSnl1YTJQRTN1MU9zZzVUcWZoeENCZ0k0OFhyRjRFdUEyc3hWNVFuL052Y2M3NjlZZnRqL0JKUzVMcURwM1FxNlZxbXN4cms3TkdFcEZTT2hVc1FvQzRzcWJjSTg0TWlrYnZaV0NWbXIwVkF5d21ockw0RkVpRjJwMXpLMEU1S0ZsTVJ6OWdCMTN4RUZTQU1kemFlaWJ5WmQzc2NtNnNGRjlvVHo0TGs2YllTVlp3aGpwYlgvOVpYOUdkV3MzZ21RTXd6MkVrdEd6azhRSkpONlZzNnpQaDhPTjk5TjU5ZDdFZkNYTkNUOW5YbXBDSllZZGtWQnJqR3BVZ1ErZE04T1JUNC8yS2RXbFBZUVFDT2NpenRjbGFuR0Q5djc0djFqeUdlOElvbWtQY3ZTNkNtTFcvU1RxZW84TkhNS3MiLCJtYWMiOiIzOWI2NjkwYjdhOWE1YjUyZTBhNWU2YmM4ZGIwZmFkNjJmMDFiOTY5YWZhMWNiZjM3ZGUwNTk2M2U0ODFkNTkyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:31:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhuZk8wamVOL1ZWOG1TZGpnWGt2VUE9PSIsInZhbHVlIjoiQ2hWUnVtUHNjSm9oVm5QOTNYWHpyUHJLU0N5dkcraE12VjBtU0NuR2RlNVA1NjFpN2VRWElVQjFyNkEvNHg1K3Z5RzJKQ0xXMHF6T1JQZmp2KzFwVFFTZGJqV3EyN3Z2SFk1MFpIeUJyR0pDME5QSXIyYmRNZ0JyclVCWGlrTnRoUDM0MlBicVJOcUltbzZLQ1ZiRXMxNUNsNXFmdmtZRUc5N1ZQMWJIczhBaklaMm9iWnhBSmozZlhJY2Zybnprenphcm94QzB4WEFoOXEreDM0V2Z0MGxHYTZlZ3RweldUOC9YNEVLSyt1NEY4b3pZMlAwc01UT01OdEVwU3VRUEJBS2MzcjhTa2ZGdkhXVFdscnFjWmttOVlWVDIzWFVRS2xFcWhOSVEvNGNYQW9pSnV5SXV4eTJ1ejFhRkJhZ2N3K1hDQ3lXVnJVWktZaHk4M3VockJIZ1RFTmNFdEFaMnoxZHFMOGhMbHdFL01FUml0cXJ4WmpUbVNxR0xueVV5ejIrR0JHOGMwdyt1cG5QMnd2Q2xpS3k0VDJ3SDRLcThyV21ub3h3cG4rRWJHZVN4ZTBCZCtUSTNqV0NCbjIzRDV0NWk4RkF0UXV1eDRaN3B1a1FSOHp1ZkYzUUllT2dyWG94dnBPaUt3RUdnMjhJVGZCRjBLazl5TnNPT1Vha2YiLCJtYWMiOiI4M2Y2MWY0MzBlNmE3ZmQwODNmNWMzMDNkNGE5NzkxN2Y5NTM1NjQ4YTc5ZGRkNjNlYjVjYjVkM2YwNzlkMWIxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:31:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9tRzdVN2NMRVN5L0w2SHhtaWNYRlE9PSIsInZhbHVlIjoiRTFWQ00wbTFZWWowWjBmMno2YUowaHZaekpTTGhYZ0tNUTlvVS9zUUUrQlUzeE82dXp1TmFoRHJGcFJHQVBFZENpRHJBVTg3L1NxVFc1ZUp6SWxsNHBKUzdDNmlmVVZJTStSRjZGSFlnSzhhTUk5MEpTaWg5QlNrSmRsWHNUWmNGaC9WeFg3L0crZ0xlWEYyTzlqdDJFSlRGdjZwOUJhQmVSNlZzOUo2eTBYYlM5RWJsM2NpQWIrbjRzcURGdjhMNFVhSnl1YTJQRTN1MU9zZzVUcWZoeENCZ0k0OFhyRjRFdUEyc3hWNVFuL052Y2M3NjlZZnRqL0JKUzVMcURwM1FxNlZxbXN4cms3TkdFcEZTT2hVc1FvQzRzcWJjSTg0TWlrYnZaV0NWbXIwVkF5d21ockw0RkVpRjJwMXpLMEU1S0ZsTVJ6OWdCMTN4RUZTQU1kemFlaWJ5WmQzc2NtNnNGRjlvVHo0TGs2YllTVlp3aGpwYlgvOVpYOUdkV3MzZ21RTXd6MkVrdEd6azhRSkpONlZzNnpQaDhPTjk5TjU5ZDdFZkNYTkNUOW5YbXBDSllZZGtWQnJqR3BVZ1ErZE04T1JUNC8yS2RXbFBZUVFDT2NpenRjbGFuR0Q5djc0djFqeUdlOElvbWtQY3ZTNkNtTFcvU1RxZW84TkhNS3MiLCJtYWMiOiIzOWI2NjkwYjdhOWE1YjUyZTBhNWU2YmM4ZGIwZmFkNjJmMDFiOTY5YWZhMWNiZjM3ZGUwNTk2M2U0ODFkNTkyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:31:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhuZk8wamVOL1ZWOG1TZGpnWGt2VUE9PSIsInZhbHVlIjoiQ2hWUnVtUHNjSm9oVm5QOTNYWHpyUHJLU0N5dkcraE12VjBtU0NuR2RlNVA1NjFpN2VRWElVQjFyNkEvNHg1K3Z5RzJKQ0xXMHF6T1JQZmp2KzFwVFFTZGJqV3EyN3Z2SFk1MFpIeUJyR0pDME5QSXIyYmRNZ0JyclVCWGlrTnRoUDM0MlBicVJOcUltbzZLQ1ZiRXMxNUNsNXFmdmtZRUc5N1ZQMWJIczhBaklaMm9iWnhBSmozZlhJY2Zybnprenphcm94QzB4WEFoOXEreDM0V2Z0MGxHYTZlZ3RweldUOC9YNEVLSyt1NEY4b3pZMlAwc01UT01OdEVwU3VRUEJBS2MzcjhTa2ZGdkhXVFdscnFjWmttOVlWVDIzWFVRS2xFcWhOSVEvNGNYQW9pSnV5SXV4eTJ1ejFhRkJhZ2N3K1hDQ3lXVnJVWktZaHk4M3VockJIZ1RFTmNFdEFaMnoxZHFMOGhMbHdFL01FUml0cXJ4WmpUbVNxR0xueVV5ejIrR0JHOGMwdyt1cG5QMnd2Q2xpS3k0VDJ3SDRLcThyV21ub3h3cG4rRWJHZVN4ZTBCZCtUSTNqV0NCbjIzRDV0NWk4RkF0UXV1eDRaN3B1a1FSOHp1ZkYzUUllT2dyWG94dnBPaUt3RUdnMjhJVGZCRjBLazl5TnNPT1Vha2YiLCJtYWMiOiI4M2Y2MWY0MzBlNmE3ZmQwODNmNWMzMDNkNGE5NzkxN2Y5NTM1NjQ4YTc5ZGRkNjNlYjVjYjVkM2YwNzlkMWIxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:31:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}