<?php
/**
 * ملف تشخيص مشكلة مبيعات POS في شاشة إدارة النقد المتقدم
 * 
 * هذا الملف يساعد في تشخيص مشكلة عدم ظهور البيانات في فلتر التاريخ
 */

// تشغيل هذا الملف من خلال المتصفح: /debug_pos_sales.php

require_once 'vendor/autoload.php';

// إعداد Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

echo "<h1>تشخيص مشكلة مبيعات POS</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .query-box { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; }
</style>";

try {
    // معلومات أساسية
    echo "<h2>1. معلومات أساسية</h2>";
    echo "<p><strong>التاريخ الحالي:</strong> " . Carbon::now()->format('Y-m-d H:i:s') . "</p>";
    echo "<p><strong>فترة الاختبار:</strong> 2025-06-01 إلى 2025-06-30</p>";
    
    $startDate = '2025-06-01';
    $endDate = '2025-06-30';
    
    // فحص الجداول الموجودة
    echo "<h2>2. فحص الجداول الموجودة</h2>";
    $tables = DB::select("SHOW TABLES LIKE '%pos%'");
    echo "<ul>";
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        echo "<li class='success'>✓ {$tableName}</li>";
    }
    echo "</ul>";
    
    // فحص بيانات جدول pos
    echo "<h2>3. فحص بيانات جدول pos</h2>";
    $posCount = DB::table('pos')->count();
    echo "<p><strong>إجمالي الفواتير:</strong> <span class='info'>{$posCount}</span></p>";
    
    $posInDateRange = DB::table('pos')
        ->whereBetween('pos_date', [$startDate, $endDate])
        ->count();
    echo "<p><strong>الفواتير في الفترة المحددة:</strong> <span class='info'>{$posInDateRange}</span></p>";
    
    // فحص بيانات جدول pos_payments
    echo "<h2>4. فحص بيانات جدول pos_payments</h2>";
    $paymentsCount = DB::table('pos_payments')->count();
    echo "<p><strong>إجمالي المدفوعات:</strong> <span class='info'>{$paymentsCount}</span></p>";
    
    $paymentsInDateRange = DB::table('pos')
        ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
        ->whereBetween('pos.pos_date', [$startDate, $endDate])
        ->count();
    echo "<p><strong>المدفوعات في الفترة المحددة:</strong> <span class='info'>{$paymentsInDateRange}</span></p>";
    
    // فحص بيانات جدول shifts
    echo "<h2>5. فحص بيانات جدول shifts</h2>";
    $shiftsCount = DB::table('shifts')->count();
    echo "<p><strong>إجمالي الشفتات:</strong> <span class='info'>{$shiftsCount}</span></p>";
    
    // فحص الفواتير المرتبطة بشفتات
    echo "<h2>6. فحص الفواتير المرتبطة بشفتات</h2>";
    $posWithShifts = DB::table('pos')
        ->whereNotNull('shift_id')
        ->whereBetween('pos_date', [$startDate, $endDate])
        ->count();
    echo "<p><strong>الفواتير المرتبطة بشفتات في الفترة:</strong> <span class='info'>{$posWithShifts}</span></p>";
    
    $posWithoutShifts = DB::table('pos')
        ->whereNull('shift_id')
        ->whereBetween('pos_date', [$startDate, $endDate])
        ->count();
    echo "<p><strong>الفواتير غير المرتبطة بشفتات في الفترة:</strong> <span class='warning'>{$posWithoutShifts}</span></p>";
    
    // اختبار الاستعلام الأصلي
    echo "<h2>7. اختبار الاستعلام الأصلي</h2>";
    
    $originalQuery = DB::table('pos')
        ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
        ->join('users', 'pos.created_by', '=', 'users.id')
        ->join('warehouses', 'pos.warehouse_id', '=', 'warehouses.id')
        ->join('shifts', 'pos.shift_id', '=', 'shifts.id')
        ->leftJoin('financial_records', 'shifts.id', '=', 'financial_records.shift_id')
        ->select(
            DB::raw('DATE(pos.pos_date) as sale_date'),
            'users.name as user_name',
            'warehouses.name as warehouse_name',
            'shifts.id as shift_id',
            DB::raw('COUNT(DISTINCT pos.id) as invoice_count'),
            DB::raw('SUM(pos_payments.amount) as total_sales')
        )
        ->whereBetween('pos.pos_date', [$startDate, $endDate])
        ->groupBy('sale_date', 'pos.created_by', 'pos.warehouse_id', 'shifts.id')
        ->orderBy('sale_date', 'desc');
    
    echo "<div class='query-box'>";
    echo "<strong>الاستعلام:</strong><br>";
    echo "<code>" . $originalQuery->toSql() . "</code>";
    echo "</div>";
    
    $originalResults = $originalQuery->get();
    echo "<p><strong>نتائج الاستعلام الأصلي:</strong> <span class='info'>" . $originalResults->count() . "</span></p>";
    
    // اختبار الاستعلام البديل
    echo "<h2>8. اختبار الاستعلام البديل (بدون شفتات)</h2>";
    
    $alternativeQuery = DB::table('pos')
        ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
        ->join('users', 'pos.created_by', '=', 'users.id')
        ->join('warehouses', 'pos.warehouse_id', '=', 'warehouses.id')
        ->select(
            DB::raw('DATE(pos.pos_date) as sale_date'),
            'users.name as user_name',
            'warehouses.name as warehouse_name',
            DB::raw('COUNT(DISTINCT pos.id) as invoice_count'),
            DB::raw('SUM(pos_payments.amount) as total_sales')
        )
        ->whereBetween('pos.pos_date', [$startDate, $endDate])
        ->groupBy('sale_date', 'pos.created_by', 'pos.warehouse_id')
        ->orderBy('sale_date', 'desc');
    
    $alternativeResults = $alternativeQuery->get();
    echo "<p><strong>نتائج الاستعلام البديل:</strong> <span class='info'>" . $alternativeResults->count() . "</span></p>";
    
    // عرض النتائج
    if ($alternativeResults->count() > 0) {
        echo "<h3>عينة من النتائج:</h3>";
        echo "<table>";
        echo "<tr><th>التاريخ</th><th>المستخدم</th><th>المستودع</th><th>عدد الفواتير</th><th>إجمالي المبيعات</th></tr>";
        foreach ($alternativeResults->take(5) as $result) {
            echo "<tr>";
            echo "<td>{$result->sale_date}</td>";
            echo "<td>{$result->user_name}</td>";
            echo "<td>{$result->warehouse_name}</td>";
            echo "<td>{$result->invoice_count}</td>";
            echo "<td>{$result->total_sales}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // التوصيات
    echo "<h2>9. التوصيات</h2>";
    echo "<ul>";
    
    if ($posWithoutShifts > 0) {
        echo "<li class='warning'>⚠️ يوجد {$posWithoutShifts} فاتورة غير مرتبطة بشفتات. يُنصح بربطها بشفتات.</li>";
    }
    
    if ($originalResults->count() == 0 && $alternativeResults->count() > 0) {
        echo "<li class='info'>💡 الاستعلام البديل (بدون شفتات) يعطي نتائج. المشكلة في ربط الشفتات.</li>";
    }
    
    if ($posInDateRange == 0) {
        echo "<li class='error'>❌ لا توجد فواتير في الفترة المحددة. تحقق من التواريخ.</li>";
    }
    
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>خطأ في التشخيص</h2>";
    echo "<p><strong>الرسالة:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>تم إنشاء هذا التقرير في: " . Carbon::now()->format('Y-m-d H:i:s') . "</small></p>";
?>
