{"__meta": {"id": "Xa88a3b20acd434d618b7f720d7bc25b5", "datetime": "2025-06-06 19:35:17", "utime": **********.495302, "method": "GET", "uri": "/users/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238515.916027, "end": **********.495337, "duration": 1.5793099403381348, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1749238515.916027, "relative_start": 0, "end": **********.218342, "relative_end": **********.218342, "duration": 1.3023149967193604, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.218366, "relative_start": 1.3023388385772705, "end": **********.495341, "relative_end": 4.0531158447265625e-06, "duration": 0.276975154876709, "duration_str": "277ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51631680, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x user.create", "param_count": null, "params": [], "start": **********.469347, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/user/create.blade.phpuser.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fuser%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.483521, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET users/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.create", "controller": "App\\Http\\Controllers\\UserController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=51\" onclick=\"\">app/Http/Controllers/UserController.php:51-62</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.011519999999999999, "accumulated_duration_str": "11.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.309169, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 46.962}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.342126, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 46.962, "width_percent": 8.333}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.350044, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 55.295, "width_percent": 9.375}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.360048, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "UserController.php:54", "source": "app/Http/Controllers/UserController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=54", "ajax": false, "filename": "UserController.php", "line": "54"}, "connection": "ty", "start_percent": 64.67, "width_percent": 6.944}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.369829, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "UserController.php:56", "source": "app/Http/Controllers/UserController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=56", "ajax": false, "filename": "UserController.php", "line": "56"}, "connection": "ty", "start_percent": 71.615, "width_percent": 7.813}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.431307, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 79.427, "width_percent": 9.375}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.437753, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.802, "width_percent": 11.198}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-101709594 data-indent-pad=\"  \"><span class=sf-dump-note>create user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101709594\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.448744, "xdebug_link": null}]}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/create", "status_code": "<pre class=sf-dump id=sf-dump-156600766 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-156600766\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1347599245 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1347599245\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2067106702 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2067106702\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1676823530 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238482606%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9SaG1TeWFZeEUwWDBhdWNQcTBBS1E9PSIsInZhbHVlIjoiRk5KN1lEcStuZ0pVMnRVTkIxbHhEdWY1L3E3bjVtZmxFbmhuZkFvQS83cGNrQTFFQ3YyeDI4Z1FiUElxL1BtdVF0dXY5TjRpYW5CTW5KNitaaHhzOVhPZGUzSVBDUWMwblBqYnh3SHVQOUhWZndMeFNCN01QMlI0YjRlRGhLOWpMVU1lWDZuWWF3WnIxMzRJWWZWenF4dGh5dkhMenFFcUJHZnZiRG5ZaWU1TVNIbXhFUVhxQVVtKy9wOXNMMGhGOHdBR3JXRXA4bFlkYWY2MnM3MGZBRnNLb0lEQnJTQSt3eHRrdDlLbC9oaXBlUDAyMEJmd05neUw4UHBnUVdZL1RTSDdwVTF4NktKMlhyd1J2SlZTM05JSWpTZnJpQThnSzVJSVVheEl2WTZYWkxZU3FpZTZRWkZqeVdScWxZSytVVW00RStRUEFxc3l4Nlh0MU9ueU92MHVTRmMzZEVKK1RWRmFvUHBCOE56RnBnWXJzZlhRdit6eGZLWENMenNPQ0lubFdFeWZUZUFvTXNPWjlsL2llNHFsZ3BTWnRZWW5ISHFWbDg2ZW53ZnpMVStPL09XYmRxU0FzMTBSaWtPaXF6TDNydnBIem5kZTRQOENwWEVVSFdDTG5adWxMU1NKZDhkcU1aL2tTNzB4TG5lTUJCL3VRMmNOYUM1WGhGTGEiLCJtYWMiOiIwMGViYmMyNzNjY2FjNTE5ODZhNGE5OGVhMDdlZGJlODA5MDhmZWRmYTg0YzNhYWIxM2U4NmFlMzMwMjE4MjNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik9yZjhGT2FZMVI1TVNYc0lwN3RrR0E9PSIsInZhbHVlIjoiZ0dTT3A1c3NRdGxkNkFyV1I0RkVpRHJwMlNOcHRlWnJmdFJlWjI3OTdtTXlXSW92Q3BNL1F3RVIyeXRtQmQvZG81SUZTT3o4L2ZzMklzTHF1d1JVcjJxSFBodTJ4eHZYbXk4N05FYnZyYzlpK2N1NjgrS1BjTmw0OFNJeU1uOUlkTFlIU05pditXNm9NRDBxWGFycjBKSTFEQUJxSlU2bVA3WENaZVNRZTVtVUl6cENCTW1tNXBHSi9RRUJCNmg0ZHJNYVp3cGdOU0h2Y1BjQ2RYNTlYUm1EN3dSWGNmQTEydE5wVzZIbXlucWs4Q0VhU09tZXV0cCtpdEgvTG9HcmV1OEdCQlN4bjRtRnJEU1g5K3dvTHVJQU83ZkRYdDlDSEduYTU3M2JYOXBaZ3NEOHExK2s1cUFjZXZwUm9hSjR0ZXd5amZRYU4yb1VyK0JqVkgvdEJ5c2dxVGNDV09pR0F5eWs0Sk5EUkdpaWMydExTTVV0cktkSWdKNFJtWVhhR2xETGhFbndMaVpkODVzc2RYZ0VHalpqSlZUT05QVFM0WUI5OHRHNjdlYU15Mk5aM2Y3a2dFVTBRWmkyVlkxTlJhTWxDWHVnTTluT1daOERqbnhvZTE2N21tZVhjR3d1S2xEYTlJSmNUWUx2ajZZbmtEYmU1d2pacHlwcS96a1AiLCJtYWMiOiIwNWYzOTJhOTEwODBkZGU3ZDZiNzFjZWY3MmZlOGViYTE0ZDFlMjI1NzUyMmI4ZGE0MTI0ZjE5OTlhMTlmOTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676823530\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-713804704 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713804704\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1424797219 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:35:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRIaCsybloxelRnOGRabnVaRHNmZXc9PSIsInZhbHVlIjoidGdZcjRZVUFoOHlmN0YvSURKbDUxblpab3F5Znc5eGdaaHhaUUxMaFVpQWVuSmd5Y2ovd2xFbXFqdXJPdzhrUlI4QXc3NHc2OFF3eEx3Y2YrYWhQTWdUK0NHOTBjNU1zTjJEbUR0UXFidU9rMzJxU253bHZyNHhtWHRsR0xSSjhjSmZPQzViek14QVVES09OSkY5cVpBTEJtZ1liZzlpMVM3RDNsRjlJSkw1Tm9maFJGMUdoaERXU1lzVjFsemtPYnBGbmpRQTcwOWdYSXp1bm1YbENoVjJOQ3p3OEV1SWpyMDZGT3FDTis1QUNTZTAyRUFnR0cyVVlnUHE5ckFrZXJQcjB4a2hjTzg1ZkRacEo0WXVTdTVOR1RhNXUyTVRPVU9NS3VhbWdURFFtbWJoTE5SenA3dlgvMmlKVVEzcnp6d3pxWmQ2VTB3VGs4UDU0aTMyU1dBUm5wTy9yT0hzWkdZZ3JVNExYWEpzWncxb0NjemxscEhOcmxhRjhBU2Z2MnI3MDM3K2dCeXBYZGhpZWtjZC9MMFhSZVZpSGVLMC9wektiZmJXeUp0WFNzZzZrclY2NkRmbFFBY1RoNDZvcmkySnptLzJ1YUZvOWRqZHg3ZG1xZW9aRkxoVnpNQ1grcTdpbXVNeXJLYUpNdmpNbEZmSFhHZWVWU2JCNE5kU0ciLCJtYWMiOiJjNzc1YTIzNjFiNzJlZThmODlkZjEyODIzODU1N2Y4Mjg5ODkxNGMwM2I4MGU1ZGMzMjI5NDU4MGI1MTk3YTVmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikt3Q2RFWi9VZkZMNXdEMVAzRVVwSGc9PSIsInZhbHVlIjoiRFc4NTNZNjZmWWFNQTE2bUpNNU03NHJBcEcwY0VrdEJaNXE3Zm1TRDQzdEhKTmhZcW1RcHJNMjRsaGl0REJsWUh3S0pBRk1XZlJxU3BNNlFsb3QvcitVNEMyRVJhNitsN0JyT2g0Yk5CUi84MTVDRGgrVkcwbzFuU3NpL0JiOFF3ODRjbysvVnRua3JIZGJoUzNyZFBzdHo4Q0ZPV01iVGh0NWVqaVovYlFBSWlpOXVtdjZMZVZDaXhMZ25MWlJFT0tUOVBiZTc3ZytxVStSSXNxYktwMjFEVE1EckJpVjN2eWt3Q09OMWMvLzZRRElRbU1PYlVCSFhpVW83WVc3M3IzM00yaUdVMGdZRzZSN0pFVW51Vk53WUxmc2xQNE5sNGpFYld0cSt3Q2lvSmlZRk1aZHBWbkFlTTFCMVFOVENjajZrWEhWOXkzR0d5N1M3aTlsVFZVTHp0OEtMSzJEbUFDc2pua3ZjR0hlckxkMVlqYnJpQUZtQlpaNGJ1dTVvUDNqUnpFTXNmY1ErTDBKcCtZZHFySllSc2lTYnZCRVA0NjJDWjI2V1NlODlMWDdnV0p2QWF5VmpIellJNkxXTGdYaEw2QVM2TnhFRVpQcHJITkpzUk5vVlRTN3FiL1hWLyt6dW1kY0dRaGhxZHM1VnlkcWFWQ1hZb1MySTRvM08iLCJtYWMiOiJmNzYxY2RjZmVhMDgzYmIyM2ZiMDhmZGJmNWIzZWU0Y2E1ZjcxMGQ4MzVkNjJiMjJiOWRjYTBiZTc3MDk2ZDU1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:35:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRIaCsybloxelRnOGRabnVaRHNmZXc9PSIsInZhbHVlIjoidGdZcjRZVUFoOHlmN0YvSURKbDUxblpab3F5Znc5eGdaaHhaUUxMaFVpQWVuSmd5Y2ovd2xFbXFqdXJPdzhrUlI4QXc3NHc2OFF3eEx3Y2YrYWhQTWdUK0NHOTBjNU1zTjJEbUR0UXFidU9rMzJxU253bHZyNHhtWHRsR0xSSjhjSmZPQzViek14QVVES09OSkY5cVpBTEJtZ1liZzlpMVM3RDNsRjlJSkw1Tm9maFJGMUdoaERXU1lzVjFsemtPYnBGbmpRQTcwOWdYSXp1bm1YbENoVjJOQ3p3OEV1SWpyMDZGT3FDTis1QUNTZTAyRUFnR0cyVVlnUHE5ckFrZXJQcjB4a2hjTzg1ZkRacEo0WXVTdTVOR1RhNXUyTVRPVU9NS3VhbWdURFFtbWJoTE5SenA3dlgvMmlKVVEzcnp6d3pxWmQ2VTB3VGs4UDU0aTMyU1dBUm5wTy9yT0hzWkdZZ3JVNExYWEpzWncxb0NjemxscEhOcmxhRjhBU2Z2MnI3MDM3K2dCeXBYZGhpZWtjZC9MMFhSZVZpSGVLMC9wektiZmJXeUp0WFNzZzZrclY2NkRmbFFBY1RoNDZvcmkySnptLzJ1YUZvOWRqZHg3ZG1xZW9aRkxoVnpNQ1grcTdpbXVNeXJLYUpNdmpNbEZmSFhHZWVWU2JCNE5kU0ciLCJtYWMiOiJjNzc1YTIzNjFiNzJlZThmODlkZjEyODIzODU1N2Y4Mjg5ODkxNGMwM2I4MGU1ZGMzMjI5NDU4MGI1MTk3YTVmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikt3Q2RFWi9VZkZMNXdEMVAzRVVwSGc9PSIsInZhbHVlIjoiRFc4NTNZNjZmWWFNQTE2bUpNNU03NHJBcEcwY0VrdEJaNXE3Zm1TRDQzdEhKTmhZcW1RcHJNMjRsaGl0REJsWUh3S0pBRk1XZlJxU3BNNlFsb3QvcitVNEMyRVJhNitsN0JyT2g0Yk5CUi84MTVDRGgrVkcwbzFuU3NpL0JiOFF3ODRjbysvVnRua3JIZGJoUzNyZFBzdHo4Q0ZPV01iVGh0NWVqaVovYlFBSWlpOXVtdjZMZVZDaXhMZ25MWlJFT0tUOVBiZTc3ZytxVStSSXNxYktwMjFEVE1EckJpVjN2eWt3Q09OMWMvLzZRRElRbU1PYlVCSFhpVW83WVc3M3IzM00yaUdVMGdZRzZSN0pFVW51Vk53WUxmc2xQNE5sNGpFYld0cSt3Q2lvSmlZRk1aZHBWbkFlTTFCMVFOVENjajZrWEhWOXkzR0d5N1M3aTlsVFZVTHp0OEtMSzJEbUFDc2pua3ZjR0hlckxkMVlqYnJpQUZtQlpaNGJ1dTVvUDNqUnpFTXNmY1ErTDBKcCtZZHFySllSc2lTYnZCRVA0NjJDWjI2V1NlODlMWDdnV0p2QWF5VmpIellJNkxXTGdYaEw2QVM2TnhFRVpQcHJITkpzUk5vVlRTN3FiL1hWLyt6dW1kY0dRaGhxZHM1VnlkcWFWQ1hZb1MySTRvM08iLCJtYWMiOiJmNzYxY2RjZmVhMDgzYmIyM2ZiMDhmZGJmNWIzZWU0Y2E1ZjcxMGQ4MzVkNjJiMjJiOWRjYTBiZTc3MDk2ZDU1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:35:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424797219\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1628941667 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628941667\", {\"maxDepth\":0})</script>\n"}}