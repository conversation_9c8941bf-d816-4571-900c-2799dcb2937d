{"__meta": {"id": "X1983c38dd9cd65b94dfc6a681963c9a7", "datetime": "2025-06-06 19:37:59", "utime": **********.625267, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238678.021316, "end": **********.625306, "duration": 1.603989839553833, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749238678.021316, "relative_start": 0, "end": **********.434065, "relative_end": **********.434065, "duration": 1.4127490520477295, "duration_str": "1.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.434089, "relative_start": 1.4127728939056396, "end": **********.625311, "relative_end": 5.0067901611328125e-06, "duration": 0.1912219524383545, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44776104, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00844, "accumulated_duration_str": "8.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.534573, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.36}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.571422, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.36, "width_percent": 13.744}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5818288, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 73.104, "width_percent": 13.033}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6035142, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.137, "width_percent": 13.863}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1250992993 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1250992993\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-737117242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-737117242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-55374780 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55374780\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1632365954 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238647866%7C51%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNnOG4xQ1BEYm4yTlZkVk44SUozUWc9PSIsInZhbHVlIjoiWUw0dDN6d0JsSk55dERQUlo1T3pzdWRwdVQrNkYvSnpJdytDaXM1NFQ4dWl2UmMxQmZpOElzN3dTNU5zN0xnWHJubzVPbXVnd08wbmtJTXQ2dENOSzExYXhmUnBkbzVqWDl5bjFVUTVGKzFCUEhkMmx1UVBMMGtCMUlzL1kvL0hBdFB2T1hKNFRNaUpGWDlEdTM1ejJzR1JmQmtHaEtYUmlvVXVnTnZRcTNySk9STGg2aTVIQ1gwZ0NOeUxWWUFPbmYrK1hxaXNpRmYrakV1Q2oveDh1YUFUdGcvZTYveENRQ0p2WFVWa2hEK2hrT2ZUcWhHaWl5eGxva3J0UnVvTURIWHJQN1dZYzZDUEU2YkJUQ3lMVlFEdDc5Rnl4dHE2ZW16SDZvSWNjSVFqc1dEcnozVWVnNXVnQnpwb25oaXROaTdUanBSZXhKbVhKVU5qM1BtNEw0eEdIcUVCR2VnYkZxbnlCSWNwbWtrc0tiSGF1K3g3TVhsTzQyM2ZhUEtPV1lzeHBSM3o3RkNFVXhQU1NsR3FteFlzWG1ZRXBXZlpCbVk3N3BYMTBoWXhMelpYTVN2VUs1RnF6YnBsRXhVMEVSZ21MNUJWbzZzRi9pWWt3UCsveEI1L0ZWaWJFcis3cjdyaTV2dlY2emo1d0FacVJCRXhrWmVtalZEMzJOOG8iLCJtYWMiOiI3ZDMyYTA5Yjk5OGNmODE5YThlMDE5Y2E1ZDUwYTEzMDM1MjgwZWRlZjEwMzM1YzI5YTk1OTJiNTIxYmM2ZjM1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkgzQ2w2WjlMY0REa1VDUlUyYzlaZ1E9PSIsInZhbHVlIjoiYUdUSktGY01uWkZFYm8zZ3hSdnBmY29FbzBhNjU1d3RjVUkrRTVhZTNwZDJCekR6RGdMa2F1TUZ2VjV5a0hDSE9qc2lPbWFQVjNlRGdieWd6a0l5S2JtZkFINnZCVExPNlpLeC9ndUFmZDZscFVGOEFLQVNTQitLSnBNRzcwOW80dEtsaWFEL2hJUXg4VGZuZHUvcUxlY1RlZEI3TWdaOVBMWCtNMFBXendqaGpjTVpWazAzRkd4dkJ5V3BwdDNpTnNmZ0kvNGVtcmR1R2E1QklWdHozYTZpblVCVC83VXNqVGliT1FSbmRWRms5Rnd1N2hTR3NTWUd2U2tOajhmcEltNGNDUGhEZVhmRnRjU0UxdVZ2eDdnbjljd1pGWlNzenVlWGZMMDFud3VwdlJwQWx6bG51Q2Fxck03MTVmbkZuN2VUMlptV0ZOdUM1aVIzY1pIUXJnYVZRaXRxK3lBRVB4bTRoTUgxWVBrVEoram9pRkZMTVZjclJGZ2V0MTYxNmFpSTl5Wk02TCszMnlWNkR4SzhkUjlzTVVzT2FHcW5ab2UvUkxzb3Z3dVFyR0Q0MFRrZTdIbjEvNS9lR2NwVjBmQ1pwTmh4Z2s4QUpPaFZnNFpWbEdVQlNnc2ZpbGtxK29NTTVmN1QyNmdSYkF2dUN5UmdkTGxXeUVQS2tYN3AiLCJtYWMiOiIwYTRmYzEyZWJhODBiOWRhYWVkZmJiMzU5YTRhNzZmN2Y0YmFkNDhjNDI0YTRkNTJjMWRlNjE2ZWE2Y2IwMDllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632365954\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-492328500 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:37:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imt4M1JnWXZrM0VWTjBOZU44OWxqZXc9PSIsInZhbHVlIjoiN1lSbWIwc2FLZ3h5Z24wOUgyMGtYTFZWdGlIMTNhL0laVURqUGZSNFM2cmJHU05WK1dNRkRzMFUxRUxBMVlkRFVlVlNqdTAxR3RlMDlFc0ZFeklsRnFEYy9qWGNKMzdJQmc5bklCSU9kek9GRURJZ1FCMk9aazdtN1NiYTU0WTVOSTVsNXgyVlhvWXdpb0pCcGt3WHJnUWZpd1JUMmxXTmtkQzdoNzdPbXh6TlRhZ1lwM1VZZVAvNmNpNEc2N0tWZ1JUaXRZZmYyUmZMZld0K1JueWdadlNGSkNqUjhoSXRXSENrOGNUdzVYS1N2dDEzKzFXNGVXdUJBM3FrWUhMREowMXBKNkZhYUdjZE1Nd3JrZVVISnIrWWlsZW0yZnU5cjhsVjkrVk1VTDMyemRJQWJYc21sam5WK01lUW9PdWd3cFRQUUN3YXRsRitPWE1OaUJQb25sQlQySjJTUmJCY2Fac1NwYms4N09YUHF5emhVWUh3dWRadXM4b1BSa3N5KytWb0Q4ZElhUzN2eGVTaTN1OUx2Y2FEOVRCT3dNSFpHT1VkK3dBZ0RFd2wvMzhoOVlqUTF1TTdiMHYyT3FNOEl5ckF6TmVJMGdDZEFwMHQrY09DcXBtRnhIWXJOc1AycU1UQm1qcFp4dFpGNm5HZXkzSS9uQkNVRHlpLzVma2UiLCJtYWMiOiIyZDcyMWFlMzMwNWNiMTA3OTQyNTYzZmMwZTJmNTBjMDk0YjA0M2VkYzllNGY3N2Y3MGYxMGQ1MzAzNzUzZjc3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlM1dkNrODZNdFdCbEZnYlNHOU1uakE9PSIsInZhbHVlIjoidlRtN0NIb3VITmpvTzlVNzVzZTIyZU5iaVdpZlZMSTBPYWFBYXNDSlBMZWNmUXd6T2NKOEhxbnhrMWtqWUdwN0lGeTMwbGVoaFg1bDNidlV4ellSQ2lTTjhXd3Q2alUwajRlRzN4UjdjSHNwcGl0REZQOFg1V09yK2VWU3oybzhhWnBHTGE5Qkt2MXF6QXlicHE4TGdhdlVpeXZlRkIxRDJzQXJrU0lMYXNnZ1dUQ0xOR1EvZGNTUXBHMmdlLzJPQWdVMERUdWVaY1luVW5OUXFOSkx0Y2pSQTJ2ODFYclBvaElLSCt0Q3hOZ1hKWlZrUVVXNGt6Ym5CTC9SZzQ3Rjh6c0ZUME8rOVRZUmxxNUYzdnVXVzh3ek1HRlVyejBrWnNNZHhVbVUrd0hLa21XWXhOcVZUbnhZa0Q0NXJXdTVCNHp6dVB0RjdORGpYVEZLSzVpZ0RYMFBMSFFYM2tFalgxQWtOaUZRcGJMZFRpZ2NQdnUyb28xbEpONVdkRXk0aGNEdzZPVThGdlZmREZVSGN2WDU3SXpBS3RJVlR4Z3BkSGEySDRZeXBLWXlKNnZBSWk4aFlpdmFPamxqcmgxTUkvOGFlVHlIRVEvL0VmNU5TYnU2SGEvNkI5U0k5aXdsWUlZVnV1ZUJWUXUzNTB0ZTFmWVNJNGRORnZTTjZ3dnQiLCJtYWMiOiI1NzMyYjBjZDM2ODc0ZTIxMTEwODNjZWQ0ZDU0Y2ViMTEwYWE5Zjk3ZGQ4MDM2ZTRmMmNlNmUyZmEzOGUyMjA3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imt4M1JnWXZrM0VWTjBOZU44OWxqZXc9PSIsInZhbHVlIjoiN1lSbWIwc2FLZ3h5Z24wOUgyMGtYTFZWdGlIMTNhL0laVURqUGZSNFM2cmJHU05WK1dNRkRzMFUxRUxBMVlkRFVlVlNqdTAxR3RlMDlFc0ZFeklsRnFEYy9qWGNKMzdJQmc5bklCSU9kek9GRURJZ1FCMk9aazdtN1NiYTU0WTVOSTVsNXgyVlhvWXdpb0pCcGt3WHJnUWZpd1JUMmxXTmtkQzdoNzdPbXh6TlRhZ1lwM1VZZVAvNmNpNEc2N0tWZ1JUaXRZZmYyUmZMZld0K1JueWdadlNGSkNqUjhoSXRXSENrOGNUdzVYS1N2dDEzKzFXNGVXdUJBM3FrWUhMREowMXBKNkZhYUdjZE1Nd3JrZVVISnIrWWlsZW0yZnU5cjhsVjkrVk1VTDMyemRJQWJYc21sam5WK01lUW9PdWd3cFRQUUN3YXRsRitPWE1OaUJQb25sQlQySjJTUmJCY2Fac1NwYms4N09YUHF5emhVWUh3dWRadXM4b1BSa3N5KytWb0Q4ZElhUzN2eGVTaTN1OUx2Y2FEOVRCT3dNSFpHT1VkK3dBZ0RFd2wvMzhoOVlqUTF1TTdiMHYyT3FNOEl5ckF6TmVJMGdDZEFwMHQrY09DcXBtRnhIWXJOc1AycU1UQm1qcFp4dFpGNm5HZXkzSS9uQkNVRHlpLzVma2UiLCJtYWMiOiIyZDcyMWFlMzMwNWNiMTA3OTQyNTYzZmMwZTJmNTBjMDk0YjA0M2VkYzllNGY3N2Y3MGYxMGQ1MzAzNzUzZjc3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlM1dkNrODZNdFdCbEZnYlNHOU1uakE9PSIsInZhbHVlIjoidlRtN0NIb3VITmpvTzlVNzVzZTIyZU5iaVdpZlZMSTBPYWFBYXNDSlBMZWNmUXd6T2NKOEhxbnhrMWtqWUdwN0lGeTMwbGVoaFg1bDNidlV4ellSQ2lTTjhXd3Q2alUwajRlRzN4UjdjSHNwcGl0REZQOFg1V09yK2VWU3oybzhhWnBHTGE5Qkt2MXF6QXlicHE4TGdhdlVpeXZlRkIxRDJzQXJrU0lMYXNnZ1dUQ0xOR1EvZGNTUXBHMmdlLzJPQWdVMERUdWVaY1luVW5OUXFOSkx0Y2pSQTJ2ODFYclBvaElLSCt0Q3hOZ1hKWlZrUVVXNGt6Ym5CTC9SZzQ3Rjh6c0ZUME8rOVRZUmxxNUYzdnVXVzh3ek1HRlVyejBrWnNNZHhVbVUrd0hLa21XWXhOcVZUbnhZa0Q0NXJXdTVCNHp6dVB0RjdORGpYVEZLSzVpZ0RYMFBMSFFYM2tFalgxQWtOaUZRcGJMZFRpZ2NQdnUyb28xbEpONVdkRXk0aGNEdzZPVThGdlZmREZVSGN2WDU3SXpBS3RJVlR4Z3BkSGEySDRZeXBLWXlKNnZBSWk4aFlpdmFPamxqcmgxTUkvOGFlVHlIRVEvL0VmNU5TYnU2SGEvNkI5U0k5aXdsWUlZVnV1ZUJWUXUzNTB0ZTFmWVNJNGRORnZTTjZ3dnQiLCJtYWMiOiI1NzMyYjBjZDM2ODc0ZTIxMTEwODNjZWQ0ZDU0Y2ViMTEwYWE5Zjk3ZGQ4MDM2ZTRmMmNlNmUyZmEzOGUyMjA3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492328500\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-110914135 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-110914135\", {\"maxDepth\":0})</script>\n"}}