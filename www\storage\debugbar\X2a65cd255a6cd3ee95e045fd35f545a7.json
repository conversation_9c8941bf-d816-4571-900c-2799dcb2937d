{"__meta": {"id": "X2a65cd255a6cd3ee95e045fd35f545a7", "datetime": "2025-06-06 20:34:23", "utime": **********.413876, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242061.900103, "end": **********.413909, "duration": 1.513805866241455, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1749242061.900103, "relative_start": 0, "end": **********.21994, "relative_end": **********.21994, "duration": 1.3198368549346924, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.219964, "relative_start": 1.3198609352111816, "end": **********.413912, "relative_end": 3.0994415283203125e-06, "duration": 0.19394803047180176, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.015119999999999998, "accumulated_duration_str": "15.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.299768, "duration": 0.010029999999999999, "duration_str": "10.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.336}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3349352, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.336, "width_percent": 6.481}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.370589, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 72.817, "width_percent": 15.212}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.391175, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.029, "width_percent": 11.971}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1660620480 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1660620480\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1273376696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1273376696\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1800687448 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800687448\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1040408738 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242029923%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9wRk94NlBRUEF4VE9xeDZzMkFIdmc9PSIsInZhbHVlIjoicUwxZ3ovaFQvamNxOUx0U05VM0ZxMXlIdzZ3WHhqTlhyekEyb1FuUENiNnB2STZCVHErc29sMHdXRXg4NkI2dnV1ODlsV0Z5a2dRZ1E2SHBRY2dSNFhzaU5MKzNCS2pBWWhycCtPbGtvUVc5dDhJVWxHMXZLY3NGa0wwU0QvdGp3UWlIQlpoTGNsakgranBxQmdGWDRYN3pyaEJsYiticlNzZ3ljNFRjOU9DM0YvZ05pM3RyZ0dROCsycXN1K2FZOVlVd3FNeDJjc2lYRTR5TXd2cW01aGlBUmpheVlSczcraDBteW5ZR3ZkMFJ6SlZYZWRWLytDV1JzZldrR29TczlyaWkvZVYxM0toenZPSmNZNUNxQ1BtTzZVSS9OMTdTclpQWjFRbXpmalc4bS91bDBKa1RzR1ZCdjNBZWYzQktUS2dBNWNrdVQ2eHNxamFsZDd5VzdhNlQ3K2p5c2xqZ1g0Zll4Z3hycFVGVHJqdWFFRXRrVkJOMzU1T3ZOS0VqNS8rQUhGL2d5UFYzWGRSQlhGb08zNmw5RlJyNkpkTHpGVERrYjk5YWZYL1VJQ0hHUFpLWVRxLzJwdXNiWUdhanRsalhzcjVWanZXYkUvWUVMZmRSSjUyL2VrbFpIV0Q5Z0o0VzV1dEo4WmJTRUsvUm9ORzdqZ3FWcVVBV2s5SEMiLCJtYWMiOiJkNjMzMGFiYWJkZWViY2EwNzU5MDdlOGE0ZjllY2RhY2Y2YWY2N2M1YjM3N2M4MGMzOThjODYyYmYzNjc1ZTBjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVHbEZsOG9OOXlwZ292RHV1dFpKelE9PSIsInZhbHVlIjoiUVFIdVNhOVp0RzMxU2tZRGorcm1wWGNYU2w4MncwZzFOU2YwcURjM0tTU085a1RGK2lta1BkMXBFTWhFTVo4QlZkWU5ua1hPMTJGOUQ5b3REMC9XdkxEcDAzVUFDUEJFekxLSFRRTVluN1I4OUxSQXpHa09VT3VHdFljNVFLdUozTWhqOTFiSFkvQm9HUmhSblZ6Zm9hOStsTkdiN1hCTnVXeFR1dW8zaTFySTNYbXcydFJhRDVpWUZtZlVyblBndW5iaGhNc1FsV3NzWmhVR1NPMmZ2OWRhc1I5cy9PNk9Ia0ZBSFY4UmJMc0V5TGJnbXFwMnBBc3d5MkZFdFdFU3k1MTc4R1NRNWY3ek13ZzZIUGdjaml5MjVtR1lmVk1aOW5jTzBsZEpTeThoZnl2QnI1dW82azc5WjhHeFRxZmM1Uk9NYS9UR3Z6bXlWbnBwT2gyamV4dU5NWURUc0xtWkFQZ2FsTE9hNDJ6YUs4MDI2NDgraXIzTWxqWTVRZkdJMEZKSGZTOXcxaHJJL2RBRE1QcEptUllYYzZZVE5ReEpKQmhyK3ZORmlnU0xVWGhHbFBjTmxEK0ZNR0MzdnB3eHJvNlY2c0tOREhUbXk3TlZia1Z0dHRzU3lUaWd6OXhDMTBxbzhockxhcVBmaUpkQUd4Y1hCdENJWUpWTlFVVmsiLCJtYWMiOiI1MWI4M2Y1ZDVhYTE3ZjA2N2FlNDliZjRmYTFjZmEyZWE4NmZhOWY3MzlmNjBlNzk0NDYwYWJhZjVlNzg2M2I1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040408738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-175648603 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175648603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1644580826 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpjamYxRWtyaGs5ZHN2WXZSVUZESGc9PSIsInZhbHVlIjoicUhhV3pNQ3pwYU5xYnJSbUg3cmlhUXBNT0dFbmE5VmloMkJaWG9UYnU5QkVlZFBYRk5VNnk5VllpUFliSi9lZC9UOTQ3VWJqbHprUUJTWHdxUDYzSHRha3NSRnlZeFpsQ0dNbWgrQUk1OGYwaCtBLy8xcGdwa1BJai9velowQjN0ZzBRd2VFSXlURDM4MDQxeG1CQk1aZWZ5dVRWZzMrcWhxYmplOFBHdDNIUkFla1RWWUpLNS9wOUo1N3lCVFBnVnhQN2xkM1FRRmh3dTB2VGsvYnI1NENJaWxtZFFLZ0NXWVJ6aUJ0dTdqUUUrTWdlTVhGa0M0ak0rUU1BUzlDejBrWXJ5RVdMNEtUS09TN3RzRngzck1hbHNRTnVnbnRvY21TZGh4RC9EbzA3b3NkaTBmSUltRXZnR0NPYnhUSmFyZHF4eXVxaUtaMmd4ZFBKL25KNlhzUmJLOVhSa2s1dWJ0VDlMODU4bkhjbHlwandURVpaZFJYQjRqb3VPRVIwL20wdWZWZlREMDdCSXI1TXRXdVVSOVhKcjc1YXd0UUlzWFFGVCtyaFJUU1E3OGltd0NLenBlaXl0WG5TRkxGNjhtMXRBRy83R0dSR0kyQjVYbTRhaE1keDVaN2RTeFJEUXUxK2IwUnRJNkVTUGMvZjNiYUhEVFY4czJ1NVdROHoiLCJtYWMiOiI3MjA5ZmY3Yzk4Njg5MDFhNGJmZDYxYjY5Y2Q0Zjk0ODIxMTZkYTNlN2RlYmI3OGYxN2E2ZGI5NjMyMDM4MTMyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imw3UXpTc0RLYjZISEZhVSs0dWxwZUE9PSIsInZhbHVlIjoia05YWmo3ZXFvUENlbkFMR0hETDNzRUxDc1k3bG0zWTY2TDJpcmJlcVZ5QkhSaG5OdUgyMk5wbmN4UlJFOHRoRmZqRHVkTEFGYjVxaXlDTmlNSk9Cd0ozcG4ydGNxUWFvSTE2SnhxaGFUYjNxa2E1eCszLzNLZGd0RU54a1E1eGlNK1JabmpiRnEwM1R1WGkrdk5kR285c0N3TGhPd1BodTg1ZFYrTTNCSGxTKzZqMjliUUdGMVJaU3NBTjVSWTNoVEp6eXczUUNSNS9pZmtBcE0ybFFvT0dreEIyZmtVL2c4enpkaDd4akZhL25ydk5abjV2d3hQQVFHVzlqOFI5KzNkWHd4SEJPRWJmMFY1OXJUKzh4NHpMeEJHQjlmaWdDM3luaUpCK1MyRVNEcmd0bEEzcHpVb2hCUTlFWU5EYVNoVWJBWnBzYjAyVHNMeks0MDdWSmVBM3JpUUIvdUxtVlB0b2ZUMkVaSTB4dVpYZ1R5b25KaEZqSzQ3OWo2UE83YzJ2bm55SmE5T1huUEgvdnNmMnRHNFZVZGMvRnBZZE5OTWRCTXlPc1B2dU1iakR2VTFTY3VDQnlwS1pOQ0FPOElRWEhhNTZheTNiWWRPZmpHRElnMTJJU1pyeTZzWkswSmJuWDYxTHROMU9pZUpkQVc5TjFjZ3MxZ2wwVDIrRzQiLCJtYWMiOiJmZjFiN2IxNDllZGU4ZTc4YjZiODNiMmNiMDRkZmJmMDk1ZjlhNDIxZmFlNjRhOTY2MjZiYWQ5NzQ2MjUyMDBlIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpjamYxRWtyaGs5ZHN2WXZSVUZESGc9PSIsInZhbHVlIjoicUhhV3pNQ3pwYU5xYnJSbUg3cmlhUXBNT0dFbmE5VmloMkJaWG9UYnU5QkVlZFBYRk5VNnk5VllpUFliSi9lZC9UOTQ3VWJqbHprUUJTWHdxUDYzSHRha3NSRnlZeFpsQ0dNbWgrQUk1OGYwaCtBLy8xcGdwa1BJai9velowQjN0ZzBRd2VFSXlURDM4MDQxeG1CQk1aZWZ5dVRWZzMrcWhxYmplOFBHdDNIUkFla1RWWUpLNS9wOUo1N3lCVFBnVnhQN2xkM1FRRmh3dTB2VGsvYnI1NENJaWxtZFFLZ0NXWVJ6aUJ0dTdqUUUrTWdlTVhGa0M0ak0rUU1BUzlDejBrWXJ5RVdMNEtUS09TN3RzRngzck1hbHNRTnVnbnRvY21TZGh4RC9EbzA3b3NkaTBmSUltRXZnR0NPYnhUSmFyZHF4eXVxaUtaMmd4ZFBKL25KNlhzUmJLOVhSa2s1dWJ0VDlMODU4bkhjbHlwandURVpaZFJYQjRqb3VPRVIwL20wdWZWZlREMDdCSXI1TXRXdVVSOVhKcjc1YXd0UUlzWFFGVCtyaFJUU1E3OGltd0NLenBlaXl0WG5TRkxGNjhtMXRBRy83R0dSR0kyQjVYbTRhaE1keDVaN2RTeFJEUXUxK2IwUnRJNkVTUGMvZjNiYUhEVFY4czJ1NVdROHoiLCJtYWMiOiI3MjA5ZmY3Yzk4Njg5MDFhNGJmZDYxYjY5Y2Q0Zjk0ODIxMTZkYTNlN2RlYmI3OGYxN2E2ZGI5NjMyMDM4MTMyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imw3UXpTc0RLYjZISEZhVSs0dWxwZUE9PSIsInZhbHVlIjoia05YWmo3ZXFvUENlbkFMR0hETDNzRUxDc1k3bG0zWTY2TDJpcmJlcVZ5QkhSaG5OdUgyMk5wbmN4UlJFOHRoRmZqRHVkTEFGYjVxaXlDTmlNSk9Cd0ozcG4ydGNxUWFvSTE2SnhxaGFUYjNxa2E1eCszLzNLZGd0RU54a1E1eGlNK1JabmpiRnEwM1R1WGkrdk5kR285c0N3TGhPd1BodTg1ZFYrTTNCSGxTKzZqMjliUUdGMVJaU3NBTjVSWTNoVEp6eXczUUNSNS9pZmtBcE0ybFFvT0dreEIyZmtVL2c4enpkaDd4akZhL25ydk5abjV2d3hQQVFHVzlqOFI5KzNkWHd4SEJPRWJmMFY1OXJUKzh4NHpMeEJHQjlmaWdDM3luaUpCK1MyRVNEcmd0bEEzcHpVb2hCUTlFWU5EYVNoVWJBWnBzYjAyVHNMeks0MDdWSmVBM3JpUUIvdUxtVlB0b2ZUMkVaSTB4dVpYZ1R5b25KaEZqSzQ3OWo2UE83YzJ2bm55SmE5T1huUEgvdnNmMnRHNFZVZGMvRnBZZE5OTWRCTXlPc1B2dU1iakR2VTFTY3VDQnlwS1pOQ0FPOElRWEhhNTZheTNiWWRPZmpHRElnMTJJU1pyeTZzWkswSmJuWDYxTHROMU9pZUpkQVc5TjFjZ3MxZ2wwVDIrRzQiLCJtYWMiOiJmZjFiN2IxNDllZGU4ZTc4YjZiODNiMmNiMDRkZmJmMDk1ZjlhNDIxZmFlNjRhOTY2MjZiYWQ5NzQ2MjUyMDBlIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644580826\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1008344184 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008344184\", {\"maxDepth\":0})</script>\n"}}