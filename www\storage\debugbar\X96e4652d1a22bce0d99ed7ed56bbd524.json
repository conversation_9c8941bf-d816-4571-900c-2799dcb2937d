{"__meta": {"id": "X96e4652d1a22bce0d99ed7ed56bbd524", "datetime": "2025-06-06 21:55:33", "utime": **********.571765, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749246931.976489, "end": **********.571802, "duration": 1.5953128337860107, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749246931.976489, "relative_start": 0, "end": **********.348124, "relative_end": **********.348124, "duration": 1.3716349601745605, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.348147, "relative_start": 1.3716578483581543, "end": **********.571806, "relative_end": 4.0531158447265625e-06, "duration": 0.22365903854370117, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797864, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.015960000000000002, "accumulated_duration_str": "15.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.441424, "duration": 0.01059, "duration_str": "10.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.353}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.486103, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.353, "width_percent": 10.714}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5208912, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 77.068, "width_percent": 12.782}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.54569, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.85, "width_percent": 10.15}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-9244976 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-9244976\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1230924019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1230924019\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1980914459 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980914459\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwj%7C0%7C1960; _clsk=v1wjex%7C1749242089260%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkMybHorS3FJZGNuQzZzTmhVdTFrTWc9PSIsInZhbHVlIjoiT3dlQTAvV2lRWVJraE96WTNoMU9KdFp0RjZpOXJvckVPREVTT2IvZWd4Sk1jV0Z2OWtlY01OcmdJUU0zdGE1SHdsTGdObTd4K0RKa29UbVQzMXVCMkk2UGxqdm1FOFB3WmlVTjJNVVBnenBMa1dqVlZDQzhZakY4ZGxYclNZMFJqVDMrcWtEazlrTnFDd0Fpb3RBL1NURUhNYVdqcVU4YWd1R2k5TWFhcURBaFp2aUFFMXdVczNKMGg5bVd5dGVLeGwxdi92SVB0Y0JpK0hMZjdJblBzQWNEU0tPREdLVjNkeGwwZWE0a3pueVN6RU1CL3lOQkZGY0hxMEI1T2Z5aXB2SnpFMTQrdzkrV3VBeXgrUE5acndwRzl4RTA4QkNTTlJCZVplbVpjemJiTFZhTGdGWi9HZCszcjV3c2hQZWhycjFTd3RVaFJTMzg2WlZoWXZvbHZtYVJEMlkxaVRnZk9neUFHNXgxcGppdTBTNWw0YTBRVjRlQXFhN25LdUZUdjB2M2grVy9VTW5oQTBKcXV1dDNZamRHbXNZRHQwaEFEWTZ3OVV2a1RPeGo2b2kxNEMrV2VWckdIUS9aZVhSVzBpcndGZnhDVEh5Smo0Y09KOVNibyszZkxXZVp1dFFzMENHVzViVGhaMFJzcDdUWXBMc0t0blh6eWNyOVhKTVoiLCJtYWMiOiJhMGJjYjg0NGJlN2E5MDI3ZTI2OGM4MmViZDg0MjIzOTY3NTBiYWU5YThiNjhhNjY3NjUwYzVjM2JlMTZjMTliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtHRmx3WlBWVkJoNUR1eDcxVC9yMWc9PSIsInZhbHVlIjoiSjZ3alIwSkhSYU9yaEdoclV5UUpBWUFQREx5WU1ZbC9mVzVZYnQ5bU9Fdk82b1N5UUFQREhnSWU2Tk43WXd6UHRDekhOOUVTVjc5L3FXdklEYjQwODhYL0s1NkdjV01jcHMzYTdTSFNjTTgxZEFrMHlZZDNZeldFRVV2dGxUbEJ5WUk2UHppUjJnR3ZhOHJOalBVWlhZeWR0Y0JLUnBrcFhqZkRweXpjaUlVckdYTnFBRjNhS3lYNktXMEl3NVJPYjNkZUZjcTlwODVVczc2RGkyNUFCSnNnb01JR1FMbGx5Sm9QOHRGNHArMXFwREROdUtlSmRXZXdIYTZzU09iSUE1MHVXbzlPMmNnNnFqeFh5bml0Tm9OSy9LZjZyWmpkQXRSSnZnRjI4R0ZvbDE3czZjUndUWEVheWtweDNSa0dNKzVYblB4U0RCcW1yQXl0bEFVSmRYc1I2MmprZUV1MktjYk9McDNXT0Z6eFRWUzFFL25oNkd2TjlQdGtRaFg1SjZTdVd0b2I1R1Jsd3htUG9nUTlWcXFGWG9lWnV1YW1lZ1RBWDJxRHJDK2ZuMWdUQUlFRjZSclVRektYN0JJYjgwOVF5M3cyVmpxczJmQ1dXTXZ6dGcxeUV1NS9tdjBZdi83L1FFZ1JGYitIdkRZclBlQXNwUStvQmt6UDF1N0YiLCJtYWMiOiIyZmI3MTRlOWZhMzA1NjUyZDg2Yjc2MjYwZmVkMDZhZWRmMGY1YzhhMjQ3MzYyMmU5NTBjNzcyZjVjN2QwNGY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hE2sfajo5A8qKfU8aNxLgaH66sUWlFc2M9YW325j</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-593826883 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 21:55:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNWOG0zdmY5TlliUVdEaHUyNWVmY3c9PSIsInZhbHVlIjoiclBOcXJIOHErL2YzMDB2WWoreUI1UU5WS1pXZWt1MGJFb1RTbnNvSWZzdkh1Ri9YTVh1TTYrQ3pEa0dWVVYyeUZtb2d6TU9lcEZveUZnUEVrd2dPUXNaa1RIMlBodUZOd2NWVkFvY1kyck4yZWUrcGNqVUtSWWt5VXJuZ0g1T3RSMm96RHlyTTdBODVON0hpTFdpeFZSV1hVR2tOR0JtdzlxYkoxdFROZXJGaC9jNVl4ZVAyaTNWYWJHVHV0a05Sc1BmTFNvaTVJcnd4bjVtVGpqTTVRd0d2RmRZTWdIYjhac1I1Zlg4b0JXSGFaSGthbG14eDlxUHhkaWpxY1BEYlB6OHk1ZWlBWTZFVUxIcjR1Qk5mS0lxWVhKaFZBNWYxc1B5cVBEM0JLWDRWWUgzT0hlRmJjOHhoeStmVkswZSt5LzkxMWZrTzVYcGttTUc2NkVpZXdXZUEremxSanJLSS9yTGdQditMUHcxc01qbjJpNi9oVHRsc3RhWTJibjA0ZGd1MVQxbGtPSGVudmJiSjZEdkZLSnBjYzNnU1FydkxKMk1GdDBUbjJ4MnpBUEtaVWxjV3p2dXhaM3FSdkxLT2FwcktjV2hPTTkxZnoxYVZSWndYRCtzS0NHZ2FaV3Z1UzFNcmxPNTZzc2ZXcUlEREs5MFpOVEtRdng1NlpWY2EiLCJtYWMiOiIwZDYxMzNhOGQ3NzM4ZmRhY2I2ZGMxOWYxMzU3MjkzZjI2ZjkyNjJlYTU4YjE4N2U4ZjM3ZWY4MTQ4ZGZmM2M3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlFaHkyQkhxcHFQVmRSM3V2cTEyZGc9PSIsInZhbHVlIjoicG1GSDJyQTRpM3Y2ZlVTOFBVei93RUZwTmxrMVRnQmhkUVhSRVZsSVczbVZ3Tndid3Y5M2g2UUFaYncvY0pEMUp4eklocFBUMXA2ckVaMGZFWldmU3lTZHJrODNmRU94aWU2VnVWbnpnbk1JT1hpMTVmRGhoa0lGOEFwaURTUnd3NDZ3enhTR0RRNStwTGN5d3ZQN0tUbkdyNTJ2ZWhxeUpsYXVKcGtJVllTTndrSjN3ekhTWkJwOHliWWdjSTVhd1IvU3ZtNWt4OTRpeGhkbWpCZWVNZTBQNFQwOENOS0t5YUJNZXd1a05HLzAzazJyejAvaGRkTkV2NG4vR3RwQWxpWmMzOENZRElGbFBNREI4SmJES0lFaU5sK09VekVSVE9ZU01GZVVYYTJKdUpJb2pKN25vZzJKdytBZ3U0Qzc4RjFzbjh3S041UTdRb0UzYnJOUEk1RSt4SkxoazJORmJQZWdsZEx2c2NWT2hRcHEzZzM5K25zNVNTQzBmdnp2ODR3TWNkUHRkMXVRVCtDWk5taFNoRHIxbE1pVCtCa0dzYW8zd0xPbThWUWlUTWtDems4ZFJQSEZacE9RTy9ZbUpMSTBVQkhUemlpNVNyYmVYYklvcm52RFo3Rkpybk9JS1U0c0ZTL2pLd0VtQ3pqTEk2WUNsYTBQQ0JLTERLVEsiLCJtYWMiOiJkZWQ4YzNjYWMwOTc4MDg5NDQ3ZDU4MWZmZGM2Mjg0MmE3ZGZhNDI4ZjU2M2RiYTE3ZmM1N2IyZGYyNTdkODBmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 23:55:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNWOG0zdmY5TlliUVdEaHUyNWVmY3c9PSIsInZhbHVlIjoiclBOcXJIOHErL2YzMDB2WWoreUI1UU5WS1pXZWt1MGJFb1RTbnNvSWZzdkh1Ri9YTVh1TTYrQ3pEa0dWVVYyeUZtb2d6TU9lcEZveUZnUEVrd2dPUXNaa1RIMlBodUZOd2NWVkFvY1kyck4yZWUrcGNqVUtSWWt5VXJuZ0g1T3RSMm96RHlyTTdBODVON0hpTFdpeFZSV1hVR2tOR0JtdzlxYkoxdFROZXJGaC9jNVl4ZVAyaTNWYWJHVHV0a05Sc1BmTFNvaTVJcnd4bjVtVGpqTTVRd0d2RmRZTWdIYjhac1I1Zlg4b0JXSGFaSGthbG14eDlxUHhkaWpxY1BEYlB6OHk1ZWlBWTZFVUxIcjR1Qk5mS0lxWVhKaFZBNWYxc1B5cVBEM0JLWDRWWUgzT0hlRmJjOHhoeStmVkswZSt5LzkxMWZrTzVYcGttTUc2NkVpZXdXZUEremxSanJLSS9yTGdQditMUHcxc01qbjJpNi9oVHRsc3RhWTJibjA0ZGd1MVQxbGtPSGVudmJiSjZEdkZLSnBjYzNnU1FydkxKMk1GdDBUbjJ4MnpBUEtaVWxjV3p2dXhaM3FSdkxLT2FwcktjV2hPTTkxZnoxYVZSWndYRCtzS0NHZ2FaV3Z1UzFNcmxPNTZzc2ZXcUlEREs5MFpOVEtRdng1NlpWY2EiLCJtYWMiOiIwZDYxMzNhOGQ3NzM4ZmRhY2I2ZGMxOWYxMzU3MjkzZjI2ZjkyNjJlYTU4YjE4N2U4ZjM3ZWY4MTQ4ZGZmM2M3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlFaHkyQkhxcHFQVmRSM3V2cTEyZGc9PSIsInZhbHVlIjoicG1GSDJyQTRpM3Y2ZlVTOFBVei93RUZwTmxrMVRnQmhkUVhSRVZsSVczbVZ3Tndid3Y5M2g2UUFaYncvY0pEMUp4eklocFBUMXA2ckVaMGZFWldmU3lTZHJrODNmRU94aWU2VnVWbnpnbk1JT1hpMTVmRGhoa0lGOEFwaURTUnd3NDZ3enhTR0RRNStwTGN5d3ZQN0tUbkdyNTJ2ZWhxeUpsYXVKcGtJVllTTndrSjN3ekhTWkJwOHliWWdjSTVhd1IvU3ZtNWt4OTRpeGhkbWpCZWVNZTBQNFQwOENOS0t5YUJNZXd1a05HLzAzazJyejAvaGRkTkV2NG4vR3RwQWxpWmMzOENZRElGbFBNREI4SmJES0lFaU5sK09VekVSVE9ZU01GZVVYYTJKdUpJb2pKN25vZzJKdytBZ3U0Qzc4RjFzbjh3S041UTdRb0UzYnJOUEk1RSt4SkxoazJORmJQZWdsZEx2c2NWT2hRcHEzZzM5K25zNVNTQzBmdnp2ODR3TWNkUHRkMXVRVCtDWk5taFNoRHIxbE1pVCtCa0dzYW8zd0xPbThWUWlUTWtDems4ZFJQSEZacE9RTy9ZbUpMSTBVQkhUemlpNVNyYmVYYklvcm52RFo3Rkpybk9JS1U0c0ZTL2pLd0VtQ3pqTEk2WUNsYTBQQ0JLTERLVEsiLCJtYWMiOiJkZWQ4YzNjYWMwOTc4MDg5NDQ3ZDU4MWZmZGM2Mjg0MmE3ZGZhNDI4ZjU2M2RiYTE3ZmM1N2IyZGYyNTdkODBmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 23:55:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593826883\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wS4eS5WQbmWAioLZmC3utlwnHP1ILsn3yZv97JLr</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}