{"__meta": {"id": "X53db7384a933af38bd87b9f2a3754a67", "datetime": "2025-06-06 19:13:07", "utime": **********.865099, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.522744, "end": **********.865134, "duration": 1.****************, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": **********.522744, "relative_start": 0, "end": **********.612043, "relative_end": **********.612043, "duration": 1.***************, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.612063, "relative_start": 1.****************, "end": **********.865137, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "253ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.033960000000000004, "accumulated_duration_str": "33.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.707423, "duration": 0.02264, "duration_str": "22.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.667}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7577631, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.667, "width_percent": 3.916}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.830801, "duration": 0.00999, "duration_str": "9.99ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 70.583, "width_percent": 29.417}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxuaUpiSGZsMjFvb25LQitXaFJ3MlE9PSIsInZhbHVlIjoicmlCckZLWERYTFYxMVZJR3B6Y1UreG5oTnRjTDREQ1JJS3M2SHRkNHpKKy9xZTB5U0tMVVQzK01yemdSY2pXWFY5VjhRY1hiOGpNYkEwRnp5WGRjS2xWby9iOGpKRGNlR3djUmVhZ2R3dW1ZVFNmNnpRZ1U3bkRtNWxpc3dFYXlZQkRlMFkrbWZsSHRNNkJhb0RwOEx0QTV4Ynd5VFdHQXVyQ1FPWHFCTElKN0RLS1NzbHN4U0dZdWcya0EzU21nb3hLRGZtaGRKTndqVGtsWWxPZWVIT2h5elVwYmJLOEZ5TDlSSzZ2UUZQcUt3ZStUSUl4L3Z1ak9iTHVvLzRXVDc1MENBdTFwdnZLU3RIT2dmNi9YdDlmb21uNkkvdHVuYVVYQi9rNGxJSG9lMVhLSFVWSE4ybndpVUtCSjFIVlI1dlVSRlZsK1ppMExUTWR4Y2tCN0p2enZSWnZnUExCWlBiVHU3TjZpTWxGQ3JQeXhZRDVDNzRVcXdaNldKd1huU3c2QXdUbk4wYzJ4ZHlqTTljZy9NZjhaNzcwajhNRHJHeVh6VyttUTJoUDZRMWxuVml2YUlXckhrNVUwMVM2dlJWRDVwNjdZZGVoRFJZSUVFaXJ2bEttaXlKVFVlcnZSZmZsQlNTZ2R3RVJ1NnUvK01RalNrWnQ4QkowZjlOSGQiLCJtYWMiOiJjY2E1MjhiZmYzMmI2ZjQxMGQzNDRjZjYyM2NjZWM4YTkzM2YxZjA5OGM1Y2I5OGY0M2U4MWQ0NDE4OWI4NDI2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBZMzRlYUJ5ai9ZdmNDRm1OMXl5eHc9PSIsInZhbHVlIjoiRUVqVENNMGhYbll1bUpDRVU0MzlWZ3ZlNG1jWWx5MU41MmtQZFhzd1ZsSzhYYzN0bU5QRm5SbCttTnU1UllzNmR4RnU5LytVTi94QUNWd2VrNnpaNExia2RrY3VzeExqSFNSZk1Rb1JLNU9rOFBvWkNqVjlJMDk1WkFkeUh1TGVsM2pkMnpvN2dTQUE4eU5TK0FVbHF1NTM0QVRubW01QzdHcnRqa0IyOW5hNDlZdk1SUkswV2Mva05GUkNGRzJydlBBQmRYcWdkTEJocFcycks5ZEpVV0txOEJpa2dqZHdZeFhMa1VzVE5CTi9lNjkzQTlVcmJmajNYREtNUTVocUliQTNKcEkrMFZ1a0w3Z2orQnJXazcxeDBsRHF0d0EvanpQQ0VBNGpaMHJubkZaK251ZTVRaWpuUmFkKzJkVzFrNlRtK1Fqa05CZkoyaUdPbWVZcmVIMy9UQzdCSUdSTjRoYS82R0JHdE1oV3ZNS1J0S3Z0S3EwZEh0MjJUSkxTTzhPc3k0a2trWWlpSzZVV0JBL1Bzakp4aGYyOHJqaStmWkw0aGhTdWVFUWFIZ043UDBaZ3N1S3ozN1RESWRVcERJV1NUbE5yeHQ1clRSQ0dpSnpUY2pvYy9oa09jQUZxRW9lOTVFeHcrQWxteVl1Nk9UbmJoUXI4dEhuQWZiV3AiLCJtYWMiOiI3OGJjZWNjYTg2OGJiOWI4Y2MyYzhjMDJmM2YxOTlmMTRlMTMwMDBlMjc5NzFmOWJhYTEwOTdhZjBiNDQ5ZjdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1293469241 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PVt0h9icZb5VqFqziyPToWE7TqW2lYjvh7uvnOnw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293469241\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1333855156 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdqakVJMjNiaTN1bUxLcDhITGl6bFE9PSIsInZhbHVlIjoiSVlVVzVROFU3aExvT3BMVEFGdE1QTlBXdnhXd0w2c0ZZeTBwRnVOcGpTWGZJdXlsTnp0YW5zQ2VIZ0dOZ3hKM1dqUEVNZm9lV2U1Ylo4ZkhYQTVHWDBSa2Fsb2dZOUxOZDY4ajVjZU05NWhSUHJNaXQ2SzE4RjFwdG8wRnlJZENMSFNKNEU2Zi9pVXdMelRORVhMU0hxK0xoSEpZR2RuN1Vwdlo4VU9kSmJUbVlpL09kWDQ0UU8rZVBDNm1LVXF5bExCV1dSTm81VkNKZUlvM0p1T3Vyb2NmRDg1OHdUOS9BOERPb01SWGM0UkxTemU4MjFzUitabS9DclpyS01pMUZxRGhlVGFxYXd4VlVWVUxNVUJXTmdDUWpMUHBnUmwxaUdHTWlXT3FYd09pR3ZRY3VKa29SU0IxZ3grMFFWRzFEK3dGYTJvQlplb0FKb1ZhUEVRandpZ1BsbDBjdVIyRVJlOVFhaFlGZkIwS3U3ZTQ0QUwxTEYyUGRqYXpESFVKdlV5ZzYyZGtEOTdaYTlRa0dqSFBGdENGQndmaUsrcm9pUHB2ZytJblpEcTBFQU9JcHBqTTN5amNtRUZsU01ma3AyVG41TFB3YjdTOTdjNUR2Um55eDFtVjY2V2ZUenpJVFdlY2gxQ1UvTGhvZGpIZ21DWG82Z1lXVXNsVHQ1UVoiLCJtYWMiOiJlOTM0ODA3NTM4NzAwNDE1ZjYwMWIxOTEzNjUxZTVjOTQyZjhmODk4NGIzZjE0NTk5N2RjOWQ0OTY3M2U3ZmUzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ild0Slp2TU5KZitLbkJsYVNnclNwaVE9PSIsInZhbHVlIjoiK1Vqcm9vazZhc0s1Y2xIMDZ5MlpQWDd3NEZVTk0rU1lWeWtiS3Q2aytDWlFsOW00eVdvSTczVFFpdjNsdGRJWktrcW5kTDFISmxwRURhaEtacXE3V3ZicGF0YktDQndmaFBOSEZHYU1UcUU3aWRMYWcwYll4NnYzWFR6c0Z5TFJEbW1mTVVzMXR2SGprWmZERW8ySVJDTDZCeUdPR0lvYS9DUVZtUmswRzVmM0I5eFlUWUpOWW5FSjgxcjV0U2pmWjBJMVU5RHhZVGM1OWVma1lmd0FVK2hxTHovWUJwa3RXNkdweEdEb1hoNHJ0bHI0MUJkMU1OcXRMNGRjRTZjTnk4ZS80TjFTam8xczM4UlcvY3hERDNZSEMrc3ZINW9NcXNkTEpET1UrVmFZb1R4Z3llbDFaMXZNZFQ3TkFRMzBJREFyR2VRREVjR3BQM3ZjNnlSdE52OHd4U1lEUCtncGpJTkRNN0pxbGJOTE1WcDJWSmR1UkJJSVlRT0ZONnN6S1JDNHkwcGk5UVVlK1dEdzBIaUprZ0YwS0RBK1FOVFJscFlxQXFQQkFlWTQ2Ym1tS1pJSlJjbWV2TzU1ZDFTRHlWL2U1UTlYbGN2Znp3K3dJdTdjVnQxL0VrYUdrbXF4UHdmQ0ZmTitENXZiVnhxRXJTSGpDZEE3UDRpYnFmSGYiLCJtYWMiOiJkMjdhNDczZjk4NGZhNzRjNjZmNDczNWIwNGM3OTI5NzEyNDZmNWQzMmM5ZjlmZjUyN2UzMWEzZDRiN2NlYzg3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdqakVJMjNiaTN1bUxLcDhITGl6bFE9PSIsInZhbHVlIjoiSVlVVzVROFU3aExvT3BMVEFGdE1QTlBXdnhXd0w2c0ZZeTBwRnVOcGpTWGZJdXlsTnp0YW5zQ2VIZ0dOZ3hKM1dqUEVNZm9lV2U1Ylo4ZkhYQTVHWDBSa2Fsb2dZOUxOZDY4ajVjZU05NWhSUHJNaXQ2SzE4RjFwdG8wRnlJZENMSFNKNEU2Zi9pVXdMelRORVhMU0hxK0xoSEpZR2RuN1Vwdlo4VU9kSmJUbVlpL09kWDQ0UU8rZVBDNm1LVXF5bExCV1dSTm81VkNKZUlvM0p1T3Vyb2NmRDg1OHdUOS9BOERPb01SWGM0UkxTemU4MjFzUitabS9DclpyS01pMUZxRGhlVGFxYXd4VlVWVUxNVUJXTmdDUWpMUHBnUmwxaUdHTWlXT3FYd09pR3ZRY3VKa29SU0IxZ3grMFFWRzFEK3dGYTJvQlplb0FKb1ZhUEVRandpZ1BsbDBjdVIyRVJlOVFhaFlGZkIwS3U3ZTQ0QUwxTEYyUGRqYXpESFVKdlV5ZzYyZGtEOTdaYTlRa0dqSFBGdENGQndmaUsrcm9pUHB2ZytJblpEcTBFQU9JcHBqTTN5amNtRUZsU01ma3AyVG41TFB3YjdTOTdjNUR2Um55eDFtVjY2V2ZUenpJVFdlY2gxQ1UvTGhvZGpIZ21DWG82Z1lXVXNsVHQ1UVoiLCJtYWMiOiJlOTM0ODA3NTM4NzAwNDE1ZjYwMWIxOTEzNjUxZTVjOTQyZjhmODk4NGIzZjE0NTk5N2RjOWQ0OTY3M2U3ZmUzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ild0Slp2TU5KZitLbkJsYVNnclNwaVE9PSIsInZhbHVlIjoiK1Vqcm9vazZhc0s1Y2xIMDZ5MlpQWDd3NEZVTk0rU1lWeWtiS3Q2aytDWlFsOW00eVdvSTczVFFpdjNsdGRJWktrcW5kTDFISmxwRURhaEtacXE3V3ZicGF0YktDQndmaFBOSEZHYU1UcUU3aWRMYWcwYll4NnYzWFR6c0Z5TFJEbW1mTVVzMXR2SGprWmZERW8ySVJDTDZCeUdPR0lvYS9DUVZtUmswRzVmM0I5eFlUWUpOWW5FSjgxcjV0U2pmWjBJMVU5RHhZVGM1OWVma1lmd0FVK2hxTHovWUJwa3RXNkdweEdEb1hoNHJ0bHI0MUJkMU1OcXRMNGRjRTZjTnk4ZS80TjFTam8xczM4UlcvY3hERDNZSEMrc3ZINW9NcXNkTEpET1UrVmFZb1R4Z3llbDFaMXZNZFQ3TkFRMzBJREFyR2VRREVjR3BQM3ZjNnlSdE52OHd4U1lEUCtncGpJTkRNN0pxbGJOTE1WcDJWSmR1UkJJSVlRT0ZONnN6S1JDNHkwcGk5UVVlK1dEdzBIaUprZ0YwS0RBK1FOVFJscFlxQXFQQkFlWTQ2Ym1tS1pJSlJjbWV2TzU1ZDFTRHlWL2U1UTlYbGN2Znp3K3dJdTdjVnQxL0VrYUdrbXF4UHdmQ0ZmTitENXZiVnhxRXJTSGpDZEE3UDRpYnFmSGYiLCJtYWMiOiJkMjdhNDczZjk4NGZhNzRjNjZmNDczNWIwNGM3OTI5NzEyNDZmNWQzMmM5ZjlmZjUyN2UzMWEzZDRiN2NlYzg3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333855156\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-8******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8********\", {\"maxDepth\":0})</script>\n"}}