{"__meta": {"id": "X519e26f4313a7f226e56baee563ffb47", "datetime": "2025-06-06 19:18:26", "utime": **********.450303, "method": "GET", "uri": "/users/14/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237504.869184, "end": **********.450375, "duration": 1.581191062927246, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1749237504.869184, "relative_start": 0, "end": **********.296803, "relative_end": **********.296803, "duration": 1.4276189804077148, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.296825, "relative_start": 1.4276409149169922, "end": **********.450379, "relative_end": 3.814697265625e-06, "duration": 0.15355396270751953, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43510240, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00616, "accumulated_duration_str": "6.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3978171, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.305}, {"sql": "select * from `users` where `users`.`id` = '14' limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.415212, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 82.305, "width_percent": 17.695}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/14/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/14/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1812823485 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1812823485\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-76656182 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-76656182\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1172718210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1172718210\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1310295459 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237503349%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpteG5NOThzRVVWenJVMEF4bElRSWc9PSIsInZhbHVlIjoiU3NndEFRLzFvMGsxSVg2b0VvSTRYODZLRTdCWWxqdmk4cDFkcnpLN21uOEF6UVFMbWNjL1kxa2Z4aTVYOEJWN2Nja1BZWmVxUi9PSEdHb0F6c2ptMUVVVW9VN1ZzVHgxQnRMOFowWHBpU1ZFSGx6OFc5Z0hneTNDbHRVcXhvTXd5QVdmeVFmcEgrUjdIZ2VoWTE0UGNDRDNyZU1ZQkYzMnM1endDM01nakJiTkltazlPcVJLYzFEUWN3bGd3TmU3UXhJUHRTOWx1TjIrTG9obmJOOWpoQVhVc1B6TUo5MUJCaVNDaEhMb0duaDE2eFdNVmx1NEdJeXRSS1o2K2hieUE4WDMzcUhPTWErM2dzWXFLUlFPM3o5cnRzN2t6bzlIWTRWWlVkejZGbXRyeXZuL1FzdVpHS1UxL25BVGhTUEtpMDNTYm9Cbmc3WkVZRTRBTkZmVGlMK2hKWUhDYkw4YUw4REVNTnhjU2hhVHgyZnRyQ0R6T1N6a3J3Qzl5OE12aXNvSUxOUlhOVHFTLzR0WUxvNW1FT1pZTWl1Qy9acWM2Z3BILyt3N1hnVmc2SFZxV2ltT2JsdGZMbElJUVJvblpYWVZ3TzZXUGIyYTdhaCsxVmU0eVJocVEvQTlMTksvZ0VibVFkZVJkaWhocktHK3dXekQ1cFZRRUhYUVZCQk4iLCJtYWMiOiI0ZjllYmZmYjUwMDE3YmM2N2E0NTBhOWM0NTQ5NWNhOWM2ZjViYWYzOWI4ZDAwYzQzNzYzZjE0MThiODcyZDk3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InM0SWZPNWNhU0s5M1h1cFdEa2p4T0E9PSIsInZhbHVlIjoidHNHNkZGaExsbVlLcnJIYSs2cTd1VnpWQVBiRnZEcFhsZXpWb3BEcStPZWtXWlVXQXU3TnpWeWZmTmhvenVIZzBPditpblpXRmZHako5TE9RTXVqVlh6a1dvYnd4NkYwdFVtbkIzZ2lNUnJQWVZpMElid2JEY0MrYmJNQTNFcXBaeW5wSER4YjZrdXBlYXZaMEh5NUZRL2xMRW1QQUNVSEVEUmRqM2ltYzQvcHNZanFhWElvNDJENWpIQXB2VS9BNTNGZlE5ekdsOXZzVkwvU0d2OWFVNTVCUGE2SHo5OXhSQ2NRK09rZjVteE5CMjJseVdxNWhYeE1ROVBzZWtPVG9FbW0rRmkzeU1BTVJOYUNoWTI5ZWNnck9OMmF0RXowNlM2V1NwOXZubzJUdldCQXBXVVB6eUFKc29xY1NTZ3RFUmJhVzNzRUxXOFNXR2VTWUJWaTZlNlpKSmx0Tnh2ODQyOUxVOFlZd0RubUovT2h5OWd3Y1owRVBMSW5RWUFYMWFYT24vVzBrVlpRbU1XcDh5aXVUa0pjR0p6RnI3ekVCMzJMalFKZUQyYTFQUzVnakRucWJKamVuQW4wUW55VDdpUlJjTHkwT0d2djExVXdMV2gxY3Z3TVArVm03QlFlMDVkR1M4cnA0cUU1MFJCQXVxeDRhNFNkY3VkaHg3ZGsiLCJtYWMiOiI1NzdkNTkxMjVlYTU0ZGZkMjMyYjM4OGFkMTNmZTk4MmM1NzIxMjdkYmZlOWRiMjY5YmU4MjhjN2NhNDkzMWMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310295459\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1784543540 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">07zLQ1IzhcCwivgYmqVlKD2YgfwQ5DGdasvfNucP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784543540\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-911723745 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:18:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InczQVZ3YTRFMDJhT3dxYWxxVkJ0dkE9PSIsInZhbHVlIjoiNTI5Rk1FRzAyMGdEL3pCVzRuUmxXbFZoNEY2WWxWN0xJZTU5ckxjVHVBQmFkaU1rSUl1WHlzMWxHNkMxY2N3am1xdHdtbFpOVDFSeVEvVjJsSGtmYmt6V2oxSzhRK0d0ZXRVcXdQWHdZNUYvK2pGZEU5ZWhUUlRIQUtsczZENzRRN1BUSUJuUmJFci90Y3ArM3lvcDVMS01NZ2w3VWxUUDlQMjErbmFYRXBVY0w3Z2hWZ2xPMTk5cXR3NzBMRmU0ZDBtMnRUY3BnOW45NWE1dWNKNmJleTdFS2pXRTI5M3NYeDVqYmV3Nk1USm9RRXFYQ2Y1THZOMFJuUEpMZlBicWJQS1RmeVBUeWkzNC93STExMVJ4bG8wQzNHMDYrbDZwejhvR0VrQUdqaEdudVllYmNUb0dOYWpHRE9MWmdsN1pxS2pQcXpLaEJOL3hoaU5oazBUTXludXAxTnlycmRzaEhoeENkamhXMlppQjBjeklDVFFYZW5RWnltWWxjUlcreFh5c21ER3M0bDAvL0FZc3NHVE5jbmJZbzUycklxSW0vRXlqQnJBWWlKQStRTjNrcm1CUVlMM2ZFZDk5OFZkcTByWThZYzN0YmVYMitIUHFqMlEwTlBjd2ErMkg3cXBNaEdyN0JySTNWOXduSkdoblR4NTNudm8ySnlNNzhRa0MiLCJtYWMiOiI0ZWQ3YTE2N2ZjYzhlODU4NjQ4NTFlYWFlZDY4MDNkYjhkNzMyMTExNzcyMzlmOGU5ZTViNzVjYTAwM2FiMzIxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlIzRE9wU1NNeHBFZW1BcXpjRERIWkE9PSIsInZhbHVlIjoicEFPKzJBdDQ0TEZoakdjbTFBUm56TVBBa0ZtRFNGUEM0dlhKQmdqSFdMTFQ3ZER0WmRnbXdSUERDaU4xU2VETVh3dTdROStUS0NkVG9PdXZvVkRYWEFUOC9JcFBnWGd1b05ZNzUvRDg3cDVXa2Q5ZnBpN0k5NkZtR0ZvL2FEdUZldGtyWVptVThIZm04Z0gvNVZ2YktYdTdEUVFZNjFOKzI1b1NLaE40c29vTll0ZW1DZkhHUzVhclpGcXlTMURBVGpyWHQ3azBvS1RpUTlEQnNqYU1VeGJOYmFDZzVtVUhteEwzeHJLc1FQY0Z4N1RNVStKeU10T0tCUUJsZlhZZFQzME9KTGhaUHZLK3RjSzlUdDg0NGxqY1FZbDZjYUdrQXZYSlBWRksrVFN5Ykk1bTVTUCtlU0tZTGtad01ibUNCbXNvRWhZMDkyT3pFdlowV1JLRmhpQVdTVlZpUGpCQnNDTTdmMElkOU9LR3c1aVVQdE9RZ0lYRURXQ2s0TTNJL1ltbGZnd1pZWW9UZC9tam1vRTV3R21SaGg2NlFJS08zNmwrMjI0akllOFBLbENFMitQU09HU3FSNnlSUmxwT1g5czd6TndHUUI5bWFvVytBVXNDNGx4Mk5uQ0x0dlBqdDBXaVVoT3h2ZW4vcThJNDlJV0pQZ0Q5aFVWTW1EeFIiLCJtYWMiOiIyN2IwNTlkOWRhZDc1MWFmNmNhNzhmZDY1ZWRiYjg5NmFkM2UzMTViYjExMDM4M2U0NzYyMGFiODcxZDlhNGE3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InczQVZ3YTRFMDJhT3dxYWxxVkJ0dkE9PSIsInZhbHVlIjoiNTI5Rk1FRzAyMGdEL3pCVzRuUmxXbFZoNEY2WWxWN0xJZTU5ckxjVHVBQmFkaU1rSUl1WHlzMWxHNkMxY2N3am1xdHdtbFpOVDFSeVEvVjJsSGtmYmt6V2oxSzhRK0d0ZXRVcXdQWHdZNUYvK2pGZEU5ZWhUUlRIQUtsczZENzRRN1BUSUJuUmJFci90Y3ArM3lvcDVMS01NZ2w3VWxUUDlQMjErbmFYRXBVY0w3Z2hWZ2xPMTk5cXR3NzBMRmU0ZDBtMnRUY3BnOW45NWE1dWNKNmJleTdFS2pXRTI5M3NYeDVqYmV3Nk1USm9RRXFYQ2Y1THZOMFJuUEpMZlBicWJQS1RmeVBUeWkzNC93STExMVJ4bG8wQzNHMDYrbDZwejhvR0VrQUdqaEdudVllYmNUb0dOYWpHRE9MWmdsN1pxS2pQcXpLaEJOL3hoaU5oazBUTXludXAxTnlycmRzaEhoeENkamhXMlppQjBjeklDVFFYZW5RWnltWWxjUlcreFh5c21ER3M0bDAvL0FZc3NHVE5jbmJZbzUycklxSW0vRXlqQnJBWWlKQStRTjNrcm1CUVlMM2ZFZDk5OFZkcTByWThZYzN0YmVYMitIUHFqMlEwTlBjd2ErMkg3cXBNaEdyN0JySTNWOXduSkdoblR4NTNudm8ySnlNNzhRa0MiLCJtYWMiOiI0ZWQ3YTE2N2ZjYzhlODU4NjQ4NTFlYWFlZDY4MDNkYjhkNzMyMTExNzcyMzlmOGU5ZTViNzVjYTAwM2FiMzIxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlIzRE9wU1NNeHBFZW1BcXpjRERIWkE9PSIsInZhbHVlIjoicEFPKzJBdDQ0TEZoakdjbTFBUm56TVBBa0ZtRFNGUEM0dlhKQmdqSFdMTFQ3ZER0WmRnbXdSUERDaU4xU2VETVh3dTdROStUS0NkVG9PdXZvVkRYWEFUOC9JcFBnWGd1b05ZNzUvRDg3cDVXa2Q5ZnBpN0k5NkZtR0ZvL2FEdUZldGtyWVptVThIZm04Z0gvNVZ2YktYdTdEUVFZNjFOKzI1b1NLaE40c29vTll0ZW1DZkhHUzVhclpGcXlTMURBVGpyWHQ3azBvS1RpUTlEQnNqYU1VeGJOYmFDZzVtVUhteEwzeHJLc1FQY0Z4N1RNVStKeU10T0tCUUJsZlhZZFQzME9KTGhaUHZLK3RjSzlUdDg0NGxqY1FZbDZjYUdrQXZYSlBWRksrVFN5Ykk1bTVTUCtlU0tZTGtad01ibUNCbXNvRWhZMDkyT3pFdlowV1JLRmhpQVdTVlZpUGpCQnNDTTdmMElkOU9LR3c1aVVQdE9RZ0lYRURXQ2s0TTNJL1ltbGZnd1pZWW9UZC9tam1vRTV3R21SaGg2NlFJS08zNmwrMjI0akllOFBLbENFMitQU09HU3FSNnlSUmxwT1g5czd6TndHUUI5bWFvVytBVXNDNGx4Mk5uQ0x0dlBqdDBXaVVoT3h2ZW4vcThJNDlJV0pQZ0Q5aFVWTW1EeFIiLCJtYWMiOiIyN2IwNTlkOWRhZDc1MWFmNmNhNzhmZDY1ZWRiYjg5NmFkM2UzMTViYjExMDM4M2U0NzYyMGFiODcxZDlhNGE3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911723745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1826106426 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/14/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826106426\", {\"maxDepth\":0})</script>\n"}}