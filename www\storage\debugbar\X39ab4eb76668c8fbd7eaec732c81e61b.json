{"__meta": {"id": "X39ab4eb76668c8fbd7eaec732c81e61b", "datetime": "2025-06-06 19:26:27", "utime": **********.518311, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237985.815071, "end": **********.518345, "duration": 1.7032740116119385, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1749237985.815071, "relative_start": 0, "end": **********.276737, "relative_end": **********.276737, "duration": 1.4616658687591553, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.276759, "relative_start": 1.4616878032684326, "end": **********.518348, "relative_end": 2.86102294921875e-06, "duration": 0.24158906936645508, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02033, "accumulated_duration_str": "20.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3785298, "duration": 0.016050000000000002, "duration_str": "16.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.947}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4287138, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.947, "width_percent": 5.509}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.468013, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 84.456, "width_percent": 8.264}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.495302, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.72, "width_percent": 7.28}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1112547736 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1112547736\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-982913899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-982913899\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-879795363 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879795363\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-453603933 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237936164%7C24%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJocDRtTjZZLzZWMWFBK3J5NmpnRkE9PSIsInZhbHVlIjoiM0hMRDVnTHRVcDUzdysxT2pWRmRnbk5OajBHK2VFSExVL2hzdmpwZmltL2h3V2JSREJ1WW1OOHRUSWE5eTlYN0hBTW5pS0c4RUVBcDNDeSt3YmpTYm9FOEw3OEkrUU9YUjJERnVWcWRIbDJsT1V2bnhGeGFCd2F5WE44QXlTaTQxQ3ZZUXJtLzhkZjlQeFRINWtnTGo2SEhWWmJna2xsTUgvamhUSlgxVTVzeTdHUWtmS0Z6dnFIY2NvSEVnKzRsSlFLNTYwUXNLZ3lNQVF4WG12QkxWOEhYQnJRRVlHWVh2N0oxczhVcWhQQTZleEE1NGZCRjhzWVNOWWFoZjVoa0dGTnB6ZlVDQnllTnRrV3BjdEFWcFNJdWtEekVVempxVFdIQnlUc2Y2Tkd6RWlXWUQzN1h5eU1VTms5NjdldFl2VnFtR2lEMENDK3pnWFJJSGE5QTlUalBBRTJva1VLZTBWK0hvQmFnZFRNYmYxNTBlVUl5L3Z6ZU11ZTFRdnBBc3lwN3Ywa2lnc3Y3aWIrQ1VYS3JsM2xpQ2FvTmhSdlRacjBIUlk0WXhibzU1U0tlRFNWL3RFWjh5Y1J0NW04U1hMRDA4Yk5ZMmRwRXY0R1FtTWJablRzaFdyR0liZFhiU242SEdtUFpOQkdsTDRpQ1YvRFdaTUFFS3pndEcrZkEiLCJtYWMiOiJjYjRjOGZmZjU4ZmNkMjJlNjFhMjE3NGQ1MjM4MzhmNmZlNTgxYjE0MzEwNTg4YWI3ZTcyNTY3ZDIwMGUyMWQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjRBTThnZEk1eERlOVUxOEFtaTNlU2c9PSIsInZhbHVlIjoiVnJyYk9Ob3U2ODJpeWVYcnpSUHhlQnZ2OXNWV2RiWVI0MkQ0Yys1UHBMbE5OdzA4OUhwNUtjRE5TZnozMGpPU2dzcDNPb3IvQlFIN3pZME9KdEd1TkNkOElBQW5aaVkrQXkybTkxOTM2NjZOeFBkYXFmdDdtTDBaR3UxenFncVFzeTZWRjhtU05FWjYwclpvczkycGdkN2R5OFJ2RU96cmFxQVdqRGdOb1c4a0tVYXBsNGlVWDJEUm9weVVnNTExK2lOdFRlVHYrTHBmUllOcjdSL0RVcERCMldacFI0TC9vdTVMUEMvQmlTQnZ0elhLa3piYmk0QkVldG9JZmZUQWZROU40RVZieTh3SjJOZmQvQjNPR0g3YkluYnRDTzROSzhZOFFLcng4VXNDazdJcWhEdy91cDN0dGVpVVJ0MFZGdnN0RU8razVoV21ndTFwZ3NjY3RyZUo1V2l1bkdrVUVYNVhOeFkvZWlPWVQ5ZkJiZStEdlBzSTdXQVhQOXZUQitNVlpFVEcwbVBlOHk5S0F1OWwxT3BTT3VBc01rZTVuY05QY3BwZURkWmlLRTdBME9oZml3WlZXYi80YVIwVU5XMEVTMytrU2IxWGVERmNONHpVUE5HbFF0d1FhdCt6dHk4UVdQWEVaQlJkZ0RmSnVIbFhYTnJwSWU0ZGs4QTciLCJtYWMiOiI3ZWU1ZmE5NWI0M2I4YzVmNzNhZDI5MGU4NjQzYzVjNzgwMmUwNWY1MGU2MTFkOWNmNGU2ZTg1NmZmNDhiYzVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453603933\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-543217755 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-543217755\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-355109189 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:26:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxtVGI0UG50SHJST3o4aEhucEJtUHc9PSIsInZhbHVlIjoiUVpWdVFBTGI3Rm8rV1BobnUwQW1lMXhHL2I1R3JsU1RJZjFmaFlQQmdyVUZOTGtwUEt0ZFA0NzlPcCtmTGxVYm5BZWVCU3c1QUhxODBlY3JRN3lXNzA3ckZsYjZDc1licXNzbUxlUWh1MVBzUXlTUHFpTnRnSnhldXNJYUNHQkZLdjI1UCtHM3hkMkxGR3lXT0RGVWRmdlhoQlA5R1RMZFNUTllZOFRKanNtQnVNVTBRM3ZiWTJJbUxwc1BCNVJrYWo4WDFRZmNMRHNTVjFFeWsvS3RZMm5NZkRnWmVkNDBMMEdGakhvNER5TThkS1IvZEx4QlZySlJXVWg3WXpwUVJ6MUZJMTJLRDl1bDdGN3FnYXVRTGpSbm8rZm0rekdMSTFGR2YrU2xDV0FMenV0V29mSFo2ZDZtcEY3bGw1dEZHS3k5NVlZcUtVL1hJUmdVZ1FLZ3lHQjA1N1RoOUpkcmxTaGRkRTFTYmlKNjErVVNrMTNTOE5PMk9FM1JyU0lxRW5NdTV2YzJXanZ2L2tpQ3RVQS9ia25xSEFtcFJ0TFpNOE43cWZXU1F0UmdxMWJKbXNUczFZNHRTeVREWC9CQUUxYkZDOVVWRk03YWZGa1RqYm5sUkdYc1hrU1BnNVY5T1JNTlZTbHFjc0NWUTdOTFpWTWVKTHM0NndOMzFLSzUiLCJtYWMiOiJiYzcxMTI3OTYzNzdhMDkwNDAxZjhkOTZmMDhjMTNmMjFlZjBmOTc2NWZkNWFiODA3ZWZlNjcxZDNjMmY4NzdmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVtMkJ1L0FjU0Q1aGtML2FmeW0weEE9PSIsInZhbHVlIjoiNytuaFlnaFljUGRKRS90RDI4azRRQ0dlK1VYN0pwVy81ZWlEYTJVeDBDaHNxKzEyWEZNK1B0MkVSZDZudXZjN3h6MGhacVYxSGtFWEY2a3drTGVLbDNsZGNlM2ZZdVppYVlKaUdNRGlEQmxrc1hPUEJiRm1nR0hybHNDT3NXUmMvN1QrZjdEUW5Cb2lZSFJrVWh6WHRKZ09xYnZjSXBzSlQ5M0g2VmVIWUhMWkJSTUthbEg5WFhrdFB0Q2dCVm91ZXdSMDl0Z3BCamhGY2VLeVJOU0E3bGZjNTNSTVJMeC9HUmkyLzJwdG4rOXJBZHQzYmFhMndZRDFvTmZBZTVtVHdmZzdZcVdRUEJCbFFJUGRsMXdmSnEybXBiVW04RElFbUdNM21QNUh5cWRHb1M1ZnB5S3hOMXZNcTkyZ2ZIM1lQZ3RsWHBPUDlDakduckNXNHdieXpYRjVIenB3VWxBSnIzSnBUVkNsSWpvSTE4TmVmNmc4Ky9wNng2Q0Z6Y3NBWVFhZUROUHlVSEo4WlZWWmFBL2hYNVY2YjdROWtIbmZDbHMxNDhwdms5SVd3TVRHUlJreEFhR2Z2ak42SjFtZUFyUDJUOHFnL0xhNHFLSnA5ZFdYK0w0Lys3ZUl1bC9CSk1zS0RGaGxPU0tMTDQ4T040dzVqaEVYMGhoOGFXdzgiLCJtYWMiOiIxMmU1NWQ1MjM4ZWQ1MjBlOTZjYTI1M2RlN2ZmZjNhZTk0NThlODUzZjgyMGYwYzhmMDkxN2IzYWQyNmUzYzE2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:26:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxtVGI0UG50SHJST3o4aEhucEJtUHc9PSIsInZhbHVlIjoiUVpWdVFBTGI3Rm8rV1BobnUwQW1lMXhHL2I1R3JsU1RJZjFmaFlQQmdyVUZOTGtwUEt0ZFA0NzlPcCtmTGxVYm5BZWVCU3c1QUhxODBlY3JRN3lXNzA3ckZsYjZDc1licXNzbUxlUWh1MVBzUXlTUHFpTnRnSnhldXNJYUNHQkZLdjI1UCtHM3hkMkxGR3lXT0RGVWRmdlhoQlA5R1RMZFNUTllZOFRKanNtQnVNVTBRM3ZiWTJJbUxwc1BCNVJrYWo4WDFRZmNMRHNTVjFFeWsvS3RZMm5NZkRnWmVkNDBMMEdGakhvNER5TThkS1IvZEx4QlZySlJXVWg3WXpwUVJ6MUZJMTJLRDl1bDdGN3FnYXVRTGpSbm8rZm0rekdMSTFGR2YrU2xDV0FMenV0V29mSFo2ZDZtcEY3bGw1dEZHS3k5NVlZcUtVL1hJUmdVZ1FLZ3lHQjA1N1RoOUpkcmxTaGRkRTFTYmlKNjErVVNrMTNTOE5PMk9FM1JyU0lxRW5NdTV2YzJXanZ2L2tpQ3RVQS9ia25xSEFtcFJ0TFpNOE43cWZXU1F0UmdxMWJKbXNUczFZNHRTeVREWC9CQUUxYkZDOVVWRk03YWZGa1RqYm5sUkdYc1hrU1BnNVY5T1JNTlZTbHFjc0NWUTdOTFpWTWVKTHM0NndOMzFLSzUiLCJtYWMiOiJiYzcxMTI3OTYzNzdhMDkwNDAxZjhkOTZmMDhjMTNmMjFlZjBmOTc2NWZkNWFiODA3ZWZlNjcxZDNjMmY4NzdmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVtMkJ1L0FjU0Q1aGtML2FmeW0weEE9PSIsInZhbHVlIjoiNytuaFlnaFljUGRKRS90RDI4azRRQ0dlK1VYN0pwVy81ZWlEYTJVeDBDaHNxKzEyWEZNK1B0MkVSZDZudXZjN3h6MGhacVYxSGtFWEY2a3drTGVLbDNsZGNlM2ZZdVppYVlKaUdNRGlEQmxrc1hPUEJiRm1nR0hybHNDT3NXUmMvN1QrZjdEUW5Cb2lZSFJrVWh6WHRKZ09xYnZjSXBzSlQ5M0g2VmVIWUhMWkJSTUthbEg5WFhrdFB0Q2dCVm91ZXdSMDl0Z3BCamhGY2VLeVJOU0E3bGZjNTNSTVJMeC9HUmkyLzJwdG4rOXJBZHQzYmFhMndZRDFvTmZBZTVtVHdmZzdZcVdRUEJCbFFJUGRsMXdmSnEybXBiVW04RElFbUdNM21QNUh5cWRHb1M1ZnB5S3hOMXZNcTkyZ2ZIM1lQZ3RsWHBPUDlDakduckNXNHdieXpYRjVIenB3VWxBSnIzSnBUVkNsSWpvSTE4TmVmNmc4Ky9wNng2Q0Z6Y3NBWVFhZUROUHlVSEo4WlZWWmFBL2hYNVY2YjdROWtIbmZDbHMxNDhwdms5SVd3TVRHUlJreEFhR2Z2ak42SjFtZUFyUDJUOHFnL0xhNHFLSnA5ZFdYK0w0Lys3ZUl1bC9CSk1zS0RGaGxPU0tMTDQ4T040dzVqaEVYMGhoOGFXdzgiLCJtYWMiOiIxMmU1NWQ1MjM4ZWQ1MjBlOTZjYTI1M2RlN2ZmZjNhZTk0NThlODUzZjgyMGYwYzhmMDkxN2IzYWQyNmUzYzE2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:26:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-355109189\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-876842521 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876842521\", {\"maxDepth\":0})</script>\n"}}