{"__meta": {"id": "X8a9fa4b851d090e7538b63e860e5b91d", "datetime": "2025-06-07 04:15:52", "utime": **********.906791, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.582574, "end": **********.906823, "duration": 1.***************, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": **********.582574, "relative_start": 0, "end": **********.710879, "relative_end": **********.710879, "duration": 1.***************, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.710897, "relative_start": 1.****************, "end": **********.906826, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03411, "accumulated_duration_str": "34.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.797325, "duration": 0.03109, "duration_str": "31.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.146}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8539088, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.146, "width_percent": 3.342}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.884325, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 94.488, "width_percent": 5.512}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269750615%7C4%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inh6VENQSWx1dkpBeHhORnVZbDJpblE9PSIsInZhbHVlIjoiR2NjYUcwa3NraXcxemtUMHd6d1dMZHV5a2ErZXV0SXQydUZrNGc5R05obUExMG1xbDB6TnVUWEhvbDJnb2pKOUVPTFN1M2ppTmRNS3ByeE93YjY2Z3YySTJyZUJNb3I0dDVaUmI4VFlRekNzTCtxRVB0R2IxY0dMUmNES3J3bWZKZlQ5Y2F3bXVnbFp6WElNNk9xdE5jZ3JPV1JHOWJvanFnRUN0MHV6Nm15MFc5SWZ4K1dYdlk2c1dOdXZvN3hxZDNYR2VtQXFXOGFwK3NEWmdaTjVCaWJVQ3hMQ3VuTm9hU0dPZGtVL0hWcW9QMWdLVXUrbTY0Sml3UWJ4Nmk0RnVaTjFDdDFwd0VhMUFyV0RkQzB1bUdmaE4zdFhuZnlMM3dTS0E5dXNlRExHcENRWFo4VGdDRXBXbWZkZEdaNm9hV1Rpa1dkVDBmQzNtMUJzZ3U1bzkzRnZiVEJreTg5WFA5cE5WMFUvY3luZmFlQnRkenRBSzRoQ3hSZmtWQllITExVL2tpNFNuNjcxSHFDUWprSVR0SzNxNUVNY29DQ1lPNW5jR0RJSDU4RGpLazZZTG1scE8yQTNZOTJUT0lhczB6eUFCTmV5ekZGRHB1Q2czMnhVbHovaXJldERwWk9HN3VMQlI4cExBRmpOY2FiUDJVT1ppQ1dzYjFkT09LS3EiLCJtYWMiOiJiZTllNGM4MWVhMzIyMDM5MDQzZGE1NGNlZGMxYjBjYzdkNDJlY2ViODkyMDNhMTQxODMyOTE2MDc2ZDk1NjM1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndiUThMa2pWUThHdUd3dUVOYTZHd1E9PSIsInZhbHVlIjoiTDd2RnliSmpCUE9tUTQ2OXFLdG5PUnZ4SVE0bVljeDhjNjhuQ3pEenpqNzVUQWtvbnozQjdaUlRlSXQxaGZyR1c3ZThscXM1YmtjRTBrd0ZLSDA5ZmlJVDBGNWdTdDNKQk5EbEUrNVVJbUcwaTBoUFp6MytQb29zVm9WS1llaGpweDI3ZStqTEVVVFVpLzh4Z21CeU9BZXpjWFVFNjR1N3hma1JLbi90MkZYT3d6c1lQZUNSRlZQcWJUSWVmOElicDlNbzBqYlhtT3BMSnF5U1ZCTFNpK2hKNFdBTmxyQnFaQSsySnMzUFU3ZUlxU3VEQlFkQmd0NXVFaThwdmRuaXR5WHZaallvU0EyRGRHQmFUSFNJMU43bVpHakVkTEpnYVM2NnNSMjY2SU1QcEFNMk5NZlpxTXowQ3crcjdJZ2tmc0dIdU9IMXpFTDRaVTVHOU5YVEYzMGZiay82cCtncnN5RDRsMFRId3lHMW1iYkx4S29nS3JKSGgwM0I1ZlZaREZxN0xaUE1GbGRvK3lRYzZpSUVzeUNZT08rOGI1Ry84VmZuOEZpRGd0c09oS3BMNDRLRll6NVdOeWJZNUpETTBCRXlUTmdNL092YXZLUzhscVJlWlVLMDFueUNaMlhWRzM2eERmU2VEY1VqaGh4dTA2M1MyWUgrdFEwMmxuUEEiLCJtYWMiOiJiOTJkMDJjZjIxMzQ1NzNlOWRiYmEyM2M4N2Q3NWYxMzM5MzI0NzRkMTEyM2I4NGQ1ZmRmM2JjM2E0NzhkNmRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-258284829 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:15:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQrOVd1V3pJL0wxamhrdDgydDA1b1E9PSIsInZhbHVlIjoieUM5Z2l1SzRROTBJR3hUeURXNVVUd1Jqc0d4eWRVZDgvRzBCS0dEKzJpblJ2L0JsYmpzeDRHT2xncktLVCt1dGlsZE5raEtvaU9yUHhqd2xSUlA4Y05pM1RXcEJWTDYyZkZMTSs0VGNUSmJwakNrT0lzQ3lBRDdzQXZyU0NOMkhMRUdBZGEyNXpxTnB0UGlwRFIrZzM4cmZGZ25jMldKWlY4dWowNEt4QUpsQ3RCcElicUlUSEVLcjgwQmZxVnhmY25CT1gzeHB6NlJpNlN2bXViM2RBU0JiT2ZVd2Q2VWFwMlJaSGc4ajIrYlMvVTBhdFJoK0ttdlowQlpDTHIzZUw3SHR4d1ZxdVNwWU5xZ2xpRWZ2UFJ5RFNHVmZsN2M0Q2Z4SlA4aFlQR28vK2VxbXdNM1NEbkY2RWhPWVlubW1DZ0J5N1o0UWFxTkFGRE5jQm5WS2lneW9Iak9wZlJXVUp3NVpnV2ZOa0UyQ080eThJY1hXVGJYU0VDQ1FSQ0ZiZXFuNTVWQ0RGY3Nyd1lkUTZCN3lyYVQvM0hrWWxkQVhwQWRxU0hPei9jWEdoQncrT3Q4aVBUVXRRUXVxWkk1WTliTVdHS1ZEY1ErK0xhOTRNMlZaNzdDR3lpRmRwUGtyTXhjZEtieldjWjlGUUtUc3E2OWx1UXEwZUJmanVxM1giLCJtYWMiOiIzZTBlNmM5MzMyODc5MTZkMWEzMmQyN2Q3NzEwOTJkYTVkZWQwZmJmNGE5ZDUwYWQxZDMzODFiMDlhM2Y3NTBiIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNmVmpEZmEyZ1lJdUdBV2RVbTlDZUE9PSIsInZhbHVlIjoiY2p5S3NKRW9mb012SVJWZ01mS1g3ZFRVblhPODd0LzNWdHRlWFBmcVhvenBzN0UyRVpwTXNoeklvNU9tZkxSOUN0eDhMbVhYVis4RUdaamMwWFZDaE8yWTRidnMySzRXRi9tUWdnWkE1MnplNENSTisveVVKZmZwM01DTXdZUkJsSUkxZzFmbjhlL0ZKTGQ5SDcrcndSUjVRdzN6MXpaK0tHdWhLdWhxZE1EQjh5dXUyVlpLUzdCZTRUUXJxNWFsQzBCODZWNFlRSUNDcWc4akppNzU0b0ZNYWplOGg4ck9vMUVmd0F5YVFRRU5HRFhYckNub1MxSTVCRVF5dFdKQUtEUkVYR2tTdENiUXc1TDE1bytjcDM3MDZkYU5qVUZzNHNpQk9vSVN0Skl6VkJ1c2YwZmtmN1cvSXRrVkx2dTUxU00zTjRyOE8rRFNtZ3lIdWg5c2FJekRmaUM5OXlKbzAvYzNhQkpSekVueE5tVElpd3RjRGpVbGRzeFhaQTFsUzhkK3FWSTNrbGM1dVRaMHV6RjV5SWlTdGhqSU9hMzJkS1J5ZmhKRHB3SWsxQWFXVlBHaTBkMS9HTjVhZndsNHlYcFg1cDZ3R2JldmtZcElkQS9vamswdFFUNm9hTXRoa3JxK1F3Nm1iL2x2S3p3UzlYNHZ6NzBYTzJrSkxTT2giLCJtYWMiOiIxNWY0MzRhYWZmN2Y0ZTk5MWIxMjVmNzMwZTg0Y2E4MmUwNzgzYTg4NDdjMGYxYjk1NDE5M2Y5ZWZmZWQ2YmZlIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:15:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQrOVd1V3pJL0wxamhrdDgydDA1b1E9PSIsInZhbHVlIjoieUM5Z2l1SzRROTBJR3hUeURXNVVUd1Jqc0d4eWRVZDgvRzBCS0dEKzJpblJ2L0JsYmpzeDRHT2xncktLVCt1dGlsZE5raEtvaU9yUHhqd2xSUlA4Y05pM1RXcEJWTDYyZkZMTSs0VGNUSmJwakNrT0lzQ3lBRDdzQXZyU0NOMkhMRUdBZGEyNXpxTnB0UGlwRFIrZzM4cmZGZ25jMldKWlY4dWowNEt4QUpsQ3RCcElicUlUSEVLcjgwQmZxVnhmY25CT1gzeHB6NlJpNlN2bXViM2RBU0JiT2ZVd2Q2VWFwMlJaSGc4ajIrYlMvVTBhdFJoK0ttdlowQlpDTHIzZUw3SHR4d1ZxdVNwWU5xZ2xpRWZ2UFJ5RFNHVmZsN2M0Q2Z4SlA4aFlQR28vK2VxbXdNM1NEbkY2RWhPWVlubW1DZ0J5N1o0UWFxTkFGRE5jQm5WS2lneW9Iak9wZlJXVUp3NVpnV2ZOa0UyQ080eThJY1hXVGJYU0VDQ1FSQ0ZiZXFuNTVWQ0RGY3Nyd1lkUTZCN3lyYVQvM0hrWWxkQVhwQWRxU0hPei9jWEdoQncrT3Q4aVBUVXRRUXVxWkk1WTliTVdHS1ZEY1ErK0xhOTRNMlZaNzdDR3lpRmRwUGtyTXhjZEtieldjWjlGUUtUc3E2OWx1UXEwZUJmanVxM1giLCJtYWMiOiIzZTBlNmM5MzMyODc5MTZkMWEzMmQyN2Q3NzEwOTJkYTVkZWQwZmJmNGE5ZDUwYWQxZDMzODFiMDlhM2Y3NTBiIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNmVmpEZmEyZ1lJdUdBV2RVbTlDZUE9PSIsInZhbHVlIjoiY2p5S3NKRW9mb012SVJWZ01mS1g3ZFRVblhPODd0LzNWdHRlWFBmcVhvenBzN0UyRVpwTXNoeklvNU9tZkxSOUN0eDhMbVhYVis4RUdaamMwWFZDaE8yWTRidnMySzRXRi9tUWdnWkE1MnplNENSTisveVVKZmZwM01DTXdZUkJsSUkxZzFmbjhlL0ZKTGQ5SDcrcndSUjVRdzN6MXpaK0tHdWhLdWhxZE1EQjh5dXUyVlpLUzdCZTRUUXJxNWFsQzBCODZWNFlRSUNDcWc4akppNzU0b0ZNYWplOGg4ck9vMUVmd0F5YVFRRU5HRFhYckNub1MxSTVCRVF5dFdKQUtEUkVYR2tTdENiUXc1TDE1bytjcDM3MDZkYU5qVUZzNHNpQk9vSVN0Skl6VkJ1c2YwZmtmN1cvSXRrVkx2dTUxU00zTjRyOE8rRFNtZ3lIdWg5c2FJekRmaUM5OXlKbzAvYzNhQkpSekVueE5tVElpd3RjRGpVbGRzeFhaQTFsUzhkK3FWSTNrbGM1dVRaMHV6RjV5SWlTdGhqSU9hMzJkS1J5ZmhKRHB3SWsxQWFXVlBHaTBkMS9HTjVhZndsNHlYcFg1cDZ3R2JldmtZcElkQS9vamswdFFUNm9hTXRoa3JxK1F3Nm1iL2x2S3p3UzlYNHZ6NzBYTzJrSkxTT2giLCJtYWMiOiIxNWY0MzRhYWZmN2Y0ZTk5MWIxMjVmNzMwZTg0Y2E4MmUwNzgzYTg4NDdjMGYxYjk1NDE5M2Y5ZWZmZWQ2YmZlIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:15:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258284829\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1385671753 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385671753\", {\"maxDepth\":0})</script>\n"}}