-- إصلاح صلاحيات التسعير للمستخدم الحالي
-- تشغيل هذا الملف في قاعدة البيانات مباشرة

-- 1. التحقق من وجود الصلاحيات المطلوبة وإنشاؤها إذا لم تكن موجودة
INSERT IGNORE INTO permissions (name, guard_name, created_at, updated_at) VALUES
('manage pricing', 'web', NOW(), NOW()),
('show pricing', 'web', NOW(), NOW()),
('edit pricing', 'web', NOW(), NOW()),
('manage inventory', 'web', NOW(), NOW());

-- 2. البحث عن معرف الشركة الأولى
SET @company_id = (SELECT id FROM users WHERE type = 'company' LIMIT 1);

-- 3. إنشاء دور "Pricing" إذا لم يكن موجوداً
INSERT IGNORE INTO roles (name, guard_name, created_by, created_at, updated_at) 
VALUES ('Pricing', 'web', @company_id, NOW(), NOW());

-- 4. الحصول على معرف دور Pricing
SET @pricing_role_id = (SELECT id FROM roles WHERE name = 'Pricing' LIMIT 1);

-- 5. الحصول على معرفات الصلاحيات
SET @manage_pricing_id = (SELECT id FROM permissions WHERE name = 'manage pricing' LIMIT 1);
SET @show_pricing_id = (SELECT id FROM permissions WHERE name = 'show pricing' LIMIT 1);
SET @edit_pricing_id = (SELECT id FROM permissions WHERE name = 'edit pricing' LIMIT 1);
SET @manage_inventory_id = (SELECT id FROM permissions WHERE name = 'manage inventory' LIMIT 1);
SET @manage_product_id = (SELECT id FROM permissions WHERE name = 'manage product & service' LIMIT 1);
SET @edit_product_id = (SELECT id FROM permissions WHERE name = 'edit product & service' LIMIT 1);
SET @show_product_id = (SELECT id FROM permissions WHERE name = 'show product & service' LIMIT 1);

-- 6. ربط الصلاحيات بدور Pricing
INSERT IGNORE INTO role_has_permissions (permission_id, role_id) VALUES
(@manage_pricing_id, @pricing_role_id),
(@show_pricing_id, @pricing_role_id),
(@edit_pricing_id, @pricing_role_id),
(@manage_inventory_id, @pricing_role_id),
(@manage_product_id, @pricing_role_id),
(@edit_product_id, @pricing_role_id),
(@show_product_id, @pricing_role_id);

-- 7. إعطاء صلاحيات التسعير لجميع مستخدمي الشركة (company)
INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT @manage_pricing_id, 'App\\Models\\User', u.id
FROM users u 
WHERE u.type = 'company';

INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT @show_pricing_id, 'App\\Models\\User', u.id
FROM users u 
WHERE u.type = 'company';

INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT @edit_pricing_id, 'App\\Models\\User', u.id
FROM users u 
WHERE u.type = 'company';

-- 8. إعطاء صلاحيات التسعير لجميع مستخدمي المحاسبة (accountant)
INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT @manage_pricing_id, 'App\\Models\\User', u.id
FROM users u 
INNER JOIN model_has_roles mhr ON u.id = mhr.model_id
INNER JOIN roles r ON mhr.role_id = r.id
WHERE r.name = 'accountant';

INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT @show_pricing_id, 'App\\Models\\User', u.id
FROM users u 
INNER JOIN model_has_roles mhr ON u.id = mhr.model_id
INNER JOIN roles r ON mhr.role_id = r.id
WHERE r.name = 'accountant';

INSERT IGNORE INTO model_has_permissions (permission_id, model_type, model_id)
SELECT @edit_pricing_id, 'App\\Models\\User', u.id
FROM users u 
INNER JOIN model_has_roles mhr ON u.id = mhr.model_id
INNER JOIN roles r ON mhr.role_id = r.id
WHERE r.name = 'accountant';

-- 9. التحقق من النتائج
SELECT 'تم إنشاء الصلاحيات بنجاح' as status;
SELECT name FROM permissions WHERE name LIKE '%pricing%';

SELECT 'تم إنشاء دور Pricing بنجاح' as status;
SELECT name, created_by FROM roles WHERE name = 'Pricing';

SELECT 'المستخدمون الذين لديهم صلاحية manage pricing' as status;
SELECT u.name, u.email, u.type
FROM users u
INNER JOIN model_has_permissions mhp ON u.id = mhp.model_id
INNER JOIN permissions p ON mhp.permission_id = p.id
WHERE p.name = 'manage pricing';

-- 10. عرض صلاحيات دور Pricing
SELECT 'صلاحيات دور Pricing' as status;
SELECT p.name as permission_name 
FROM permissions p 
JOIN role_has_permissions rhp ON p.id = rhp.permission_id 
JOIN roles r ON rhp.role_id = r.id 
WHERE r.name = 'Pricing';

-- ملاحظات:
-- 1. هذا الملف يعطي صلاحيات التسعير لجميع مستخدمي الشركة والمحاسبة
-- 2. يمكن تخصيص الصلاحيات لمستخدمين محددين حسب الحاجة
-- 3. تأكد من تشغيل هذا الملف بصلاحيات إدارية في قاعدة البيانات
