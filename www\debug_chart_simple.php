<?php
// تشخيص بسيط لشجرة الحسابات
echo "<h1>تشخيص شجرة الحسابات - فحص قاعدة البيانات</h1>";

// إعدادات قاعدة البيانات من ملف .env
$host = '127.0.0.1';
$dbname = 'ty'; // اسم قاعدة البيانات من ملف .env
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p><strong>تعليمات:</strong></p>";
    echo "<ul>";
    echo "<li>تأكد من أن خادم MySQL يعمل</li>";
    echo "<li>تحقق من اسم قاعدة البيانات في الملف</li>";
    echo "<li>تحقق من اسم المستخدم وكلمة المرور</li>";
    echo "</ul>";
    exit;
}

// فحص الجداول
echo "<h2>فحص وجود الجداول</h2>";
$tables = ['chart_of_account_types', 'chart_of_account_sub_types', 'chart_of_accounts', 'users'];

foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ الجدول $table موجود</p>";
            
            // عد الصفوف
            $countStmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $countStmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p>&nbsp;&nbsp;&nbsp;عدد الصفوف: $count</p>";
        } else {
            echo "<p style='color: red;'>✗ الجدول $table غير موجود</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في فحص الجدول $table: " . $e->getMessage() . "</p>";
    }
}

// فحص بيانات أنواع الحسابات
echo "<h2>بيانات أنواع الحسابات</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM chart_of_account_types LIMIT 10");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($types) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>منشئ بواسطة</th><th>تاريخ الإنشاء</th></tr>";
        foreach ($types as $type) {
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td>{$type['created_by']}</td>";
            echo "<td>{$type['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ لا توجد أنواع حسابات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جلب أنواع الحسابات: " . $e->getMessage() . "</p>";
}

// فحص بيانات الحسابات
echo "<h2>بيانات الحسابات</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM chart_of_accounts LIMIT 10");
    $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($accounts) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الكود</th><th>الاسم</th><th>النوع</th><th>مفعل</th><th>منشئ بواسطة</th></tr>";
        foreach ($accounts as $account) {
            echo "<tr>";
            echo "<td>{$account['id']}</td>";
            echo "<td>{$account['code']}</td>";
            echo "<td>{$account['name']}</td>";
            echo "<td>{$account['type']}</td>";
            echo "<td>" . ($account['is_enabled'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$account['created_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ لا توجد حسابات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جلب الحسابات: " . $e->getMessage() . "</p>";
}

// فحص المستخدمين
echo "<h2>بيانات المستخدمين</h2>";
try {
    $stmt = $pdo->query("SELECT id, name, email, created_by FROM users LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>البريد الإلكتروني</th><th>منشئ بواسطة</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>" . ($user['created_by'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ لا توجد مستخدمين</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في جلب المستخدمين: " . $e->getMessage() . "</p>";
}

echo "<h2>الحلول المقترحة</h2>";
echo "<div style='background-color: #f0f8ff; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h3>إذا كانت الجداول فارغة:</h3>";
echo "<ol>";
echo "<li>تشغيل الأوامر التالية في terminal:</li>";
echo "<pre>php artisan migrate<br>php artisan db:seed</pre>";
echo "<li>أو إدراج البيانات الأساسية يدوياً</li>";
echo "</ol>";

echo "<h3>إذا كانت البيانات موجودة ولكن لا تظهر في الصفحة:</h3>";
echo "<ol>";
echo "<li>تحقق من تسجيل الدخول</li>";
echo "<li>تحقق من الصلاحيات</li>";
echo "<li>تحقق من قيم created_by</li>";
echo "</ol>";
echo "</div>";
?>

<script>
// إضافة زر لتحديث الصفحة
document.body.innerHTML += '<br><button onclick="location.reload()" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">تحديث الفحص</button>';
</script>
