{"__meta": {"id": "X9f13b63e1e2dd9c9f0fe7666fc1c1b66", "datetime": "2025-06-06 20:34:23", "utime": **********.363504, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242061.902403, "end": **********.363539, "duration": 1.4611358642578125, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1749242061.902403, "relative_start": 0, "end": **********.176708, "relative_end": **********.176708, "duration": 1.2743048667907715, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.176727, "relative_start": 1.2743239402770996, "end": **********.363542, "relative_end": 3.0994415283203125e-06, "duration": 0.1868150234222412, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44800120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00903, "accumulated_duration_str": "9.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2561111, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 53.931}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.290369, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 53.931, "width_percent": 10.963}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.319211, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 64.895, "width_percent": 21.705}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.339209, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.6, "width_percent": 13.4}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-773210051 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-773210051\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-877030543 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-877030543\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1429380619 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429380619\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-156235425 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242029923%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9wRk94NlBRUEF4VE9xeDZzMkFIdmc9PSIsInZhbHVlIjoicUwxZ3ovaFQvamNxOUx0U05VM0ZxMXlIdzZ3WHhqTlhyekEyb1FuUENiNnB2STZCVHErc29sMHdXRXg4NkI2dnV1ODlsV0Z5a2dRZ1E2SHBRY2dSNFhzaU5MKzNCS2pBWWhycCtPbGtvUVc5dDhJVWxHMXZLY3NGa0wwU0QvdGp3UWlIQlpoTGNsakgranBxQmdGWDRYN3pyaEJsYiticlNzZ3ljNFRjOU9DM0YvZ05pM3RyZ0dROCsycXN1K2FZOVlVd3FNeDJjc2lYRTR5TXd2cW01aGlBUmpheVlSczcraDBteW5ZR3ZkMFJ6SlZYZWRWLytDV1JzZldrR29TczlyaWkvZVYxM0toenZPSmNZNUNxQ1BtTzZVSS9OMTdTclpQWjFRbXpmalc4bS91bDBKa1RzR1ZCdjNBZWYzQktUS2dBNWNrdVQ2eHNxamFsZDd5VzdhNlQ3K2p5c2xqZ1g0Zll4Z3hycFVGVHJqdWFFRXRrVkJOMzU1T3ZOS0VqNS8rQUhGL2d5UFYzWGRSQlhGb08zNmw5RlJyNkpkTHpGVERrYjk5YWZYL1VJQ0hHUFpLWVRxLzJwdXNiWUdhanRsalhzcjVWanZXYkUvWUVMZmRSSjUyL2VrbFpIV0Q5Z0o0VzV1dEo4WmJTRUsvUm9ORzdqZ3FWcVVBV2s5SEMiLCJtYWMiOiJkNjMzMGFiYWJkZWViY2EwNzU5MDdlOGE0ZjllY2RhY2Y2YWY2N2M1YjM3N2M4MGMzOThjODYyYmYzNjc1ZTBjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVHbEZsOG9OOXlwZ292RHV1dFpKelE9PSIsInZhbHVlIjoiUVFIdVNhOVp0RzMxU2tZRGorcm1wWGNYU2w4MncwZzFOU2YwcURjM0tTU085a1RGK2lta1BkMXBFTWhFTVo4QlZkWU5ua1hPMTJGOUQ5b3REMC9XdkxEcDAzVUFDUEJFekxLSFRRTVluN1I4OUxSQXpHa09VT3VHdFljNVFLdUozTWhqOTFiSFkvQm9HUmhSblZ6Zm9hOStsTkdiN1hCTnVXeFR1dW8zaTFySTNYbXcydFJhRDVpWUZtZlVyblBndW5iaGhNc1FsV3NzWmhVR1NPMmZ2OWRhc1I5cy9PNk9Ia0ZBSFY4UmJMc0V5TGJnbXFwMnBBc3d5MkZFdFdFU3k1MTc4R1NRNWY3ek13ZzZIUGdjaml5MjVtR1lmVk1aOW5jTzBsZEpTeThoZnl2QnI1dW82azc5WjhHeFRxZmM1Uk9NYS9UR3Z6bXlWbnBwT2gyamV4dU5NWURUc0xtWkFQZ2FsTE9hNDJ6YUs4MDI2NDgraXIzTWxqWTVRZkdJMEZKSGZTOXcxaHJJL2RBRE1QcEptUllYYzZZVE5ReEpKQmhyK3ZORmlnU0xVWGhHbFBjTmxEK0ZNR0MzdnB3eHJvNlY2c0tOREhUbXk3TlZia1Z0dHRzU3lUaWd6OXhDMTBxbzhockxhcVBmaUpkQUd4Y1hCdENJWUpWTlFVVmsiLCJtYWMiOiI1MWI4M2Y1ZDVhYTE3ZjA2N2FlNDliZjRmYTFjZmEyZWE4NmZhOWY3MzlmNjBlNzk0NDYwYWJhZjVlNzg2M2I1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156235425\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-130976804 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130976804\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-275374050 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:34:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJXU3VoVVZBc3pVNEN0aVNEU1FTa1E9PSIsInZhbHVlIjoiSUEwOHJwRW9WbGFiREtITi9CWHIrUENoZ3IrYXhIZi9sYTc5d3Z1cjZSRzZzRzA2dFEraUpJQWc4ZEE2YStQN29pU2ZxVmRLZjBsK2NHQk95b010M2prdkFFNnY2K3cvdk9uU24rcVUxTzRuYUdabFhjZmU4MnpuU0hvT2NRR0t4MmcwQXhyM0syd0FzWWZEcUlwbG9HM25pTTBHUytqME91NFd5Y3V5MHJ3RGdoNTdmb2paVHFVb2g4elZrVTF6M0k3SitvYnFyNWhWRHVlajhRK29lQ1FJRElzOHZHT2xQQXd1MlFLMytCSEFRd2dEbktvREFLbERPZ1JHelArMXpaVzVacldNMWRqRWgraldYc0thYTRKeDk4MEw2KyszeW9vbHBnSlZkQmFROTVtNVJyVmVkbFVtOHFLc3VSUG1zcmhYNU1DTUZYVFdpZDhjdXlQRG1qbmZzUENaRzN0NDBpZk1TcGVJck5oRk8waDVKazE4YWd0RXl3N2FzT1E2VjVmaUFEWHd6VFpXNXFEMjNmQy9CTHNsMmhYZzB3cnlrZnoxUHdNclNleXBqOEFPdHd2N2QxeWdieGNBSjczRjFDM0RtcDN0cVl5d1o5VGJXY3dnS1VIL1NHcU1zbjY0Vit2ZHhRdkFvVHdYVndEelpvVkNvdWJmbURRTlBEaE0iLCJtYWMiOiI4NzYwMzUyMDFlMTg3MTljNmJlNzQxMDk1MGM5MGQ5NDI5NmViNDYyNjc0OGViZDBmZDM3ODk5NDliYmM3YzgxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRiN0NMK2R6Mk81UEJ5M1JUaXV2d2c9PSIsInZhbHVlIjoiSldUUlE5elk1UkV4QUxXa2VHNHM0cUY1K1hjVlByRllpRWxoNFpidWdiYnlSYy9Bb3V2U0ZqMWdXcWwveU9BTkcwR3NWQnRkanluRFNKVkdZSEFjenlGZ09wSlZPajFvOExMS2JSbElGaS9Qd0dPTjNwamZzOUY4YlhjTENXWUVlcEFWdGlNdXZwczhtNHRNTTVjRXdXNkQvSzB2dlZVSTg1N1VCSXE0cVp0d283d2tKZlRUNDlmOSt0YXQ3Sk41Wk80Nnp2NkN3SzFmcUtjUE1GVjdmcGkwZm9Oa3gxQlhEanE5TUx2cmxxNVRmQVJDVi96V21uYXY5WG9GYUIyVjJ4aGNDUGc5UVlyZWlxKzdybmpYNjcxOVU0dWtqSk5sMThoU2Rvbll3OEFkREFaTlFEdCtseE9oSWhHbnFOalZIeWNueGpjR2lmNWUrcVhrS2duTGc5dDRvM0wwQjVDTUJqcm1iRXRGUWhnOTl1Zm9wQ1JmSEFTNFBRKzJnYXZmbzVIYVM2ZGJZU1NkSHJQazBhQ09ZY2ZxamFYNWVMVitYWE9qZk5FdGlWd0ZleEs1MllwdFpGSXVJVXNsQlJSOS9pcVcvN3V4bFFWa1BoaEJVYnFWSWJkN3V2MG5ZRVFsRUx6azVma3g0RFZpeFAxYi9qMGM1ZUtCKzlJaDBEMUoiLCJtYWMiOiIxNjdjYmRhYzM2MTdkNzMzMDM2NTBlYzUwYmJlMGZkZTNmNTc1OWIxZDllYjczZGFiMmVjMGJmN2VlYzBiMTVkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:34:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJXU3VoVVZBc3pVNEN0aVNEU1FTa1E9PSIsInZhbHVlIjoiSUEwOHJwRW9WbGFiREtITi9CWHIrUENoZ3IrYXhIZi9sYTc5d3Z1cjZSRzZzRzA2dFEraUpJQWc4ZEE2YStQN29pU2ZxVmRLZjBsK2NHQk95b010M2prdkFFNnY2K3cvdk9uU24rcVUxTzRuYUdabFhjZmU4MnpuU0hvT2NRR0t4MmcwQXhyM0syd0FzWWZEcUlwbG9HM25pTTBHUytqME91NFd5Y3V5MHJ3RGdoNTdmb2paVHFVb2g4elZrVTF6M0k3SitvYnFyNWhWRHVlajhRK29lQ1FJRElzOHZHT2xQQXd1MlFLMytCSEFRd2dEbktvREFLbERPZ1JHelArMXpaVzVacldNMWRqRWgraldYc0thYTRKeDk4MEw2KyszeW9vbHBnSlZkQmFROTVtNVJyVmVkbFVtOHFLc3VSUG1zcmhYNU1DTUZYVFdpZDhjdXlQRG1qbmZzUENaRzN0NDBpZk1TcGVJck5oRk8waDVKazE4YWd0RXl3N2FzT1E2VjVmaUFEWHd6VFpXNXFEMjNmQy9CTHNsMmhYZzB3cnlrZnoxUHdNclNleXBqOEFPdHd2N2QxeWdieGNBSjczRjFDM0RtcDN0cVl5d1o5VGJXY3dnS1VIL1NHcU1zbjY0Vit2ZHhRdkFvVHdYVndEelpvVkNvdWJmbURRTlBEaE0iLCJtYWMiOiI4NzYwMzUyMDFlMTg3MTljNmJlNzQxMDk1MGM5MGQ5NDI5NmViNDYyNjc0OGViZDBmZDM3ODk5NDliYmM3YzgxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRiN0NMK2R6Mk81UEJ5M1JUaXV2d2c9PSIsInZhbHVlIjoiSldUUlE5elk1UkV4QUxXa2VHNHM0cUY1K1hjVlByRllpRWxoNFpidWdiYnlSYy9Bb3V2U0ZqMWdXcWwveU9BTkcwR3NWQnRkanluRFNKVkdZSEFjenlGZ09wSlZPajFvOExMS2JSbElGaS9Qd0dPTjNwamZzOUY4YlhjTENXWUVlcEFWdGlNdXZwczhtNHRNTTVjRXdXNkQvSzB2dlZVSTg1N1VCSXE0cVp0d283d2tKZlRUNDlmOSt0YXQ3Sk41Wk80Nnp2NkN3SzFmcUtjUE1GVjdmcGkwZm9Oa3gxQlhEanE5TUx2cmxxNVRmQVJDVi96V21uYXY5WG9GYUIyVjJ4aGNDUGc5UVlyZWlxKzdybmpYNjcxOVU0dWtqSk5sMThoU2Rvbll3OEFkREFaTlFEdCtseE9oSWhHbnFOalZIeWNueGpjR2lmNWUrcVhrS2duTGc5dDRvM0wwQjVDTUJqcm1iRXRGUWhnOTl1Zm9wQ1JmSEFTNFBRKzJnYXZmbzVIYVM2ZGJZU1NkSHJQazBhQ09ZY2ZxamFYNWVMVitYWE9qZk5FdGlWd0ZleEs1MllwdFpGSXVJVXNsQlJSOS9pcVcvN3V4bFFWa1BoaEJVYnFWSWJkN3V2MG5ZRVFsRUx6azVma3g0RFZpeFAxYi9qMGM1ZUtCKzlJaDBEMUoiLCJtYWMiOiIxNjdjYmRhYzM2MTdkNzMzMDM2NTBlYzUwYmJlMGZkZTNmNTc1OWIxZDllYjczZGFiMmVjMGJmN2VlYzBiMTVkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:34:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275374050\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-838534394 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838534394\", {\"maxDepth\":0})</script>\n"}}