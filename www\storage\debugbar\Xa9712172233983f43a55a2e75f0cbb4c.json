{"__meta": {"id": "Xa9712172233983f43a55a2e75f0cbb4c", "datetime": "2025-06-06 19:13:14", "utime": **********.833179, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.394482, "end": **********.833212, "duration": 1.***************, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": **********.394482, "relative_start": 0, "end": **********.616846, "relative_end": **********.616846, "duration": 1.****************, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.616868, "relative_start": 1.***************, "end": **********.833215, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03091, "accumulated_duration_str": "30.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.718172, "duration": 0.02734, "duration_str": "27.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.45}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.774018, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.45, "width_percent": 4.626}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.806627, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.077, "width_percent": 6.923}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlcvNVlFVlJMQ1ZzeGd3NFVaSnlWWHc9PSIsInZhbHVlIjoiNGR6eGZxUGJBblA3SDVFL2hvWSt3SUdNcUtGdTZIVmNDdHNaOG9lRTFwYzVRRGgvSkpzK2pwd2xVZVluSnczUjhHOVduL2t6aExZcHYzTXFFRWpZSm12a3pXMnYremVLVlRSNk5STzczdDVUOGF5U3lMS3FFNXo4dXNOemlHK1M4YXQwb0Q5YmoxZ0cwTXhIMWxxNFBYU3ZuU0NSaTlBVHl2aXk5eE1QV0FHQm4xanBFL3ZMWW9hNVRrTU1rcitVb05hcU16bkhOYjlnYmRLYVBJUTBvZ3JrejI2bEdERFJMZ2YvREwvNzk5WUhZK2hQUlBtYVkza045TUNVdlBPLzdYZHRBdCtLd0NWc001d29GeEZBTXJqQ0p4RDV5OGxUemNaaVdhQVdNbnV1eUt5ZmZ5NEJ1V3pIWkd0cHFhOVpoUm9iWG1GU24rcEMxb00vRHVJM2ZSUmdWZGNZNWs3VkdOZmhBd1g5c01hL09FK1BGcjZuaDd5d3pDNUlaVDZYdGJDNkJUdThkZ0xidzJhdmI4VnMvOEdsOUJLMWRuQm1uNTY2aTNSMjFFWEJTdWVIL25uM2FIU3NKZzhMYjhabEZvMFZBUEpieXlhZkhJWHFIY3htUWRvaFZUTHBUR2hqSW9Vck52LzY2bzBwTDlyQVdacFVUUXY0OUQrMmJYdHUiLCJtYWMiOiJiOTZkNmIxNDM5MWY1ZGU0NWViNGI1NzY0MDc0MDgzZGExZjcwMWRjMjE0Y2E1ZGFjMGM4Y2Q3OWUzNmU3Yjk1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik8xc1ZCeWhKb25RMjFVaUcyQmRyYUE9PSIsInZhbHVlIjoicTgrTGlqU3hmcnN4dnZ1cEtCUnprRS9yNWhkUWNnYzBlN2xmS1FySmpGeGJxTUcrQ28xUVM3SHpQU0xEUWU2OU9kVGlyUFFOOW1veC9DcVdOT2w4eTE0bUFJS09NMzcwSGRoSWJCYU1yOER0WUdqTk5hd3Z5dHE4WEp4eGUwUG9aS1ZQQjFMdWdOc0dJZDFTV2tuMnBwaElpellPQm1WOTR4alNSY1c2ZWl2enN6clE3cDM1bFAzMmNYRS81Nmd2cjlhM1dha2JJK2VFbzdwNkRXSXAwSmUza21Ob0Jud0tWWDRoYTUxUmtUbkhvZk5xTWhmTk5Tc0UwSE9FczMzaTA2VkU0dG1CVDd4Rk13dkxHRWEvcWFxUTJlWjVlK2o0Tm5vODZoc2lRazFBNlZiUUJhOXlTMjlKdW5QVkhaTGZWa2VVTTRPeS80aGYyYTJDdXMvaW1kM3ZLQkprZU1YYkJhZ1U2QmRCaGFQbzcwdTFUZDNEK3E2bFZMd1NDcW9PYjdHMHhJOGdWVzJvRThkSnZodDlQM0RPY3ZvMWNoQ0l1T095TmhBMWdtZkJDUEtjWVlmV0xoUXo0cHpXNFIyd2VxUG9LTUpMNVBEVjg1SUpHQXNRSXc3aEZNOFVoK1MxTkdWRFh5aVhsdDBJS3dFVXp3ZUhibXltdnA3ZDRzalciLCJtYWMiOiI2MjExNzMzNjUyZTNhNDY3N2I4YzA0YTVmY2QwNTE0MWIzZTMxZTU1NjUzZTdlNjU0OTk2MDczMDUyM2JmMjcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-647156110 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PVt0h9icZb5VqFqziyPToWE7TqW2lYjvh7uvnOnw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647156110\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-836559432 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:13:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBNRnFYakZFdmY3dzJzMmFXaVc5ZUE9PSIsInZhbHVlIjoiWjQ4MHF1V3hMN2dCaWRTREh5WmJCT1lIQU95MTliV2dwYVEyMnJydFkxdWkzOUhySWZPL1pkcndaN2d0eVJ1QzVSdm5ENFByVkpsOWpvWWF0eG04VERrRHBPZjh3czk0M2F3cExsbk9JdHEwak5ZWXhNUEtwRThoZzUzSU1wVnZWNm9WK3I2aXlqUTllNlNFb2l0OUF0aEN1OWRTWVJtTXZXMU5kR2Z6QUZnQW43R2FmV1R1WHIrNVNya25kcWdrcEVOTE5WTFE2UnFMUWhsamZISjdDOG1VWFpmRmEzMk1ERnQ0TG04QUs2Kzl4U05nQnFUTDUrYTlxVytWSlphQnZCdTMyMmdybDFFdUFyckNCcTViOGZ2MDBUVFJmM21nUjhsU2krZHVwV1libUJkU0NKdkJNM3dvL3VVK3R1KytIUE5KRmZMbmw3NFJXZTJodFRzWkVmbFRYdmYzY3NCdGNNbXluakJYcUVDZkRPM09OeW1mTDNOK1BaaGlDVmxLZlVCdWZBRHhDTzRXSUEwMTBnb0FHemU3NCtRZUNwd0Z0blFtbWM1M3JEM0JYSEx6eXZ6S1JLTFdZeEJpQ3lzNFNiL0YydVRrY3ZzNmhvQ1draXFSakwySGV1TjVoZ0YzV25YUUIvYUtCODRqdGZWNEIzbC9wRk1KVCtVSlRrbnIiLCJtYWMiOiJmN2E4MTZjOGZlYmU0ZTgwZmEwNWZiYzg0NTEzYTE4ZWIxZjg0YThkZTRiNTE5YzI3NWM3NmFhZmMxNTRhNTE4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNlSW1POGJWcXBwRXNiYURHODdaeGc9PSIsInZhbHVlIjoiTnhkUzJ4UHdtdENXVFJwQWtSS1YvOEk1UEFKRE1qa1VYa2lWNDZxRnY4QzJHVTFSdll6U0RpTWxwcjBmK2phdjEwRm8wNWlWVTAzVldFellaR3dqNGd6VkJSVmFsUXpDQnFOVVRiYXdFQWl3ODVteWdsaW5nTFVRTVNGUm1xQU9WRXY3aFE4QVI3TDNiajJ6RE1lWWNyT0RkR2prcTJHVkEzOEZSY1lCNFlvRCswZDFJbXJzR1EzRzFaZGEwUlFWSnE2aWw4L1Z2TWc2YkJoV0J5MG5iVFVLaXBSZmI3cEJlMXo3Ri9hOXJjanZ1c1dJamFrcGUzZ21MaXBSYlY5SzVRV2djNUYvbnk5V0IrdzhWUFdmVEpzRU1TZEh6a3EyeHpvRGYvWjQ3a1ZVZ2ZXWEtyRkZ5cXpCdHMxSTdPaHU1V0tramN1UlV5Z0pSSDBxYklPQmVVTUkrcVQyc3J1TlhWWlVCRklMTkUzN3BaYzNIV21jWVM5TjVvWGZKb0xMd1k1Q1FiRWRJakhJekE5K2lVTVh5NUt2UlNMZzRNNnlzMHVHN2l6eUxJam8vYkFGQ2pRTmoyMmwvdEphWkZKZ3ErR2xhTWpHSXpuRkVnY0RjOFE3LzBCckwxcWtkRnVSdmdFR04ybk5FQ2VhS3QweHBSbzVrbWNrTVpHSVB6RlMiLCJtYWMiOiJkYmViY2Q0MTk2N2FjYzI1OWNhMGJjMWNkMDM3NWEzMDIxZDU4Yjg2NThkMTQyNzM5NTIyN2RhMWMzYTI4OTMwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:13:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBNRnFYakZFdmY3dzJzMmFXaVc5ZUE9PSIsInZhbHVlIjoiWjQ4MHF1V3hMN2dCaWRTREh5WmJCT1lIQU95MTliV2dwYVEyMnJydFkxdWkzOUhySWZPL1pkcndaN2d0eVJ1QzVSdm5ENFByVkpsOWpvWWF0eG04VERrRHBPZjh3czk0M2F3cExsbk9JdHEwak5ZWXhNUEtwRThoZzUzSU1wVnZWNm9WK3I2aXlqUTllNlNFb2l0OUF0aEN1OWRTWVJtTXZXMU5kR2Z6QUZnQW43R2FmV1R1WHIrNVNya25kcWdrcEVOTE5WTFE2UnFMUWhsamZISjdDOG1VWFpmRmEzMk1ERnQ0TG04QUs2Kzl4U05nQnFUTDUrYTlxVytWSlphQnZCdTMyMmdybDFFdUFyckNCcTViOGZ2MDBUVFJmM21nUjhsU2krZHVwV1libUJkU0NKdkJNM3dvL3VVK3R1KytIUE5KRmZMbmw3NFJXZTJodFRzWkVmbFRYdmYzY3NCdGNNbXluakJYcUVDZkRPM09OeW1mTDNOK1BaaGlDVmxLZlVCdWZBRHhDTzRXSUEwMTBnb0FHemU3NCtRZUNwd0Z0blFtbWM1M3JEM0JYSEx6eXZ6S1JLTFdZeEJpQ3lzNFNiL0YydVRrY3ZzNmhvQ1draXFSakwySGV1TjVoZ0YzV25YUUIvYUtCODRqdGZWNEIzbC9wRk1KVCtVSlRrbnIiLCJtYWMiOiJmN2E4MTZjOGZlYmU0ZTgwZmEwNWZiYzg0NTEzYTE4ZWIxZjg0YThkZTRiNTE5YzI3NWM3NmFhZmMxNTRhNTE4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNlSW1POGJWcXBwRXNiYURHODdaeGc9PSIsInZhbHVlIjoiTnhkUzJ4UHdtdENXVFJwQWtSS1YvOEk1UEFKRE1qa1VYa2lWNDZxRnY4QzJHVTFSdll6U0RpTWxwcjBmK2phdjEwRm8wNWlWVTAzVldFellaR3dqNGd6VkJSVmFsUXpDQnFOVVRiYXdFQWl3ODVteWdsaW5nTFVRTVNGUm1xQU9WRXY3aFE4QVI3TDNiajJ6RE1lWWNyT0RkR2prcTJHVkEzOEZSY1lCNFlvRCswZDFJbXJzR1EzRzFaZGEwUlFWSnE2aWw4L1Z2TWc2YkJoV0J5MG5iVFVLaXBSZmI3cEJlMXo3Ri9hOXJjanZ1c1dJamFrcGUzZ21MaXBSYlY5SzVRV2djNUYvbnk5V0IrdzhWUFdmVEpzRU1TZEh6a3EyeHpvRGYvWjQ3a1ZVZ2ZXWEtyRkZ5cXpCdHMxSTdPaHU1V0tramN1UlV5Z0pSSDBxYklPQmVVTUkrcVQyc3J1TlhWWlVCRklMTkUzN3BaYzNIV21jWVM5TjVvWGZKb0xMd1k1Q1FiRWRJakhJekE5K2lVTVh5NUt2UlNMZzRNNnlzMHVHN2l6eUxJam8vYkFGQ2pRTmoyMmwvdEphWkZKZ3ErR2xhTWpHSXpuRkVnY0RjOFE3LzBCckwxcWtkRnVSdmdFR04ybk5FQ2VhS3QweHBSbzVrbWNrTVpHSVB6RlMiLCJtYWMiOiJkYmViY2Q0MTk2N2FjYzI1OWNhMGJjMWNkMDM3NWEzMDIxZDU4Yjg2NThkMTQyNzM5NTIyN2RhMWMzYTI4OTMwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:13:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836559432\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1080341400 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080341400\", {\"maxDepth\":0})</script>\n"}}