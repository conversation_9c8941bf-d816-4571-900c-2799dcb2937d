<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;

class LoginCustomizationHelper
{
    /**
     * Get the path of a login customization image
     *
     * @param string $imageName
     * @return string|null
     */
    public static function getImagePath($imageName)
    {
        $extensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
        
        if ($imageName === 'login_favicon') {
            $extensions = array_merge(['ico'], $extensions);
        }
        
        foreach ($extensions as $ext) {
            $filePath = public_path('storage/login_customization/' . $imageName . '.' . $ext);
            if (File::exists($filePath)) {
                return asset('storage/login_customization/' . $imageName . '.' . $ext);
            }
        }
        
        return null;
    }
    
    /**
     * Get all background images
     *
     * @return array
     */
    public static function getBackgroundImages()
    {
        $images = [];
        
        for ($i = 1; $i <= 3; $i++) {
            $imagePath = self::getImagePath('login_bg_image_' . $i);
            if ($imagePath) {
                $images[] = $imagePath;
            }
        }
        
        return $images;
    }
    
    /**
     * Get custom logo path
     *
     * @return string|null
     */
    public static function getCustomLogo()
    {
        return self::getImagePath('login_custom_logo');
    }
    
    /**
     * Get custom favicon path
     *
     * @return string|null
     */
    public static function getCustomFavicon()
    {
        return self::getImagePath('login_favicon');
    }
    
    /**
     * Delete old image files
     *
     * @param string $imageName
     * @return void
     */
    public static function deleteOldImages($imageName)
    {
        $extensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];
        
        if ($imageName === 'login_favicon') {
            $extensions = array_merge(['ico'], $extensions);
        }
        
        foreach ($extensions as $ext) {
            $filePath = public_path('storage/login_customization/' . $imageName . '.' . $ext);
            if (File::exists($filePath)) {
                File::delete($filePath);
            }
        }
    }
    
    /**
     * Ensure login customization directory exists
     *
     * @return void
     */
    public static function ensureDirectoryExists()
    {
        $directory = public_path('storage/login_customization');
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }
    }
    
    /**
     * Validate uploaded image
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $fieldName
     * @return array
     */
    public static function validateImage($file, $fieldName)
    {
        $maxSize = 20 * 1024 * 1024; // 20MB in bytes
        $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
        
        if ($fieldName === 'login_favicon') {
            $allowedMimes = array_merge($allowedMimes, ['image/x-icon', 'image/vnd.microsoft.icon']);
        }
        
        $errors = [];
        
        // Check file size
        if ($file->getSize() > $maxSize) {
            $errors[] = __('File size must be less than 20MB');
        }
        
        // Check MIME type
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            $errors[] = __('Invalid file type. Please upload a valid image file.');
        }
        
        // Check if file is actually an image
        if (!getimagesize($file->getPathname())) {
            $errors[] = __('File is not a valid image.');
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Process and save uploaded image
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $imageName
     * @return array
     */
    public static function saveImage($file, $imageName)
    {
        try {
            // Validate image
            $validation = self::validateImage($file, $imageName);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => implode(', ', $validation['errors'])
                ];
            }
            
            // Ensure directory exists
            self::ensureDirectoryExists();
            
            // Delete old images
            self::deleteOldImages($imageName);
            
            // Get file extension
            $extension = $file->getClientOriginalExtension();
            $fileName = $imageName . '.' . $extension;
            
            // Move file
            $uploadPath = public_path('storage/login_customization');
            $file->move($uploadPath, $fileName);
            
            return [
                'success' => true,
                'filename' => $fileName,
                'path' => asset('storage/login_customization/' . $fileName)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => __('Error uploading file: ') . $e->getMessage()
            ];
        }
    }
}
