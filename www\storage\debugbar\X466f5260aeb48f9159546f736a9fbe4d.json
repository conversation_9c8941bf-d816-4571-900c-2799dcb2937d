{"__meta": {"id": "X466f5260aeb48f9159546f736a9fbe4d", "datetime": "2025-06-06 20:42:30", "utime": **********.910347, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242549.297857, "end": **********.910394, "duration": 1.612536907196045, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1749242549.297857, "relative_start": 0, "end": **********.727438, "relative_end": **********.727438, "duration": 1.4295809268951416, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.727466, "relative_start": 1.4296090602874756, "end": **********.910398, "relative_end": 4.0531158447265625e-06, "duration": 0.18293190002441406, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761304, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.008570000000000001, "accumulated_duration_str": "8.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.824796, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.793}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.854603, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.793, "width_percent": 10.385}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8626869, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 71.179, "width_percent": 15.169}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.884766, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.348, "width_percent": 13.652}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv", "_previous": "array:1 [\n  \"url\" => \"http://localhost/profile?16=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1367646016 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1367646016\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-826393407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826393407\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1927451178 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927451178\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2024742435 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/profile?16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">_clck=xo2xva%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=8pzxed%7C1749242535108%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpDSEZ2TEk0KzNxZ09FVTFxelRVakE9PSIsInZhbHVlIjoicVFnVkZoa2xyZXBETnJITzdIOWY3WFpEZVVQb3hkalhNMXRpZzYyWm04aU9Od2hEcGtuRExpbTdjdHVwNHg2TXFxSENkZm80R0FQNFNrblNyYUZGUmVOcnhaU0x3TytXc0ZNUGV3dUdUWEhKbGZ3WkIrV3p0bTVZSlQ4YWs3S0NHTEdyUEFWbnJDQXJhSW0yK2lXeitFUlZEQVNtek5wV2lmS2ZueS9PUTBNLzZ0bmVtZkwyWmdySkc1UmRQTFlSbEZOVjNvSWxDbVFFZWFObDBZcVViWlcwaWVieEFURERkZU9KY1M3cTBoaEtaTi9PRzNsVXhwaVltc2hZbFJpUGNWR0huK0QvNUFDYXNTNlBmQUZzdDBlQjBGZGtaR0JoOWJCZWVPdldpMmxhdmQ4VHhKdm5NUXpXUng2SVRrRWdsaXNtdEQyRnYwQjkwK2lwdm8rMGwvZzZ6VGlDbG1XbzdiVkU1dFF3N1MvaW1VbGN5dGdCMG0vVlVoaXpLeVBBL3hpYjRoV3VNeGhDdlliNVlwNzBITGdkS2JnMG1UQzRDd2tCejdJakFkSzdZQmhjc1BaL290aHV2MUtadGxpQlV2SmpMZWZoZFY1M3lualhEUGdWbEVkdnR4Mkw0SER4dnJSVTJkQW4zRndMSE0rOGNRWTd2VTc1L2VUaUdib2oiLCJtYWMiOiI4YmIxYzg2YTUzMGVmNDIyNmZlZGFhZmEyNzNlZDdiMDlhOWM2MzUyZGEzM2QyOTdkZTVlODcyMzEyZDc4OWZkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5IaFNNbHQ1TVdHVWtoWU1FVndhQlE9PSIsInZhbHVlIjoiSTAySHNJNGgycGNXZ05HcHR1SGh6dzAvOWVOU0U1YmlrZ1ZpZ0UzRVFrRzlxVDFXS05TNUp2S1RsSHRva04zOXl0UHZTMW0vM2pxUUFBekwzdDkzaHAxM0hGMXRLcTA3QWlZN2d6Ui85azRrN0Fyc2xsc3ZkVHZnSVoxaVE5anRPV25sT3YyRDV5cUU3K0RBanNibjhtZXBOZ045TXA2c2liNGNPQ0VRZzVRanJVb3lZR0M5MHBzYzdPMHZ5RmVZSUNzOWE0L09LMjcxeWprRXRYRThiU2VFcnl3andWWGdTN1NoanlGN2I4Si9HZVZtbVA2bjh3Q1Y5L0dITTI1dXRkSWlMVk05bmI0ZTU0bm11b2NzNkIwazVrdGFPa09LRnlFTVNSUHBpRHRDcXZzazNNUmEwTFpJNDB4eTF2TmNWMzlvUjJPaEpyWnJ1cmJZRFFUUnZxMEZ2YUhqRmxJRjZYUG9IaTM4TCtBSlpaZXhERHlTVk5maWIxVEhOM3JyVWdVVUMvYWNQdm1zRXlVYkQyY3pqYStwYk1VUmpIQ1krY2xubE5uNE1oYnlHc0FYTzdWWERBK2NVSXlyekR6TllYSzgwUXBsV2gyaE1hd1RidEhrdmt4L0dRZFhoTWJtV0tPbGxxVzBDZS9OdmE3THRZelJrMWRpSWlDWEthVGwiLCJtYWMiOiI3ZmFjOWQ4ZTcwMjQwZDVlMDEzODA0ZWM5NmM2Y2IyYThhNTU1Y2M5NDM5NzU0NmMyZTI0M2Q0MTJjZjdiYjg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024742435\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1Mgwy0E1kOADJg4wSzllo3ylSTAx8J3JTYC2bXcn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1500142362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:42:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImsxenkzSlh0OEpvcDJ6R0JJcXdHdVE9PSIsInZhbHVlIjoic0tDeVZIUm9LTmdCQ0NZdldmYkszYzhwbGIzTkUwTTljNzNtM1Yra25CKzRyY2tCZ3BCVTZkMkNtRlloZ3l3ditpREZIa29nTWlGQTVvKzBRYm44dWJseCtLMm1ncjRUS2o3NHlDZjNPNEUxUmhVVXp6YVI0djc0RUF5NVhKODVhbFV3OE52dXdxYVpDNTRCRHVIKzNNaWxsaGM0UXk2YWNWMHlNWWtYN09RMG9JMjRHcHp3VlFDSmtDV0Nhb1VyQ1Ywak9hTHVwNnl2bmN6NnhJQVNSY3dwMERTZGZvcTh4UEZ5M1FTSklsRGhTdStUeHJ3S25weWNIOVFGcHpsVndSYm9tazVaNytUZkxOc3NGQmVWZGp2QXdMdXZ4N0hMVVRZRHZiNUNicUhnclREdXFHaWhUN2x5Z21nUlVOM01mUEhCeG50MGVBOGV0WUhMc1E5dmdOaWFqU1RxR20zY2U2UGJBZ2xlTFBNSVNJWE00Q2UxSFJkQVpZVlBQV2c3bXQybGQvbkZNb04wTHl2NmJRT0xpOUIvVXVLUThFYXk3SE45eGpvZG9XL2pPZHFSMjZTOTZyM0FXUExGaVVCRmVOTGsvWXlTUkRJUDZ1WTNhcnFVanlmWmhmbEtOYUx0Vi9Kd1hZcHhxQkpwRVpuYjMxU1p0NnNUTlJSZXZnaWMiLCJtYWMiOiI1MTI2NDA0MGJlMGI0YWY2NGVlZWRhODU4NzczYWRhNmMwM2ZiYWQxNTIzNDQwMGVhYmY3OGM2MmE4NGVmNjY2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:42:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhDdC9oemRjSDZvc2Jvd0x0R01RSlE9PSIsInZhbHVlIjoiZEFFUjdkejl6RVZaM1BFcktRU1pzQ0lyb2cyUUVheVNkVkE3TGtMY0FLaHcxSzkrRmo3RldXRDQ1VGdTMlBkZWpYTjBKcFVpZXNmMlYxTDBYNzRTNFlYVEJOSGhnTXBOWE5TbEQwT1Bhc1I1cUZqaTROQVMzUFI1ZVVuQWpqUkNiWEVrS1JyNWFjcDNkVU9FaWxPTVpxdHNpSEdnZlQzQytnbGhaeW1lNUZoUE1sN2pHYlV3b3dvbVlMeU1RejRkR3phM2xFL3AwNStEL1FDRGhKWGRXcDdLQmtOT1pQRlVRNlhvaG1WZzBMVFQ3a08vUHVWUTQ0cDk2VTBEV2FPd2Q3czhXc3lpRDhsT3dVQVhtMVpsdGJuaDV3UEhJdDNNVGRiNzVRTTUwQk14MVZIWlY0NGxVR2VycUFCKzQ3YXZIdWNWc2tiNGk5U1pDZmNNMjkvcGNoTUxHODhRdDQzS0FrRWYxU01MUDFiWEtuZGtvc2Z0QVdvK1h3VTRlNjhwdXdnTm5zRGFocjA5UDdnWDRibUNkUy9KVTlZMzFDMzJJWkNGZXhzbE03Y1J4Nk9DVjdRYnNoKzdTd094azRKUnRrRVBJZ0ZKYjdZNnA4TTJ3dGlWY0lKY1JYclRnTGlUVEl4enkxOWVudDhOdDlDVFdIRjhUMTlzZ1Z5U01zTDciLCJtYWMiOiIyNWU2NTJjOGMyN2FjNGMzNzYzNjg1NjNmZTdkMjYzYjE0NWFhZjEzMDkzY2NiOWRiMDcxMTcxMDBjNjFmY2YzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:42:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImsxenkzSlh0OEpvcDJ6R0JJcXdHdVE9PSIsInZhbHVlIjoic0tDeVZIUm9LTmdCQ0NZdldmYkszYzhwbGIzTkUwTTljNzNtM1Yra25CKzRyY2tCZ3BCVTZkMkNtRlloZ3l3ditpREZIa29nTWlGQTVvKzBRYm44dWJseCtLMm1ncjRUS2o3NHlDZjNPNEUxUmhVVXp6YVI0djc0RUF5NVhKODVhbFV3OE52dXdxYVpDNTRCRHVIKzNNaWxsaGM0UXk2YWNWMHlNWWtYN09RMG9JMjRHcHp3VlFDSmtDV0Nhb1VyQ1Ywak9hTHVwNnl2bmN6NnhJQVNSY3dwMERTZGZvcTh4UEZ5M1FTSklsRGhTdStUeHJ3S25weWNIOVFGcHpsVndSYm9tazVaNytUZkxOc3NGQmVWZGp2QXdMdXZ4N0hMVVRZRHZiNUNicUhnclREdXFHaWhUN2x5Z21nUlVOM01mUEhCeG50MGVBOGV0WUhMc1E5dmdOaWFqU1RxR20zY2U2UGJBZ2xlTFBNSVNJWE00Q2UxSFJkQVpZVlBQV2c3bXQybGQvbkZNb04wTHl2NmJRT0xpOUIvVXVLUThFYXk3SE45eGpvZG9XL2pPZHFSMjZTOTZyM0FXUExGaVVCRmVOTGsvWXlTUkRJUDZ1WTNhcnFVanlmWmhmbEtOYUx0Vi9Kd1hZcHhxQkpwRVpuYjMxU1p0NnNUTlJSZXZnaWMiLCJtYWMiOiI1MTI2NDA0MGJlMGI0YWY2NGVlZWRhODU4NzczYWRhNmMwM2ZiYWQxNTIzNDQwMGVhYmY3OGM2MmE4NGVmNjY2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:42:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhDdC9oemRjSDZvc2Jvd0x0R01RSlE9PSIsInZhbHVlIjoiZEFFUjdkejl6RVZaM1BFcktRU1pzQ0lyb2cyUUVheVNkVkE3TGtMY0FLaHcxSzkrRmo3RldXRDQ1VGdTMlBkZWpYTjBKcFVpZXNmMlYxTDBYNzRTNFlYVEJOSGhnTXBOWE5TbEQwT1Bhc1I1cUZqaTROQVMzUFI1ZVVuQWpqUkNiWEVrS1JyNWFjcDNkVU9FaWxPTVpxdHNpSEdnZlQzQytnbGhaeW1lNUZoUE1sN2pHYlV3b3dvbVlMeU1RejRkR3phM2xFL3AwNStEL1FDRGhKWGRXcDdLQmtOT1pQRlVRNlhvaG1WZzBMVFQ3a08vUHVWUTQ0cDk2VTBEV2FPd2Q3czhXc3lpRDhsT3dVQVhtMVpsdGJuaDV3UEhJdDNNVGRiNzVRTTUwQk14MVZIWlY0NGxVR2VycUFCKzQ3YXZIdWNWc2tiNGk5U1pDZmNNMjkvcGNoTUxHODhRdDQzS0FrRWYxU01MUDFiWEtuZGtvc2Z0QVdvK1h3VTRlNjhwdXdnTm5zRGFocjA5UDdnWDRibUNkUy9KVTlZMzFDMzJJWkNGZXhzbE03Y1J4Nk9DVjdRYnNoKzdTd094azRKUnRrRVBJZ0ZKYjdZNnA4TTJ3dGlWY0lKY1JYclRnTGlUVEl4enkxOWVudDhOdDlDVFdIRjhUMTlzZ1Z5U01zTDciLCJtYWMiOiIyNWU2NTJjOGMyN2FjNGMzNzYzNjg1NjNmZTdkMjYzYjE0NWFhZjEzMDkzY2NiOWRiMDcxMTcxMDBjNjFmY2YzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:42:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500142362\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1233482952 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27Ue0B46bx5huuKFdrJ4UScy6PtqGV2nFtAU0nbv</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/profile?16=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1233482952\", {\"maxDepth\":0})</script>\n"}}