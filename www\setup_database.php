<?php
/**
 * إعداد قاعدة البيانات البعيدة
 * Remote Database Setup
 */

if ($_POST) {
    $host = $_POST['host'] ?? '';
    $port = $_POST['port'] ?? '3306';
    $database = $_POST['database'] ?? '';
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // قراءة ملف .env الحالي
    $envFile = __DIR__ . '/.env';
    $envContent = file_get_contents($envFile);
    
    // تحديث إعدادات قاعدة البيانات
    $envContent = preg_replace('/DB_HOST=.*/', "DB_HOST={$host}", $envContent);
    $envContent = preg_replace('/DB_PORT=.*/', "DB_PORT={$port}", $envContent);
    $envContent = preg_replace('/DB_DATABASE=.*/', "DB_DATABASE={$database}", $envContent);
    $envContent = preg_replace('/DB_USERNAME=.*/', "DB_USERNAME={$username}", $envContent);
    $envContent = preg_replace('/DB_PASSWORD=.*/', "DB_PASSWORD={$password}", $envContent);
    
    // حفظ الملف
    if (file_put_contents($envFile, $envContent)) {
        $success = "تم تحديث إعدادات قاعدة البيانات بنجاح! / Database settings updated successfully!";
    } else {
        $error = "فشل في حفظ الإعدادات / Failed to save settings";
    }
}

// قراءة الإعدادات الحالية
function loadEnv($file) {
    $env = [];
    if (file_exists($file)) {
        $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                list($key, $value) = explode('=', $line, 2);
                $env[trim($key)] = trim($value, '"\'');
            }
        }
    }
    return $env;
}

$env = loadEnv(__DIR__ . '/.env');
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        button { background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .current-settings { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إعداد قاعدة البيانات البعيدة</h1>
        <h2>Remote Database Setup</h2>
        
        <?php if (isset($success)): ?>
            <div class="success"><?= $success ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="error"><?= $error ?></div>
        <?php endif; ?>
        
        <div class="info">
            <strong>ملاحظة:</strong> تأكد من أن قاعدة البيانات البعيدة تسمح بالاتصالات الخارجية<br>
            <strong>Note:</strong> Make sure your remote database allows external connections
        </div>
        
        <div class="current-settings">
            <h3>الإعدادات الحالية / Current Settings:</h3>
            <strong>Host:</strong> <?= htmlspecialchars($env['DB_HOST'] ?? 'Not set') ?><br>
            <strong>Port:</strong> <?= htmlspecialchars($env['DB_PORT'] ?? 'Not set') ?><br>
            <strong>Database:</strong> <?= htmlspecialchars($env['DB_DATABASE'] ?? 'Not set') ?><br>
            <strong>Username:</strong> <?= htmlspecialchars($env['DB_USERNAME'] ?? 'Not set') ?><br>
        </div>
        
        <form method="POST">
            <div class="form-group">
                <label for="host">عنوان الخادم / Database Host:</label>
                <input type="text" id="host" name="host" value="<?= htmlspecialchars($env['DB_HOST'] ?? '') ?>" placeholder="example.com or IP address" required>
            </div>
            
            <div class="form-group">
                <label for="port">المنفذ / Port:</label>
                <input type="text" id="port" name="port" value="<?= htmlspecialchars($env['DB_PORT'] ?? '3306') ?>" placeholder="3306" required>
            </div>
            
            <div class="form-group">
                <label for="database">اسم قاعدة البيانات / Database Name:</label>
                <input type="text" id="database" name="database" value="<?= htmlspecialchars($env['DB_DATABASE'] ?? '') ?>" placeholder="database_name" required>
            </div>
            
            <div class="form-group">
                <label for="username">اسم المستخدم / Username:</label>
                <input type="text" id="username" name="username" value="<?= htmlspecialchars($env['DB_USERNAME'] ?? '') ?>" placeholder="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور / Password:</label>
                <input type="password" id="password" name="password" value="<?= htmlspecialchars($env['DB_PASSWORD'] ?? '') ?>" placeholder="password">
            </div>
            
            <button type="submit">💾 حفظ الإعدادات / Save Settings</button>
        </form>
        
        <hr style="margin: 30px 0;">
        
        <div style="text-align: center;">
            <a href="test_db_connection.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                🔍 اختبار الاتصال / Test Connection
            </a>
        </div>
    </div>
</body>
</html>
