{"__meta": {"id": "Xb083af4158e7023fa343baba0a9e342e", "datetime": "2025-06-06 19:34:43", "utime": **********.971098, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238482.224601, "end": **********.971126, "duration": 1.7465250492095947, "duration_str": "1.75s", "measures": [{"label": "Booting", "start": 1749238482.224601, "relative_start": 0, "end": **********.767282, "relative_end": **********.767282, "duration": 1.5426809787750244, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.76731, "relative_start": 1.5427088737487793, "end": **********.971129, "relative_end": 2.86102294921875e-06, "duration": 0.20381903648376465, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00993, "accumulated_duration_str": "9.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.890255, "duration": 0.00614, "duration_str": "6.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.833}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.921835, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.833, "width_percent": 9.668}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.929097, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 71.501, "width_percent": 12.487}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.94967, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.988, "width_percent": 16.012}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-622694677 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-622694677\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1180070733 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1180070733\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-35901291 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35901291\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-971173060 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238462862%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InprUWVETzdwaXB1NmdmNnQyN1UvUmc9PSIsInZhbHVlIjoiSEUzTmpVeTY1TTlUZ0Uxalo5SzV3ZGpYZmtFSTBnMEpBMEpEY1RSZUJGaXhQbmQwaFRia1Noem1aaGhuQ3EvbXdzdXRKNjFKWHArV0xIZjhnMjZvRU5LdW5NVDFINEEzdnY4N3pUU2pET2tncUZXWGZuWk1qNjRJeXUyQ3AzTVVnNXJlWTZIMmtrV0ZxWi9MMWZYc3VweUkvL3hWaEdXNnNUaTZka1NTdVJ1VlN4RnZJZHFLb2Zjemo1SkhOeFpFNVRjOTd5N01ibDd1RmRtZnRxMlN2VFdOcCtkTW1idkR3UE4xcmpZakM1KzRZMG9sUHRoZTVDSlpNYjc5TWJhU2VHVDh0SStpdytlYmFqcEJ0NjR4eSttaVJwazV4V0pTL2xkV1FtLytEVFhOVlIzbDZRc1lNZy96d1Bna1JsTzA4R1A5UEVHZTl4MWxZOVh6cUo1SStNSURQalVPM1lhaGhxOElPNDY0V3JvM0huWm95ZE9EN05TUmdZRTZ3bVFJU1lPT0p0QzZMRklIUHNHRnlVOThsS0NjL2Z2QnhrZTlNaklrRE1FWkg5NVdKQmdTbjBiNFFRQUlBZHU5UnFTV0ZtUGhFUDF1ajJzblVjUDNZT2lLQlZjOVFScFdrZGFrWVdHZTg1Mlpxay93SkczR1VLQmRpK2RWWG9xbmlOTmsiLCJtYWMiOiJkZGFkZTllYjYzNGY2ZGM2NTVkNTAzMGYyZjc1YmNhM2I1MTk4NDU2MjgyODY0MDk1ODdhY2Q3YWJjOTZkZDRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhMSjkwYkVtVW5UUnFSNVovYWZMVHc9PSIsInZhbHVlIjoiSldSbVNzcjU5Y0tqTWNVQ00renkxUjlsblRoYmlsK2lXOGZUTG5sUmZzV0N6V0FXeG8xMEFSUnpjY2V5Y1NMOTZPeVJ5YS9lZ1RQMHBIb3Z3YlBTUkFsUndFTjhzR2lXOFRCNTNFa00wakNGaE0zZ0wyeVlETktSbGR2UFRjRVNnRGJEUnlXcTErUHNxcFFtTWgrUEdVVnVMdUlyK0duczN3VjZObGtaalRLQU0xZ1EwRzNnQzIrVFp2djNwWXR1RHpyRUZDT0MvZFBDclA1bUYzODhuWWFGLzZobnltV1ZCMW5TRG94MWxXZjFPS3VJTHovOVZEVi9ETGR5RjRwVVNZV3BkRU5mUWlFUjdtS1dTVDFsd1RXQnNpbXg0dVBKN1l4cjJKOWFsSlBlOUh6ZWM3eHRoMWZBSGVOMkVsdTNucWRkTVdYTnZsMW5WOFpXdndWNG16cHZOSHlneXI1YkRjSGhKNHlGRFp1dy8xSUJ0SkRPNjhTc1ZWaGtDT0ZPZGgyREM4T1E1QXJiWVExNklscTdQM1AzWkZidWNHNzlWNmYxRFBpWWRha0lDNzlFcCsxbHQ5K2xqdlNMT0MzVUtaYWkrcFZKaU8zelJuM2RreWEzbnRmSTRjM0kvMWRYcHFDRlAwbnBIRDVhMVB2bEdFUVNyZy9mWmN6V0lIMHAiLCJtYWMiOiI0YzIzYzg3OWQxNDkwMTdlY2ExYzZjZTlhODU5Y2EwZjcyNWQ4ZmFkNTVmNDc3NTIzY2IyZmEwNGU4ZGE0ZmRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971173060\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1333035928 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333035928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-452972021 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:34:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Img3Qld3bzdzWUxueVRUTWJkOUpmaEE9PSIsInZhbHVlIjoiQ3ZFRWZSdjBtSGpOSUU2aFhKekNxVnQvT2Z3V0d2aVFDanZITVFqaFNwK2hRMythVlF6a0p6WmlLZ1o0eG9pRlVuS3ZTSkRBV1dwOWloZ0ZnVisyQTFseSttVGxCVERQU0ZUZWxCQTdOSGRXUDZDN3ljRGl2M1gxN1F5alBGSHZSa243SkE2TFBwQ0hYenpDSFU0dE5PODhNUXkvaDFmdkE0VjUzU2NvRFBEYzNTMFRmbVg0NWQwdVFtSkMvNTcxQTBIVURjUTVoa21aUGgyYU9SdU45cmZhWG9vZFk3SGQzZHBIT3BSVDkvYmc1RXYxQjc5NFJLU1JEZkxjWFZjQjMySGdCd1h4Ty9hdE9MTFN1NDVUelorc2JsNm5oK1hDRktJQmU5OXJDYmJ5aVpqRkFXWk1zc1o1YS9yQWJSTUhzajJKay90N1lNQjZVd1g1L0ZRZnVwR0tSYmlSZGR2VEdZeURlNnpST2JmM0kzdlJ3S2JqZTc4Wkcwc1Zlc1hLYVQ3RWhLZWRzdy83cnp0UWU1NVNPM01JdlF5dUg3M2NNUTU2bTJ1U0E3R1lCOGpRWG5yVE9ZaGNUdTlOUXB6VlUxa2xQcDZJeDVJdlNHRzJmOERRM1ZuYXpnSklHcVNtaHBJbDVNQnVSRjlxRXBlSC9ML3I2YmFRYnpDTUNuQzAiLCJtYWMiOiJlOGRlYWU2MmRiYjA0ZTAxMTZmYjQ1MTdmZmFlNjFkMDU2MmUzM2U5MjgxYmQ0YWI1MWM3ZDIxZjlhMWE3YmQ0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjF6cXRwdTNHbUwyMU4veVdLcmVBdVE9PSIsInZhbHVlIjoicXFocGx0ZFBkYlIxU0lXYnpWRnVkc1NLcnNKZVFuWGFLREQ3RWFGcTYwZzB6MjVkWEpvMW5ubktCWnE0cXc5RG0yL1ZlTjcxVVhMcElhUTVEajM0SEdvSVphbnprT1k1UER6RjNPSmViN0t4SFQ0WXovdTQwT3U5c25FZXZrVk42cisvTk0zZTJod004bGNYcHdLVU1FRm8vTVh5Mm1IUTNTaW9BOVRNU3dDZU1NclNlMTVPcFVROXNyK1VwSDJlTi9IbXNZS0cxQ3NXT2dlaWVTbDg5V2lQa0k5a1lKeGVvUEpiUWRhV2tMbkFmY3RGdjEvdG1OTjBjOHFEK2N4aFBzL2k2NmpZMHYzeUMxb0FRb2FjYy9uckRZODIrK01DSlNBSEpQaVhJRmNycEdyUlUwQk9xSlRLb2VNNTRBaDU1L0pQTUxmMkdhNjlXRTdWV3p0M0dFYVZ1QmpKMnptVk14WktidUtQRXlPcGJaV3dRWlBycXVxQTB1MHlvUEZEVFErQkk1Qk40SHhlYmZpakJMUGM3YlIwZTg1bGJvaEJ2ZzJGdllJbXllZStQM2FPSDZ4a1JKTDFhRWhHWW0wVW9UdzZXWi9uN00vMm5wM1UwRWd6Ri9xbExyeENvU21lQnl3WjRqZnFpaE5BUEFibitERStCd1JwL25jdk9WNFMiLCJtYWMiOiJhY2RhMGU3MDEyYzEzMjNlZjk3ODU4ZjUyMGExYTRlNDk5NmM0M2EyNTdiOTk3NjQ1NzFhYzE0ZDg0OGY0ZWYyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:34:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Img3Qld3bzdzWUxueVRUTWJkOUpmaEE9PSIsInZhbHVlIjoiQ3ZFRWZSdjBtSGpOSUU2aFhKekNxVnQvT2Z3V0d2aVFDanZITVFqaFNwK2hRMythVlF6a0p6WmlLZ1o0eG9pRlVuS3ZTSkRBV1dwOWloZ0ZnVisyQTFseSttVGxCVERQU0ZUZWxCQTdOSGRXUDZDN3ljRGl2M1gxN1F5alBGSHZSa243SkE2TFBwQ0hYenpDSFU0dE5PODhNUXkvaDFmdkE0VjUzU2NvRFBEYzNTMFRmbVg0NWQwdVFtSkMvNTcxQTBIVURjUTVoa21aUGgyYU9SdU45cmZhWG9vZFk3SGQzZHBIT3BSVDkvYmc1RXYxQjc5NFJLU1JEZkxjWFZjQjMySGdCd1h4Ty9hdE9MTFN1NDVUelorc2JsNm5oK1hDRktJQmU5OXJDYmJ5aVpqRkFXWk1zc1o1YS9yQWJSTUhzajJKay90N1lNQjZVd1g1L0ZRZnVwR0tSYmlSZGR2VEdZeURlNnpST2JmM0kzdlJ3S2JqZTc4Wkcwc1Zlc1hLYVQ3RWhLZWRzdy83cnp0UWU1NVNPM01JdlF5dUg3M2NNUTU2bTJ1U0E3R1lCOGpRWG5yVE9ZaGNUdTlOUXB6VlUxa2xQcDZJeDVJdlNHRzJmOERRM1ZuYXpnSklHcVNtaHBJbDVNQnVSRjlxRXBlSC9ML3I2YmFRYnpDTUNuQzAiLCJtYWMiOiJlOGRlYWU2MmRiYjA0ZTAxMTZmYjQ1MTdmZmFlNjFkMDU2MmUzM2U5MjgxYmQ0YWI1MWM3ZDIxZjlhMWE3YmQ0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjF6cXRwdTNHbUwyMU4veVdLcmVBdVE9PSIsInZhbHVlIjoicXFocGx0ZFBkYlIxU0lXYnpWRnVkc1NLcnNKZVFuWGFLREQ3RWFGcTYwZzB6MjVkWEpvMW5ubktCWnE0cXc5RG0yL1ZlTjcxVVhMcElhUTVEajM0SEdvSVphbnprT1k1UER6RjNPSmViN0t4SFQ0WXovdTQwT3U5c25FZXZrVk42cisvTk0zZTJod004bGNYcHdLVU1FRm8vTVh5Mm1IUTNTaW9BOVRNU3dDZU1NclNlMTVPcFVROXNyK1VwSDJlTi9IbXNZS0cxQ3NXT2dlaWVTbDg5V2lQa0k5a1lKeGVvUEpiUWRhV2tMbkFmY3RGdjEvdG1OTjBjOHFEK2N4aFBzL2k2NmpZMHYzeUMxb0FRb2FjYy9uckRZODIrK01DSlNBSEpQaVhJRmNycEdyUlUwQk9xSlRLb2VNNTRBaDU1L0pQTUxmMkdhNjlXRTdWV3p0M0dFYVZ1QmpKMnptVk14WktidUtQRXlPcGJaV3dRWlBycXVxQTB1MHlvUEZEVFErQkk1Qk40SHhlYmZpakJMUGM3YlIwZTg1bGJvaEJ2ZzJGdllJbXllZStQM2FPSDZ4a1JKTDFhRWhHWW0wVW9UdzZXWi9uN00vMm5wM1UwRWd6Ri9xbExyeENvU21lQnl3WjRqZnFpaE5BUEFibitERStCd1JwL25jdk9WNFMiLCJtYWMiOiJhY2RhMGU3MDEyYzEzMjNlZjk3ODU4ZjUyMGExYTRlNDk5NmM0M2EyNTdiOTk3NjQ1NzFhYzE0ZDg0OGY0ZWYyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:34:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452972021\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1897929676 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897929676\", {\"maxDepth\":0})</script>\n"}}