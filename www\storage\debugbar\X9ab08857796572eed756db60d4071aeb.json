{"__meta": {"id": "X9ab08857796572eed756db60d4071aeb", "datetime": "2025-06-06 19:36:45", "utime": **********.712409, "method": "POST", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238604.377781, "end": **********.712455, "duration": 1.3346741199493408, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1749238604.377781, "relative_start": 0, "end": **********.520022, "relative_end": **********.520022, "duration": 1.1422410011291504, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.520044, "relative_start": 1.1422631740570068, "end": **********.71246, "relative_end": 5.0067901611328125e-06, "duration": 0.19241595268249512, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44988944, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=75\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:75-235</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01761, "accumulated_duration_str": "17.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6546328, "duration": 0.016050000000000002, "duration_str": "16.05ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:78", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=78", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "78"}, "connection": "ty", "start_percent": 0, "width_percent": 91.141}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.685064, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:81", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=81", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "81"}, "connection": "ty", "start_percent": 91.141, "width_percent": 8.859}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"status\"\n  ]\n  \"new\" => []\n]", "status": "Your Account is disable from company.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">140</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">XSRF-TOKEN=eyJpdiI6IlYyTDc5YTE3OEVNckZXS09tT3lQY2c9PSIsInZhbHVlIjoidHZUTkFueEJWdEU3dzczanYva1FkRWtRNk9jQ0Y1VmsrOXlNSjc5dW5lMVFRZndlSjZFYi9lMFdObUxXSjBnUnlhOGR2NW9pb29MWXlKSk1pZ1ZkbGpkb1ByYjZ2dWhXcUlwUmVUc0JIS09ySUUydWd5RlptRjlSQ1RsVW9tWWk5YzFYYm9YUndWblBydlhkR2hBNFJkRzlrNEpuTXhyUkhSRzEzbGduSW9UWW5lTmdLZE5uMzBPTHlHSVFJbjFObThlRGxoWDdtOGtzMmRScld1aHRLU2lLbVFvY3dacmRyV2NLdlV3d2hpeEhVWXhLdnNVTkZwMjhZbmJkQnhpaElSWlA3L0ptOE5VSG10YllVWkczQk5mYnJDUldJTXJTVGxhbVZDcWNiMjJOV29pQ25nUXN2VUVBVmNpTk4rODd2amNEWXU4aUhvcjNnc1JnS2dWbzhDd1hoMGRoZjdUVmlsN2M4aUYxYUhTbE9oOEtmY2FSaGVRYzRqMmdCYS9IMllsc3NFTGpDMXN0TDhITUgzNzBzOVYvNHcxQjFVUUU1aUdabWF1K3AzTUI5SDNKbnRhcEZYK1FmdjFtcVZ6ZWxybFRNalkwWEdNbVNBT0I2d3NPMFlGMFFVaFA2ejF0OHhmemI1YTNJOE5iVlBZSzNvaXd5Ry9kZ0Q4WXRWMTgiLCJtYWMiOiI0YWNkOWM0ZjIwODFkN2Q5OWY1ZjliZjU5NGExMDZkNmRlYmNjZjJlMDVkYmRkYTMzZDFmYzZmODIwOTQzZDUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklXWEJkV0oyUHlZVHJab08wc0xUanc9PSIsInZhbHVlIjoiY05jVXp2RnROd3AxZDdudVdKYXV6NkxDNDdoeVNqV1VJVWF2TitTcnRhWDljVWVPODg2VnhGcGFlakRUMndRamhHMmZBMmNOQWpqMUdweEJ0aTRZRVpzcEhLYmtPZ0RrTEhnOFdJSmhGV3dVU21oZmN2K3pRS251VzVtMndXZnByaXpzODB6cW8raDJ5SDRpdGZBeVlnWU50M3VycGQvL0dRaUxWNW1zZzJ2SjcrNDdPR2wrem43eXBQbjJTOVdORHZKeitISHVaUnNjK01BT2NnRVpqT3hqUFFWMWlzSWpmZ29qSzVHZThuK3ZHZ2oySGVoeE9KRjZKRlNKOWoyNjRydjhKN25XckZGQVFBUWhyZEo3NDNyRjFVYzVLOUxyZEZ4ME1JRDNINkFVb1Zha3duUW9sV256WTYvdFBHK1VBdDF4d0pQNDc4WUdwLzBtaDZuTXorS3V1akloK0lpOEVnMjdEWFpFbEVWNkd0RURWT1Y0ZFF4cVR5bGRVa3VwdWZVR3lQVmpXVFpPakxnNFpEVnBxUExBQStZUEQ1bkRNL3lIYTROZUVncHh0Q2dzdlN4WS9RM3NOQi9ha213TzRPTVhHTGtHRGJvQ1NuWjc2bzJlZGQ4ZmxnOHVoaUU5UjR4Q25meEd3MWF5WjFsT1dGcWR1NGYzKzh6cW5DcnkiLCJtYWMiOiI4MjcyMGQzODVmMjg0MWYxZGQ5YjQ0NTQwYTFhMmVjMDg2MzNmYTVlZDRjZjBiNzU0MGUwOWM1ZTBmYzcxYjliIiwidGFnIjoiIn0%3D; _clck=1jor13%7C2%7Cfwj%7C0%7C1983; _clsk=151odr7%7C1749238592944%7C1%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1884500191 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:36:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZEQmRzRXI2UUdxQ3FVcnphR2V6V0E9PSIsInZhbHVlIjoiMEh2bUFXaWNIYVRFRUlLU1FFZHd1SjRueWtpK3EvbUZwSXNvTytlYlU1bVdtWFJCaUlMeDQ0VVFZbSthTitpcnpKam9oUE1vMGVJRjg3M2IyTE5QTE9Hdm12Vzd2allUNTVINXpsd1Q5TWJZVUZHUWJjZWtpUDFaZUZyQWV5TVBnVjNCbVF6QlExODBRU0kzVWJDNi9aeDdYaTBXVlVTUnpRMDZaRncvWUxLZGpFZk5kYU1CTjZnTDR5Zzd6NW9TRUhEU0EwOHlxRWU5OS8ydmQza0Q4dzl0cFRPTnMrRlpiV2NxS3JmcHp2MFB6NWFWLzY5NGJTaEErSSttMDVvcnpkMzdPVTU5bVoxQS9HYXo0cXdEamtmb2lsNmx5c3BrRzJDdDkycFBDMGVYK3V3K3QwY3pGelhoRWsxejFmZjhzTGYzUXcwY01oRTczT1huak55Mm9MdDRMTnJORi9NSDdReHc1TS8reXR1RVI5QjQrZVc1Y2hUVGVzUnk0WE1BTXBMVURjNTcyV2Z2bFY5U3hKWU5CclBhbkxidm5GWGdyWkZLZkNQNHpMeGRaaHlrR3Avbk9FN1BMcUttb0t3blFxWVRUL0pZdU1TdWZ5MW5YYTVMYmU3OUxISnYwU2N5VUJad055ZG4xU0xXVWxnanVtUWw1S2YvNlp2MGFFMjMiLCJtYWMiOiJhMWM1MzNlM2VmZTFmZDc5NDYxNWVjY2I3NTc4NmFkZDJiZmFmMjQyYzA2OWYyZTZmNjY2YzRhNDJjNWU4ODE0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:36:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilh4U3FuWE9KWGYxb2tTb2tZcWd5K1E9PSIsInZhbHVlIjoibDczeWNnL3BmNHY3ckYvaUpITTZoaW5UcTRtMS9wSE1YOEhsMEpTWE5sTEJVVGszaWF6WnVyVWx6RVhOYkdITTJGQ0tqeTFVaTcxUVhjYWJQb09uWVRSMzRpcVYyWGEyeENDOEE2TEpVZk1wdHc3bDhjakUwZmVDVTdzSWFyZTlyVEN4S1dXYmU4enpEOTZTWWd3S2VsMWJraHZ5ZEtna1FseExPQkpic1dXSjBFMkcxaHF4THJLcjNQWGxnM1BNbTdHS2Jvc3FHNlZjMFNLaWFYMm1jbkg2cTJFRGdkNDU5a2NIWEVSVFlrMU90NEdkNFpOWEtUWFlsVmYxdlhkM01haVo4SWkrbnEyWFpsSExTaEtjYTk4SnUzR3VpSExCaUhQalhLcHY3WlB3aFRHcXJqZWJwQ3hlSHRxWVZSb3NtOHJ6dWI1Ull5emYxbUdPSGJ2MnljQVNTbmdLcU1VTVltbys0SnZDV28yNkdJWW9vWjgyWmdobWpvUVdUTUNkUXYxbnBiZi9ueHFLT0QvbmRiK1dTN3NMbDVoRU91L1pBSzJkeHFISzZhU2pFaGhUQlFxSVdPazFHc3ZiUGZpMDZFQjJFQ2d2d2ZXN0lBb1dWRHZTMUdNUFFRSURTZlVZOWlhN3pTbkRwNGxFRldPeUJ2YlJZdXJyaXU3YjQ4VUMiLCJtYWMiOiI1NTUxYzc1OWUxMTMxNDMwOTRkNWYzNjEzMTlhZTAzOGFhNTI0MDVkYzJmYzQ0YWEzY2E3YjgxOTMyNjJmMGFiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:36:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZEQmRzRXI2UUdxQ3FVcnphR2V6V0E9PSIsInZhbHVlIjoiMEh2bUFXaWNIYVRFRUlLU1FFZHd1SjRueWtpK3EvbUZwSXNvTytlYlU1bVdtWFJCaUlMeDQ0VVFZbSthTitpcnpKam9oUE1vMGVJRjg3M2IyTE5QTE9Hdm12Vzd2allUNTVINXpsd1Q5TWJZVUZHUWJjZWtpUDFaZUZyQWV5TVBnVjNCbVF6QlExODBRU0kzVWJDNi9aeDdYaTBXVlVTUnpRMDZaRncvWUxLZGpFZk5kYU1CTjZnTDR5Zzd6NW9TRUhEU0EwOHlxRWU5OS8ydmQza0Q4dzl0cFRPTnMrRlpiV2NxS3JmcHp2MFB6NWFWLzY5NGJTaEErSSttMDVvcnpkMzdPVTU5bVoxQS9HYXo0cXdEamtmb2lsNmx5c3BrRzJDdDkycFBDMGVYK3V3K3QwY3pGelhoRWsxejFmZjhzTGYzUXcwY01oRTczT1huak55Mm9MdDRMTnJORi9NSDdReHc1TS8reXR1RVI5QjQrZVc1Y2hUVGVzUnk0WE1BTXBMVURjNTcyV2Z2bFY5U3hKWU5CclBhbkxidm5GWGdyWkZLZkNQNHpMeGRaaHlrR3Avbk9FN1BMcUttb0t3blFxWVRUL0pZdU1TdWZ5MW5YYTVMYmU3OUxISnYwU2N5VUJad055ZG4xU0xXVWxnanVtUWw1S2YvNlp2MGFFMjMiLCJtYWMiOiJhMWM1MzNlM2VmZTFmZDc5NDYxNWVjY2I3NTc4NmFkZDJiZmFmMjQyYzA2OWYyZTZmNjY2YzRhNDJjNWU4ODE0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:36:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilh4U3FuWE9KWGYxb2tTb2tZcWd5K1E9PSIsInZhbHVlIjoibDczeWNnL3BmNHY3ckYvaUpITTZoaW5UcTRtMS9wSE1YOEhsMEpTWE5sTEJVVGszaWF6WnVyVWx6RVhOYkdITTJGQ0tqeTFVaTcxUVhjYWJQb09uWVRSMzRpcVYyWGEyeENDOEE2TEpVZk1wdHc3bDhjakUwZmVDVTdzSWFyZTlyVEN4S1dXYmU4enpEOTZTWWd3S2VsMWJraHZ5ZEtna1FseExPQkpic1dXSjBFMkcxaHF4THJLcjNQWGxnM1BNbTdHS2Jvc3FHNlZjMFNLaWFYMm1jbkg2cTJFRGdkNDU5a2NIWEVSVFlrMU90NEdkNFpOWEtUWFlsVmYxdlhkM01haVo4SWkrbnEyWFpsSExTaEtjYTk4SnUzR3VpSExCaUhQalhLcHY3WlB3aFRHcXJqZWJwQ3hlSHRxWVZSb3NtOHJ6dWI1Ull5emYxbUdPSGJ2MnljQVNTbmdLcU1VTVltbys0SnZDV28yNkdJWW9vWjgyWmdobWpvUVdUTUNkUXYxbnBiZi9ueHFLT0QvbmRiK1dTN3NMbDVoRU91L1pBSzJkeHFISzZhU2pFaGhUQlFxSVdPazFHc3ZiUGZpMDZFQjJFQ2d2d2ZXN0lBb1dWRHZTMUdNUFFRSURTZlVZOWlhN3pTbkRwNGxFRldPeUJ2YlJZdXJyaXU3YjQ4VUMiLCJtYWMiOiI1NTUxYzc1OWUxMTMxNDMwOTRkNWYzNjEzMTlhZTAzOGFhNTI0MDVkYzJmYzQ0YWEzY2E3YjgxOTMyNjJmMGFiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:36:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884500191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Your Account is disable from company.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}