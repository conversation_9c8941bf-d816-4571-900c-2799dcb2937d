{"__meta": {"id": "X4982dc677a31127e8bc96e1f37be543f", "datetime": "2025-06-06 19:18:07", "utime": **********.109538, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.545935, "end": **********.109579, "duration": 1.****************, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": **********.545935, "relative_start": 0, "end": **********.866502, "relative_end": **********.866502, "duration": 1.****************, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.866525, "relative_start": 1.****************, "end": **********.109584, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02922, "accumulated_duration_str": "29.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.975753, "duration": 0.02581, "duration_str": "25.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.33}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0385008, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.33, "width_percent": 4.449}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.078439, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.779, "width_percent": 7.221}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1oxw2al%7C1749179613444%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVtVnNCOHNFTW8wTithQzlmeThKSlE9PSIsInZhbHVlIjoiS0RXUHFOclNMZWFOU0ljWm0vNXFMdGZKSDVwMEV2dlZRaitwN2xFUXhsYkx2c0t2cUxnUXdMeER4WTFERzg4ei94d0xucTNtVTcwb1pWcVFhNXliSjFFeVZXSk00Uldma2tSYjIya3g3MW9SaWdtd1UwQTFNZ25jNGdXQTNkVU1xQ1QvODN4SW1KY2FpZ3NHQXQ0N3NWckQ0RkNMODdPektWMndYQWsycm1RVnlHVHdMMC9GUG9ITVVWWFozcTAyR1RwdVhFRis3djdOMlBlemhoQXM0VXpzZmliMUZ2YzdtKzF6czlZaXhaNVBLalVzSTlwVmlnT2c5d0gwQVRweldsV3NsSlE1dnl1aXRzcFpWNVFoRENGZHhYa05udzA4elpNZi9qa1RZRExtbWthYjBiQ3ZzaHR3cGozbXNKOUhINmZiWUhZUDh4b0NPNEJ2VFkybndBRHRQQ1c4VFZ2SUx5S1M3QS91eU5aSnFVTUxvVXp1Uks0K1kvQU94eHhMMGVBWEE3dHNkZys2ZkJBS2oyWFlLRzFYY0RpcnVRQjNhem1LMDJZMFlqK21UeURkb0ZNUWhzVUdXTTlVeVBjbS9tM29jTkMzWXRYL1FOeTljNkdBR2FlRktOZW52OVA4NGxHb0NVN2VhcU1sRTVKSHhmaVI2QmFGenNMTkpvR0oiLCJtYWMiOiJhYmY4OTVmYjZhMjc1YjMwZjFhMjg2NzQyMjUwNzE3ZTA5Njk5NmVmOTcwMmZiMDQwNzkxZTk1YjE0YTc0ZTU5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InZBcFh3aTgyWGxjRFNTUEN6c2E2UkE9PSIsInZhbHVlIjoiUzhDeW9UV3ZMc3hMdnR1TEtPL3JPK0NsNnV4MERrSGxPcHdEVDhwWXJKZDM3dlc2R2lFMXUvZmxpR0kxeHRkU2JFYkdKd2xWekVCeUhxb2VwVE1XSytKWnVKQzJmZnROVnJPN2VCbURlVy9MWWtuczhoblVLV0Z6b1k4SnBFemdQMDk5ZG54eVpqWFRZV2hNS2gvQU5LT2pENVNMOU42ZmNtaUdZR0lQWm1nOXFDN3lVN2s1N2xraUFJWHpieUdjWERPZUpMa0FUTmNNSDM2eWlrdVprK2tHeHdjVUU5VzZOU0lJTkxLUi95Q2VpUFIxVnhkaU8xeDBNT2h4MmpMbFR3Z2xheUFmVyszdDFLTTB2RVkyb3RqMVY4Yko1VnB4VXFnNWxSNDlkR1kyVWJvM2w2czZNSVBpL0RxZzY0SG11MjlJMVJFeGpHWWE3Q3dmZkZudkt6SVhnUkJmYmlvZ2M1ZysyOWxTVnFQeGFpeFhLTzdYUnk4cmFqbWJFQm1yS1FUSHpReHVzTmJKNDVVVjhqTk16T1FTclJMazRsR2c2NS8vb2lqNVpjeXo0V3JPUThZV01kZk1ZWlhrRlh0dHZIQjdLaDBLbTMvUWJjKzVDNnFBc1FabXVTZDVTWFBaRWQ5aUZLaXViZWhFdy9IbTIyMjhGZ0M4enc2aVh5a0QiLCJtYWMiOiI2MDQwNGFmNGJmYTJkMzg4MDY5MWI2YzUzOWMyNmVhZTVmMzQ5MGFlNDY3YTlmMzQxYjI1ZTA2ZmI1ZDVlZjM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2007550818 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">07zLQ1IzhcCwivgYmqVlKD2YgfwQ5DGdasvfNucP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2007550818\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1078448327 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:18:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdObGQ2SGdPY1M2MUVCN0lCOWFjVWc9PSIsInZhbHVlIjoiWlA3dzB6ZWpERXRyTzVEOVhtQjNsRG9WYzMxNGtEUnhWeUVwSTc4WFU0aXFsZ0VFRXp5cXM0dXh6WCsra2Z2dktCckZUR0RCVmNlVklmYWtzRk0xSW9lZEFaZVdTd1d3NHR5YXhRMGF0dXV4YS9JeWtEQjFhem94dWN3SFh2Z2h1TzZGRmtoSkpaYUZjTEcvd0JheFN0N0VUZ01IS3ovRDAwbmdDZDR5WklhTVdYUTI3eGF5bkFyVk1XeXlVZ0x3aXZyVmZYYXlYMnI3T0ZOWitpeFRqUTROUHY2c09OWDZnbldScGMxT2N2cHdvelhZMmIrT1AyV3Rna1ZxOFF4ZXk3Y2pqclArdGU2ZUZHa2MzTEhjR29CZ3FleEJFMXFLdGZBT2pNbmo3ZVBkS0ZBM2s2NkdLSnN0cktuMjlFZnQvR0xOMEs5NnBia3lrY1ZmWjNvZ2N1UkNuQTErQnZlSzQyZFpnRTIvSTBCOHZFWVZnZlozOVo2Y01ERzhCREliNUFYV3VIamhSWDhWSmdIelpMVUk0dUpwKzUrYkt5RWFmR3BvcWphcmVpbVhYZzg5dXp0TmU4WGp5eGhqbEV1cHNUWkU4ZHZsdjZBOHRrSmlQb0NGR0NybkxKYWoyNmh6a04zRUdPai9Yay9nQUNRTDlxdUFKRmdsdGc3Vk5IRDIiLCJtYWMiOiJhYjI2MGNiYjFhYzVmOWM3ZjU5MTM1NzU2YWI0ZjFkMTY5OTU3MzVlN2IzN2QyZmJkYjQyOGNjMjgxOTZhYTZiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImRPSTgzdEFEWmJPVDF1NUNscWZ2QWc9PSIsInZhbHVlIjoiQnBkcWE1aVhqZlpNN3M4MmUwUDVnNnd4MkNBNllOUzgyc0NyMkdSZFVldGlEMjRKdGdGbEJCZDRzNHVjbm1YN1doZDlhMFRndHVXYTRkZzJ5Q3NjYXRHZy9VTWdQTzBoMlJ5ME5LZUxISkg1QlA2M1lEdVlubXJWWlpON3pLUkNwcU1IUkcrNVBSU2hhTERpS0I0UTRzTHJsaUs0N2VGRjRsOGhUaU1JdjJ4M0EzV1hsd21jQkpITXZrVm82QXFGU0Z1Si9RZ0pkcHZkVkZsdklQVEZIU2g4SlRyU1AxMlUrU0Y0NmEvY2FkMnNsVkpIeE5nMTVvWDk1cjVJQWhNYVNKaWEzN0lOSGJSR0V4Z0pYSGg0cnJCWmpoT01FbjZGSGdwZ0ZNMHVFSHNERlMxOFRENStkSTduUm4vR3ovYVE1Zm5sVnVvTWRLNDRhR25vcENTQ0dQaS8wQ1VqbDl0MkNOL3Y0bDA5TUVwY1I2QUdEZFhQVmZacm5zNkUvaWUrNUZSdURZaFJBL2xWemt0TXNvUTZLZkRxZ3pKbGl0NGR2eGJjcGkwaVhQVGVrdkdwbTVRUGRLNlc3ZFd0OS9ZaDZYdmpoUGk0T3hNTEhEZXhhNU5idlpKM3VkTDhLOHFvQ05zdDdRZXM2YXdXN0FwTEJMRU12NDFMdTVrdUZoeEciLCJtYWMiOiI4N2MzZDI2M2YyMjI2MDYyZTAxYzVlYzY3YzYwNjY4YmI1NDQ0MjEyYjI4Y2NkMDczZTkyZGVkOTVlMGFkNDY2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:18:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdObGQ2SGdPY1M2MUVCN0lCOWFjVWc9PSIsInZhbHVlIjoiWlA3dzB6ZWpERXRyTzVEOVhtQjNsRG9WYzMxNGtEUnhWeUVwSTc4WFU0aXFsZ0VFRXp5cXM0dXh6WCsra2Z2dktCckZUR0RCVmNlVklmYWtzRk0xSW9lZEFaZVdTd1d3NHR5YXhRMGF0dXV4YS9JeWtEQjFhem94dWN3SFh2Z2h1TzZGRmtoSkpaYUZjTEcvd0JheFN0N0VUZ01IS3ovRDAwbmdDZDR5WklhTVdYUTI3eGF5bkFyVk1XeXlVZ0x3aXZyVmZYYXlYMnI3T0ZOWitpeFRqUTROUHY2c09OWDZnbldScGMxT2N2cHdvelhZMmIrT1AyV3Rna1ZxOFF4ZXk3Y2pqclArdGU2ZUZHa2MzTEhjR29CZ3FleEJFMXFLdGZBT2pNbmo3ZVBkS0ZBM2s2NkdLSnN0cktuMjlFZnQvR0xOMEs5NnBia3lrY1ZmWjNvZ2N1UkNuQTErQnZlSzQyZFpnRTIvSTBCOHZFWVZnZlozOVo2Y01ERzhCREliNUFYV3VIamhSWDhWSmdIelpMVUk0dUpwKzUrYkt5RWFmR3BvcWphcmVpbVhYZzg5dXp0TmU4WGp5eGhqbEV1cHNUWkU4ZHZsdjZBOHRrSmlQb0NGR0NybkxKYWoyNmh6a04zRUdPai9Yay9nQUNRTDlxdUFKRmdsdGc3Vk5IRDIiLCJtYWMiOiJhYjI2MGNiYjFhYzVmOWM3ZjU5MTM1NzU2YWI0ZjFkMTY5OTU3MzVlN2IzN2QyZmJkYjQyOGNjMjgxOTZhYTZiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImRPSTgzdEFEWmJPVDF1NUNscWZ2QWc9PSIsInZhbHVlIjoiQnBkcWE1aVhqZlpNN3M4MmUwUDVnNnd4MkNBNllOUzgyc0NyMkdSZFVldGlEMjRKdGdGbEJCZDRzNHVjbm1YN1doZDlhMFRndHVXYTRkZzJ5Q3NjYXRHZy9VTWdQTzBoMlJ5ME5LZUxISkg1QlA2M1lEdVlubXJWWlpON3pLUkNwcU1IUkcrNVBSU2hhTERpS0I0UTRzTHJsaUs0N2VGRjRsOGhUaU1JdjJ4M0EzV1hsd21jQkpITXZrVm82QXFGU0Z1Si9RZ0pkcHZkVkZsdklQVEZIU2g4SlRyU1AxMlUrU0Y0NmEvY2FkMnNsVkpIeE5nMTVvWDk1cjVJQWhNYVNKaWEzN0lOSGJSR0V4Z0pYSGg0cnJCWmpoT01FbjZGSGdwZ0ZNMHVFSHNERlMxOFRENStkSTduUm4vR3ovYVE1Zm5sVnVvTWRLNDRhR25vcENTQ0dQaS8wQ1VqbDl0MkNOL3Y0bDA5TUVwY1I2QUdEZFhQVmZacm5zNkUvaWUrNUZSdURZaFJBL2xWemt0TXNvUTZLZkRxZ3pKbGl0NGR2eGJjcGkwaVhQVGVrdkdwbTVRUGRLNlc3ZFd0OS9ZaDZYdmpoUGk0T3hNTEhEZXhhNU5idlpKM3VkTDhLOHFvQ05zdDdRZXM2YXdXN0FwTEJMRU12NDFMdTVrdUZoeEciLCJtYWMiOiI4N2MzZDI2M2YyMjI2MDYyZTAxYzVlYzY3YzYwNjY4YmI1NDQ0MjEyYjI4Y2NkMDczZTkyZGVkOTVlMGFkNDY2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:18:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078448327\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-7******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7********\", {\"maxDepth\":0})</script>\n"}}