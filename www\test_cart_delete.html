<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حذف المنتج من السلة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .product-row {
            border: 1px solid #eee;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .hidden-form {
            display: none;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>اختبار وظيفة حذف المنتج من السلة</h1>
    
    <div class="test-section">
        <h2>المشكلة المبلغ عنها:</h2>
        <p>زر الحذف (🗑️) في سلة التسوق يحذف جميع المنتجات بدلاً من حذف المنتج المحدد فقط.</p>
    </div>

    <div class="test-section">
        <h2>محاكاة السلة:</h2>
        
        <!-- منتج 1 -->
        <div class="product-row" id="product-1">
            <strong>منتج رقم 1 - شيبس</strong>
            <button class="btn btn-danger bs-pass-para-pos" 
                    data-confirm="هل أنت متأكد؟" 
                    data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟" 
                    data-confirm-yes="delete-form-1" 
                    title="حذف" 
                    data-id="1">
                🗑️ حذف
            </button>
            
            <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-1" class="hidden-form">
                <input name="_method" type="hidden" value="DELETE">
                <input name="_token" type="hidden" value="test-token">
                <input type="hidden" name="session_key" value="pos">
                <input type="hidden" name="id" value="1">
            </form>
        </div>

        <!-- منتج 2 -->
        <div class="product-row" id="product-2">
            <strong>منتج رقم 2 - عصير</strong>
            <button class="btn btn-danger bs-pass-para-pos" 
                    data-confirm="هل أنت متأكد؟" 
                    data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟" 
                    data-confirm-yes="delete-form-2" 
                    title="حذف" 
                    data-id="2">
                🗑️ حذف
            </button>
            
            <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-2" class="hidden-form">
                <input name="_method" type="hidden" value="DELETE">
                <input name="_token" type="hidden" value="test-token">
                <input type="hidden" name="session_key" value="pos">
                <input type="hidden" name="id" value="2">
            </form>
        </div>

        <!-- منتج 3 -->
        <div class="product-row" id="product-3">
            <strong>منتج رقم 3 - حليب</strong>
            <button class="btn btn-danger bs-pass-para-pos" 
                    data-confirm="هل أنت متأكد؟" 
                    data-text="لا يمكن التراجع عن هذا الإجراء. هل تريد المتابعة؟" 
                    data-confirm-yes="delete-form-3" 
                    title="حذف" 
                    data-id="3">
                🗑️ حذف
            </button>
            
            <form method="post" action="/remove-from-cart" accept-charset="UTF-8" id="delete-form-3" class="hidden-form">
                <input name="_method" type="hidden" value="DELETE">
                <input name="_token" type="hidden" value="test-token">
                <input type="hidden" name="session_key" value="pos">
                <input type="hidden" name="id" value="3">
            </form>
        </div>
    </div>

    <div class="test-section">
        <h2>زر إفراغ السلة:</h2>
        <button class="btn btn-danger empty-cart-direct">
            🗑️ إفراغ السلة بالكامل
        </button>
    </div>

    <div class="test-section">
        <h2>سجل الأحداث:</h2>
        <div id="log" class="log">
            جاهز للاختبار...
        </div>
        <button class="btn btn-success" onclick="clearLog()">مسح السجل</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 'تم مسح السجل...<br>';
        }

        // محاكاة وظيفة حذف المنتج الفردي
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('bs-pass-para-pos')) {
                e.preventDefault();
                
                const formId = e.target.getAttribute('data-confirm-yes');
                const productId = e.target.getAttribute('data-id');
                const form = document.getElementById(formId);
                
                log(`تم الضغط على زر حذف المنتج رقم: ${productId}`);
                
                if (!form) {
                    log(`خطأ: لم يتم العثور على النموذج بالمعرف: ${formId}`);
                    return;
                }
                
                const productIdInput = form.querySelector('input[name="id"]');
                const sessionKeyInput = form.querySelector('input[name="session_key"]');
                
                if (!productIdInput || !productIdInput.value) {
                    log('خطأ: معرف المنتج غير موجود في النموذج');
                    return;
                }
                
                log(`معرف المنتج في النموذج: ${productIdInput.value}`);
                log(`مفتاح الجلسة: ${sessionKeyInput ? sessionKeyInput.value : 'غير موجود'}`);
                
                if (confirm('هل أنت متأكد من حذف هذا المنتج فقط؟')) {
                    log(`تأكيد حذف المنتج رقم: ${productIdInput.value}`);
                    
                    // محاكاة إرسال النموذج
                    const productRow = document.getElementById(`product-${productIdInput.value}`);
                    if (productRow) {
                        productRow.style.opacity = '0.5';
                        productRow.innerHTML += '<br><em style="color: green;">تم حذف هذا المنتج</em>';
                        log(`تم حذف المنتج رقم ${productIdInput.value} بنجاح`);
                    }
                } else {
                    log('تم إلغاء عملية الحذف');
                }
            }
        });

        // محاكاة وظيفة إفراغ السلة
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('empty-cart-direct')) {
                e.preventDefault();
                
                log('تم الضغط على زر إفراغ السلة بالكامل');
                
                if (confirm('هل أنت متأكد من إفراغ السلة بالكامل؟ سيتم حذف جميع المنتجات!')) {
                    log('تأكيد إفراغ السلة بالكامل');
                    
                    // محاكاة إفراغ السلة
                    const productRows = document.querySelectorAll('.product-row');
                    productRows.forEach(row => {
                        row.style.opacity = '0.3';
                        row.innerHTML += '<br><em style="color: red;">تم حذف هذا المنتج (إفراغ السلة)</em>';
                    });
                    
                    log('تم إفراغ السلة بالكامل - حذف جميع المنتجات');
                } else {
                    log('تم إلغاء عملية إفراغ السلة');
                }
            }
        });

        log('تم تحميل الصفحة - جاهز للاختبار');
    </script>
</body>
</html>
