{"__meta": {"id": "X464fb2eef43fc76086bfcaa67184fc0e", "datetime": "2025-06-06 19:38:39", "utime": **********.554089, "method": "GET", "uri": "/user-login/eyJpdiI6IlNhUlM0Sjh4RDNMMnYyNHJDWXB0d2c9PSIsInZhbHVlIjoiSVBxS05NdDdxNkZzUkwzQlRnSTJlUT09IiwibWFjIjoiMTg4YTFiNWNlMzVhNDViY2M2ODFiZTAxZWQ0MjM4ZjMwOTU3NGQyZGIyYmRhMmUzZmQxMzU5MmZjNzA4MjZiNiIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238718.008319, "end": **********.554121, "duration": 1.545802116394043, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1749238718.008319, "relative_start": 0, "end": **********.389276, "relative_end": **********.389276, "duration": 1.3809571266174316, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.389298, "relative_start": 1.380979061126709, "end": **********.554125, "relative_end": 4.0531158447265625e-06, "duration": 0.1648271083831787, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44001952, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET user-login/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UserController@LoginManage", "namespace": null, "prefix": "", "where": [], "as": "users.login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=698\" onclick=\"\">app/Http/Controllers/UserController.php:698-728</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0202, "accumulated_duration_str": "20.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `users`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 701}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.482742, "duration": 0.01508, "duration_str": "15.08ms", "memory": 0, "memory_str": null, "filename": "UserController.php:701", "source": "app/Http/Controllers/UserController.php:701", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=701", "ajax": false, "filename": "UserController.php", "line": "701"}, "connection": "ty", "start_percent": 0, "width_percent": 74.653}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 702}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.514452, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 74.653, "width_percent": 6.386}, {"sql": "update `users` set `is_enable_login` = 1, `users`.`updated_at` = '2025-06-06 19:38:39' where `id` = 16", "type": "query", "params": [], "bindings": ["1", "2025-06-06 19:38:39", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 718}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.523714, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "UserController.php:718", "source": "app/Http/Controllers/UserController.php:718", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=718", "ajax": false, "filename": "UserController.php", "line": "718"}, "connection": "ty", "start_percent": 81.04, "width_percent": 18.96}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/user-login/eyJpdiI6IlNhUlM0Sjh4RDNMMnYyNHJDWXB0d2c9PSIsInZhbHVlIjoiSVBxS05NdDdxNkZzUkwzQlRnSTJlUT09IiwibWFjIjoiMTg4YTFiNWNlMzVhNDViY2M2ODFiZTAxZWQ0MjM4ZjMwOTU3NGQyZGIyYmRhMmUzZmQxMzU5MmZjNzA4MjZiNiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User login enable successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user-login/eyJpdiI6IlNhUlM0Sjh4RDNMMnYyNHJDWXB0d2c9PSIsInZhbHVlIjoiSVBxS05NdDdxNkZzUkwzQlRnSTJlUT09IiwibWFjIjoiMTg4YTFiNWNlMzVhNDViY2M2ODFiZTAxZWQ0MjM4ZjMwOTU3NGQyZGIyYmRhMmUzZmQxMzU5MmZjNzA4MjZiNiIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1880876332 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1880876332\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1247579902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1247579902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1319677624 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkI1S1BYZWw3RWlGa0FEZTd5NUcyTEE9PSIsInZhbHVlIjoiaEc3SmJDR0tQT1JCeDYydURhaFFma3YxSUZndWZUMzhhblptVUtDZU1UdjdCdStwYUs1amxuYWJyQ2Ewa05QRHI1Z0ZEdkJNMUM4WFdHTE9oZXBOWW5CTEwxR25kUkt2YkdLNkdUbjNabmtKSHAySXUwc05Tc05PZmVydnE1bjdPcjJKZlA5WWZoMWdLWHQzY28xSXpqeGZyS0VRM1g5bEwyQjc3aEtDQjlnNWhCaXFINTNYbDJ2cStnMHdnNG9Kdm9Mc2wvT3BSMEVjL2FxQU40c3krakNsaDZXRGZTT3J5TlN4SjBsRXNaSzhSaWZiN1NHVnFEK29hV0ZYdGRqUFdWcFNhU0UxQ1lLS285UjA4ODRUOTBtUmYxUEYxRTZnWG5Fc2lEOEhneWh6Tzc4S3VISE5oQ3ZDdDA0VktiOEl6eTFRRGNQL0N2WXRDSUkzMmZDRC9kRlEvOGVGazVPZGlvbDNhdU5NU0g5bFRxclZ5dENWNHhQTXprMkhWNWhnd0xORWNKY3Z6WEl3ZTI2bGpyRTBjYVVEYjFGWmZZK3lBSjhtMTRVbzJ4Wk5UdTcxQThWZ0JDb2pFVUlnTHpFQ0ZGRWw3RmFXRmlzWktRR2R2RDQ5Q0duclF4TUdNOEVPRDA1U0JWMjZmVnVXbkJHcjhKV3NhSTU5WHNXdlVvSmsiLCJtYWMiOiJiMWNkMmZhNTMwODM0Yzg0ODMxNjhlMTA2NGQ3MDAwYzNkOGJlYTc2MGFiNzQwNGEwOWU3NjkxYjVjOWQxMjY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktEOE8xajdqd2pTZ2FIU1VnRXAreEE9PSIsInZhbHVlIjoiWmljaW00YmFvOEpzMXJWbmRDMEE1Lzl3WU1iU0hxWWIxWThPZVpTeVV6MGkwL29MR216dFlCWlowOHAwQzZlZFZKY1VWZDRLR2VScmJXYTZxUDc2blEvMThPWHkyY2hPOHVVMDlPc2lBZG5VOG5GZVdVNFdpNFFHTm9IZXZiM29kMHhJMkxsV0w2WHgweGVVcVZWTWlFQm90N3dwR01kTnBnNk83Q1VCaWpvaUpmbUlGVVRmQWxsdnMyWE5yQkZ4eDEremh6MXdpaU5JcTBkZzRxVzlVU3dWaGRhT3ZXdXdkeTN1REFpSHR6endNc0hGUGVsRWRVUW5vZ096RGE5VXFDSWZsbXdkZXNiS3h6YlA4NUM2cUNEckNabXdrNVFYdk03RUlUbXhnTVVSUS83eDluMWZ4czUvN2Jwa28xM1FWNVJTRXBHWWpGQmFXL1NxdmxiUXlvMzVLL3VJeWVlMDNKeFRIVVBxdXR0UU9Uckk4elN0c2RwNlExSnh6SnAxbTZ3WFlIRjZ5dzBNVmlQZEg0YU80V3lVMTFSZExEcW55bVhTRVl2VHJnLzhkck9QRlZrVElaR2Rib1pOQmk5V012aHAyTU00THhvWUhzWDFCVHUyUlB5aEJsWEgvTFJPMmJZY3dwaXFQQlA3L0dhU2FYU0EvbG4xbFlFbkRyVUsiLCJtYWMiOiI4YTVmMWIzZmFhM2QwNDBmOWEyNWY4MzExMmY1ZDdhOTRkODBhNGQyNWVkNjA1YjFmMzBiNTkzZTNkMTU0NWNmIiwidGFnIjoiIn0%3D; _clsk=mp51by%7C1749238717359%7C54%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319677624\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-629724797 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-629724797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1390673059 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:38:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxsalF4bWNaNnpBd0wxOTFHV0RqcWc9PSIsInZhbHVlIjoidXNPb0NZSG54UEtyWUM5NkNhYzRjRnlsdGtvbkZBaXU1enpaTVRoQkltRUhpMkE2dmhZcUx1V3NsbzVQR3d0R3FiQnl1a09jdm1jdTBzRkIwSFlSV1AvLzgrZDczQitIQnpHVTQvVTIwcUVBeDRiZ1UrTHBmeStldlRUV2poZVUySWQyZG5XSTVDamhiU0N2aU5ONHovblVvM1puVys3WEFJeG9saXNVNWhHV0IyZU5FQUFNQ3Y2NTNuUFpsTVZXTnBONE43OENBM1NDMHpxSUN6VkFOc05Db2RRRXpTWFNaOXZNRjNMMlV2ditpcFdJZ1pzT0FJa3YxUEE2c1ZlcVIweU1mZmY1ZHN2N3I3NUdYZDlyRURUR1NML3Y3SEUzai9ZNzlhaHlNcGhobWJBeFZQZUxOUGdNRnZNaDNyMkVSQ0g5VkNFUzN3d3VxaDRVek5yeFZkQUV0enZaQTB0R3VObHRpa01LRi8xL3dKQlFnUDNiOTM5UUtsMVFEdFhCc1JyRVRuUU5DYXJnRmZyZG9jZUlXQ2p1eVNZVWprQTVvLzlqNDRXL3E3Rms5REtETEhNTnQwWWh3UXFvSFpZV2JxRU5Pa1FQSFFZLzdQQkJsV051RHhJemczNEF6RTc0SlhvWTdsSlVPNUVRT0U1VG9vNEJtNkVETm5iaHdXRFoiLCJtYWMiOiI0OTI0NGYzZWYwMjU3NzQzYmUwMjM1YWU1Y2I0Y2I1YTQ4Yzc4MDk1NjYyOGE3OWM2MDUxMDk3NGFlMzExZDVkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikk0Q0g0WTJRNW9MVGkxdnA0OVU0SEE9PSIsInZhbHVlIjoiR1FySlYwSVJRYy94MEsxWEV0SVRjelA2eHVSc1hLSGhkRFk4QkU3OEJIeU1icUJzNGEwdStXN0xpU3FNV251b1N6UXZYRE8zZzhGTFpYbFV2c3BKM2xBWXNVWFp6VWRDYnZQcDAxckcrM3lrdGlQLzRNUXhxYUxUNkZJUEhlbVgweVVlTUZOMGtVS0NjUkpVZTlyeXpjbzRQVkEvN3lrSnJtbGhvL3RsMG5kYXV6VjJIaVZnVEVDeWhQVlN1MkNOZEx1cU5ic1VNMFJqYk40MXN1ODFuWWdtMW03bGRGL0c5NFo4ckxabVAzTTJZdVI4T3htS0xNYTN2dkc2c2NLWjEycG90eDNmTDJXVTZxanQwWGlacGVXRDNLeVA3Yi9RcTU4Tk5IbmRJRlY0blBVak1yVG9oK0lyODJVNGFxV3gzSjV4T3pzTEROZW5CYmNQeStXOEczQTNGWEw1OHdIMnNwUzFQdWVTb3hWcXpWVk9Ca25hdnpyemlPT2psekVvcmZaY3VtNCtWVzhETGpTdlZCNEhzeE5laDQ4Q25hME5uSFU4T3gvQWVaQy9XWHRZZUVBRXVlVWcyMndHMCtTbVdlZWZseHptWUdudGpQWGZRQXlacHhSU3U1VUQ3cndQUzNYTFV4Z0lrQjdaYjU0ZG0rMldibisxSXAzNlNscW4iLCJtYWMiOiJkNmQzYWIxNmYwNzE4MWQyMWVkNTAzZDkwZjhjMzc4NTExYmMzYTY5YmJkMzMxOWRhN2UxM2RmMjhmOWM4MjY1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:38:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxsalF4bWNaNnpBd0wxOTFHV0RqcWc9PSIsInZhbHVlIjoidXNPb0NZSG54UEtyWUM5NkNhYzRjRnlsdGtvbkZBaXU1enpaTVRoQkltRUhpMkE2dmhZcUx1V3NsbzVQR3d0R3FiQnl1a09jdm1jdTBzRkIwSFlSV1AvLzgrZDczQitIQnpHVTQvVTIwcUVBeDRiZ1UrTHBmeStldlRUV2poZVUySWQyZG5XSTVDamhiU0N2aU5ONHovblVvM1puVys3WEFJeG9saXNVNWhHV0IyZU5FQUFNQ3Y2NTNuUFpsTVZXTnBONE43OENBM1NDMHpxSUN6VkFOc05Db2RRRXpTWFNaOXZNRjNMMlV2ditpcFdJZ1pzT0FJa3YxUEE2c1ZlcVIweU1mZmY1ZHN2N3I3NUdYZDlyRURUR1NML3Y3SEUzai9ZNzlhaHlNcGhobWJBeFZQZUxOUGdNRnZNaDNyMkVSQ0g5VkNFUzN3d3VxaDRVek5yeFZkQUV0enZaQTB0R3VObHRpa01LRi8xL3dKQlFnUDNiOTM5UUtsMVFEdFhCc1JyRVRuUU5DYXJnRmZyZG9jZUlXQ2p1eVNZVWprQTVvLzlqNDRXL3E3Rms5REtETEhNTnQwWWh3UXFvSFpZV2JxRU5Pa1FQSFFZLzdQQkJsV051RHhJemczNEF6RTc0SlhvWTdsSlVPNUVRT0U1VG9vNEJtNkVETm5iaHdXRFoiLCJtYWMiOiI0OTI0NGYzZWYwMjU3NzQzYmUwMjM1YWU1Y2I0Y2I1YTQ4Yzc4MDk1NjYyOGE3OWM2MDUxMDk3NGFlMzExZDVkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikk0Q0g0WTJRNW9MVGkxdnA0OVU0SEE9PSIsInZhbHVlIjoiR1FySlYwSVJRYy94MEsxWEV0SVRjelA2eHVSc1hLSGhkRFk4QkU3OEJIeU1icUJzNGEwdStXN0xpU3FNV251b1N6UXZYRE8zZzhGTFpYbFV2c3BKM2xBWXNVWFp6VWRDYnZQcDAxckcrM3lrdGlQLzRNUXhxYUxUNkZJUEhlbVgweVVlTUZOMGtVS0NjUkpVZTlyeXpjbzRQVkEvN3lrSnJtbGhvL3RsMG5kYXV6VjJIaVZnVEVDeWhQVlN1MkNOZEx1cU5ic1VNMFJqYk40MXN1ODFuWWdtMW03bGRGL0c5NFo4ckxabVAzTTJZdVI4T3htS0xNYTN2dkc2c2NLWjEycG90eDNmTDJXVTZxanQwWGlacGVXRDNLeVA3Yi9RcTU4Tk5IbmRJRlY0blBVak1yVG9oK0lyODJVNGFxV3gzSjV4T3pzTEROZW5CYmNQeStXOEczQTNGWEw1OHdIMnNwUzFQdWVTb3hWcXpWVk9Ca25hdnpyemlPT2psekVvcmZaY3VtNCtWVzhETGpTdlZCNEhzeE5laDQ4Q25hME5uSFU4T3gvQWVaQy9XWHRZZUVBRXVlVWcyMndHMCtTbVdlZWZseHptWUdudGpQWGZRQXlacHhSU3U1VUQ3cndQUzNYTFV4Z0lrQjdaYjU0ZG0rMldibisxSXAzNlNscW4iLCJtYWMiOiJkNmQzYWIxNmYwNzE4MWQyMWVkNTAzZDkwZjhjMzc4NTExYmMzYTY5YmJkMzMxOWRhN2UxM2RmMjhmOWM4MjY1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:38:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390673059\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-596067794 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"228 characters\">http://localhost/user-login/eyJpdiI6IlNhUlM0Sjh4RDNMMnYyNHJDWXB0d2c9PSIsInZhbHVlIjoiSVBxS05NdDdxNkZzUkwzQlRnSTJlUT09IiwibWFjIjoiMTg4YTFiNWNlMzVhNDViY2M2ODFiZTAxZWQ0MjM4ZjMwOTU3NGQyZGIyYmRhMmUzZmQxMzU5MmZjNzA4MjZiNiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"31 characters\">User login enable successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-596067794\", {\"maxDepth\":0})</script>\n"}}