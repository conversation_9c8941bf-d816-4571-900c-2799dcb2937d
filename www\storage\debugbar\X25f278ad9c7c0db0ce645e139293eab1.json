{"__meta": {"id": "X25f278ad9c7c0db0ce645e139293eab1", "datetime": "2025-06-06 20:37:27", "utime": **********.476368, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242245.84123, "end": **********.476402, "duration": 1.6351721286773682, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749242245.84123, "relative_start": 0, "end": **********.27869, "relative_end": **********.27869, "duration": 1.43746018409729, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.278711, "relative_start": 1.437481164932251, "end": **********.476406, "relative_end": 4.0531158447265625e-06, "duration": 0.19769501686096191, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00893, "accumulated_duration_str": "8.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3752239, "duration": 0.00566, "duration_str": "5.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.382}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4130151, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.382, "width_percent": 11.198}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.423611, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 74.58, "width_percent": 11.87}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.449673, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.45, "width_percent": 13.55}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1506334863 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1506334863\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1709019689 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1709019689\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1992294523 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992294523\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1299555001 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242227559%7C9%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InN6bU9KanBMV0hWTEtBMW9EcU9OQVE9PSIsInZhbHVlIjoiMFNXcFN1UkcxZmlyUXUycmJMeFlKUzUzNGpuWS9OSlNMRHdpQUx2ZUFSNmFaNjFPdWdFU0g4TGkyRXgyTzBUTTdrWGgxU0h2VGZlZFU3TVFNVlEwZ0pFY0ZaOURLajBxUGVRYTVIOWxQK0VZTnJ3Q1lMMU85Z3JUU1VzaGlhT0hRV1FnWUNjOGhnaFlsMFdkRGpja05CdG4wY2pkQ0ZCVTNtUDRUYTNMNThnMk5pamVsRlRIZkxsVnVwaWExS014cXoybkljU3dPcXRVUDdNVkdacTFIVEpFaTV1b21ybzdBMVZkMkRiSnFmeHNXZ0hEWWdDS3dKQ2RGbk1wbFJSakt6cktocExoSDJOSGVrckRHTVdZVG9rZTlzdmNZSEV3YnJzdVRrdWhIeWQwNDE2dlR2cmM3OGFNMnEvN2NFWUx0VDg1a1lpS3NZL3c2Uy9VOWdKeFA4cjJWa1BOTTlPd3BBZ3h6WFhCYW9wS1d1Z0UvQ2pETTg3SFdndDlnajlrZE9lNUZyYUR4b0I3bk80YzBLVjYwWEdXQUNUSzVKM2RjNFFITWYrZEZXajZrYlJnZ1pGeXZLQ2dYNXlPa1ViME80ZVFrTGpkaU1VN3ZvdlZxZmx6eEk1NlY5VmorelhPUnRLSGhVT2tjY3NpeHZ0dlpKM0ExbmFCOEFVT3UzakkiLCJtYWMiOiI4NGVkY2M0OGU3YTczZGE5NmQzMzIwN2FmNTUyMzBmNTFhNDlmZGFkNjk0NDdlOTlhMDdmMDQxYTUzOGQ5NTBiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjE5OW5Ealo2cWJiSjBQaktDaXRGeEE9PSIsInZhbHVlIjoielF3WWpJOW1PMGFhVmc2Sis0N0JDZWI0akN0TUtzUFJHUzJnV2RleVliSmlvWnlodlVub1h6UVplNGVtZTM3S0NuVHNMN2prNmdrRnRnNmJhdW9HWXZSdjFZTlNSNm1UMEFZOElYZHdjQnBFZUxEZm1VQ3FEbnJQYjJTS2xhQUk4RGdUc1l4aEJKZnlFUVhJRGN5N1Fqd1B2ZGUzUjFIeUd6ZGRsUVkrUXdid1VkV0ZsWkFaUERYbGVSSkJRZGR2RTlDWmxNelRlMXVnS0tHc250YWtrb2JGYVhHcU42ekJUUkJNOFpEVGVsUmdCSlZNL05CN1NxekFXVU96aFNiN29ud2lTbG91elNMM2x2ZHh1TU5Ib1NudVl5MWxsV3Z4ODQ3dVFaYjBycCthNzk3aXZhVEpQemE5Z2g4dVlWTkxIcHIyUkI5NzFLQkl1NG8rc00raURQeEJ5b1h0WVFseGk2NXBNeW1xbExoc0VPcjkwdHBrRmpkRVpVSmRxQ1M2bXdjQ1lmYXdLQTNibENramwxNktUeW5xR1FtY21FR25LWUhFNUxqdGt0V1FUL2RBSVdEalFlS2JBRVEza3VsM3dOZEdJZE5tSGZaQ1FNYjd5cFFUS2FKYzE1TzlHUnY4aTRURi9QMHhDMy9ZYk5tcnRubFZld2RsTWtOeVp6ZksiLCJtYWMiOiJjZWZjZjIyMzJjZWFmZDFlMzMwYjczMTdjNzE5YzU2YTdkZWViNTY0NmNiMDRmZTZkNWQ5ZDhmY2UxMDAzNGNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299555001\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-677332435 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MpwVbLVNdPBleitYM66OL5GzCiGTbitGislJpX6Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677332435\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:37:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5Eb3R6MU1XdE95V2VDVVhYZzlPZGc9PSIsInZhbHVlIjoidFBEcmZIdkpCSndiMy9DYWwzMEZEMmFrNkRnbkNISkUvNG5TRkNqYlRHU2VrTUxPMDdVWXoyYTk2OFgrUUdUeVlmbUFEM3FpdjhnbDM0eHRJV1lhY3pyTE5hbXVxRlVFdEZRNnY2WmVjbjZlNmhPbVRDRnRJZjZ3NkhQOGxDM1Vrd0lPSWpEYjB6YjNVK2pnSzJZZWFlM0NHM2NyRzNzNHI5aUNRTG9kNkNhRVExRVg0cVFWQmJTbU8yWXJpeDZabnA4enU2Qkw4azdoL09LbSsvTVlLdW9zb3dweGlaOURNREN5REZkc2ljZE9NYzkyWVlsRXRSdlZDZDhGN2JnMFlNSWFXL0ZJTkxMOHRiSkRNMExYWDZjSGdYNjRpVmVTMFA5SUlVREdMaVMrQ0FQcHcxTUZ5bGtGbE5JUEpPY1pWVW10Sy9nWEhvcHU3Z0QyeUtTWnU4WXBReXRoNlZockZnSHV1cVVNMXJSd0MvKyt1TEo1UzRvNm01NnAxQ09RdFRTQWxvVzZ0dkxGT0J1aG5zMXpabFVTUXVoUktMbFRKZVVORFpFVy90MWVFOU5ydmJBeGJLRE15RjZIWTZOYW05QlNBWmZTZUsvMkd6dzkzY1B5ckI0STNLSlhNMWN6YmQ0dEphOS9sVmVGUGdTQ0daV3NhZzJXYnlXWTNtY1EiLCJtYWMiOiI4YWVjNzEwYmQ2M2NkYzMxMmViYzJlYzNmM2IzN2IzMjJlN2JmODQyZTVjZWQ2ZjUyNzMxZDIwN2FiMDE1ZWUxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9DZnlMcEcwbW1TbDZpazF1MHBQQVE9PSIsInZhbHVlIjoiZnlUOFFyWll3WXNrdXBGRWhvaGNSN3JrZzBKR2kyVStnTzJ1b211M3pHYWlQVlhBcWozdHlqOFFtaHFhSEV0NXdTUkErV2VvR1V4dUY2WDAvajcwdFBGV05IVWR1SFgwMzdiMXU5NnNnTVlpS01SSHNUcitwNHd5c0gxUFh2QU9JbXFtbzFIK2FJVVF1SUF0MDF3a01RaFc5bnQ3SkxwUnUxaWpNcjlqQzFEVDc2NzFJeDUwRXhxb1BwWk0wdklCVFFDYXg0WEdScDkyb2hyMGpHNTJoUEx0MVNMditldW80WjZmTzk2MmxjbjV6MnA1ekRBYUU3dXBIOFpXUHJqWko5ajJJanhSOS91ZkZ2WERoaCtWdHVDQ3Z5eGNnYTRjbEZwdXM5N3FCYVRMbXJ5Njgzb0ZqejczK005aGRXVEZCVkNWcGg0WUJEUnBmM1NtYWE5cGJqOTN5cU15UDRvK1paY2dVRmtsNE1XNHF0THhwV2FqeGtLSHk4ajNqQTFiVTlTZ24zbThweE5TbWZKNzUybmYvdWRrNUZIdVFmT3dvMi9OY29QWXRWOEJHVnlDTTdtN1FPV1YwamNaMFB5aWI2SlRna0VZRHYvZHFabG1uRUJ6N3BIQVV2T0FjNkJ5Q1pUaXUwQStZVzhVT25KQWwyeHRhZXFEck1VbXM5SlUiLCJtYWMiOiI1NTY2ZDE2NjdhM2NhNjdlNmE3M2I1ZjRhOGI0MTkxMDQ0NWY0ODYzOTgyMTEwMTZiNzk2YjFiODU3YmM0YTFkIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:37:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5Eb3R6MU1XdE95V2VDVVhYZzlPZGc9PSIsInZhbHVlIjoidFBEcmZIdkpCSndiMy9DYWwzMEZEMmFrNkRnbkNISkUvNG5TRkNqYlRHU2VrTUxPMDdVWXoyYTk2OFgrUUdUeVlmbUFEM3FpdjhnbDM0eHRJV1lhY3pyTE5hbXVxRlVFdEZRNnY2WmVjbjZlNmhPbVRDRnRJZjZ3NkhQOGxDM1Vrd0lPSWpEYjB6YjNVK2pnSzJZZWFlM0NHM2NyRzNzNHI5aUNRTG9kNkNhRVExRVg0cVFWQmJTbU8yWXJpeDZabnA4enU2Qkw4azdoL09LbSsvTVlLdW9zb3dweGlaOURNREN5REZkc2ljZE9NYzkyWVlsRXRSdlZDZDhGN2JnMFlNSWFXL0ZJTkxMOHRiSkRNMExYWDZjSGdYNjRpVmVTMFA5SUlVREdMaVMrQ0FQcHcxTUZ5bGtGbE5JUEpPY1pWVW10Sy9nWEhvcHU3Z0QyeUtTWnU4WXBReXRoNlZockZnSHV1cVVNMXJSd0MvKyt1TEo1UzRvNm01NnAxQ09RdFRTQWxvVzZ0dkxGT0J1aG5zMXpabFVTUXVoUktMbFRKZVVORFpFVy90MWVFOU5ydmJBeGJLRE15RjZIWTZOYW05QlNBWmZTZUsvMkd6dzkzY1B5ckI0STNLSlhNMWN6YmQ0dEphOS9sVmVGUGdTQ0daV3NhZzJXYnlXWTNtY1EiLCJtYWMiOiI4YWVjNzEwYmQ2M2NkYzMxMmViYzJlYzNmM2IzN2IzMjJlN2JmODQyZTVjZWQ2ZjUyNzMxZDIwN2FiMDE1ZWUxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9DZnlMcEcwbW1TbDZpazF1MHBQQVE9PSIsInZhbHVlIjoiZnlUOFFyWll3WXNrdXBGRWhvaGNSN3JrZzBKR2kyVStnTzJ1b211M3pHYWlQVlhBcWozdHlqOFFtaHFhSEV0NXdTUkErV2VvR1V4dUY2WDAvajcwdFBGV05IVWR1SFgwMzdiMXU5NnNnTVlpS01SSHNUcitwNHd5c0gxUFh2QU9JbXFtbzFIK2FJVVF1SUF0MDF3a01RaFc5bnQ3SkxwUnUxaWpNcjlqQzFEVDc2NzFJeDUwRXhxb1BwWk0wdklCVFFDYXg0WEdScDkyb2hyMGpHNTJoUEx0MVNMditldW80WjZmTzk2MmxjbjV6MnA1ekRBYUU3dXBIOFpXUHJqWko5ajJJanhSOS91ZkZ2WERoaCtWdHVDQ3Z5eGNnYTRjbEZwdXM5N3FCYVRMbXJ5Njgzb0ZqejczK005aGRXVEZCVkNWcGg0WUJEUnBmM1NtYWE5cGJqOTN5cU15UDRvK1paY2dVRmtsNE1XNHF0THhwV2FqeGtLSHk4ajNqQTFiVTlTZ24zbThweE5TbWZKNzUybmYvdWRrNUZIdVFmT3dvMi9OY29QWXRWOEJHVnlDTTdtN1FPV1YwamNaMFB5aWI2SlRna0VZRHYvZHFabG1uRUJ6N3BIQVV2T0FjNkJ5Q1pUaXUwQStZVzhVT25KQWwyeHRhZXFEck1VbXM5SlUiLCJtYWMiOiI1NTY2ZDE2NjdhM2NhNjdlNmE3M2I1ZjRhOGI0MTkxMDQ0NWY0ODYzOTgyMTEwMTZiNzk2YjFiODU3YmM0YTFkIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:37:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}