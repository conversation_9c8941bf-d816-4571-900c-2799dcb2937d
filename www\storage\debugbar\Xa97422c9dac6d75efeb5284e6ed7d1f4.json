{"__meta": {"id": "Xa97422c9dac6d75efeb5284e6ed7d1f4", "datetime": "2025-06-06 20:41:15", "utime": **********.762988, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242474.517672, "end": **********.763027, "duration": 1.2453548908233643, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1749242474.517672, "relative_start": 0, "end": **********.610451, "relative_end": **********.610451, "duration": 1.0927789211273193, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.610468, "relative_start": 1.0927958488464355, "end": **********.763031, "relative_end": 4.0531158447265625e-06, "duration": 0.15256309509277344, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43525128, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02258, "accumulated_duration_str": "22.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.696954, "duration": 0.02171, "duration_str": "21.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.147}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.729569, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 96.147, "width_percent": 3.853}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1158571989 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1158571989\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1836220072 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1836220072\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242413154%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklCWTM4dnd5bUg2bjlsZ2NPOVQ0dXc9PSIsInZhbHVlIjoiTlFEQ0QweTVLKzFUZUxONEJBNHRTSDRUc0kzWGw0cm1mbDV4NVdoeS85S2pCcDl1eU5FYm5JOTkwZTdiNkxxTTJDZEg5N0J1bzgxOXVEazhFUHlBZEpvN016UkhTSVBabmxjMDdacDVHUTY4VmM0TkJrZHRQRk9mZUZERVcyWWZhQWdhM2FybnNFYXRqc0lMUG50dkp2OEtWb2ZIL0QxTGs2L3JlV1lQVDBZOVVPcmRsTEE3aG5MNkVoYVA5OFB3SGlOdFVHRk5ENTM0bFNnMGhIZWJvVy9xa2o5QTAwdWwrZWY0UHUrZCsxR1U5U1d2RHNzVE9EbXpZd2hTU000cmpBRUdYaU4wcDVWYTdKRzFZcWJCM3lENlNZVXhwWjJTL3N3bEpxbnMvdW5GZitIVU5SSlRuMEZFL1lNNk12bllSbW84bFgzUjNuWk9CNy9oNXY5WUpiSGxSbktEM0lTTTZOQ3ZKMGVPNTR5ZU8wK1U3dFRRZ0FHNjJjY1FJaTkrTzhldzdxYWU1NFoxbk1naVp2dEx0VmZEVzVtQk5rUUJULzBZUEE4a0IrZUZMQjhkZ3VpR3BQa3AwMkU3SEg2V2JUVHZ2OFgwZVlMTjZyM3NnOGZrRTNBd202a2Y1ZmRlS3E3Sjh1SE5jTUhLMDdFV0JyMGVPNFpqcXROZjZHK00iLCJtYWMiOiIyN2QzYjQ5OWZiMWJiNDhlMjAyOWJjMWQzNjBjZGZmYWJmMGNjYmVjNmRjZjYzMDI1NThhYTIxNzdmYmEzNDRjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InYxZjB6ZXVwemFIZ1JZdkFGcFVNS0E9PSIsInZhbHVlIjoiWTVNd1JJekZ3Q1cwVGQxcnpESDk5M3ZPVDNVVU1wTzhuVkxQS1lSMC9STnpxdGtGaDNRbE1ZNVlLZUhKS0UyendYb2k1cjBYUUZyUU1LYWJrN2lmNm5LaDhvUWVuVXhiVENXY3l2dFpMWDkxQSsyRGNYSE5jTDNoOEpwdVEwMDVDNURzU3djKysvS0dtbkU3NEJoUWtpSXEzNFZXeFhnRGE0aE50Y2xmSHlyOXZ0UTBBYWhYd2lSWG1nSmpuREgvSlY2MUMyODNrUFRMT2pHS2JpTUdMcDBPUm94STdiVVh1bGROM21VbTlGOEFBZnhEeTdyZEVHODlNa0VpWS9HM3doSlh1Y2JUNHFwS1dvblRiSXNRejczVDdLVGZWVDlIUHNCWWxzem1EeXd4b2RQdkxmam9iUE1hUzdGaEFKdG5OOXhRL0d5U2QrcHl2WkMzR0w5d3dEZVNJVVQ1blhqME51MitXMDczQ3Fqak1vTVcyNlU5d2c2c2M5Zk9GTlRnMUZaZ0dKTGJTMjg2UkZaZDhsQjY3ZHA4L0lUNzV0bHB5cllHdG55TktjR2UwcHF4akNvNXZTU01iWGdtM2pqd2IzYXRxTi92YTVvY1Jpd001UmpGa25WV3pLbDhKNVZvZ2Y4STlWNjJrbEVlamY4TXZaWlBsVTRweXZNWDdzZ1AiLCJtYWMiOiI0NDg0NmViNjZiZjkwZTlkNGI2ZGIyMmZjY2M2MjE0ZTFkMDIwYTE4ZDdhOWUwZTM0ODZjZTUwMzIyZGRiMTNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1210771811 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5SyoJjvx9ER6DwaeNylpmlrCKyunmBMNBrM7fYhk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210771811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1268672858 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:41:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFqU0RpOS9QSWtRSVdLR3hackZJWVE9PSIsInZhbHVlIjoiU0NTZnYwMWlTOXJUdWRXK3pmMDF4QjVUaEVtTlBnR1RwNEJVK3RlRGZaQjhKSTR1RWo2SldoT09IYXZnUW9ZVG1VZ3VFYkxiVTN0dzRqUU1sVmwvZk9RUzkrYU5wbFVsazFYNEdIVXUrdXRQWnd5bWFwaHhNaXJMWEY1dFZBVjZwSERLU3c2M044VXZVeXEzemRuUlNSMTk4VjVNZUlnWGtwRmx3RlVaVlU1NUVScUdSWWZ6S3hRWlgzWURmQzBtVWZ6eGRmakVNMmVpRkJMY2dJY1hXYm9YU3ZxVzRIeG1ySGIyLzFKODJjbVBVRXN0ZVJRS1BMOGRQOEJNcXV0alZsZEZJQXhrYUNXNFpXaGxBNW5BZXYxNHdNcGlSemF4NUxqVFloeHh4Q3hkbjRKT1NHcHMvc0kvVlBWRVJZOHVhN2lPRjJIaFJxR3kreXN5TGZjZDRXL0lXZ3BldVA1K3R4ZGxhQ1E5WlVrUHhUdUJBbVM1ZTJNc0s3QnhrZVVVc2VPbnh4RW1pQStXS0hqUG52dGZhaEx4aGUxdkFrSkVQdWU4aDNoQmt0Ymt0eGJrT0ZicW0wYUx4V0lRT3Niak1aUGRUODRhbTh1N1RLa0lkdzFwelNJcEVXbTJzZldsYW1kZjhKWklMaGR6MHAzTmtFU3N1VVhLczR0QUJyeS8iLCJtYWMiOiJkODIyOTQ4NjI2NTk2NzA2ZjdiZDZlMWFlYjA5OGEzMjQ3ZmE5NWU2YjJjZmVlODhhYzk5YWM3ZGYwYTM3MTg2IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNqUm1aa0RhVk1pQXdnSy9XbnFNUUE9PSIsInZhbHVlIjoiODlUQTVxeFh0L3JvL084bStHR1RjQld3SHJZUkxoU1llQnpGU0JHaVhWUW0zTzg3WmlUTEl3VzF4T2tlN1BKTGZzTnBGRWM5YUZTQ1NyaGhpQ04vMWFYQzhYTlhaaGR0ZERDZDR3alJnY1lpd1NWS0gra01aaTNuTzN3ZjlVeGVjV1ZWM2lxekNUREdJZnpCMnlGbHlhc3gvQkRlZjVQa0pGTWRJa1M2bDRUNXBsRFFvUHZPWUhLMEpITmZHV2hCcExXZFRUUnVUSVBXbWZyNlFwV0grRlJ3cEoyVWR3Q3VmM0dKUUFDNlBLckVhVG1icFd5bTA0ZTltc3V4OHJYaVJid0tvUURhdUVUb2V3Q1NBeHgrMjBGYVlsUzlIWERVdFdSdXIvR0RzZHVkNjNwVXhieUtXUWsvY3l3eDhpd0xCTWRIREx3MEdtQnpsYzRrdVE5aVZTdDNPdmp0V1IvOUlydlhiV0NHNDlvQmZ6T1R4NHBvczlObVRUR0dOVjluYTdBbFQ1b1BraHZhaTJ6NmxVWnR6WXpJVWtNMkllOHRoKzJhR09GSkt2d1F1Z1hnOHdGZC9BS1VjRVMrZlgzOEVkZUoyTlhsSkRGa0lQLzFkVEJTN1RsT2xOWnVMWnBQdDlFUUNHb3A3NVRWYmxpdENpUzRMcGZsS2pTTkpjTWQiLCJtYWMiOiJjNTMyMTQ4ODE2MGQ4NGJjNzdhMzUxYTQ5NjYwN2M1NzEzZDA2N2YxNjc4ZTY3MmRiM2I0OWQ0MThkMjRmOTQyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:41:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFqU0RpOS9QSWtRSVdLR3hackZJWVE9PSIsInZhbHVlIjoiU0NTZnYwMWlTOXJUdWRXK3pmMDF4QjVUaEVtTlBnR1RwNEJVK3RlRGZaQjhKSTR1RWo2SldoT09IYXZnUW9ZVG1VZ3VFYkxiVTN0dzRqUU1sVmwvZk9RUzkrYU5wbFVsazFYNEdIVXUrdXRQWnd5bWFwaHhNaXJMWEY1dFZBVjZwSERLU3c2M044VXZVeXEzemRuUlNSMTk4VjVNZUlnWGtwRmx3RlVaVlU1NUVScUdSWWZ6S3hRWlgzWURmQzBtVWZ6eGRmakVNMmVpRkJMY2dJY1hXYm9YU3ZxVzRIeG1ySGIyLzFKODJjbVBVRXN0ZVJRS1BMOGRQOEJNcXV0alZsZEZJQXhrYUNXNFpXaGxBNW5BZXYxNHdNcGlSemF4NUxqVFloeHh4Q3hkbjRKT1NHcHMvc0kvVlBWRVJZOHVhN2lPRjJIaFJxR3kreXN5TGZjZDRXL0lXZ3BldVA1K3R4ZGxhQ1E5WlVrUHhUdUJBbVM1ZTJNc0s3QnhrZVVVc2VPbnh4RW1pQStXS0hqUG52dGZhaEx4aGUxdkFrSkVQdWU4aDNoQmt0Ymt0eGJrT0ZicW0wYUx4V0lRT3Niak1aUGRUODRhbTh1N1RLa0lkdzFwelNJcEVXbTJzZldsYW1kZjhKWklMaGR6MHAzTmtFU3N1VVhLczR0QUJyeS8iLCJtYWMiOiJkODIyOTQ4NjI2NTk2NzA2ZjdiZDZlMWFlYjA5OGEzMjQ3ZmE5NWU2YjJjZmVlODhhYzk5YWM3ZGYwYTM3MTg2IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNqUm1aa0RhVk1pQXdnSy9XbnFNUUE9PSIsInZhbHVlIjoiODlUQTVxeFh0L3JvL084bStHR1RjQld3SHJZUkxoU1llQnpGU0JHaVhWUW0zTzg3WmlUTEl3VzF4T2tlN1BKTGZzTnBGRWM5YUZTQ1NyaGhpQ04vMWFYQzhYTlhaaGR0ZERDZDR3alJnY1lpd1NWS0gra01aaTNuTzN3ZjlVeGVjV1ZWM2lxekNUREdJZnpCMnlGbHlhc3gvQkRlZjVQa0pGTWRJa1M2bDRUNXBsRFFvUHZPWUhLMEpITmZHV2hCcExXZFRUUnVUSVBXbWZyNlFwV0grRlJ3cEoyVWR3Q3VmM0dKUUFDNlBLckVhVG1icFd5bTA0ZTltc3V4OHJYaVJid0tvUURhdUVUb2V3Q1NBeHgrMjBGYVlsUzlIWERVdFdSdXIvR0RzZHVkNjNwVXhieUtXUWsvY3l3eDhpd0xCTWRIREx3MEdtQnpsYzRrdVE5aVZTdDNPdmp0V1IvOUlydlhiV0NHNDlvQmZ6T1R4NHBvczlObVRUR0dOVjluYTdBbFQ1b1BraHZhaTJ6NmxVWnR6WXpJVWtNMkllOHRoKzJhR09GSkt2d1F1Z1hnOHdGZC9BS1VjRVMrZlgzOEVkZUoyTlhsSkRGa0lQLzFkVEJTN1RsT2xOWnVMWnBQdDlFUUNHb3A3NVRWYmxpdENpUzRMcGZsS2pTTkpjTWQiLCJtYWMiOiJjNTMyMTQ4ODE2MGQ4NGJjNzdhMzUxYTQ5NjYwN2M1NzEzZDA2N2YxNjc4ZTY3MmRiM2I0OWQ0MThkMjRmOTQyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:41:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268672858\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2116014161 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2116014161\", {\"maxDepth\":0})</script>\n"}}