{"__meta": {"id": "X4833033f3fe007a7112781dd8b4ad858", "datetime": "2025-06-06 19:24:23", "utime": **********.322675, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237861.725256, "end": **********.322714, "duration": 1.5974581241607666, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749237861.725256, "relative_start": 0, "end": **********.120864, "relative_end": **********.120864, "duration": 1.3956079483032227, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120887, "relative_start": 1.3956310749053955, "end": **********.322718, "relative_end": 3.814697265625e-06, "duration": 0.20183086395263672, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44851120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024560000000000002, "accumulated_duration_str": "24.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.2350411, "duration": 0.02189, "duration_str": "21.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.129}, {"sql": "select * from `users` where `id` = 14 limit 1", "type": "query", "params": [], "bindings": ["14"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 70}], "start": **********.2883341, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 89.129, "width_percent": 5.415}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2983642, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 94.544, "width_percent": 5.456}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "14", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1825844055 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1825844055\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1992527351 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1992527351\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-144548230 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-144548230\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-31703905 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237788930%7C15%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InEwNkt0L2szWng4Y3pZMWdtT0xGeEE9PSIsInZhbHVlIjoiZnFyTUVXMGhEb1lIQkdMQUx0RzljOXJBTDU4amo3UC90MUwwWndyTnA4a1ZZNUd3SXZEMzM4N1FXL2RYVEVuaGJXVW9Pc3R6Rng0Mnh6YmVuT25uc1BsaTVSOFc2L21qelF2Qkp6TVlHQkFXMjZPZTJEbkZaRldGSTltRFJKTmZpWmsrWkxnMnF1OFdta1ZvYU0wa3pHbDkyaVYvVE1sdUJEdDM1N1hTK2gwWkNiTEZ6ZmtJWG1kSDZ1VTl2WEdXYS9PSWIvL2Rna2hHZy8wclpHaTd1aGRyVC84OGdsaHlGWExwMzR2eUlrUGNWcFJOc1VMYmVVZDJuNUJuelJuS1EyZ0ZIbU1HUFl6b3hZT203dXpGaGJsMDJLQ3ZFOVloT1p2aytaa2g4eXBlRGFpd2w4RkhrTlJVR21uRm1zWDhrM0QrSE4xY3BmN0VDd001c3VtUWZlNGZlQWhKVmloOU5DZjk5L0gvUWxOT2JXTFNnRE1VdUVoa2wvRnBTN3hjZ0s2MkwyUE9Nc0tlQ2prVk9jRm13NnZ1WGNodUJuRTRxQ09sUXBrdFhxUXV6SjBSQ01GbTQwaTZ3OWlidGFYUm9wVmFvM2hiYmJublF4WTVuMW1yWmFMblVHbVNhT1dDd25Kc2lsQlZ5V3U2RmZuZm5icjJTcjV1N1RDd3hDWFMiLCJtYWMiOiI5NmZjYjhhZTY1ZTlmZWQwNTBhZjA5ZTJmNjcxZDVkY2U5NTRmMDFjNWQxYzk1NWVlNzY4YzEzNTA5ODA1MDI3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhJZDg5TTZVUlIyU0JSTkZOYmdhN0E9PSIsInZhbHVlIjoiTGdHVFlzTDR4REJVN0pBSjlOQmZIall0RHdYSVlFRmFHQUFqNnJxclRSSTJrQ3hZZ3dVT1ZUcEZRUjhlYjF5WEgwQ29YOHlhZFpmeDJrUWZsZm1mU1F4RWQyU2lqKy9rdTBzdmNSQXI1Mzd1QzBjZEpvaWpOUmJWY1FQc3cyU1RSbnBiWGs5a3hnOEt2M0dkRi9HZm55L0xXNHJscHpWcXh4M1dBeGVDc3VKVFBIdzB0UU40T1ZXY1N0Q2c2TUpHVHBtQ0o1ZUJvN0hNcXJMQ0ZnOEFtTzlPOVlEcEJ0eHdPL1JqL3kzVmJReGdaYWRGRWU2bExmYWtmWFpTcy9RZ3MxSE4zem10TzlqaW51RHZMaGZOL24vMncvQ0czaU1JbUYvUVZ1TTRvUlYyV2pBRXZmNzViYU5HdGpHZDRuZHpqZmRPTVZJdmpsOU5QZU9EOHBIYjNkWm5jSkNPODI4dWhTNFZkVGR2WTVkQkIyWDJvWkNmZzFkRHlML0RqdmIvc211RDBBanlFVzFyVlZVU2ZWTTFnQ2NUSEZ3Sk54Tk1ucUNENFAxMnh6K2JmVnplVXdMTERoTUR1c203cXp2MEdkVWExMWw5N2dhV0NWeVZyQmxtLzVWTnZqdDhWZDdXL05LSnV4OXhiS2lJbFQvTjltaGxZbEt4UUtHQ1BydEsiLCJtYWMiOiI5NWJhYmU0NjdkYjE1MDI1NmMwODNmZWQwYTNjOWQyMGY5MTdhNGU3MmU5YmJkNTYwMTc4ZTU1Y2QxOTkzM2JmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31703905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1610319757 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2TJKqHAtljzEVNQBDmGi5b8BsPcUfJ7M5tLKIIbJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1610319757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1017603121 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:24:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii95VXJDeG5CZC95VjhQbFdCbFFLWWc9PSIsInZhbHVlIjoiVkIzL1U1akJvWEFPQ1RLUzMyVVJMdks1K0tRV1h4YWN5dGczVkhzOERNNEFNMDJxQTBpOGw4OUlPN1RnYVdZTFhCY0hUNzF2Y3kwL2FZaHg1TE1OenIvVlJBNmhzczRZN3M2Q3AzcVlYOFNtOXJhRmRkR0RpWW1LTVNJdnZMTVE4K0p4cEt3a0tmejlVaVhxL2tUUkNORjF4NTFMMWJrNnlRVmc1MXVmSXVhelgyZDRWeWR3WC9DQzBqVlpsNGI4S09DVW1Hd0pMVkVXbE9qTVozd2YyM2FQdm13MURjODBOY3VpQ3J6RG5zY0pieHdXNEdOMlAzd0QrS2ZIaWJIMlp0QUl2UEJsMnpNa0RSb01Yc21mSXBaZ0xpNysrN3N4RGJ5UFpzc0pBeVhJSkZLY1psSUthVkJocy9waFN2K0lDWDNJVHdaL1BnMUJ2VkFxNU5OY1pJdUNyVHVxOHp5M2Jmc1l6dDQ3WkVQelBNQVk2a2RjcllUTDRZQjFWVUVkVzRITWp1MkdGU3l5ZGZRZkxsdFdJSkdvM0NISEhtVEYxWTRpenlqeXNETzFlbnpqZjluNTUwRjB1UnZKSDRha1JUTzFOYXo2ZDA4TTkrcTZiY1loTHE1aHRXTmJ2ZUM3NGNwRlRMeGtiQm9ROEZYRGVSRDB2dUZBdDVTOS9zQ2YiLCJtYWMiOiI4MGZhNWYwZWFiNDE2NGMyNDBhYmZkMTU1Y2JhZmIwOWQ2MDkzMjVjMTg0OTMxYjc3YzZhZGQ4MTg3NWExYjcwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Inoyd1A2b1ByN3RaNW1XTTkvZVZiS1E9PSIsInZhbHVlIjoiZlFmbndMQjhVd3pNRlZ5UENIQUpqYlZMZ0FxTFFCUWRuaFJYeWtFbDdCVjVETm9ySVhjb3ZPenBDcTZIMFU0Sy9ZL3p1ZVBEWjNLbXR0R2lsK3ZHYml6RWtHelR5S0xsMjhOSlpJb2hBVVlySXkvZnVFZ3BIQ0Jnb21XYWVSNVNMMlIwOE5KTFQxejQrQXErMjVMYlFRZmpSOVdWMTZyS0VwQjROZXViVk9aZDZHZEI0M2R4ZlN2OUxzeVZJb3M3U2x1TkpscXRGQ2JEVUFDT1cxS2ppeXRISG5SNWVxbE1Vc0pUUVVsLzlYK3pMVlZLa1ozeWRSUk40Z0FiT2RsRGtCWWprQ0NoUndhWDIycGxFMnBtekkrOXVYS05URGRtQjRjaUY4N2dMREMzai9PdE9wWU04eUdobHNMV1NmYkcrSlZlR1pqZWcwbnZLOFExc0NqaW0yeEZmMkpsY2tOeWpIeXppdnlJTDg5NkUrUEk4c08zVzVuRlBXSk5mVnJTbzFMUENjOFZpcUZMbHJoaU1Ed2Rhb3RNZHJhK1pHV1VRQmZrS1hNbFJHVS8yZXF1VWJJcG1uc2U0eWZ2REVyK2xxL3hRU2xmVGY4VmtvZjR6dFN4OG94NW5MYVF1a25JczR2WnpueGR4VTA3N1MvSkFpQVE1ZTF6RUJ2Q243MHkiLCJtYWMiOiIyMGEzMmM4YzhlNGFmODUzMTJhY2IyMWZkZjRjMDZiMjc1MTg2YzQwYzVlNDBmMjUyZWViYzc5NzQxYjZiN2M0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii95VXJDeG5CZC95VjhQbFdCbFFLWWc9PSIsInZhbHVlIjoiVkIzL1U1akJvWEFPQ1RLUzMyVVJMdks1K0tRV1h4YWN5dGczVkhzOERNNEFNMDJxQTBpOGw4OUlPN1RnYVdZTFhCY0hUNzF2Y3kwL2FZaHg1TE1OenIvVlJBNmhzczRZN3M2Q3AzcVlYOFNtOXJhRmRkR0RpWW1LTVNJdnZMTVE4K0p4cEt3a0tmejlVaVhxL2tUUkNORjF4NTFMMWJrNnlRVmc1MXVmSXVhelgyZDRWeWR3WC9DQzBqVlpsNGI4S09DVW1Hd0pMVkVXbE9qTVozd2YyM2FQdm13MURjODBOY3VpQ3J6RG5zY0pieHdXNEdOMlAzd0QrS2ZIaWJIMlp0QUl2UEJsMnpNa0RSb01Yc21mSXBaZ0xpNysrN3N4RGJ5UFpzc0pBeVhJSkZLY1psSUthVkJocy9waFN2K0lDWDNJVHdaL1BnMUJ2VkFxNU5OY1pJdUNyVHVxOHp5M2Jmc1l6dDQ3WkVQelBNQVk2a2RjcllUTDRZQjFWVUVkVzRITWp1MkdGU3l5ZGZRZkxsdFdJSkdvM0NISEhtVEYxWTRpenlqeXNETzFlbnpqZjluNTUwRjB1UnZKSDRha1JUTzFOYXo2ZDA4TTkrcTZiY1loTHE1aHRXTmJ2ZUM3NGNwRlRMeGtiQm9ROEZYRGVSRDB2dUZBdDVTOS9zQ2YiLCJtYWMiOiI4MGZhNWYwZWFiNDE2NGMyNDBhYmZkMTU1Y2JhZmIwOWQ2MDkzMjVjMTg0OTMxYjc3YzZhZGQ4MTg3NWExYjcwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Inoyd1A2b1ByN3RaNW1XTTkvZVZiS1E9PSIsInZhbHVlIjoiZlFmbndMQjhVd3pNRlZ5UENIQUpqYlZMZ0FxTFFCUWRuaFJYeWtFbDdCVjVETm9ySVhjb3ZPenBDcTZIMFU0Sy9ZL3p1ZVBEWjNLbXR0R2lsK3ZHYml6RWtHelR5S0xsMjhOSlpJb2hBVVlySXkvZnVFZ3BIQ0Jnb21XYWVSNVNMMlIwOE5KTFQxejQrQXErMjVMYlFRZmpSOVdWMTZyS0VwQjROZXViVk9aZDZHZEI0M2R4ZlN2OUxzeVZJb3M3U2x1TkpscXRGQ2JEVUFDT1cxS2ppeXRISG5SNWVxbE1Vc0pUUVVsLzlYK3pMVlZLa1ozeWRSUk40Z0FiT2RsRGtCWWprQ0NoUndhWDIycGxFMnBtekkrOXVYS05URGRtQjRjaUY4N2dMREMzai9PdE9wWU04eUdobHNMV1NmYkcrSlZlR1pqZWcwbnZLOFExc0NqaW0yeEZmMkpsY2tOeWpIeXppdnlJTDg5NkUrUEk4c08zVzVuRlBXSk5mVnJTbzFMUENjOFZpcUZMbHJoaU1Ed2Rhb3RNZHJhK1pHV1VRQmZrS1hNbFJHVS8yZXF1VWJJcG1uc2U0eWZ2REVyK2xxL3hRU2xmVGY4VmtvZjR6dFN4OG94NW5MYVF1a25JczR2WnpueGR4VTA3N1MvSkFpQVE1ZTF6RUJ2Q243MHkiLCJtYWMiOiIyMGEzMmM4YzhlNGFmODUzMTJhY2IyMWZkZjRjMDZiMjc1MTg2YzQwYzVlNDBmMjUyZWViYzc5NzQxYjZiN2M0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017603121\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-275839216 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Fyq4sPWVtHOCQpxmYDC32zJqGlvV7dp4sPPhRvLy</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>14</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275839216\", {\"maxDepth\":0})</script>\n"}}