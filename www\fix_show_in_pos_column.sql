-- Direct SQL script to add the show_in_pos column to the product_service_categories table

-- Add the column if it doesn't exist
ALTER TABLE `product_service_categories` 
ADD COLUMN IF NOT EXISTS `show_in_pos` TINYINT(1) NOT NULL DEFAULT 1;

-- Update existing records
UPDATE `product_service_categories` SET `show_in_pos` = 1 WHERE `type` = 'product & service';
UPDATE `product_service_categories` SET `show_in_pos` = 0 WHERE `type` != 'product & service';
