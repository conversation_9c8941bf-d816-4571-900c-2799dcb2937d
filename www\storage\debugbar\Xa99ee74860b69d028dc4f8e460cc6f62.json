{"__meta": {"id": "Xa99ee74860b69d028dc4f8e460cc6f62", "datetime": "2025-06-06 19:24:41", "utime": **********.440492, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237879.756364, "end": **********.44053, "duration": 1.6841659545898438, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1749237879.756364, "relative_start": 0, "end": **********.19707, "relative_end": **********.19707, "duration": 1.4407057762145996, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.197095, "relative_start": 1.4407308101654053, "end": **********.440534, "relative_end": 4.0531158447265625e-06, "duration": 0.2434391975402832, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.030329999999999996, "accumulated_duration_str": "30.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.302885, "duration": 0.025079999999999998, "duration_str": "25.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.69}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3572788, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.69, "width_percent": 5.407}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.397827, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 88.098, "width_percent": 6.462}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4149709, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.56, "width_percent": 5.44}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-583151195 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-583151195\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-865875769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-865875769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1019014271 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019014271\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-720062986 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237867275%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM4Q01yY0YvSDlDQXVWSzh4QmgzMFE9PSIsInZhbHVlIjoiWkk1V1NBTFJ6U3BxQmJsM1E3L0dXczdwT0QrMVhqK0dkcXk4RHRpNUxIZEtvOWNaOHFnTFVjeERTbHFPUmlpSGwra0tObC9hcEVoejhWSEJ3V2NRRndWblRZNkJyM3ZjeHNhRCs2ZEpzUER6VjB0UkZxd2YyVUh1ZzR0WUpHajl6N2NTMzlvbVgxUmdBa1JXNzIrODRzUjNIMk53RmNHZ0dpbVN1ZGpqdlkyZjVhMDFBRUtkQWo1TkVTQlQyTUhiY2VhNFlIOXAzdkVja2M3MW54U2JBOU1FVlQwU1ZBdW02Zi9nUjVNTXNrRjRKZWFRSkRVWUs3NGYwNEwyTVh1Q3hSWWh6bXpJb3Y4dytRZVV4VSt0NmRhOUZQQ3pianpKRVFza21FV2ZlYmZzMzVmRGZubGw1N2JDQXg4RnRSRWJHcnhjZnVjQXIyVnR3QWJqTXljdEprV25NMEIzNENudDdTcGtmeVQwenZVS1h0RVJrS2NjcFpMWGhZcEFOcWxNd1BVTXJsV04yMUhVcWpnWG9VU20va3lwdTFGWlRHalZHbFJERDY4RHVaNlVocytaSGI2ZjJpQ1pTU2lFRTBza3QwR2NtWGg2WGwvSVRKVGpLbUZCUklmU09zZmY1MTdYZXphYVN0RGJVV21xZFFBSjROYjNPSWM4QjNYbVMzTnQiLCJtYWMiOiI0ZjUwMjdjZjZjMGY4YWE0NzJlZjdlNDVjZjNiNjc2ZTI0OGMwYzUyNzI5MjM4M2U3ZTU4ZGJlM2ViMzNkZDQ2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilo3VHlaWGlDRSt5V2tRY0ptcVlXdEE9PSIsInZhbHVlIjoiaTZhK3N3dDFBU3YrTlZsM0xvNVlVRDZGd2ZTa3RETHJBdWF3clZhY3NXSXhrZXVqdXlORThkUnVhK0F2b1Q0a1BMTUh2MEJUa2ppTTQycUJhR29DdnM5UDB5emVybTllK09mOW92K0ZSd1MrL0dYZk96aUZCcTcrSFRFNjhyR0d2dmF5UUJ2Nk1wQTJiTzduWW5YbmM5K3hkc1pETlhZajFmMDZzVENJSEVIcHVZUUIwamhuZXplY0ZLQXhmbUtrQ3I2cU12K09ZVlkxWmIxNnU5Qktnd25JRnZMbzFHc1VBK1lSVFVtRUdLVUxPUGgrQ2ZqalBlNUJpSjZwM0U5TE9YV3BlcmcwYUN3K25PU2ZmOTc0a01xYnRvT1lPQmI5TU9iSDVJa215WmE4NG5GaldmWWJBRWdnZEN0NjJldmJoOGk1QTg2RW9jS3JHSW1ZMXE0VEQ2QVJuS2U4Mm9sbmlucGxyTk4yYU5CbjRKQTBkY1ZOYUVHZk5RSzVBU3I4N0ljRnozL1ZseWdOdTBCRWd1L2NiaGxmSFRPNHVmdjYwdFo2aFY4UHlTN3ZRMU14TlFPa0dKQ2dzWUpvS05FYlREQ1l2WFF3Z2dnSGFVelpDWG5TRG9DclpCaS9qSHFoSnlzS3dkK2xaV3ZLOG1PdGJvS3B0QWFGSXZmTWpNMlkiLCJtYWMiOiI5Zjg4NzJiNzE4MGQ3MmJmYzZhMzQ1NWUxNzM2MzFjMzgyYWE0OGIzMTgzYzc0MWYzNzM4NjQyMTRmN2ZhMzIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720062986\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-737870483 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bqimaRKNMYDaY70bMn4neav5Q7SUSQNahmKxKJLG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737870483\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-562796476 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:24:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBPQTA1VnhXSmVGdk8rYVdzTzZMUmc9PSIsInZhbHVlIjoiOERYQ2dQU1RHdGgzdGIyQXRQbGJ0aEtZS1lnMXF1MEdlczZ4VE8vUkovOHdnTElsaVlvbnhCR09rM0p0VS9JQTlNTG0wSVZJL1JFbWdHbkp0RjIzRitRVmxsb0tLNFlZSUlLK1Axc0cwdFk3ZXJhVWdXT1paZDVlbTBwKzViVWNsdDJIRWVPVXNqN0xZM3F6ZkFEdjdXMldMOElaTmpvcUNvem9BQk5yOUE1alJOL2p6dHZJOHpnRGJsTmk1Z2pma000cFNoK2VjYkMvZVBWR2taU210V3JFSFg4c1MxYi9JZEhQM3JibVpiYnBhRnhDVEkyMHNDc0xXZ1E3YkROL3BBaW1xd0NDMTdyWUErMklJQ1dNaXNNai90T3ZKV0Y2RWpYcGdTL05mMk41Rnp3T00vcFhTMkZ1K3h6bXZWVUNXWHpGd0t2a2RuZHQ1Ukh0WFdXc3RiZm13anpzM1FsZ1RILzhaMWxEWmZML1VSNXVnV21nMDBrSWxoNTRpcWR5eEFGZERxa2pVd1VJYytyYmc0eHdBa3doMVlXLy8wdlMybVk3V0FTMG5QejFzZVdoTHZyNitjeUY2OWQxTWdDOHpSZ3FJUHUvMEdWSTdqL1c2TVpLYnVldWVpMTZjOEpIOVB2WE1reWZsdTUwMU5GNisvMjJSVUJoTUJmdk0zVXciLCJtYWMiOiI4MjEwODUzN2Y3YWZhODAyYjk3NWU1ZWVhNjEyYmVjZGY0NTIyZTk3NDQ4YTE1NzQ1MmVhYTRhOTg5ZmUwZWYzIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjVRendTeFRzTTJtZ3owb2NwWExZRmc9PSIsInZhbHVlIjoieXRlamVycU0xTW1JRVhRTzZzSnU5QS9NaEsxVHJLaTdGa2FiSHNlZkxXUUpJRGNkRTY0SGlLK0ZnMHdJQUNHRzgyS1EybWZvcFpJanJpS28vbW1maUVYRXR1dTE1WE1GcWZmb05zSlJoZ2VYN2JSbFpHZ1ZNMGd0aUZpZ25VS3kyOUdEa1QwVDM2dWpmcGYyazVaT0NUbXRxUDJVRFRpd3N1N1NNd094bWhJaERWWHZoRmZsc1JqMEFHRWtFS29DMG9pUWxHd3I1NFJIbFg5Tm5uR1pCaHdSeXZkSmYrNldLcUVObEhsSkMrbjFZSFpQS1ZPSEFFSmp2OTFhTXI5ZzFzRXptODZHK1ROS251RGo1VlZEUEV5MU42QkxQaUpWVzVrZ0pwNHhhd0tSQTBFM1htOXIrQnZINnI1NkhBVzBVSzJwM0hZZkx5ODZ3OGxaRlNxSVZaejRSZk9kTnk4NktRcEdTWlJVbDFIMmNuREJWRDNZVEZZemdQTHAvNlRjczk2Y3NURXFJRFYvZ01uS1JlaFkyZ0ZtbnZpbkFUWmhYWlNQZjJWdnZYN2xjdnFESGVBUHlCbDg3YmRqYTJ5QVUrRERsRTRJWDNqbUhmbWovRTAvNWhQYXJRQW9TYjdqeGNnUGl3dC82Um4vNVdYYkVZMUxUOENzWFlqU2trWTMiLCJtYWMiOiI4OGRjOWExODYzZDA0ZjNkYzIyMjU3NjMxMTUxMmM2NjYzYTc4N2Q4NGY2OWIxNDJjNWUyZmEzMjVjODc0ZmQxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBPQTA1VnhXSmVGdk8rYVdzTzZMUmc9PSIsInZhbHVlIjoiOERYQ2dQU1RHdGgzdGIyQXRQbGJ0aEtZS1lnMXF1MEdlczZ4VE8vUkovOHdnTElsaVlvbnhCR09rM0p0VS9JQTlNTG0wSVZJL1JFbWdHbkp0RjIzRitRVmxsb0tLNFlZSUlLK1Axc0cwdFk3ZXJhVWdXT1paZDVlbTBwKzViVWNsdDJIRWVPVXNqN0xZM3F6ZkFEdjdXMldMOElaTmpvcUNvem9BQk5yOUE1alJOL2p6dHZJOHpnRGJsTmk1Z2pma000cFNoK2VjYkMvZVBWR2taU210V3JFSFg4c1MxYi9JZEhQM3JibVpiYnBhRnhDVEkyMHNDc0xXZ1E3YkROL3BBaW1xd0NDMTdyWUErMklJQ1dNaXNNai90T3ZKV0Y2RWpYcGdTL05mMk41Rnp3T00vcFhTMkZ1K3h6bXZWVUNXWHpGd0t2a2RuZHQ1Ukh0WFdXc3RiZm13anpzM1FsZ1RILzhaMWxEWmZML1VSNXVnV21nMDBrSWxoNTRpcWR5eEFGZERxa2pVd1VJYytyYmc0eHdBa3doMVlXLy8wdlMybVk3V0FTMG5QejFzZVdoTHZyNitjeUY2OWQxTWdDOHpSZ3FJUHUvMEdWSTdqL1c2TVpLYnVldWVpMTZjOEpIOVB2WE1reWZsdTUwMU5GNisvMjJSVUJoTUJmdk0zVXciLCJtYWMiOiI4MjEwODUzN2Y3YWZhODAyYjk3NWU1ZWVhNjEyYmVjZGY0NTIyZTk3NDQ4YTE1NzQ1MmVhYTRhOTg5ZmUwZWYzIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjVRendTeFRzTTJtZ3owb2NwWExZRmc9PSIsInZhbHVlIjoieXRlamVycU0xTW1JRVhRTzZzSnU5QS9NaEsxVHJLaTdGa2FiSHNlZkxXUUpJRGNkRTY0SGlLK0ZnMHdJQUNHRzgyS1EybWZvcFpJanJpS28vbW1maUVYRXR1dTE1WE1GcWZmb05zSlJoZ2VYN2JSbFpHZ1ZNMGd0aUZpZ25VS3kyOUdEa1QwVDM2dWpmcGYyazVaT0NUbXRxUDJVRFRpd3N1N1NNd094bWhJaERWWHZoRmZsc1JqMEFHRWtFS29DMG9pUWxHd3I1NFJIbFg5Tm5uR1pCaHdSeXZkSmYrNldLcUVObEhsSkMrbjFZSFpQS1ZPSEFFSmp2OTFhTXI5ZzFzRXptODZHK1ROS251RGo1VlZEUEV5MU42QkxQaUpWVzVrZ0pwNHhhd0tSQTBFM1htOXIrQnZINnI1NkhBVzBVSzJwM0hZZkx5ODZ3OGxaRlNxSVZaejRSZk9kTnk4NktRcEdTWlJVbDFIMmNuREJWRDNZVEZZemdQTHAvNlRjczk2Y3NURXFJRFYvZ01uS1JlaFkyZ0ZtbnZpbkFUWmhYWlNQZjJWdnZYN2xjdnFESGVBUHlCbDg3YmRqYTJ5QVUrRERsRTRJWDNqbUhmbWovRTAvNWhQYXJRQW9TYjdqeGNnUGl3dC82Um4vNVdYYkVZMUxUOENzWFlqU2trWTMiLCJtYWMiOiI4OGRjOWExODYzZDA0ZjNkYzIyMjU3NjMxMTUxMmM2NjYzYTc4N2Q4NGY2OWIxNDJjNWUyZmEzMjVjODc0ZmQxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562796476\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1945567074 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945567074\", {\"maxDepth\":0})</script>\n"}}