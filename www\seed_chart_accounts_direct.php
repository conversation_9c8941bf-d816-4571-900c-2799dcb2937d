<?php
// تشغيل بذور شجرة الحسابات مباشرة
echo "<h1>إنشاء بيانات شجرة الحسابات</h1>";

// إعدادات قاعدة البيانات
$host = '127.0.0.1';
$dbname = 'ty';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات ناجح</p>";
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>إنشاء أنواع الحسابات</h2>";

// إنشاء أنواع الحسابات
$accountTypes = [
    [1, 'Assets', 1],
    [2, 'Liabilities', 1],
    [3, 'Equity', 1],
    [4, 'Income', 1],
    [5, 'Expenses', 1],
    [6, 'Costs of Goods Sold', 1],
];

$stmt = $pdo->prepare("INSERT IGNORE INTO chart_of_account_types (id, name, created_by, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");

foreach ($accountTypes as $type) {
    try {
        $stmt->execute($type);
        echo "<p style='color: green;'>✓ تم إنشاء نوع الحساب: {$type[1]}</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ نوع الحساب موجود مسبقاً: {$type[1]}</p>";
    }
}

echo "<h2>إنشاء الأنواع الفرعية</h2>";

// إنشاء الأنواع الفرعية
$subTypes = [
    [1, 'Current Asset', 1, 1],
    [2, 'Inventory Asset', 1, 1],
    [3, 'Non-current Asset', 1, 1],
    [4, 'Current Liabilities', 2, 1],
    [5, 'Long Term Liabilities', 2, 1],
    [6, 'Share Capital', 2, 1],
    [7, 'Retained Earnings', 2, 1],
    [8, 'Owners Equity', 3, 1],
    [9, 'Sales Revenue', 4, 1],
    [10, 'Other Revenue', 4, 1],
    [11, 'Costs of Goods Sold', 6, 1],
    [12, 'Payroll Expenses', 5, 1],
    [13, 'General and Administrative expenses', 5, 1],
];

$stmt = $pdo->prepare("INSERT IGNORE INTO chart_of_account_sub_types (id, name, type, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");

foreach ($subTypes as $subType) {
    try {
        $stmt->execute($subType);
        echo "<p style='color: green;'>✓ تم إنشاء النوع الفرعي: {$subType[1]}</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ النوع الفرعي موجود مسبقاً: {$subType[1]}</p>";
    }
}

echo "<h2>إنشاء الحسابات</h2>";

// إنشاء الحسابات
$accounts = [
    [1, 'Checking Account', 1060, 1, 1, 0, 1, 'Main checking account', 1],
    [2, 'Petty Cash', 1065, 1, 1, 0, 1, 'Petty cash account', 1],
    [3, 'Account Receivables', 1200, 1, 1, 0, 1, 'Customer receivables', 1],
    [4, 'Inventory', 1510, 1, 2, 0, 1, 'Main inventory account', 1],
    [5, 'Accounts Payable', 2000, 2, 4, 0, 1, 'Vendor payables', 1],
    [6, 'Accrued Liabilities', 2100, 2, 4, 0, 1, 'Accrued expenses', 1],
    [7, 'Owner Equity', 3000, 3, 8, 0, 1, 'Owner equity account', 1],
    [8, 'Retained Earnings', 3200, 3, 8, 0, 1, 'Retained earnings', 1],
    [9, 'Sales Revenue', 4000, 4, 9, 0, 1, 'Main sales revenue', 1],
    [10, 'Service Revenue', 4100, 4, 9, 0, 1, 'Service income', 1],
    [11, 'Other Income', 4200, 4, 10, 0, 1, 'Miscellaneous income', 1],
    [12, 'Office Expenses', 5000, 5, 13, 0, 1, 'General office expenses', 1],
    [13, 'Rent Expense', 5100, 5, 13, 0, 1, 'Office rent', 1],
    [14, 'Utilities Expense', 5200, 5, 13, 0, 1, 'Utilities and services', 1],
    [15, 'Salary Expense', 5300, 5, 12, 0, 1, 'Employee salaries', 1],
    [16, 'Cost of Goods Sold', 6000, 6, 11, 0, 1, 'Direct costs of products sold', 1],
];

$stmt = $pdo->prepare("INSERT IGNORE INTO chart_of_accounts (id, name, code, type, sub_type, parent, is_enabled, description, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");

foreach ($accounts as $account) {
    try {
        $stmt->execute($account);
        echo "<p style='color: green;'>✓ تم إنشاء الحساب: {$account[1]} (كود: {$account[2]})</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ الحساب موجود مسبقاً: {$account[1]}</p>";
    }
}

echo "<h2>فحص النتائج النهائية</h2>";

// فحص النتائج
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_account_types");
    $typesCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_account_sub_types");
    $subTypesCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM chart_of_accounts");
    $accountsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>النوع</th><th>العدد</th><th>الحالة</th></tr>";
    echo "<tr><td>أنواع الحسابات</td><td>$typesCount</td><td>" . ($typesCount > 0 ? "<span style='color: green;'>✓</span>" : "<span style='color: red;'>✗</span>") . "</td></tr>";
    echo "<tr><td>الأنواع الفرعية</td><td>$subTypesCount</td><td>" . ($subTypesCount > 0 ? "<span style='color: green;'>✓</span>" : "<span style='color: red;'>✗</span>") . "</td></tr>";
    echo "<tr><td>الحسابات</td><td>$accountsCount</td><td>" . ($accountsCount > 0 ? "<span style='color: green;'>✓</span>" : "<span style='color: red;'>✗</span>") . "</td></tr>";
    echo "</table>";
    
    if ($typesCount > 0 && $subTypesCount > 0 && $accountsCount > 0) {
        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin-top: 20px;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✓ تم إنشاء البيانات بنجاح!</h3>";
        echo "<p>يمكنك الآن الذهاب إلى صفحة شجرة الحسابات وستجد البيانات تظهر بشكل صحيح.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في فحص النتائج: " . $e->getMessage() . "</p>";
}

echo "<h2>الخطوات التالية</h2>";
echo "<ol>";
echo "<li>تأكد من تسجيل الدخول إلى النظام</li>";
echo "<li>تحقق من أن لديك صلاحية 'manage chart of account'</li>";
echo "<li>اذهب إلى صفحة شجرة الحسابات</li>";
echo "</ol>";

echo "<br>";
echo "<a href='http://localhost/chart-of-account' target='_blank' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>اختبار شجرة الحسابات</a>";
echo "<a href='debug_chart_simple.php' style='background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فحص البيانات</a>";
?>
