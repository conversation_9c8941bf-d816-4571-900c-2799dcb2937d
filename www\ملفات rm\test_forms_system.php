<?php
/**
 * ملف اختبار نظام النماذج
 * يمكن تشغيله من المتصفح لاختبار النظام
 */

// تحديد المسار الأساسي
$basePath = __DIR__;

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار نظام النماذج</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }";
echo ".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo "h1, h2 { color: #333; }";
echo "ul { list-style-type: none; padding: 0; }";
echo "li { padding: 5px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🧪 اختبار نظام النماذج</h1>";

// 1. فحص الملفات المطلوبة
echo "<div class='section'>";
echo "<h2>1. فحص الملفات المطلوبة</h2>";

$requiredFiles = [
    'database/migrations/2024_01_01_000000_create_forms_table.php' => 'Migration للنماذج',
    'app/Models/Form.php' => 'نموذج Form',
    'app/Http/Controllers/FormController.php' => 'كونترولر النماذج',
    'resources/views/forms/create.blade.php' => 'صفحة إنشاء النماذج',
    'resources/views/forms/index.blade.php' => 'صفحة عرض النماذج',
    'resources/views/forms/dashboard-section.blade.php' => 'قسم النماذج في الشاشة الرئيسية',
    'storage/app/public/forms' => 'مجلد حفظ النماذج'
];

foreach ($requiredFiles as $file => $description) {
    $fullPath = $basePath . '/' . $file;
    if (file_exists($fullPath) || is_dir($fullPath)) {
        echo "<div class='success'>✅ $description: موجود</div>";
    } else {
        echo "<div class='error'>❌ $description: غير موجود</div>";
    }
}
echo "</div>";

// 2. فحص Routes
echo "<div class='section'>";
echo "<h2>2. فحص Routes</h2>";

$routesFile = $basePath . '/routes/web.php';
if (file_exists($routesFile)) {
    $routesContent = file_get_contents($routesFile);
    
    if (strpos($routesContent, 'FormController') !== false) {
        echo "<div class='success'>✅ FormController مضاف في Routes</div>";
    } else {
        echo "<div class='error'>❌ FormController غير مضاف في Routes</div>";
    }
    
    if (strpos($routesContent, 'forms.show') !== false) {
        echo "<div class='success'>✅ Route forms.show موجود</div>";
    } else {
        echo "<div class='error'>❌ Route forms.show غير موجود</div>";
    }
} else {
    echo "<div class='error'>❌ ملف Routes غير موجود</div>";
}
echo "</div>";

// 3. فحص DashboardController
echo "<div class='section'>";
echo "<h2>3. فحص DashboardController</h2>";

$dashboardFile = $basePath . '/app/Http/Controllers/DashboardController.php';
if (file_exists($dashboardFile)) {
    $dashboardContent = file_get_contents($dashboardFile);
    
    if (strpos($dashboardContent, 'use App\Models\Form;') !== false) {
        echo "<div class='success'>✅ Form Model مستورد في DashboardController</div>";
    } else {
        echo "<div class='error'>❌ Form Model غير مستورد في DashboardController</div>";
    }
    
    if (strpos($dashboardContent, 'Form::getVisibleForms()') !== false) {
        echo "<div class='success'>✅ استدعاء النماذج مضاف في DashboardController</div>";
    } else {
        echo "<div class='error'>❌ استدعاء النماذج غير مضاف في DashboardController</div>";
    }
} else {
    echo "<div class='error'>❌ DashboardController غير موجود</div>";
}
echo "</div>";

// 4. فحص dashboard.blade.php
echo "<div class='section'>";
echo "<h2>4. فحص dashboard.blade.php</h2>";

$dashboardView = $basePath . '/resources/views/dashboard/dashboard.blade.php';
if (file_exists($dashboardView)) {
    $dashboardViewContent = file_get_contents($dashboardView);
    
    if (strpos($dashboardViewContent, 'forms.dashboard-section') !== false) {
        echo "<div class='success'>✅ قسم النماذج مضاف في dashboard.blade.php</div>";
    } else {
        echo "<div class='error'>❌ قسم النماذج غير مضاف في dashboard.blade.php</div>";
    }
} else {
    echo "<div class='error'>❌ dashboard.blade.php غير موجود</div>";
}
echo "</div>";

// 5. تعليمات التشغيل
echo "<div class='section'>";
echo "<h2>5. تعليمات التشغيل</h2>";
echo "<div class='info'>";
echo "<h3>للمستخدمين من نوع Company:</h3>";
echo "<ul>";
echo "<li>1. سجل دخول كمستخدم من نوع 'company'</li>";
echo "<li>2. اذهب إلى الشاشة الرئيسية (Dashboard)</li>";
echo "<li>3. ستجد قسم 'النماذج' في أعلى الصفحة</li>";
echo "<li>4. اضغط على 'إنشاء نموذج جديد'</li>";
echo "<li>5. املأ البيانات المطلوبة وارفع ملف PDF</li>";
echo "<li>6. حدد من يمكنه رؤية النموذج</li>";
echo "</ul>";

echo "<h3>للمستخدمين الآخرين:</h3>";
echo "<ul>";
echo "<li>1. سجل دخول كمستخدم (كاشير، سوبر فايزر، دليفري، محاسب)</li>";
echo "<li>2. اذهب إلى القائمة الجانبية واختر 'النماذج' > 'عرض النماذج'</li>";
echo "<li>3. ستجد النماذج المتاحة لك مقسمة حسب النوع (تشغيلية/مالية)</li>";
echo "<li>4. اضغط على أيقونة العين لعرض النموذج</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

// 6. الملفات المطلوب نقلها للسيرفر
echo "<div class='section'>";
echo "<h2>6. الملفات المطلوب نقلها للسيرفر</h2>";
echo "<div class='info'>";
echo "<h3>الملفات الجديدة:</h3>";
echo "<ul>";
echo "<li>• database/migrations/2024_01_01_000000_create_forms_table.php</li>";
echo "<li>• app/Models/Form.php</li>";
echo "<li>• app/Http/Controllers/FormController.php</li>";
echo "<li>• resources/views/forms/create.blade.php</li>";
echo "<li>• resources/views/forms/index.blade.php</li>";
echo "<li>• resources/views/forms/dashboard-section.blade.php</li>";
echo "</ul>";

echo "<h3>الملفات المحدثة:</h3>";
echo "<ul>";
echo "<li>• app/Http/Controllers/DashboardController.php</li>";
echo "<li>• resources/views/dashboard/dashboard.blade.php</li>";
echo "<li>• resources/views/partials/admin/menu.blade.php</li>";
echo "<li>• routes/web.php</li>";
echo "</ul>";

echo "<h3>أوامر السيرفر:</h3>";
echo "<ul>";
echo "<li>• php artisan migrate (لإنشاء جدول النماذج)</li>";
echo "<li>• mkdir -p storage/app/public/forms (لإنشاء مجلد النماذج)</li>";
echo "<li>• php artisan storage:link (إذا لم يكن موجود)</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
