# نظام مراقبة المستخدمين والأموال

## 📋 نظرة عامة

تم إنشاء نظام شامل لمراقبة المستخدمين والأموال في نظام POS الكلاسيكي، يوفر رؤية شاملة للعمليات المالية والمستخدمين في الوقت الفعلي.

## 🎯 الميزات الرئيسية

### 1. مراقبة المستخدمين
- عرض جميع المستخدمين مع حالة الشفت الحالية
- النقد والشبكة الحالية لكل مستخدم
- إجمالي المبيعات لكل مستخدم
- عدد المعاملات ومتوسط قيمة الفاتورة

### 2. إدارة الشفتات
- عرض جميع الشفتات (مفتوحة/مغلقة)
- رصيد البداية والنقد الحالي
- أوقات الفتح والإغلاق
- ربط الشفتات بالمستودعات والمستخدمين

### 3. المعاملات المالية
- سندات القبض والصرف
- المعاملات النقدية ومعاملات الشبكة
- ربط المعاملات بالشفتات والمستودعات

### 4. مبيعات POS
- عرض جميع فواتير POS الكلاسيكي
- تفصيل المبالغ النقدية والشبكة
- ربط الفواتير بالعملاء والمستودعات

## 🔧 الملفات المنشأة

### 1. Controller
```
app/Http/Controllers/UserMonitoringController.php
```
- إدارة جميع العمليات المتعلقة بمراقبة المستخدمين
- فلترة البيانات حسب التاريخ والمستودع والمستخدم
- حساب الإحصائيات والملخصات

### 2. View
```
resources/views/user-monitoring/index.blade.php
```
- واجهة شاملة لعرض جميع البيانات
- فلاتر تفاعلية للبحث والتصفية
- جداول منظمة لعرض البيانات
- تصميم متجاوب وجذاب

### 3. Routes
```
routes/web.php (تم التحديث)
```
- إضافة route جديد: `/user-monitoring`
- حماية بـ middleware المطلوبة

## 📊 الجداول المعروضة

### 1. جدول مراقبة المستخدمين الرئيسي
| العمود | الوصف |
|--------|--------|
| اسم المستخدم | اسم وإيميل المستخدم |
| المستودع | المستودع المرتبط بالمستخدم |
| حالة الشفت | مفتوح/مغلق مع رقم الشفت |
| النقد الحالي | المبلغ النقدي الحالي |
| الشبكة الحالية | مبلغ الشبكة الحالي |
| إجمالي المبيعات | إجمالي مبيعات الفترة |
| إجمالي الشبكة | إجمالي معاملات الشبكة |
| عدد المعاملات | عدد الفواتير ومتوسط القيمة |

### 2. جدول الشفتات
| العمود | الوصف |
|--------|--------|
| رقم الشفت | معرف الشفت |
| اسم المستخدم | المستخدم المسؤول |
| المستودع | المستودع المرتبط |
| رصيد البداية | الرصيد عند فتح الشفت |
| النقد الحالي | النقد الحالي في الشفت |
| الشبكة الحالية | مبلغ الشبكة الحالي |
| وقت الفتح | تاريخ ووقت فتح الشفت |
| وقت الإغلاق | تاريخ ووقت إغلاق الشفت |
| حالة الشفت | مفتوح/مغلق |

### 3. جدول إدارة النقد والسندات
| العمود | الوصف |
|--------|--------|
| نوع المعاملة | مبيعة/سند قبض/سند صرف |
| اسم المستخدم | المستخدم المسؤول |
| المستودع | المستودع المرتبط |
| المبلغ الإجمالي | إجمالي مبلغ المعاملة |
| مبلغ نقدي | الجزء النقدي |
| مبلغ شبكة | الجزء عبر الشبكة |
| طريقة الدفع | نقدي/شبكة |
| التاريخ والوقت | وقت المعاملة |
| رقم الشفت | الشفت المرتبط |

### 4. جدول مبيعات POS
| العمود | الوصف |
|--------|--------|
| رقم الفاتورة | معرف فاتورة POS |
| اسم المستخدم | الكاشير |
| المستودع | المستودع المرتبط |
| اسم العميل | العميل أو "عميل نقدي" |
| إجمالي الفاتورة | إجمالي مبلغ الفاتورة |
| مبلغ نقدي | الجزء النقدي |
| مبلغ شبكة | الجزء عبر الشبكة |
| طريقة الدفع | نقدي/شبكة/مختلط |
| التاريخ والوقت | وقت إنشاء الفاتورة |

## 🎛️ الفلاتر المتاحة

### 1. فلترة التاريخ السريعة
- **اليوم**: البيانات لليوم الحالي فقط
- **أمس**: بيانات يوم أمس
- **آخر 3 أيام**: البيانات لآخر 3 أيام
- **آخر أسبوع**: البيانات لآخر 7 أيام
- **آخر شهر**: البيانات لآخر 30 يوم
- **فترة مخصصة**: تحديد تاريخ من وإلى

### 2. فلاتر أخرى
- **المستودع**: فلترة حسب مستودع معين
- **المستخدم**: فلترة حسب مستخدم معين
- **طريقة الدفع**: نقدي/شبكة/جميع الطرق
- **حالة الشفت**: مفتوح/مغلق/جميع الحالات

## 📈 الإحصائيات المعروضة

### البطاقات العلوية
1. **إجمالي النقد**: مجموع النقد في جميع الشفتات
2. **إجمالي الشبكة**: مجموع معاملات الشبكة
3. **إجمالي المبيعات**: مجموع المبيعات للفترة
4. **الشفتات المفتوحة**: عدد الشفتات النشطة

### مؤشرات إضافية
- نسبة النقدي مقابل الشبكة
- متوسط قيمة الفاتورة
- عدد المعاملات الإجمالي

## ⚡ الوظائف التفاعلية

### 1. التحديث التلقائي
- تحديث البيانات كل 30 ثانية
- مؤشر تحديث في أعلى الصفحة
- إيقاف التحديث عند التفاعل مع الفلاتر

### 2. التصدير والطباعة
- تصدير البيانات إلى Excel
- تصدير البيانات إلى PDF
- طباعة الجداول
- نسخ المبالغ بالنقر عليها

### 3. اختصارات لوحة المفاتيح
- **Ctrl+R**: تحديث الصفحة
- **Ctrl+E**: تصدير إلى Excel
- **Ctrl+P**: طباعة

## 🎨 التصميم والألوان

### نظام الألوان
- **الأزرق المتدرج**: للعناوين والأزرار الرئيسية
- **الأخضر**: للمبالغ النقدية
- **الأزرق**: لمعاملات الشبكة
- **البرتقالي**: للمبالغ الإجمالية
- **الأحمر/الأخضر**: لحالات الشفت

### التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- جداول قابلة للتمرير على الشاشات الصغيرة
- أزرار وعناصر واضحة للمس

## 🔗 الربط مع النظام

### النماذج المستخدمة
- `Pos`: فواتير POS الكلاسيكي
- `PosProduct`: منتجات الفواتير
- `Shift`: الشفتات
- `FinancialRecord`: السجلات المالية
- `FinancialTransactions`: المعاملات المالية
- `User`: المستخدمين
- `warehouse`: المستودعات
- `Customer`: العملاء

### العلاقات المضافة
- ربط `Pos` مع `User` (createdBy, user)
- ربط `Pos` مع `Shift`
- ربط `Shift` مع `warehouse`
- ربط `Shift` مع `FinancialRecord`

## 🚀 كيفية الاستخدام

1. **الوصول للصفحة**: `/user-monitoring`
2. **اختيار الفلاتر**: حدد التاريخ والمستودع والمستخدم
3. **مراجعة البيانات**: تصفح الجداول والإحصائيات
4. **التصدير**: استخدم أزرار التصدير للحصول على التقارير
5. **المراقبة المستمرة**: اترك الصفحة مفتوحة للتحديث التلقائي

## 🔧 التطوير المستقبلي

### ميزات مقترحة
- إشعارات فورية للمعاملات الكبيرة
- رسوم بيانية تفاعلية
- تقارير مجدولة
- تصدير مخصص للبيانات
- مقارنة الأداء بين الفترات
- تحليلات متقدمة للمبيعات

### تحسينات تقنية
- استخدام WebSockets للتحديث الفوري
- تحسين الأداء للبيانات الكبيرة
- إضافة cache للاستعلامات
- تحسين واجهة المستخدم

---

## ✅ التحديثات المنجزة

### الإصدار 1.1 - التحديث الشامل
- ✅ **إزالة البطاقات القديمة**: تم حذف البطاقات العلوية واستبدالها بإحصائيات مبسطة
- ✅ **تحديث جدول المستخدمين**: إضافة avatars وتحسين التصميم
- ✅ **تحديث جدول الشفتات**: إضافة مدة الشفت وإجمالي النقد والشبكة
- ✅ **تحديث جدول المعاملات**: إضافة أيقونات وتحسين عرض البيانات
- ✅ **تحديث جدول POS**: التركيز على POS الكلاسيكي مع تفاصيل شاملة
- ✅ **تحسين التصميم**: إضافة avatars وأيقونات وألوان محسنة
- ✅ **تحسين الاستجابة**: تصميم متجاوب للشاشات الصغيرة

### الميزات الجديدة المضافة
- 🎨 **Avatars للمستخدمين**: عرض الحرف الأول من اسم المستخدم
- 📊 **إحصائيات مبسطة**: عرض الأرقام المهمة بدون بطاقات معقدة
- ⏱️ **مدة الشفت**: حساب وعرض مدة الشفت بالساعات والدقائق
- 🔢 **عدد الأصناف**: عرض عدد الأصناف في كل فاتورة POS
- 🎯 **تركيز على POS الكلاسيكي**: إزالة الاعتماد على POS V2
- 📱 **تصميم محسن للموبايل**: تحسين العرض على الشاشات الصغيرة

---

**تم إنشاء هذا النظام بواسطة**: Augment Agent
**التاريخ**: 2024
**الإصدار**: 1.1 (محدث)
