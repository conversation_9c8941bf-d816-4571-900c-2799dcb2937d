{"__meta": {"id": "X8fc8951539a557288bde6c4a83b80a30", "datetime": "2025-06-06 19:25:24", "utime": **********.504748, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749237922.804963, "end": **********.50479, "duration": 1.699826955795288, "duration_str": "1.7s", "measures": [{"label": "Booting", "start": 1749237922.804963, "relative_start": 0, "end": **********.264221, "relative_end": **********.264221, "duration": 1.4592578411102295, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.2643, "relative_start": 1.4593369960784912, "end": **********.504794, "relative_end": 3.814697265625e-06, "duration": 0.2404937744140625, "duration_str": "240ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44797960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02286, "accumulated_duration_str": "22.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.364346, "duration": 0.019149999999999997, "duration_str": "19.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.771}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.416211, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.771, "width_percent": 4.899}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.457169, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 88.67, "width_percent": 6.255}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.479067, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.926, "width_percent": 5.074}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-222017001 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-222017001\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-878306953 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-878306953\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1584831536 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584831536\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-647418671 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237917033%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVFenAxSTJ4RFBIZWpUSXV2ZE5DYUE9PSIsInZhbHVlIjoiTUxCL3VSSmRvUGJPNmtLU2xqaDNuV1NhbVUyeEVpM2w0U3UyakVLRFk0MGRmbm9XSk5mR25HMG82YVExalhtZHdIWmhQaGRyTndWNW1QZEZEd3BpbVpPRHFJRU1rVXE5UjJGcUYxQnl4QytiUEQxUXV1UlpqRURSNGw1R3crRzR5MXhMMFJMelVhUU9NTEVSUXlmOXAxdk92UGdNSy9uWkwwWHdWcmdjN2FlTHUvY2MxZjFsZkZHdFRYOXJMUll4Z3YxMnRrdzl2RE1ZSHlhUDA0SGJtOWVlMCtudUdWNzVTT0Q3UXNydlN4dExmYm94cjMzTWE2aVFUd25XK3A1ZGVPbVhJaC9pdUREWGR6OS9ZbmZFdllnYnZ3UCtYQVNmZzljNFV0d0t2bGZ1ZEs2d3lLT3h2OWMvTkFFZEN1Zms1aUx2cVBHbzM5RnhHRC9wWkFPRU9YdHV4VVlFUEx6eHNxR1NCcDgzdUhxeTZ0bmtPV040cGdTTGt2NDh2OFhBMFNGS3RyMWRQbXJ1N1FTZVA2aWVIb1lwVXIzdGVQWkhUbG9UWFBBYmwyeFI1T1ZqcXhkbVFnRUFHM0s3VFVqK0NmdWhZUEFFc1VFQThOQUEzVGFxZlkyekNCcFVVRy8zR2hZZFNsWnNFUG5JT2N2a3VJVWMzbVRrUERCOGRBL0giLCJtYWMiOiI0ZWYyYzA1YWQ0YzAzZDRiMTEyZTgyNjVmZmJlZjU3NGYyN2JjMmM2NGMwMmVjMDBjYjY2OTg1Yjg5MTk3ZmE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlZR2FJNHNqUnBJMTBKcEtpcndnK2c9PSIsInZhbHVlIjoickZmVmZ5dnJBSmxKV1ZLVFhzcWdiWU5GWWs2bkFkWHZheDREeWcwWnc2cHhralR6MXY1TjFzUUwzaEV4Q3I5UWN2dWlZWG1Ka3h6ODNMRVl2YktSU01vejh2YVRibDJSRFFKdGRORlN1T3hLVkZPQjJrMzFvaXdHVlpPM2F6TjFwTW5aSEE0TnlwRVk0SUozUG9iL3RDNG80dlZHV2w4YUV3QTQxSCtXYiszSE9jcWJuRUg3b2lVYW1lRUFrRy9pZ1ZjMjlvcFRYcGJqMWhhckRjeUxQdXdLUWg3Q2ZIelMxNzZVUUlPb3VUQVRVbnFGVytUUThFUUN5eDlmWEVkaFFGOEowWTRCOVVDNmRHMnJrZDNVWG5hczhMWUFMc0Y2RzhqdXRHUmFwVFpNMzFKUFBrUFRtQmpwd2xHZVViQUo3MFZJN2E0c2FwOVRuVUdWbUhETUNmUUhJWk51OVdpbGVDeUZkcHg5czdPZnVueTdLVkIyNDhOaWdZQlpCd0pJbDh6eEljaXZtRGJIUzBkVktyTEpYVHBPK21OUTIvbkVhRDRUcWtlSVp1bUVyT0VYdlhxdm9yZ2VWc1V3bGRHNXpmY21xaFZJa1dZZEY5ZDM2V3RZVnFpaEprbkRzWXRSWElVOWdOUU9mR0ordHMrL21sNHh2bmpWMkdaZDNSaFgiLCJtYWMiOiIwMGQ4MTRlYmNmZmI5NDA1ODY4ZDZiMzEwMGIyNmVjZWFlOGE5OTdkODUxMTJiMTRkZGJiYWNiMDIxZmFkYTEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647418671\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-774098089 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ak8xVOT6bxaCXIKJynTfWCN8KMCSpchuPqW6fa4X</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774098089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:25:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpVTGlURVJXVm9oSzFSVDgxaFFJbUE9PSIsInZhbHVlIjoiamp5cEYwQllRbEZaVUVQblRneVNTVEJRTi83am9wcUYxWi9MRDdtR29CYmEwMnViNERKY3kyYWpWWElnL0IwMkxibnFHZVREd01pT2kzbzBma05jK0h4Q1R5SGZhQWtLamdoZ0JEUDhXVzQzbi84MDQxeWdranF2ZTA4RG1XTyt0R0hJbm44V1N0cXUwU3VsYUs4UnVpT0FUcVpYbURjUk84dHhuSnRYbWZuRTZ6dEdvb0NBM1RPYTByUk10amxYUkt0WDlFaWVpL1ovemIyc2J0Y2ZnOGorZHIzN2VxNGdRcVcyVTZLbVo1QWltcGJtK0tFcVJEUm0rVHFsNEJsd1hPWmIwdWtwVmxHRjlsbDJxNExnSnpUNzhqZXRtMUlQRjFXZTNYQnR4WFd3MVIvb3FaNzVya1lDb09ZUG5EZTRtazZQdnNMVUQ2UWFCUU9aTG9aVGc5SHVPUUcra1NNcWpYYW5CekFtMmFvcm5PQ0Y2YjEwSEI0QVV6ZXVUaUg5ZVRmRkxMODZqelZLT0N0eXR1WGNWampwNllEL094TXd1cHlDdzc4eVdsMGdmaVE1aXdVZmg5akpCSnFTREQwc28xdS9ZL2Y2WGVBTGl2citUemw1ei9mMEFBNXBuZDRNNk5YUVVpODVwRE5RS0ZnaG9VQVJ4cHJFMXYwU3NiQ0ciLCJtYWMiOiI4MzM4Yzk3M2FmMmI1MWVlOTZhOWQ2NmFkNTFiNDcxNjIxOWFjZTNiZjY4YzFiNTY3NDk0MWIzZTMwZDc2M2QwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:25:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFGMmk3OG14emFMcHhnSUFQbitFOXc9PSIsInZhbHVlIjoic2N4c01RWUllK0VmSzFUVlJuTXNNQ253QWZIYzYrcG5kekphZHJPWVI0VUZHUWt5SS9nQzYyRlZYcDFqWHVMZDdTZHdPSUF3dk91VXhBNUZ5S3g1c0tvWHIwVDdoTTZTZlBSdWJ4Z3RUaStzT2dHRWdtV2ZjdmlML0Fuck12QmwzUVJOT1JtWGlXdzREckcyVW5Nc0tPU2NKTTNqSjc2NzdnNk1zWGhkSlV0WDJtN1VxaTVreXllK1d0YVhLL1k4U3VZeXBMRWxJWEZxelBIZGtMc1lHczJxT0JqMnhwbmpjYk5GNGdwNzFMTmhnY2poQ1I3WnZzODh5d0xmK0tUNGhDZlNPMHIvWkpKQXVjb1ZQeFBIZjNPODFEVXBGcjh3TTJnNWx4SHBUemdoRnJEbFpQNGJuVERCdE85L1RyZTZGQ1FNOHdjZW55aGlDTkR3VWYwREFhQUFBSUM3aE9MVDZlS2lRcWhEVXRkNVhLQmQ5bTU1dGpqaVoxN0xaTElEVHJSKzUxeUFVMm1IOWRYZHFKNUFhRDlGU0xxZ0c1RW1WWkhQUkljQW9GTis5dW8wb2tEWmY1RURISHYvbzRwZkVscFlCeTdpNm9XUWpPeG1Qb1Y3TFZpakpBcHMrZFN4T2lSRm9HL1hFR1dnS3JpcDA5eHM0L0UrMUhHeE5pUDkiLCJtYWMiOiI2NDdlYjNhNmIwNTBhZmE3NDUyOGVhMzE1ODUxODYzMDZmMzg0NjQ0NDU4ZWFjNTg2ZGNlNTM2ZDIzMDIzNzZmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:25:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpVTGlURVJXVm9oSzFSVDgxaFFJbUE9PSIsInZhbHVlIjoiamp5cEYwQllRbEZaVUVQblRneVNTVEJRTi83am9wcUYxWi9MRDdtR29CYmEwMnViNERKY3kyYWpWWElnL0IwMkxibnFHZVREd01pT2kzbzBma05jK0h4Q1R5SGZhQWtLamdoZ0JEUDhXVzQzbi84MDQxeWdranF2ZTA4RG1XTyt0R0hJbm44V1N0cXUwU3VsYUs4UnVpT0FUcVpYbURjUk84dHhuSnRYbWZuRTZ6dEdvb0NBM1RPYTByUk10amxYUkt0WDlFaWVpL1ovemIyc2J0Y2ZnOGorZHIzN2VxNGdRcVcyVTZLbVo1QWltcGJtK0tFcVJEUm0rVHFsNEJsd1hPWmIwdWtwVmxHRjlsbDJxNExnSnpUNzhqZXRtMUlQRjFXZTNYQnR4WFd3MVIvb3FaNzVya1lDb09ZUG5EZTRtazZQdnNMVUQ2UWFCUU9aTG9aVGc5SHVPUUcra1NNcWpYYW5CekFtMmFvcm5PQ0Y2YjEwSEI0QVV6ZXVUaUg5ZVRmRkxMODZqelZLT0N0eXR1WGNWampwNllEL094TXd1cHlDdzc4eVdsMGdmaVE1aXdVZmg5akpCSnFTREQwc28xdS9ZL2Y2WGVBTGl2citUemw1ei9mMEFBNXBuZDRNNk5YUVVpODVwRE5RS0ZnaG9VQVJ4cHJFMXYwU3NiQ0ciLCJtYWMiOiI4MzM4Yzk3M2FmMmI1MWVlOTZhOWQ2NmFkNTFiNDcxNjIxOWFjZTNiZjY4YzFiNTY3NDk0MWIzZTMwZDc2M2QwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:25:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFGMmk3OG14emFMcHhnSUFQbitFOXc9PSIsInZhbHVlIjoic2N4c01RWUllK0VmSzFUVlJuTXNNQ253QWZIYzYrcG5kekphZHJPWVI0VUZHUWt5SS9nQzYyRlZYcDFqWHVMZDdTZHdPSUF3dk91VXhBNUZ5S3g1c0tvWHIwVDdoTTZTZlBSdWJ4Z3RUaStzT2dHRWdtV2ZjdmlML0Fuck12QmwzUVJOT1JtWGlXdzREckcyVW5Nc0tPU2NKTTNqSjc2NzdnNk1zWGhkSlV0WDJtN1VxaTVreXllK1d0YVhLL1k4U3VZeXBMRWxJWEZxelBIZGtMc1lHczJxT0JqMnhwbmpjYk5GNGdwNzFMTmhnY2poQ1I3WnZzODh5d0xmK0tUNGhDZlNPMHIvWkpKQXVjb1ZQeFBIZjNPODFEVXBGcjh3TTJnNWx4SHBUemdoRnJEbFpQNGJuVERCdE85L1RyZTZGQ1FNOHdjZW55aGlDTkR3VWYwREFhQUFBSUM3aE9MVDZlS2lRcWhEVXRkNVhLQmQ5bTU1dGpqaVoxN0xaTElEVHJSKzUxeUFVMm1IOWRYZHFKNUFhRDlGU0xxZ0c1RW1WWkhQUkljQW9GTis5dW8wb2tEWmY1RURISHYvbzRwZkVscFlCeTdpNm9XUWpPeG1Qb1Y3TFZpakpBcHMrZFN4T2lSRm9HL1hFR1dnS3JpcDA5eHM0L0UrMUhHeE5pUDkiLCJtYWMiOiI2NDdlYjNhNmIwNTBhZmE3NDUyOGVhMzE1ODUxODYzMDZmMzg0NjQ0NDU4ZWFjNTg2ZGNlNTM2ZDIzMDIzNzZmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:25:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}