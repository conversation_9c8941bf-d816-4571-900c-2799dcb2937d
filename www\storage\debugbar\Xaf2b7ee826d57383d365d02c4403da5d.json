{"__meta": {"id": "Xaf2b7ee826d57383d365d02c4403da5d", "datetime": "2025-06-06 19:24:42", "utime": **********.638044, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.397539, "end": **********.63807, "duration": 1.****************, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": **********.397539, "relative_start": 0, "end": **********.453026, "relative_end": **********.453026, "duration": 1.****************, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.453043, "relative_start": 1.****************, "end": **********.638073, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02775, "accumulated_duration_str": "27.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.538832, "duration": 0.02463, "duration_str": "24.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.757}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5880952, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.757, "width_percent": 4.216}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.616549, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.973, "width_percent": 7.027}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237880080%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFocSt4RDAzTmVtYTZha0ZjWjlMOFE9PSIsInZhbHVlIjoiM1IzbjJUMEl6KzlRcWd0Sm0zdkdSSk5Qd3JUd2tHN1ViMnIxNStBSHdETmJ2NC9CZkJVQ3FFenkyQTBFbXl3dXNkRGVOWW9hR2l2QmZpQ3hvODZqdEQvQXdnVUhkVDM5Q3VnUU9zbHE2b052dHN3RU80N253ZFhFNXJyYU9EVGJGb2p6bjk1dVZSM2s3U3V1dStxK3VPaU5ZTWFES0dGejBkVXJKVStqZklaTVBkOXpVSGh2cXNXdGFoL1JUV1ZTeG1CUjJEc3pwdzB5QVJwVkpqREpsYWpiUTVDOEtLaFk2VzB3ZElUMHRpOTgzTnVSS2hkWG1JMTZMSkxzU0d4VUxOQmFaWVFoeW9ab2VTZ1lwUUd1R0V5MFVHUXozNUxrdUVwYkZWSE9nYWFVVGdvQm5xVUJRaEFHZXVxemhZOTVIdExlZ3hPMDBKd3NEUjgwTTVJOUd0a2p6M0FlaEt6YnorWmxpa0QyUTZRalIvREl0QUVpMSt1Mk5ld0xqT2kzYVVKQlZrQ0Z4dk0vWFVUeFd0ZlJldkxwY1pSUy9VUmlsZmoxaW1VUnJ6QnVtMGlrWktQQklnRWUyRXFNdGt3QXkwZitmbUwyVFVmaEkxQ0hzSWJwVTllcittU01jTnFaK2hDTGsvb1gxRmJVQmRPOE11K1JTcE5UVS9qREZWVmwiLCJtYWMiOiJlOTJhOThjYTZmODg4NjhjYWJiNmQxZmJiYTBmMTYyY2NmNmI3ZmVjNzU4YWI0OWE4ZDM0NGNhNWJhZjgxOTFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImtOUTdwalg1UVZpV2pRUGl5bktjOEE9PSIsInZhbHVlIjoiZytKVDdVbE1UMmd2Uml1Z1dnTFpoNlE4S2RYVVcwSDJ1UExjc2hRNWRrOXNjeFBoaEFvTEZ6YkNXOEhOOEtZUk1IRmdwU09DRXhka0xkc2poWFJYT0Y1dHA2NmlQRlNUMENzcTNscUJxQ0Myc0JrOVhjUFEvZ2luZFRCZ2RQUzBMUG9CSHcyampoQ01mTm9hNVJEMThVTVNKSStodUVlNjV2NFVYbjlmYlpMbnZmcnl6UG90MFBqUjdSbkxscmZjYjJEbmpNdWhnVGVqc2YzckdwQ1ZwZk5VWkRteXpBWWYwR0J3Y0hSYnRBZEZXdDZ2NVpISWhqYmdhUDNldFRROXNFM2tiUHZWZWpZVDErQlcxa1pqazhUWGpyN015L2ZPYWhwMXdpMkxrdFRtOEFuanVyK3JFTnFLNm5Xck1rV1VoR1RqUVlheGF4OVBodHlEMmlrZFpPaU5XNld6N0hpdVJ4R0FqSndBT2JyUGp5ci9raXBDUHduWTlZcE1lYjBpYW1GMlhIdzUwTCtWVStEeGRSN1Qwc00yWHloNkZDRUtBRmpHay9DcXEwa0s1STNwdiswcU15UDQvcTlPQTRCZGdNQmwwUzFnWjJOU2JIQnNUTW5Xb0s0VWlNL1lsaTEvZ29iaXIzQTlyWnZqR2MwbU1SNW9xSUFpRllENXVoaGkiLCJtYWMiOiI3NWQ5NDJjZjUwZDE0ZTQ5YjQ5NTBlZTZkZjZlOGEwYzJiOTIyOTY2ZjIwYmNkNmE1YTFmOTk4MTIyZDZmNWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-330543425 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bqimaRKNMYDaY70bMn4neav5Q7SUSQNahmKxKJLG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330543425\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-654494090 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:24:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZwSStlN2Z3ZG5SaU0vSS9mS01hL0E9PSIsInZhbHVlIjoiSkFXYi9vQnhCd3B5Q1hZZkJxcnlOMDhLUmlIT2ZIM3I1eFRaTldubzc1R0hHL0UvWVVwbW53bkhPYUl0UFg5S2FCb3Q4a0NibTA1S055NWI2eEc3b0tMTENqRHZYVElKbnBRc1dkaFUzclB4RTN3TUZiendWaUhFVTJqMXArenMvRzltUytJeGNza0drOVBsYjRJRkNhZEdHVmJFd2gwazNIMHFGWTNSR1NxOEJkbXZxbXdBTjBsQXlRWTJndnZQNEtEVU5TYkJ0TEVLTDgyU2ZKcVhoT1RmU0xGVmVOZWNqYngyMlh5TmNuTHRVUjdxdHpKZEo5UHgxZlZwSERSUXpVemRuQkQyN1ZaaENFaEIxU0Q3SEVGL05scDZRSmFGZ3Nrd25UMEFHbXVxUXVrQ2kzR05uUEhvUUVkOFFzbHpGczc3WVZyM255OG53eU9pY0s2dkJyenpZVU4xT3M4M2RXak9rbHdiZlUxTFJTYnZsbFJIdGhJakU4WFBDTlRhV1N4NnJxNVJ5WXR6YmNQcDdOVnlObHNzMWEvYzhjejZtVVNSaWhVc1dOc0lBSzF4ZzVDM00yREo1eEYzZm9rbWRhK1lBMHAzZjUyb2NNOEFEcDI4QXBCR25ucnkxV2FPaEdlc28yRksrMW41WVI2Sm9LaFdWN2UrRmxJbXg0VVAiLCJtYWMiOiJmODFjOWYxNTMyMDhkNmE2M2JmMTc5ZWE3MjU3MWJiMzI3YWJlN2M4YmRkNDdiMGM4ZmQxNTQwYzQ2ZTNkYzllIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVDSmFMRXd2cDJQR2pCWHR6S3NDdWc9PSIsInZhbHVlIjoiVmtrbk1yMzY5Z01LUmFRSmhPWXJNbWdkUUI2RjNSUnk0ZnpQYm5HUzkvSkplemQ2N1F0VCtQQ0lHN09QUGx3aWlPUGQ1cG1jb3VLS0ZFMm5ETEpRajV5REVUSU5vVUpqbTJJbXdtdnUzd0ErTS80RzgxaGxYNCtJVE5JVDBwQ3NZbkNwcWlMRXY3N0pCdnE4M3E5aGxkbGh5V1F2bFBFY1JHaGs3TlJ2ZXhHaEpBaGRWamRxN04zVmVDUzVwV3FqWXVLS0NMUzNpRVVpaUh4U0xraXREVVJ2VnFFYnd0OTY3a0x2bFYzWDNMeVcwYmRHOHZ2WkRLNTRmL254dXQ5TTNsTW42NEVQRXVUQ0NUT3hHVWJQcU5NajVXd1hqVVBiY05uUFVIVTEvOVV1N1ZGakpoMzUwMmgyQzcrQzk1YkxTT0pud0lrbmdDa29qYS9aL1dsNG9HMVNDY2lhUkNiTFE4Ni9LS3BQcWVlQlNaSjliUDJlUHlvK1laUkRzT0xOcEpoUGw2VDVpMDNMTTJpelpDdlpuN0lKbC90aThqQzQ0K0ZSTithSXZVKzFMVlN0UjFwejMyaE1DbTVIeEhqQTVwTDNPZ0FoUWZSWENDNWJSOTVYRGRndXg5SUl1em5ERVVTemNTR0x6NHQycmV4L3VDUnk3RU1oaWZGdXN5eUYiLCJtYWMiOiJhYjAyMmZhOTE1NDAwNDVmNTNkZTIxMThkNmQ0Y2RlMTQxNWU4OGY2MGU0OGExZTBiOGNjMjNhNWRkNjMwMzlhIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZwSStlN2Z3ZG5SaU0vSS9mS01hL0E9PSIsInZhbHVlIjoiSkFXYi9vQnhCd3B5Q1hZZkJxcnlOMDhLUmlIT2ZIM3I1eFRaTldubzc1R0hHL0UvWVVwbW53bkhPYUl0UFg5S2FCb3Q4a0NibTA1S055NWI2eEc3b0tMTENqRHZYVElKbnBRc1dkaFUzclB4RTN3TUZiendWaUhFVTJqMXArenMvRzltUytJeGNza0drOVBsYjRJRkNhZEdHVmJFd2gwazNIMHFGWTNSR1NxOEJkbXZxbXdBTjBsQXlRWTJndnZQNEtEVU5TYkJ0TEVLTDgyU2ZKcVhoT1RmU0xGVmVOZWNqYngyMlh5TmNuTHRVUjdxdHpKZEo5UHgxZlZwSERSUXpVemRuQkQyN1ZaaENFaEIxU0Q3SEVGL05scDZRSmFGZ3Nrd25UMEFHbXVxUXVrQ2kzR05uUEhvUUVkOFFzbHpGczc3WVZyM255OG53eU9pY0s2dkJyenpZVU4xT3M4M2RXak9rbHdiZlUxTFJTYnZsbFJIdGhJakU4WFBDTlRhV1N4NnJxNVJ5WXR6YmNQcDdOVnlObHNzMWEvYzhjejZtVVNSaWhVc1dOc0lBSzF4ZzVDM00yREo1eEYzZm9rbWRhK1lBMHAzZjUyb2NNOEFEcDI4QXBCR25ucnkxV2FPaEdlc28yRksrMW41WVI2Sm9LaFdWN2UrRmxJbXg0VVAiLCJtYWMiOiJmODFjOWYxNTMyMDhkNmE2M2JmMTc5ZWE3MjU3MWJiMzI3YWJlN2M4YmRkNDdiMGM4ZmQxNTQwYzQ2ZTNkYzllIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVDSmFMRXd2cDJQR2pCWHR6S3NDdWc9PSIsInZhbHVlIjoiVmtrbk1yMzY5Z01LUmFRSmhPWXJNbWdkUUI2RjNSUnk0ZnpQYm5HUzkvSkplemQ2N1F0VCtQQ0lHN09QUGx3aWlPUGQ1cG1jb3VLS0ZFMm5ETEpRajV5REVUSU5vVUpqbTJJbXdtdnUzd0ErTS80RzgxaGxYNCtJVE5JVDBwQ3NZbkNwcWlMRXY3N0pCdnE4M3E5aGxkbGh5V1F2bFBFY1JHaGs3TlJ2ZXhHaEpBaGRWamRxN04zVmVDUzVwV3FqWXVLS0NMUzNpRVVpaUh4U0xraXREVVJ2VnFFYnd0OTY3a0x2bFYzWDNMeVcwYmRHOHZ2WkRLNTRmL254dXQ5TTNsTW42NEVQRXVUQ0NUT3hHVWJQcU5NajVXd1hqVVBiY05uUFVIVTEvOVV1N1ZGakpoMzUwMmgyQzcrQzk1YkxTT0pud0lrbmdDa29qYS9aL1dsNG9HMVNDY2lhUkNiTFE4Ni9LS3BQcWVlQlNaSjliUDJlUHlvK1laUkRzT0xOcEpoUGw2VDVpMDNMTTJpelpDdlpuN0lKbC90aThqQzQ0K0ZSTithSXZVKzFMVlN0UjFwejMyaE1DbTVIeEhqQTVwTDNPZ0FoUWZSWENDNWJSOTVYRGRndXg5SUl1em5ERVVTemNTR0x6NHQycmV4L3VDUnk3RU1oaWZGdXN5eUYiLCJtYWMiOiJhYjAyMmZhOTE1NDAwNDVmNTNkZTIxMThkNmQ0Y2RlMTQxNWU4OGY2MGU0OGExZTBiOGNjMjNhNWRkNjMwMzlhIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654494090\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1016112270 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016112270\", {\"maxDepth\":0})</script>\n"}}