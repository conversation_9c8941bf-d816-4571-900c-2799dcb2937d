{"__meta": {"id": "Xacc25742f5ba3b914912248b8836afac", "datetime": "2025-06-06 19:39:34", "utime": **********.45693, "method": "POST", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238772.833276, "end": **********.456989, "duration": 1.6237130165100098, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1749238772.833276, "relative_start": 0, "end": **********.252958, "relative_end": **********.252958, "duration": 1.4196820259094238, "duration_str": "1.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.252976, "relative_start": 1.4196999073028564, "end": **********.456994, "relative_end": 5.0067901611328125e-06, "duration": 0.20401811599731445, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44974112, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=75\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:75-235</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0265, "accumulated_duration_str": "26.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 78}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.390738, "duration": 0.02512, "duration_str": "25.12ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:78", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=78", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "78"}, "connection": "ty", "start_percent": 0, "width_percent": 94.792}, {"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.426197, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "AuthenticatedSessionController.php:81", "source": "app/Http/Controllers/Auth/AuthenticatedSessionController.php:81", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=81", "ajax": false, "filename": "AuthenticatedSessionController.php", "line": "81"}, "connection": "ty", "start_percent": 94.792, "width_percent": 5.208}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"status\"\n  ]\n  \"new\" => []\n]", "status": "Your Account is disable from company.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-502508367 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">144</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=1jor13%7C2%7Cfwj%7C0%7C1983; XSRF-TOKEN=eyJpdiI6IkhPSXFQV2VBK3VOKzkxaVA2VVJqV1E9PSIsInZhbHVlIjoiR3ZuOVpDdUlTd0hTdkxpdXBuWUg3MWxIanVVUGplTkk2OFRhUnZUYUhZcW9MSzk4MXdqNjdUejlzYnRWNU9rNU4xNFZHK3lmaHlDOTUyQ0NNU2NCOTlBSTJxbWR5V2tsWHBZVDR1YlR5ZWFhNHVQZ0VjODZUT1ZiQmVLa2VqNFBqY1lnYS9vdGVjUnlsSTBFQm5JRjV6MEtpcWt2OU5YMVcwVWh0dHVta1JZUzZrWkIvRmFRbjhBTm5HT1k0VUE5bzEyTjAvSzJyZWU4ZTA1dlN0Z0xHc0xWRVVYVFYwem9MSStEYVVHM2FjT3ZRdWpaUFdiSWRQMXRFQ29zN04ydk5RZ2RVeWdSaWczQUVOcFRsNXVJaml6WmZtZ0VuS1h3dFZoTnkyMFJrdnRGMnFRZ0ZYZ3MzK0xXdGI1c3RVZ2ltV2EyR1JEZzhUSTl1N3ozSmt0d2oyT0k3NXQ3MnI0Wi9WbEVRMEF1RWFva095VGF6eHV3YWNOaFl2WnNCZ1ljZGhuVjNYQ0dYMHlnRmZ1U1hlOC9NUUxmL2lhL2tMU29pRHhUNW1xM1F2L2Z4WE90WFFmVGRvRWFHK2NvakNWZmJrQUQ4Wlo2U2tZWjRrT1QvalVCb3NrSHlZY0VyZTcvSGRWRFFISDFxcjFJZy8yaC9DdkxLNXE3Y0k1QnJEQ08iLCJtYWMiOiIwMGQ1NTE0NzdkMGZkMWVkNDQyNGI3MzZkNmNjMWRiOTViZGJkNzhkZGM1NDJhYWE5MTcwMTgxOTMwNzFlNTE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhpY3hrRFkxU1oyS2daWFFpTnZSQWc9PSIsInZhbHVlIjoiSzZLMlcwRmgySEpSeGVjRjFuUWtMdjdWQk1keEw1N1IyeE9Gd1JxSjd6ek5ZOGdVMUc0THBBcC9GQlFhN1VDd2hqdjBsd09memNBS21ZOTI5YjZ0RlFHcmIxQ2YrNlljckFibE9oQmphaGlJY0JqWmZDaDluNmJJdFVybFU1QzRJQWw4Y0RCOHNlVGd5TlJBYlhUMlBNMUpnU3o3QmswOXA2N1VwR3NOY0xlOVJhUCtQb3NtRmQ3czR6RUE2ejhVdnhhVW5rTUlZTjc4TVVoRHFTWFhnbFlBdy8wdG9vMXVFQ2xWOXJ6SkdQMWxpZDIzaGNpc0JXUDBUNFZUNHN1cWVtSHVrV0dFMERwZTBmR3BES3h5Y1MxRVZwMnY5WUdxWEp3NnVXRmVGRlQyS1gyaS96ak5oWTZ0VU1qbnpLRUNTTGVtZ2VaM3V0ZkttT0luR3dQNHJycWpYWXRHdnRkSzNJMVZSTTg3OS90OFkzUUdjWWZrQnRzQXBzT20zVTQxc0pIdDg5YnpUZ1RDd0ZyLzkvNHIrcDdVaXlyQ050eDhNUys5VnJMN2g1WXAxb09sa2YwTWkyd242ZnVxSG9GUGxWWFpFVzczK05Za1YreG5HcDVGUlpaOEtGRkVvczF6d3FWSlR2Sk1KUGRYTGRRbVZHQ3JnaDlxQmFwU0ozVC8iLCJtYWMiOiI5ODlmZWRlYmNkMWVkZjAxMWI1ZDI2ZTIxNjc5NjY0YWMyOGQ3YmI0YWE2NGFlMjY3YzZhZWVkZTgyZDgwOTgwIiwidGFnIjoiIn0%3D; _clsk=151odr7%7C1749238748618%7C9%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502508367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-609558086 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609558086\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1876181971 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:39:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InI5Z0tMQ2FVNlBBUW5UazdsMUJSc0E9PSIsInZhbHVlIjoiMlZ5ZWlFcFlDMDdaL1BCM3ZucWUrRUtTbmNFSU9LYkMzSDJ6MDdQVkJMbG1oVDAvUVpTblBhR01taWV1dGlPN1Z4QXh1MTBaUDV0NFViNzhpSDd2UVdMMkhORE8yQ0RlbXRpc0Q2c3BsKzVBNmVIZ1o3R3lrTjBoSmJKQ0F1cUpzTDVwbndwRmtGMGs2VkVHK2tuZ0ZWWDdRQkIzSmQ0eEl3TFlzWWhVeVA1VlVqL3BZM29hTndlbEtYTytLRWdCK3QrUlNLYk5ibm8xSlRFWHg2eVVQa1VLQnc2RitFYlV1RThFeTVCYUF5dGhRdWYxZXV2N1BYVVhwNGJtTjFrUlFNNHc3aUc5SG9PUDNqTjZYR3dib3RGKzdCcVhlRFRqay9MbjF4VXJEclBjc2s0Ty9aUzZpYW85SUU4MFlOSS9oTzRoemVFNGJlSVdMTlhTclY2SHNTWHdtM2lNeS9zWUltNlJrRXZ1Rzd3ak1aV3V4bjUyVlNNaWw4WGcyand5eDBIWHdGbzAxUVFpRVErVDBJdDltRU43cHJPRFBXQ09IRHZTa0lUdlZHdkw2dEJGZU8xQ2dKK2pMTW5ZdkN0TnYrbXFSanVITDR6RS9Cd1JrRVA5L1U4NmpHOW9TcmVKUFkzNVBVLyt4Nk1ObWkydUtCaWE4bXRZYjVLN254WXgiLCJtYWMiOiI1NTNjMmQ5NDAyMWQzZjQwYjBlYTllM2NhNTgyNTNkMWJmNjQ3MzQ0NjRlNTlmZWQ0ZDY1ZDkwNmFjOWU3YTc0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZHalBadGNwOG9HSWkycEIxN3dEbXc9PSIsInZhbHVlIjoicUpDMEpHSXZLN0pmSXRWbU4zTWVycnZZNzNkWDhNdmpCbjNGZ2xSVld5NDh3Y0FGdDE0OFA3ZTVMek9XOFRXS2poZ01qUkE5dTNDVzl4UVVvWC8vUFJwaHZCcVBQeU5oeTN4N2JCdkpkTlkrNm95c1NKdUtyMG9KeUU5bTRsOXJBM05NNlZwNnJ3VVhCdFczaUdNQmJ3MzhxMzRkYlZEc1VKbHVsK2RYSE5JQUxMT29ORG1vR09YRDdPdUZqdzZrcG9pY0hUdDdidkMvR1VNaEZCVnovdzFVa3Z6bXVPZXlvV0hjT1IzMkdJM1JIc0dTT2VjZjRlLzg4ekdaemU5Q05wL2RjRjIzV243TFFIMG5XMGRzSm4vdTVFU3VYN0FIL08xVzZzYzJyK2orYXNPeWZIT0x5MnVqNmh6TEt5SW82NENtV0xVQjV2K01uQkh3ZnZnWlNVU2hsbGdEc2ZRMktUWjZtZlpqYWRneGUyeFVabittYmFYMHpHZnZlWCtWUDRlL1hZWUx6bWUxUkxqOENYWFBXTXlHa2lHN2MyNnVjcWk2L2ZyNWFuQllWcUVLQ1dmUUpKU1Y5UTAyUFJ0dDBDWXpvbDd6WTNiMG1HZnRYdFk5UGhJR1JLOXJUK0F0NUpObXlqMWhiZGlQMjdnMUx4ek92SUJWdGFBRFV1dVoiLCJtYWMiOiJiMzBjYmZlODg4MTliYjcwMDJiYTVkMGJlM2U4OTVhOTQ3OGM4OWRjYzFmOWRhZGZjMzVlZTg1Y2I2M2VkM2Q4IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InI5Z0tMQ2FVNlBBUW5UazdsMUJSc0E9PSIsInZhbHVlIjoiMlZ5ZWlFcFlDMDdaL1BCM3ZucWUrRUtTbmNFSU9LYkMzSDJ6MDdQVkJMbG1oVDAvUVpTblBhR01taWV1dGlPN1Z4QXh1MTBaUDV0NFViNzhpSDd2UVdMMkhORE8yQ0RlbXRpc0Q2c3BsKzVBNmVIZ1o3R3lrTjBoSmJKQ0F1cUpzTDVwbndwRmtGMGs2VkVHK2tuZ0ZWWDdRQkIzSmQ0eEl3TFlzWWhVeVA1VlVqL3BZM29hTndlbEtYTytLRWdCK3QrUlNLYk5ibm8xSlRFWHg2eVVQa1VLQnc2RitFYlV1RThFeTVCYUF5dGhRdWYxZXV2N1BYVVhwNGJtTjFrUlFNNHc3aUc5SG9PUDNqTjZYR3dib3RGKzdCcVhlRFRqay9MbjF4VXJEclBjc2s0Ty9aUzZpYW85SUU4MFlOSS9oTzRoemVFNGJlSVdMTlhTclY2SHNTWHdtM2lNeS9zWUltNlJrRXZ1Rzd3ak1aV3V4bjUyVlNNaWw4WGcyand5eDBIWHdGbzAxUVFpRVErVDBJdDltRU43cHJPRFBXQ09IRHZTa0lUdlZHdkw2dEJGZU8xQ2dKK2pMTW5ZdkN0TnYrbXFSanVITDR6RS9Cd1JrRVA5L1U4NmpHOW9TcmVKUFkzNVBVLyt4Nk1ObWkydUtCaWE4bXRZYjVLN254WXgiLCJtYWMiOiI1NTNjMmQ5NDAyMWQzZjQwYjBlYTllM2NhNTgyNTNkMWJmNjQ3MzQ0NjRlNTlmZWQ0ZDY1ZDkwNmFjOWU3YTc0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZHalBadGNwOG9HSWkycEIxN3dEbXc9PSIsInZhbHVlIjoicUpDMEpHSXZLN0pmSXRWbU4zTWVycnZZNzNkWDhNdmpCbjNGZ2xSVld5NDh3Y0FGdDE0OFA3ZTVMek9XOFRXS2poZ01qUkE5dTNDVzl4UVVvWC8vUFJwaHZCcVBQeU5oeTN4N2JCdkpkTlkrNm95c1NKdUtyMG9KeUU5bTRsOXJBM05NNlZwNnJ3VVhCdFczaUdNQmJ3MzhxMzRkYlZEc1VKbHVsK2RYSE5JQUxMT29ORG1vR09YRDdPdUZqdzZrcG9pY0hUdDdidkMvR1VNaEZCVnovdzFVa3Z6bXVPZXlvV0hjT1IzMkdJM1JIc0dTT2VjZjRlLzg4ekdaemU5Q05wL2RjRjIzV243TFFIMG5XMGRzSm4vdTVFU3VYN0FIL08xVzZzYzJyK2orYXNPeWZIT0x5MnVqNmh6TEt5SW82NENtV0xVQjV2K01uQkh3ZnZnWlNVU2hsbGdEc2ZRMktUWjZtZlpqYWRneGUyeFVabittYmFYMHpHZnZlWCtWUDRlL1hZWUx6bWUxUkxqOENYWFBXTXlHa2lHN2MyNnVjcWk2L2ZyNWFuQllWcUVLQ1dmUUpKU1Y5UTAyUFJ0dDBDWXpvbDd6WTNiMG1HZnRYdFk5UGhJR1JLOXJUK0F0NUpObXlqMWhiZGlQMjdnMUx4ek92SUJWdGFBRFV1dVoiLCJtYWMiOiJiMzBjYmZlODg4MTliYjcwMDJiYTVkMGJlM2U4OTVhOTQ3OGM4OWRjYzFmOWRhZGZjMzVlZTg1Y2I2M2VkM2Q4IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876181971\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Your Account is disable from company.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}