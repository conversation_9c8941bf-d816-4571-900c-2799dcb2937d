{"__meta": {"id": "X360a3a41ec2d4d14f387ab91f0c752de", "datetime": "2025-06-06 19:39:07", "utime": **********.978094, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238746.440912, "end": **********.978123, "duration": 1.5372109413146973, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1749238746.440912, "relative_start": 0, "end": **********.726, "relative_end": **********.726, "duration": 1.285088062286377, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.726027, "relative_start": 1.2851150035858154, "end": **********.978127, "relative_end": 4.0531158447265625e-06, "duration": 0.25209999084472656, "duration_str": "252ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45695216, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.874251, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.892796, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.952891, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.963192, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03317, "accumulated_duration_str": "33.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.803123, "duration": 0.0148, "duration_str": "14.8ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 44.619}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8256059, "duration": 0.01021, "duration_str": "10.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 44.619, "width_percent": 30.781}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8466752, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 75.399, "width_percent": 2.954}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.876078, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 78.354, "width_percent": 3.437}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8952448, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 81.791, "width_percent": 3.286}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.924302, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 85.077, "width_percent": 4.07}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.934116, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 89.147, "width_percent": 3.166}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9416518, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 92.312, "width_percent": 3.377}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.956162, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.689, "width_percent": 4.311}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-952888690 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-952888690\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-570750354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-570750354\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1411976017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1411976017\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-14126748 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=1jor13%7C2%7Cfwj%7C0%7C1983; _clsk=151odr7%7C1749238742091%7C8%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlV0WDdUenJVeFVNSHhwdXVQeCtRN1E9PSIsInZhbHVlIjoiNXBOVWxJOFNWdUZTSm1xaUlDaUNVKzU5bExwMm5ITm94bnRrczFoT2JtOE85c1lBcW5tNWtLUXRDcllSR2YyRnBEN1doVG9ZaW82RHZmY1o3bzZqM1YxUGlFMGhnQUZOUTcrM1FXbndhbUhJRk1SL0hXbnRtY3ljalliVXl0UmNQWDJ5YWZRVE9BQUFkaGk0MWlYR2VuKzI1MG1uSkppTUlsL2YrOGhzeE1WdnNqUi94VXJaVDFPb2dncmE0MVVjL3l0WFJwZGh1eW5GZUJvTURCVHdXbkhPdTc3OSswT3poaWRKKzI0S2E2QThydDk5WUttNWxOaTJJR1FRYVAvMmRSdDU0R1RSTXhUSDcrb3A0Z3RYUmtFdUhHV3hGVEJHYStJd1VhWUhqUEhCQlpyNDZMOGc0NC9udXdYbnV1elBHenJUWFVKRkRjaExXTXlQTVlmeEJFTlZxVFVVR05aYmc3MUx0aWVjUFcwWGhHdVZ6bW9kWlFCNWpjc3JJYTBXbTFQRENBbVpxVnJhQzNIQWVnMGJwS2RvVjdrOUNEcXZKdUViT0tST1ZCYU1TRTFrK3djNHU5MXgwL1dKQXNudUVkbVZ4eGlZNTNubCs1VTFDaUgzSTc4anhFL1pDYVhFZ3hRL0ZsVUd5TTUzWGZWekdPUlpDUEVtMllnT1d0V1giLCJtYWMiOiI4MTk1OWU5MGI1ZWFmNDY2YjUwNDEzNjYxMjk3Njc5MDViYTIyNDU4ODA3MzA2MjQwYWM1MDdmNWFiMTBmMmIxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlYxbmtjbkRYc0lTbFV4amdiS0lQa0E9PSIsInZhbHVlIjoick1oRkVEZkJ6Y3BYRXhvSXhFajJLOHNjdGtvQWtsQjhZVVpYcEpFQ252cHFxRTF1Wk5FcXIxVlMwRTVVTER6Z2x2MW12czlZZlJxOHFDVzVFVjNXbDdSMmhIYVpDanFPQWo4TFJkVjFhYkNGSTEyNTd3bjhhUWt5aFZwNFU4OWM1RklJMUkvdFdqbThoUC9TTEV0VFA0RzdONkNLS2NLdmRtSEQ2WlNiclRYeHptQ2szMjV2ZlNSc1BkQ0pLQWpNSDllamNwVWR0N2NnSHg0dDNmSno0VmlRTGtOT0R0VTczeFRESGhQMUN3TmRVdkdwdjk3dmNnTUN3MThNelFDVlBTa3dvN0hWRnBIL09aWGVBNXlYV2tpY1E0UzhUaUpxNElKZU5PYkhyc0xLUmczeVdoL0M0OUxPUEhjcm1xL2M4aURFRDFVekNqZGVIS0JvVC91bkN3OWV4TENudVVIWE4wMjdPWmUrcTBRUGgwUElWRTZ2QUNVeVAybXBoZlJGTTNrNnkvb2VuYU40VkE3L1R1K010Wm1xZnMyMU1DYzZyQk1keTZ2Q2l1UFcvU1VGczczNFRjU0lNTWcvNkxqRWx3ejRRV2hEeXdGNXlTVkdpbTJIb0ppVGQ3eUxlT0phQVdqYW9pQS9kODdBbE0yTngzK3VuakZpdWh2YUQrdFEiLCJtYWMiOiJkYWMwZGNhMmU5ZjU5NzY4OGQzZTc1OTIzYjQxZGU2NDNmZWVhYTYxMmJmZDdjNTUzZWQyOGZiODVmYWU3YTBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14126748\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-20724334 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20724334\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1739270160 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:39:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhPSXFQV2VBK3VOKzkxaVA2VVJqV1E9PSIsInZhbHVlIjoiR3ZuOVpDdUlTd0hTdkxpdXBuWUg3MWxIanVVUGplTkk2OFRhUnZUYUhZcW9MSzk4MXdqNjdUejlzYnRWNU9rNU4xNFZHK3lmaHlDOTUyQ0NNU2NCOTlBSTJxbWR5V2tsWHBZVDR1YlR5ZWFhNHVQZ0VjODZUT1ZiQmVLa2VqNFBqY1lnYS9vdGVjUnlsSTBFQm5JRjV6MEtpcWt2OU5YMVcwVWh0dHVta1JZUzZrWkIvRmFRbjhBTm5HT1k0VUE5bzEyTjAvSzJyZWU4ZTA1dlN0Z0xHc0xWRVVYVFYwem9MSStEYVVHM2FjT3ZRdWpaUFdiSWRQMXRFQ29zN04ydk5RZ2RVeWdSaWczQUVOcFRsNXVJaml6WmZtZ0VuS1h3dFZoTnkyMFJrdnRGMnFRZ0ZYZ3MzK0xXdGI1c3RVZ2ltV2EyR1JEZzhUSTl1N3ozSmt0d2oyT0k3NXQ3MnI0Wi9WbEVRMEF1RWFva095VGF6eHV3YWNOaFl2WnNCZ1ljZGhuVjNYQ0dYMHlnRmZ1U1hlOC9NUUxmL2lhL2tMU29pRHhUNW1xM1F2L2Z4WE90WFFmVGRvRWFHK2NvakNWZmJrQUQ4Wlo2U2tZWjRrT1QvalVCb3NrSHlZY0VyZTcvSGRWRFFISDFxcjFJZy8yaC9DdkxLNXE3Y0k1QnJEQ08iLCJtYWMiOiIwMGQ1NTE0NzdkMGZkMWVkNDQyNGI3MzZkNmNjMWRiOTViZGJkNzhkZGM1NDJhYWE5MTcwMTgxOTMwNzFlNTE1IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhpY3hrRFkxU1oyS2daWFFpTnZSQWc9PSIsInZhbHVlIjoiSzZLMlcwRmgySEpSeGVjRjFuUWtMdjdWQk1keEw1N1IyeE9Gd1JxSjd6ek5ZOGdVMUc0THBBcC9GQlFhN1VDd2hqdjBsd09memNBS21ZOTI5YjZ0RlFHcmIxQ2YrNlljckFibE9oQmphaGlJY0JqWmZDaDluNmJJdFVybFU1QzRJQWw4Y0RCOHNlVGd5TlJBYlhUMlBNMUpnU3o3QmswOXA2N1VwR3NOY0xlOVJhUCtQb3NtRmQ3czR6RUE2ejhVdnhhVW5rTUlZTjc4TVVoRHFTWFhnbFlBdy8wdG9vMXVFQ2xWOXJ6SkdQMWxpZDIzaGNpc0JXUDBUNFZUNHN1cWVtSHVrV0dFMERwZTBmR3BES3h5Y1MxRVZwMnY5WUdxWEp3NnVXRmVGRlQyS1gyaS96ak5oWTZ0VU1qbnpLRUNTTGVtZ2VaM3V0ZkttT0luR3dQNHJycWpYWXRHdnRkSzNJMVZSTTg3OS90OFkzUUdjWWZrQnRzQXBzT20zVTQxc0pIdDg5YnpUZ1RDd0ZyLzkvNHIrcDdVaXlyQ050eDhNUys5VnJMN2g1WXAxb09sa2YwTWkyd242ZnVxSG9GUGxWWFpFVzczK05Za1YreG5HcDVGUlpaOEtGRkVvczF6d3FWSlR2Sk1KUGRYTGRRbVZHQ3JnaDlxQmFwU0ozVC8iLCJtYWMiOiI5ODlmZWRlYmNkMWVkZjAxMWI1ZDI2ZTIxNjc5NjY0YWMyOGQ3YmI0YWE2NGFlMjY3YzZhZWVkZTgyZDgwOTgwIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:39:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhPSXFQV2VBK3VOKzkxaVA2VVJqV1E9PSIsInZhbHVlIjoiR3ZuOVpDdUlTd0hTdkxpdXBuWUg3MWxIanVVUGplTkk2OFRhUnZUYUhZcW9MSzk4MXdqNjdUejlzYnRWNU9rNU4xNFZHK3lmaHlDOTUyQ0NNU2NCOTlBSTJxbWR5V2tsWHBZVDR1YlR5ZWFhNHVQZ0VjODZUT1ZiQmVLa2VqNFBqY1lnYS9vdGVjUnlsSTBFQm5JRjV6MEtpcWt2OU5YMVcwVWh0dHVta1JZUzZrWkIvRmFRbjhBTm5HT1k0VUE5bzEyTjAvSzJyZWU4ZTA1dlN0Z0xHc0xWRVVYVFYwem9MSStEYVVHM2FjT3ZRdWpaUFdiSWRQMXRFQ29zN04ydk5RZ2RVeWdSaWczQUVOcFRsNXVJaml6WmZtZ0VuS1h3dFZoTnkyMFJrdnRGMnFRZ0ZYZ3MzK0xXdGI1c3RVZ2ltV2EyR1JEZzhUSTl1N3ozSmt0d2oyT0k3NXQ3MnI0Wi9WbEVRMEF1RWFva095VGF6eHV3YWNOaFl2WnNCZ1ljZGhuVjNYQ0dYMHlnRmZ1U1hlOC9NUUxmL2lhL2tMU29pRHhUNW1xM1F2L2Z4WE90WFFmVGRvRWFHK2NvakNWZmJrQUQ4Wlo2U2tZWjRrT1QvalVCb3NrSHlZY0VyZTcvSGRWRFFISDFxcjFJZy8yaC9DdkxLNXE3Y0k1QnJEQ08iLCJtYWMiOiIwMGQ1NTE0NzdkMGZkMWVkNDQyNGI3MzZkNmNjMWRiOTViZGJkNzhkZGM1NDJhYWE5MTcwMTgxOTMwNzFlNTE1IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhpY3hrRFkxU1oyS2daWFFpTnZSQWc9PSIsInZhbHVlIjoiSzZLMlcwRmgySEpSeGVjRjFuUWtMdjdWQk1keEw1N1IyeE9Gd1JxSjd6ek5ZOGdVMUc0THBBcC9GQlFhN1VDd2hqdjBsd09memNBS21ZOTI5YjZ0RlFHcmIxQ2YrNlljckFibE9oQmphaGlJY0JqWmZDaDluNmJJdFVybFU1QzRJQWw4Y0RCOHNlVGd5TlJBYlhUMlBNMUpnU3o3QmswOXA2N1VwR3NOY0xlOVJhUCtQb3NtRmQ3czR6RUE2ejhVdnhhVW5rTUlZTjc4TVVoRHFTWFhnbFlBdy8wdG9vMXVFQ2xWOXJ6SkdQMWxpZDIzaGNpc0JXUDBUNFZUNHN1cWVtSHVrV0dFMERwZTBmR3BES3h5Y1MxRVZwMnY5WUdxWEp3NnVXRmVGRlQyS1gyaS96ak5oWTZ0VU1qbnpLRUNTTGVtZ2VaM3V0ZkttT0luR3dQNHJycWpYWXRHdnRkSzNJMVZSTTg3OS90OFkzUUdjWWZrQnRzQXBzT20zVTQxc0pIdDg5YnpUZ1RDd0ZyLzkvNHIrcDdVaXlyQ050eDhNUys5VnJMN2g1WXAxb09sa2YwTWkyd242ZnVxSG9GUGxWWFpFVzczK05Za1YreG5HcDVGUlpaOEtGRkVvczF6d3FWSlR2Sk1KUGRYTGRRbVZHQ3JnaDlxQmFwU0ozVC8iLCJtYWMiOiI5ODlmZWRlYmNkMWVkZjAxMWI1ZDI2ZTIxNjc5NjY0YWMyOGQ3YmI0YWE2NGFlMjY3YzZhZWVkZTgyZDgwOTgwIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:39:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739270160\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1170039116 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170039116\", {\"maxDepth\":0})</script>\n"}}