#!/bin/bash

# 🚀 Script لنقل تحديثات POS الكلاسيكية إلى السيرفر
# تأكد من تعديل المتغيرات أدناه قبل التشغيل

# ========== إعدادات السيرفر ==========
SERVER_USER="your_username"
SERVER_HOST="your_server_ip"
SERVER_PATH="/path/to/your/project"
LOCAL_PROJECT_PATH="."

# ========== ألوان للعرض ==========
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ========== دوال مساعدة ==========
print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# ========== فحص المتطلبات ==========
check_requirements() {
    print_step "فحص المتطلبات..."
    
    # فحص وجود rsync
    if ! command -v rsync &> /dev/null; then
        print_error "rsync غير مثبت. يرجى تثبيته أولاً"
        exit 1
    fi
    
    # فحص وجود ssh
    if ! command -v ssh &> /dev/null; then
        print_error "ssh غير مثبت. يرجى تثبيته أولاً"
        exit 1
    fi
    
    print_success "جميع المتطلبات متوفرة"
}

# ========== إنشاء نسخة احتياطية ==========
create_backup() {
    print_step "إنشاء نسخة احتياطية على السيرفر..."
    
    ssh $SERVER_USER@$SERVER_HOST "
        cd $SERVER_PATH
        
        # إنشاء مجلد النسخ الاحتياطية
        mkdir -p backups/pos_update_$(date +%Y%m%d_%H%M%S)
        BACKUP_DIR=backups/pos_update_$(date +%Y%m%d_%H%M%S)
        
        # نسخ الملفات الحالية
        cp -r app/Http/Controllers/PosController.php \$BACKUP_DIR/ 2>/dev/null || true
        cp -r app/Models/Pos*.php \$BACKUP_DIR/ 2>/dev/null || true
        cp -r resources/views/pos \$BACKUP_DIR/ 2>/dev/null || true
        cp routes/web.php \$BACKUP_DIR/ 2>/dev/null || true
        
        echo 'تم إنشاء النسخة الاحتياطية في:' \$BACKUP_DIR
    "
    
    print_success "تم إنشاء النسخة الاحتياطية"
}

# ========== نقل الملفات الأساسية ==========
deploy_core_files() {
    print_step "نقل الملفات الأساسية..."
    
    # Controllers
    print_step "نقل Controllers..."
    rsync -avz --progress app/Http/Controllers/PosController.php $SERVER_USER@$SERVER_HOST:$SERVER_PATH/app/Http/Controllers/
    rsync -avz --progress app/Http/Controllers/PosV2Controller.php $SERVER_USER@$SERVER_HOST:$SERVER_PATH/app/Http/Controllers/
    
    # Models
    print_step "نقل Models..."
    rsync -avz --progress app/Models/Pos*.php $SERVER_USER@$SERVER_HOST:$SERVER_PATH/app/Models/
    
    # Views
    print_step "نقل Views..."
    rsync -avz --progress --delete resources/views/pos/ $SERVER_USER@$SERVER_HOST:$SERVER_PATH/resources/views/pos/
    rsync -avz --progress --delete resources/views/pos_v2/ $SERVER_USER@$SERVER_HOST:$SERVER_PATH/resources/views/pos_v2/
    
    # Routes
    print_step "نقل Routes..."
    rsync -avz --progress routes/web.php $SERVER_USER@$SERVER_HOST:$SERVER_PATH/routes/
    
    print_success "تم نقل الملفات الأساسية"
}

# ========== نقل ملفات قاعدة البيانات ==========
deploy_database_files() {
    print_step "نقل ملفات قاعدة البيانات..."
    
    # Migrations
    rsync -avz --progress database/migrations/*pos*.php $SERVER_USER@$SERVER_HOST:$SERVER_PATH/database/migrations/ 2>/dev/null || true
    
    # SQL Scripts
    rsync -avz --progress *.sql $SERVER_USER@$SERVER_HOST:$SERVER_PATH/ 2>/dev/null || true
    
    print_success "تم نقل ملفات قاعدة البيانات"
}

# ========== تشغيل أوامر ما بعد النقل ==========
post_deployment() {
    print_step "تشغيل أوامر ما بعد النقل..."
    
    ssh $SERVER_USER@$SERVER_HOST "
        cd $SERVER_PATH
        
        echo '🔄 تحديث Composer...'
        composer dump-autoload
        
        echo '🔄 تشغيل Migrations...'
        php artisan migrate --force
        
        echo '🔄 تنظيف Cache...'
        php artisan cache:clear
        php artisan route:clear
        php artisan config:clear
        php artisan view:clear
        
        echo '🔄 تحسين الأداء...'
        php artisan config:cache
        php artisan route:cache
        
        echo '✅ تم الانتهاء من أوامر ما بعد النقل'
    "
    
    print_success "تم تشغيل أوامر ما بعد النقل"
}

# ========== اختبار النظام ==========
test_system() {
    print_step "اختبار النظام..."
    
    ssh $SERVER_USER@$SERVER_HOST "
        cd $SERVER_PATH
        
        echo '🧪 فحص Routes...'
        php artisan route:list | grep pos
        
        echo '🧪 فحص قاعدة البيانات...'
        php artisan migrate:status | grep pos
        
        echo '✅ انتهى الاختبار'
    "
    
    print_success "تم اختبار النظام"
}

# ========== الدالة الرئيسية ==========
main() {
    echo "=========================================="
    echo "🚀 نقل تحديثات POS الكلاسيكية"
    echo "=========================================="
    
    # فحص المتطلبات
    check_requirements
    
    # تأكيد من المستخدم
    echo ""
    print_warning "هل أنت متأكد من أنك تريد نقل التحديثات إلى السيرفر؟"
    print_warning "السيرفر: $SERVER_USER@$SERVER_HOST"
    print_warning "المسار: $SERVER_PATH"
    echo ""
    read -p "اكتب 'yes' للمتابعة: " confirm
    
    if [ "$confirm" != "yes" ]; then
        print_error "تم إلغاء العملية"
        exit 1
    fi
    
    # تنفيذ خطوات النقل
    create_backup
    deploy_core_files
    deploy_database_files
    post_deployment
    test_system
    
    echo ""
    echo "=========================================="
    print_success "🎉 تم نقل جميع التحديثات بنجاح!"
    echo "=========================================="
    echo ""
    print_warning "يرجى اختبار النظام للتأكد من عمل جميع الوظائف:"
    echo "1. تسجيل الدخول إلى النظام"
    echo "2. فتح صفحة POS"
    echo "3. اختبار إضافة منتجات للسلة"
    echo "4. اختبار عملية الدفع"
    echo "5. اختبار الطباعة الحرارية"
    echo "6. اختبار زر 'طباعة فاتورة'"
    echo ""
}

# ========== تشغيل البرنامج ==========
main "$@"
