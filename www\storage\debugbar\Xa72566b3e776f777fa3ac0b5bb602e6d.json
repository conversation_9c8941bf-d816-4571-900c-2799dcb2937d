{"__meta": {"id": "Xa72566b3e776f777fa3ac0b5bb602e6d", "datetime": "2025-06-07 04:33:30", "utime": **********.531337, "method": "GET", "uri": "/assets/js/plugins/feather.min.js.map", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749270808.791996, "end": **********.531378, "duration": 1.739382028579712, "duration_str": "1.74s", "measures": [{"label": "Booting", "start": 1749270808.791996, "relative_start": 0, "end": **********.359797, "relative_end": **********.359797, "duration": 1.5678009986877441, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.359822, "relative_start": 1.5678260326385498, "end": **********.531383, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException", "message": "The route assets/js/plugins/feather.min.js.map could not be found.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php", "line": 44, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>163</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">handleMatchedRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Routing\\AbstractRouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a><samp data-depth=4 id=sf-dump-*********-ref253 class=sf-dump-compact>\n        +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#58</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#54</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#61</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#56</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>REDIRECT_REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n            \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n            \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n            \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n            \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n            \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270800184%7C15%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBjb1Fnc00yUmhpYWdmTjIxUGVHOGc9PSIsInZhbHVlIjoid085WG83TU5hZEI2ZkVkKzlWbkkzZUFzL1FrMGFnT2s4MnNLdWFHZHAzUXZBK3puN2oveENMd3JuTHNEVXlsNVlETFh2NzhjQ1lOWjlkN1l3WVdzNUF6K1FHdy9UZHdMYkd5YzZBZzkyUGpaTFEvMWJmMTlockFmR0wvOVlnOWhlNmJwMDduV3RaTnZNVFJVN1M5c2ZxL3JyV2UrZVRtWnJOUGV1bDVBdURUK1FBV0xQSHVWYkZRUWFYb25ock5rZDhXS3dCRHJBaldreC9wOHlBK1dkL044ZjRvdHQ3WjJreDZxL1RCSncwZWRaYU1ZV2xEYkdwUDVob2FwKzJOeEE4NVNISksxZEpoL29lZ2EvM2RsbG9KUTZoeHgveTYxYk5YQUt0ZjVYbDUvSDJLRTVNckxPQThmV3dCWDZiUXE5eDJxdnBBU0xlOWdwNTdPYkZVR0lGTlQ4Z3hhZm45WkZ4cTZ1VFljdDB6OFkyZjVJTU8vbFdVVWZCNDE1Qjd4ZkwxTnhiNWNYTnNSSEViSUpOS09VbVVPNDZNNy9vaThjRWhpMlZMaGt2VGV2ZGdydXJISnZSOTh0UDM5Ukoza2VDaFl1dStpNWt2NUJGZTRzMWVJbFJubEpoQlJFb0o0c2JNVmxVQ3owMjZtZHY4ekwzZW1SUFZiNW95OXVlanUiLCJtYWMiOiIzZjk1YTdkZjEwNDM0NzlkOTk2ODU1ZWI2ODdhYzVkOGMwYjRjYWNiZmQ2NDRjNGNkZjdiMjQyZDNmNmFkOTZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlgwVkdmbjVjeU5aWXlDM3AvaHR5b2c9PSIsInZhbHVlIjoiWVZRRXhma2h6aUhoUHNZSnhPamplakkzNkRQNExXM3FCUmhEZlhQeGp3cE43aEV2TXo4MGtmM3gwYlRtcDBCcUxUaWhDdlNaZ1M4MzVUOG80ZUF5cVREQVZOdXQvMlFvd2VoOW44VjNwM3liOFV5Y0M0Vkc3dWhpM2sxVlY1eVJMbXZFMHdva1Y1UTV2T2NxdzVvWk04SzI0dE1vTHJobGRianlkcEh3Y2ZmbEozakN6bWhubjlYQ25OSlRtZG85QXZ4enBuaU1HWlBOQ0Z4OExuZ2kwVnFYVzh3TWsrUnhxVGRaanlIOFVVenUxb0hycndxZHg0cklZbHBXZXZmekZtVTBLeHk2bG5EcCtBUSsvZjZWUmxnYW9MLzl5R1dGQlc2aXdGQStWeUF5S0g4eEdURmVYZnRvNGtaT3BmMkhkeGhhbWhML0UxdzJHOGxwazhRMWcwK050ZVNiVVdnSCtBQTlZVkVCUGlhL0hiZU1kUnI4THNyWlR5ZUNYUDIzY2w0ZFd2YzFqWm41emNTQzlPZmx1cXlseVk1T2oxUUdPOWR2TTQ3b2dtVWtuSDE5VXpRbElRL1VtQ1JBeHlmZnhMRTR6dE0rdlRKTDkweVhSWjgzaFBVNVQrai9TODkyWm1CeW0xWVFRREdoWE1wTGtpUEZOZWdhMDdJMkU5VUIiLCJtYWMiOiIyZmJkNGYxN2Q3Mjk2YzM2YzRlZTM1YjkwNDRlNWUzMTNkMTMxNmU2ZmU5MzQwNWVkZjVkMmM0YjNlOWNjNGI5IiwidGFnIjoiIn0%3D</span>\"\n            \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"792 characters\">C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\WebStorm 2025.1.1\\bin;</span>\"\n            \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n            \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n            \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n            \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n            \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n            \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n            \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n            \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n            \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n            \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n            \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"17 characters\">C:/laragon/www/to</span>\"\n            \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n            \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n            \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"17 characters\">C:/laragon/www/to</span>\"\n            \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"34 characters\">C:/laragon/www/to/public/index.php</span>\"\n            \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51487</span>\"\n            \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/public/assets/js/plugins/feather.min.js.map</span>\"\n            \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n            \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n            \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n            \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n            \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/assets/js/plugins/feather.min.js.map</span>\"\n            \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/public/index.php</span>\"\n            \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/public/index.php</span>\"\n            \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1749270808.792</span>\n            \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1749270808</span>\n          </samp>]\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#60</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#59</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>cc_cookie</span>\" => \"<span class=sf-dump-str title=\"67 characters\">{&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}</span>\"\n            \"<span class=sf-dump-key>cookie_consent_logged</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>__stripe_mid</span>\" => \"<span class=sf-dump-str title=\"42 characters\">a091af98-270d-406e-ab7e-33a6e610d6e2414686</span>\"\n            \"<span class=sf-dump-key>_clck</span>\" => \"<span class=sf-dump-str title=\"19 characters\">nups8l|2|fwk|0|1960</span>\"\n            \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"852 characters\">eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0=</span>\"\n            \"<span class=sf-dump-key>_clsk</span>\" => \"<span class=sf-dump-str title=\"46 characters\">hqqi3x|1749270800184|15|1|s.clarity.ms/collect</span>\"\n            \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"852 characters\">eyJpdiI6InBjb1Fnc00yUmhpYWdmTjIxUGVHOGc9PSIsInZhbHVlIjoid085WG83TU5hZEI2ZkVkKzlWbkkzZUFzL1FrMGFnT2s4MnNLdWFHZHAzUXZBK3puN2oveENMd3JuTHNEVXlsNVlETFh2NzhjQ1lOWjlkN1l3WVdzNUF6K1FHdy9UZHdMYkd5YzZBZzkyUGpaTFEvMWJmMTlockFmR0wvOVlnOWhlNmJwMDduV3RaTnZNVFJVN1M5c2ZxL3JyV2UrZVRtWnJOUGV1bDVBdURUK1FBV0xQSHVWYkZRUWFYb25ock5rZDhXS3dCRHJBaldreC9wOHlBK1dkL044ZjRvdHQ3WjJreDZxL1RCSncwZWRaYU1ZV2xEYkdwUDVob2FwKzJOeEE4NVNISksxZEpoL29lZ2EvM2RsbG9KUTZoeHgveTYxYk5YQUt0ZjVYbDUvSDJLRTVNckxPQThmV3dCWDZiUXE5eDJxdnBBU0xlOWdwNTdPYkZVR0lGTlQ4Z3hhZm45WkZ4cTZ1VFljdDB6OFkyZjVJTU8vbFdVVWZCNDE1Qjd4ZkwxTnhiNWNYTnNSSEViSUpOS09VbVVPNDZNNy9vaThjRWhpMlZMaGt2VGV2ZGdydXJISnZSOTh0UDM5Ukoza2VDaFl1dStpNWt2NUJGZTRzMWVJbFJubEpoQlJFb0o0c2JNVmxVQ3owMjZtZHY4ekwzZW1SUFZiNW95OXVlanUiLCJtYWMiOiIzZjk1YTdkZjEwNDM0NzlkOTk2ODU1ZWI2ODdhYzVkOGMwYjRjYWNiZmQ2NDRjNGNkZjdiMjQyZDNmNmFkOTZmIiwidGFnIjoiIn0=</span>\"\n            \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"852 characters\">eyJpdiI6IlgwVkdmbjVjeU5aWXlDM3AvaHR5b2c9PSIsInZhbHVlIjoiWVZRRXhma2h6aUhoUHNZSnhPamplakkzNkRQNExXM3FCUmhEZlhQeGp3cE43aEV2TXo4MGtmM3gwYlRtcDBCcUxUaWhDdlNaZ1M4MzVUOG80ZUF5cVREQVZOdXQvMlFvd2VoOW44VjNwM3liOFV5Y0M0Vkc3dWhpM2sxVlY1eVJMbXZFMHdva1Y1UTV2T2NxdzVvWk04SzI0dE1vTHJobGRianlkcEh3Y2ZmbEozakN6bWhubjlYQ25OSlRtZG85QXZ4enBuaU1HWlBOQ0Z4OExuZ2kwVnFYVzh3TWsrUnhxVGRaanlIOFVVenUxb0hycndxZHg0cklZbHBXZXZmekZtVTBLeHk2bG5EcCtBUSsvZjZWUmxnYW9MLzl5R1dGQlc2aXdGQStWeUF5S0g4eEdURmVYZnRvNGtaT3BmMkhkeGhhbWhML0UxdzJHOGxwazhRMWcwK050ZVNiVVdnSCtBQTlZVkVCUGlhL0hiZU1kUnI4THNyWlR5ZUNYUDIzY2w0ZFd2YzFqWm41emNTQzlPZmx1cXlseVk1T2oxUUdPOWR2TTQ3b2dtVWtuSDE5VXpRbElRL1VtQ1JBeHlmZnhMRTR6dE0rdlRKTDkweVhSWjgzaFBVNVQrai9TODkyWm1CeW0xWVFRREdoWE1wTGtpUEZOZWdhMDdJMkU5VUIiLCJtYWMiOiIyZmJkNGYxN2Q3Mjk2YzM2YzRlZTM1YjkwNDRlNWUzMTNkMTMxNmU2ZmU5MzQwNWVkZjVkMmM0YjNlOWNjNGI5IiwidGFnIjoiIn0=</span>\"\n          </samp>]\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#55</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270800184%7C15%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBjb1Fnc00yUmhpYWdmTjIxUGVHOGc9PSIsInZhbHVlIjoid085WG83TU5hZEI2ZkVkKzlWbkkzZUFzL1FrMGFnT2s4MnNLdWFHZHAzUXZBK3puN2oveENMd3JuTHNEVXlsNVlETFh2NzhjQ1lOWjlkN1l3WVdzNUF6K1FHdy9UZHdMYkd5YzZBZzkyUGpaTFEvMWJmMTlockFmR0wvOVlnOWhlNmJwMDduV3RaTnZNVFJVN1M5c2ZxL3JyV2UrZVRtWnJOUGV1bDVBdURUK1FBV0xQSHVWYkZRUWFYb25ock5rZDhXS3dCRHJBaldreC9wOHlBK1dkL044ZjRvdHQ3WjJreDZxL1RCSncwZWRaYU1ZV2xEYkdwUDVob2FwKzJOeEE4NVNISksxZEpoL29lZ2EvM2RsbG9KUTZoeHgveTYxYk5YQUt0ZjVYbDUvSDJLRTVNckxPQThmV3dCWDZiUXE5eDJxdnBBU0xlOWdwNTdPYkZVR0lGTlQ4Z3hhZm45WkZ4cTZ1VFljdDB6OFkyZjVJTU8vbFdVVWZCNDE1Qjd4ZkwxTnhiNWNYTnNSSEViSUpOS09VbVVPNDZNNy9vaThjRWhpMlZMaGt2VGV2ZGdydXJISnZSOTh0UDM5Ukoza2VDaFl1dStpNWt2NUJGZTRzMWVJbFJubEpoQlJFb0o0c2JNVmxVQ3owMjZtZHY4ekwzZW1SUFZiNW95OXVlanUiLCJtYWMiOiIzZjk1YTdkZjEwNDM0NzlkOTk2ODU1ZWI2ODdhYzVkOGMwYjRjYWNiZmQ2NDRjNGNkZjdiMjQyZDNmNmFkOTZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlgwVkdmbjVjeU5aWXlDM3AvaHR5b2c9PSIsInZhbHVlIjoiWVZRRXhma2h6aUhoUHNZSnhPamplakkzNkRQNExXM3FCUmhEZlhQeGp3cE43aEV2TXo4MGtmM3gwYlRtcDBCcUxUaWhDdlNaZ1M4MzVUOG80ZUF5cVREQVZOdXQvMlFvd2VoOW44VjNwM3liOFV5Y0M0Vkc3dWhpM2sxVlY1eVJMbXZFMHdva1Y1UTV2T2NxdzVvWk04SzI0dE1vTHJobGRianlkcEh3Y2ZmbEozakN6bWhubjlYQ25OSlRtZG85QXZ4enBuaU1HWlBOQ0Z4OExuZ2kwVnFYVzh3TWsrUnhxVGRaanlIOFVVenUxb0hycndxZHg0cklZbHBXZXZmekZtVTBLeHk2bG5EcCtBUSsvZjZWUmxnYW9MLzl5R1dGQlc2aXdGQStWeUF5S0g4eEdURmVYZnRvNGtaT3BmMkhkeGhhbWhML0UxdzJHOGxwazhRMWcwK050ZVNiVVdnSCtBQTlZVkVCUGlhL0hiZU1kUnI4THNyWlR5ZUNYUDIzY2w0ZFd2YzFqWm41emNTQzlPZmx1cXlseVk1T2oxUUdPOWR2TTQ3b2dtVWtuSDE5VXpRbElRL1VtQ1JBeHlmZnhMRTR6dE0rdlRKTDkweVhSWjgzaFBVNVQrai9TODkyWm1CeW0xWVFRREdoWE1wTGtpUEZOZWdhMDdJMkU5VUIiLCJtYWMiOiIyZmJkNGYxN2Q3Mjk2YzM2YzRlZTM1YjkwNDRlNWUzMTNkMTMxNmU2ZmU5MzQwNWVkZjVkMmM0YjNlOWNjNGI5IiwidGFnIjoiIn0%3D</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"37 characters\">/assets/js/plugins/feather.min.js.map</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"37 characters\">/assets/js/plugins/feather.min.js.map</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n        #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isSafeContentPreferred</span>: <span class=sf-dump-const title=\"Uninitialized property\">? bool</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">trustedValuesCache</span>: []\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isIisRewrite</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#3362</a><samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n          <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#84</a> &#8230;}\n          <span class=sf-dump-meta>use</span>: {<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref>#7</a> &#8230;}\n          </samp>}\n          <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\AuthServiceProvider.php</span>\"\n          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-const>null</span>\n        <span class=sf-dump-meta>basePath</span>: \"\"\n        <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n      </samp>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>763</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">match</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Routing\\RouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">findRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23228 title=\"2 occurrences\">#3228</a><samp data-depth=4 id=sf-dump-*********-ref23228 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$destination</span>: <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23259 title=\"2 occurrences\">#3259</a><samp data-depth=6 id=sf-dump-*********-ref23259 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Foundation\\Http\\Kernel\n33 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Kernel</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel</span> {<a class=sf-dump-ref>#47</a> &#8230;}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php\n84 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">198 to 202</span>\"\n          </samp>}\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">142 to 148</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">app/Http/Middleware/RemoveInjectedVerifyScript.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>18</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\RemoveInjectedVerifyScript</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23216 title=\"2 occurrences\">#3216</a><samp data-depth=4 id=sf-dump-*********-ref23216 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23228 title=\"2 occurrences\">#3228</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23198 title=\"3 occurrences\">#3198</a><samp data-depth=4 id=sf-dump-*********-ref23198 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23216 title=\"2 occurrences\">#3216</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\RemoveInjectedVerifyScript</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23198 title=\"3 occurrences\">#3198</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2270 title=\"2 occurrences\">#270</a><samp data-depth=4 id=sf-dump-*********-ref2270 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23198 title=\"3 occurrences\">#3198</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"31 characters\">App\\Http\\Middleware\\TrimStrings</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2271 title=\"2 occurrences\">#271</a><samp data-depth=4 id=sf-dump-*********-ref2271 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2270 title=\"2 occurrences\">#270</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"52 characters\">App\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23285 title=\"3 occurrences\">#3285</a><samp data-depth=4 id=sf-dump-*********-ref23285 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2271 title=\"2 occurrences\">#271</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Middleware\\TrustProxies</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23285 title=\"3 occurrences\">#3285</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23287 title=\"3 occurrences\">#3287</a><samp data-depth=4 id=sf-dump-*********-ref23287 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23285 title=\"3 occurrences\">#3285</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23287 title=\"3 occurrences\">#3287</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23281 title=\"2 occurrences\">#3281</a><samp data-depth=4 id=sf-dump-*********-ref23281 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23287 title=\"3 occurrences\">#3287</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23055 title=\"2 occurrences\">#3055</a><samp data-depth=4 id=sf-dump-*********-ref23055 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23281 title=\"2 occurrences\">#3281</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23290 title=\"2 occurrences\">#3290</a><samp data-depth=4 id=sf-dump-*********-ref23290 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23055 title=\"2 occurrences\">#3055</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref>#3291</a><samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23290 title=\"2 occurrences\">#3290</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n79 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23259 title=\"2 occurrences\">#3259</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-*********-ref253 title=\"34 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $this->getRouteForMethods($request, $others);\n", "        }\n", "\n", "        throw new NotFoundHttpException(sprintf(\n", "            'The route %s could not be found.',\n", "            $request->path()\n", "        ));\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FAbstractRouteCollection.php&line=44", "ajax": false, "filename": "AbstractRouteCollection.php", "line": "44"}}]}, "views": {"nb_templates": 2, "templates": [{"name": "1x errors::404", "param_count": null, "params": [], "start": **********.505216, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions/views/404.blade.phperrors::404", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FExceptions%2Fviews%2F404.blade.php&line=1", "ajax": false, "filename": "404.blade.php", "line": "?"}, "render_count": 1, "name_original": "errors::404"}, {"name": "1x errors::minimal", "param_count": null, "params": [], "start": **********.512199, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions/views/minimal.blade.phperrors::minimal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FExceptions%2Fviews%2Fminimal.blade.php&line=1", "ajax": false, "filename": "minimal.blade.php", "line": "?"}, "render_count": 1, "name_original": "errors::minimal"}]}, "route": [], "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NBRzCe457M4GLnkTtZ1CO7jOTRkMu2tDapOswCaX", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/assets/js/plugins/feather.min.js.map", "status_code": "<pre class=sf-dump id=sf-dump-1946954465 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-1946954465\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-225669210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-225669210\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1431277925 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1431277925\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1614290656 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2866 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; laravel_session=eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0%3D; _clsk=hqqi3x%7C1749270800184%7C15%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBjb1Fnc00yUmhpYWdmTjIxUGVHOGc9PSIsInZhbHVlIjoid085WG83TU5hZEI2ZkVkKzlWbkkzZUFzL1FrMGFnT2s4MnNLdWFHZHAzUXZBK3puN2oveENMd3JuTHNEVXlsNVlETFh2NzhjQ1lOWjlkN1l3WVdzNUF6K1FHdy9UZHdMYkd5YzZBZzkyUGpaTFEvMWJmMTlockFmR0wvOVlnOWhlNmJwMDduV3RaTnZNVFJVN1M5c2ZxL3JyV2UrZVRtWnJOUGV1bDVBdURUK1FBV0xQSHVWYkZRUWFYb25ock5rZDhXS3dCRHJBaldreC9wOHlBK1dkL044ZjRvdHQ3WjJreDZxL1RCSncwZWRaYU1ZV2xEYkdwUDVob2FwKzJOeEE4NVNISksxZEpoL29lZ2EvM2RsbG9KUTZoeHgveTYxYk5YQUt0ZjVYbDUvSDJLRTVNckxPQThmV3dCWDZiUXE5eDJxdnBBU0xlOWdwNTdPYkZVR0lGTlQ4Z3hhZm45WkZ4cTZ1VFljdDB6OFkyZjVJTU8vbFdVVWZCNDE1Qjd4ZkwxTnhiNWNYTnNSSEViSUpOS09VbVVPNDZNNy9vaThjRWhpMlZMaGt2VGV2ZGdydXJISnZSOTh0UDM5Ukoza2VDaFl1dStpNWt2NUJGZTRzMWVJbFJubEpoQlJFb0o0c2JNVmxVQ3owMjZtZHY4ekwzZW1SUFZiNW95OXVlanUiLCJtYWMiOiIzZjk1YTdkZjEwNDM0NzlkOTk2ODU1ZWI2ODdhYzVkOGMwYjRjYWNiZmQ2NDRjNGNkZjdiMjQyZDNmNmFkOTZmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlgwVkdmbjVjeU5aWXlDM3AvaHR5b2c9PSIsInZhbHVlIjoiWVZRRXhma2h6aUhoUHNZSnhPamplakkzNkRQNExXM3FCUmhEZlhQeGp3cE43aEV2TXo4MGtmM3gwYlRtcDBCcUxUaWhDdlNaZ1M4MzVUOG80ZUF5cVREQVZOdXQvMlFvd2VoOW44VjNwM3liOFV5Y0M0Vkc3dWhpM2sxVlY1eVJMbXZFMHdva1Y1UTV2T2NxdzVvWk04SzI0dE1vTHJobGRianlkcEh3Y2ZmbEozakN6bWhubjlYQ25OSlRtZG85QXZ4enBuaU1HWlBOQ0Z4OExuZ2kwVnFYVzh3TWsrUnhxVGRaanlIOFVVenUxb0hycndxZHg0cklZbHBXZXZmekZtVTBLeHk2bG5EcCtBUSsvZjZWUmxnYW9MLzl5R1dGQlc2aXdGQStWeUF5S0g4eEdURmVYZnRvNGtaT3BmMkhkeGhhbWhML0UxdzJHOGxwazhRMWcwK050ZVNiVVdnSCtBQTlZVkVCUGlhL0hiZU1kUnI4THNyWlR5ZUNYUDIzY2w0ZFd2YzFqWm41emNTQzlPZmx1cXlseVk1T2oxUUdPOWR2TTQ3b2dtVWtuSDE5VXpRbElRL1VtQ1JBeHlmZnhMRTR6dE0rdlRKTDkweVhSWjgzaFBVNVQrai9TODkyWm1CeW0xWVFRREdoWE1wTGtpUEZOZWdhMDdJMkU5VUIiLCJtYWMiOiIyZmJkNGYxN2Q3Mjk2YzM2YzRlZTM1YjkwNDRlNWUzMTNkMTMxNmU2ZmU5MzQwNWVkZjVkMmM0YjNlOWNjNGI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1614290656\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1668499197 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => \"<span class=sf-dump-str title=\"67 characters\">{&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}</span>\"\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => \"<span class=sf-dump-str title=\"42 characters\">a091af98-270d-406e-ab7e-33a6e610d6e2414686</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => \"<span class=sf-dump-str title=\"19 characters\">nups8l|2|fwk|0|1960</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"852 characters\">eyJpdiI6InlqczZCbjgzVXlJY2hQZjd3TVlPM0E9PSIsInZhbHVlIjoiV0tOMUsrWnRaNXdJNk9mR1BZSDBKcndmQnBtdnRXczVTbmNGWHNDT1RwUi8xY0wvMEp2T1hhTkkyOXJpMmx1ditSSm5qK2FjUk9mWUNPRXI5UnhRY2oyWmQ3aXVONkhWMnNYUDUvRG5XNm9za3RCS3FPVS92MDRtUjlmQU1JaGc2WVE4bUZ1ZEZhMytISytpaDJPTUZmQVJXOE5BRGFPSGlsRzNlamhzTXh2bkVhVlRicWdHcS9MdXhCV0RUMEd0RldkZ29iNXhMbGtrWWM0R1IzeitkV0F4S3BlLy9MMmVCZzBub0VXWSs5b0NLdEVVaVZDZHBXekdINVdXZWR2WFV1NlQ0a09CNDEvWHU2UzlNQm5KVGJlWXIreGhsZStqYWUxRXhKM21ZTTFaTEMrM1luaCs0dzhkZmFiKzhWZElPUCt6ZWZrYm9xWDZwdkxLVGxVYWFsV2Njc0JkUFVqNFV0dldGSE5yVGx6bHdldEJ6cmlyTU44NGdCNFRDZXM2T3J3SUlSMzkwWVRUSlhhcmNRVXZINU1RY0ptdldKdm5zdXl1WC8wSDlCOEZMSk5OZk5va0ZvZVA4dVdySkVrbThScmRjcHI1b0lnWW8vS2NRQms1RTJYRE4wQkNzUDkzUFRnNnZBdzl0T1phaWJaaXl1d1RLbUdKWDhoWkhlbG8iLCJtYWMiOiIxMjlhNDFjOWEzMjJjYWEyMjljYzg1MDc3ZTJiODhmYjAxNGU0NzgzODI4YzBhZjRjYWIwMjkwMjBmOGE2Yzc3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => \"<span class=sf-dump-str title=\"46 characters\">hqqi3x|1749270800184|15|1|s.clarity.ms/collect</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"852 characters\">eyJpdiI6InBjb1Fnc00yUmhpYWdmTjIxUGVHOGc9PSIsInZhbHVlIjoid085WG83TU5hZEI2ZkVkKzlWbkkzZUFzL1FrMGFnT2s4MnNLdWFHZHAzUXZBK3puN2oveENMd3JuTHNEVXlsNVlETFh2NzhjQ1lOWjlkN1l3WVdzNUF6K1FHdy9UZHdMYkd5YzZBZzkyUGpaTFEvMWJmMTlockFmR0wvOVlnOWhlNmJwMDduV3RaTnZNVFJVN1M5c2ZxL3JyV2UrZVRtWnJOUGV1bDVBdURUK1FBV0xQSHVWYkZRUWFYb25ock5rZDhXS3dCRHJBaldreC9wOHlBK1dkL044ZjRvdHQ3WjJreDZxL1RCSncwZWRaYU1ZV2xEYkdwUDVob2FwKzJOeEE4NVNISksxZEpoL29lZ2EvM2RsbG9KUTZoeHgveTYxYk5YQUt0ZjVYbDUvSDJLRTVNckxPQThmV3dCWDZiUXE5eDJxdnBBU0xlOWdwNTdPYkZVR0lGTlQ4Z3hhZm45WkZ4cTZ1VFljdDB6OFkyZjVJTU8vbFdVVWZCNDE1Qjd4ZkwxTnhiNWNYTnNSSEViSUpOS09VbVVPNDZNNy9vaThjRWhpMlZMaGt2VGV2ZGdydXJISnZSOTh0UDM5Ukoza2VDaFl1dStpNWt2NUJGZTRzMWVJbFJubEpoQlJFb0o0c2JNVmxVQ3owMjZtZHY4ekwzZW1SUFZiNW95OXVlanUiLCJtYWMiOiIzZjk1YTdkZjEwNDM0NzlkOTk2ODU1ZWI2ODdhYzVkOGMwYjRjYWNiZmQ2NDRjNGNkZjdiMjQyZDNmNmFkOTZmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"852 characters\">eyJpdiI6IlgwVkdmbjVjeU5aWXlDM3AvaHR5b2c9PSIsInZhbHVlIjoiWVZRRXhma2h6aUhoUHNZSnhPamplakkzNkRQNExXM3FCUmhEZlhQeGp3cE43aEV2TXo4MGtmM3gwYlRtcDBCcUxUaWhDdlNaZ1M4MzVUOG80ZUF5cVREQVZOdXQvMlFvd2VoOW44VjNwM3liOFV5Y0M0Vkc3dWhpM2sxVlY1eVJMbXZFMHdva1Y1UTV2T2NxdzVvWk04SzI0dE1vTHJobGRianlkcEh3Y2ZmbEozakN6bWhubjlYQ25OSlRtZG85QXZ4enBuaU1HWlBOQ0Z4OExuZ2kwVnFYVzh3TWsrUnhxVGRaanlIOFVVenUxb0hycndxZHg0cklZbHBXZXZmekZtVTBLeHk2bG5EcCtBUSsvZjZWUmxnYW9MLzl5R1dGQlc2aXdGQStWeUF5S0g4eEdURmVYZnRvNGtaT3BmMkhkeGhhbWhML0UxdzJHOGxwazhRMWcwK050ZVNiVVdnSCtBQTlZVkVCUGlhL0hiZU1kUnI4THNyWlR5ZUNYUDIzY2w0ZFd2YzFqWm41emNTQzlPZmx1cXlseVk1T2oxUUdPOWR2TTQ3b2dtVWtuSDE5VXpRbElRL1VtQ1JBeHlmZnhMRTR6dE0rdlRKTDkweVhSWjgzaFBVNVQrai9TODkyWm1CeW0xWVFRREdoWE1wTGtpUEZOZWdhMDdJMkU5VUIiLCJtYWMiOiIyZmJkNGYxN2Q3Mjk2YzM2YzRlZTM1YjkwNDRlNWUzMTNkMTMxNmU2ZmU5MzQwNWVkZjVkMmM0YjNlOWNjNGI5IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668499197\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1978903311 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:33:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978903311\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1733803299 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NBRzCe457M4GLnkTtZ1CO7jOTRkMu2tDapOswCaX</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733803299\", {\"maxDepth\":0})</script>\n"}}