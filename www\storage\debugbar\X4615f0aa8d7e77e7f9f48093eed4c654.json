{"__meta": {"id": "X4615f0aa8d7e77e7f9f48093eed4c654", "datetime": "2025-06-06 19:28:58", "utime": **********.914162, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238137.462849, "end": **********.914198, "duration": 1.4513490200042725, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1749238137.462849, "relative_start": 0, "end": **********.734603, "relative_end": **********.734603, "duration": 1.2717540264129639, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.734631, "relative_start": 1.2717821598052979, "end": **********.914202, "relative_end": 4.0531158447265625e-06, "duration": 0.17957091331481934, "duration_str": "180ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44761240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01676, "accumulated_duration_str": "16.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8179002, "duration": 0.01326, "duration_str": "13.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.117}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.863788, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.117, "width_percent": 7.399}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.872584, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 86.516, "width_percent": 5.847}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.889026, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.363, "width_percent": 7.637}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1308472234 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1308472234\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-134766495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-134766495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-375233499 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375233499\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238091179%7C32%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImwraHBYOHFWYzk0cmVhSWdvSm8rZWc9PSIsInZhbHVlIjoiNHhLcXdpTTg4cGkrNDNQKzlnWVRsL2J6TloxTGYwYzdFdXBFWGpzKzdmNFFmRzd0L3FRTitPbkU5dVlldW1SRkt6WUIva1I2VnpJM3hSVDR5WVIrcmpnZ2k5Wk5odXBEY2tRVTRLZHdnOVg4Y1ovZ01HbjRyKzVJM3VJRmowZlRQcDA1QlM5NmlIeFFQVDhOMk1GVDMzTFh1VVN4VGZVcUtwVHBZK0ZjSGtyaG5odmFWaUJLUndLL1ZEMTJiMmdhOWtJMUdrVWFtb0JsWER1VU5zYTcyNWJ3OThxS3liUUxvUndpSUxNTkE2d2hmakpzUCtKd3Y1QjNJOUhEUmQzU2xsbFlaNXlwb2RpMnFZZUtFMGZQNHJweVQ0ekxLUGhwbGNSaXhTRThRdmluTjVwWUZxaENtR0xDWVJseEpVTkZETjhLK1haR1Rqak5Ec0l1YXBZTnA1Z1UzbC9NQWgzeXVucVN3NEdaRks5TnRac1o2TU92b1FqUmpZalI0WlRPaHlFMVdNSE9YQWN0aVVXbmp2Um5FckZaY0JWUGVadm9XNlo1WDB5bHJ5alF1STRrejBhTmtwamlSZzVLZG4zN0V3aGZQdVpNYXVYell6RW5WZjhZZGN6cmJQc0N0c3kwK2sxd2pHS2dyWE5QUHZ1QWIzaTlkMW5RMEsxaG91VFAiLCJtYWMiOiJiYmM1M2UwNGNjZTQxMWM3NDEzNmRjNDA0MjllOTYwZjYwZWQ1MGIyZTU4MzBiYzBiOWE4YTdlNDdlNDMyMzVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkkrcENsdjZETjFRS1BFd2xkOGp4bEE9PSIsInZhbHVlIjoiOWpCU1ducmQzdzFtT2QxL2lOQlVOUkhCQkNiUjg1Um5sWU5URHFUNTZHcUdBdXNMQ0FzN2hHMzBsWkdhWXduZXRUV2RMUXFCa2F6cGdPcEhtNFZZb25EYk13SGV6dnFTTDBTdnNuTDhHQWtJSHR4OGNiRi9FdThUTnJKT3VDZGpjelhLNnhxM1BuMFBHQlB3Y1VDZEJpMys1cE0yakp5UjdtbUdOaHNQV3BJYmZidUtDNzVUMDduQzJRLy9melF6OTdRVXkrY3l1YXpFWWVKemtFSmNnZTd3OGszcThXR1E1UERpSVhjMkpOdllMT3h1OFpVa2wvd0JDUVg0QVExb3J4anpVMld4SjV2UEN1MGUyVU1HK3FMVzcrZmVmZEY3bWQyOE1uMnNZN1V5RDU3ZDVVV2VSeEdoc0VHRFRINndzZGNOQjN0NzVKVm9PcWU0b2x6ZHZiY1VnUndETndjMlhFL3ZyNG90WjMrS3IxbFVxNkV5akM3R2xXcUVFQ0ZQZjA1NGxBcHpIYmFhemRLNTFERWRYZGNPcFFMRXF1R2JqRlM5cVlrQldlN2ZrRUVjTzBaa1dPRHE4THNwSGpjN2QxaytjRjhXbWlKNWN3dERaTGpPdWRUQVdlZHMxQzltWnRWdk5QYlBjMkxYeStUMnMrWmNwekZkMWVkUVZpRFIiLCJtYWMiOiJlMTU2OGU1ZGE3NTc5MjIzOWI5YzQxNGM4MzBlNDMzM2Q0NzJmNGI4ZTU1MmEzZWMyMDJlMGU2YjlkOTA5NmQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-40321813 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:28:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJzVkdhS0ZCY0MycUd3WGN0dnRNSVE9PSIsInZhbHVlIjoiZnpmL1NjYjJZRXl6WkNFNVdXOTUxNmRRSmNBZUxqcENrNGVoTG83UDFkY2F2V1BVSjlQUWJxVzc5Y3gzcHlWSGdwa2xrcDlVUG8xMnp2eVprNitCOG5IdWRMZE1RTXJ0TWxxbVNmYmhrRkRGdDFFZVFrT2ZONXUwcFcwZURqaHBnb3MxVVFkNkJqTlJheHRtZlQvTmpzRlh6RGFvWGh3c25TQ3QrV0lWT095OE5GZHBTYzZCWG91UEFtMnVCZ0hHb1lteXA4TEtYM1lUbUZMVjNwMWZWcmVsRW1MWHRocWhoK2VtUi8vQTJlb1dHZ3lnVVd4ek02R2d4Q1craWN5NmRvYzhWMmJRRnpNcmJPeFpWak1QMmJ1em5QaVR3SjhqeEVUbWozMEpIdTBsemcwRUZHQ3lOK09qbG5rNk1FVWtTQXJMSjRBYzJGTzl0NUs2YUxtdTJOUjlpOFFxaTJPbGFwSjR4N0tKMFlvWGRSV1VCK3czelFlNndaS01RcElobHZRVGc2SkVnQ3RGQ0lDTEhIbm95akw4Tk8xdFhROGNEMml6U0ZKSGJZTE5UOHY0ei9YaXB1Y3ViOElHZnBXdlQvSzVwNXJlQ0ltUStwZ21hMkF2R2JpNXk3T2padGtzdnlGUWw3SkdoZEk2T1NsdVR6emJ6aFVYdUpxTStOdHQiLCJtYWMiOiI1MmRmNjlmOWY4YTVhODg2NDkwZTExNmRmYzVkYzQ2YWY3YzM0Y2Y1NjhjOWNiYWQ4NTllMDIxM2M5NDA2ZjE0IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:28:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFPWTE2Ri9zc3lWSGtsMjJIak5SYnc9PSIsInZhbHVlIjoiQzhUK1Qxa1o4K09SWTVmL3hISmFwSWtCTjRjU3ZjVmZpbE1IaVJ3aUI2OWNzY0h4K3l2ZWdzUGwwc3F0TndRRmZOQXpvZlRFNVRDcHRrRmwrS0ppckZWQm9UUHM0NHdVaVVsalh4THgzMVN2T0RHcElGbUxmNmVuV3lDMmJMVlYwWnBmK2V5TlhTcGNwdkJBTC9qbklHUUVpNitoTWJjTll1d1Y5cFFTVU03M3dwNEFYbDR2eHB3KysvanQ1WDF5OUJuSjR0VUF1Ylo4M1lFVG1HNmZ4bGMzQkFNVlRtOUNldHZmOUJkczVxcGJWMXFlT25LVDR2K2tNS2VPbzQzbStKRWtUemtIUzlkb09IanBFRVN4Tk5yMDNTTHhraENqeHZBUUdSWWE5akh6N1N2S1lWZVhpWGQ4QzJCV04vWWVnaElGTnNybjdNSGxvbGJBVmZUaEFzaWNSV0dqczZlcVBkalI2cDFURHNTbHNGblUrVEJPcUxmRitiWG5VejkwQmtudFo3bkxXUUhrT0s4clRQanp6SjRZZTNtb215eHBuUUZWdC9NZGxrY3NHUVNMcTRyN2VKdDdleEJWc3RVV280bGpRQlBRTWNhSzZGbFE1UzA4R3UzYWhaSFh1SUpsbnJpZ3lYbk9keTNLKzhmTkZxWXNzK3gzV01UZjhuUHgiLCJtYWMiOiJkMmUxZDVlZWNkMDdmYzg3NGNhYTIzZjFiYjNmOWFkOGRiMjNhYzRmYzg0M2Y5NDRiOTQwYmVjMjdkM2RjMzNiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:28:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJzVkdhS0ZCY0MycUd3WGN0dnRNSVE9PSIsInZhbHVlIjoiZnpmL1NjYjJZRXl6WkNFNVdXOTUxNmRRSmNBZUxqcENrNGVoTG83UDFkY2F2V1BVSjlQUWJxVzc5Y3gzcHlWSGdwa2xrcDlVUG8xMnp2eVprNitCOG5IdWRMZE1RTXJ0TWxxbVNmYmhrRkRGdDFFZVFrT2ZONXUwcFcwZURqaHBnb3MxVVFkNkJqTlJheHRtZlQvTmpzRlh6RGFvWGh3c25TQ3QrV0lWT095OE5GZHBTYzZCWG91UEFtMnVCZ0hHb1lteXA4TEtYM1lUbUZMVjNwMWZWcmVsRW1MWHRocWhoK2VtUi8vQTJlb1dHZ3lnVVd4ek02R2d4Q1craWN5NmRvYzhWMmJRRnpNcmJPeFpWak1QMmJ1em5QaVR3SjhqeEVUbWozMEpIdTBsemcwRUZHQ3lOK09qbG5rNk1FVWtTQXJMSjRBYzJGTzl0NUs2YUxtdTJOUjlpOFFxaTJPbGFwSjR4N0tKMFlvWGRSV1VCK3czelFlNndaS01RcElobHZRVGc2SkVnQ3RGQ0lDTEhIbm95akw4Tk8xdFhROGNEMml6U0ZKSGJZTE5UOHY0ei9YaXB1Y3ViOElHZnBXdlQvSzVwNXJlQ0ltUStwZ21hMkF2R2JpNXk3T2padGtzdnlGUWw3SkdoZEk2T1NsdVR6emJ6aFVYdUpxTStOdHQiLCJtYWMiOiI1MmRmNjlmOWY4YTVhODg2NDkwZTExNmRmYzVkYzQ2YWY3YzM0Y2Y1NjhjOWNiYWQ4NTllMDIxM2M5NDA2ZjE0IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:28:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFPWTE2Ri9zc3lWSGtsMjJIak5SYnc9PSIsInZhbHVlIjoiQzhUK1Qxa1o4K09SWTVmL3hISmFwSWtCTjRjU3ZjVmZpbE1IaVJ3aUI2OWNzY0h4K3l2ZWdzUGwwc3F0TndRRmZOQXpvZlRFNVRDcHRrRmwrS0ppckZWQm9UUHM0NHdVaVVsalh4THgzMVN2T0RHcElGbUxmNmVuV3lDMmJMVlYwWnBmK2V5TlhTcGNwdkJBTC9qbklHUUVpNitoTWJjTll1d1Y5cFFTVU03M3dwNEFYbDR2eHB3KysvanQ1WDF5OUJuSjR0VUF1Ylo4M1lFVG1HNmZ4bGMzQkFNVlRtOUNldHZmOUJkczVxcGJWMXFlT25LVDR2K2tNS2VPbzQzbStKRWtUemtIUzlkb09IanBFRVN4Tk5yMDNTTHhraENqeHZBUUdSWWE5akh6N1N2S1lWZVhpWGQ4QzJCV04vWWVnaElGTnNybjdNSGxvbGJBVmZUaEFzaWNSV0dqczZlcVBkalI2cDFURHNTbHNGblUrVEJPcUxmRitiWG5VejkwQmtudFo3bkxXUUhrT0s4clRQanp6SjRZZTNtb215eHBuUUZWdC9NZGxrY3NHUVNMcTRyN2VKdDdleEJWc3RVV280bGpRQlBRTWNhSzZGbFE1UzA4R3UzYWhaSFh1SUpsbnJpZ3lYbk9keTNLKzhmTkZxWXNzK3gzV01UZjhuUHgiLCJtYWMiOiJkMmUxZDVlZWNkMDdmYzg3NGNhYTIzZjFiYjNmOWFkOGRiMjNhYzRmYzg0M2Y5NDRiOTQwYmVjMjdkM2RjMzNiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:28:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40321813\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-9624772 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9624772\", {\"maxDepth\":0})</script>\n"}}