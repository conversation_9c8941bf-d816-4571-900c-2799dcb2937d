{"__meta": {"id": "Xa7642d22ed704f1952a70d23b1eb12fb", "datetime": "2025-06-06 19:37:29", "utime": **********.133527, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238647.489141, "end": **********.133566, "duration": 1.6444249153137207, "duration_str": "1.64s", "measures": [{"label": "Booting", "start": 1749238647.489141, "relative_start": 0, "end": 1749238648.927213, "relative_end": 1749238648.927213, "duration": 1.4380719661712646, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749238648.927238, "relative_start": 1.4380970001220703, "end": **********.133571, "relative_end": 5.0067901611328125e-06, "duration": 0.20633292198181152, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44763400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00807, "accumulated_duration_str": "8.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0168302, "duration": 0.00501, "duration_str": "5.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.082}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.069139, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.082, "width_percent": 12.268}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.08123, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 74.349, "width_percent": 13.879}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.104373, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.228, "width_percent": 11.772}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-367031933 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-367031933\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1290224305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1290224305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-511763932 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511763932\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-590548406 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749238565066%7C50%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJmRTd3MTVFZTcrUGg2VGhpWE1MSnc9PSIsInZhbHVlIjoiZlJrbHY4TXV0dmtndFZKMUo5WTBPVjFBdWxscHdYYVlyeGVDV2o1cFlEQTRWQ2s0L1B1QWJkcE04VVZOZW5idVNySEhYdlV2UzhWd1o0eVVKb1c1L1ZHeDg5d2VtaDZIbklZT0Y3dzNmRFczdHhxT044cnVQVjlzdldrZFRKUG4rWFBEWkliVi9CWHNSamJOSWczeDRTVUF5VDJKc3lIMGdkcFBhRHpPTDhUUDhhNVNWZWVaazNSeFB5c0NRZFlMZkJOQjZzRTVLRkdrVzRYUU1TOGdzSTlRUWNXZzFLdHplbjFjN0dLTENoSk9BRC9SVWxCbyttMWhUaWpCT3gzU3BzVk13NHAzMURUWUFSYlpkSTN5UDMzVFVyenN4QnJKK01XaEVzWkE5UThnN1hCZEsvS1JGcllQdjk1amtoZGpwL0d3d3dPV0QzTWRyK1dTSmFVd1NweE1Ud3Zsb3dZeng0SUlyT1F6aTZHSDl1ZTRnK2VWdjNVWGVacFBxakNTVFAyKzFJUG0vcEd2RFlOS1FjZEhFV21JSUZUNWtpYnNEZFFMaUgvcmxJRzZUTTBaOElsZk1FbXlpWFBCU0QrME1BYjJMRmhteHp0NUZKbXNrR0s3MWRmU09ZN0NBZXg4YmR2OHlybFVab2VZSWZxVktldDI5MFhZczh6TFc1bHIiLCJtYWMiOiJmNTBhYWMzM2RmMjY1MWJiZWMyOGQ0NzVkZDRkNTljMjkzNmZlODJjZTA2NzEyMmZlOWIwMjkwMzU0ODBmMGE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFqUk16YUFBZGhMaDQzeFJRNWNJWGc9PSIsInZhbHVlIjoiaHphK0E2YVhFQ3YzdnVKa3YrQVloaUZRV1o2RDU0LytQN29XWFBaWGdRU1EyYjhPTXdZVTd2dFdUTzdJZzRHbkpaNkZudVhnOVk1bW5uMlhJZmlPR1hndWpNQXk0ejF6VGgzVTJZYmRoME1DR2JPQisyd1UyOUgrZFhFR2YybmVaM1BhOHdEeTJGTmRSM0NjWXJkRm45RTZoNnczdkdrWUJLNmREdCthVVpSN2NPYzZOTDdHemZLcEhlbysrbmVwWjJOeFJuTGg3aUh6ZDk3WnVTNG9tSjVtYmVEVkRkT1AwRTdJVW96MjlreUtoYWl1ZmhNQUkrOG1OK1dwLzVPbHMvUU9Wckk0V1FkMWZGcE5HYlIrNDE2by96OVJtY3RyNVV3enh4T3JWcDZaTjFSZVUwYk8wbFlEdlhsZUNpK3ErMHp5V084dnU3enNCMGYxTjdNbFFKQUxyZGRDUGNseXF3Sm15Zk9kajg1VGZleUx5UkN2QS9kYlRPN1NUem1nSVpOVWYyZHpvanZqRWRSTFhpQS91b1U1TTVVU1dwYjdvOGxCMW9QeFlOMU5vYzZJN1dMdWVhWXdCZnJpMVhuemsvOVpuUXB2NDI4cS8rZkRCZFFDUU1icW8rd1JIVFdpUXFIcDRtd2ZPVk9KZXQvK3p4c1ExNjBaUlJZSWozOHYiLCJtYWMiOiI2NzYzMGE5MGY2YmJkZjViYzhmNjhkYWEwYjMxYTE2YWM1YTM0ZDRjYWRjMTQzYTJiODczNDg2YTlkZjJlZmY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590548406\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1530890432 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SP3TwyIpV6SLjlS9jg5OHV9UjdlnIWXvvnBW98CX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530890432\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-347221387 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:37:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijc2NTBPMjVpMFVRMktGYlpVUm9paXc9PSIsInZhbHVlIjoiQVkybjlJRUp4eGMwS1ZqY29KTGlnNkI4MVJ3VmRJNTZDMWU3UWdoV01XNkFEbWYzN1RNVVkxL2sxWHJIY1hhVUZBaWhPQUN4eHo5cmNISjhLTXJORkdEMm1HYXJkT1VqVC90bVIrc25HbTgxMitULzBFOTJXVjhyT2xkQURPMUErN1VnYWpWeitvOWdsT1FPdGt2eTlCYlJGaEJ3ZXY0amw5YlRLZWFJc25kYjVhTHlZNHVycXpJODBDaHVaQXFTTEd1Y01INGdPbFl3eUhucTU2clZ3c3lJQkRhOHBRN2FBR3JjNFhQVHZNQlBScXRZZHpucnl4R0IyMkUvVGlKQlRCbHU5STJsbXJNYmxGeUFrNVNtbi9sZjJ4czUyOVc4UkhMZ1dzOEJZekNvWmhIazBPVUp3R0tmbm93QWdpSjVqajhSQjVkc3JHTFU1NlQxYWRwVVN3cE03LzNzN1QwQy9nN1gzaFNyYnZPbWsxd3ZkeW9NK3F0UjNRYVlNeTM5ZTVza1BveWNPejFkb1plUHBzYzh5LzJBQUM4M2VoS0xtUzhNZlFKa2hCYzN0MTlzVURaZnRhRDlQUHpMbFFrUHVNaDhLSGUrWkNwM29tM0cyYzFrMGJ1WGhycktXcndxeU9Zdm5LRllFNng3TkYxT28vU3NTRGJJN0RtdnlEbUEiLCJtYWMiOiJhZDc4ZjdkOTVmOTRhMGJhNTQzYWE2MzBlNzE0YTkwNDc4MTIzMjFiNWQzYTY2ZjUyNzA2M2U4ODUwOGViYjdmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImErbjR2Q1BoTHJaTEVGUGlYcS84aGc9PSIsInZhbHVlIjoiSUhEdkxJNkNvb1pPRkp0amptdmVsK1lNdVRiZGtBWG5nZk5pYkI0Y2pkdFJtUmVUcGJmUmxmRVpWbVJ3b2tJaUxmajd1SjhRZS9tRzRnNW9KNWlYNVhOenBadzRJOGlQV0s0czVQV1JmZmNQQXJYL0ZTRHVRb1JycHFYRWM1OE11cGw1WGtVS0w1WlhCUWhRK0ltQVJlS0tpTGxEazJ0WStYWU9jcGt4cUdtQnJiK3p4OERiTXBwMWFvRVZFYWlLTTVzT3VYVXdZOEJlU2JsekdUK012QUVHZXNhV2YvN1kzMFNXTGlEclkzcENKUlR6andYOE9QUzR0cmk3VnpaeERMWElMWmYyM291U0ZPcE1JS0o2Qyt0aUlKYjM5dGlETnRVU3g2dXNTRlVxY29HUkgzcG5WaHdWMFpicTVYdkVNWHhQOE1oSGw1bEQySktKWWw1cjVUcjhHRGRBczdTOVFRc1ZrVGFxT205MVQvQ3FvcjMybkRDVHd6Nk9qSnlvUGkvNkIvS2VNaXlHZzhsdVNXMU1iSEFGODZsaWJqMzRaUXltWVRBbkxnS0NIWFUycmJJdytDNEplc09nODU4R1ZrOFdCbmtjL1Jma2xjeG15MzhOeTd5eFNPODBNTWNkSjA1cG1EOU5BLzFqUnhBUFRJL01nNW1DNXJ1SDNzQzEiLCJtYWMiOiI0Njc3OWM0NjI2N2M5ZWY2MDc3MDhhYTE2ZGVhMmYyYmU2NTE1OGEwODI0YjhiZTI0OGQ4MGE4ZTg3NjgwYzRmIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:37:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijc2NTBPMjVpMFVRMktGYlpVUm9paXc9PSIsInZhbHVlIjoiQVkybjlJRUp4eGMwS1ZqY29KTGlnNkI4MVJ3VmRJNTZDMWU3UWdoV01XNkFEbWYzN1RNVVkxL2sxWHJIY1hhVUZBaWhPQUN4eHo5cmNISjhLTXJORkdEMm1HYXJkT1VqVC90bVIrc25HbTgxMitULzBFOTJXVjhyT2xkQURPMUErN1VnYWpWeitvOWdsT1FPdGt2eTlCYlJGaEJ3ZXY0amw5YlRLZWFJc25kYjVhTHlZNHVycXpJODBDaHVaQXFTTEd1Y01INGdPbFl3eUhucTU2clZ3c3lJQkRhOHBRN2FBR3JjNFhQVHZNQlBScXRZZHpucnl4R0IyMkUvVGlKQlRCbHU5STJsbXJNYmxGeUFrNVNtbi9sZjJ4czUyOVc4UkhMZ1dzOEJZekNvWmhIazBPVUp3R0tmbm93QWdpSjVqajhSQjVkc3JHTFU1NlQxYWRwVVN3cE03LzNzN1QwQy9nN1gzaFNyYnZPbWsxd3ZkeW9NK3F0UjNRYVlNeTM5ZTVza1BveWNPejFkb1plUHBzYzh5LzJBQUM4M2VoS0xtUzhNZlFKa2hCYzN0MTlzVURaZnRhRDlQUHpMbFFrUHVNaDhLSGUrWkNwM29tM0cyYzFrMGJ1WGhycktXcndxeU9Zdm5LRllFNng3TkYxT28vU3NTRGJJN0RtdnlEbUEiLCJtYWMiOiJhZDc4ZjdkOTVmOTRhMGJhNTQzYWE2MzBlNzE0YTkwNDc4MTIzMjFiNWQzYTY2ZjUyNzA2M2U4ODUwOGViYjdmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImErbjR2Q1BoTHJaTEVGUGlYcS84aGc9PSIsInZhbHVlIjoiSUhEdkxJNkNvb1pPRkp0amptdmVsK1lNdVRiZGtBWG5nZk5pYkI0Y2pkdFJtUmVUcGJmUmxmRVpWbVJ3b2tJaUxmajd1SjhRZS9tRzRnNW9KNWlYNVhOenBadzRJOGlQV0s0czVQV1JmZmNQQXJYL0ZTRHVRb1JycHFYRWM1OE11cGw1WGtVS0w1WlhCUWhRK0ltQVJlS0tpTGxEazJ0WStYWU9jcGt4cUdtQnJiK3p4OERiTXBwMWFvRVZFYWlLTTVzT3VYVXdZOEJlU2JsekdUK012QUVHZXNhV2YvN1kzMFNXTGlEclkzcENKUlR6andYOE9QUzR0cmk3VnpaeERMWElMWmYyM291U0ZPcE1JS0o2Qyt0aUlKYjM5dGlETnRVU3g2dXNTRlVxY29HUkgzcG5WaHdWMFpicTVYdkVNWHhQOE1oSGw1bEQySktKWWw1cjVUcjhHRGRBczdTOVFRc1ZrVGFxT205MVQvQ3FvcjMybkRDVHd6Nk9qSnlvUGkvNkIvS2VNaXlHZzhsdVNXMU1iSEFGODZsaWJqMzRaUXltWVRBbkxnS0NIWFUycmJJdytDNEplc09nODU4R1ZrOFdCbmtjL1Jma2xjeG15MzhOeTd5eFNPODBNTWNkSjA1cG1EOU5BLzFqUnhBUFRJL01nNW1DNXJ1SDNzQzEiLCJtYWMiOiI0Njc3OWM0NjI2N2M5ZWY2MDc3MDhhYTE2ZGVhMmYyYmU2NTE1OGEwODI0YjhiZTI0OGQ4MGE4ZTg3NjgwYzRmIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:37:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347221387\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-337788385 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-337788385\", {\"maxDepth\":0})</script>\n"}}