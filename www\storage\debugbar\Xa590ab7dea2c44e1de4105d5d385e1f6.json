{"__meta": {"id": "Xa590ab7dea2c44e1de4105d5d385e1f6", "datetime": "2025-06-06 19:24:44", "utime": **********.312301, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.648828, "end": **********.31233, "duration": 1.****************, "duration_str": "1.66s", "measures": [{"label": "Booting", "start": **********.648828, "relative_start": 0, "end": **********.117679, "relative_end": **********.117679, "duration": 1.***************, "duration_str": "1.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.117699, "relative_start": 1.****************, "end": **********.312333, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01146, "accumulated_duration_str": "11.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.211949, "duration": 0.00841, "duration_str": "8.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.386}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.246634, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.386, "width_percent": 9.075}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2850819, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 82.461, "width_percent": 17.539}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=mp51by%7C1749237880080%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBPQTA1VnhXSmVGdk8rYVdzTzZMUmc9PSIsInZhbHVlIjoiOERYQ2dQU1RHdGgzdGIyQXRQbGJ0aEtZS1lnMXF1MEdlczZ4VE8vUkovOHdnTElsaVlvbnhCR09rM0p0VS9JQTlNTG0wSVZJL1JFbWdHbkp0RjIzRitRVmxsb0tLNFlZSUlLK1Axc0cwdFk3ZXJhVWdXT1paZDVlbTBwKzViVWNsdDJIRWVPVXNqN0xZM3F6ZkFEdjdXMldMOElaTmpvcUNvem9BQk5yOUE1alJOL2p6dHZJOHpnRGJsTmk1Z2pma000cFNoK2VjYkMvZVBWR2taU210V3JFSFg4c1MxYi9JZEhQM3JibVpiYnBhRnhDVEkyMHNDc0xXZ1E3YkROL3BBaW1xd0NDMTdyWUErMklJQ1dNaXNNai90T3ZKV0Y2RWpYcGdTL05mMk41Rnp3T00vcFhTMkZ1K3h6bXZWVUNXWHpGd0t2a2RuZHQ1Ukh0WFdXc3RiZm13anpzM1FsZ1RILzhaMWxEWmZML1VSNXVnV21nMDBrSWxoNTRpcWR5eEFGZERxa2pVd1VJYytyYmc0eHdBa3doMVlXLy8wdlMybVk3V0FTMG5QejFzZVdoTHZyNitjeUY2OWQxTWdDOHpSZ3FJUHUvMEdWSTdqL1c2TVpLYnVldWVpMTZjOEpIOVB2WE1reWZsdTUwMU5GNisvMjJSVUJoTUJmdk0zVXciLCJtYWMiOiI4MjEwODUzN2Y3YWZhODAyYjk3NWU1ZWVhNjEyYmVjZGY0NTIyZTk3NDQ4YTE1NzQ1MmVhYTRhOTg5ZmUwZWYzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVRendTeFRzTTJtZ3owb2NwWExZRmc9PSIsInZhbHVlIjoieXRlamVycU0xTW1JRVhRTzZzSnU5QS9NaEsxVHJLaTdGa2FiSHNlZkxXUUpJRGNkRTY0SGlLK0ZnMHdJQUNHRzgyS1EybWZvcFpJanJpS28vbW1maUVYRXR1dTE1WE1GcWZmb05zSlJoZ2VYN2JSbFpHZ1ZNMGd0aUZpZ25VS3kyOUdEa1QwVDM2dWpmcGYyazVaT0NUbXRxUDJVRFRpd3N1N1NNd094bWhJaERWWHZoRmZsc1JqMEFHRWtFS29DMG9pUWxHd3I1NFJIbFg5Tm5uR1pCaHdSeXZkSmYrNldLcUVObEhsSkMrbjFZSFpQS1ZPSEFFSmp2OTFhTXI5ZzFzRXptODZHK1ROS251RGo1VlZEUEV5MU42QkxQaUpWVzVrZ0pwNHhhd0tSQTBFM1htOXIrQnZINnI1NkhBVzBVSzJwM0hZZkx5ODZ3OGxaRlNxSVZaejRSZk9kTnk4NktRcEdTWlJVbDFIMmNuREJWRDNZVEZZemdQTHAvNlRjczk2Y3NURXFJRFYvZ01uS1JlaFkyZ0ZtbnZpbkFUWmhYWlNQZjJWdnZYN2xjdnFESGVBUHlCbDg3YmRqYTJ5QVUrRERsRTRJWDNqbUhmbWovRTAvNWhQYXJRQW9TYjdqeGNnUGl3dC82Um4vNVdYYkVZMUxUOENzWFlqU2trWTMiLCJtYWMiOiI4OGRjOWExODYzZDA0ZjNkYzIyMjU3NjMxMTUxMmM2NjYzYTc4N2Q4NGY2OWIxNDJjNWUyZmEzMjVjODc0ZmQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-538617318 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bqimaRKNMYDaY70bMn4neav5Q7SUSQNahmKxKJLG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538617318\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-39413686 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:24:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNPWGl6b1FWbzh4NU1SV0Z5dkNYdlE9PSIsInZhbHVlIjoiNzMvRkJYdFdtZmtlOXhFZ1VQM1hVdlg3UVNFT2pTOURqci93VVB1UlB4ZlI1R3J0UnJVV2NBSERJZDJJM1l4VFJKQ0U3cEFWdGNWYkJpTmIrOUJYN2UvNWlrRDI3ZVgxT3MyZjN1aU1aQVVzUWtOOVEwVUN3bzJRcHc0QTgyVVFvYkxwQlFUVnMwaE1DWVRMT0hCemt5MWVUYmh3YWdiVVZ1MkR3MEwvc0RwVVZiUzRGcFpLelZRRVByTVNQNDBzYVRtRnJqdU4zREt2UFhMYldIbWppOUZsYmZqUGgwdEJWNTJXS2dFWVJQdDNma2JDWE5PWFpuTENWN1BDRG9zM3VCWGtKZWpSUUZSKzhwQnVpbTNMbWJPVkhQaXZ4ZDUvS1Y3ME8weWxNMG9kN25XbEl0cklJOXNKU0dGN2VtNTFQS3EyVXJlMG9ERUJaY3lIS0Z1cHVKb1hheXM1LzdYODJBak8veWFHMldWZmNXeWZJNXhxUXhhYThqN0hwM2p6UkVEVHRRUmFCcVk5dUszLzl6eWw1OXdhUWNHRnE1cHhrcHlQN3RlQlpOWHVxVFZaNGZSOXlLZ3R5RzVVNWY2dW5QdGtydFc0SU95amtYR2gyZk1ZZHgzdWIrbnY4V2tzdUJSeS9EemVUZzVWRXhLdXcveFdlVmJFeUNYTElJN20iLCJtYWMiOiJjYjJjZGYzZTIxZDJmYTA0ZWY4NmM0ZTkzMTEzN2ZkODg3Y2E2NDA2Y2Q5MzE2YzAzMDEyYmIwYTQ3ZTE3MGI3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFJZE9Wb0RFK21tWWdqRXV0YjQ1b0E9PSIsInZhbHVlIjoibXBxeG1GOER5SUl0TWhUbmowMGhwNWQ0U1FnNTFjdnR2TDNrQXZseE94YUVLOGsrQU04eW5BdS9Baktqb1JCU3FKNlppTWIvQlU2U2ZVbjBpaFN0M2VPajMwWGRwNEE0ZXdHWnc0T2V5UDNVM2E0UGoyVlcyZ0dRc0FZQTdBUEtzbUhxQlpraTJnVzVBK3JOQWVDTTJGNVlQamRpL2RzZ1MwZCtWK1VETHNvNlN2ekovVFdKN0UzWDZDR1Qvd2FEYWIxRTkzdjd6S01mLy9zbUZuNnA1TW94a0RjZkp5R2E1eUpNMVkxUTgwSFJHKzR2SS9BQXByV1lObUk2ZWtjLzVaaDdVUm1hYkc1SU1PdU1IclJtMUtXTmpkUTd3elk2T1BBUDJKQjBwTlNqYkJnb0dMdWpPYitHM1BnNGh6ajlMYnAvUU45NWlDa3FzNFFyNkhXMm1Ma3ZHVDZLcm9MQWZFbjcvOVBXQzRpNzhJODY3OXVYQnJvVFZwUzNRVTd1NERNemV6aE5aUnJKN3lNcnNkTUlwSjZJSnNwRnVNVytVbW12QndQdnNGKzVoS28vUlkyVjZvaGJOQUhaNDJiQ29UWFEwMVJYalloOFZqRGJKZ0hyNGhDWFFHR1ZoazVZYTZ2WlpTZm5KNFJjb3VSRE5PNy9GV1Z0NlBEMi9RNXAiLCJtYWMiOiJlNzIzZTMxYTBkN2NjNmUyMzI4ZjFjODhkMWQwMTA4OTliYmQ5ZGJkNTE2ZTVhZmJiNjhhYjI1Y2NiYjVhNGZiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:24:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNPWGl6b1FWbzh4NU1SV0Z5dkNYdlE9PSIsInZhbHVlIjoiNzMvRkJYdFdtZmtlOXhFZ1VQM1hVdlg3UVNFT2pTOURqci93VVB1UlB4ZlI1R3J0UnJVV2NBSERJZDJJM1l4VFJKQ0U3cEFWdGNWYkJpTmIrOUJYN2UvNWlrRDI3ZVgxT3MyZjN1aU1aQVVzUWtOOVEwVUN3bzJRcHc0QTgyVVFvYkxwQlFUVnMwaE1DWVRMT0hCemt5MWVUYmh3YWdiVVZ1MkR3MEwvc0RwVVZiUzRGcFpLelZRRVByTVNQNDBzYVRtRnJqdU4zREt2UFhMYldIbWppOUZsYmZqUGgwdEJWNTJXS2dFWVJQdDNma2JDWE5PWFpuTENWN1BDRG9zM3VCWGtKZWpSUUZSKzhwQnVpbTNMbWJPVkhQaXZ4ZDUvS1Y3ME8weWxNMG9kN25XbEl0cklJOXNKU0dGN2VtNTFQS3EyVXJlMG9ERUJaY3lIS0Z1cHVKb1hheXM1LzdYODJBak8veWFHMldWZmNXeWZJNXhxUXhhYThqN0hwM2p6UkVEVHRRUmFCcVk5dUszLzl6eWw1OXdhUWNHRnE1cHhrcHlQN3RlQlpOWHVxVFZaNGZSOXlLZ3R5RzVVNWY2dW5QdGtydFc0SU95amtYR2gyZk1ZZHgzdWIrbnY4V2tzdUJSeS9EemVUZzVWRXhLdXcveFdlVmJFeUNYTElJN20iLCJtYWMiOiJjYjJjZGYzZTIxZDJmYTA0ZWY4NmM0ZTkzMTEzN2ZkODg3Y2E2NDA2Y2Q5MzE2YzAzMDEyYmIwYTQ3ZTE3MGI3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFJZE9Wb0RFK21tWWdqRXV0YjQ1b0E9PSIsInZhbHVlIjoibXBxeG1GOER5SUl0TWhUbmowMGhwNWQ0U1FnNTFjdnR2TDNrQXZseE94YUVLOGsrQU04eW5BdS9Baktqb1JCU3FKNlppTWIvQlU2U2ZVbjBpaFN0M2VPajMwWGRwNEE0ZXdHWnc0T2V5UDNVM2E0UGoyVlcyZ0dRc0FZQTdBUEtzbUhxQlpraTJnVzVBK3JOQWVDTTJGNVlQamRpL2RzZ1MwZCtWK1VETHNvNlN2ekovVFdKN0UzWDZDR1Qvd2FEYWIxRTkzdjd6S01mLy9zbUZuNnA1TW94a0RjZkp5R2E1eUpNMVkxUTgwSFJHKzR2SS9BQXByV1lObUk2ZWtjLzVaaDdVUm1hYkc1SU1PdU1IclJtMUtXTmpkUTd3elk2T1BBUDJKQjBwTlNqYkJnb0dMdWpPYitHM1BnNGh6ajlMYnAvUU45NWlDa3FzNFFyNkhXMm1Ma3ZHVDZLcm9MQWZFbjcvOVBXQzRpNzhJODY3OXVYQnJvVFZwUzNRVTd1NERNemV6aE5aUnJKN3lNcnNkTUlwSjZJSnNwRnVNVytVbW12QndQdnNGKzVoS28vUlkyVjZvaGJOQUhaNDJiQ29UWFEwMVJYalloOFZqRGJKZ0hyNGhDWFFHR1ZoazVZYTZ2WlpTZm5KNFJjb3VSRE5PNy9GV1Z0NlBEMi9RNXAiLCJtYWMiOiJlNzIzZTMxYTBkN2NjNmUyMzI4ZjFjODhkMWQwMTA4OTliYmQ5ZGJkNTE2ZTVhZmJiNjhhYjI1Y2NiYjVhNGZiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:24:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39413686\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-488145352 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488145352\", {\"maxDepth\":0})</script>\n"}}