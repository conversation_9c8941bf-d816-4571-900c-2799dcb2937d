# خطوات تشخيص مشكلة الشاشة المنبثقة الفارغة

## 1. فحص Network Tab في Developer Tools
1. افتح Developer Tools (F12)
2. اذهب إلى تبويب Network
3. انقر على رابط نقاط البيع
4. ابحث عن طلب AJAX إلى `/pos-financial-record/opening-balance`
5. تحقق من:
   - Status Code (يجب أن يكون 200)
   - Response (المحتوى المرجع)
   - أي أخطاء في الاستجابة

## 2. فحص Console Tab
1. اذهب إلى تبويب Console
2. ابحث عن أي أخطاء JavaScript
3. تحقق من رسائل الخطأ

## 3. فحص الملفات المرفوعة
تأكد من رفع هذه الملفات:
- app/Http/Controllers/FinancialRecordController.php
- app/Models/FinancialRecord.php
- app/Models/Shift.php
- app/Services/FinancialRecordService.php
- resources/views/pos/financial_record/opening-balance.blade.php
- routes/web.php (مع إضافة الروابط الجديدة)

## 4. فحص قاعدة البيانات
تأكد من تشغيل هذه الـ migrations:
- create_shifts_table
- create_financial_records_table
- add_is_sale_session_new_to_users_table

## 5. فحص الصلاحيات
تأكد من أن المستخدم لديه صلاحية "manage pos"
