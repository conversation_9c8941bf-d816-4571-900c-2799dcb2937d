{"__meta": {"id": "X8df0c1e8d9a1ec0344dc2385bcb20335", "datetime": "2025-06-06 19:36:28", "utime": **********.866636, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749238587.430249, "end": **********.866676, "duration": 1.436427116394043, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749238587.430249, "relative_start": 0, "end": **********.591623, "relative_end": **********.591623, "duration": 1.1613740921020508, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.591644, "relative_start": 1.1613950729370117, "end": **********.866679, "relative_end": 2.86102294921875e-06, "duration": 0.27503490447998047, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45690904, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.762374, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.778474, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.840174, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.851127, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.05502, "accumulated_duration_str": "55.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.669814, "duration": 0.02809, "duration_str": "28.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 51.054}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.707768, "duration": 0.01886, "duration_str": "18.86ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 51.054, "width_percent": 34.278}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.734934, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 85.333, "width_percent": 1.509}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.763974, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 86.841, "width_percent": 2.145}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7809029, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 88.986, "width_percent": 2.036}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.810267, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 91.021, "width_percent": 2.635}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.820878, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 93.657, "width_percent": 2.017}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4679}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 4609}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.827986, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4679", "source": "app/Models/Utility.php:4679", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=4679", "ajax": false, "filename": "Utility.php", "line": "4679"}, "connection": "ty", "start_percent": 95.674, "width_percent": 2.054}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.8428159, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 97.728, "width_percent": 2.272}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1646615557 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1646615557\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-948543092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-948543092\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-898661803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-898661803\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1262692848 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1734 characters\">XSRF-TOKEN=eyJpdiI6IlA2VEJWQStHYUdhcWUvUFNyLzdCaUE9PSIsInZhbHVlIjoicEU5aVA2SStGa01qUk5mZS9HbGtIOTRvTlZ1NjRxRHQyS1p6ZUVML1ZtTmorSzNvMWRIWHpyaFpXL1JIaFhhTGl0ZmRyZTFzNDB3ZWpwdll5K3NsU3JWOE5rd1RzWGFkSjA0SCttZU53UGpkTlE5SElPTURLckw2VDJMbHZXU2cyQ2VWSm0zZTJGMXIvMFdxT096K29tY1ltUFpwdm5FL0VRTThBMG5aVFAwNlVOTlF4WEFZSkczMGdlR2xPdVBjN0JXeGVVN3hrbm1KdjNJNDVaSVJnc2ZJY2VlSWZXd0RRbXI4TzVRaWVqUm1KWmtUdXU2cE93dGdPWUhEa2Vxb3crTzRLY2trOHZkbXBQeXgvbXZwWUJyZmE1a3JMelJDaGVQMEl2amtBbFRiZDB4Mld3NWxGQjJtdnhCTHI5TEtaYnVma08xMFJLZW1NTTl5Qk9QZGFSTHVCd1dLd3hXRXVNaE9OLzVINFAyYUdiOENJc0o2Ti8wTDdEdXhUaXhQZFNta3hDV09WeG03ZUg3RmtYZVJWSHZxYnhGSFBINXlTUlhvdkVpN09tQ0Yxc0NQL0ViNE1jT1RmaStUR3FCRE1WWDB6VUd5S2gzUk9jbjdhQmFFK1BNRkJITmlSNVBLRGFVMVZsU3dVcEVjZ0gzWlF6bzB6YTd3WHBPWHlEbFgiLCJtYWMiOiI2NjE0NDg4Mzg5YTZjOWE5M2UwNWVlMjY5YzgyYjdjZjAwNDZjYmIyNzE0ZDMyMGYwZWQ5NmYwOWJlNzkyMTJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjVZWWNtaGdnTEE1b1ZQM2IzYXdWd1E9PSIsInZhbHVlIjoiNWJyZDFOaVBYVGtycWI0b3ZVc2g3eU5mdHdoaDY1MjZudTFBMG5Db0dUR2RoNkNXbGl5Q2pCeTBVYXVhYk04VE1nMWJERzJET3dxV21TZldNTU9VVW1VZFYvaU1pYkpZTHo1MmtKa1IxN0NCWEg4RVYwOXB3WkZVajVFNitYbUR1VHFDNkNWekNxN1BDMWE4Um1OdHFaRWh0OW9rNEJGdWszcmRYcDRkbmtsZlBIQTFrbkh0L3hnRGF0d0REK1ZpcCtpbDkrc3J0VkdWQTc5SUVDUHJLcW5CY0tucjdhRlJaSG1SbkVRK0ttMVEzdjZWMzBYZC9oKzBnVm9sZ3Joak1GbUEyTGJjYVg5S3hKZ3dTTjlza2l4OURjNTNmcFlwNExldEdMSFhvclJVMEh4QkpWcGpmR2VyaG0xdjFYeStQRXNFTHBiUHVHR2lHRXBPUnlVR2IvRk05OER4MFZXYmdBMEp1VDg0SjRTNmRRNzdmK3N1dENZR2c5Rzd2YklkTHBaYVh3aUpDamlGZTNRbjdUSHNHc2FKczR2ckM1NjduNHhlQVl2VjdIODBPenREcVNSZEM1dWw3VVNodVFOWWVINUdvZVdrMlAzazhQTDMrQy80ekRLR0U4TGZ0RjZXUmFrVEdCZS9kZVNRM3NsNjRyZk1kSGRVOUVDbFZqUUIiLCJtYWMiOiJhZjk3NzE2NzFiMGVmYWI2NTI4ZDVkYmI1MWEwOWE5ZWU2MzM2YWI4ODhmOGM0NjVkOTE2NWM2N2NhYTAxOWU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262692848\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOZK4vN7lTGMSSRxMBpMo3lLnBOKwWlGXI684UA3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1160206782 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 19:36:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlYyTDc5YTE3OEVNckZXS09tT3lQY2c9PSIsInZhbHVlIjoidHZUTkFueEJWdEU3dzczanYva1FkRWtRNk9jQ0Y1VmsrOXlNSjc5dW5lMVFRZndlSjZFYi9lMFdObUxXSjBnUnlhOGR2NW9pb29MWXlKSk1pZ1ZkbGpkb1ByYjZ2dWhXcUlwUmVUc0JIS09ySUUydWd5RlptRjlSQ1RsVW9tWWk5YzFYYm9YUndWblBydlhkR2hBNFJkRzlrNEpuTXhyUkhSRzEzbGduSW9UWW5lTmdLZE5uMzBPTHlHSVFJbjFObThlRGxoWDdtOGtzMmRScld1aHRLU2lLbVFvY3dacmRyV2NLdlV3d2hpeEhVWXhLdnNVTkZwMjhZbmJkQnhpaElSWlA3L0ptOE5VSG10YllVWkczQk5mYnJDUldJTXJTVGxhbVZDcWNiMjJOV29pQ25nUXN2VUVBVmNpTk4rODd2amNEWXU4aUhvcjNnc1JnS2dWbzhDd1hoMGRoZjdUVmlsN2M4aUYxYUhTbE9oOEtmY2FSaGVRYzRqMmdCYS9IMllsc3NFTGpDMXN0TDhITUgzNzBzOVYvNHcxQjFVUUU1aUdabWF1K3AzTUI5SDNKbnRhcEZYK1FmdjFtcVZ6ZWxybFRNalkwWEdNbVNBT0I2d3NPMFlGMFFVaFA2ejF0OHhmemI1YTNJOE5iVlBZSzNvaXd5Ry9kZ0Q4WXRWMTgiLCJtYWMiOiI0YWNkOWM0ZjIwODFkN2Q5OWY1ZjliZjU5NGExMDZkNmRlYmNjZjJlMDVkYmRkYTMzZDFmYzZmODIwOTQzZDUxIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:36:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklXWEJkV0oyUHlZVHJab08wc0xUanc9PSIsInZhbHVlIjoiY05jVXp2RnROd3AxZDdudVdKYXV6NkxDNDdoeVNqV1VJVWF2TitTcnRhWDljVWVPODg2VnhGcGFlakRUMndRamhHMmZBMmNOQWpqMUdweEJ0aTRZRVpzcEhLYmtPZ0RrTEhnOFdJSmhGV3dVU21oZmN2K3pRS251VzVtMndXZnByaXpzODB6cW8raDJ5SDRpdGZBeVlnWU50M3VycGQvL0dRaUxWNW1zZzJ2SjcrNDdPR2wrem43eXBQbjJTOVdORHZKeitISHVaUnNjK01BT2NnRVpqT3hqUFFWMWlzSWpmZ29qSzVHZThuK3ZHZ2oySGVoeE9KRjZKRlNKOWoyNjRydjhKN25XckZGQVFBUWhyZEo3NDNyRjFVYzVLOUxyZEZ4ME1JRDNINkFVb1Zha3duUW9sV256WTYvdFBHK1VBdDF4d0pQNDc4WUdwLzBtaDZuTXorS3V1akloK0lpOEVnMjdEWFpFbEVWNkd0RURWT1Y0ZFF4cVR5bGRVa3VwdWZVR3lQVmpXVFpPakxnNFpEVnBxUExBQStZUEQ1bkRNL3lIYTROZUVncHh0Q2dzdlN4WS9RM3NOQi9ha213TzRPTVhHTGtHRGJvQ1NuWjc2bzJlZGQ4ZmxnOHVoaUU5UjR4Q25meEd3MWF5WjFsT1dGcWR1NGYzKzh6cW5DcnkiLCJtYWMiOiI4MjcyMGQzODVmMjg0MWYxZGQ5YjQ0NTQwYTFhMmVjMDg2MzNmYTVlZDRjZjBiNzU0MGUwOWM1ZTBmYzcxYjliIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 21:36:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlYyTDc5YTE3OEVNckZXS09tT3lQY2c9PSIsInZhbHVlIjoidHZUTkFueEJWdEU3dzczanYva1FkRWtRNk9jQ0Y1VmsrOXlNSjc5dW5lMVFRZndlSjZFYi9lMFdObUxXSjBnUnlhOGR2NW9pb29MWXlKSk1pZ1ZkbGpkb1ByYjZ2dWhXcUlwUmVUc0JIS09ySUUydWd5RlptRjlSQ1RsVW9tWWk5YzFYYm9YUndWblBydlhkR2hBNFJkRzlrNEpuTXhyUkhSRzEzbGduSW9UWW5lTmdLZE5uMzBPTHlHSVFJbjFObThlRGxoWDdtOGtzMmRScld1aHRLU2lLbVFvY3dacmRyV2NLdlV3d2hpeEhVWXhLdnNVTkZwMjhZbmJkQnhpaElSWlA3L0ptOE5VSG10YllVWkczQk5mYnJDUldJTXJTVGxhbVZDcWNiMjJOV29pQ25nUXN2VUVBVmNpTk4rODd2amNEWXU4aUhvcjNnc1JnS2dWbzhDd1hoMGRoZjdUVmlsN2M4aUYxYUhTbE9oOEtmY2FSaGVRYzRqMmdCYS9IMllsc3NFTGpDMXN0TDhITUgzNzBzOVYvNHcxQjFVUUU1aUdabWF1K3AzTUI5SDNKbnRhcEZYK1FmdjFtcVZ6ZWxybFRNalkwWEdNbVNBT0I2d3NPMFlGMFFVaFA2ejF0OHhmemI1YTNJOE5iVlBZSzNvaXd5Ry9kZ0Q4WXRWMTgiLCJtYWMiOiI0YWNkOWM0ZjIwODFkN2Q5OWY1ZjliZjU5NGExMDZkNmRlYmNjZjJlMDVkYmRkYTMzZDFmYzZmODIwOTQzZDUxIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:36:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklXWEJkV0oyUHlZVHJab08wc0xUanc9PSIsInZhbHVlIjoiY05jVXp2RnROd3AxZDdudVdKYXV6NkxDNDdoeVNqV1VJVWF2TitTcnRhWDljVWVPODg2VnhGcGFlakRUMndRamhHMmZBMmNOQWpqMUdweEJ0aTRZRVpzcEhLYmtPZ0RrTEhnOFdJSmhGV3dVU21oZmN2K3pRS251VzVtMndXZnByaXpzODB6cW8raDJ5SDRpdGZBeVlnWU50M3VycGQvL0dRaUxWNW1zZzJ2SjcrNDdPR2wrem43eXBQbjJTOVdORHZKeitISHVaUnNjK01BT2NnRVpqT3hqUFFWMWlzSWpmZ29qSzVHZThuK3ZHZ2oySGVoeE9KRjZKRlNKOWoyNjRydjhKN25XckZGQVFBUWhyZEo3NDNyRjFVYzVLOUxyZEZ4ME1JRDNINkFVb1Zha3duUW9sV256WTYvdFBHK1VBdDF4d0pQNDc4WUdwLzBtaDZuTXorS3V1akloK0lpOEVnMjdEWFpFbEVWNkd0RURWT1Y0ZFF4cVR5bGRVa3VwdWZVR3lQVmpXVFpPakxnNFpEVnBxUExBQStZUEQ1bkRNL3lIYTROZUVncHh0Q2dzdlN4WS9RM3NOQi9ha213TzRPTVhHTGtHRGJvQ1NuWjc2bzJlZGQ4ZmxnOHVoaUU5UjR4Q25meEd3MWF5WjFsT1dGcWR1NGYzKzh6cW5DcnkiLCJtYWMiOiI4MjcyMGQzODVmMjg0MWYxZGQ5YjQ0NTQwYTFhMmVjMDg2MzNmYTVlZDRjZjBiNzU0MGUwOWM1ZTBmYzcxYjliIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 21:36:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160206782\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1907154371 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEaY03hL3MJLZRF1S32UHdqvejhjPtU3ScY6HLpE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907154371\", {\"maxDepth\":0})</script>\n"}}