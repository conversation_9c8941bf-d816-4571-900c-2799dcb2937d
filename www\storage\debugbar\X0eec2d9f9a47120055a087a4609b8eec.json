{"__meta": {"id": "X0eec2d9f9a47120055a087a4609b8eec", "datetime": "2025-06-07 04:17:30", "utime": **********.568599, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.304253, "end": **********.568631, "duration": 1.****************, "duration_str": "1.26s", "measures": [{"label": "Booting", "start": **********.304253, "relative_start": 0, "end": **********.402146, "relative_end": **********.402146, "duration": 1.****************, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.402168, "relative_start": 1.****************, "end": **********.568634, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01738, "accumulated_duration_str": "17.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4783118, "duration": 0.01483, "duration_str": "14.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.328}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.517707, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.328, "width_percent": 6.732}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5459611, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.06, "width_percent": 7.94}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269838044%7C5%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVldU42aUZIK2YzWktRbm83bit3cnc9PSIsInZhbHVlIjoiYXJoMEFrZFUrTWg4UWZLcGZWUXh5cXFpZFpKRVhBUHlLMWN2RE83U3B5cjI1bVQyNkhqM3V2UUpMRUcyQmlLQlVIY0FDNnlqVjZRZy8ybzV2SUE1TGdZaGU3amU5cHNTWW1COEpoY3JMN2Rad0k3VzA4dlZuRW85K2w4b2hJODZpWU9mV0U2RjJRY2xxUjdBcEowVzBubVJZb1ZBSVdraGMwc1U3dy9sRVMzSE9wSmJId0VRUCtnT2hINUNsYjFJSUJJZG5VTzNzWjZTZ0IwaTlRUUE4dkV6c0k5cEg5ZXpBdXY4REM4WHFJbDd4K3RGcFBDcFArWGgrdjRtUFMwQXFCb2hFL0krSmwxZ2NJMTM0OUlkV0VoVmtNck1INDUyS0pORGVMUGxIdkh2MmVidHNKdDdBLzl5bDh1b0FSOXEweWxOU3JCV0NIOHBLdjlsdDZWODRCeFN1Q1IwUWFoWTM1b0lwTldFTWNwWnpuK1VmWlMydnZxcmdaNktZZHg0bzVMcnBLVXVTUTlOeHZGZEtheVR4ZDNxdy93TFhsTjZ2OUMzN3UrQXFmWWllblRTMjFxZzAxbTZ2Q252YnNNWFp2dWprRVNjTnZCeHVyRlU5UGRmZDA3Y0xUSmxmandEN1l3UG9jVS9TME5iREUrNE5FczUxdVphRnE4OEFTSnciLCJtYWMiOiIyMGVkZGY4NjM1OGYxZTdjZDdlNmU0NDg1OWNkZjExOTJmM2U2Mjk5OTEwZDM4N2U4M2VlMjhkYzYzM2I5ZjM2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNEVmRQbDRqaFZSUDdCT3FwYnhnRXc9PSIsInZhbHVlIjoiTUhmbTVBZmdvRzZKTkVDekN1MG5SWWdlcFIwQ1ZzQXpSd0VzVjdmUkhrUlZiNExMeVJLeENtVVpXSCtoQWFIaTBuUDlqOUZxUkFjcVR3NXduVXBWTXpSMUwvZjZBVitXbjVSMTY5MUNiWWNuL1pGbVNCM0lJdThwNWYyQ011dlhkbFNsUDN1UDNnK01aRVlEVlJTVFJrbkZhVUF1QkFUR0JqY2xWWER6c3NjcERTMmtwWHZZZjhrdldLMFpvOUxKaEg4VjhRZHlWQUl3WTFKZjFPQ3lGeXVrYy8wd1d1czVMWW15eU56Q29KK3k3RU8vc1A2R1loU0xEY1JpQ08zeXNySzdnOTNaYVg4VlVpRnRJMlA1dnJaR09mZDVId3JjL1cwT3BVMUFUZFdBT2IwaXE1NXpDVklWaGhjYTlvU2ZIaUVQL2lXdHYwQlcwa2tmMmk1ODZqTlBHV2lqUVljTnJ6MEtwRTFkVHo0WmwrUTFPditqd3V3M0w1OVRCaXZYTHpFd2V5cTRFaktTRUFQOWFJUUpSRGIrVkJNVFd5dzhaNCttK25yMGV6RTV2czB5bGozcll2US9Cb0JkblZ5ZWIxVzE0WG5kRm9UOWVuOGdZYy9SdVRqcVVJZis5V2JyQmpRZDdGTkR3OGlLVnA1Wnc0eTZwdlZoZXZESFlMN1giLCJtYWMiOiJlNzIzNjdhZTQ5MTM1YTRiYjgyNTcyYmJjM2NiMjcwMTEzYjdhNWJjN2RiYWYxMDgwZmE1ZjhhODFiYTJhOTFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-723579180 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-723579180\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-588033186 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:17:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhueWNKZXhNY0RSMFQ1ZXhrK0dtU3c9PSIsInZhbHVlIjoicUp2RHRYYitmOG1YNEhwM0VVZTdzNTUxV3pMbHRsTVg4V0J3bmMwZStmSWFvQ01ScWRqdWRjMDQ2OENJZ2l3Q3grNGVMK0RSR3FnQllrVUlQbDRBQUlEM2UxUkxQK3ZIeDhoWFJkU3ZhSFhLeEg3TFpQT2NVSFpRTjZoRXU0ZFkxWFd6ZDdaUC9ERGxQdERla3p3TDFOWUJONGJVenk0d2FBNVQvSm50dVVnaTVCNjU1M1VVY2dkZWsyK1k2WHVjY3B4MDA0c0tSZFJFQjZWc3NYQmp1QURVa1h2dlJkVXJmZU0xYkxGRCtmK2pHS3JMUjh6QkpNU1lZU1pYRGJlS0FHdllPdGc3ZURaSjhlSGcwYVh2NFJXV1B1bm5yM1llUjNxYTUyWUI4NFZhaHRXbCsvRGtzWnloUkU2K3B0NDA3U0Y3djVsSFhremhBcHFKR2NiYkJSSk5ER1BBSzZCL2ZnY2FTWTZCUzRDbC92NnVXK2VZdmNTVTBMUUx4U0NuNUJuUkx4V3pFaTJRQmhJdzFxOTJ5U0hqeTg5ajdCSHczS3dST3NETzkvTlVvVUtITzViRnFEbjhKZlU1ZzdzT3NDVzcwaXpXUDNYWEpTNXZSWkRDelJhQVBXb0RVMHUvd3NtZDVReDZIYzVqckY2QVE0UDNBQkNPL0tieTNvWmgiLCJtYWMiOiIxOWYwOTI1ZTRjMzRkODZiOTA5ODhkNzQ4MDk4ZjhkMTcxNzkxM2YwOTdjZjMwMTE1YjIyNjkyNDZlODZmMDI1IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:17:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklJWVozS3BWSnBneXhZWVJLaWsyWEE9PSIsInZhbHVlIjoiVGZqTEx3bjRvdC9yTGd5b1Vzc0l4RnUybjhGNWVsUCtFcExNVWV6Tmk4Z1d6cnhrekZlY1gwTDQ2d0lNRlRlWkQxTVJyeU1FaXdDUy9VNVVaRjdORXY5d2xBK0VJNjdnOXFOeS9TTk00azd5cHNZYUdwL2JOTUozU2tvcDRTMHl1UWtnT0F6cmVOWXVLMHd0TVdCRVNkdVM0N2tPR01Pa3NTSkJ6VGQ1U29rb253ZER4MDhNS0xZTUxVdUdwY0h1Q3lma21Benl6dkRMU2dIQTJ4RVhtZklUTzhKNUl0TDE4SnlNM3FBcDJzaXlwQ1M4MEFyc0twTmhBeXllcDVDZFRUZmFqamF1dm0wcWNaR0NHRng0TnJFckNjOTRrZkZSdUFQNDFGQ0xlRHQ1SThxanAwd3EzRjg5ZWd3T0VEQ255MEtEUWp3ZFl2U0t2S2NVS29yS3ZWTEN0ejVaWmlXdHhqM3VxM1pKZXVqM3IzQzF6UFFCRE1NT2NUUHF3Vmtkd0g0eGc5VXF1VlNRbU00RG9MTkY0MXNsN2FkSURsN0cwTmpRTW9FenpobCtrWGtUOGM2UWJQbm8zMGJnQ25DT1dtQy8xaHB2R25pMERJdlVtTk1qQTh5Z2d5VkRnNHZEVnVmVU04ZHpGZ25mKzRNamU4dmFmZmdEVGVYL2h3MWUiLCJtYWMiOiIwODYxYTI2NDQwZTAyMGIxZmY3MDUyYTQzOTM2YWUxMjQyZGNlNzIwMGFiYTk5Mzk0MzY3M2E0Y2RjZTJhY2NiIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:17:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhueWNKZXhNY0RSMFQ1ZXhrK0dtU3c9PSIsInZhbHVlIjoicUp2RHRYYitmOG1YNEhwM0VVZTdzNTUxV3pMbHRsTVg4V0J3bmMwZStmSWFvQ01ScWRqdWRjMDQ2OENJZ2l3Q3grNGVMK0RSR3FnQllrVUlQbDRBQUlEM2UxUkxQK3ZIeDhoWFJkU3ZhSFhLeEg3TFpQT2NVSFpRTjZoRXU0ZFkxWFd6ZDdaUC9ERGxQdERla3p3TDFOWUJONGJVenk0d2FBNVQvSm50dVVnaTVCNjU1M1VVY2dkZWsyK1k2WHVjY3B4MDA0c0tSZFJFQjZWc3NYQmp1QURVa1h2dlJkVXJmZU0xYkxGRCtmK2pHS3JMUjh6QkpNU1lZU1pYRGJlS0FHdllPdGc3ZURaSjhlSGcwYVh2NFJXV1B1bm5yM1llUjNxYTUyWUI4NFZhaHRXbCsvRGtzWnloUkU2K3B0NDA3U0Y3djVsSFhremhBcHFKR2NiYkJSSk5ER1BBSzZCL2ZnY2FTWTZCUzRDbC92NnVXK2VZdmNTVTBMUUx4U0NuNUJuUkx4V3pFaTJRQmhJdzFxOTJ5U0hqeTg5ajdCSHczS3dST3NETzkvTlVvVUtITzViRnFEbjhKZlU1ZzdzT3NDVzcwaXpXUDNYWEpTNXZSWkRDelJhQVBXb0RVMHUvd3NtZDVReDZIYzVqckY2QVE0UDNBQkNPL0tieTNvWmgiLCJtYWMiOiIxOWYwOTI1ZTRjMzRkODZiOTA5ODhkNzQ4MDk4ZjhkMTcxNzkxM2YwOTdjZjMwMTE1YjIyNjkyNDZlODZmMDI1IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:17:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklJWVozS3BWSnBneXhZWVJLaWsyWEE9PSIsInZhbHVlIjoiVGZqTEx3bjRvdC9yTGd5b1Vzc0l4RnUybjhGNWVsUCtFcExNVWV6Tmk4Z1d6cnhrekZlY1gwTDQ2d0lNRlRlWkQxTVJyeU1FaXdDUy9VNVVaRjdORXY5d2xBK0VJNjdnOXFOeS9TTk00azd5cHNZYUdwL2JOTUozU2tvcDRTMHl1UWtnT0F6cmVOWXVLMHd0TVdCRVNkdVM0N2tPR01Pa3NTSkJ6VGQ1U29rb253ZER4MDhNS0xZTUxVdUdwY0h1Q3lma21Benl6dkRMU2dIQTJ4RVhtZklUTzhKNUl0TDE4SnlNM3FBcDJzaXlwQ1M4MEFyc0twTmhBeXllcDVDZFRUZmFqamF1dm0wcWNaR0NHRng0TnJFckNjOTRrZkZSdUFQNDFGQ0xlRHQ1SThxanAwd3EzRjg5ZWd3T0VEQ255MEtEUWp3ZFl2U0t2S2NVS29yS3ZWTEN0ejVaWmlXdHhqM3VxM1pKZXVqM3IzQzF6UFFCRE1NT2NUUHF3Vmtkd0g0eGc5VXF1VlNRbU00RG9MTkY0MXNsN2FkSURsN0cwTmpRTW9FenpobCtrWGtUOGM2UWJQbm8zMGJnQ25DT1dtQy8xaHB2R25pMERJdlVtTk1qQTh5Z2d5VkRnNHZEVnVmVU04ZHpGZ25mKzRNamU4dmFmZmdEVGVYL2h3MWUiLCJtYWMiOiIwODYxYTI2NDQwZTAyMGIxZmY3MDUyYTQzOTM2YWUxMjQyZGNlNzIwMGFiYTk5Mzk0MzY3M2E0Y2RjZTJhY2NiIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:17:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-588033186\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1500079185 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500079185\", {\"maxDepth\":0})</script>\n"}}