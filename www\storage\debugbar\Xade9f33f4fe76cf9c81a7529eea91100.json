{"__meta": {"id": "Xade9f33f4fe76cf9c81a7529eea91100", "datetime": "2025-06-06 20:35:29", "utime": **********.296643, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242127.857252, "end": **********.296673, "duration": 1.4394211769104004, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1749242127.857252, "relative_start": 0, "end": **********.096088, "relative_end": **********.096088, "duration": 1.2388360500335693, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.096122, "relative_start": 1.2388701438903809, "end": **********.296676, "relative_end": 2.86102294921875e-06, "duration": 0.20055389404296875, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44858568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.029999999999999995, "accumulated_duration_str": "30ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.1978228, "duration": 0.02763, "duration_str": "27.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.1}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.247658, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.1, "width_percent": 3.233}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.276259, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 95.333, "width_percent": 4.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-703727989 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-703727989\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-622530053 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-622530053\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-997344145 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-997344145\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-25883539 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i6cij5%7C1749242076252%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRoRkhrN2RuWDVhZTAwc293eGlCcWc9PSIsInZhbHVlIjoiRmhQUnBPK2dyK2NzUVZoSlRsZDlHdHpTS3VQd3h5d0JlYmExaXNvZmFXMWY2TlBWOFE4K084OWkvKzRTOGVGeE1rSW5LcE1vNkhIKzl3MVhMdEFtUnRnK2l4TVBybGFPbUZpQWFDM0NJL2htbERXR1ltdHoxOWdtNjU4MFJZakFiODZOSzhpdklTL3Yrb3pJQlI4Mkhnb0t6TFptRW9NcVNTakFpZG1ER2NRcjkvVE1kc2dlWS96dGNGOU5tUktZNEdJNnZwNFQ2TkdxTDA0RE5wU2k5UWVGbWw0eHdTTVN5QkZjcDJRc1orZGJvYWU5UGR4aXRPdFlTdTdtaHkwRFBvODBNMC9IaHlWV1M2ZGpGaUtmM3BuczM0dE9kalBGb1lta1hGdHFhV0k2QzY4bE9LbDVKVXlZd3JOdWV4ZXhYTWFENFJSdU81TFN6UmJVQ2F6NUZvSi95ZVhNekQwTUN2a3hiVllGUXNlbVd4OGV6ZklUa3pSMXhiSi9iSVBnYzl4Z1l6N2FtRnVlTERmOTJmZm43aEVJTEFhY3NrVmFvaGhiV0dvOUsvRkpNYi83a3RTNkc4cEZBS3pCOHlOMXNPMWw1TmpSdTBMZ1NlQW90U2FLc24vTWVKOHpmR2MzeFZlaEZ1cnl1MGtxRHhMaGlhU3puWHkyZzNxdEkwbTMiLCJtYWMiOiIzN2RlNmRhODY5MTY5NTg0ZmQ0NTNhM2JlOWQ1NjQyMGRlOGIyNTllOWI5NjNkM2ViNDRiYmI5NWJjNWE1Y2VhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJwdVlWa1dRYkozaVFhTVZFTjRvYXc9PSIsInZhbHVlIjoiT0VVSVNla0VGbnBkQUtIZ0VKNlVJVWtZL1pXc1lDWVdxTlZNSTAraVlyei9ZeTNUeU1BYTZoVFJiTHZlNjNoMzFEZERMSVlPUU9VaHJOMUsxVTV1eVhybVZwc2QwbUVnUWRRUVh0UDRQREhuMG1XOWRCMWh3cUdZTlFWcTJRa0J4MkpMcWJKSi9yRmhuc3pSZ0F3RDVwck5zVVBDK0h3U2wrOGtlVWM3ZmpUNkZTTWJVa1F2Z29NOEhZNUVoNHJjL3hydzdKZ29tTHVQbEExWFlYTkNnNFFyZFZtTkRrWFdGR0J6VnlLZHpDWngzSS9uWEF1eHMrLytIb1pxdGRTRnk2ZUFZa2s3ZnJIMHpRVnhVdnNEekE3cTRwOUwzNkxVWVVvTFpmSnBXT0N2RWpBK0ptSGN5N2xsYkRDb0REQWIybWxJM0szVWZ4cWZjMzM2dXdiSHYzMTdjWkVYUE85RC9JcnY2RW1nUWZFdXlGT241V21qK1F0emRyVXJ0RUg0ODdSNmhNSFoxQ0llaEJNdGRyNDMralZQZXNmU2F3RGNiUTY4YkpZaEJ4YkVkVndENmI5VmZaa2FsTE9MRkZMTkJuRUFBZFBrNm14RjlXTEZHL3BQSzlNMThUYndOWXZPeWhhRDhLQlBOTzBTZXIzRVBNQXlXRmQrOEZpeVpKc3oiLCJtYWMiOiIzZWJkYWMzODkzMzA3NGQzZTdhNjBkMWRhY2VkZjAxNWMxYWQ0NTNjYmExMDQyOTNhYWQxODgzNjg0OTY4OGUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25883539\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1429239041 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7vCprTGia0kRtX3d143VCVgcgruwl5sNb03RJByE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429239041\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1449771346 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:35:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9GTHZGN20rVDN2c3oyaTRINnpIdXc9PSIsInZhbHVlIjoibjcyNCtYZ0tPMzJPRkxhU0RsVmtTWE9mVWwrZ01paS9NdWcvN2YwNDBLWWM1RTZGaExPTlFrMURCOCtGRmhteTRZWG9iYW9FdzRoQWN4cHFJckFQa3BtL0xZMmc0OHFZSHNVejF1QWJYZjVOQWhNc2w1ZDg3ZFJTT0k4KzQvcHpwakltQlBjOEtzTWtMLzBERWxza3ZqZXc0dzhKMEJ1OXdMNVdpZjF4UU8vUlNHd3F0emZvZDYyT21CWG5KakJ5VnZFbXV2ZS9CMUJna3gyNWxobXNQWU5TeUNtaFd6Q0NHcHRlZUY5VWJzYjJuNmFROVZHTXkrRlVTTWxndVM3UjVKcDlPeTM3MFlxTGMreWlCb2REYTZnR1BCOThxM1grVFFEa3lYYjYvTmJNbUhoN1dBbnNIdFQyTVhYT0Q4QjZhNU0rTHNLOHYzTkg4RCtuUGx1ZkNyV3NnV3R2SlBnbzBRSk4zK25Rdm1rMFBTaWxTdUVubm1uUzJuMUVULzB0NG5tWjhaa3ZGOSs0V09lU1Y5NnpQQzZ6TWVJV1BUZEp3NURRV1dLRW0waHhkTzVNNnFrdzErczR2NmVyWUx6SFBKUEpHWldpN2JCb0FITGYvMjc3WVo3MFNaZk9qV3h5U0I3OXo5UitIdlJSamRLQzVkQnMwNU1SYi9aaGhnNm4iLCJtYWMiOiIzYmYyZGY5Mjc3YjA1MDY2NzlmYmQ3YjFjOWZjODM2NDMyNTk1MDczYTZiNzIwYTk3MDA5MmQxMjk4OWY1ZTMyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:35:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkVXV3R5N1JqUnpzM0pUdkxVUEVLTnc9PSIsInZhbHVlIjoiTVl2a0pEd1JZQjJtdXUrcHhRd2czaE55ZTVxa1Zwc3gzMjVEb2FSN09zUkhCOWVkMitLZUJFRjhEYVhmZXdzejk0NkxDMHh1dlFMOCtjWTBuelllU1ZxNGUza3JWdFhJd1o1ZDZlSDI2OG9MOHJzYzZKaUJQUy9VN1NPWlc2dWxjYWw0ekZSVXpVYXVQcktLN3FqenRXbXhRM2dLdXlkWFhzSmx2ai9aeGpHcjA4R0paNmxIZk5wekd5Uit5TUd6Q2JyMjVld3RPTy9BR3RTdUdrdmlQdWR6RHQ2Z012RzNWZXJoVmtpMkhmNmxJTGRHUTUwVUx1U3c1ODhPb0d3ZjFQS3IxTllHZHd4akpTdi9lY3lNaTNxd2xsZnI5R051T0V1Rk5NcjF6Nm05eWlxczdLNE9RbUlLcnVKZGE1aUlmM1FDR3U0UGdtYUlxd1NlWGZhNDhoSHowWWF2SGxrNEVBZThNa1JVZjhFZzUwV09uY0VOYzFIcmRtaGVlMVVWVmpWbG1lM0lFQ0lBRWVIYjhISkpnMHhrUEp5SXZraVdiMTJCbW5wZzM2SFl5TXV2bGRuaWlEUGRkOUVTTDN3WmFnTk1xTkhWbnIxNVkrci9qeVc3N0NmaEp3SE1rMlM5UjVSalMwanc3V2dGSTB4UHdOOFA1NVczT2xDakc2dUgiLCJtYWMiOiJhOGU0MmFjZGZlYmFiZDA1Y2NlMTBkNGRiMzJkMzdkMDVhMDk3NjVjMjk3MmE1N2E4NGVjZGJmZGM5NjcyNTcyIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:35:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9GTHZGN20rVDN2c3oyaTRINnpIdXc9PSIsInZhbHVlIjoibjcyNCtYZ0tPMzJPRkxhU0RsVmtTWE9mVWwrZ01paS9NdWcvN2YwNDBLWWM1RTZGaExPTlFrMURCOCtGRmhteTRZWG9iYW9FdzRoQWN4cHFJckFQa3BtL0xZMmc0OHFZSHNVejF1QWJYZjVOQWhNc2w1ZDg3ZFJTT0k4KzQvcHpwakltQlBjOEtzTWtMLzBERWxza3ZqZXc0dzhKMEJ1OXdMNVdpZjF4UU8vUlNHd3F0emZvZDYyT21CWG5KakJ5VnZFbXV2ZS9CMUJna3gyNWxobXNQWU5TeUNtaFd6Q0NHcHRlZUY5VWJzYjJuNmFROVZHTXkrRlVTTWxndVM3UjVKcDlPeTM3MFlxTGMreWlCb2REYTZnR1BCOThxM1grVFFEa3lYYjYvTmJNbUhoN1dBbnNIdFQyTVhYT0Q4QjZhNU0rTHNLOHYzTkg4RCtuUGx1ZkNyV3NnV3R2SlBnbzBRSk4zK25Rdm1rMFBTaWxTdUVubm1uUzJuMUVULzB0NG5tWjhaa3ZGOSs0V09lU1Y5NnpQQzZ6TWVJV1BUZEp3NURRV1dLRW0waHhkTzVNNnFrdzErczR2NmVyWUx6SFBKUEpHWldpN2JCb0FITGYvMjc3WVo3MFNaZk9qV3h5U0I3OXo5UitIdlJSamRLQzVkQnMwNU1SYi9aaGhnNm4iLCJtYWMiOiIzYmYyZGY5Mjc3YjA1MDY2NzlmYmQ3YjFjOWZjODM2NDMyNTk1MDczYTZiNzIwYTk3MDA5MmQxMjk4OWY1ZTMyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:35:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkVXV3R5N1JqUnpzM0pUdkxVUEVLTnc9PSIsInZhbHVlIjoiTVl2a0pEd1JZQjJtdXUrcHhRd2czaE55ZTVxa1Zwc3gzMjVEb2FSN09zUkhCOWVkMitLZUJFRjhEYVhmZXdzejk0NkxDMHh1dlFMOCtjWTBuelllU1ZxNGUza3JWdFhJd1o1ZDZlSDI2OG9MOHJzYzZKaUJQUy9VN1NPWlc2dWxjYWw0ekZSVXpVYXVQcktLN3FqenRXbXhRM2dLdXlkWFhzSmx2ai9aeGpHcjA4R0paNmxIZk5wekd5Uit5TUd6Q2JyMjVld3RPTy9BR3RTdUdrdmlQdWR6RHQ2Z012RzNWZXJoVmtpMkhmNmxJTGRHUTUwVUx1U3c1ODhPb0d3ZjFQS3IxTllHZHd4akpTdi9lY3lNaTNxd2xsZnI5R051T0V1Rk5NcjF6Nm05eWlxczdLNE9RbUlLcnVKZGE1aUlmM1FDR3U0UGdtYUlxd1NlWGZhNDhoSHowWWF2SGxrNEVBZThNa1JVZjhFZzUwV09uY0VOYzFIcmRtaGVlMVVWVmpWbG1lM0lFQ0lBRWVIYjhISkpnMHhrUEp5SXZraVdiMTJCbW5wZzM2SFl5TXV2bGRuaWlEUGRkOUVTTDN3WmFnTk1xTkhWbnIxNVkrci9qeVc3N0NmaEp3SE1rMlM5UjVSalMwanc3V2dGSTB4UHdOOFA1NVczT2xDakc2dUgiLCJtYWMiOiJhOGU0MmFjZGZlYmFiZDA1Y2NlMTBkNGRiMzJkMzdkMDVhMDk3NjVjMjk3MmE1N2E4NGVjZGJmZGM5NjcyNTcyIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:35:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449771346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1826516903 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BnSoEJ9XuSDgieWeVm9IQmSGN2fuYJgOWbZ4Hn3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826516903\", {\"maxDepth\":0})</script>\n"}}