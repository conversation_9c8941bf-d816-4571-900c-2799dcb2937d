{"__meta": {"id": "Xa8d3aa76d33d9ca0ab1a521b25527e02", "datetime": "2025-06-06 20:44:31", "utime": **********.512057, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749242669.653929, "end": **********.512103, "duration": 1.8581740856170654, "duration_str": "1.86s", "measures": [{"label": "Booting", "start": 1749242669.653929, "relative_start": 0, "end": **********.234505, "relative_end": **********.234505, "duration": 1.580575942993164, "duration_str": "1.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.234525, "relative_start": 1.5805959701538086, "end": **********.512108, "relative_end": 5.0067901611328125e-06, "duration": 0.27758312225341797, "duration_str": "278ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44767504, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.009430000000000001, "accumulated_duration_str": "9.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3405972, "duration": 0.00536, "duration_str": "5.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 56.84}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.441874, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 56.84, "width_percent": 9.544}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4518929, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "ty", "start_percent": 66.384, "width_percent": 13.256}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.481857, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.639, "width_percent": 20.361}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-790525174 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">_clck=159toa%7C2%7Cfwj%7C0%7C1983; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; laravel_session=eyJpdiI6Ik1DVzgzaVM3SFpra2RiV0dWWkdHeEE9PSIsInZhbHVlIjoiSW03UWU1TzJPK3hTWTIxQXVrbnhvVXF6VlBpU2lkdGhvUTR1VmxDay9EcEIzL0pxTXIzamsxZ1RSMU9GemJKVU94YVd3bXRsdGFSTTJmNWZyLzVOcjJFQ3ZoVVh2Und3OUJGSmN2dWo4VnpwNS9Xei9Sc2hUQmxKWGVSMC9rVWRtMlY0MUxFcVYydUQ5RzJ0SSt6K3krbHh0TDVUV2VTOFF1TDNZRnBub01GaXIrSlM0ODJaS3BKNkdVWWxMVTMyakJiTm5Cb3J4TFI3VWV0U1IwNno3R2g1bjR5QUg4Q0FXcHIzVVAwdzFGNnZaTXZTY2RDY2R5L1hOQ1JSdzc3cUlOdW1iR2NQNEpvd21XeDNXVFJ4MklMWmJBUGtIQzJBRnc2LzVFbWNNOGZ5QkU4bkJWS1Z2aEtJY3ZSd1plclQrTXk0VXc3Y0JpWHNXUkFrUWIvcUk5M3FtNEdHYUNIRjJnY0dBbmlaNUVJdWtER3RJam5ic1NsblhPUnhjdUE0aXhOdktSYitUWVRhdTNoNmtOcTU4dnhaUGxQYTFQY2RGSFZ2aHJQTDlyTVUzZzdIN20yM1dPOHA5dnN6Wi9hdVhtMlZJT0t5SW9zSGZtV1NWNWRuR08yVDFVL2JxM1pFeDloeTlXTXhicGRGSjNlOGhGb0hTcE1QSWhOSmlqSTAiLCJtYWMiOiI2ODE0NWI5ZGVjZWNjMjI0YTFlYWVjMjk4OGFiMjE3MWI3YzZkODY2NDhjOGQ1NGRiNTgzYzFkYzY2M2Q2NDBkIiwidGFnIjoiIn0%3D; _clsk=1i6cij5%7C1749242662369%7C26%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVEL0xvRWRZSlJjMXRKVmdDWUQ3THc9PSIsInZhbHVlIjoiQmgzRUtsaHJtRHV1K0xYUitiQUUxM3AvWjA4S3ZwVjZFT1NOSkVjVDdhWUtGcUhqbzlPUlJ0QTFDcitzUWZrclpoOHE0Q2dzMXg5dzQ0Wjk2N0lReVRYNDdxWVc5dGFESXAwbzRuZ2U3b2hUSU1kZ1VpVG9KK1dPNXZCOEZWRDAyQ294Y051UUhVZ2ZkS2JOa055czRUM0orS1oxeG9EL2JXSmx3QTM4MmlKSVZqbUdNdG1yUjJ5TDhjRkZXa04rWWRuT2NjU09rS2JPUG1EdFdSUHZaNXBYTGkycWxLdGZLL0haM3ppWUVEZXAxUWtjaFlNK1hmMFllZjFLVGw4RlVCTE1jaDVsazRTOGk0Y2VPcVh5SEdTZnJnK1psWHlXQ1ErS0ZpdFoyZXlmTXU2NlN3ZWhmV3IzcEpjdlZPV0s1TkxVVFJvc0tMQU1GTThHMFFaU0xhVkpKQWJjbDFhaVEvaGRpaUN5Y2lNS25BYk9ubkFSS2VyTEFCdzVtQXBVMzJ6Ui9sZlpOdTkyNDcvUldBdTVDVVZvWnd4b0d4WWFBZVFNYWZrSkp6N1FJeXk4dFpnVWtKTFN0S0lvR2xuMmNwUTd3UTcvbHJJbHBicnZYUUxlalVId3JjSW5valRVMUZ4Y08wRWNIR09xRTF0M1hDZ1ZJUjF6Q0VReXJ4WjEiLCJtYWMiOiI2OGI1NDdjNzE0M2IyNGNhMjg2YmRlZDJiNGJkOWFhZDA3MWMzMDA1MThiNDY0ODY0NDE0NjU3NzRhMzYyM2QwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImszWjJFOW82NXJCYWhUK0tKVnJRUFE9PSIsInZhbHVlIjoiWG1sdldRZVJ3OW4wTmZKVlNvenpnRmdCNm9vbU5PYzhjS0ZrVFlpdmhtZEVtTjY1MFZqdGovZUp3OFFieDNzcit0K2haelV3Tmh6MnN4c1FXVWtvZE4zc0kwUGRwRm9EQ3ZNa1JTQ0MvelU2VGJac2owTzJScjRyV3ExTjduZ3lmTThEYStWR2JUNlFPd0RHU2dFUlFOOXByU0FMSE1YZVRxSGdXMS82dDdoU1FHd20wditsbW9lUXkvd2V0cFdISHMrcUpQbUFPYlgya2EyeHpUdGRXSHBpOTdwdnZHcmlkNzFrVlAvc2Nsck1wcStmM1d0R01QNWc4RXcxVXJKWFBXNGovUHRmVlhVWnBaZGFsL3NtMWRBaWlpc3RHNzZBN1plRkJiNE1VQUxqRVpjbzJlM1lwc2lyeDhZVFE3V1VOdTVyNGQ2OHlySld6enMzelBxZzJSb1laeXNaRjFpaHhwYk43SjA3M0RPK0g3dU5UN01sbXk5WU1naDNyZ1k5Q0tzZHJ4T1dYNEluSWtSNkZFWk1BVHd2aUVWZjVNZVhwNmtTcklCeFZKNlJEYWF3UURvczVSOVJlNGtWTVRVMjR1RmkwZ1FJb3hJamZqMGlER2JhK2VveWk5cHkyRVJHT0Y3ZE9VVW1kMlBhMDRaYmtheWFLemovUGFqTXZJRDIiLCJtYWMiOiJjOTM5MjU1Zjg1ZTE3MTIxMjY2NmRkYmJhZTRkOTM2OTZhNjM0YzBlMjA0YTFjZDg4MTFlMmU2ODNhNzBkNWZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790525174\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1508155561 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">19o8GZJlmCRhkVQqQRnrzKTy9LwoYzgyFnAzuAOO</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6Wt4M4bI8NbCUmFc1THCmhEuEOoDYpn2bPL8rDvY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508155561\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1286788988 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 06 Jun 2025 20:44:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxOK0wzdUlaQjdRUXlSclpmYjdJa3c9PSIsInZhbHVlIjoiZURBL3NoZFc1VjA3K1kwekdGWWRmcis4NVBGMENqc3pHQkd6anRscUg3ZmxvUkViMGlicW9MdTJLY1I4NGZDOUprdXRuQ080ZDFwdmhmM3dXeW16dm9FVXdjUmZsSnM1L2FwNnk2ZEIxSitZOExmK1E1Q3RSS01MWXY4QVl0bVpZRzl2aWFnTkt6aHBZRU5YQTB4eGFTU1ZTZ2RhM3NIZGJEU2pSZnNPdm9xZmxuUmhicTg1dDRPMDhzZkpaQUMrZUhiUDVuTDFWM3RxaXJmNWZWakJUV1FwL0JlUzBtSzVDRzllMVR5QlpaSXEvbGxuSUNINy9MYUljSnQ1b3pGT2JWTWdja3lzMXhrMzJ5LzhRS3pKeUVQY1hRM29ZT0RJQTl0QW1MK1NQZzZlQVRXdldoMVZxeDg2NTF2S3FpaWgrUTBBeUJLc1NFMlY2Si9HQWRET2JudFFPQU1Rb2V2K1BWQmZNM3YyQ1FYWklqL0xJMytJTlloSnFkZThzaU1XendzN2dyV1hJY2ZYbUlncXl6OVRheEg2QVVEMWRKazN2WVZ2MitjOTcrQnRtZTl4RXAvb2I1YTdMdFZaKzZiQmlhQ0NkS3ZvRGtwKzZyUVlvbWtBUmI2aEl5KzVmNmpCSkpPa3BqNEhqaDJOWE9hUmxTUUdWaGtUQ1VmdUsxT3AiLCJtYWMiOiJjNWQ3ZTdmNDgzYzBjYjlhMTU3MzJjNjMwNDY1NGY0MWI4YmNhOTBmNTFmZjRiMGMzNDRiNGEwNmFlNzg2MmY3IiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:44:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJtWUZBQjZxaHdZY09oQWEyU0VIRnc9PSIsInZhbHVlIjoiN05DWFBMaS91VUNzWkFaNGpkM0pnZHlQM2pzTlFkelNjVGdTcEZ2QWpGdnRjK25XM2Y0cXIwMGtTa3lKMldablpUM1p4L29lbC9Qdi9ScTkzUC95RndCT0FnS2pWU2pmWVdwQTFaMTd0SjlQQTRsZk1TUG5IcG92MWZ3V0xOYXJEbmxyR2poRXJXRHJNYUFLMy93bG5HT0NKVktMNmd5VVpyT3RVTEtXNFBDdFRzM0dpS2lHSEQwV3VEd0V5NGgxVUg2bFNoZFNWaHlOTWpKYkcwcHJxZklMQzdpQk0vOE5VbmlmM0pLQkRZdllrd3pjTmdoaVRXSU9pRU5hRDBCL1BWQmpMc09USE9oRUtkNHdXQWhkd0w0T1NwT0I1bkx0UW5md1IvdHhCREphYXhKY2RmZkN3Y2c5b3JnWTBXL1RaSHg2VGFUUWIrbWtLZUpkWUVNSzJHWFBCUncwZjhxMG9naXBGYVcwM1hhUmhUZEJGN3ZJNmxhMHFvYnByV3hIVnFDL3g1T0lOS3FCaW1qdUQwVGx0YSthY2l0ZEZRSFQ5TnlhbE9BVC9kRlN5NXc1bTc3bFJzNU5URmJ6cVFyRjRQVzUySGNLYjl2b3lGWEJDSW4vSEVaSUhqV05sYmNUREF4VGh1cG5iUlF5cTRXeFdjWTh0cVZLRkZPeTlkSC8iLCJtYWMiOiIxNjBjMWI0YjQ1ZmQ5MTQzOGI3ZmFiZDAwNjI0NDVjZDJjODBiNjI4MThlNGUyNDQ3OWFhN2ZiZmFiN2VjYTBiIiwidGFnIjoiIn0%3D; expires=Fri, 06 Jun 2025 22:44:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxOK0wzdUlaQjdRUXlSclpmYjdJa3c9PSIsInZhbHVlIjoiZURBL3NoZFc1VjA3K1kwekdGWWRmcis4NVBGMENqc3pHQkd6anRscUg3ZmxvUkViMGlicW9MdTJLY1I4NGZDOUprdXRuQ080ZDFwdmhmM3dXeW16dm9FVXdjUmZsSnM1L2FwNnk2ZEIxSitZOExmK1E1Q3RSS01MWXY4QVl0bVpZRzl2aWFnTkt6aHBZRU5YQTB4eGFTU1ZTZ2RhM3NIZGJEU2pSZnNPdm9xZmxuUmhicTg1dDRPMDhzZkpaQUMrZUhiUDVuTDFWM3RxaXJmNWZWakJUV1FwL0JlUzBtSzVDRzllMVR5QlpaSXEvbGxuSUNINy9MYUljSnQ1b3pGT2JWTWdja3lzMXhrMzJ5LzhRS3pKeUVQY1hRM29ZT0RJQTl0QW1MK1NQZzZlQVRXdldoMVZxeDg2NTF2S3FpaWgrUTBBeUJLc1NFMlY2Si9HQWRET2JudFFPQU1Rb2V2K1BWQmZNM3YyQ1FYWklqL0xJMytJTlloSnFkZThzaU1XendzN2dyV1hJY2ZYbUlncXl6OVRheEg2QVVEMWRKazN2WVZ2MitjOTcrQnRtZTl4RXAvb2I1YTdMdFZaKzZiQmlhQ0NkS3ZvRGtwKzZyUVlvbWtBUmI2aEl5KzVmNmpCSkpPa3BqNEhqaDJOWE9hUmxTUUdWaGtUQ1VmdUsxT3AiLCJtYWMiOiJjNWQ3ZTdmNDgzYzBjYjlhMTU3MzJjNjMwNDY1NGY0MWI4YmNhOTBmNTFmZjRiMGMzNDRiNGEwNmFlNzg2MmY3IiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:44:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJtWUZBQjZxaHdZY09oQWEyU0VIRnc9PSIsInZhbHVlIjoiN05DWFBMaS91VUNzWkFaNGpkM0pnZHlQM2pzTlFkelNjVGdTcEZ2QWpGdnRjK25XM2Y0cXIwMGtTa3lKMldablpUM1p4L29lbC9Qdi9ScTkzUC95RndCT0FnS2pWU2pmWVdwQTFaMTd0SjlQQTRsZk1TUG5IcG92MWZ3V0xOYXJEbmxyR2poRXJXRHJNYUFLMy93bG5HT0NKVktMNmd5VVpyT3RVTEtXNFBDdFRzM0dpS2lHSEQwV3VEd0V5NGgxVUg2bFNoZFNWaHlOTWpKYkcwcHJxZklMQzdpQk0vOE5VbmlmM0pLQkRZdllrd3pjTmdoaVRXSU9pRU5hRDBCL1BWQmpMc09USE9oRUtkNHdXQWhkd0w0T1NwT0I1bkx0UW5md1IvdHhCREphYXhKY2RmZkN3Y2c5b3JnWTBXL1RaSHg2VGFUUWIrbWtLZUpkWUVNSzJHWFBCUncwZjhxMG9naXBGYVcwM1hhUmhUZEJGN3ZJNmxhMHFvYnByV3hIVnFDL3g1T0lOS3FCaW1qdUQwVGx0YSthY2l0ZEZRSFQ5TnlhbE9BVC9kRlN5NXc1bTc3bFJzNU5URmJ6cVFyRjRQVzUySGNLYjl2b3lGWEJDSW4vSEVaSUhqV05sYmNUREF4VGh1cG5iUlF5cTRXeFdjWTh0cVZLRkZPeTlkSC8iLCJtYWMiOiIxNjBjMWI0YjQ1ZmQ5MTQzOGI3ZmFiZDAwNjI0NDVjZDJjODBiNjI4MThlNGUyNDQ3OWFhN2ZiZmFiN2VjYTBiIiwidGFnIjoiIn0%3D; expires=Fri, 06-Jun-2025 22:44:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286788988\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">q73Zc8mbNWsXBIeyA5lJWtCS0NkrRckF7ABxI1nF</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}