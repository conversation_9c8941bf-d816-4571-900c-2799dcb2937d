{"__meta": {"id": "X9722996b287131a02d75d9038a0f9721", "datetime": "2025-06-07 04:18:40", "utime": **********.254009, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749269919.074716, "end": **********.254041, "duration": 1.1793248653411865, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1749269919.074716, "relative_start": 0, "end": **********.132941, "relative_end": **********.132941, "duration": 1.0582249164581299, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.13296, "relative_start": 1.058243989944458, "end": **********.254045, "relative_end": 4.0531158447265625e-06, "duration": 0.12108492851257324, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43525344, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01191, "accumulated_duration_str": "11.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2065132, "duration": 0.01099, "duration_str": "10.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.275}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.226949, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 92.275, "width_percent": 7.725}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1355769800 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1355769800\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1465227190 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwk%7C0%7C1960; _clsk=hqqi3x%7C1749269849313%7C6%7C1%7Cs.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1Uam9jQ0dabUlOOHlSajNpMWxrTVE9PSIsInZhbHVlIjoiT1h1MmVtTUVQdGh5MS9ndUszazZ6MWNxRWNiaU9VZWlQUWI4cGIyRC9HSWR1YndVTndNdnlhbHRDaWMzNHRZdFlZOVpWamFkZEZyVGlRODFUU1oyaUJSQWw4Zys3ZTBPVXBJV0xnVThDZm1kUU8wWHVTcU9Qa0ZVd0xMVldwZTNlMkppQzRscWplUnM0K1YwaWhBNDNkd3BUb1d5Vkdvcnd2TFlQdjltV1RLVEUzbCtWVEdUUDlQak5tdmtMUFljSE9OelBROGFvdk9LbktVWDR5RkQ0bi9CVS95MTllNHZ2a3o4VFZCUDJvSVNEZzVZbWZBWk1KcEViaHk2UmdUYlk3ZkJCblRaL3NqZzVFV3Y1UjFET1dLbTNrUWdOY1NQbGlqdk5ZMGt4UUpTNU1BWUtLaGw0cDAwQXkzdlQvM280VTlmWXlNOUJobXFlOTBQcTc3SU1zNkZBVEtFdkZycEo4cHFITk5HTWpqUG5OTEIrWWtBZTk5ZlVKWG9rc2FXdzAxcUwxS2JHN1UzYVQ1cXlXaDJCVGpQY2dNSit3YllhWDFDY1RSQUhFS28xMEtuVWtQdERsS01mOVVSZFpIc1JDTHhoc00zbGZKT0JkaFcrUm95S2V2WCtwS1ozZ3VoamxZbG5nSG1oNEpXMTcya3c3RElyL0thZk9KMzg0c0QiLCJtYWMiOiIwNjk4OGE5NmY4NmIzMzQxNThjZWJjMmZiMDljNjA2ODUzZjg2MDU5YjhmZjFmNmM0NWU1MmMwNWRiYzA3MGQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdsSURxS1hRN01HR3NRL2psc3Bsd0E9PSIsInZhbHVlIjoiVnhZdnZROTQ4NVg2bXpFV1Jkc0tXODdYS2dObDJPTno0UnM3cU1CbjhtRkxEZlp4cjFwUGl4RmpuWFpwQ3lzN3lNNmlLcDFSSE1mQkdEOFkyL3NBMTd2ai9Id0lPMXdqRHcxdVJqeXZtZld1Z0psRXZUTnRWUnhnakl3T01UeGZ0WDNMRmg0alhoWjQ2SmpGSm15VWJwMXRUWjhPTVFBN3VvcDdTb2phb1FXVW16Uk9LN29uZStzYU56R3lvcjlnaVQ5eUgvTlJtYnExQzdOQjRVZXFZV0VaQ3UwNEEyUEd5ZHVsTE5xVUdBdDJ0MUFIYjdpMUFIR3dZRndXRjVBallMSFJ6WTBFeVhrVlNHdkU5Q3ZGVVlaa1dBQzNNemhDYS9UcFBMa09ZbjNuWGRJaVB5enRsSTdWbjVvbmdCTEVDaUw5Y0ZPV24yM2RsQTk0bkVuQVhraTQxL25jT2ZKci9qRDdVUXlRbEhsK28wNnMwMFA4RkVYVGFQSVlHY3N5aXpJU05IZE1PUVZzUGc4SXA5WnJYdjBYMEMzRWZaR2tSWnY5QSs2T0VaZXprYXNrMkVEaWJlc0QzWmJJdzQvVk45Sms1WjJZd3JtaGhnb1pNei92NVJkY1p5VkdmemZKQTR3SU9JV25McDJoRXJKelN2eDVuait5YzlWRTE3V2wiLCJtYWMiOiJkMmJjNzEwN2VkMjE2ODIyMGVkMTg4YmRhNzlhZjhjYTU3NTM1ZGExNmNhYWEwOGE2ZjI4ZmYzYjUwMWJlOWZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465227190\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-602236513 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2k4lY4iXgd0GFT4dN2bOkC0QecMRXGDWfBm73q9q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602236513\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2041729283 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 07 Jun 2025 04:18:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpiN0svMERqNjJ2ZE9wWmZzNlhkVmc9PSIsInZhbHVlIjoiQXNJbVlpa3YrMkhaZU4wUU1wTGRUTWV1eWtzMkxmd1QzVzg5STdQNk13OGVEVnQzVkdJSmhjRzZ6NTYxNytUM21EVWhvdHh5dGRTblZOSzUyZHEwNDB4ckF5NjlDWUlRUFNXMWdmVUZnWjFNZDg1WGxCcG1OcWF6S0R6YWJXSnNhdHpRblA2UVp6VTFnYWUyMDdUZ0dvRitJUzVMMW1ibXJid293S3NZb0FQbGo2dVFUVUNjMnRCZG56RXVMNG85dDZQTXFrZ3YrWkhBcTI4VW13cWNiRnhad0o0Sm1PbmZxUVEyanVmcTYrWTNsOFNvSjFjT1VVa0NGTGRCT2lFTWR3YnVpQlBkcXRnVVdrT1NpaGpqdWpwakQzNUlBSjN0RHhGREQzMmprTTJ1bkY0TVNBWktNZnhmb25vcUlpOHhtOVZqMlJGclpSL1hESC9ZUXBZV2xrTlVqM0NFKzZsdFVnbGNYQWEvVGxhbnUwMTNjc1hNVzFRYjFjZ0hKU1ZVZ1gyeVYwdzZqUnpNV3dwZGIrbmdURlBSOHc3R3U4YUUvWDBPM0tNVTVzUXpQRXAvSysxSTRPTzcxSUYyWWp1NGZvMWZmWDI0R0tSc1BvTXBnVWtVVitjaWxpUnlGL2VYZXpLTU1wanM1SFNRSE8rYURLbXV1TTJQZjU3R21WZGkiLCJtYWMiOiI2M2E0MjEyOTM4YjM3MTk2ZWNiNGY2N2QzM2U5NDNkNDkwY2Y1OGI0MjcwOTc4NWE0MDRjMGU0MDRmMDczOGVkIiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:18:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1TZlNsdWx0TU54d3AvTFJIaXlFM1E9PSIsInZhbHVlIjoiNm5DVGZzcmUvMTFuZ2VGWmFCZElCa2dWVjNIbk1LRG1PbWlFRFVQRzRUaGFoaDFxK0Nmck1pbjBzNXpvbi9VT2JJWm1VZ3RqRXhNMkUrVzdLd0wvY05nMnJWZmY1bUhaM0FFZmtWZTVndW1lQUw0ci9aMDNqMndEOVJoQTgxZVNPSWFQM3l3OUk5dFVkdnVOM0dPTUFBNGMwTjVtMGtnOWhoMXllb1FDRFJuOEY4OTZGMFBoU20ycW1qTnFSU0dJeGhhbjZUWEVkWGYranBNQUlnY0hWL2w5TGtya0lkYXduQmhwNzVhdllKNjRwSTU0UmhSUVJmSHpyUTY5bDQvOG90WVVJcnJ1OTFWSUUwOEFDSDBPYjB2TjJKU2ZKa0ZHWWtVUmVTYzBjRjlCektFelVnNVFLV05jdFlGandnTlFuaG1sM2dZaGRJUjNOQVFCS0hOR3dxclpkMnlac1JaSjdNa1RTYnM4b1R1UHlNTGV2ZERDZll1d3NncjM4ZVRnQ0NNbytmQUJpdmYyUGpnNmJueXd0R3JWdkY0MGtQNmRKM1lIeGVDbXlYcElMWm5QbnhhalhKNDRoYmdQWTdOd3FMaVQ1TGVzQ2dGTGZmd2V2Tk1GOTZyRzQrdXArVmhFTVVLaUFxQnVFRlgwVHVBb3NFZFFsbjgvQktiMzRrYlYiLCJtYWMiOiI1YWZiODQyZmMwMjdjMDcwNTg2MWFkNDQyNmMzM2JiMWYzMjU1YjA3NGNlMzY2M2Q2NGU2NmM0YjNjNWRiODM0IiwidGFnIjoiIn0%3D; expires=Sat, 07 Jun 2025 06:18:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpiN0svMERqNjJ2ZE9wWmZzNlhkVmc9PSIsInZhbHVlIjoiQXNJbVlpa3YrMkhaZU4wUU1wTGRUTWV1eWtzMkxmd1QzVzg5STdQNk13OGVEVnQzVkdJSmhjRzZ6NTYxNytUM21EVWhvdHh5dGRTblZOSzUyZHEwNDB4ckF5NjlDWUlRUFNXMWdmVUZnWjFNZDg1WGxCcG1OcWF6S0R6YWJXSnNhdHpRblA2UVp6VTFnYWUyMDdUZ0dvRitJUzVMMW1ibXJid293S3NZb0FQbGo2dVFUVUNjMnRCZG56RXVMNG85dDZQTXFrZ3YrWkhBcTI4VW13cWNiRnhad0o0Sm1PbmZxUVEyanVmcTYrWTNsOFNvSjFjT1VVa0NGTGRCT2lFTWR3YnVpQlBkcXRnVVdrT1NpaGpqdWpwakQzNUlBSjN0RHhGREQzMmprTTJ1bkY0TVNBWktNZnhmb25vcUlpOHhtOVZqMlJGclpSL1hESC9ZUXBZV2xrTlVqM0NFKzZsdFVnbGNYQWEvVGxhbnUwMTNjc1hNVzFRYjFjZ0hKU1ZVZ1gyeVYwdzZqUnpNV3dwZGIrbmdURlBSOHc3R3U4YUUvWDBPM0tNVTVzUXpQRXAvSysxSTRPTzcxSUYyWWp1NGZvMWZmWDI0R0tSc1BvTXBnVWtVVitjaWxpUnlGL2VYZXpLTU1wanM1SFNRSE8rYURLbXV1TTJQZjU3R21WZGkiLCJtYWMiOiI2M2E0MjEyOTM4YjM3MTk2ZWNiNGY2N2QzM2U5NDNkNDkwY2Y1OGI0MjcwOTc4NWE0MDRjMGU0MDRmMDczOGVkIiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:18:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1TZlNsdWx0TU54d3AvTFJIaXlFM1E9PSIsInZhbHVlIjoiNm5DVGZzcmUvMTFuZ2VGWmFCZElCa2dWVjNIbk1LRG1PbWlFRFVQRzRUaGFoaDFxK0Nmck1pbjBzNXpvbi9VT2JJWm1VZ3RqRXhNMkUrVzdLd0wvY05nMnJWZmY1bUhaM0FFZmtWZTVndW1lQUw0ci9aMDNqMndEOVJoQTgxZVNPSWFQM3l3OUk5dFVkdnVOM0dPTUFBNGMwTjVtMGtnOWhoMXllb1FDRFJuOEY4OTZGMFBoU20ycW1qTnFSU0dJeGhhbjZUWEVkWGYranBNQUlnY0hWL2w5TGtya0lkYXduQmhwNzVhdllKNjRwSTU0UmhSUVJmSHpyUTY5bDQvOG90WVVJcnJ1OTFWSUUwOEFDSDBPYjB2TjJKU2ZKa0ZHWWtVUmVTYzBjRjlCektFelVnNVFLV05jdFlGandnTlFuaG1sM2dZaGRJUjNOQVFCS0hOR3dxclpkMnlac1JaSjdNa1RTYnM4b1R1UHlNTGV2ZERDZll1d3NncjM4ZVRnQ0NNbytmQUJpdmYyUGpnNmJueXd0R3JWdkY0MGtQNmRKM1lIeGVDbXlYcElMWm5QbnhhalhKNDRoYmdQWTdOd3FMaVQ1TGVzQ2dGTGZmd2V2Tk1GOTZyRzQrdXArVmhFTVVLaUFxQnVFRlgwVHVBb3NFZFFsbjgvQktiMzRrYlYiLCJtYWMiOiI1YWZiODQyZmMwMjdjMDcwNTg2MWFkNDQyNmMzM2JiMWYzMjU1YjA3NGNlMzY2M2Q2NGU2NmM0YjNjNWRiODM0IiwidGFnIjoiIn0%3D; expires=Sat, 07-Jun-2025 06:18:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041729283\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4T84fhF9pPJpQhK278TyPiT9Zp1ZT3XyFyp21kTx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}